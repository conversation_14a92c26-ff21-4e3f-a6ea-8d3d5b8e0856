{"components": [{"key": "recent_jap_enceph_vaccine", "type": "radio", "input": true, "label": "Have you had 2 doses of the Japanese Encephalitis Vaccine within the last 12 months?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": false, "optionsLabelPosition": "right"}, {"key": "currently_pregnant", "type": "radio", "input": true, "label": "Are you currently pregnant?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": false, "optionsLabelPosition": "right"}, {"key": "destination_country", "data": {"json": ["NOT ON THE LIST", "Bangladesh", "Bhutan", "Brunei", "Burma", "Cambodia", "China", "India", "Indonesia", "Japan", "Laos", "Malaysia", "Nepal", "North Korea", "Papua New Guinea", "<PERSON><PERSON>", "Singapore", "South Korea", "Sri Lanka", "Taiwan", "Thailand", "Timor-Leste", "Vietnam"]}, "type": "select", "input": true, "label": "Please select the country of your birth (or “NOT ON LIST”):", "widget": "html5", "dataSrc": "json", "tableView": true, "customConditional": "show = data.recent_jap_enceph_vaccine == false;"}, {"key": "diagnosis_shingles", "type": "radio", "input": true, "label": "Are you concerned you may be experiencing a shingles outbreak?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": false, "customConditional": "show = data.diagnosis_new_hsv == false;", "optionsLabelPosition": "right"}, {"key": "ocular_herpes", "type": "radio", "input": true, "label": "Are you concerned you may be experiencing a herpes outbreak in your eye?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": false, "customConditional": "show = data.diagnosis_shingles == false;", "optionsLabelPosition": "right"}, {"key": "diagnosis_uncertain_hsv", "type": "radio", "input": true, "label": "Do you have a new rash that you're not sure is related to herpes?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": false, "customConditional": "show = data.ocular_herpes == false;", "optionsLabelPosition": "right"}, {"key": "herpes_contraindications", "type": "selectboxes", "input": true, "label": "Have you had any of the following medical conditions:", "values": [{"label": "Bone marrow transplant", "value": "bone_marrow", "shortcut": ""}, {"label": "Kidney transplant", "value": "kidney_transplant", "shortcut": ""}, {"label": "Kidney disease", "value": "kidney_disease", "shortcut": ""}, {"label": "Advanced HIV or 'AIDS'", "value": "aids", "shortcut": ""}, {"label": "Currently breastfeeding", "value": "breastfeeding", "shortcut": ""}], "inputType": "checkbox", "tableView": false, "customConditional": "show = data.diagnosis_new_hsv == false && data.diagnosis_shingles == false && data.ocular_herpes == false && data.diagnosis_uncertain_hsv == false;", "optionsLabelPosition": "right"}, {"key": "no_hsv_contraindications", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = _.some(_.values(data.herpes_contraindications)) || data.no_hsv_contraindications;"}, "tableView": false, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.diagnosis_new_hsv == false && data.diagnosis_shingles == false && data.ocular_herpes == false && data.diagnosis_uncertain_hsv == false;"}]}