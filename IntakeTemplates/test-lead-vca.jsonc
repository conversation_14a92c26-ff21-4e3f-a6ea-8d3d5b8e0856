{"components": [{"key": "indications", "type": "selectboxes", "input": true, "label": "Please select if any of the following apply to you:", "values": [{"label": "Fire Range Instructor", "value": "fire_instructor", "shortcut": ""}, {"label": "Shooting Range Employee", "value": "employee_shooting_range", "shortcut": ""}, {"label": "Soldier in the Armed Forces", "value": "armed_forces", "shortcut": ""}, {"label": "Police Officer", "value": "police_officer", "shortcut": ""}, {"label": "Fire Fighter", "value": "fire_fighter", "shortcut": ""}, {"label": "<PERSON>", "value": "hunter", "shortcut": ""}, {"label": "Amateur Shooter with Regular Range Visits", "value": "amateur_shooter", "shortcut": ""}, {"label": "Professional Athlete with Regular Range Visits", "value": "professional_shooter", "shortcut": ""}, {"label": "Have exposure to lead in the workplace", "value": "workplace_exposure", "shortcut": ""}, {"label": "Have ongoing exposure to lead paint", "value": "paint_exposure", "shortcut": ""}], "inputType": "checkbox", "tableView": true, "optionsLabelPosition": "right"}, {"key": "no_indications", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = _.some(_.values(data.indications)) || data.no_indications;"}, "tableView": false, "customClass": "mt-n3", "defaultValue": false}, {"key": "contraindications", "type": "selectboxes", "input": true, "label": "Are you experiencing any of the following symptoms:", "values": [{"label": "Headaches", "value": "headache", "shortcut": ""}, {"label": "Fatigue", "value": "fatigue", "shortcut": ""}, {"label": "Abdominal or Pelvic Pain", "value": "abdo_pain", "shortcut": ""}, {"label": "Chest Pain or Pressure", "value": "chest_pain", "shortcut": ""}, {"label": "Weight Loss", "value": "weight_loss", "shortcut": ""}, {"label": "Joint or Muscle Pain", "value": "joint_pain", "shortcut": ""}, {"label": "Low or Depressed Mood", "value": "low_mood", "shortcut": ""}, {"label": "Anxiety or Worried Thoughts", "value": "anxiety", "shortcut": ""}, {"label": "Difficulties with Concentration", "value": "concentration_difficulties", "shortcut": ""}, {"label": "Feel Lightheaded or Faint", "value": "presy<PERSON><PERSON>", "shortcut": ""}, {"label": "Heart Palpitations (slow or fast heart beat)", "value": "palpitations", "shortcut": ""}], "inputType": "checkbox", "tableView": true, "customConditional": "show = !data.no_indications", "optionsLabelPosition": "right"}, {"key": "no_contraindications", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = _.some(_.values(data.contraindications)) || data.no_contraindications;"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = !data.no_indications"}, {"key": "contraindication_keys", "type": "textfield", "input": true, "label": "Contraindication keys:", "hidden": true, "disabled": true, "multiple": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "value = _.keys(_.pickBy(data.contraindications));", "refreshOnChange": true}, {"key": "approved_html", "type": "content", "input": true, "tableView": false, "clearOnHide": false, "defaultValue": "You're eligible to discuss monitoring of your blood lead levels. We do not currently offer other heavy metal testing at this time."}]}