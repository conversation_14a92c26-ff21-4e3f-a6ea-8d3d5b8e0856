{% load template_extras %}
{% with data=questionnaire.hpc.data %}
{% with summary=questionnaire.raw_formio_summary %}
{% with insured=questionnaire.insured_assays.all %}
{% with uninsured=questionnaire.uninsured_assays.all %}
{% with rxts=questionnaire.rxts.all %}

<!-- Introduction -->
<p>Hi {{ patient.name }},</p>
<p>
    This is {{ doctor.name }} (CPSO #{{ doctor.cpso_number }}) from our clinic.  
    Based on your responses, we've reviewed your request for medication to delay your period.  
    If you believe any of your answers may have been incorrect or have additional symptoms to report, please let us know so we can follow up securely. Otherwise, here's the plan moving forward.
</p>

{% if summary.rx_preference_suppress_menses and summary.rx_preference_suppress_menses.c_val == 'not_sure' %}
<p><strong><em>You indicated you have no preference for NETA or MPA. We’ve elected to provide you with a prescription for <strong>MPA</strong> as it is more convenient (twice-daily dosing) and more affordable. If you have further questions or wish to change your option, we can arrange a real-time messaging chat.</em></strong></p>
{% endif %}

<!-- Patient Screening -->
<ul>
    <li><strong>Your health history indicates you have never been diagnosed with any of the following:</strong>
        <ul>
            <li>High blood pressure</li>
            <li>History of blood clots (e.g., DVT or pulmonary embolism)</li>
            <li>Stroke or mini-stroke (TIA)</li>
            <li>Breast cancer or strong suspicion of it</li>
            <li>Significant liver disease (cirrhosis, liver cancer)</li>
            <li>Blood clotting disorders (e.g., Factor V Leiden, Protein C/S deficiency)</li>
        </ul>
    </li>
    {% for s in summary|confirm:"smoking_status,recent_blood_pressure,medication_contraindications" %}
        <li>{{ s|safe }}</li>
    {% endfor %}
</ul>

<!-- Travel Details -->
<h5>Travel Details</h5>
<ul>
  <li><strong>Travel Start Date:</strong>
    {% if summary.travel_start_date and summary.travel_start_date.c_val %}
      {{ summary.travel_start_date.c_val }}
    {% else %}
      Not provided
    {% endif %}
  </li>

  <li><strong>Travel Duration (Days):</strong>
    {% if summary.travel_days and summary.travel_days.c_val %}
      {{ summary.travel_days.c_val }}
    {% else %}
      Not provided
    {% endif %}
  </li>

  <li><strong>Expected Menses Date:</strong>
    {% if summary.expected_menses_date and summary.expected_menses_date.c_val %}
      {{ summary.expected_menses_date.c_val }}
    {% else %}
      Not provided or uncertain
    {% endif %}
  </li>

<li><strong>Medication Preference:</strong>
    {% if summary.rx_preference_suppress_menses and summary.rx_preference_suppress_menses.c_val %}
      {% if summary.rx_preference_suppress_menses.c_val == 'not_sure' %}
        Not sure
      {% else %}
        {{ summary.rx_preference_suppress_menses.c_val }}
      {% endif %}
    {% else %}
      Not selected
    {% endif %}
  </li>
</ul>

<!-- Health Condition Summary -->
<h5>Health Condition Summary</h5>
<ul>
  {% with summary=questionnaire.raw_formio_summary %}

  <li><strong>Current Medications:</strong> 
    {% if summary.medications_list and summary.medications_list.c_val %}
      {{ summary.medications_list.c_val }}
    {% else %}
      None
    {% endif %}
  </li>

  <li><strong>Medication Allergies:</strong> 
    {% if summary.medication_allergies_list and summary.medication_allergies_list.c_val %}
      {{ summary.medication_allergies_list.c_val }}
    {% else %}
      None
    {% endif %}
  </li>

  <li><strong>Past Surgeries:</strong> 
    {% if summary.past_surgeries_list and summary.past_surgeries_list.c_val %}
      {{ summary.past_surgeries_list.c_val }}
    {% else %}
      None
    {% endif %}
  </li>

  <li><strong>Health Conditions:</strong> 
    {% if summary.health_conditions_list and summary.health_conditions_list.c_val %}
      {{ summary.health_conditions_list.c_val }}
    {% else %}
      None
    {% endif %}
  </li>

  {% endwith %}
</ul>

<!-- Advice Section -->
<h5>Advice</h5>
<p>
    You confirm that you have reviewed and understood the information regarding the use of hormone-based medications to delay menstruation, including potential side effects like nausea, breast tenderness, or breakthrough bleeding.
</p>

<!-- Plan Section -->
<h5>Plan</h5>
{% if rxts %}
<p><strong>Prescriptions:</strong></p>
<ul>
    {% for rx in rxts %}
    <li>{{ rx.display_name }}</li>
    {% endfor %}
</ul>
{% else %}
<p>No prescriptions have been issued at this time.</p>
{% endif %}

<!-- Instructions Section -->
<h5>Instructions</h5>
<ol>
    <li><strong>Start Date:</strong> Begin the medication on <strong>{{ data.pill_start_date }}</strong> as calculated during your intake.</li>
    <li><strong>Duration:</strong> You will need to take the medication for a total of <strong>{{ data.total_pill_days }}</strong> days.</li>
    <li><strong>Effectiveness:</strong> Start the medication at least 5 days before your expected period to help reliably delay menstruation.</li>
    <li><strong>Missed Dose:</strong> If you miss a dose, take it as soon as you remember. Do not double up.</li>
    <li><strong>Side Effects:</strong> You may experience nausea, bloating, or mild spotting. These usually resolve without intervention, but if you feel unwell reach out to a local provider or pharmacist. If you stop your medication, you will experience a bleed within 1-2 days.</li>
</ol>

<!-- Footer -->
<p>Best regards,<br>{{ doctor.name }}</p>

{% endwith %}
{% endwith %}
{% endwith %}
{% endwith %}
{% endwith %}