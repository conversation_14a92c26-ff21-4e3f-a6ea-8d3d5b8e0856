{% load template_extras %}
{% with rxts=questionnaire.rxts.all rxks=questionnaire.rxt_keys %}
<p>Hi {{ patient.name }},</p>
<p>
    This is {{ doctor.name }} (CPSO #{{ doctor.cpso_number }}) from our clinic. 
    If you have questions about your responses or feel your answers missed any symptoms or conditions, 
    we can arrange for secure real-time messaging. Otherwise, we can proceed with the plan below, but I need you to confirm the following from your medical history.
</p>
{# =================  MEDICAL SUMMARY (EpiPen Renewal)  ================= #}
{% with data=questionnaire.hpc.data summary=questionnaire.raw_formio_summary %}
<h3 style="text-align:center;"><strong>Medical History Summary</strong></h3>
<ul>
  {# Reason for EpiPen renewal #}
  {% with reasons=summary|confirm:"epipen_renewal_reason,other_epipen_renewal_reason" %}
  {% if reasons %}
    <li><strong>Reason for renewal</strong>
      <ul>
        {% for qa in reasons %}
          <li>{% autoescape off %}{{ qa|replace:": |: <strong>" }}{% endautoescape %}</strong></li>
        {% endfor %}
      </ul>
    </li>
  {% endif %}
  {% endwith %}

  {# Current anaphylaxis concern & symptoms #}
  {% with cur=summary|confirm:"concern_current_anaphylaxis,current_anaphylaxis_symptoms" %}
  {% if cur %}
    <li><strong>Current symptoms / concern</strong>
      <ul>
        {% for qa in cur %}
          <li>{% autoescape off %}{{ qa|replace:": |: <strong>" }}{% endautoescape %}</strong></li>
        {% endfor %}
      </ul>
    </li>
  {% endif %}
  {% endwith %}

  {# Allergy triggers & use context for this prescription #}
  {% with trig=summary|confirm:"epipen_for_triggers,epipen_trigger_other_free_text,epipen_trigger_details,epipen_last_reaction_timing" %}
  {% if trig %}
    <li><strong>Allergy triggers & use</strong>
      <ul>
        {% for qa in trig %}
          <li>
            {% if ":" in qa %}
              {% autoescape off %}{{ qa|replace:": |: <strong>" }}{% endautoescape %}</strong>
            {% else %}
              {{ qa|safe }}
            {% endif %}
          </li>
        {% endfor %}
      </ul>
    </li>
  {% endif %}
  {% endwith %}

  {# Prior anaphylaxis history #}
  {% with ana=summary|confirm:"anaphylaxis_history,anaphylaxis_year,anaphylaxis_triggers,anaphylaxis_emergency_care,anaphylaxis_specialist_followup,anaphylaxis_followup_type,anaphylaxis_last_followup_range,anaphylaxis_specialist_location" %}
  {% if ana %}
    <li><strong>Prior anaphylaxis history</strong>
      <ul>
        {% for qa in ana %}
          <li>
            {% if ":" in qa %}
              {% autoescape off %}{{ qa|replace:": |: <strong>" }}{% endautoescape %}</strong>
            {% else %}
              {{ qa|safe }}
            {% endif %}
          </li>
        {% endfor %}
      </ul>
    </li>
  {% endif %}
  {% endwith %}

  {# Prior EpiPen use & outcomes (only if applicable) #}
  {% with prior=summary|confirm:"prior_epipen_used_before,prior_epipen_outcome" %}
  {% if prior %}
    <li><strong>Prior EpiPen use & outcomes</strong>
      <ul>
        {% for qa in prior %}
          <li>{% autoescape off %}{{ qa|replace:": |: <strong>" }}{% endautoescape %}</strong></li>
        {% endfor %}
      </ul>
    </li>
  {% endif %}
  {% endwith %}

  {# Other health conditions (comorbidities) #}
  {% with comorb=summary|confirm:"risk_comorbid_conditions" %}
  {% if comorb %}
    <li><strong>Other health conditions</strong>
      <ul>
        {% for qa in comorb %}
          <li>{% autoescape off %}{{ qa|replace:": |: <strong>" }}{% endautoescape %}</strong></li>
        {% endfor %}
      </ul>
    </li>
  {% endif %}
  {% endwith %}

  {# Device status & training #}
  {% with dev=summary|confirm:"device_on_hand_count,device_expiry_range,device_last_used_range,device_training_source,device_use_issues" %}
  {% if dev %}
    <li><strong>Device status & training</strong>
      <ul>
        {% for qa in dev %}
          <li>
            {% if ":" in qa %}
              {% autoescape off %}{{ qa|replace:": |: <strong>" }}{% endautoescape %}</strong>
            {% else %}
              {{ qa|safe }}
            {% endif %}
          </li>
        {% endfor %}
      </ul>
    </li>
  {% endif %}
  {% endwith %}
</ul>
{% endwith %}

{# ================  EpiPen QUANTITY & DISPENSING SUMMARY  ================ #}
{% with summary=questionnaire.raw_formio_summary %}
<h5>EpiPen Quantity & Dispensing Summary</h5>
<ul>
  {# Patient selections (only quantity + preference) #}
  {% with qty=summary|confirm:"epipen_quantity" disp=summary|confirm:"epipen_dispense_preference" %}
  {% if qty or disp %}
    <li><strong>Medication request</strong>
      <ul>
        {% for qa in qty %}
          <li>{% autoescape off %}{{ qa|replace:": |: <strong>" }}{% endautoescape %}</strong></li>
        {% endfor %}
        {% for qa in disp %}
          <li>{% autoescape off %}{{ qa|replace:": |: <strong>" }}{% endautoescape %}</strong></li>
        {% endfor %}
      </ul>
    </li>
  {% endif %}
  {% endwith %}
</ul>
{% endwith %}



<!-- Plan Section -->
<h3 style="text-align:center;"><strong>Plan</strong></h3>

{# ===============  EpiPen PRESCRIPTION & ADVICE SUMMARY  =============== #}
{% with summary=questionnaire.raw_formio_summary rxts=questionnaire.rxts.all %}
{% if rxts %}
  <p>Based on your intake, an EpiPen prescription has been prepared as follows:</p>
  <ul>
    {% for rx in rxts %}
      <li>{{ rx.display_name }}</li>
    {% endfor %}
  </ul>
{% else %}
  <p>No EpiPen prescription has been issued at this time.</p>
{% endif %}

<h3 style="text-align:center;"><strong>EpiPen Advice Acknowledgements</strong></h3>

<ul style="list-style-type:none; padding-left:0;">
  {# Emergency use & ER care #}
  {% with val=summary|confirm:"epipen_emergency_use" %}
    {% if val %}
      {% for qa in val %}
        {% if "do_not_understand" in qa %}
          <li><strong style="color:red;">Does not understand emergency use & ER care</strong></li>
        {% else %}
          <li><strong>Understands emergency use & ER care</strong></li>
        {% endif %}
      {% endfor %}
    {% endif %}
  {% endwith %}

  {# When to use #}
  {% with val=summary|confirm:"epipen_when_to_use" %}
    {% if val %}
      {% for qa in val %}
        {% if "do_not_understand" in qa %}
          <li><strong style="color:red;">Does not understand when to use the EpiPen</strong></li>
        {% else %}
          <li><strong>Understands when to use the EpiPen</strong></li>
        {% endif %}
      {% endfor %}
    {% endif %}
  {% endwith %}

  {# After insect stings or food reactions #}
  {% with val=summary|confirm:"epipen_trigger_specific" %}
    {% if val %}
      {% for qa in val %}
        {% if "do_not_understand" in qa %}
          <li><strong style="color:red;">Does not understand steps after insect stings or food reactions</strong></li>
        {% else %}
          <li><strong>Understands steps after insect stings or food reactions</strong></li>
        {% endif %}
      {% endfor %}
    {% endif %}
  {% endwith %}

  {# Possible side effects #}
  {% with val=summary|confirm:"epipen_side_effects" %}
    {% if val %}
      {% for qa in val %}
        {% if "do_not_understand" in qa %}
          <li><strong style="color:red;">Does not understand possible side effects</strong></li>
        {% else %}
          <li><strong>Understands possible side effects</strong></li>
        {% endif %}
      {% endfor %}
    {% endif %}
  {% endwith %}

  {# Injection-site infection #}
  {% with val=summary|confirm:"epipen_injection_site_infection" %}
    {% if val %}
      {% for qa in val %}
        {% if "do_not_understand" in qa %}
          <li><strong style="color:red;">Does not understand injection-site infection advice</strong></li>
        {% else %}
          <li><strong>Understands injection-site infection advice</strong></li>
        {% endif %}
      {% endfor %}
    {% endif %}
  {% endwith %}

  {# Storage #}
  {% with val=summary|confirm:"epipen_storage" %}
    {% if val %}
      {% for qa in val %}
        {% if "do_not_understand" in qa %}
          <li><strong style="color:red;">Does not understand how to store the EpiPen</strong></li>
        {% else %}
          <li><strong>Understands how to store the EpiPen</strong></li>
        {% endif %}
      {% endfor %}
    {% endif %}
  {% endwith %}

  {# Injection technique #}
  {% with val=summary|confirm:"epipen_injection_technique" %}
    {% if val %}
      {% for qa in val %}
        {% if "do_not_understand" in qa %}
          <li><strong style="color:red;">Does not understand injection technique</strong></li>
        {% else %}
          <li><strong>Understands injection technique</strong></li>
        {% endif %}
      {% endfor %}
    {% endif %}
  {% endwith %}
</ul>

{% endwith %}

<p class="mt-3">
    <strong>Confirmation:</strong>
    By completing and submitting this intake, you confirm that the information provided is accurate to the best of your knowledge, <u>that you have read and understand all advice contained in the intake form</u>, and that you agree to seek emergency or in-person medical care if severe side-effects or new symptoms develop.
  </p>
<p>Best regards,<br>{{ doctor.name }}</p>
{% endwith %}