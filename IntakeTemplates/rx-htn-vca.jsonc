{"components": [{"key": "diagnosis_hbp", "type": "radio", "input": true, "label": "Have you been diagnosed with high blood pressure (hypertension) in the past?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "confirmLabel": "Diagnosed with high blood pressure:", "optionsLabelPosition": "right"}, {"key": "on_prescribed_hbp_medication", "type": "radio", "input": true, "label": "Are you currently or were recently prescribed medication for high blood pressure?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "confirmLabel": "Prescribed medication for high blood pressure:", "customConditional": "show = data.diagnosis_hbp === 'yes';", "optionsLabelPosition": "right"}, {"key": "bp_greater_160_100", "type": "radio", "input": true, "label": "Is your blood pressure regularly greater than 160/100 mmHg?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "I don't know", "value": "unknown"}], "validate": {"required": true}, "tableView": true, "confirmLabel": "Blood pressure greater than 160/100 mmHg:", "customConditional": "show = data.on_prescribed_hbp_medication === 'yes';", "optionsLabelPosition": "right"}, {"key": "symptoms_contra", "type": "selectboxes", "input": true, "label": "Are you experiencing any of the following symptoms?", "values": [{"label": "Chest pain", "value": "chest_pain"}, {"label": "Shortness of breath", "value": "sob"}, {"label": "New swelling in my legs, ankles, or feet", "value": "edema"}, {"label": "Heart palpitations", "value": "palpitations"}, {"label": "Headache or vision changes", "value": "headache"}, {"label": "Numbness or weakness (face, arm, or leg)", "value": "numbness_weakness"}, {"label": "Difficulty speaking or confusion", "value": "speech_confusion"}, {"label": "Fainting or loss of consciousness", "value": "syncope"}, {"label": "Nausea or vomiting", "value": "nausea_vomiting"}], "inputType": "checkbox", "tableView": true, "confirmLabel": "Symptoms present:", "customConditional": "show = data.bp_greater_160_100 === 'no';", "optionsLabelPosition": "right"}, {"key": "no_symptoms_contra", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "Please select at least one symptom or confirm none apply."}, "validate": {"custom": "valid = !!data.no_symptoms_contra || !!_.some(_.values(data.symptoms_contra));"}, "tableView": true, "defaultValue": false, "customClass": "mt-n3", "customConditional": "show = data.bp_greater_160_100 === 'no';"}, {"key": "no_indications", "type": "textfield", "input": true, "label": "No Indication:", "hidden": true, "disabled": true, "multiple": false, "tableView": true, "clearOnHide": false, "defaultValue": true, "calculateValue": "value = (data.diagnosis_hbp === 'yes' && data.on_prescribed_hbp_medication === 'yes') ? false : true;", "refreshOnChange": true}, {"key": "contraindication_keys", "type": "textfield", "input": true, "label": "Contraindication keys:", "hidden": true, "disabled": true, "multiple": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "value = [];\n\n// If BP is >160/100 or unknown\nif (data.bp_greater_160_100 === 'yes' || data.bp_greater_160_100 === 'unknown') {\n  value.push('high_or_unknown_bp');\n}\n\n// If any cardiac/respiratory symptoms\nif (_.some(_.values(data.symptoms_contra))) {\n  value.push('cardiac_resp_symptoms');\n}\n", "refreshOnChange": true}, {"key": "rejected_html", "type": "content", "input": true, "tableView": false, "clearOnHide": false, "defaultValue": "<h3 class='text-red'>You're ineligible for monitoring at this time. Please seek in-person care with a physician to discuss your concerns.</h3>", "refreshOnChange": true}]}