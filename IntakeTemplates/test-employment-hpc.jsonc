{"components": [{"key": "heading_employment_bloodwork", "html": "<h1><center><strong>Employment Bloodwork Testing</strong></h1><center><p>To expedite your care, please answer the following questions to the best of your abilities. &nbsp;We ask these questions to provide the best possible care.&nbsp;</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "heading_medical_indication", "html": "<h4>Reason for Testing&nbsp;</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "medical_testing_indication", "type": "selectboxes", "input": true, "label": "Please select what you are interested in today:", "values": [{"label": "Bloodwork for School", "value": "school_bloodwork", "shortcut": ""}, {"label": "Bloodwork for Work", "value": "bloodwork_work", "shortcut": ""}, {"label": "Bloodwork for Travel", "value": "bloodwork_travel", "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "optionsLabelPosition": "right"}, {"key": "other_medical_indication", "type": "textarea", "input": true, "label": "Please state your “other” reason(s) for testing:", "tableView": true, "autoExpand": false, "customConditional": "show = data.medical_testing_indication.other;"}, {"key": "heading_medical_indication", "html": "<h4>Medical Indication&nbsp;</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "header_vaccine_records", "html": "<h4>General Questions&nbsp;</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "vaccine_records", "type": "radio", "input": true, "label": "Do you have access to or can you obtain access to your vaccine records?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "required_testing", "type": "selectboxes", "input": true, "label": "Please select what testing is required:", "values": [{"label": "I don't know", "value": "doesn't_know", "shortcut": ""}, {"label": "Hepatitis A", "value": "hep_a", "shortcut": ""}, {"label": "Hepatitis B", "value": "hep_b", "shortcut": ""}, {"label": "Hepatitis C", "value": "hep_c", "shortcut": ""}, {"label": "HIV", "value": "hiv", "shortcut": ""}, {"label": "Syphilis", "value": "vdrl", "shortcut": ""}, {"label": "<PERSON><PERSON><PERSON>", "value": "measles", "shortcut": ""}, {"label": "<PERSON><PERSON>", "value": "mumps", "shortcut": ""}, {"label": "<PERSON><PERSON><PERSON>", "value": "rubella", "shortcut": ""}, {"label": "Varicella", "value": "varicella", "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "optionsLabelPosition": "right"}, {"key": "other__required_testing", "type": "textarea", "input": true, "label": "Please state what other tests you require. TeleTest does not offer additional routine blood through this intake (i.e. cholesterol, diabetes, etc).", "tableView": true, "autoExpand": false, "conditional": {"eq": "other", "show": true, "when": "previous_medication"}}, {"key": "hep_a_header", "html": "<h3>Hepatitis A Vaccination&nbsp;</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.required_testing.hep_a", "refreshOnChange": false}, {"key": "prior_hep_a_vaccination", "type": "radio", "input": true, "label": "Have you had any doses of a Hepatitis A vaccine before?", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "Don't Know", "value": "dont_know", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.required_testing.hep_a", "optionsLabelPosition": "right"}, {"key": "quantity_hep_a_doses", "type": "radio", "input": true, "label": "How many doses of Hepatitis A Vaccine have you recieved?", "inline": false, "values": [{"label": "1 injection", "value": "1_dose", "shortcut": ""}, {"label": "2 injections", "value": "2_doses", "shortcut": ""}, {"label": "3 injections", "value": "3_doses", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.prior_hep_a_vaccination == 'yes';", "optionsLabelPosition": "right"}, {"key": "hep_b_header", "html": "<h4>Hepatitis B Vaccination&nbsp;</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.required_testing.hep_b", "refreshOnChange": false}, {"key": "hep_b_vaccinated", "type": "radio", "input": true, "label": "Have you had Hepatitis B Vaccination before?", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "I don't know", "value": "doesnt_know", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.required_testing.hep_b", "optionsLabelPosition": "right"}, {"key": "review_record_prompt", "html": "Please review your vaccine records before continuing and confirm your vaccination history.", "type": "content", "input": false, "label": "Content", "tableView": true, "customConditional": "show = data.hep_b_vaccinated == 'doesnt_know' && data.vaccine_records == 'true';", "refreshOnChange": false}, {"key": "number_of_hep_b_vaccines", "type": "radio", "input": true, "label": "Do you know how many doses of Hepatitis B vaccine you recieved?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No/I don't know", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.hep_b_vaccinated == 'yes';", "optionsLabelPosition": "right"}, {"key": "quantity_hep_b_doses", "type": "radio", "input": true, "label": "How many doses of Hepatitis B Vaccine have you recieved?", "inline": false, "values": [{"label": "1 injection", "value": "1_dose", "shortcut": ""}, {"label": "2 injections", "value": "2_doses", "shortcut": ""}, {"label": "3 injections", "value": "3_doses", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.number_of_hep_b_vaccines == true;", "optionsLabelPosition": "right"}, {"key": "two_doses_before_16", "type": "radio", "input": true, "label": "Did you have 2 doses of hepatitis B completed before age 16?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No/Don't Know", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.quantity_hep_b_doses != '1_dose' && data.hep_b_vaccinated == 'yes' && data.number_of_hep_b_vaccines == true;", "optionsLabelPosition": "right"}, {"key": "hep_b_seperated_4_months", "type": "radio", "input": true, "label": "Were your two doses of Hepatitis B vaccine seperated by 4 months or more?", "inline": false, "values": [{"label": "Yes", "value": "hep_b_not_indicated", "shortcut": ""}, {"label": "No", "value": "booster_hep_b_indicated", "shortcut": ""}, {"label": "Don't Know", "value": "dont_know", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.two_doses_before_16 == true;", "optionsLabelPosition": "right"}, {"key": "mmr_header", "html": "<h3><PERSON><PERSON><PERSON> and R<PERSON>lla Vaccination&nbsp;</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.required_testing.measles || data.required_testing.mumps || data.required_testing.rubella", "refreshOnChange": false}, {"key": "prior_mmr_vaccination", "type": "radio", "input": true, "label": "Have you had vaccination against measles, mumps or rubella before?", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "Don't Know", "value": "dont_know", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.required_testing.measles || data.required_testing.mumps || data.required_testing.rubella", "optionsLabelPosition": "right"}, {"key": "quantity_mmr_doses", "type": "radio", "input": true, "label": "How many doses of measles, mumps or rubella have you recieved (Note: in some countries other than Canada, the vaccines are given seperately.  Please select 'other' and provide the details below if you received the vaccines seperately).", "inline": false, "values": [{"label": "1 injection", "value": "1_dose", "shortcut": ""}, {"label": "2 injections", "value": "2_doses", "shortcut": ""}, {"label": "3 injections", "value": "3_doses", "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.prior_mmr_vaccination == 'yes';", "optionsLabelPosition": "right"}, {"key": "other_mmr", "type": "textarea", "input": true, "label": "Please state what vaccines and how many doses you have received:", "tableView": true, "autoExpand": false, "conditional": {"eq": "other", "show": true, "when": "quantity_mmr_doses"}}, {"key": "varicella_header", "html": "<h3><PERSON><PERSON><PERSON><PERSON> (Chicken Pox)&nbsp;</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.required_testing.varicella", "refreshOnChange": false}, {"key": "prior_varicella_vaccination", "type": "radio", "input": true, "label": "Have you had the chicken pox (varicella) vaccine before?", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "Don't Know", "value": "dont_know", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.required_testing.varicella", "optionsLabelPosition": "right"}, {"key": "prior_varicella_vaccination", "type": "radio", "input": true, "label": "Have you had chicken pox (varicella) before?", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No/Don't Know", "value": "no_don't_know", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.required_testing.varicella", "optionsLabelPosition": "right"}, {"key": "content2", "html": "<h2>Questions for the Doctor</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "additional_questions", "type": "textarea", "input": true, "label": "Please use this area to ask any questions that you might have for your health care provider:", "tableView": true, "autoExpand": false}]}