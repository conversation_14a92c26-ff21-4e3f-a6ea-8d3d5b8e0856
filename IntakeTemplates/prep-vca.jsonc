{"components": [{"key": "interested_in_prep", "type": "radio", "input": true, "label": "Are you interested in starting PrEP to reduce your risk of acquiring HIV?", "description": "PrEP (Pre-Exposure Prophylaxis) is a daily medication that significantly reduces the risk of HIV when taken as prescribed.", "values": [{"label": "Yes, I want to start PrEP", "value": true, "shortcut": ""}, {"label": "No, I am not interested", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "confirm_label": "Interested in PrEP:", "optionsLabelPosition": "right"}, {"key": "not_interested_html", "type": "content", "input": true, "tableView": false, "clearOnHide": false, "defaultValue": "<h3 class='text-red'>You have indicated that you are not interested in PrEP.</h3><p>If you change your mind, you can revisit this page at any time to learn more about your eligibility.</p>", "customConditional": "show = data.interested_in_prep === false;"}, {"key": "hiv_exposure_recent", "type": "radio", "input": true, "confirm_label": "Recent HIV exposure:", "label": "Have you had a known or suspected exposure to HIV in the past 72 hours?", "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = data.interested_in_prep === true;"}, {"key": "seek_pep", "html": "<ul class='text-red'><li>We recommend seeking immediate medical attention at the nearest emergency department for <strong>Post-Exposure Prophylaxis (PEP)</strong>.</li><li>PrEP is not a substitute for PEP when exposure occurred within the last 72 hours.</li></ul>", "type": "content", "input": true, "tableView": false, "clearOnHide": false, "customConditional": "show = data.hiv_exposure_recent === true;"}, {"key": "symptoms_hiv_acute", "type": "selectboxes", "input": true, "label": "Are you currently experiencing any of the following symptoms? (These may indicate acute HIV infection)", "values": [{"label": "Fever", "value": "fever", "shortcut": ""}, {"label": "Night sweats", "value": "night_sweats", "shortcut": ""}, {"label": "<PERSON><PERSON>", "value": "rash", "shortcut": ""}, {"label": "Swollen lymph nodes", "value": "lymph_nodes", "shortcut": ""}, {"label": "Sore throat", "value": "sore_throat", "shortcut": ""}, {"label": "Fatigue", "value": "fatigue", "shortcut": ""}], "inputType": "checkbox", "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = data.interested_in_prep === true && data.hiv_exposure_recent === false;"}, {"key": "no_hiv_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "Please select at least one symptom or 'None of the above'."}, "validate": {"custom": "valid = !!data.no_hiv_symptoms || !!_.some(_.values(data.symptoms_hiv_acute));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.interested_in_prep === true && data.hiv_exposure_recent === false;"}, {"key": "renal_disease", "type": "radio", "input": true, "label": "Do you have a history of kidney disease or have been told your kidney function is impaired?", "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "tableView": true, "optionsLabelPosition": "right", "confirm_label": "Renal disease:", "customConditional": "show = data.interested_in_prep === true && data.hiv_exposure_recent === false && data.no_hiv_symptoms === true;"}, {"key": "hep_b_history", "type": "radio", "input": true, "label": "Do you have Hepatitis B infection or a history of Hepatitis B?", "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "tableView": true, "optionsLabelPosition": "right", "confirm_label": "Hepatitis B history:", "customConditional": "show = data.interested_in_prep === true && data.renal_disease === false;"}, {"key": "medication_allergies", "type": "selectboxes", "input": true, "label": "Do you have any allergies to the following medications?", "values": [{"label": "Tenofovir", "value": "tenofovir", "shortcut": ""}, {"label": "Emtricitabine", "value": "emtricitabine", "shortcut": ""}], "inputType": "checkbox", "tableView": true, "optionsLabelPosition": "right", "confirm_label": "Medication allergies:", "customConditional": "show = data.interested_in_prep === true && data.hep_b_history === false;"}, {"key": "no_medication_allergies", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "Please select at least one option or 'None of the above'."}, "validate": {"custom": "valid = !!data.no_medication_allergies || !!_.some(_.values(data.medication_allergies));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.interested_in_prep === true && data.hep_b_history === false;"}, {"key": "no_indications", "type": "textfield", "input": true, "label": "No Indication:", "hidden": true, "disabled": true, "multiple": false, "tableView": true, "clearOnHide": false, "calculateValue": "value = (data.interested_in_prep === false) ? true : false;", "refreshOnChange": true}, {"key": "contraindication_keys", "type": "textfield", "input": true, "label": "Contraindication keys:", "hidden": true, "disabled": true, "multiple": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "value = []; if(data.interested_in_prep === true) { if(data.hiv_exposure_recent === true) { value.push('hiv_exposure_recent'); } else { if(!data.no_hiv_symptoms && _.some(_.values(data.symptoms_hiv_acute))) { value.push('hiv_symptoms'); } } if(data.renal_disease === true) { value.push('renal_disease'); } if(data.bone_disease === true) { value.push('bone_disease'); } if(data.hep_b_history === true) { value.push('hep_b_history'); } if(!data.no_medication_allergies && _.some(_.values(data.medication_allergies))) { value.push('medication_allergy'); } }", "refreshOnChange": true}, {"key": "rejected_html", "type": "content", "input": true, "tableView": false, "clearOnHide": false, "defaultValue": "<h3 class='text-red'>We are unable to provide a prescription for PrEP at this time.</h3><p>Based on your responses, you may still be eligible for PrEP. We recommend following up with a local <strong>sexual health clinic</strong> for further assessment and support.</p>", "refreshOnChange": true}, {"key": "approved_html", "type": "content", "input": true, "tableView": false, "clearOnHide": false, "defaultValue": "<h3 class='text-green'>You are eligible to discuss PrEP</h3></br><p>PrEP (Pre-Exposure Prophylaxis) is a medication strategy that can reduce the risk of acquiring HIV by over <strong>99%</strong> when taken correctly.</p><p>TeleTest physicians provide:</p><ul><li>Professional, judgement-free HIV prevention care</li><li>Support in selecting the most appropriate PrEP option for you</li><li>Guidance on lab testing and prescription follow-up</li></ul><p><strong>We offer the following PrEP options:</strong></p><ul><li><strong>Daily PrEP with Truvada (TDF/FTC):</strong> Available for all patients at risk of HIV, including those engaging in receptive anal or vaginal sex.</li><li><strong>Daily PrEP with Descovy (TAF/FTC):</strong> Available only for cisgender men and transgender women. Not approved for those at risk from receptive vaginal sex.</li><li><strong>On-Demand PrEP (2-1-1 schedule using Truvada):</strong> Available to cisgender men who have sex with men and are able to plan their sexual activity at least 2 hours in advance. Not suitable for vaginal or front-hole exposure.</li></ul><p><strong>Next steps:</strong></p><ul><li>We will arrange baseline HIV and STI testing, along with kidney function labs.</li><li>Once your results are reviewed, a prescription will be issued and sent to your local pharmacy.</li></ul><p>For more information about our PrEP services and OHIP coverage, please visit <a href='https://docs.teletest.ca/prep' target='_blank'>this link</a>.</p>", "refreshOnChange": true}]}