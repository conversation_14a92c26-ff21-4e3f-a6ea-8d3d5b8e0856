{"components": [{"key": "had_vasectomy", "type": "radio", "input": true, "confirm_label": "Had a vasectomy:", "label": "Have you had a vasectomy?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "tableView": true, "optionsLabelPosition": "right"}, {"key": "vasectomy_timing", "type": "radio", "input": true, "label": "Has it been more than 3 months since your vasectomy?", "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = data.had_vasectomy === true;"}, {"key": "semen_analysis_reason", "type": "selectboxes", "input": true, "label": "Please select if any of the following reasons apply to you:", "values": [{"label": "Have been trying unsuccessfully to have a baby with my partner", "value": "trying_baby", "shortcut": ""}, {"label": "Have a medical condition or medication that affects fertility", "value": "medical_condition", "shortcut": ""}, {"label": "Have had a previous abnormal semen analysis test that requires follow-up", "value": "previous_abnormal_test", "shortcut": ""}, {"label": "Have a partner with a medical condition that impacts their fertility", "value": "partner_medical_condition", "shortcut": ""}, {"label": "Require for donation/surrogacy", "value": "donation_surrogacy", "shortcut": ""}], "inputType": "selectboxes", "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = data.had_vasectomy === false;"}, {"key": "none_of_the_above", "type": "checkbox", "input": true, "label": "None of the above", "customClass": "mt-n3", "defaultValue": false, "errors": {"custom": "Please select at least one option or 'None of the above'."}, "validate": {"custom": "valid = !!data.none_of_the_above || !!_.some(_.values(data.semen_analysis_reason));"}, "tableView": true, "customConditional": "show = data.had_vasectomy === false;"}, {"key": "no_indication", "type": "textfield", "input": true, "label": "No Indication:", "hidden": true, "disabled": true, "tableView": true, "clearOnHide": false, "defaultValue": "no_indication", "calculateValue": "value = (!data.had_vasectomy && !_.some(_.values(data.semen_analysis_reason))) ? 'no_indication' : '';", "refreshOnChange": true}, {"key": "contraindications", "type": "textfield", "input": true, "label": "Contraindications:", "hidden": true, "disabled": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "value = (data.had_vasectomy && data.vasectomy_timing === false) ? ['contraindications'] : [];", "refreshOnChange": true}]}