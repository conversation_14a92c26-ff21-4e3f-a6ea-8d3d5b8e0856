{"name": "insured-infos", "type": "form", "title": "Insured Infos", "display": "form", "components": [{"key": "insuredInfos", "type": "textfield", "input": true, "label": "insured", "hidden": true, "disabled": true, "multiple": true, "hideLabel": true, "tableView": false, "spellcheck": false, "clearOnHide": false, "calculateValue": {"_compact": {"merge": [[{"if": [{"<": [{"var": "data.recTestFreqMonths_B12"}, {"var": "data.testFreqMonths_B12"}]}, "B12", ""]}, {"if": [{"<": [{"var": "data.recTestFreqMonths_CELS"}, {"var": "data.testFreqMonths_CELS"}]}, "CELS", ""]}, {"if": [{"<": [{"var": "data.recTestFreqMonths_CELP"}, {"var": "data.testFreqMonths_CELP"}]}, "CELP", ""]}, {"if": [{"<": [{"var": "data.recTestFreqMonths_FBG"}, {"var": "data.testFreqMonths_FBG"}]}, "FBG", ""]}, {"if": [{"<": [{"var": "data.recTestFreqMonths_HBA1C"}, {"var": "data.testFreqMonths_HBA1C"}]}, "HBA1C", ""]}, {"if": [{"<": [{"var": "data.recTestFreqMonths_HCG"}, {"var": "data.testFreqMonths_HCG"}]}, "HCG", ""]}, {"if": [{"<": [{"var": "data.recTestFreqMonths_HEPB"}, {"var": "data.testFreqMonths_HEPB"}]}, "HEPB", ""]}, {"if": [{"<": [{"var": "data.recTestFreqMonths_HEPC"}, {"var": "data.testFreqMonths_HEPC"}]}, "HEPC", ""]}, {"if": [{"<": [{"var": "data.recTestFreqMonths_LP"}, {"var": "data.testFreqMonths_LP"}]}, "LP", ""]}, {"if": [{"<": [{"var": "data.recTestFreqMonths_TT"}, {"var": "data.testFreqMonths_TT"}]}, "TT", ""]}], {"if": [{"<": [{"var": "data.recTestFreqMonths_STD"}, {"var": "data.testFreqMonths_STD"}]}, ["CT", "GC", "TRICH", "HIV", "VDRL"], ""]}]}}, "clearOnRefresh": true}, {"key": "insured", "type": "textfield", "input": true, "label": "insured", "hidden": true, "disabled": true, "multiple": true, "hideLabel": true, "tableView": false, "spellcheck": false, "clearOnHide": false, "calculateValue": {"var": "data.assay_choices"}, "clearOnRefresh": true}, {"key": "uninsured", "type": "textfield", "input": true, "label": "Uninsured", "hidden": true, "disabled": true, "multiple": true, "hideLabel": true, "tableView": false, "spellcheck": false, "clearOnHide": false, "calculateValue": {"_difference": [{"var": "data.assay_choices"}, {"var": "data.insured"}]}, "clearOnRefresh": true}]}