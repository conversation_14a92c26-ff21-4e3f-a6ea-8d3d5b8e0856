{"components": [{"key": "current_age", "type": "radio", "input": true, "label": "Please specify your current age:", "inline": false, "values": [{"label": "<35", "value": true, "shortcut": ""}, {"label": "35-40", "value": false, "shortcut": ""}, {"label": "40+", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "current_pregnancy", "type": "radio", "input": true, "label": "Are you concerned you are currently pregnant?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "current_miscarriage", "type": "radio", "input": true, "label": "Are you concerned you are experiencing a miscarriage?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "indication_testing", "type": "radio", "input": true, "label": "Are you interested in egg freezing?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "duration_attempting_conception", "type": "radio", "input": true, "label": "How long have you been trying to get pregnant?", "inline": false, "values": [{"label": "< 3 months", "value": "<3_months", "shortcut": ""}, {"label": "3-6 months", "value": "3-6_months", "shortcut": ""}, {"label": "6-12 months", "value": "6-12_months", "shortcut": ""}, {"label": "12+ months", "value": "3-6_months", "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "amh_contraindication", "label": "Are you currently experiencing any of the following symptoms:", "values": [{"label": "Abdominal Pain", "value": "abdominal_pain", "shortcut": ""}, {"label": "Shortness of breath", "value": "dyspnea", "shortcut": ""}, {"label": "Feel lightheaded or faint", "value": "presy<PERSON><PERSON>", "shortcut": ""}, {"label": "Arm or leg swelling", "value": "limb_swelling", "shortcut": ""}, {"label": "Heart palpitations (slow or fast heart beat)", "value": "palpitations", "shortcut": ""}, {"label": "Coughing up blood", "value": "hemoptysis", "shortcut": ""}, {"label": "Erection lasting more than 2 hours", "value": "priapism", "shortcut": ""}, {"label": "Testicular lump", "value": "testicular_mass", "shortcut": ""}], "input": true, "inputType": "checkbox", "customConditional": "show = (data.diagnosis_testosterone_deficiency == 'yes' || data.on_trt_therapy_currently == 'yes') && data.on_anabolic_steroids == 'no';", "optionsLabelPosition": "right", "tableView": true, "type": "selectboxes"}, {"key": "no_testosterone_contraindications", "customClass": "mt-n3", "defaultValue": false, "input": true, "label": "None of the above", "tableView": true, "type": "checkbox", "customConditional": "show = data.biological_sex_birth == 'male' && data.diagnosis_testosterone_deficiency == 'yes' && data.on_anabolic_steroids == 'no';", "validate": {"custom": "valid = !!data.no_testosterone_contraindications || !!_.some(_.values(data.testosterone_contraindication));"}, "errors": {"custom": "required, or select a symptom."}}]}