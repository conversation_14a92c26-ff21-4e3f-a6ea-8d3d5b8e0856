{"name": "redFlags", "type": "form", "title": "Red Flags", "display": "form", "components": [{"key": "patch_vs_pill", "type": "radio", "input": true, "label": "Please select what you are interested in:", "inline": false, "values": [{"label": "Birth Control Pill", "value": "ocp_pill"}, {"label": "Birth Control Patch", "value": "birth_control_patch"}, {"label": "Delaying My Period", "value": "progesterone_pill"}], "tableView": false, "customConditional": "show = data.blood_pressure == 'normotensive';", "optionsLabelPosition": "right"}, {"key": "indication_social", "type": "radio", "input": true, "label": "Are you looking to delay your period for social reasons (i.e. travel, holidays, religious pilgrimage)?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "tableView": false, "customConditional": "show = data.blood_pressure == 'normotensive' && data.patch_vs_pill == 'progesterone_pill';", "optionsLabelPosition": "right"}, {"key": "contraindications", "type": "selectboxes", "input": true, "label": "Please check any of the following that apply to you:", "values": [{"label": "Pregnant", "value": "currently_pregnant", "shortcut": ""}, {"label": "Unsure if pregnant", "value": "possibly_pregnant", "shortcut": ""}, {"label": "Had a baby within the last 42 days", "value": "baby_recently", "shortcut": ""}, {"label": "High Blood Pressure", "value": "dx_htn", "shortcut": ""}, {"label": "Deep Vein Thrombosis (DVT)", "value": "dx_DVT", "shortcut": ""}, {"label": "Pulmonary Embolus (PE)", "value": "dx_PE", "shortcut": ""}, {"label": "Breast Cancer (Past or Present)", "value": "dx_breast_cancer", "shortcut": ""}, {"label": "I am 35+ and smoke tobacco products", "value": "dx_smoker", "shortcut": ""}, {"label": "A history of migraine headaches and experience vision changes, muscle weakness or numbness/tingling with your headaches", "value": "migraine_aura", "shortcut": "", "customConditional": "show = data.patch_vs_pill == 'progesterone_pill';"}], "inputType": "checkbox", "tableView": false, "customConditional": "show = data.blood_pressure == 'normotensive' && (data.patch_vs_pill == 'ocp_pill' || (data.patch_vs_pill == 'progesterone_pill' && !!data.indication_social));", "optionsLabelPosition": "right"}, {"key": "no_contraindications", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = data.no_contraindications == true || !!_.some(_.values(data.contraindications));"}, "tableView": false, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.blood_pressure == 'normotensive' && (data.patch_vs_pill == 'ocp_pill' || (data.patch_vs_pill == 'progesterone_pill' && !!data.indication_social));"}, {"key": "contraindication_keys", "type": "textfield", "input": true, "label": "Contraindication keys:", "hidden": true, "disabled": true, "multiple": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "value = _.concat(_.keys(_.pickBy(data.contraindications)), (data.blood_pressure != 'normotensive' ? [data.blood_pressure] : []));", "refreshOnChange": true}, {"key": "no_indications", "type": "textfield", "input": true, "label": "No Indication:", "hidden": true, "disabled": true, "multiple": false, "tableView": true, "clearOnHide": false, "defaultValue": true, "calculateValue": "value = data.patch_vs_pill == 'progesterone_pill' && !data.indication_social;", "refreshOnChange": true}, {"key": "rejected_html", "type": "content", "input": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.patch_vs_pill != 'progesterone_pill' ? '' : `<h3 class='text-red'>We cannot currently offer you a prescription for medication to suppress your menstrual cycle.</h3>` + (!!data.no_indications ? `<p>We can only offer medication to delay your period for social indications.</p><p>If you are looking to supress your menstrual cycles using birth control pills or patches, please select the appropriate panel 'Birth Control Prescription' <a href='https://teletest.ca/app/care/womens-health/' target='_blank'>(HERE)</a></p>` : `<ul><li>Some medical conditions prevent the safe use of NETA and MPA. Hormonal IUDs are an effective alternative for most women. If you would like a hormonal IUD, please out to a local health unit for a prescription.</li></ul>If you are looking to supress your menstrual cycles using birth control pills or patches, please select the appropriate panel 'Birth Control Prescription'<a href='https://teletest.ca/app/care/womens-health/' target='_blank'>(HERE)</a>`);"}]}