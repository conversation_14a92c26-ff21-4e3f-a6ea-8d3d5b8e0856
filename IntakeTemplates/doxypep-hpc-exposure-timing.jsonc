{"components": [{"key": "doxypep_instructions_header", "html": "<h2 class='text-center'>Doxy-PEP Intake Form</h2><p>Please complete this questionnaire to help us determine whether Doxycycline <strong>Post-Exposure Prophylaxis (Doxy-PEP)</strong> is appropriate for you after a recent sexual exposure.</p>", "type": "content", "input": false, "label": "Instructions", "tableView": false, "refreshOnChange": false}, {"key": "general_information_header", "html": "<h3>Intake History</h3>", "type": "content", "input": false, "label": "General Information", "tableView": false, "refreshOnChange": false}, {"key": "exposure_date_time", "type": "datetime", "input": true, "label": "When did the potential sexual exposure occur? (Doxy-PEP should be taken within 72 hours)", "format": "MMMM dd, yyyy hh:mm a", "widget": {"mode": "single", "type": "calendar", "format": "MMMM dd, yyyy hh:mm a", "allowInput": true, "enableTime": true, "noCalendar": false, "displayInTimezone": "viewer", "useLocaleSettings": true}, "validate": {"required": true}, "tableView": true, "enableDate": true, "enableTime": true, "confirm_label": "Date and time of exposure:"}, {"key": "time_since_exposure", "type": "textfield", "input": true, "label": "Time since exposure (hours):", "disabled": true, "tableView": true, "confirm_label": "Time since exposure:", "calculateValue": "value = data.exposure_date_time ? Math.floor((new Date() - new Date(data.exposure_date_time)) / (1000 * 60 * 60)) : '';"}, {"key": "doxypep_use_heading", "html": "<h3>Doxy-PEP Use</h3>", "type": "content", "input": false, "tableView": false}, {"key": "past_doxypep_use", "type": "selectboxes", "input": true, "label": "Have you ever taken Doxy-PEP (a single 200 mg doxycycline dose after a sexual exposure)?", "values": [{"label": "Yes - I have taken Doxy-PEP before", "value": "doxypep", "shortcut": ""}], "tableView": true, "confirm_label": "Past Doxy-PEP use:", "optionsLabelPosition": "right"}, {"key": "never_used_doxypep", "type": "checkbox", "input": true, "label": "Never taken Doxy-PEP", "errors": {"custom": "required, or select an option."}, "validate": {"custom": "valid = !!data.never_used_doxypep || _.some(_.values(data.past_doxypep_use));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "last_dose_doxypep", "data": {"values": [{"label": "Within 24 hours", "value": "within_24h"}, {"label": "1 day ago", "value": "1_day"}, {"label": "2 days ago", "value": "2_days"}, {"label": "3 days ago", "value": "3_days"}, {"label": "4 days ago", "value": "4_days"}, {"label": "5 days ago", "value": "5_days"}, {"label": "6 days ago", "value": "6_days"}, {"label": "7 days ago (1 week)", "value": "7_days"}, {"label": "1 week ago", "value": "1_week"}, {"label": "2 weeks ago", "value": "2_weeks"}, {"label": "3 weeks ago", "value": "3_weeks"}, {"label": "4 weeks ago", "value": "4_weeks"}, {"label": "5 weeks ago", "value": "5_weeks"}, {"label": "6 weeks ago", "value": "6_weeks"}, {"label": "1 month ago", "value": "1_month"}, {"label": "2 months ago", "value": "2_months"}, {"label": "3 months ago", "value": "3_months"}, {"label": "4 months ago", "value": "4_months"}, {"label": "5 months ago", "value": "5_months"}, {"label": "6 months ago", "value": "6_months"}, {"label": "7 months ago", "value": "7_months"}, {"label": "8 months ago", "value": "8_months"}, {"label": "9 months ago", "value": "9_months"}, {"label": "10 months ago", "value": "10_months"}, {"label": "11 months ago", "value": "11_months"}, {"label": "12 months ago", "value": "12_months"}, {"label": "More than 1 year ago", "value": "more_than_1_year"}]}, "type": "select", "input": true, "label": "When did you last take Doxy-PEP?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Last Doxy-PEP dose:", "customConditional": "show = data.past_doxypep_use && data.past_doxypep_use.doxypep"}, {"key": "doxypep_tolerance", "type": "radio", "input": true, "label": "How did you tolerate your last Doxy-PEP dose?", "values": [{"label": "No side effects", "value": "none"}, {"label": "Mild side effects", "value": "mild"}, {"label": "Moderate but tolerable side effects", "value": "moderate_tolerable"}, {"label": "Severe side effects", "value": "severe", "adminFlag": true}], "validate": {"required": true}, "tableView": true, "confirm_label": "Tolerance to last Doxy-PEP dose:", "customConditional": "show = data.past_doxypep_use && data.past_doxypep_use.doxypep"}, {"key": "sti_test_after_doxypep", "type": "radio", "input": true, "label": "Since your last dose of Doxy-PEP, have you been tested for chlamydia, gonorrhea, or syphilis (and received results)?", "values": [{"label": "Completed STI testing - all results negative", "value": "tested_negative"}, {"label": "Completed STI testing - tested POSITIVE for at least one STI", "value": "tested_positive", "adminFlag": true}, {"label": "Have not completed STI testing", "value": "no_testing"}], "validate": {"required": true}, "tableView": true, "confirm_label": "STI testing after last Doxy-PEP dose:", "customConditional": "show = ['within_24h','1_day','2_days','3_days','4_days','5_days','6_days','7_days','1_week','2_weeks','3_weeks','4_weeks','5_weeks','6_weeks','1_month','2_months','3_months','4_months','5_months','6_months','7_months','8_months','9_months','10_months','11_months','12_months'].includes(data.last_dose_doxypep);", "optionsLabelPosition": "right"}, {"key": "medication_safety_header", "type": "content", "input": false, "label": "Medication Safety", "html": "<h3>Medication Safety</h3>", "tableView": false, "refreshOnChange": false}, {"key": "antibiotic_allergies", "type": "selectboxes", "input": true, "label": "Do you have a known allergy to any of the following antibiotics? (Select all that apply)", "values": [{"label": "Doxycycline", "value": "doxycycline"}, {"label": "Tetracycline", "value": "tetracycline"}, {"label": "Minocycline", "value": "minocycline"}], "adminFlag": true, "confirm_label": "Antibiotic Allergies:", "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = data.proceed_with_doxypep;"}, {"key": "none_of_the_above_antibiotic_allergies", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select at least one allergy."}, "validate": {"custom": "valid = !!data.none_of_the_above_antibiotic_allergies || _.some(_.values(data.antibiotic_allergies));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.proceed_with_doxypep;"}, {"key": "allergies_not_present", "type": "textfield", "input": true, "label": "I DO NOT have the following antibiotic allergies:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "I do not have the following antibiotic allergies:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.antibiotic_allergies, _.negate(_.identity))), _.startCase), ', '), /_/g, ' ');", "customConditional": "show = data.proceed_with_doxypep;"}, {"key": "doxy_contraindications", "type": "selectboxes", "input": true, "label": "Do any of the following apply to you? (Select all that apply)", "values": [{"label": "Currently pregnant or unsure if pregnant", "value": "pregnancy"}, {"label": "Breastfeeding", "value": "breastfeeding"}, {"label": "Have liver disease", "value": "liver_disease"}, {"label": "Currently taking Accutane or Epuris / Clarus (i.e. isotretinoin)", "value": "isotretinoin"}], "adminFlag": true, "confirm_label": "Health Conditions Present:", "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = data.proceed_with_doxypep;"}, {"key": "none_of_the_above_doxy_contraindications", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a contraindication."}, "validate": {"custom": "valid = !!data.none_of_the_above_doxy_contraindications || _.some(_.values(data.doxy_contraindications));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.proceed_with_doxypep;"}, {"key": "contraindications_not_present", "type": "textfield", "input": true, "label": "The following health conditions do not apply to me:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "Health conditions not present:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.doxy_contraindications, _.negate(_.identity))), _.startCase), ', '), /_/g, ' ');", "customConditional": "show = data.proceed_with_doxypep;"}, {"key": "side_effects_header", "type": "content", "input": false, "label": "Side Effects", "html": "<h3>Side Effects</h3>", "tableView": false, "refreshOnChange": false}, {"key": "side_effects_doxycycline", "type": "content", "input": false, "html": "<p>Like any medication, doxycycline can cause side effects. Most are mild and temporary.</p><strong>Doxycycline&nbsp;(single&nbsp;200&nbsp;mg dose)</strong><ul><li>Stomach upset / nausea - <em>common</em> (≈ 10-20 %)</li><li>Diarrhea - <em>common</em> (≈ 5 %)</li><li>Photosensitivity (sun-burn-like rash) - <em>common</em> (≈ 4 %)</li><li>Esophageal irritation / heartburn - <em>uncommon</em></li><li>Headache - <em>uncommon</em></li><li>Yeast infection - <em>rare</em></li><li>Severe allergic reaction (hives, facial swelling, trouble breathing) - <em>very rare</em>; seek care immediately</li></ul>", "customConditional": "show = data.proceed_with_doxypep;"}, {"key": "side_effects_doxy_ack", "type": "radio", "input": true, "label": "I have reviewed the side-effects above:", "values": [{"label": "✔ I understand these risks and wish to proceed", "value": "understand_proceed"}, {"label": "✖ I'm unsure / need more counselling before proceeding", "value": "need_counselling"}], "validate": {"required": true}, "tableView": true, "customClass": "mt-3", "optionsLabelPosition": "right", "confirm_label": "Doxycycline side-effects:", "customConditional": "show = data.proceed_with_doxypep;"}, {"key": "confirmations_header", "type": "content", "input": false, "label": "Confirmations", "html": "<h3>Confirmations</h3>", "tableView": false, "refreshOnChange": false}, {"key": "med_info_accuracy_ack", "type": "checkbox", "input": true, "label": "I confirm that the medical conditions, current medications, and drug allergies I have provided are complete and accurate. I understand TeleTest physicians will rely on this information when making prescribing recommendations.", "validate": {"required": true}, "errors": {"required": "Please confirm the accuracy of your medical information before continuing."}, "tableView": true, "confirm_label": "My medical conditions, current medications, and drug allergies are complete and accurate:", "defaultValue": false, "customClass": "mt-2"}, {"key": "proceed_with_doxypep", "type": "checkbox", "confirm_label": "I wish to proceed with Doxy-PEP treatment:", "input": true, "label": "I wish to proceed with a single 200 mg dose of doxycycline (Doxy-PEP) to help prevent chlamydia, gonorrhea, and syphilis after my recent exposure.", "errors": {"required": "You must confirm your decision to proceed before continuing."}, "validate": {"required": true}, "tableView": true, "defaultValue": false}]}