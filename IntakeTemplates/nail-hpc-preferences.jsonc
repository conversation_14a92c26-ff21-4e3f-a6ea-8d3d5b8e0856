{"components": [{"key": "onychomycosis_preferences_header", "type": "content", "input": false, "label": "Content", "html": "<h2>Nail Fungus - Treatment & Testing Options</h2><p>Thick or discoloured nails are <strong>not always</strong> due to fungus. Past injuries, tight shoes or work boots, and repeated micro-trauma can make nails look infected when they aren't, so medicine may not help.</p><p>Confirming the cause is easy:</p><ol><li>Pick up a free nail collection kit at any <strong>LifeLabs</strong> location.</li><li>Clip a small piece of the affected nail into the kit.</li><li>Drop it back at the lab - results are ready in about <strong>4 weeks</strong>.</li></ol><p>Please choose one of the plans below:</p>"}, {"key": "antifungal_plan_choice", "type": "radio", "input": true, "label": "Which option best suits you?", "confirm_label": "Onychomycosis Plan:", "values": [{"label": "Treatment only - start Jublia® topical solution now", "value": "treatment_only"}, {"label": "Testing + Treatment - start Jublia® now and adjust if test negative", "value": "testing_plus_treatment"}, {"label": "Testing only - treatment offered later if test positive", "value": "testing_only"}], "validate": {"required": true}, "tableView": true}, {"key": "jublia_side_effects_header", "type": "content", "input": false, "label": "Content", "customConditional": "show = data.antifungal_plan_choice === 'treatment_only' || data.antifungal_plan_choice === 'testing_plus_treatment';", "html": "<h3>Review Jublia® Side-Effects</h3><ul><li><strong>Ingrown toenail</strong> - 2.3%</li><li><strong>Skin irritation / redness </strong> - 2.2%</li><li><strong>Blisters </strong> - 1.6%</li><li><strong>Pain, burning or stinging</strong> - 1.1%</li><li><strong>Allergic reaction</strong> - &lt;1% (rare)</li></ul>"}, {"key": "jublia_side_effects_ack", "type": "checkbox", "input": true, "label": "I have read and understand these potential side-effects.", "tableView": false, "validate": {"required": true}, "customConditional": "show = data.antifungal_plan_choice === 'treatment_only' || data.antifungal_plan_choice === 'testing_plus_treatment';"}, {"key": "jublia_contraindications", "type": "selectboxes", "input": true, "label": "Do any of the following apply to you?", "values": [{"label": "Allergy to efinaconazole or other antifungals", "value": "azole_allergy", "shortcut": ""}, {"label": "Pregnant or breastfeeding", "value": "pregnancy", "shortcut": ""}, {"label": "Open wounds or ulcer around the affected nail", "value": "open_wound", "shortcut": ""}], "inputType": "checkbox", "tableView": true, "optionsLabelPosition": "right", "adminFlag": true, "confirm_label": "Contraindications:", "customConditional": "show = data.antifungal_plan_choice === 'treatment_only' || data.antifungal_plan_choice === 'testing_plus_treatment';"}, {"key": "no_jublia_contraindications", "type": "checkbox", "input": true, "label": "None of the above", "customClass": "mt-n3", "defaultValue": false, "errors": {"custom": "Select a contraindication, or confirm none."}, "validate": {"custom": "valid = !!data.no_jublia_contraindications || !!_.some(_.values(data.jublia_contraindications));"}, "tableView": true, "customConditional": "show = data.antifungal_plan_choice === 'treatment_only' || data.antifungal_plan_choice === 'testing_plus_treatment';"}, {"key": "jublia_contraindications_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following contraindications:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "Contraindications (none):", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.jublia_contraindications, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "rxts", "type": "textfield", "input": true, "label": "Rx Templates:", "hidden": true, "disabled": true, "multiple": true, "adminFlag": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "value = (data.antifungal_plan_choice === 'treatment_only' || data.antifungal_plan_choice === 'testing_plus_treatment') ? ['jublia-10p'] : [];", "refreshOnChange": true}]}