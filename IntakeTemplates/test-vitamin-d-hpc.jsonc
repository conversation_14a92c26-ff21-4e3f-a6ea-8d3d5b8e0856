{"components": [{"key": "heading_main_vitamin_d", "html": "<h1><center><strong>Vitamin D Prescription & Testing</strong></h1><center><p>To expedite your care, please answer the following questions to the best of your abilities. &nbsp;We ask these questions to provide the best possible care.&nbsp;</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "heading_medical_indication", "html": "<h4>Indication for Testing&nbsp;</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "screening_indication", "type": "selectboxes", "input": true, "label": "Please clarify why you are interested in preventative health screening:", "values": [{"label": "Test Vitamin D levels", "value": "test_vitamin_d_levels", "shortcut": ""}, {"label": "Obtain Precription for Vitamin D", "value": "prescription_vitamin_d", "shortcut": ""}, {"label": "I am taking medications that require monitoring", "value": "medication_monitoring", "shortcut": ""}, {"label": "I have symptoms", "value": "current_symptoms", "shortcut": ""}, {"label": "Other", "value": "other_reason", "shortcut": ""}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "confirm_label": "Reason(s) for testing:", "optionsLabelPosition": "right"}, {"key": "other_screening_indication", "type": "textarea", "input": true, "label": "Please state your “other” reason(s) for seeking preventative health screening:", "tableView": true, "autoExpand": false, "confirm_label": "Other reason(s) for testing:", "customConditional": "show = data.screening_indication.other_reason;"}, {"key": "confirm_no_symptoms", "type": "radio", "input": true, "label": "Please confirm that you do not have any symptoms at the present time:", "values": [{"label": "No symptoms", "value": "no_symptoms"}, {"label": "I have symptoms and wish to update my selection", "value": "have_symptoms"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Current symptoms:", "customConditional": "show = !data.screening_indication?.current_symptoms;"}, {"key": "heading_constitutional_symptoms", "html": "<h2>General Symptoms</h2><p>These are general symptoms that may affect your overall health. Please let us know if you are experiencing any of the following.</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.screening_indication?.current_symptoms || data.confirm_no_symptoms === 'have_symptoms';"}, {"key": "constitutional_symptoms", "type": "selectboxes", "input": true, "label": "Do you have any of the following general symptoms?", "values": [{"label": "Have fevers without having cold symptoms (e.g., runny nose, sore throat)", "value": "fever", "shortcut": ""}, {"label": "Losing weight without dieting or exercising", "value": "weight_loss", "shortcut": ""}, {"label": "Feeling unusually tired", "value": "fatigue", "shortcut": ""}, {"label": "Sweating at night", "value": "night_sweats", "shortcut": ""}, {"label": "Feeling unusually cold", "value": "chills", "shortcut": ""}, {"label": "Not feeling hungry", "value": "loss_of_appetite", "shortcut": ""}], "inputType": "checkbox", "adminFlag": true, "tableView": true, "confirm_label": "Constitutional symptoms:", "optionsLabelPosition": "right", "customConditional": "show = data.screening_indication?.current_symptoms || data.confirm_no_symptoms === 'have_symptoms';"}, {"key": "none_of_the_above_constitutional_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = !!data.none_of_the_above_constitutional_symptoms || _.some(_.values(data.constitutional_symptoms));"}, "adminFlag": true, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.screening_indication?.current_symptoms || data.confirm_no_symptoms === 'have_symptoms';"}, {"key": "constitutional_symptoms_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following general symptoms:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": true, "tableView": false, "spellcheck": false, "clearOnHide": true, "confirm_label": "General symptoms not present:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.constitutional_symptoms, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "heading_heart_symptoms", "html": "<h2>Heart Symptoms</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.screening_indication?.current_symptoms || data.confirm_no_symptoms === 'have_symptoms';"}, {"key": "cardiovascular_symptoms", "type": "selectboxes", "input": true, "label": "Do you have any of the following heart-related symptoms?", "values": [{"label": "Chest pain, tightness or discomfort", "value": "chest_pain"}, {"label": "Palpitations", "value": "palpitations"}, {"label": "Swelling in the legs, ankles, or feet", "value": "swelling"}, {"label": "Dizziness or fainting", "value": "dizziness"}], "tableView": true, "customConditional": "show = data.screening_indication?.current_symptoms || data.confirm_no_symptoms === 'have_symptoms';", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_cardiovascular_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "validate": {"custom": "valid = !!data.none_of_the_above_cardiovascular_symptoms || _.some(_.values(data.cardiovascular_symptoms));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.screening_indication?.current_symptoms || data.confirm_no_symptoms === 'have_symptoms';"}, {"key": "heading_chest_pain", "html": "<h4>Details About Your Chest Pain</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.cardiovascular_symptoms?.chest_pain;"}, {"key": "chest_pain_onset", "data": {"values": [{"label": "Less than 1 hour ago", "value": "<1_hour"}, {"label": "1-3 hours ago", "value": "1-3_hours"}, {"label": "4-6 hours ago", "value": "4-6_hours"}, {"label": "6-12 hours ago", "value": "6-12_hours"}, {"label": "12-24 hours ago", "value": "12-24_hours"}, {"label": "1-2 days ago", "value": "1-2_days"}, {"label": "3-7 days ago", "value": "3-7_days"}, {"label": "1-2 weeks ago", "value": "1-2_weeks"}, {"label": "2-4 weeks ago", "value": "2-4_weeks"}, {"label": "1-3 months ago", "value": "1-3_months"}, {"label": "4-6 months ago", "value": "4-6_months"}, {"label": "7-12 months ago", "value": "7-12_months"}, {"label": "More than 1 year ago", "value": ">1_year"}, {"label": "Ongoing since as long as I can remember", "value": "ongoing"}, {"label": "I'm not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "When did your chest pain start?", "widget": "html5", "validate": {"required": true}, "tableView": true, "placeholder": "Select the time range", "customConditional": "show = data.cardiovascular_symptoms?.chest_pain;"}, {"key": "chest_pain_palliation", "type": "selectboxes", "input": true, "label": "What seems to make your chest pain better or worse?", "values": [{"label": "It feels better when I rest", "value": "better_with_rest"}, {"label": "It gets worse when I'm active or exercising", "value": "worse_with_exertion"}, {"label": "It improves after I take specific medication, like pain relievers or heart medication", "value": "better_with_medication"}, {"label": "It feels better when I change position or lie down", "value": "better_with_position_change"}, {"label": "It gets worse after eating or drinking", "value": "worse_after_eating"}, {"label": "It gets worse when I'm stressed or anxious", "value": "worse_with_stress"}, {"label": "It doesn't change no matter what I do", "value": "unchanged"}], "tableView": true, "customConditional": "show = data.cardiovascular_symptoms?.chest_pain;"}, {"key": "chest_pain_prior_history", "type": "radio", "input": true, "label": "Have you ever had chest pain like this before?", "values": [{"label": "Yes, I've had similar pain before", "value": "yes"}, {"label": "No, this is the first time I've experienced this", "value": "no"}, {"label": "I'm not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.cardiovascular_symptoms?.chest_pain;"}, {"key": "chest_pain_prior_diagnosis", "type": "selectboxes", "input": true, "label": "If you've had similar chest pain before, were you given any of these diagnoses?", "values": [{"label": "Heart attack", "value": "heart_attack"}, {"label": "<PERSON><PERSON> (chest pain related to heart disease)", "value": "angina"}, {"label": "Heartburn or acid reflux", "value": "heartburn"}, {"label": "Muscle strain or injury", "value": "muscle_strain"}, {"label": "Panic attack or anxiety-related chest pain", "value": "panic_attack"}, {"label": "Pleurisy (inflammation of the lining of the lungs)", "value": "pleurisy"}, {"label": "Costochondritis (inflammation of the rib cartilage)", "value": "costochondritis"}, {"label": "No diagnosis was provided", "value": "no_diagnosis"}, {"label": "I don't know", "value": "unknown"}], "tableView": true, "customConditional": "show = data.cardiovascular_symptoms?.chest_pain && data.chest_pain_prior_history === 'yes';"}, {"key": "chest_pain_description", "type": "selectboxes", "input": true, "label": "How would you describe your chest pain? (Select all that apply)", "values": [{"label": "Tightness or squeezing sensation", "value": "tightness"}, {"label": "Sharp or stabbing pain", "value": "sharp"}, {"label": "Burning sensation", "value": "burning"}, {"label": "Dull ache or heaviness", "value": "dull_ache"}, {"label": "Pressure-like discomfort", "value": "pressure"}, {"label": "Radiates to the arms, jaw, or back", "value": "radiating"}], "tableView": true, "customConditional": "show = data.cardiovascular_symptoms?.chest_pain;"}, {"key": "heading_palpitations", "html": "<h4>Details About Your Palpitations</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.cardiovascular_symptoms?.palpitations;"}, {"key": "palpitations_onset", "data": {"values": [{"label": "Less than 1 hour ago", "value": "<1_hour"}, {"label": "1-3 hours ago", "value": "1-3_hours"}, {"label": "4-6 hours ago", "value": "4-6_hours"}, {"label": "6-12 hours ago", "value": "6-12_hours"}, {"label": "12-24 hours ago", "value": "12-24_hours"}, {"label": "1-2 days ago", "value": "1-2_days"}, {"label": "3-7 days ago", "value": "3-7_days"}, {"label": "1-2 weeks ago", "value": "1-2_weeks"}, {"label": "2-4 weeks ago", "value": "2-4_weeks"}, {"label": "1-3 months ago", "value": "1-3_months"}, {"label": "4-6 months ago", "value": "4-6_months"}, {"label": "7-12 months ago", "value": "7-12_months"}, {"label": "More than 1 year ago", "value": ">1_year"}, {"label": "Ongoing since as long as I can remember", "value": "ongoing"}, {"label": "I'm not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "When did you first notice the palpitations?", "widget": "html5", "validate": {"required": true}, "tableView": true, "placeholder": "Select the time range", "customConditional": "show = data.cardiovascular_symptoms?.palpitations;"}, {"key": "palpitations_description", "type": "selectboxes", "input": true, "label": "How would you describe your palpitations? (Select all that apply)", "values": [{"label": "Feels like my heart is racing", "value": "heart_racing"}, {"label": "Feels like my heart is skipping beats", "value": "skipping_beats"}, {"label": "Feels like fluttering in my chest", "value": "fluttering"}, {"label": "Feels like pounding or thumping", "value": "pounding"}, {"label": "Uncomfortable but not painful", "value": "uncomfortable"}, {"label": "Accompanied by chest pain or dizziness", "value": "associated_symptoms"}], "tableView": true, "customConditional": "show = data.cardiovascular_symptoms?.palpitations;"}, {"key": "palpitations_triggers", "type": "selectboxes", "input": true, "label": "Have you noticed any triggers for your palpitations? (Select all that apply)", "values": [{"label": "Physical activity or exercise", "value": "exercise"}, {"label": "Stress or anxiety", "value": "stress"}, {"label": "Caffeine or energy drinks", "value": "caffeine"}, {"label": "Alcohol consumption", "value": "alcohol"}, {"label": "Medications or supplements", "value": "medications"}, {"label": "None of these seem to trigger it", "value": "none"}], "tableView": true, "customConditional": "show = data.cardiovascular_symptoms?.palpitations;"}, {"key": "palpitations_resolution", "type": "selectboxes", "input": true, "label": "What seems to make your palpitations go away? (Select all that apply)", "values": [{"label": "Resting or sitting down", "value": "rest"}, {"label": "Practicing relaxation techniques (e.g., deep breathing)", "value": "relaxation"}, {"label": "Stopping physical activity", "value": "stopping_exercise"}, {"label": "Taking prescribed medications", "value": "taking_medications"}, {"label": "Avoiding caffeine", "value": "avoiding_caffeine"}, {"label": "Avoiding alcohol", "value": "avoiding_alcohol"}, {"label": "They disappear on their own without any intervention", "value": "spontaneous_resolution"}, {"label": "Nothing seems to make them go away", "value": "nothing"}], "tableView": true, "customConditional": "show = data.cardiovascular_symptoms?.palpitations;"}, {"key": "heading_swelling", "html": "<h4>Details About Your Swelling</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.cardiovascular_symptoms?.swelling;"}, {"key": "swelling_location", "type": "selectboxes", "input": true, "label": "Where have you noticed swelling? (Select all that apply)", "values": [{"label": "Legs", "value": "legs"}, {"label": "<PERSON><PERSON>", "value": "ankles"}, {"label": "Feet", "value": "feet"}, {"label": "Abdomen", "value": "abdomen"}, {"label": "Face", "value": "face"}], "tableView": true, "customConditional": "show = data.cardiovascular_symptoms?.swelling;"}, {"key": "swelling_time", "data": {"values": [{"label": "Less than 24 hours ago", "value": "<24_hours"}, {"label": "1-2 days ago", "value": "1-2_days"}, {"label": "3-7 days ago", "value": "3-7_days"}, {"label": "1-2 weeks ago", "value": "1-2_weeks"}, {"label": "2-4 weeks ago", "value": "2-4_weeks"}, {"label": "More than 1 month ago", "value": "1_month+"}]}, "type": "select", "input": true, "label": "When did you first notice the swelling?", "widget": "html5", "validate": {"required": true}, "tableView": true, "placeholder": "Select the time range", "customConditional": "show = data.cardiovascular_symptoms?.swelling;"}, {"key": "swelling_triggers", "type": "selectboxes", "input": true, "label": "Have you noticed any triggers for your swelling?", "values": [{"label": "Prolonged standing or sitting", "value": "prolonged_standing"}, {"label": "High salt intake", "value": "high_salt"}, {"label": "Heat or hot weather", "value": "hot_weather"}, {"label": "Alcohol", "value": "alcohol"}, {"label": "Injury or trauma", "value": "injury"}, {"label": "None of these", "value": "none"}], "tableView": true, "customConditional": "show = data.cardiovascular_symptoms?.swelling;"}, {"key": "heading_dizziness", "html": "<h4>Details About Your Dizziness</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.cardiovascular_symptoms?.dizziness;"}, {"key": "dizziness_onset", "data": {"values": [{"label": "Less than 1 hour ago", "value": "<1_hour"}, {"label": "1-3 hours ago", "value": "1-3_hours"}, {"label": "4-6 hours ago", "value": "4-6_hours"}, {"label": "6-12 hours ago", "value": "6-12_hours"}, {"label": "12-24 hours ago", "value": "12-24_hours"}, {"label": "1-2 days ago", "value": "1-2_days"}, {"label": "3-7 days ago", "value": "3-7_days"}, {"label": "1-2 weeks ago", "value": "1-2_weeks"}, {"label": "2-4 weeks ago", "value": "2-4_weeks"}, {"label": "More than 1 month ago", "value": "1_month+"}]}, "type": "select", "input": true, "label": "When did you first experience dizziness?", "widget": "html5", "validate": {"required": true}, "tableView": true, "placeholder": "Select the time range", "customConditional": "show = data.cardiovascular_symptoms?.dizziness;"}, {"key": "dizziness_description", "type": "selectboxes", "input": true, "label": "How would you describe your dizziness?", "values": [{"label": "Feeling lightheaded or faint", "value": "lightheaded"}, {"label": "Spinning sensation (vertigo)", "value": "spinning"}, {"label": "Unsteady or off-balance", "value": "unsteady"}, {"label": "Nausea associated with dizziness", "value": "nausea"}], "tableView": true, "customConditional": "show = data.cardiovascular_symptoms?.dizziness;"}, {"key": "cardio_specialist_consultation", "type": "radio", "input": true, "label": "Have you seen a healthcare specialist for an examination or consultation about these symptoms?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "I don't remember", "value": "dont_remember"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = Object.values(data.cardiovascular_symptoms || {}).some(value => value);"}, {"key": "heading_breathing_symptoms", "html": "<h2>Breathing Symptoms</h2><p>Please share if you are experiencing any symptoms related to breathing.</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.screening_indication?.current_symptoms || data.confirm_no_symptoms === 'have_symptoms';"}, {"key": "respiratory_symptoms", "type": "selectboxes", "input": true, "label": "Do you have any of the following symptoms?", "values": [{"label": "Coughing", "value": "cough"}, {"label": "Shortness of breath", "value": "shortness_of_breath"}, {"label": "Wheezing", "value": "wheezing"}, {"label": "Chest tightness", "value": "chest_tightness"}], "tableView": true, "customConditional": "show = data.screening_indication?.current_symptoms || data.confirm_no_symptoms === 'have_symptoms';", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_respiratory_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "validate": {"custom": "valid = !!data.none_of_the_above_respiratory_symptoms || _.some(_.values(data.respiratory_symptoms));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.screening_indication?.current_symptoms || data.confirm_no_symptoms === 'have_symptoms';"}, {"key": "heading_cough_details", "html": "<h4>Details About Your Cough</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.respiratory_symptoms?.cough;"}, {"key": "cough_onset", "data": {"values": [{"label": "Less than 24 hours ago", "value": "<24_hours"}, {"label": "1-3 days ago", "value": "1-3_days"}, {"label": "4-7 days ago", "value": "4-7_days"}, {"label": "1-2 weeks ago", "value": "1-2_weeks"}, {"label": "2-4 weeks ago", "value": "2-4_weeks"}, {"label": "More than 1 month ago", "value": ">1_month"}]}, "type": "select", "input": true, "label": "When did your cough start?", "widget": "html5", "validate": {"required": true}, "tableView": true, "placeholder": "Select the time range", "customConditional": "show = data.respiratory_symptoms?.cough;"}, {"key": "cough_description", "type": "selectboxes", "input": true, "label": "How would you describe your cough?", "values": [{"label": "Dry cough", "value": "dry_cough"}, {"label": "Wet or productive cough", "value": "productive_cough"}, {"label": "Coughing up blood", "value": "blood_cough"}, {"label": "Persistent or chronic cough", "value": "persistent_cough"}], "tableView": true, "customConditional": "show = data.respiratory_symptoms?.cough;"}, {"key": "cough_triggers", "type": "selectboxes", "input": true, "label": "Have you noticed any triggers for your cough?", "values": [{"label": "Cold air", "value": "cold_air"}, {"label": "Physical activity", "value": "physical_activity"}, {"label": "Dust or allergens", "value": "allergens"}, {"label": "None of these seem to trigger it", "value": "no_triggers"}], "tableView": true, "customConditional": "show = data.respiratory_symptoms?.cough;"}, {"key": "cough_consultation", "type": "radio", "input": true, "label": "Have you consulted a healthcare provider about your cough?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "I don't remember", "value": "dont_remember"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.respiratory_symptoms?.cough;"}, {"key": "heading_wheezing_details", "html": "<h4>Details About Your Wheezing</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.respiratory_symptoms?.wheezing;"}, {"key": "wheezing_onset", "data": {"values": [{"label": "Less than 24 hours ago", "value": "<24_hours"}, {"label": "1-3 days ago", "value": "1-3_days"}, {"label": "4-7 days ago", "value": "4-7_days"}, {"label": "1-2 weeks ago", "value": "1-2_weeks"}, {"label": "2-4 weeks ago", "value": "2-4_weeks"}, {"label": "More than 1 month ago", "value": ">1_month"}]}, "type": "select", "input": true, "label": "When did your wheezing start?", "widget": "html5", "validate": {"required": true}, "tableView": true, "placeholder": "Select the time range", "customConditional": "show = data.respiratory_symptoms?.wheezing;"}, {"key": "wheezing_description", "type": "selectboxes", "input": true, "label": "How would you describe your wheezing?", "values": [{"label": "Occurs with physical activity", "value": "with_activity"}, {"label": "Occurs at rest", "value": "at_rest"}, {"label": "Accompanied by shortness of breath", "value": "with_shortness_of_breath"}, {"label": "Happens mostly at night", "value": "night_wheezing"}, {"label": "Triggered by allergens (e.g., pollen, dust)", "value": "allergen_triggered"}], "tableView": true, "customConditional": "show = data.respiratory_symptoms?.wheezing;"}, {"key": "wheezing_triggers", "type": "selectboxes", "input": true, "label": "Have you noticed any triggers for your wheezing?", "values": [{"label": "Physical activity or exertion", "value": "physical_activity"}, {"label": "Cold air", "value": "cold_air"}, {"label": "Allergens (e.g., pollen, pet dander)", "value": "allergens"}, {"label": "Stress or anxiety", "value": "stress"}, {"label": "Dust or smoke exposure", "value": "dust_smoke"}, {"label": "Respiratory infections (e.g., colds, flu)", "value": "respiratory_infections"}, {"label": "Strong odors or fumes (e.g., perfumes, cleaning products)", "value": "strong_odors"}, {"label": "Weather changes (e.g., humidity, temperature shifts)", "value": "weather_changes"}, {"label": "None of these seem to trigger it", "value": "no_triggers"}], "tableView": true, "customConditional": "show = data.respiratory_symptoms?.wheezing;"}, {"key": "wheezing_alleviators", "type": "selectboxes", "input": true, "label": "What helps reduce or stop your wheezing?", "values": [{"label": "Resting", "value": "resting"}, {"label": "Using an inhaler or medication", "value": "inhaler"}, {"label": "Avoiding allergens or irritants", "value": "avoiding_triggers"}, {"label": "None of these help", "value": "none"}], "tableView": true, "customConditional": "show = data.respiratory_symptoms?.wheezing;"}, {"key": "wheezing_consultation", "type": "radio", "input": true, "label": "Have you consulted a healthcare provider about your wheezing?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "I don't remember", "value": "dont_remember"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.respiratory_symptoms?.wheezing;"}, {"key": "asthma_copd_diagnosis", "type": "radio", "input": true, "label": "Have you been diagnosed with asthma or chronic obstructive pulmonary disease (COPD)?", "values": [{"label": "Yes, asthma", "value": "asthma"}, {"label": "Yes, COPD", "value": "copd"}, {"label": "No", "value": "no"}, {"label": "I don't know", "value": "dont_know"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.wheezing_consultation === 'yes';"}, {"key": "cardio_investigations", "type": "selectboxes", "input": true, "label": "Have you had any of the following tests or procedures for your heart or breathing-related symptoms?", "values": [{"label": "Electrocardiogram (ECG)", "value": "ecg"}, {"label": "Stress test", "value": "stress_test"}, {"label": "Chest X-ray", "value": "chest_xray"}, {"label": "Blood tests (e.g., troponin)", "value": "blood_tests"}, {"label": "Echocardiogram", "value": "echocardiogram"}, {"label": "I haven't had any tests", "value": "no_tests"}], "tableView": true, "customConditional": "show = Object.values(data.cardiovascular_symptoms || {}).some(value => value) || Object.values(data.respiratory_symptoms || {}).some(value => value);"}, {"key": "heading_abdominal_symptoms", "html": "<h2>Abdominal or Stomach Symptoms</h2><p>Provide information about any symptoms you are experiencing in your abdomen or stomach area.</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.screening_indication?.current_symptoms || data.confirm_no_symptoms === 'have_symptoms';"}, {"key": "abdominal_gastrointestinal_symptoms", "type": "selectboxes", "input": true, "label": "Do you have any of the following abdominal or stomach related symptoms?", "values": [{"label": "Abdominal pain", "value": "abdominal_pain"}, {"label": "<PERSON><PERSON><PERSON>", "value": "nausea"}, {"label": "Vomiting", "value": "vomiting"}, {"label": "Diarrhea", "value": "diarrhea"}, {"label": "Constipation", "value": "constipation"}, {"label": "Bloating or gas", "value": "bloating_gas"}, {"label": "Rectal bleeding", "value": "rectal_bleeding"}], "tableView": true, "customConditional": "show = data.screening_indication?.current_symptoms || data.confirm_no_symptoms === 'have_symptoms';", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_abdominal_gastrointestinal_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "validate": {"custom": "valid = !!data.none_of_the_above_abdominal_gastrointestinal_symptoms || _.some(_.values(data.abdominal_gastrointestinal_symptoms));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.screening_indication?.current_symptoms || data.confirm_no_symptoms === 'have_symptoms';"}, {"key": "heading_abdominal_pain", "html": "<h4>Details About Your Abdominal Pain</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.abdominal_gastrointestinal_symptoms?.abdominal_pain;"}, {"key": "abdominal_pain_onset", "data": {"values": [{"label": "Less than 1 hour ago", "value": "<1_hour"}, {"label": "1-6 hours ago", "value": "1-6_hours"}, {"label": "6-12 hours ago", "value": "6-12_hours"}, {"label": "12-24 hours ago", "value": "12-24_hours"}, {"label": "1-2 days ago", "value": "1-2_days"}, {"label": "2-7 days ago", "value": "2-7_days"}, {"label": "1-2 weeks ago", "value": "1-2_weeks"}, {"label": "More than 2 weeks ago", "value": ">2_weeks"}]}, "type": "select", "input": true, "label": "When did your abdominal pain start?", "widget": "html5", "validate": {"required": true}, "tableView": true, "placeholder": "Select the time range", "customConditional": "show = data.abdominal_gastrointestinal_symptoms?.abdominal_pain;"}, {"key": "abdominal_pain_description", "type": "selectboxes", "input": true, "label": "How would you describe your abdominal pain?", "values": [{"label": "Sharp or stabbing pain", "value": "sharp"}, {"label": "Dull or aching pain", "value": "dull"}, {"label": "Cramping pain", "value": "cramping"}, {"label": "Burning sensation", "value": "burning"}, {"label": "Pain that comes and goes", "value": "intermittent"}, {"label": "Pain that is constant", "value": "constant"}], "tableView": true, "customConditional": "show = data.abdominal_gastrointestinal_symptoms?.abdominal_pain;"}, {"key": "abdominal_pain_triggers", "type": "selectboxes", "input": true, "label": "Have you noticed any triggers for your abdominal pain?", "values": [{"label": "After eating", "value": "after_eating"}, {"label": "Before eating (hunger pain)", "value": "before_eating"}, {"label": "After physical activity", "value": "after_activity"}, {"label": "Stress or anxiety", "value": "stress"}, {"label": "None of these seem to trigger it", "value": "no_triggers"}], "tableView": true, "customConditional": "show = data.abdominal_gastrointestinal_symptoms?.abdominal_pain;"}, {"key": "abdominal_pain_relief", "type": "selectboxes", "input": true, "label": "What helps relieve your abdominal pain?", "values": [{"label": "Resting", "value": "rest"}, {"label": "Using over-the-counter medications (e.g., antacids, pain relievers)", "value": "otc_medications"}, {"label": "Avoiding certain foods", "value": "avoiding_foods"}, {"label": "None of these help", "value": "no_relief"}], "tableView": true, "customConditional": "show = data.abdominal_gastrointestinal_symptoms?.abdominal_pain;"}, {"key": "heading_nausea_vomiting", "html": "<h4>Details About Your Nausea or Vomiting</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.abdominal_gastrointestinal_symptoms?.nausea || data.abdominal_gastrointestinal_symptoms?.vomiting;"}, {"key": "nausea_onset", "data": {"values": [{"label": "Less than 6 hours ago", "value": "<6_hours"}, {"label": "6-12 hours ago", "value": "6-12_hours"}, {"label": "12-24 hours ago", "value": "12-24_hours"}, {"label": "1-2 days ago", "value": "1-2_days"}, {"label": "2-7 days ago", "value": "2-7_days"}, {"label": "More than 1 week ago", "value": ">1_week"}]}, "type": "select", "input": true, "label": "When did your nausea or vomiting start?", "widget": "html5", "validate": {"required": true}, "tableView": true, "placeholder": "Select the time range", "customConditional": "show = data.abdominal_gastrointestinal_symptoms?.nausea || data.abdominal_gastrointestinal_symptoms?.vomiting;"}, {"key": "nausea_description", "type": "selectboxes", "input": true, "label": "How would you describe your nausea or vomiting?", "values": [{"label": "Mild nausea", "value": "mild_nausea"}, {"label": "Severe nausea", "value": "severe_nausea"}, {"label": "Occasional vomiting", "value": "occasional_vomiting"}, {"label": "Frequent vomiting", "value": "frequent_vomiting"}, {"label": "Dry heaving without vomiting", "value": "dry_heaving"}], "tableView": true, "customConditional": "show = data.abdominal_gastrointestinal_symptoms?.nausea || data.abdominal_gastrointestinal_symptoms?.vomiting;"}, {"key": "heading_testing", "html": "</br><h3>Previous Vitamin D Testing&nbsp;</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "previous_vit_d_test", "type": "radio", "input": true, "label": "Have you ever had your Vitamin D levels tested in the past?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No/Don't Know", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "confirm_label": "Previous vitamin D testing:", "optionsLabelPosition": "right"}, {"key": "last_vit_d_test", "data": {"values": [{"label": "<6 weeks", "value": "less_6_weeks"}, {"label": "6 weeks - 3 months", "value": "6_weeks-3_months"}, {"label": "3-6 months", "value": "3-6_months"}, {"label": "6-12 months", "value": "6_12_months"}, {"label": "12-24 months", "value": "12_24_months"}, {"label": "24+ months", "value": "24+_months"}]}, "type": "select", "input": true, "label": "When was your last Vitamin D test outside of TeleTest completed?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Last vitamin D test timing:", "customConditional": "show = data.previous_vit_d_test == true;", "optionsLabelPosition": "right"}, {"key": "last_vit_d_results", "type": "radio", "input": true, "label": "What was your last vitamin D level on your last set of bloodwork?", "inline": false, "values": [{"label": "I Don't Know", "value": "doesn't_know", "shortcut": ""}, {"label": "< 30 nmol/L (12 ng/mL) ", "value": "<30 nmol/L", "shortcut": ""}, {"label": "30-50 nmol/L (12-20 ng/mL)", "value": "30-50_nmol/L", "shortcut": ""}, {"label": "50-75 nmol/L (20-30 ng/mL)", "value": "50-75_nmol/L", "shortcut": ""}, {"label": "75-125 nmol/L (30-50 ng/mL)", "value": "75-125_nmol/L", "shortcut": ""}, {"label": "125-375 nmol/L (50-150 ng/mL)", "value": "125_nmol/L", "shortcut": ""}, {"label": "375+ nmol/L (150+ ng/mL)", "value": "375_nmol/L+", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "confirm_label": "Last vitamin D level:", "customConditional": "show = data.previous_vit_d_test == true;", "optionsLabelPosition": "right"}, {"key": "stable_vit_d_bloodwork", "type": "radio", "input": true, "label": "Have you had stable Vitamin D values over several blood tests?", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "Don't know", "value": "don't_know", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "confirm_label": "Stable vitamin D values:", "customConditional": "show = data.previous_vit_d_test == true;", "optionsLabelPosition": "right"}, {"key": "heading_medication", "html": "<h3>Vitamin D Supplements&nbsp;</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "currently_on_meds", "type": "radio", "input": true, "label": "Are you currently on Vitamin D supplements?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "confirm_label": "Currently on vitamin D supplements:", "optionsLabelPosition": "right"}, {"key": "current_vit_d_medication", "type": "radio", "input": true, "label": "Please select which of the following medication you are currently on:", "inline": false, "values": [{"label": "Vitamin D2 (ergocalciferol) <strong>Pills</strong>", "value": "d2-pills", "shortcut": ""}, {"label": "Vitamin D2 (ergocalciferol) <strong>Drops</strong>", "value": "d2-drops", "shortcut": ""}, {"label": "Vitamin D3 (cholecalciferol) <strong>Pills</strong>", "value": "d3-pills", "shortcut": ""}, {"label": "Vitamin D3 (cholecalciferol) <strong>Drops</strong>", "value": "d3-drops", "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "confirm_label": "Current vitamin D medication:", "customConditional": "show = data.currently_on_meds == true;", "optionsLabelPosition": "right"}, {"key": "other_medication", "type": "textarea", "input": true, "label": "Please state your “other” medication:", "tableView": true, "autoExpand": false, "confirm_label": "Other medication details:", "customConditional": "show = data.current_vit_d_medication == 'other';"}, {"key": "previously_on_meds", "type": "radio", "input": true, "label": "Were you previously on Vitamin D supplements?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "confirm_label": "Previously on vitamin D supplements:", "customConditional": "show = data.currently_on_meds == false;", "optionsLabelPosition": "right"}, {"key": "past_vit_d_medication", "type": "radio", "input": true, "label": "Please select which of the following medication you were previously on:", "inline": false, "values": [{"label": "Vitamin D2 (ergocalciferol) <strong>Pills</strong>", "value": "d2-pills", "shortcut": ""}, {"label": "Vitamin D2 (ergocalciferol) <strong>Drops</strong>", "value": "d2-drops", "shortcut": ""}, {"label": "Vitamin D3 (cholecalciferol) <strong>Pills</strong>", "value": "d3-pills", "shortcut": ""}, {"label": "Vitamin D3 (cholecalciferol) <strong>Drops</strong>", "value": "d3-drops", "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "confirm_label": "Past vitamin D medication:", "customConditional": "show = data.previously_on_meds == true;", "optionsLabelPosition": "right"}, {"key": "last_vit_d_test", "data": {"values": [{"label": "<1 week ago", "value": "less_1_weeks"}, {"label": "2-4 weeks ago", "value": "2-4_weeks_ago"}, {"label": "4 weeks - 6 weeks ago", "value": "4_weeks-6_weeks"}, {"label": "6 weeks - 3 months ago", "value": "6_weeks-3_months"}, {"label": "3-6 months", "value": "3-6_months"}, {"label": "6-12 months", "value": "6_12_months"}, {"label": "12-24 months", "value": "12_24_months"}, {"label": "24+ months", "value": "24+_months"}]}, "type": "select", "input": true, "label": "When were you last on Vitamin D supplements?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Last time on vitamin D supplements:", "customConditional": "show = data.previous_vit_d_test == true;", "optionsLabelPosition": "right"}, {"key": "current_vit_d_dosing", "data": {"values": [{"label": "400 IU (10 mcg) daily", "value": "400_iu"}, {"label": "600 IU (15 mcg) daily", "value": "600_iu"}, {"label": "800 IU (20 mcg) daily", "value": "800_iu"}, {"label": "1000 IU (25 mcg) daily", "value": "1000_iu"}, {"label": "2000 IU (50 mcg) daily", "value": "2000_iu"}, {"label": "2500 IU (62.5 mcg) daily", "value": "2500_iu"}, {"label": "4000 IU (100 mcg) daily", "value": "4000_iu"}, {"label": "5000 IU (125 mcg) daily", "value": "5000_iu"}, {"label": "Other", "value": "other"}]}, "type": "select", "input": true, "label": "Please specify your Vitamin D dose:", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Vitamin D dose:", "customConditional": "show = data.currently_on_meds == true || data.previously_on_meds == true;", "optionsLabelPosition": "right"}, {"key": "current_dose_other", "type": "textarea", "input": true, "label": "As you selected other, please specify your dose below:", "validate": {"required": true}, "tableView": true, "autoExpand": false, "confirm_label": "Other vitamin D dose:", "customConditional": "show = data.current_vit_d_dosing == 'other';"}, {"key": "duration_current_dose", "type": "radio", "input": true, "label": "How long have you been taking your current daily dose?", "inline": false, "values": [{"label": "< 6 weeks", "value": "<6_weeks"}, {"label": "6 weeks - 3 months", "value": "6_weeks-3_months"}, {"label": "3-6 months", "value": "3_months-6_months"}, {"label": "6-12 months", "value": "6-12_months"}, {"label": "12+ months", "value": "12+_months"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Duration on current dose:", "customConditional": "show = data.currently_on_meds == true;", "optionsLabelPosition": "right"}, {"key": "last_time_took_medication", "type": "radio", "input": true, "label": "When were you last on Vitamin D supplements?", "inline": false, "values": [{"label": "< 6 weeks", "value": "<6_weeks"}, {"label": "6 weeks - 3 months", "value": "6_weeks-3_months"}, {"label": "3-6 months", "value": "3_months-6_months"}, {"label": "6-12 months", "value": "6-12_months"}, {"label": "12 months", "value": "12+_months"}], "validate": {"required": true}, "tableView": true, "confirm_label": "When last on vitamin D supplements:", "customConditional": "show = data.previously_on_meds == true;", "optionsLabelPosition": "right"}, {"key": "reason_stopping", "type": "radio", "input": true, "label": "Please select your reason(s) for stopping your Vitamin D supplement:", "values": [{"label": "I ran out of medication", "value": "ran_out_of_medication", "shortcut": ""}, {"label": "Didn't have anyone to monitor my Vitamin D levels", "value": "no_one_monitor_tsh", "shortcut": ""}, {"label": "Had a normal vitamin D level after running out of pills", "value": "normal_tsh_after_cessation", "shortcut": ""}, {"label": "I wasn't sure I needed them anymore", "value": "felt_didn't_need_them", "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "confirm_label": "Reason(s) for stopping:", "customConditional": "show = data.previously_on_meds == true;", "optionsLabelPosition": "right"}, {"key": "stopped_med_other", "type": "textarea", "input": true, "label": "Please state your “other” reason(s) for stopping your Vitamin D:", "tableView": true, "autoExpand": false, "confirm_label": "Other reason for stopping:", "customConditional": "show = data.reason_stopping == 'other';"}, {"key": "heading_compliance", "html": "<h3>Medication Compliance&nbsp;</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.currently_on_meds == true || data.previously_on_meds == true;"}, {"key": "current_medication_consistency", "type": "radio", "input": true, "label": "When you take vitamin D, how would you describe how consistent you are with your medication:", "inline": false, "values": [{"label": "I take my medication consistently and <strong>never</strong> miss doses.", "value": "consistent_usage", "shortcut": ""}, {"label": "I take my medication consistently but <strong>sometimes</strong> miss doses.", "value": "regularly_misses_doses", "shortcut": ""}, {"label": "I don't take my medication consistently.", "value": "inconsistent_usage", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "confirm_label": "Medication consistency:", "customConditional": "show = data.currently_on_meds == true || data.previously_on_meds == true;", "optionsLabelPosition": "right"}, {"key": "heading_low_vit_d_rf", "html": "<h3>Vitamin D Risk Factors&nbsp;</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "hx_low_vit_d", "type": "radio", "input": true, "label": "Do you have a history of low vitamin D levels in the past?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "confirm_label": "History of low vitamin D:", "optionsLabelPosition": "right"}, {"key": "heading_conditions_predisposing_low_vit_d", "html": "<h4>Relevant Medical History&nbsp;</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "greater_2000_iu_vitamin_d_daily", "type": "radio", "input": true, "label": "Do you consume (on average) more than 2000 IU of Vitamin D daily?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "confirm_label": "Consumes >2000 IU vitamin D daily:", "optionsLabelPosition": "right"}, {"key": "vit_d_risk_medical_conditions", "type": "selectboxes", "input": true, "label": "Have you been diagnosed by a physician with any of the following medical conditions:", "values": [{"label": "Osteoporosis / Osteopenia (requires prior Bone Mineral Density test)", "value": "osteoporosis_osteopenia", "shortcut": ""}, {"label": "Rickets", "value": "rickets", "shortcut": ""}, {"label": "Celiac disease", "value": "celiac_disease", "shortcut": ""}, {"label": "Gluten intolerance", "value": "gluten_intolerance", "shortcut": ""}, {"label": "Cystic fibrosis", "value": "cystic_fibrosis", "shortcut": ""}, {"label": "<PERSON><PERSON><PERSON>'s disease", "value": "crohns_disease", "shortcut": ""}, {"label": "Chronic kidney disease (eGFR < 30)", "value": "ckd_egfr_lt_30", "shortcut": ""}, {"label": "Nephrotic syndrome", "value": "nephrotic_syndrome", "shortcut": ""}], "inputType": "checkbox", "adminFlag": true, "tableView": true, "confirm_label": "Medical conditions affecting Vitamin D:", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_vit_d_risk_medical_conditions", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a condition."}, "validate": {"custom": "valid = !!data.none_of_the_above_vit_d_risk_medical_conditions || _.some(_.values(data.vit_d_risk_medical_conditions));"}, "adminFlag": true, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "vit_d_risk_medical_conditions_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following Vitamin D–related conditions:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "Vitamin D–related conditions not present:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.vit_d_risk_medical_conditions, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "vit_d_risk_medical_procedures", "type": "selectboxes", "adminFlag": true, "input": true, "label": "Have you had any of the following medical procedures:", "values": [{"label": "Roux-en-Y gastric bypass", "value": "roux_en_y", "shortcut": ""}, {"label": "Adjustable gastric banding", "value": "adjustable_banding", "shortcut": ""}, {"label": "Gastrectomy", "value": "gastrectomy", "shortcut": ""}, {"label": "Removal of part of your small bowel (small intestine)", "value": "small_bowel_resection", "shortcut": ""}], "inputType": "checkbox", "tableView": true, "confirm_label": "Medical procedures affecting Vitamin D:", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_vit_d_risk_medical_procedures", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a procedure."}, "validate": {"custom": "valid = !!data.none_of_the_above_vit_d_risk_medical_procedures || _.some(_.values(data.vit_d_risk_medical_procedures));"}, "adminFlag": true, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "vit_d_risk_medical_procedures_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following medical procedures:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "Medical procedures not present:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.vit_d_risk_medical_procedures, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "vit_d_risk_medications", "type": "selectboxes", "input": true, "label": "Are you on any of the following medications?", "values": [{"label": "Cholestyramine <strong>(<PERSON>: <PERSON><PERSON>)</strong>", "value": "cholestyramine"}, {"label": "Colestipol <strong>(Brand: <PERSON><PERSON><PERSON>)</strong>", "value": "colestipol"}, {"label": "Orlistat <strong>(Brand: <PERSON><PERSON><PERSON>)</strong>", "value": "orlistat"}, {"label": "Any of the following seizure medication: phenytoin, fosphenytoin, primidone", "value": "seizure_medication"}, {"label": "Daily use of mineral oil, senokot, bisadodyl", "value": "laxative_daily_use"}, {"label": "Daily use of prednisone", "value": "prednisone_daily_use"}], "adminFlag": true, "confirm_label": "Vitamin D Risk Medications:", "tableView": true, "optionsLabelPosition": "right"}, {"key": "none_of_the_above_vit_d_risk_medications", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a medication."}, "validate": {"custom": "valid = !!data.none_of_the_above_vit_d_risk_medications || _.some(_.values(data.vit_d_risk_medications));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "vit_d_risk_medications_not_present", "type": "textfield", "input": true, "label": "I DO NOT take the following medications:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "Vitamin D risk medications not taken:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.vit_d_risk_medications, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "fitzpatrick_type", "type": "radio", "input": true, "label": "Please select your skin color (lightest to darkest), based on your <a href=\"https://www.arpansa.gov.au/sites/default/files/legacy/pubs/RadiationProtection/FitzpatrickSkinType.pdf\" target='_blank'>Fitzpatrick Skin Type</a>", "inline": false, "values": [{"label": "Type 1: <PERSON><PERSON>", "value": "type_1", "shortcut": ""}, {"label": "Type 2: White Skin", "value": "type_2", "shortcut": ""}, {"label": "Type 3: Light Brown Skin", "value": "type_3", "shortcut": ""}, {"label": "Type 4: Moderate Brown Skin", "value": "type_4", "shortcut": ""}, {"label": "Type 5: <PERSON>", "value": "type_5", "shortcut": ""}, {"label": "Type 6: Deeply Pigmented Dark Brown to Black Skin", "value": "type_6", "shortcut": ""}, {"label": "I Don't Know", "value": "doesn't_know", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "confirm_label": "Fitzpatrick Skin Type:", "optionsLabelPosition": "right"}, {"key": "clothing_religious_garb", "type": "radio", "input": true, "label": "Do you wear clothing that covers most of your skin for religous or cultural reasons (ie. head scarf, zostikon, niqab, habit, etc)?", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "confirm_label": "Wears clothing that covers most of skin:", "optionsLabelPosition": "right"}, {"key": "intake_kind", "type": "textfield", "input": true, "label": "Intake Kind", "hidden": true, "disabled": true, "tableView": true, "clearOnHide": false, "calculateValue": "value = !data.intake_denial_reasons?.length ? 'mail' : 'chat';"}, {"key": "intake_mt_key", "type": "textfield", "input": true, "label": "Intake Mail Template Key", "hidden": true, "disabled": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.intake_kind=='mail'?'intake-vit-d':'intake-denial'"}, {"key": "allowed_rt_keys", "type": "textfield", "input": true, "label": "Allowed Rep<PERSON>", "hidden": true, "disabled": true, "multiple": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.intake_mt_key=='intake-denial'?[]:['get-rx','appointment-intake','edit-intake']"}]}