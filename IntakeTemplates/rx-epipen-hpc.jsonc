{"components": [{"key": "content", "html": "<h2 style='text-align:center;'>EpiPen Renewal Request</h2><p>Please answer the following questions related to your request for an EpiPen prescription renewal.</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "heading_current_anaphylaxis_check", "html": "</br><h4>Current Symptoms</h4>", "type": "content", "input": false, "label": "Content", "tableView": false}, {"key": "concern_current_anaphylaxis", "type": "radio", "input": true, "label": "Are you concerned you're currently experiencing an anaphylactic reaction?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "confirm_label": "Concerned about current anaphylaxis:"}, {"key": "current_anaphylaxis_symptoms", "type": "selectboxes", "input": true, "label": "Which symptoms are you having right now? (Select all that apply)", "values": [{"label": "Trouble breathing / shortness of breath / wheeze", "value": "breathing_difficulty"}, {"label": "Throat or tongue swelling / tightness / hoarse voice", "value": "throat_tongue_swelling"}, {"label": "Trouble swallowing", "value": "dysphagia"}, {"label": "Widespread hives, flushing, or swelling (face/lips/eyes)", "value": "hives_swelling"}, {"label": "Dizziness, faintness, or feeling about to pass out", "value": "dizziness_faint"}, {"label": "Severe abdominal pain, vomiting, or diarrhea after exposure", "value": "gi_symptoms"}, {"label": "Rapidly worsening symptoms after a likely trigger", "value": "rapid_progression"}], "adminFlag": true, "tableView": true, "optionsLabelPosition": "right", "confirm_label": "Current anaphylaxis symptoms:"}, {"key": "none_of_the_above_current_anaphylaxis_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "Select at least one symptom, or choose \"None of the above\"."}, "validate": {"custom": "valid = !!data.none_of_the_above_current_anaphylaxis_symptoms || _.some(_.values(data.current_anaphylaxis_symptoms));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "heading_epipen_indication", "html": "</br><h4>Reason for EpiPen Renewal</h4>", "type": "content", "input": false, "label": "Reason for Renewal", "tableView": false, "refreshOnChange": false}, {"key": "epipen_renewal_reason", "type": "selectboxes", "input": true, "label": "Please clarify why you are requesting an EpiPen (epinephrine auto-injector) renewal:", "values": [{"label": "My previous EpiPen has expired or is expiring soon", "value": "expired_epipen", "shortcut": ""}, {"label": "I lost or damaged my EpiPen and need a replacement", "value": "lost_damaged", "shortcut": ""}, {"label": "I require an extra device for school, work, or travel", "value": "extra_device", "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "confirm_label": "Reason(s) for EpiPen renewal:", "optionsLabelPosition": "right"}, {"key": "other_epipen_renewal_reason", "type": "textarea", "input": true, "label": "Please state your “other” reason(s) for requesting an EpiPen renewal:", "tableView": true, "autoExpand": false, "confirm_label": "Other reason(s) for EpiPen renewal:", "customConditional": "show = !!(data.epipen_renewal_reason && data.epipen_renewal_reason.other);"}, {"key": "heading_prior_anaphylaxis", "html": "</br><h4>Prior Anaphylaxis History</h4>", "type": "content", "input": false, "label": "Content", "tableView": false}, {"key": "anaphylaxis_history", "type": "radio", "input": true, "label": "Have you ever had a severe allergic reaction (anaphylaxis)?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Prior anaphylaxis history:", "optionsLabelPosition": "right"}, {"key": "anaphylaxis_year", "type": "number", "input": true, "label": "What year did you first experience a severe allergic reaction (anaphylaxis)?", "validate": {"max": 2100, "min": 1900}, "tableView": true, "confirm_label": "First reaction year:", "customConditional": "show = data.anaphylaxis_history === 'yes';"}, {"key": "anaphylaxis_triggers", "type": "selectboxes", "input": true, "label": "What triggered your past anaphylaxis? (Select all that apply)", "values": [{"label": "Peanuts / tree nuts", "value": "nuts"}, {"label": "Other foods (e.g., shellfish, milk, egg, soy)", "value": "other_foods"}, {"label": "Insect sting (e.g., bee, wasp)", "value": "insect"}, {"label": "Medication", "value": "medication"}, {"label": "Other / not sure", "value": "other_unsure"}], "tableView": true, "confirm_label": "Past anaphylaxis trigger(s):", "optionsLabelPosition": "right", "customConditional": "show = data.anaphylaxis_history === 'yes';"}, {"key": "anaphylaxis_emergency_care", "type": "radio", "input": true, "label": "Have you ever needed emergency care (ER visit, ambulance, or hospitalization) for anaphylaxis?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "tableView": true, "confirm_label": "Emergency care required:", "optionsLabelPosition": "right", "customConditional": "show = data.anaphylaxis_history === 'yes';"}, {"key": "anaphylaxis_specialist_followup", "type": "radio", "input": true, "label": "Are you currently being followed by an allergist or immunologist?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "tableView": true, "confirm_label": "Specialist follow-up:", "optionsLabelPosition": "right", "customConditional": "show = data.anaphylaxis_history === 'yes';"}, {"key": "anaphylaxis_followup_type", "type": "selectboxes", "input": true, "label": "Which type of specialist is following you?", "values": [{"label": "Allergist", "value": "allergist"}, {"label": "Immunologist", "value": "immunologist"}, {"label": "Family Doctor / PCP", "value": "family_doctor"}, {"label": "Other / not sure", "value": "other_unsure"}], "tableView": true, "confirm_label": "Specialist type:", "optionsLabelPosition": "right", "customConditional": "show = data.anaphylaxis_specialist_followup === 'yes';"}, {"key": "anaphylaxis_last_followup_range", "data": {"values": [{"label": "Within the past 3 months", "value": "within_3m"}, {"label": "3–6 months ago", "value": "3_6m"}, {"label": "6–12 months ago", "value": "6_12m"}, {"label": "1–2 years ago", "value": "1_2y"}, {"label": "More than 2 years ago", "value": "over_2y"}, {"label": "Not sure", "value": "unsure"}]}, "type": "select", "input": true, "label": "When was your last follow-up with this specialist?", "widget": "html5", "multiple": false, "tableView": true, "confirm_label": "Last follow-up:", "customConditional": "show = data.anaphylaxis_specialist_followup === 'yes';"}, {"key": "anaphylaxis_specialist_location", "type": "radio", "input": true, "label": "Is your specialist located in Canada or outside Canada?", "values": [{"label": "In Canada", "value": "canada"}, {"label": "Outside Canada", "value": "outside"}], "tableView": true, "confirm_label": "Doctor location:", "optionsLabelPosition": "right", "customConditional": "show = data.anaphylaxis_specialist_followup === 'yes';"}, {"key": "heading_epipen_use_context", "html": "</br><h4>Allergy Triggers & Use</h4>", "type": "content", "input": false, "label": "Content", "tableView": false}, {"key": "epipen_for_triggers", "type": "selectboxes", "input": true, "label": "What allergy trigger(s) is this EpiPen intended for? (Select all that apply)", "values": [{"label": "Peanuts / tree nuts", "value": "nuts"}, {"label": "Other foods (e.g., shellfish, milk, egg, soy)", "value": "other_foods"}, {"label": "Insect sting (e.g., bee, wasp)", "value": "insect"}, {"label": "Medication", "value": "medication"}, {"label": "Latex", "value": "latex"}, {"label": "Exercise-induced reaction", "value": "exercise"}, {"label": "Unknown trigger", "value": "unknown"}], "adminFlag": true, "tableView": true, "confirm_label": "EpiPen intended trigger(s):", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_epipen_for_triggers", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "Please select a trigger, or choose \"None of the above\" and describe it."}, "validate": {"custom": "valid = _.some(_.values(data.epipen_for_triggers)) || (data.none_of_the_above_epipen_for_triggers && !!data.epipen_trigger_other_free_text);"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "epipen_trigger_other_free_text", "type": "textfield", "input": true, "label": "Please describe the trigger/use:", "validate": {"required": true}, "tableView": true, "spellcheck": false, "confirm_label": "Trigger/use description (patient text):", "customConditional": "show = !!data.none_of_the_above_epipen_for_triggers;"}, {"key": "epipen_trigger_details", "type": "textfield", "input": true, "label": "If applicable, specify the food/medication/other details (optional):", "tableView": true, "spellcheck": false, "confirm_label": "Trigger details (if provided):", "customConditional": "show = !!(data.epipen_for_triggers && (data.epipen_for_triggers.nuts || data.epipen_for_triggers.other_foods || data.epipen_for_triggers.medication || data.epipen_for_triggers.latex));"}, {"key": "epipen_last_reaction_timing", "type": "select", "input": true, "label": "When was your most recent reaction to this trigger?", "widget": "html5", "multiple": false, "data": {"values": [{"label": "Within the past 3 months", "value": "within_3m"}, {"label": "3–6 months ago", "value": "3_6m"}, {"label": "6–12 months ago", "value": "6_12m"}, {"label": "1–2 years ago", "value": "1_2y"}, {"label": "More than 2 years ago", "value": "over_2y"}, {"label": "No prior reaction to this trigger", "value": "no_prior"}, {"label": "Not sure", "value": "unsure"}]}, "validate": {"required": true}, "tableView": true, "confirm_label": "Most recent reaction timing:"}, {"key": "heading_prior_epipen_use", "html": "</br><h4>Prior EpiPen Use & Past Reactions</h4><p>You indicated a history relevant to anaphylaxis. Please share details so we can confirm the medical need and ensure safe prescribing.</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.anaphylaxis_history === 'yes';"}, {"key": "prior_epipen_used_before", "type": "radio", "input": true, "label": "Have you ever used an EpiPen during an allergic reaction?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "tableView": true, "confirm_label": "EpiPen used previously:", "optionsLabelPosition": "right", "customConditional": "show = data.anaphylaxis_history === 'yes';"}, {"key": "prior_epipen_outcome", "type": "selectboxes", "input": true, "label": "What happened after using the EpiPen? (Select all that apply)", "values": [{"label": "Symptoms improved", "value": "improved"}, {"label": "Required ambulance or ER visit", "value": "er_visit"}, {"label": "Needed a second dose", "value": "second_dose"}, {"label": "No improvement / worsened", "value": "no_improvement"}, {"label": "Not sure / don’t remember", "value": "unsure"}], "adminFlag": true, "tableView": true, "confirm_label": "Outcome after EpiPen use:", "optionsLabelPosition": "right", "customConditional": "show = data.prior_epipen_used_before === 'yes';"}, {"key": "none_of_the_above_prior_epipen_outcome", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select an outcome."}, "validate": {"custom": "valid = !!data.none_of_the_above_prior_epipen_outcome || _.some(_.values(data.prior_epipen_outcome));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.prior_epipen_used_before === 'yes';"}, {"key": "heading_context_risk_factors", "html": "</br><h4>Other Health Conditions</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = !!(data.epipen_renewal_reason) || data.anaphylaxis_history === 'yes';"}, {"key": "risk_comorbid_conditions", "type": "selectboxes", "input": true, "label": "Do you have any of the following conditions? (Select all that apply)", "values": [{"label": "Asthma", "value": "asthma"}, {"label": "Mast cell disorder / mastocytosis", "value": "mast_cell"}, {"label": "Heart disease", "value": "cvd"}], "adminFlag": true, "tableView": true, "confirm_label": "Comorbid conditions:", "optionsLabelPosition": "right", "customConditional": "show = !!(data.epipen_renewal_reason) || data.anaphylaxis_history === 'yes';"}, {"key": "none_of_the_above_risk_comorbid_conditions", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a condition."}, "validate": {"custom": "valid = !!data.none_of_the_above_risk_comorbid_conditions || _.some(_.values(data.risk_comorbid_conditions));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = !!(data.epipen_renewal_reason) || data.anaphylaxis_history === 'yes';"}, {"key": "heading_epipen_device_status", "html": "</br><h4>EpiPen Device Status & Training</h4><p>Tell us about the device(s) you have and your familiarity with using them.</p>", "type": "content", "input": false, "label": "Content", "tableView": false}, {"key": "device_on_hand_count", "data": {"values": [{"label": "0 (none on hand)", "value": "0"}, {"label": "1 device", "value": "1"}, {"label": "2 devices", "value": "2"}, {"label": "3 or more devices", "value": "3_plus"}]}, "type": "select", "input": true, "label": "How many EpiPens do you currently have on hand?", "widget": "html5", "multiple": false, "tableView": true, "confirm_label": "Devices on hand:"}, {"key": "device_expiry_range", "data": {"values": [{"label": "All devices are valid (> 3 months to expire)", "value": "gt3m"}, {"label": "Expiring within 3 months", "value": "lt3m"}, {"label": "Already expired", "value": "expired"}, {"label": "Not sure", "value": "unsure"}]}, "type": "select", "input": true, "label": "What is the expiry status of your current device(s)?", "widget": "html5", "multiple": false, "tableView": true, "confirm_label": "Expiry status:"}, {"key": "device_last_used_range", "data": {"values": [{"label": "Within the past 6 months", "value": "within_6m"}, {"label": "6–12 months ago", "value": "6_12m"}, {"label": "1–2 years ago", "value": "1_2y"}, {"label": "More than 2 years ago", "value": "over_2y"}, {"label": "Never used", "value": "never"}, {"label": "Not sure", "value": "unsure"}]}, "type": "select", "input": true, "label": "When did you last have to use an EpiPen?", "widget": "html5", "multiple": false, "tableView": true, "confirm_label": "Last EpiPen use:"}, {"key": "device_training_source", "data": {"values": [{"label": "Allergist / clinician in person", "value": "clinician"}, {"label": "Pharmacist teaching", "value": "pharmacist"}, {"label": "Video or printed guide", "value": "media"}, {"label": "No formal training / not sure", "value": "none"}]}, "type": "select", "input": true, "label": "Who taught you how to use an EpiPen?", "widget": "html5", "multiple": false, "tableView": true, "confirm_label": "Training source:"}, {"key": "device_use_issues", "type": "selectboxes", "input": true, "label": "If you've used an EpiPen before, did you have any difficulties? (Select all that apply)", "values": [{"label": "Hesitation / delay using it", "value": "delay"}, {"label": "Incorrect injection site", "value": "site_error"}, {"label": "Did not hold long enough", "value": "hold_short"}, {"label": "Accidental injection (thumb/finger)", "value": "accidental"}], "adminFlag": true, "tableView": true, "confirm_label": "Difficulties using device:", "optionsLabelPosition": "right", "customConditional": "show = !!(data.device_last_used_range && data.device_last_used_range !== 'never' && data.device_last_used_range !== 'unsure');"}, {"key": "none_of_the_above_device_use_issues", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a difficulty."}, "validate": {"custom": "valid = !!data.none_of_the_above_device_use_issues || _.some(_.values(data.device_use_issues));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = !!(data.device_last_used_range && data.device_last_used_range !== 'never' && data.device_last_used_range !== 'unsure');"}, {"key": "intake_denial_reasons", "type": "textfield", "input": true, "label": "Intake Denial Reasons:", "hidden": true, "disabled": true, "multiple": true, "adminFlag": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "value = [];", "refreshOnChange": true}, {"key": "intake_kind", "type": "textfield", "input": true, "label": "Intake Kind", "hidden": true, "disabled": true, "tableView": true, "clearOnHide": false, "calculateValue": "value = !data.intake_denial_reasons?.length ? 'mail' : 'chat';"}, {"key": "intake_mt_key", "type": "textfield", "input": true, "label": "Intake Mail Template Key", "hidden": true, "disabled": true, "tableView": true, "clearOnHide": false, "calculateValue": "value = data.intake_kind=='mail'?'intake-epipen':'intake-denial'"}, {"key": "allowed_rt_keys", "type": "textfield", "input": true, "label": "Allowed Rep<PERSON>", "hidden": true, "disabled": true, "multiple": true, "tableView": true, "clearOnHide": false, "calculateValue": "value = data.intake_mt_key=='intake-denial'?[]:['get-rx','appointment-intake','edit-intake']"}]}