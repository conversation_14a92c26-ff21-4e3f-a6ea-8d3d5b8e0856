{"components": [{"key": "mobile_table_css", "tag": "style", "type": "htmlelement", "content": "@media (max-width:600px){\n  /* hide any header row inside either grid */\n  .flaretbl table thead,\n  .flaretbl .datagrid-table thead{display:none!important;}\n  /* turn rows into cards */\n  .flaretbl table tbody tr{display:block;margin:0 0 1rem;border:1px solid #e0e0e0;border-radius:4px;}\n  /* make each cell full-width and wrap text */\n  .flaretbl table tbody td{display:block;width:100%;border:none;padding:6px 12px;white-space:normal;}\n}"}, {"key": "smoking_header", "html": "</br><h1 style=\"text-align:center;\">Smoking-Cessation Treatment Options</h1>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "smoking_overview", "html": "<div class=\"alert alert-success\" style=\"padding:1rem;\"><h4>Your Medication Options</h4><p>Below are three proven ways to help you quit. Pick <em>one</em> that fits you best — we do <strong>not</strong> advise using two at the same time.</p><br><h4>Option&nbsp;A: Zonnic® Nicotine Pouches</h4><p>Mint-flavoured, tobacco-free pouches that sit under your lip and release nicotine in minutes. Sold <em>over-the-counter</em>; you only need a prescription if you want to claim the cost through your drug plan. Use one pouch when a craving hits. <strong>Maximum&nbsp;15 pouches in 24&nbsp;hours.</strong></p><br><h4>Option&nbsp;B: Champix® (varenicline)</h4><p>Makes cigarettes taste less rewarding and eases withdrawal. Dosing: 0.5&nbsp;mg once daily ×&nbsp;3&nbsp;days, then 0.5&nbsp;mg twice daily ×&nbsp;4&nbsp;days, then 1&nbsp;mg twice daily for 11&nbsp;weeks. Generally the <em>most effective</em> single medicine for quitting.</p><br><h4>Option&nbsp;C: Zyban® SR (bupropion&nbsp;150&nbsp;mg)</h4><p>Reduces cravings and weight gain after quitting. Dosing: 150&nbsp;mg once daily ×&nbsp;3&nbsp;days, then 150&nbsp;mg twice daily for 7-12&nbsp;weeks. Considered <em>effective</em>, especially if you prefer a non-nicotine pill.</p><br><h4>Who Should Avoid Each Medicine?</h4><table class=\"table table-bordered\"><thead><tr><th>Medication</th><th>Avoid if…</th></tr></thead><tbody><tr><td>Zonnic® pouches</td><td>Under&nbsp;18; pregnant or breastfeeding; mouth or jaw problems.</td></tr><tr><td>Champix®</td><td>Pregnant or breastfeeding; you have severe kidney disease (eGFR&nbsp;&lt;&nbsp;30&nbsp;mL/min); past serious mood changes or suicidal thoughts on Champix.</td></tr><tr><td>Zyban® SR</td><td>You have a risk of seizures (e.g., diagnosed with epilepsy, ave an eating disorder, drink more than 3&nbsp;drinks daily); have high blood pressure that isn't treated; recently sudden stoped drinking alcohol or some sleep medications.</td></tr></tbody></table><br><h4>TeleTest Recommendation</h4><p><strong>Preferred:</strong> Try Champix first if you have no reasons to avoid it. <strong>Alternatives:</strong> Choose Zyban if Champix isn’t suitable, or Zonnic pouches if you prefer a non-prescription option.</p></div>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "select_med_heading", "html": "<h2>Choose Your Quit-Smoking Medicine</h2><p>Please pick the option you’d like to start with. If you’re unsure, select “Not sure” and we’ll show you a side-by-side comparison.</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "rx_preference_smoking", "type": "radio", "input": true, "label": "Which medicine would you like to begin?", "widget": "html5", "values": [{"label": "Champix® (most effective)", "value": "champix"}, {"label": "Zyban® SR 150 mg (effective)", "value": "zyban"}, {"label": "Zonnic® nicotine pouches (effective)", "value": "zonnic"}, {"label": "Not sure — need guidance", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Medication preference:", "optionsLabelPosition": "right"}, {"key": "champix_heading", "type": "content", "input": false, "html": "<strong>Champix® </strong>", "customConditional": "show = data.rx_preference_smoking === 'champix';"}, {"key": "contraindications_champix", "type": "selectboxes", "input": true, "label": "Do any of the following apply to you? (Champix®)", "values": [{"label": "Pregnant or breastfeeding", "value": "pregnant"}, {"label": "Severe kidney disease (eGFR < 30 mL/min)", "value": "kidney"}, {"label": "Past serious mood disorder / suicidal ideas", "value": "mood"}, {"label": "Allergy to varenicline", "value": "allergy"}], "adminFlag": true, "confirm_label": "Champix Contraindications:", "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = data.rx_preference_smoking === 'champix';"}, {"key": "none_of_the_above_contra_champix", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a contraindication."}, "validate": {"custom": "valid = !!data.none_of_the_above_contra_champix || _.some(_.values(data.contraindications_champix));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.rx_preference_smoking === 'champix';"}, {"key": "champix_contra_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following Champix contraindications:", "hidden": true, "disabled": true, "tableView": true, "confirm_label": "Champix: no contraindications for", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.contraindications_champix, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "side_effects_champix", "type": "content", "input": false, "html": "<strong>Champix® - Possible Side-Effects</strong><ul><li>Nausea - <em>common</em></li><li>Vivid dreams / insomnia - <em>common</em></li><li>Headache - <em>common</em></li><li>Dry mouth - <em>uncommon</em></li><li>Mood changes or suicidal thoughts - <em>rare</em>; stop and seek help</li></ul>", "customConditional": "show = data.rx_preference_smoking === 'champix';"}, {"key": "side_effects_ack_champix", "type": "radio", "input": true, "label": "I have reviewed these side-effects:", "values": [{"label": "✔ I understand and wish to proceed", "value": "understand_proceed"}, {"label": "✖ I’m unsure / need more counselling", "value": "need_counselling"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Understands Champix side-effects:", "customConditional": "show = data.rx_preference_smoking === 'champix';", "optionsLabelPosition": "right"}, {"key": "zyban_heading", "type": "content", "input": false, "html": "<strong>Zyban® SR </strong>", "customConditional": "show = data.rx_preference_smoking === 'zyban';"}, {"key": "contraindications_zyban", "type": "selectboxes", "input": true, "label": "Do any of the following apply to you? (Zyban® SR)", "values": [{"label": "History of seizures", "value": "seizure"}, {"label": "Eating disorder (anorexia / bulimia)", "value": "eating_dis"}, {"label": "MAO inhibitor taken in the last 14 days:<ul class=\"ml-3\"><li>Phenelzine (Nardil®)</li><li>Tranylcypromine (Parnate®)</li><li>Moclobemide</li><li>Linezolid</li><li>Selegiline patch</li></ul>", "value": "mao"}, {"label": "High blood pressure that isn't treated", "value": "htn"}, {"label": "Recent sudden stop of alcohol / sleepign medications", "value": "sleep_meds"}], "adminFlag": true, "confirm_label": "Zyban Contraindications:", "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = data.rx_preference_smoking === 'zyban';"}, {"key": "none_of_the_above_contra_zyban", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a contraindication."}, "validate": {"custom": "valid = !!data.none_of_the_above_contra_zyban || _.some(_.values(data.contraindications_zyban));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.rx_preference_smoking === 'zyban';"}, {"key": "zyban_contra_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following Zyban contraindications:", "hidden": true, "disabled": true, "tableView": true, "confirm_label": "<PERSON><PERSON><PERSON>: no contraindications for", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.contraindications_zyban, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "side_effects_zyban", "type": "content", "input": false, "html": "<strong>Zyban® SR - Possible Side-Effects</strong><ul><li>Dry mouth - <em>common</em></li><li>Insomnia - <em>common</em></li><li>Headache - <em>common</em></li><li>Nausea - <em>common</em></li><li>Skin rash - <em>uncommon</em></li><li>Seizure - <em>rare</em>; stop and seek help</li></ul>", "customConditional": "show = data.rx_preference_smoking === 'zyban';"}, {"key": "side_effects_ack_zyban", "type": "radio", "input": true, "label": "I have reviewed these side-effects:", "values": [{"label": "✔ I understand and wish to proceed", "value": "understand_proceed"}, {"label": "✖ I’m unsure / need more counselling", "value": "need_counselling"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Understands Zyban side-effects:", "customConditional": "show = data.rx_preference_smoking === 'zyban';", "optionsLabelPosition": "right"}, {"key": "zonnic_heading", "type": "content", "input": false, "html": "<strong>Zonnic® </strong>", "customConditional": "show = data.rx_preference_smoking === 'zonnic';"}, {"key": "contraindications_zonnic", "type": "selectboxes", "input": true, "label": "Do any of the following apply to you?", "values": [{"label": "Under 18 years old", "value": "under18"}, {"label": "Pregnant or breastfeeding", "value": "pregnant"}, {"label": "Severe mouth or jaw problems", "value": "mouth_jaw"}, {"label": "Allergy to nicotine products", "value": "allergy"}], "adminFlag": true, "confirm_label": "Zonnic Contraindications:", "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = data.rx_preference_smoking === 'zonnic';"}, {"key": "none_of_the_above_contra_zonnic", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a contraindication."}, "validate": {"custom": "valid = !!data.none_of_the_above_contra_zonnic || _.some(_.values(data.contraindications_zonnic));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.rx_preference_smoking === 'zonnic';"}, {"key": "zonnic_contra_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following Zonnic contraindications:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "Zonnic: no contraindications for", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.contraindications_zonnic, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "side_effects_zonnic", "type": "content", "input": false, "html": "<strong>Zonnic® - Possible Side-Effects</strong><ul><li>Mouth or throat irritation - <em>common</em></li><li>Hiccups - <em>common</em></li><li>Nausea - <em>common</em></li><li>Jaw discomfort - <em>uncommon</em></li><li>Fast heartbeat - <em>rare</em>; stop and seek help</li></ul>", "customConditional": "show = data.rx_preference_smoking === 'zonnic';"}, {"key": "side_effects_ack_zonnic", "type": "radio", "input": true, "label": "I have reviewed these side-effects:", "values": [{"label": "✔ I understand and wish to proceed", "value": "understand_proceed"}, {"label": "✖ I’m unsure / need more counselling", "value": "need_counselling"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Understands Zonnic side-effects:", "customConditional": "show = data.rx_preference_smoking === 'zonnic';", "optionsLabelPosition": "right"}, {"key": "safety_heading", "type": "content", "input": false, "html": "</br><h2> Safety Warning</h2>", "tableView": false}, {"key": "suicide_warning", "type": "content", "input": false, "html": "<div class=\"alert alert-danger\" style=\"padding:1rem;\">If you have thoughts of suicide, self-harm, or harming others at any time, <u>stop the medication and go to the nearest Emergency Department or call 9-1-1</u>.</div>", "tableView": false}, {"key": "suicide_warning_ack", "type": "radio", "input": true, "label": "I understand this emergency advice:", "values": [{"label": "✔ I understand and will seek help if needed", "value": "understand"}, {"label": "✖ I do not understand / need clarification", "value": "do_not_understand"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Understands emergency-care advice:", "optionsLabelPosition": "right"}, {"key": "confirmations_header", "type": "content", "input": false, "label": "Confirmations", "html": "<h3>Confirmations</h3>", "tableView": false, "refreshOnChange": false}, {"key": "med_info_accuracy_ack", "type": "checkbox", "input": true, "label": "I confirm that the medical conditions, current medications, and drug allergies I have provided are complete and accurate. I understand TeleTest physicians will rely on this information when making prescribing recommendations.", "validate": {"required": true}, "errors": {"required": "Please confirm the accuracy of your medical information before continuing."}, "tableView": true, "confirm_label": "My medical conditions, current medications, and drug allergies are complete and accurate:", "defaultValue": false, "customClass": "mt-2"}, {"key": "confirm_zonnic_proceed", "type": "checkbox", "input": true, "label": "✔ I wish to proceed with Zonnic® nicotine pouches", "validate": {"required": true}, "tableView": true, "customClass": "mt-3", "confirm_label": "Proceeds with Zonnic:", "customConditional": "show = data.rx_preference_smoking === 'zonnic';"}, {"key": "confirm_zyban_proceed", "type": "checkbox", "input": true, "label": "✔ I wish to proceed with Zyban® SR (bupropion)", "validate": {"required": true}, "tableView": true, "confirm_label": "Proceeds with Zyban:", "customClass": "mt-3", "customConditional": "show = data.rx_preference_smoking === 'zyban';"}, {"key": "confirm_champix_proceed", "type": "checkbox", "input": true, "label": "✔ I wish to proceed with Champix® (varenicline)", "validate": {"required": true}, "tableView": true, "customClass": "mt-3", "confirm_label": "Proceeds with Champix:", "customConditional": "show = data.rx_preference_smoking === 'champix';"}, {"key": "rxts", "type": "textfield", "input": true, "label": "Rx Templates:", "hidden": true, "disabled": true, "multiple": true, "adminFlag": true, "tableView": true, "clearOnHide": false, "refreshOnChange": true, "calculateValue": "const map={\n  champix:['teva-varenicline-starter-pack-combo-05mg1mg','teva-varenicline-1mg-continuation-pack'],\n  zyban:['zyban-150mg'],\n  zonnic:['zonnic-pouch']\n};\nvalue = map[data.rx_preference_smoking] || [];"}, {"key": "has_additional_questions", "type": "radio", "input": true, "label": "Do you have any additional questions/comments for the doctor or concerns?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "confirm_label": "Has additional questions:", "optionsLabelPosition": "right"}, {"key": "additional_questions", "type": "textarea", "input": true, "label": "Please include any questions/comments here:", "tableView": true, "autoExpand": false, "customConditional": "show = data.has_additional_questions;"}, {"key": "intake_kind", "type": "textfield", "input": true, "label": "Intake Kind", "hidden": true, "disabled": true, "tableView": true, "clearOnHide": false, "calculateValue": "value = !data.intake_denial_reasons?.length ? 'mail' : 'chat';"}, {"key": "intake_mt_key", "type": "textfield", "input": true, "label": "Intake Mail Template Key", "hidden": true, "disabled": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.intake_kind=='mail'?'intake-smoking':'intake-denial'"}, {"key": "allowed_rt_keys", "type": "textfield", "input": true, "label": "Allowed Rep<PERSON>", "hidden": true, "disabled": true, "multiple": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.intake_mt_key=='intake-denial'?[]:['get-rx','appointment-intake','edit-intake']"}]}