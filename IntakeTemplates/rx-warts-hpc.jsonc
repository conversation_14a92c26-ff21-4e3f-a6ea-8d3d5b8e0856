{"components": [{"key": "heading_warts_section", "html": "<h2><strong>Instructions</strong></h2><p>To expedite your care, please answer the following questions to the best of your abilities. &nbsp;We ask these questions to provide the best possible care. <strong>For the purposes of this intake, any bump, growth or sore is called a 'lesion'.</strong> If you have any questions, please list them in the space provided.", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "heading_fmhx", "html": "<h2>Medical History&nbsp;</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "personal_hx", "type": "selectboxes", "input": true, "label": "Have you been diagnosed with any of the following:", "inline": false, "values": [{"label": "Genital warts", "value": "genital_warts", "shortcut": ""}, {"label": "Molloscum Contagiosum", "value": "molloescum_contagiosum", "shortcut": ""}, {"label": "I've have not been diagnosed", "value": "not_diagnosed", "shortcut": ""}, {"label": "None of the above", "value": "none of the above", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "confirm_label": "Confirmed diagnosis:", "optionsLabelPosition": "right"}, {"key": "duration_symptoms", "data": {"values": [{"label": "0-3 months", "value": "0-3_months"}, {"label": "3-6 months", "value": "3-6_months"}, {"label": "12-24 months", "value": "12-24_months"}, {"label": "24+ months", "value": "24+_months"}]}, "type": "select", "input": true, "label": "How long have you had lesions?", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.fm_hx.colon_cancer;", "optionsLabelPosition": "right"}, {"key": "increase_number_lesions", "type": "radio", "input": true, "label": "Have you noticed an increasing number of lesions since you first noticed them?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": false, "optionsLabelPosition": "right"}, {"key": "confirmed_hpv_contact", "type": "radio", "input": true, "label": "Have you had contact with anyone with a confirmed case of HPV?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No/I don't know", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": false, "optionsLabelPosition": "right"}, {"key": "confirmed_molloscum_contact", "type": "radio", "input": true, "label": "Have you had contact with anyone with a confirmed case of molloscum contagiosum?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No/I don't know", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": false, "optionsLabelPosition": "right"}, {"key": "previous_cream_treatments", "type": "selectboxes", "input": true, "label": "Please select if you have attempted any of the following <strong>self-applied</strong> treatments:", "inline": false, "values": [{"label": "Podophyllotoxin (Podofilox)", "value": "podophyllotoxin", "shortcut": ""}, {"label": "Imiquimod 5% cream (Zy<PERSON>lara, Aldera)", "value": "imiquimod_5%", "shortcut": ""}, {"label": "Imiquimod 3.75% cream (Zy<PERSON>lara, Aldera)", "value": "imiquimod_3.75%", "shortcut": ""}, {"label": "Sinecatechins 15% Ointment (Veregen)", "value": "sinecatechins_15%", "shortcut": ""}, {"label": "Podophyllin", "value": "podophyllin", "shortcut": ""}, {"label": "Fluorouracil (5-FU)", "value": "5_fu", "shortcut": ""}, {"label": "None of the above", "value": "none_of_the_above", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "confirm_label": "Previous prescription treatments:", "optionsLabelPosition": "right"}, {"key": "pevious_physician_administed_treatments", "type": "selectboxes", "input": true, "label": "Please select if you have attempted any of the following <strong>physician provided</strong> treatments:", "inline": false, "values": [{"label": "Trichloroacetic Acid (TCA)", "value": "trichloroacetic_acid", "shortcut": ""}, {"label": "Liquid Nitrogen (i.e. Cryotherapy)", "value": "liquid_n2", "shortcut": ""}, {"label": "Electrosurgery", "value": "electrosurgery", "shortcut": ""}, {"label": "Laser Treatment", "value": "laser_treatment", "shortcut": ""}, {"label": "None of the above", "value": "no_tca_n2_electrosurgery_laser", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "confirm_label": "Previous physician applied treatments:", "optionsLabelPosition": "right"}, {"key": "wart_location", "type": "selectboxes", "input": true, "label": "Please select where you have warts:", "values": [{"label": "Genitals", "value": "genitals", "shortcut": ""}, {"label": "Pubic Area", "value": "pubic_area", "shortcut": ""}, {"label": "Around my anal area", "value": "peri-anal", "shortcut": ""}, {"label": "On my anus", "value": "on_anus", "shortcut": ""}, {"label": "Legs (i.e. thigh, buttock)", "value": "legs_thigh_buttock", "shortcut": ""}, {"label": "Urethra (the area where you urinate from)", "value": "urethra", "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "confirm_label": "Current metabolic conditions:", "optionsLabelPosition": "right"}, {"key": "wart_site_other", "type": "textarea", "input": true, "label": "As you selected other, please specify any other areas:", "validate": {"required": true}, "tableView": true, "autoExpand": false, "customConditional": "show = data.wart_location.other;"}, {"key": "number_warts", "data": {"values": [{"label": "1-3", "value": "1-3"}, {"label": "4-7", "value": "4-7"}, {"label": "7-10", "value": "7-10"}, {"label": "10-15", "value": "10-15"}, {"label": "15+", "value": "15+"}]}, "type": "select", "input": true, "label": "How many lesions have you noticed?", "widget": "html5", "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "treatment_interested_in", "type": "selectboxes", "input": true, "label": "Please select which treatment you are interested in:", "inline": false, "values": [{"label": "Podophyllotoxin (Podofilox)", "value": "podophyllotoxin", "shortcut": ""}, {"label": "Sinecatechins 15% Ointment (Veregen)", "value": "sinecatechins_15%", "shortcut": ""}, {"label": "I don't know", "value": "doesnt_know", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "confirm_label": "Interested in:", "optionsLabelPosition": "right"}, {"key": "content2", "html": "<h2>Questions for the Doctor</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "additional_questions", "type": "textarea", "input": true, "label": "Please use this area to ask any questions that you might have for your health care provider:", "tableView": true, "autoExpand": false}]}