{"components": [{"key": "doctor_prescribed_trt", "type": "radio", "input": true, "label": "Are you currently on doctor‑prescribed testosterone replacement therapy (TRT)?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "non_testosterone_ped", "type": "radio", "input": true, "label": "Are you using any non‑testosterone performance‑enhancing drugs (e.g., oral steroids, injectable steroids, SARMS)?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = data.doctor_prescribed_trt === 'yes';"}, {"key": "cardiac_symptoms", "type": "selectboxes", "input": true, "label": "Are you currently experiencing any of the following cardiac‑related symptoms?", "values": [{"label": "Chest pain or heaviness", "value": "chest_pain", "shortcut": ""}, {"label": "Shortness of breath", "value": "dyspnea", "shortcut": ""}, {"label": "Heart palpitations (fast or slow)", "value": "palpitations", "shortcut": ""}, {"label": "Dizziness or feeling faint", "value": "dizziness", "shortcut": ""}, {"label": "Leg or ankle swelling", "value": "peripheral_edema", "shortcut": ""}, {"label": "Unexplained fatigue", "value": "fatigue", "shortcut": ""}], "inputType": "checkbox", "tableView": true, "optionsLabelPosition": "right"}, {"key": "no_cardiac_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = !!data.no_cardiac_symptoms || !!_.some(_.values(data.cardiac_symptoms));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "echo_recent", "type": "radio", "input": true, "label": "Have you had an echocardiogram (ECHO) within the last 3 months?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "echo_recent_result", "type": "radio", "input": true, "label": "What were the results of that echocardiogram?", "values": [{"label": "Normal", "value": "normal"}, {"label": "Abnormal", "value": "abnormal"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.echo_recent === 'yes';", "optionsLabelPosition": "right"}, {"key": "normal_echo_notice", "type": "content", "input": true, "tableView": false, "clearOnHide": false, "customConditional": "show = data.echo_recent === 'yes' && data.echo_recent_result === 'normal';", "defaultValue": "<p class='text-green'><strong>Good news!</strong> Your recent echocardiogram was normal. Repeat testing is <u>not</u> required until <strong>6 months</strong> have passed since your last study.</p>"}, {"key": "symptom_keys", "type": "textfield", "input": true, "label": "Symptom keys:", "hidden": true, "disabled": true, "multiple": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "value = _.keys(_.pickBy(data.cardiac_symptoms));", "refreshOnChange": true}, {"key": "no_indications", "type": "textfield", "input": true, "label": "No Indication:", "hidden": true, "disabled": true, "multiple": false, "tableView": true, "clearOnHide": false, "defaultValue": true, "calculateValue": "value = data.echo_recent === 'yes' && data.echo_recent_result === 'normal' && !_.some(_.values(data.cardiac_symptoms));", "refreshOnChange": true}]}