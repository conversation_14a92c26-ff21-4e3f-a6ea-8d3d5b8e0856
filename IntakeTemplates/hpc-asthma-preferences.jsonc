{"components": [{"key": "mobile_table_css", "tag": "style", "type": "htmlelement", "content": "@media (max-width:600px){.asthmatbl table thead,.asthmatbl .datagrid-table thead{display:none!important;} .asthmatbl table tbody tr{display:block;margin:0 0 1rem;border:1px solid #e0e0e0;border-radius:4px;} .asthmatbl table tbody td{display:block;width:100%;border:none;padding:6px 12px;white-space:normal;}}"}, {"key": "asthma_header", "type": "content", "input": false, "label": "Content", "html": "<h1 style=\"text-align:center;\">Asthma Medication Plan</h1>"}, {"key": "asthma_overview", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "html": "<div class=\"alert alert-success\" style=\"padding:1rem;\"><h4>Why two inhalers?</h4><p><strong>1. Quick-relief puffer (Ventolin® / salbutamol)</strong> gives rapid rescue when breathing suddenly tightens. <em>Using it more than 4 times a week, or for night-time symptoms more than once a week, means your asthma isn’t well-controlled.</em></p><p><strong>2. Daily preventer inhaler</strong> (usually a steroid or a steroid + long-acting medicine) calms airway inflammation and <em>prevents</em> flare-ups. Missing it lets the fire smoulder.</p><h4>Good to know</h4><ul><li><strong>Breo® Ellipta</strong> is a convenient once-daily combo inhaler, but it can be pricey without insurance.</li><li>If you feel a cold coming, will be around high pollen or tropical humidity, or are travelling far from care, <strong>step up</strong> your preventer (or start a steroid inhaler) for a week to head off trouble.</li></ul></div>"}, {"key": "info_accordion", "tag": "div", "type": "htmlelement", "input": false, "content": "<details style=\"border:1px solid #dee2e6;border-radius:6px;padding:0.75rem;margin-bottom:0.75rem;\"><summary style=\"font-weight:600;cursor:pointer;\">When to step-up treatment</summary><p style=\"margin-top:0.5rem;\"><strong>Clues you need more than just Ventolin®:</strong></p><ul style=\"line-height:1.5;margin-bottom:0.5rem;\"><li>Blue puffer needed &gt;4 times/week</li><li>Night-time symptoms &gt;1 night/week</li><li>Any ER visit or oral prednisone in the past year</li></ul><p class=\"text-muted\" style=\"font-size:0.85rem;margin:0;\">These red flags mean adding or increasing a preventer inhaler.</p></details>\n\n<details style=\"border:1px solid #dee2e6;border-radius:6px;padding:0.75rem;margin-bottom:0.75rem;\"><summary style=\"font-weight:600;cursor:pointer;\">Choosing your daily preventer</summary><p style=\"margin-top:0.5rem;\">Most adults do best on a <strong>combination inhaler</strong> (steroid + long-acting medicine) such as Symbicort®, Advair®/Wixela™, or Breo® because it calms swelling <em>and</em> keeps airways relaxed.</p><ul style=\"line-height:1.5;margin-bottom:0.5rem;\"><li><strong>Breo® Ellipta</strong> – once daily, very convenient but the priciest.</li><li><strong>Symbicort®</strong> – twice daily, widely covered by plans.</li><li><strong>Flovent®, Pulmicort®, Alvesco®</strong> – <em>steroid-only</em> options that are usually the most budget-friendly.</li></ul><p class=\"small\" style=\"margin:0;\">If you’re starting a preventer for the first time, expect 2–4 weeks before breathing feels steadier.</p></details>\n\n<details style=\"border:1px solid #dee2e6;border-radius:6px;padding:0.75rem;margin-bottom:0.75rem;\"><summary style=\"font-weight:600;cursor:pointer;\">Myths about inhaled steroids &amp; long-acting medicines</summary><p style=\"margin-top:0.5rem;\">Quick truths to common worries:</p><ul style=\"line-height:1.6;margin-bottom:0.5rem;\"><li><strong>“Steroids will bulk me up.”</strong> Inhaled doses act locally in the lungs; they don’t build muscle like gym steroids.</li><li><strong>“I’ll become dependent.”</strong> Preventers aren’t habit-forming—you can stop safely under guidance, but symptoms may return.</li><li><strong>“They stunt growth.”</strong> In adults, no impact; in kids, any height effect is tiny (≈1 cm) and outweighed by better lung health.</li><li><strong>“Long-acting meds replace my blue puffer.”</strong> Keep the blue puffer for sudden tightness; the preventer works in the background.</li><li><strong>“Daily use means my asthma is severe.”</strong> Even mild asthma benefits from low-dose daily control to avoid flare-ups.</li></ul></details>\n\n<details style=\"border:1px solid #dee2e6;border-radius:6px;padding:0.75rem;\"><summary style=\"font-weight:600;cursor:pointer;\">💲 Price guide (Ontario estimates)</summary><p style=\"margin:0.75rem 0 1rem;\">Table sorted from <strong>cheapest to most expensive</strong> on a cost-per-day basis. Prices vary by pharmacy; Costco is often lowest. Always check your insurer.</p><div style=\"overflow-x:auto;\"><table style=\"width:100%;border-collapse:collapse;font-size:0.9rem;\"><thead><tr style=\"background:#f8f9fa;\"><th style=\"text-align:left;padding:6px 8px;border:1px solid #dee2e6;\">Inhaler</th><th style=\"text-align:left;padding:6px 8px;border:1px solid #dee2e6;\">Strength / Format</th><th style=\"text-align:left;padding:6px 8px;border:1px solid #dee2e6;\">Pack size</th><th style=\"text-align:left;padding:6px 8px;border:1px solid #dee2e6;\">Typical doses / day*</th><th style=\"text-align:left;padding:6px 8px;border:1px solid #dee2e6;\">Est. cost</th><th style=\"text-align:left;padding:6px 8px;border:1px solid #dee2e6;\">Cost / day</th></tr></thead><tbody><tr><td style=\"padding:6px 8px;border:1px solid #dee2e6;\">Pulmicort® Turbuhaler</td><td style=\"padding:6px 8px;border:1px solid #dee2e6;\">200 µg</td><td style=\"padding:6px 8px;border:1px solid #dee2e6;\">200 doses</td><td style=\"padding:6px 8px;border:1px solid #dee2e6;\">2</td><td style=\"padding:6px 8px;border:1px solid #dee2e6;\">$70 – 80</td><td style=\"padding:6px 8px;border:1px solid #dee2e6;\">$0.70 – 0.80</td></tr><tr><td style=\"padding:6px 8px;border:1px solid #dee2e6;\">Flovent® HFA</td><td style=\"padding:6px 8px;border:1px solid #dee2e6;\">125 µg</td><td style=\"padding:6px 8px;border:1px solid #dee2e6;\">120 puffs</td><td style=\"padding:6px 8px;border:1px solid #dee2e6;\">2</td><td style=\"padding:6px 8px;border:1px solid #dee2e6;\">$45 – 50</td><td style=\"padding:6px 8px;border:1px solid #dee2e6;\">$0.75 – 0.85</td></tr><tr><td style=\"padding:6px 8px;border:1px solid #dee2e6;\">Alvesco® HFA</td><td style=\"padding:6px 8px;border:1px solid #dee2e6;\">100 µg</td><td style=\"padding:6px 8px;border:1px solid #dee2e6;\">120 puffs</td><td style=\"padding:6px 8px;border:1px solid #dee2e6;\">2</td><td style=\"padding:6px 8px;border:1px solid #dee2e6;\">$50 – 60</td><td style=\"padding:6px 8px;border:1px solid #dee2e6;\">$0.85 – 1.00</td></tr><tr><td style=\"padding:6px 8px;border:1px solid #dee2e6;\">Wixela™</td><td style=\"padding:6px 8px;border:1px solid #dee2e6;\">250/50 µg Diskus</td><td style=\"padding:6px 8px;border:1px solid #dee2e6;\">60 doses</td><td style=\"padding:6px 8px;border:1px solid #dee2e6;\">2</td><td style=\"padding:6px 8px;border:1px solid #dee2e6;\">$40 – 60</td><td style=\"padding:6px 8px;border:1px solid #dee2e6;\">$1.35 – 2.00</td></tr><tr><td style=\"padding:6px 8px;border:1px solid #dee2e6;\">Symbicort®</td><td style=\"padding:6px 8px;border:1px solid #dee2e6;\">200 µg</td><td style=\"padding:6px 8px;border:1px solid #dee2e6;\">120 puffs</td><td style=\"padding:6px 8px;border:1px solid #dee2e6;\">2</td><td style=\"padding:6px 8px;border:1px solid #dee2e6;\">$100 – 110</td><td style=\"padding:6px 8px;border:1px solid #dee2e6;\">$1.70 – 1.85</td></tr><tr><td style=\"padding:6px 8px;border:1px solid #dee2e6;\">Breo® Ellipta</td><td style=\"padding:6px 8px;border:1px solid #dee2e6;\">100/25 µg</td><td style=\"padding:6px 8px;border:1px solid #dee2e6;\">30 doses</td><td style=\"padding:6px 8px;border:1px solid #dee2e6;\">1</td><td style=\"padding:6px 8px;border:1px solid #dee2e6;\">$90 – 110</td><td style=\"padding:6px 8px;border:1px solid #dee2e6;\">$3.00 – 3.70</td></tr></tbody></table></div><p class=\"text-muted\" style=\"font-size:0.8rem;margin-top:0.5rem;\">*Typical starting plan for mild asthma (for example, one puff in the morning and one at night, or one dose once daily).<br>Data: TeleTest price sheet, updated Jun 2025.</p></details>"}, {"key": "rescue_header", "type": "content", "input": false, "label": "Content", "html": "<h4 style=\"margin-top:1rem;\">Quick-relief (blue) puffer</h4>"}, {"key": "ventolin_renewal", "type": "radio", "input": true, "label": "Do you need a new Ventolin® (salbutamol) prescription?", "values": [{"label": "Yes - please renew it", "value": "yes"}, {"label": "No - I still have plenty", "value": "no"}], "widget": "html5", "validate": {"required": true}, "tableView": true}, {"key": "preventer_header", "type": "content", "input": false, "label": "Content", "html": "<h4 style=\"margin-top:1rem;\">Daily preventer (controller) inhaler</h4>"}, {"key": "maintenance_choice", "type": "radio", "input": true, "label": "Which daily preventer sounds best for you?", "values": [{"label": "<span style=\"color:#28a745;font-weight:bold;\">Combination inhaler – e.g.&nbsp;Symbicort®, Advair®/Wixela™, Breo® <em>(most effective for most adults)</em></span>", "value": "combo"}, {"label": "Steroid-only inhaler – e.g.&nbsp;Flovent®, Pulmicort®, Alvesco®", "value": "steroid_only"}, {"label": "Other / I have something specific in mind", "value": "other"}, {"label": "<span style=\"color:#dc3545;font-weight:bold;\">🚫 I don’t want a maintenance inhaler</span>", "value": "none"}], "widget": "html5", "validate": {"required": true}, "tableView": true}, {"key": "maintenance_other", "type": "textfield", "label": "Tell us the inhaler you prefer:", "tableView": true, "validate": {"required": true}, "customConditional": "show = data.maintenance_choice === 'other';"}, {"key": "no_maint_warning", "type": "htmlelement", "tag": "div", "input": false, "customConditional": "show = data.maintenance_choice === 'none';", "content": "<div class='alert alert-warning' style='margin-top:0.75rem;'><h5>Before you skip a daily preventer …</h5><ul style='margin-left:1rem;line-height:1.5;'><li><strong>Fewer flare-ups:</strong> a daily preventer keeps airways calm so you reach for your blue puffer far less often.</li><li><strong>Peaceful nights:</strong> most people sleep through without wheeze or cough when their inflammation is controlled.</li><li><strong>Stay out of emergency:</strong> regular preventer users are much less likely to need urgent care.</li><li><strong>Keep doing what you love:</strong> easier exercise, fewer missed work or school days.</li></ul><p style='margin:0;'>Even when you feel okay, swelling can smoulder quietly. A preventer is like a tiny fire-extinguisher for your lungs. <em>If you still prefer no daily inhaler, that’s okay—we just want to be sure it’s an informed choice.</em></p></div>"}, {"key": "no_maint_confirm", "type": "radio", "input": true, "label": "Do you still want to continue without a maintenance inhaler?", "values": [{"label": "Yes - I understand the risks and still prefer none", "value": "yes"}, {"label": "No - let me choose a maintenance inhaler", "value": "no"}], "widget": "html5", "customConditional": "show = data.maintenance_choice === 'none';", "tableView": true, "validate": {"required": true}}, {"key": "no_maint_reasons", "type": "selectboxes", "input": true, "label": "Why would you prefer to skip a daily preventer? <small>(Select all that apply)</small>", "values": [{"label": "I’m worried about side-effects", "value": "side_effects", "shortcut": ""}, {"label": "Too expensive / not covered by insurance", "value": "cost", "shortcut": ""}, {"label": "I feel controlled with rescue puffer only", "value": "rescue_only", "shortcut": ""}, {"label": "I’m uncomfortable using steroids long-term", "value": "steroid_fears", "shortcut": ""}, {"label": "I’m unsure how to use the inhaler properly", "value": "technique", "shortcut": ""}, {"label": "Other reason", "value": "other", "shortcut": ""}], "optionsLabelPosition": "right", "tableView": true, "customConditional": "show = data.maintenance_choice === 'none' && data.no_maint_confirm === 'yes';"}, {"key": "no_maint_none", "type": "checkbox", "input": true, "label": "None of the above", "customClass": "mt-n3", "tableView": true, "errors": {"custom": "Select at least one reason—or choose 'None of the above'."}, "validate": {"custom": "valid = !!data.no_maint_none || !!_.some(_.values(data.no_maint_reasons));"}, "customConditional": "show = data.maintenance_choice === 'none' && data.no_maint_confirm === 'yes';", "defaultValue": false}, {"key": "no_maint_other_text", "type": "textfield", "label": "Tell us your other reason:", "tableView": true, "customConditional": "show = data.no_maint_reasons && data.no_maint_reasons.other;", "validate": {"required": true}}, {"key": "combo_med", "type": "radio", "input": true, "label": "Pick your combination inhaler", "values": [{"label": "<strong>Symbicort® Turbuhaler</strong> (preferred)", "value": "symbicort"}, {"label": "Wixela™ Diskus (generic Advair®)", "value": "wixela"}, {"label": "Breo® Ellipta", "value": "breo"}], "widget": "html5", "customConditional": "show = data.maintenance_choice === 'combo';", "tableView": true, "validate": {"required": true}}, {"key": "symbicort_prev", "type": "radio", "input": true, "label": "Have you used Symbicort® before?", "values": [{"label": "Yes, I’ve used it", "value": "yes"}, {"label": "No, this will be new", "value": "no"}], "widget": "html5", "customConditional": "show = data.combo_med === 'symbicort';", "tableView": true, "validate": {"required": true}}, {"key": "symbicort_trial_radio", "type": "radio", "input": true, "label": "We’ll start you on:", "defaultValue": "100/6 1 puff BID", "values": [{"label": "<span style='color:#28a745;font-weight:bold;'>Symbicort® 100/6 µg — 1 puff twice daily</span>", "value": "100/6 1 puff BID"}], "widget": "html5", "customConditional": "show = data.combo_med === 'symbicort' && data.symbicort_prev === 'no';", "tableView": true, "validate": {"required": true}}, {"key": "symbicort_alert", "type": "htmlelement", "tag": "div", "input": false, "customConditional": "show = data.combo_med === 'symbicort' && data.symbicort_prev === 'no';", "content": "<div style='background:#d4edda;border:1px solid #c3e6cb;border-radius:6px;padding:0.75rem;margin-top:0.5rem;'><table style='width:100%;border-collapse:collapse;font-size:0.9rem;'><thead><tr><th style='text-align:left;padding:4px;'>Medication</th><th style='text-align:left;padding:4px;'>How to use</th></tr></thead><tbody><tr><td style='padding:4px;'>Symbicort® 100/6 µg</td><td style='padding:4px;'>1 puff twice daily</td></tr></tbody></table><p style='margin:0.5rem 0 0;'>Try this low dose for two weeks. If you’re still wheezy or using your rescue puffer a lot, check in before changing the dose.</p></div>"}, {"key": "symbicort_strength", "type": "radio", "input": true, "label": "Symbicort® strength (µg per puff)", "defaultValue": "200/6", "values": [{"label": "100/6 µg", "value": "100/6"}, {"label": "<span style='color:#28a745;font-weight:bold;'>200/6 µg </span>", "value": "200/6"}], "widget": "html5", "customConditional": "show = data.combo_med === 'symbicort' && data.symbicort_prev === 'yes';", "tableView": true, "validate": {"required": true}}, {"key": "symbicort_dose", "type": "radio", "input": true, "label": "Pick your Symbicort® dose", "defaultValue": "1 puff BID", "values": [{"label": "<span style='color:#28a745;'>1 puff twice daily </span>", "value": "1 puff BID"}, {"label": "2 puffs twice daily", "value": "2 puffs BID"}], "widget": "html5", "customConditional": "show = data.combo_med === 'symbicort' && data.symbicort_prev === 'yes';", "tableView": true, "validate": {"required": true}}, {"key": "wixela_prev", "type": "radio", "input": true, "label": "Have you used Wixela™ before?", "values": [{"label": "Yes, I’ve used it", "value": "yes"}, {"label": "No, this will be new", "value": "no"}], "widget": "html5", "customConditional": "show = data.combo_med === 'wixela';", "tableView": true, "validate": {"required": true}}, {"key": "wixela_trial_radio", "type": "radio", "input": true, "label": "We’ll start you on:", "defaultValue": "100/50 1 dose BID", "values": [{"label": "<span style='color:#28a745;font-weight:bold;'>Wixela™ 100/50 µg — 1 inhalation twice daily</span>", "value": "100/50 1 dose BID"}], "widget": "html5", "customConditional": "show = data.combo_med === 'wixela' && data.wixela_prev === 'no';", "tableView": true, "validate": {"required": true}}, {"key": "wixela_alert", "type": "htmlelement", "tag": "div", "input": false, "customConditional": "show = data.combo_med === 'wixela' && data.wixela_prev === 'no';", "content": "<div style='background:#d4edda;border:1px solid #c3e6cb;border-radius:6px;padding:0.75rem;margin-top:0.5rem;'><table style='width:100%;border-collapse:collapse;font-size:0.9rem;'><thead><tr><th style='text-align:left;padding:4px;'>Medication</th><th style='text-align:left;padding:4px;'>How to use</th></tr></thead><tbody><tr><td style='padding:4px;'>Wixela™ 100/50 µg</td><td style='padding:4px;'>1 inhalation twice daily</td></tr></tbody></table><p style='margin:0.5rem 0 0;'>Use this starter dose for two weeks. If control is poor, contact us before stepping up.</p></div>"}, {"key": "wixela_strength", "type": "radio", "input": true, "label": "Wixela™ strength (µg per dose)", "defaultValue": "250/50", "values": [{"label": "100/50 µg", "value": "100/50"}, {"label": "<span style='color:#28a745;font-weight:bold;'>250/50 µg </span>", "value": "250/50"}], "widget": "html5", "customConditional": "show = data.combo_med === 'wixela' && data.wixela_prev === 'yes';", "tableView": true, "validate": {"required": true}}, {"key": "wixela_dose", "type": "radio", "input": true, "label": "How many doses each day?", "defaultValue": "1 dose BID", "values": [{"label": "<span style='color:#28a745;'>1 inhalation twice daily </span>", "value": "1 dose BID"}, {"label": "1 inhalation once daily", "value": "1 dose OD"}], "widget": "html5", "customConditional": "show = data.combo_med === 'wixela' && data.wixela_prev === 'yes';", "tableView": true, "validate": {"required": true}}, {"key": "breo_prev", "type": "radio", "input": true, "label": "Have you used Breo® before?", "values": [{"label": "Yes, I’ve used it", "value": "yes"}, {"label": "No, this will be new", "value": "no"}], "widget": "html5", "customConditional": "show = data.combo_med === 'breo';", "tableView": true, "validate": {"required": true}}, {"key": "breo_trial_radio", "type": "radio", "input": true, "label": "We’ll start you on:", "defaultValue": "100/25 1 dose OD", "values": [{"label": "<span style='color:#28a745;font-weight:bold;'>Breo® Ellipta 100/25 µg — 1 inhalation once daily</span>", "value": "100/25 1 dose OD"}], "widget": "html5", "customConditional": "show = data.combo_med === 'breo' && data.breo_prev === 'no';", "tableView": true, "validate": {"required": true}}, {"key": "breo_strength", "type": "radio", "input": true, "label": "Breo® strength (µg per inhalation)", "defaultValue": "100/25", "values": [{"label": "<span style='color:#28a745;font-weight:bold;'>100/25 µg (standard)</span>", "value": "100/25"}, {"label": "200/25 µg", "value": "200/25"}], "widget": "html5", "customConditional": "show = data.combo_med === 'breo' && data.breo_prev === 'yes';", "tableView": true, "validate": {"required": true}}, {"key": "steroid_med", "type": "radio", "input": true, "label": "Pick your steroid-only inhaler", "values": [{"label": "<strong>Flovent®</strong>", "value": "flovent"}, {"label": "Pulmicort® Turbuhaler", "value": "pulmicort"}, {"label": "Alvesco®", "value": "alvesco"}], "widget": "html5", "customConditional": "show = data.maintenance_choice === 'steroid_only';", "tableView": true, "validate": {"required": true}}, {"key": "flovent_device", "type": "radio", "input": true, "label": "Flovent® type", "values": [{"label": "Flovent® HFA ", "value": "hfa"}, {"label": "Flovent® Diskus", "value": "diskus"}], "widget": "html5", "customConditional": "show = data.steroid_med === 'flovent';", "tableView": true, "validate": {"required": true}}, {"key": "flovent_strength", "type": "radio", "input": true, "label": "Flovent® strength", "defaultValue": "250", "values": [{"label": "MDI – 50 µg", "value": "50"}, {"label": "MDI – 125 µg", "value": "125"}, {"label": "<span style='color:#28a745;font-weight:bold;'>MDI – 250 µg</span>", "value": "250"}, {"label": "Diskus – 100 µg", "value": "100"}, {"label": "Diskus – 250 µg", "value": "250d"}], "widget": "html5", "customConditional": "show = data.steroid_med === 'flovent';", "tableView": true, "validate": {"required": true}}, {"key": "flovent_prev", "type": "radio", "input": true, "label": "Have you used Flovent® before?", "values": [{"label": "Yes, I’ve used it", "value": "yes"}, {"label": "No, this will be new", "value": "no"}], "widget": "html5", "customConditional": "show = data.steroid_med === 'flovent';", "tableView": true, "validate": {"required": true}}, {"key": "flovent_trial_radio", "type": "radio", "input": true, "label": "We’ll start you on:", "defaultValue": "50 1 puff BID", "values": [{"label": "<span style='color:#28a745;font-weight:bold;'>Flovent® 50 µg — 1 puff twice daily</span>", "value": "50 1 puff BID"}], "widget": "html5", "customConditional": "show = data.steroid_med === 'flovent' && data.flovent_prev === 'no';", "tableView": true, "validate": {"required": true}}, {"key": "flovent_alert", "type": "htmlelement", "tag": "div", "input": false, "customConditional": "show = data.steroid_med === 'flovent' && data.flovent_prev === 'no';", "content": "<div style='background:#d4edda;border:1px solid #c3e6cb;border-radius:6px;padding:0.75rem;margin-top:0.5rem;'><table style='width:100%;border-collapse:collapse;font-size:0.9rem;'><thead><tr><th style='text-align:left;padding:4px;'>Medication</th><th style='text-align:left;padding:4px;'>How to use</th></tr></thead><tbody><tr><td style='padding:4px;'>Flovent® 50 µg</td><td style='padding:4px;'>1 puff twice daily</td></tr></tbody></table><p style='margin:0.5rem 0 0;'>Try this gentle dose first. Contact us if you still need your rescue puffer more than 4×/week.</p></div>"}, {"key": "flovent_dose", "type": "radio", "input": true, "label": "Pick your Flovent® dose", "defaultValue": "1 puff BID", "values": [{"label": "<span style='color:#28a745;'>1 puff twice daily </span>", "value": "1 puff BID"}, {"label": "2 puffs twice daily", "value": "2 puffs BID"}], "widget": "html5", "customConditional": "show = data.steroid_med === 'flovent' && data.flovent_prev === 'yes';", "tableView": true, "validate": {"required": true}}, {"key": "pulmicort_strength", "type": "radio", "input": true, "label": "Pulmicort® strength (µg per inhalation)", "defaultValue": "400", "values": [{"label": "100 µg", "value": "100"}, {"label": "200 µg", "value": "200"}, {"label": "<span style='color:#28a745;font-weight:bold;'>400 µg </span>", "value": "400"}], "widget": "html5", "customConditional": "show = data.steroid_med === 'pulmicort';", "tableView": true, "validate": {"required": true}}, {"key": "pulmicort_prev", "type": "radio", "input": true, "label": "Have you used Pulmicort® before?", "values": [{"label": "Yes, I’ve used it", "value": "yes"}, {"label": "No, this will be new", "value": "no"}], "widget": "html5", "customConditional": "show = data.steroid_med === 'pulmicort';", "tableView": true, "validate": {"required": true}}, {"key": "pulmicort_trial_radio", "type": "radio", "input": true, "label": "We’ll start you on:", "defaultValue": "100 1 dose BID", "values": [{"label": "<span style='color:#28a745;font-weight:bold;'>Pulmicort® 100 µg — 1 inhalation twice daily</span>", "value": "100 1 dose BID"}], "widget": "html5", "customConditional": "show = data.steroid_med === 'pulmicort' && data.pulmicort_prev === 'no';", "tableView": true, "validate": {"required": true}}, {"key": "pulmicort_alert", "type": "htmlelement", "tag": "div", "input": false, "customConditional": "show = data.steroid_med === 'pulmicort' && data.pulmicort_prev === 'no';", "content": "<div style='background:#d4edda;border:1px solid #c3e6cb;border-radius:6px;padding:0.75rem;margin-top:0.5rem;'><table style='width:100%;border-collapse:collapse;font-size:0.9rem;'><thead><tr><th style='text-align:left;padding:4px;'>Medication</th><th style='text-align:left;padding:4px;'>How to use</th></tr></thead><tbody><tr><td style='padding:4px;'>Pulmicort® 100 µg</td><td style='padding:4px;'>1 inhalation twice daily</td></tr></tbody></table><p style='margin:0.5rem 0 0;'>Use this starter dose for two weeks, then review symptom control.</p></div>"}, {"key": "pulmicort_dose", "type": "radio", "input": true, "label": "Pick your Pulmicort® dose", "defaultValue": "1 dose BID", "values": [{"label": "<span style='color:#28a745;'>1 inhalation twice daily </span>", "value": "1 dose BID"}, {"label": "2 inhalations twice daily", "value": "2 doses BID"}], "widget": "html5", "customConditional": "show = data.steroid_med === 'pulmicort' && data.pulmicort_prev === 'yes';", "tableView": true, "validate": {"required": true}}, {"key": "alvesco_strength", "type": "radio", "input": true, "label": "Alvesco® strength (µg per puff)", "defaultValue": "200", "values": [{"label": "100 µg", "value": "100"}, {"label": "<span style='color:#28a745;font-weight:bold;'>200 µg </span>", "value": "200"}], "widget": "html5", "customConditional": "show = data.steroid_med === 'alvesco';", "tableView": true, "validate": {"required": true}}, {"key": "alvesco_prev", "type": "radio", "input": true, "label": "Have you used Alvesco® before?", "values": [{"label": "Yes, I’ve used it", "value": "yes"}, {"label": "No, this will be new", "value": "no"}], "widget": "html5", "customConditional": "show = data.steroid_med === 'alvesco';", "tableView": true, "validate": {"required": true}}, {"key": "alvesco_trial_radio", "type": "radio", "input": true, "label": "We’ll start you on:", "defaultValue": "100 1 puff BID", "values": [{"label": "<span style='color:#28a745;font-weight:bold;'>Alvesco® 100 µg — 1 puff twice daily</span>", "value": "100 1 puff BID"}], "widget": "html5", "customConditional": "show = data.steroid_med === 'alvesco' && data.alvesco_prev === 'no';", "tableView": true, "validate": {"required": true}}, {"key": "alvesco_alert", "type": "htmlelement", "tag": "div", "input": false, "customConditional": "show = data.steroid_med === 'alvesco' && data.alvesco_prev === 'no';", "content": "<div style='background:#d4edda;border:1px solid #c3e6cb;border-radius:6px;padding:0.75rem;margin-top:0.5rem;'><table style='width:100%;border-collapse:collapse;font-size:0.9rem;'><thead><tr><th style='text-align:left;padding:4px;'>Medication</th><th style='text-align:left;padding:4px;'>How to use</th></tr></thead><tbody><tr><td style='padding:4px;'>Alvesco® 100 µg</td><td style='padding:4px;'>1 puff twice daily</td></tr></tbody></table><p style='margin:0.5rem 0 0;'>Stay on this dose for a couple of weeks. If you still feel tight or wheezy, reach out before changing.</p></div>"}, {"key": "alvesco_dose", "type": "radio", "input": true, "label": "Pick your Alvesco® dose", "defaultValue": "1 puff BID", "values": [{"label": "<span style='color:#28a745;'>1 puff twice daily </span>", "value": "1 puff BID"}, {"label": "2 puffs twice daily", "value": "2 puffs BID"}], "widget": "html5", "customConditional": "show = data.steroid_med === 'alvesco' && data.alvesco_prev === 'yes';", "tableView": true, "validate": {"required": true}}, {"key": "asthma_plan_summary_notice", "type": "content", "input": false, "label": "Content", "html": "<br><h4>Here’s a summary of the plan we’ll prepare for you</h4><p>Full instructions will appear in your TeleTest portal and on the pharmacy label.</p>"}, {"key": "asthma_plan_table", "type": "datagrid", "input": true, "label": "📝 Your asthma prescriptions", "disabled": true, "reorder": false, "addAnother": false, "tableView": true, "customClass": "asthmatbl", "components": [{"key": "med", "type": "textarea", "label": "Medication", "disabled": true, "tableView": true}, {"key": "directions", "type": "textarea", "label": "How to use it", "disabled": true, "tableView": true}], "calculateValue": "value = (() => {\n    const rows = [];\n\n    /* rescue puffer */\n    if (data.ventolin_renewal === 'yes') {\n      rows.push({ med: 'Ventolin® (salbutamol) 100 µg', directions: '1–2 puffs only when needed (max every 4 h)' });\n    }\n\n    /* helper to translate dose tags to plain language */\n    const human = str => str\n      .replace(/BID/,'twice daily')\n      .replace(/OD/,'once daily')\n      .replace(/dose/,'inhalation')\n      .replace(/puff/,'puff');\n\n    /* COMBINATION INHALERS */\n    if (data.maintenance_choice === 'combo') {\n      if (data.combo_med === 'symbicort') {\n        if (data.symbicort_prev === 'no') {\n          rows.push({ med: 'Symbicort® Turbuhaler 100/6 µg', directions: '1 puff twice daily (starter dose)' });\n        } else {\n          const strength = data.symbicort_strength || '100/6';\n          const dose     = human(data.symbicort_dose || '1 puff BID');\n          rows.push({ med: `Symbicort® Turbuhaler ${strength} µg`, directions: dose });\n        }\n      }\n      if (data.combo_med === 'wixela') {\n        if (data.wixela_prev === 'no') {\n          rows.push({ med: 'Wixela™ Diskus 100/50 µg', directions: '1 inhalation twice daily (starter dose)' });\n        } else {\n          const str  = data.wixela_strength || '100/50';\n          const dose = human(data.wixela_dose || '1 dose BID');\n          rows.push({ med: `Wixela™ Diskus ${str} µg`, directions: dose });\n        }\n      }\n      if (data.combo_med === 'breo') {\n        if (data.breo_prev === 'no') {\n          rows.push({ med: 'Breo® Ellipta 100/25 µg', directions: '1 inhalation once daily (starter dose)' });\n        } else {\n          const str  = data.breo_strength || '100/25';\n          rows.push({ med: `Breo® Ellipta ${str} µg`, directions: '1 inhalation once daily' });\n        }\n      }\n    }\n\n    /* STEROID-ONLY INHALERS */\n    if (data.maintenance_choice === 'steroid_only') {\n      if (data.steroid_med === 'flovent') {\n        if (data.flovent_prev === 'no') {\n          rows.push({ med: 'Flovent® HFA 50 µg', directions: '1 puff twice daily' });\n        } else {\n          const device = data.flovent_device === 'diskus' ? 'Diskus' : 'HFA';\n          const str    = data.flovent_strength || '125';\n          const dose   = human(data.flovent_dose || '1 puff BID');\n          rows.push({ med: `Flovent® ${device} ${str} µg`, directions: dose });\n        }\n      }\n      if (data.steroid_med === 'pulmicort') {\n        if (data.pulmicort_prev === 'no') {\n          rows.push({ med: 'Pulmicort® Turbuhaler 100 µg', directions: '1 inhalation twice daily' });\n        } else {\n          const str  = data.pulmicort_strength || '200';\n          const dose = human(data.pulmicort_dose || '1 dose BID');\n          rows.push({ med: `Pulmicort® Turbuhaler ${str} µg`, directions: dose });\n        }\n      }\n      if (data.steroid_med === 'alvesco') {\n        if (data.alvesco_prev === 'no') {\n          rows.push({ med: 'Alvesco® 100 µg', directions: '1 puff twice daily' });\n        } else {\n          const str  = data.alvesco_strength || '100';\n          const dose = human(data.alvesco_dose || '1 puff BID');\n          rows.push({ med: `Alvesco® ${str} µg`, directions: dose });\n        }\n      }\n    }\n\n    /* user-supplied custom inhaler */\n    if (data.maintenance_choice === 'other' && data.maintenance_other) {\n      rows.push({ med: data.maintenance_other, directions: 'Use as directed' });\n    }\n\n    return rows.length ? rows : [{ med: '—', directions: 'No medications chosen yet' }];\n})();"}]}