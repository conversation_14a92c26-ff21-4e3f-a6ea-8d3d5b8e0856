{"components": [{"key": "men_acwy_doses", "type": "radio", "input": true, "label": "How many doses of the meningococcal ACYW vaccine (Men-ACYW) have you had since turning 12?", "values": [{"label": "0 doses", "value": 0}, {"label": "1 dose", "value": 1}, {"label": "2 + doses", "value": 2}, {"label": "Unsure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Men-ACYW doses after age 12:", "optionsLabelPosition": "right"}, {"key": "last_menacwy_over_5yrs", "type": "radio", "input": true, "label": "Was your most recent Men-ACYW shot more than 5 years ago?", "values": [{"label": "Yes", "value": true}, {"label": "No", "value": false}, {"label": "Unsure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Last dose > 5 years ago:", "customConditional": "show = (data.men_acwy_doses === 1 || data.men_acwy_doses === 2);", "optionsLabelPosition": "right"}, {"key": "request_reason", "type": "radio", "input": true, "label": "Why are you asking for the Men-ACYW shot today?", "values": [{"label": "Medical condition or medication", "value": "high_risk_medical"}, {"label": "Travel where it’s required", "value": "travel"}, {"label": "Public-health outbreak", "value": "outbreak"}, {"label": "Routine protection", "value": "routine"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Reason for request:", "optionsLabelPosition": "right"}, {"key": "high_risk_conditions", "type": "selectboxes", "input": true, "label": "Select any conditions or medicines that apply:", "values": [{"label": "No spleen / removal of spleen", "value": "asplenia"}, {"label": "Inherited complement disorder", "value": "complement_deficiency"}, {"label": "On complement-blocking medicine (e.g., eculizumab, ravulizumab, sutimlimab)", "value": "complement_inhibitor"}, {"label": "HIV infection", "value": "hiv"}, {"label": "Lab work with meningococcal bacteria", "value": "lab_exposure"}], "inputType": "checkbox", "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = data.request_reason === 'high_risk_medical';"}, {"key": "no_high_risk_conditions", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "Select at least one condition, or tick None of the above."}, "validate": {"custom": "valid = !!data.no_high_risk_conditions || !!_.some(_.values(data.high_risk_conditions));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.request_reason === 'high_risk_medical';"}, {"key": "allergic_reaction_men_acwy", "type": "radio", "input": true, "label": "Have you ever had a severe allergic reaction to Men-ACYW or anything in it?", "values": [{"label": "Yes", "value": true}, {"label": "No", "value": false}], "validate": {"required": true}, "tableView": true, "confirm_label": "Severe allergy to Men-ACYW:", "customConditional": "show = (data.men_acwy_doses === 0 || data.men_acwy_doses === 'unsure') || ((data.men_acwy_doses === 1 || data.men_acwy_doses === 2) && data.last_menacwy_over_5yrs === true);", "optionsLabelPosition": "right"}, {"key": "current_fever", "type": "radio", "input": true, "label": "Do you have a fever of 38 °C (100.4 °F) or higher today?", "values": [{"label": "Yes", "value": true}, {"label": "No", "value": false}], "validate": {"required": true}, "tableView": true, "confirm_label": "Fever ≥ 38 °C:", "customConditional": "show = (data.men_acwy_doses === 0 || data.men_acwy_doses === 'unsure') || ((data.men_acwy_doses === 1 || data.men_acwy_doses === 2) && data.last_menacwy_over_5yrs === true);", "optionsLabelPosition": "right"}, {"key": "no_indications", "type": "textfield", "input": true, "label": "No indication:", "hidden": true, "disabled": true, "tableView": true, "clearOnHide": false, "defaultValue": true, "calculateValue": "value = ((data.men_acwy_doses === 1 || data.men_acwy_doses === 2) && !((data.request_reason === 'high_risk_medical' || data.request_reason === 'travel' || data.request_reason === 'outbreak') && data.last_menacwy_over_5yrs === true)) || (data.allergic_reaction_men_acwy === true) || (data.current_fever === true);", "refreshOnChange": true}, {"key": "contraindication_keys", "type": "textfield", "input": true, "label": "Contraindication keys:", "hidden": true, "disabled": true, "multiple": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "value = []; var needsBooster = (data.request_reason === 'high_risk_medical' || data.request_reason === 'travel' || data.request_reason === 'outbreak') && data.last_menacwy_over_5yrs === true; if ((data.men_acwy_doses === 1 || data.men_acwy_doses === 2) && !needsBooster) { value.push('series_completed'); } if (data.allergic_reaction_men_acwy === true) { value.push('allergy'); } if (data.current_fever === true) { value.push('fever'); }", "refreshOnChange": true}, {"key": "rejected_html", "type": "content", "input": true, "tableView": false, "clearOnHide": false, "defaultValue": "<h3 class='text-red'>We’re unable to arrange a Men-ACYW prescription through this service right now.</h3><p>Please contact your <strong>local public-health unit</strong> for guidance.</p>"}, {"key": "approved_html", "type": "content", "input": true, "tableView": false, "clearOnHide": false, "defaultValue": "<h3 class='text-green'>You’re eligible for Men-ACYW.</h3><p><strong>Do<PERSON> needed:</strong> One 0.5 mL shot in the arm.</p><p><strong>Next steps:</strong> Requests received before <strong>1 PM</strong> get a same-day consult. We’ll fax the prescription to your pharmacy, and many pharmacies accept walk-ins so you can usually be vaccinated the same day.</p>", "refreshOnChange": true}]}