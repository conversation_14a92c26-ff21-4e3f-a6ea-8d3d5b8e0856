{"components": [{"key": "heading_testosterone_section", "html": "<h1><center><strong>Performance Enhancing Drugs - Monitoring Labwork</strong></h1><center><p>To expedite your care, please answer the following questions to the best of your abilities. &nbsp;We ask these questions to provide the best possible care.&nbsp;</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "heading_ped_history", "html": "<h4>PED History&nbsp;</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "ped_use_status", "type": "radio", "input": true, "label": "What best describes your use of performance-enhancing drugs (PEDs)?", "values": [{"label": "I am currently using PEDs", "value": "currently_using"}, {"label": "I have used PEDs in the past", "value": "previously_used"}, {"label": "I plan to start using PEDs", "value": "planning_to_start"}], "validate": {"required": true}, "tableView": true, "confirm_label": "PED Use Status:", "optionsLabelPosition": "right"}, {"key": "current_testosterone_and_other_peds", "type": "radio", "input": true, "label": "Which of the following best describes your current PED use?", "values": [{"label": "I am only using testosterone and have no plans to use other PEDs", "value": "testosterone_only"}, {"label": "I am using testosterone and other non-testosterone PEDs (i.e. Trenbolone)", "value": "testosterone_and_other"}, {"label": "I am only using non-testosterone PEDs (i.e. Anavar)", "value": "non_testosterone_only"}, {"label": "I am currently only using testosterone, but I have used other PEDs in the past", "value": "testosterone_only_with_history"}, {"label": "I am currently only using testosterone, but I plan to use other PEDs in the future", "value": "testosterone_only_with_future_use"}, {"label": "Prefer Not to Disclose", "value": "prefer_not_to_disclose"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Current PED Use Includes:", "customConditional": "show = data.ped_use_status === 'currently_using';"}, {"key": "trt_source_location", "type": "radio", "input": true, "label": "Where do you get your testosterone?<br><ul><li>Canadian pharmacy = standard TRT testing</li><li>Non-prescribed = additional screening recommended</li><li>Helps determine what testing is appropriate for you</li></ul>", "values": [{"label": "Prescribed by a Canadian provider and filled at a Canadian pharmacy", "value": "canadian_prescribed"}, {"label": "Purchased through a non-prescribed or alternative source", "value": "non_prescribed"}, {"label": "Prefer not to disclose", "value": "prefer_not_to_disclose"}], "validate": {"required": true}, "tableView": true, "confirm_label": "TRT Source:", "customConditional": "show = data.current_testosterone_and_other_peds === 'testosterone_only';"}, {"key": "baseline_testing_before_peds", "type": "radio", "input": true, "label": "Did you complete bloodwork before starting TRT or PEDs?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Prefer not to disclose", "value": "prefer_not_to_disclose"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Baseline Testing Completed:", "customConditional": "show = data.ped_use_status === 'currently_using';"}, {"key": "low_testosterone_on_baseline", "type": "radio", "input": true, "label": "Did your initial bloodwork show low testosterone levels before starting TRT or PEDs?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "I don't remember", "value": "dont_remember"}, {"label": "Prefer not to disclose", "value": "prefer_not_to_disclose"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Low Testosterone on Baseline:", "customConditional": "show = data.baseline_testing_before_peds === 'yes';"}, {"key": "trt_recommendation_message", "html": "<div style='border-left: 4px solid #28a745; background-color: #d4edda; padding: 10px; margin-bottom: 10px;'><strong>Recommendation:</strong> If you had low testosterone on initial bloodwork, prescribed testosterone replacement therapy (TRT) is considered a safer and more effective option than non-prescribed products purchased online. Prescribed TRT ensures proper dosing, monitoring, and lower risk of contaminants.</div>", "type": "content", "input": false, "customConditional": "show = data.low_testosterone_on_baseline === 'yes';"}, {"key": "trt_recommendation_understanding", "type": "radio", "input": true, "label": "Do you understand this recommendation?", "values": [{"label": "I understand", "value": "understand"}, {"label": "I do not understand", "value": "do_not_understand"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Understands Safer TRT Recommendation:", "customConditional": "show = data.low_testosterone_on_baseline === 'yes';"}, {"key": "heading_testosterone", "html": "</br><h4>Testosterone</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.ped_drug && data.ped_drug.testosterone === true;"}, {"key": "current_ped_cycle_phase", "type": "radio", "input": true, "label": "What part of your PED cycle are you currently on?", "values": [{"label": "On-Cycle/Blast (high dose or intensive phase)", "value": "blast"}, {"label": "Off-Cycle/Cruise (lower dose maintenance phase)", "value": "cruise"}, {"label": "Tapering/Bridging (transition phase between cycles)", "value": "bridge"}, {"label": "Post-Cycle Therapy (PCT)", "value": "pct"}, {"label": "Prefer Not to Disclose", "value": "prefer_not_to_disclose"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Current PED Cycle Phase:", "customConditional": "show = data.ped_use_status === 'currently_using';"}, {"key": "heading_blast_phase", "html": "</br><h4>On-Cycle/Blast Phase</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.current_ped_cycle_phase === 'blast';"}, {"key": "heading_cruise_phase", "html": "</br><h4>Off-Cycle/Cruise Phase</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.current_ped_cycle_phase === 'cruise';"}, {"key": "heading_bridge_phase", "html": "</br><h4>Tapering/Bridging Phase</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.current_ped_cycle_phase === 'bridge';"}, {"key": "heading_pct_phase", "html": "</br><h4>Post-Cycle Therapy (PCT)</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.current_ped_cycle_phase === 'pct';"}, {"key": "ped_drug", "type": "selectboxes", "input": true, "label": "Please select what you're <strong>currently</strong> on:", "values": [{"label": "Testosterone", "value": "testosterone"}, {"label": "Oral Anabolic Androgenic Steroids (AAS)", "value": "oral_aas"}, {"label": "Injectable Anabolic Androgenic Steroids (AAS)", "value": "injectable_aas"}, {"label": "Selective Estrogen Receptor Modulators (SERM)", "value": "serm"}, {"label": "Selective Androgen Receptor Modulators (SARM)", "value": "sarm"}, {"label": "Erythropoietin (EPO) or Derivatives", "value": "epo"}, {"label": "Aromatase Inhibitors (AI)", "value": "ai"}, {"label": "Human chorionic gonadotropin (hCG) or Derivatives", "value": "hcg"}, {"label": "Fat Burning Compounds (T3, Clenbuterol, and DNP)", "value": "fat_burning"}, {"label": "Prefer Not to Disclose", "value": "prefer_not_to_disclose"}], "inputType": "checkbox", "tableView": true, "confirm_label": "Selected PED Categories:", "optionsLabelPosition": "right"}, {"key": "no_ped_drug", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = !!data.no_ped_drug || !!_.some(_.values(data.ped_drug));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.current_testosterone_and_other_peds === 'testosterone_and_other' || data.current_testosterone_and_other_peds === 'non_testosterone_only' || data.current_testosterone_and_other_peds === 'prefer_not_to_disclose';"}, {"key": "testosterone_heading", "html": "</br><h4>Testosterone</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.ped_drug && data.ped_drug.testosterone === true;"}, {"key": "testosterone_ester_type", "type": "radio", "input": true, "label": "Are you using a single ester or combining multiple esters of testosterone?", "inline": false, "values": [{"label": "Single ester", "value": "single"}, {"label": "Multiple esters in a premixed product (e.g. Sustanon)", "value": "premixed"}, {"label": "Combining different esters on my own (self-mixed)", "value": "self_mixed"}, {"label": "Prefer not to disclose", "value": "prefer_not_disclose"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.ped_drug && data.ped_drug.testosterone === true;", "optionsLabelPosition": "right"}, {"key": "testosterone_which_esters", "type": "selectboxes", "input": true, "label": "Which testosterone esters are you currently using?", "values": [{"label": "Testosterone Cypionate", "value": "cypionate"}, {"label": "Testosterone Enanthate", "value": "enanthate"}, {"label": "Testosterone Propionate", "value": "propionate"}, {"label": "Sustanon (blend of multiple esters)", "value": "su<PERSON><PERSON>"}, {"label": "Other", "value": "other"}, {"label": "Prefer not to disclose", "value": "prefer_not_disclose"}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "confirm_label": "Current Testosterone Esters:", "customConditional": "show = data.ped_drug && data.ped_drug.testosterone === true && data.testosterone_ester_type"}, {"key": "testosterone_weekly_dose", "type": "number", "input": true, "label": "What is your total weekly dose of testosterone? (in mg)? Enter 0 if you prefer not to disclose.", "suffix": "mg", "validate": {"min": 0}, "tableView": true, "customConditional": "show = data.ped_drug && data.ped_drug.testosterone === true;"}, {"key": "testosterone_testing_recommendation", "html": "<div style='border-left: 4px solid #198754; background-color: #d1e7dd; padding: 10px; margin-bottom: 10px;'><strong>Recommendation:</strong> Based on the weekly dose you entered, your testosterone levels may exceed the upper reporting limit of some labs. LifeLabs typically caps reported values at 52 nmol/L and may not show your actual number beyond that threshold. Dynacare, on the other hand, provides results even at higher concentrations up to 260 nmol/L. For more precise monitoring, we recommend using Dynacare when you select your lab, if one is available near you.</div>", "type": "content", "input": false, "customConditional": "show = data.ped_drug && data.ped_drug.testosterone === true && data.testosterone_weekly_dose > 250;"}, {"key": "testosterone_weekly_dose_understanding", "type": "radio", "input": true, "label": "Just to confirm: the number you entered is your total weekly dose, not the amount per injection.", "values": [{"label": "That's correct", "value": "confirmed_correct"}, {"label": "I do not understand", "value": "do_not_understand"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Understands Weekly Dose Entry:", "customConditional": "show = data.ped_drug && data.ped_drug.testosterone === true && data.testosterone_weekly_dose !== 0;"}, {"key": "testosterone_dosing_frequency", "type": "radio", "input": true, "label": "How often do you administer your testosterone dose?", "values": [{"label": "Once weekly", "value": "once_weekly"}, {"label": "Twice weekly (e.g. every 3.5 days)", "value": "twice_weekly"}, {"label": "Every other day (EOD)", "value": "eod"}, {"label": "Daily (microdosing)", "value": "daily"}, {"label": "Other / irregular schedule", "value": "other"}, {"label": "Prefer not to disclose", "value": "prefer_not_disclose"}], "tableView": true, "confirm_label": "Testosterone Dosing Frequency:", "customConditional": "show = data.ped_drug && data.ped_drug.testosterone === true;", "optionsLabelPosition": "right"}, {"key": "testosterone_testing_recommendation", "html": "<div style='border-left: 4px solid #198754; background-color: #d1e7dd; padding: 10px; margin-bottom: 10px;'><strong>Recommendation:</strong> Timing your bloodwork is important for accuracy while on testosterone therapy. Testosterone Cypionate typically reaches steady levels about 4 weeks after a dose change, while Testosterone Enanthate may take around 10 weeks. Blood should be drawn just before your next injection to capture the trough level — your lowest testosterone concentration. Fasting is not required for testosterone testing, unless you're also checking things like cholesterol or glucose or are not on testosterone (in which case fasting is required for accurate assessment). Testing too early, before reaching steady state, may not give you an accurate reflection of your hormone levels — especially if you're adjusting your dose based on blood concentrations. </div>", "type": "content", "input": false, "customConditional": "show = data.ped_drug && data.ped_drug.testosterone === true;"}, {"key": "testosterone_testing_understanding", "type": "radio", "input": true, "label": "Do you understand this recommendation about timing your testosterone bloodwork?", "values": [{"label": "I understand", "value": "understand"}, {"label": "I do not understand", "value": "do_not_understand"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Understands Testosterone Testing Guidance:", "customConditional": "show = data.ped_drug && data.ped_drug.testosterone === true;"}, {"key": "heading_oral_aas", "tag": "h4", "type": "htmlelement", "input": false, "content": "Oral Anabolic Androgenic Steroids (AAS)", "customConditional": "show = data.ped_drug && data.ped_drug.oral_aas === true;"}, {"key": "specific_oral_aas", "type": "selectboxes", "input": true, "label": "Which specific oral anabolic steroids do you currently use?", "values": [{"label": "Anadrol (Oxymetholone)", "value": "anadrol"}, {"label": "<PERSON><PERSON> (Oxandrolone)", "value": "<PERSON><PERSON><PERSON>"}, {"label": "Dianabol (Methandrostenolone)", "value": "dianabol"}, {"label": "Winstrol (Stanozolol)", "value": "winstrol"}, {"label": "Turinabol", "value": "turinabol"}, {"label": "Methyltestosterone", "value": "methyltestosterone"}, {"label": "Prefer Not To Disclose", "value": "prefer_not_to_disclose"}, {"label": "Other", "value": "other"}], "inputType": "checkbox", "tableView": true, "confirm_label": "Oral Anabolic Steroids:", "customConditional": "show = data.ped_drug && data.ped_drug.oral_aas === true;"}, {"key": "heading_injectable_aas", "tag": "h4", "type": "htmlelement", "input": false, "content": "Injectable Anabolic Androgenic Steroids (AAS)", "customConditional": "show = data.ped_drug && data.ped_drug.injectable_aas === true;"}, {"key": "specific_inj_aas", "type": "selectboxes", "input": true, "label": "Which specific injectable anabolic steroids do you currently use?", "values": [{"label": "Equipoise (Boldenone)", "value": "equipoise"}, {"label": "<PERSON>on (Drostanolone)", "value": "masteron"}, {"label": "Nandrolone (Deca-Durabolin)", "value": "nandrolone"}, {"label": "Primobolan (Methenolone)", "value": "primobolan"}, {"label": "Testosterone (see above if already selected)", "value": "testosterone"}, {"label": "Trenbolone", "value": "trenbolone"}, {"label": "Prefer Not To Disclose", "value": "prefer_not_to_disclose"}, {"label": "Other", "value": "other"}], "inputType": "checkbox", "tableView": true, "confirm_label": "Injectable Anabolic Steroids:", "customConditional": "show = data.ped_drug && data.ped_drug.injectable_aas === true;"}, {"key": "heading_serm", "tag": "h4", "type": "htmlelement", "input": false, "content": "Selective Estrogen Receptor Modulators (SERM)", "customConditional": "show = data.ped_drug && data.ped_drug.serm === true;"}, {"key": "specific_serms", "type": "selectboxes", "input": true, "label": "Which specific SERMs (Selective Estrogen Receptor Modulators) do you currently use?", "values": [{"label": "Clomiphene (Clomid)", "value": "clomiphene"}, {"label": "Tamoxifen (Nolvadex)", "value": "tamoxifen"}, {"label": "Tor<PERSON><PERSON>ne", "value": "toremifene"}, {"label": "Raloxifene", "value": "raloxifene"}, {"label": "Other", "value": "other"}, {"label": "Prefer not to disclose", "value": "prefer_not_disclose"}], "inputType": "checkbox", "tableView": true, "confirm_label": "Current SERMs:", "customConditional": "show = data.ped_drug && data.ped_drug.serm === true;"}, {"key": "heading_sarm", "tag": "h4", "type": "htmlelement", "input": false, "content": "Selective Androgen Receptor Modulators (SARM)", "customConditional": "show = data.ped_drug && data.ped_drug.sarm === true;"}, {"key": "specific_sarms", "type": "selectboxes", "input": true, "label": "Which specific SARMs (Selective Androgen Receptor Modulators) do you currently use?", "values": [{"label": "<PERSON><PERSON><PERSON> (MK-2866)", "value": "ostarine"}, {"label": "Ligandrol (LGD-4033)", "value": "ligandrol"}, {"label": "Testolone (RAD-140)", "value": "rad140"}, {"label": "YK-11", "value": "yk11"}, {"label": "<PERSON><PERSON><PERSON> (S4)", "value": "and<PERSON><PERSON>"}, {"label": "Other", "value": "other"}, {"label": "Prefer not to disclose", "value": "prefer_not_disclose"}], "inputType": "checkbox", "tableView": true, "confirm_label": "Current SARMs:", "customConditional": "show = data.ped_drug && data.ped_drug.sarm === true;"}, {"key": "heading_epo", "tag": "h4", "type": "htmlelement", "input": false, "content": "Erythropoietin (EPO) or Derivatives", "customConditional": "show = data.ped_drug && data.ped_drug.epo === true;"}, {"key": "specific_epo", "type": "selectboxes", "input": true, "label": "Which specific EPO (Erythropoietin) compounds or derivatives do you currently use?", "values": [{"label": "Erythropoietin", "value": "epo_alfa"}, {"label": "Darbepoetin", "value": "da<PERSON><PERSON><PERSON><PERSON>"}, {"label": "Other", "value": "other"}, {"label": "Prefer not to disclose", "value": "prefer_not_disclose"}], "inputType": "checkbox", "tableView": true, "confirm_label": "Current EPO Compounds:", "customConditional": "show = data.ped_drug && data.ped_drug.epo === true;"}, {"key": "heading_ai", "tag": "h4", "type": "htmlelement", "input": false, "content": "Aromatase Inhibitors (AI)", "customConditional": "show = data.ped_drug && data.ped_drug.ai === true;"}, {"key": "specific_ais", "type": "selectboxes", "input": true, "label": "Which specific aromatase inhibitors (AIs) do you currently use?", "values": [{"label": "Anastrozole (Arimidex)", "value": "anastrozole"}, {"label": "Letrozole (Femara)", "value": "letrozole"}, {"label": "Exemestane (Aromasin)", "value": "exemestane"}, {"label": "Other", "value": "other"}, {"label": "Prefer not to disclose", "value": "prefer_not_disclose"}], "inputType": "checkbox", "tableView": true, "confirm_label": "Current Aromatase Inhibitors:", "customConditional": "show = data.ped_drug && data.ped_drug.ai === true;"}, {"key": "heading_hcg", "tag": "h4", "type": "htmlelement", "input": false, "content": "Human chorionic gonadotropin (hCG) or Derivatives", "customConditional": "show = data.ped_drug && data.ped_drug.hcg === true;"}, {"key": "specific_hcg", "type": "selectboxes", "input": true, "label": "Which hCG (human chorionic gonadotropin) or related compounds are you currently using?", "values": [{"label": "hCG (Pregnyl, Novarel)", "value": "hcg"}, {"label": "hMG (human menopausal gonadotropin)", "value": "hmg"}, {"label": "Recombinant hCG", "value": "recombinant_hcg"}, {"label": "Other", "value": "other"}, {"label": "Prefer not to disclose", "value": "prefer_not_disclose"}], "inputType": "checkbox", "tableView": true, "confirm_label": "Current hCG or Derivatives:", "customConditional": "show = data.ped_drug && data.ped_drug.hcg === true;"}, {"key": "heading_fat_burning", "tag": "h4", "type": "htmlelement", "input": false, "content": "Fat Burning Compounds (T3, Clenbuterol, and DNP)", "customConditional": "show = data.ped_drug && data.ped_drug.fat_burning === true;"}, {"key": "specific_fat_burners", "type": "selectboxes", "input": true, "label": "Which fat-burning compounds are you currently using?", "values": [{"label": "T3 (Liothyronine)", "value": "t3"}, {"label": "Clenbuterol", "value": "clenbuterol"}, {"label": "DNP (2,4-Dinitrophenol)", "value": "dnp"}, {"label": "Other", "value": "other"}, {"label": "Prefer not to disclose", "value": "prefer_not_disclose"}], "inputType": "checkbox", "tableView": true, "confirm_label": "Current Fat Burning Compounds:", "customConditional": "show = data.ped_drug && data.ped_drug.fat_burning === true;"}, {"key": "heading_symptoms", "html": "</br><h2>Current Symptoms and Medication Side Effects</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "heading_heart_health", "html": "</br><h4>Heart Health</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "cardiovascular_symptoms", "type": "selectboxes", "input": true, "label": "Do you have any of the following cardiovascular symptoms?", "values": [{"label": "Chest pain, tightness or discomfort", "value": "chest_pain"}, {"label": "Palpitations", "value": "palpitations"}, {"label": "Swelling in the legs, ankles, or feet", "value": "swelling"}, {"label": "Dizziness or fainting", "value": "dizziness"}], "adminFlag": true, "confirm_label": "Cardiovascular Symptoms:", "tableView": true, "optionsLabelPosition": "right"}, {"key": "none_of_the_above_cardiovascular_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = !!data.none_of_the_above_cardiovascular_symptoms || _.some(_.values(data.cardiovascular_symptoms));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "cardiac_symptoms_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following cardiac symptoms:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "Heart related symptoms", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.cardiovascular_symptoms, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "heading_chest_pain", "html": "</br><h4>Chest Pain</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.chest_pain === true;"}, {"key": "chest_pain_onset", "data": {"values": [{"label": "Today", "value": "today"}, {"label": "1 day ago", "value": "1_day"}, {"label": "2 days ago", "value": "2_days"}, {"label": "3 days ago", "value": "3_days"}, {"label": "4 days ago", "value": "4_days"}, {"label": "5 days ago", "value": "5_days"}, {"label": "6 days ago", "value": "6_days"}, {"label": "7 days ago", "value": "7_days"}, {"label": "1-2 weeks ago", "value": "1_2_weeks"}, {"label": "2-4 weeks ago", "value": "2_4_weeks"}, {"label": "1-3 months ago", "value": "1_3_months"}, {"label": "More than 3 months ago", "value": "3_plus_months"}]}, "type": "select", "input": true, "label": "When did the chest pain or discomfort start?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Chest Pain Onset:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.chest_pain === true;"}, {"key": "chest_pain_triggers", "type": "selectboxes", "input": true, "label": "What brings the chest pain on or makes it worse?", "values": [{"label": "Physical activity or exertion", "value": "exertion"}, {"label": "Stress or anxiety", "value": "stress"}, {"label": "Eating a heavy meal", "value": "meal"}, {"label": "Lying down", "value": "lying"}, {"label": "Breathing deeply or coughing", "value": "breathing"}, {"label": "Unknown or unpredictable", "value": "unknown"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Chest Pain Triggers:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.chest_pain === true;"}, {"key": "chest_pain_relievers", "type": "selectboxes", "input": true, "label": "What makes the chest pain better?", "values": [{"label": "Rest", "value": "rest"}, {"label": "Lying down", "value": "lying"}, {"label": "Standing upright", "value": "standing"}, {"label": "Medication (e.g., nitroglycerin, pain relievers)", "value": "medication"}, {"label": "Nothing helps", "value": "nothing"}, {"label": "Unsure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Chest Pain Relievers:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.chest_pain === true;"}, {"key": "chest_pain_character", "type": "radio", "input": true, "label": "How would you describe the pain?", "values": [{"label": "<PERSON>", "value": "sharp"}, {"label": "Dull/aching", "value": "dull"}, {"label": "Tight/pressure-like", "value": "pressure"}, {"label": "Burning", "value": "burning"}, {"label": "Stabbing", "value": "stabbing"}, {"label": "Other", "value": "other"}], "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.chest_pain === true;"}, {"key": "chest_pain_location", "type": "selectboxes", "input": true, "label": "Where is the pain located?", "values": [{"label": "Centre of chest", "value": "centre_chest"}, {"label": "Left side of chest", "value": "left_chest"}, {"label": "Right side of chest", "value": "right_chest"}, {"label": "Upper chest or sternum", "value": "upper_chest"}, {"label": "Other", "value": "other"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Chest Pain Location:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.chest_pain === true;"}, {"key": "chest_pain_radiation", "type": "selectboxes", "input": true, "label": "Does the pain radiate to any of the following areas?", "values": [{"label": "Left arm", "value": "left_arm"}, {"label": "Right arm", "value": "right_arm"}, {"label": "<PERSON><PERSON>", "value": "jaw"}, {"label": "Neck", "value": "neck"}, {"label": "Back", "value": "back"}, {"label": "No radiation", "value": "none"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Chest Pain Radiation:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.chest_pain === true;"}, {"key": "chest_pain_pattern", "type": "radio", "input": true, "label": "Is the chest pain constant or does it come and go?", "values": [{"label": "Constant", "value": "constant"}, {"label": "Comes and goes (intermittent)", "value": "intermittent"}, {"label": "Unsure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Chest Pain Pattern:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.chest_pain === true;"}, {"key": "heading_palpitations", "html": "</br><h4>Palpitations</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.palpitations === true;"}, {"key": "palpitations_onset", "data": {"values": [{"label": "Today", "value": "today"}, {"label": "1 day ago", "value": "1_day"}, {"label": "2-3 days ago", "value": "2_3_days"}, {"label": "4-7 days ago", "value": "4_7_days"}, {"label": "1-2 weeks ago", "value": "1_2_weeks"}, {"label": "2-4 weeks ago", "value": "2_4_weeks"}, {"label": "More than a month ago", "value": "1_month_plus"}]}, "type": "select", "input": true, "label": "When did your palpitations begin?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Palpitations Onset:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.palpitations === true;"}, {"key": "palpitations_rhythm", "type": "radio", "input": true, "label": "How would you describe the rhythm of your palpitations?", "values": [{"label": "Regular and fast", "value": "regular_fast"}, {"label": "Irregular and fast", "value": "irregular_fast"}, {"label": "Skipped beats", "value": "skipped_beats"}, {"label": "Flutters", "value": "flutters"}, {"label": "Other", "value": "other"}], "tableView": true, "confirm_label": "Palpitations Rhythm:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.palpitations === true;"}, {"key": "palpitations_triggers", "type": "selectboxes", "input": true, "label": "What triggers the palpitations?", "values": [{"label": "Exercise or physical activity", "value": "exercise"}, {"label": "Stress or anxiety", "value": "stress"}, {"label": "Caffeine or stimulants", "value": "caffeine"}, {"label": "Alcohol", "value": "alcohol"}, {"label": "Occur at rest", "value": "rest"}, {"label": "Occur at night", "value": "night"}, {"label": "No clear trigger", "value": "unknown"}], "tableView": true, "confirm_label": "Palpitations Triggers:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.palpitations === true;"}, {"key": "palpitations_duration", "data": {"values": [{"label": "A few seconds", "value": "seconds"}, {"label": "Less than 5 minutes", "value": "less_5_min"}, {"label": "5-30 minutes", "value": "5_30_min"}, {"label": "30 minutes to a few hours", "value": "30min_hours"}, {"label": "More than a few hours", "value": "many_hours"}, {"label": "Constant", "value": "constant"}, {"label": "Varies", "value": "varies"}]}, "type": "select", "input": true, "label": "How long do the palpitations usually last?", "widget": "html5", "confirm_label": "Palpitations Duration:", "tableView": true, "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.palpitations === true;"}, {"key": "palpitations_associated_symptoms", "type": "selectboxes", "input": true, "label": "Do you experience any of the following during palpitations?", "values": [{"label": "Dizziness or lightheadedness", "value": "dizziness"}, {"label": "Shortness of breath", "value": "sob"}, {"label": "Chest pain", "value": "chest_pain"}, {"label": "Sweating", "value": "sweating"}, {"label": "Fainting or near-fainting", "value": "fainting"}, {"label": "No other symptoms", "value": "none"}], "tableView": true, "confirm_label": "Palpitations Associated Symptoms:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.palpitations === true;"}, {"key": "heading_swelling", "html": "</br><h4>Swelling</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.swelling === true;"}, {"key": "swelling_location", "type": "selectboxes", "input": true, "label": "Where is the swelling located?", "values": [{"label": "Feet", "value": "feet"}, {"label": "<PERSON><PERSON>", "value": "ankles"}, {"label": "Lower legs", "value": "lower_legs"}, {"label": "Thighs", "value": "thighs"}, {"label": "Abdomen", "value": "abdomen"}, {"label": "Other", "value": "other"}], "tableView": true, "confirm_label": "Swelling Location:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.swelling === true;"}, {"key": "swelling_sidedness", "type": "radio", "input": true, "label": "Is the swelling on one side or both sides?", "values": [{"label": "Both sides", "value": "bilateral"}, {"label": "One side only", "value": "unilateral"}, {"label": "Varies", "value": "varies"}, {"label": "Unsure", "value": "unsure"}], "tableView": true, "confirm_label": "Swelling Sidedness:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.swelling === true;"}, {"key": "swelling_onset", "data": {"values": [{"label": "Today", "value": "today"}, {"label": "1 day ago", "value": "1_day"}, {"label": "2-3 days ago", "value": "2_3_days"}, {"label": "4-7 days ago", "value": "4_7_days"}, {"label": "1-2 weeks ago", "value": "1_2_weeks"}, {"label": "More than 2 weeks ago", "value": "2_plus_weeks"}]}, "type": "select", "input": true, "label": "When did the swelling begin?", "widget": "html5", "tableView": true, "confirm_label": "Swelling Onset:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.swelling === true;"}, {"key": "swelling_timing", "type": "radio", "input": true, "label": "When is the swelling most noticeable?", "values": [{"label": "By the end of the day", "value": "evening"}, {"label": "In the morning", "value": "morning"}, {"label": "Constant throughout the day", "value": "constant"}, {"label": "Varies day to day", "value": "varies"}, {"label": "Unsure", "value": "unsure"}], "tableView": true, "confirm_label": "Swelling Timing:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.swelling === true;"}, {"key": "swelling_pitting", "type": "radio", "input": true, "label": "When you press on the swollen area, does it leave a dent (pitting)?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "unsure"}], "tableView": true, "confirm_label": "Swelling Pitting:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.swelling === true;"}, {"key": "heading_dizziness", "html": "</br><h4>Dizziness or Fainting</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.dizziness === true;"}, {"key": "dizziness_onset", "data": {"values": [{"label": "Today", "value": "today"}, {"label": "1 day ago", "value": "1_day"}, {"label": "2-3 days ago", "value": "2_3_days"}, {"label": "4-7 days ago", "value": "4_7_days"}, {"label": "1-2 weeks ago", "value": "1_2_weeks"}, {"label": "More than 2 weeks ago", "value": "2_plus_weeks"}]}, "type": "select", "input": true, "label": "When did the dizziness or fainting episodes begin?", "widget": "html5", "tableView": true, "confirm_label": "Dizziness Onset:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.dizziness === true;"}, {"key": "dizziness_frequency", "type": "radio", "input": true, "label": "How often do you experience dizziness or fainting?", "values": [{"label": "Once", "value": "once"}, {"label": "Occasionally (less than once a week)", "value": "occasional"}, {"label": "Frequently (once or more per week)", "value": "frequent"}, {"label": "Daily or almost daily", "value": "daily"}, {"label": "Unsure", "value": "unsure"}], "tableView": true, "confirm_label": "Dizziness Frequency:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.dizziness === true;"}, {"key": "dizziness_character", "type": "radio", "input": true, "label": "How would you describe the dizziness?", "values": [{"label": "Lightheadedness or feeling faint", "value": "lightheaded"}, {"label": "Spinning or vertigo", "value": "spinning"}, {"label": "Unsteady or off balance", "value": "unsteady"}, {"label": "Hard to describe", "value": "hard_to_describe"}], "tableView": true, "confirm_label": "Dizziness Character:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.dizziness === true;"}, {"key": "dizziness_timing", "type": "selectboxes", "input": true, "label": "When does the dizziness or fainting usually happen?", "values": [{"label": "After standing up", "value": "standing_up"}, {"label": "After exertion or exercise", "value": "exertion"}, {"label": "At rest", "value": "rest"}, {"label": "With dehydration or hunger", "value": "dehydration"}, {"label": "Without a clear trigger", "value": "unknown"}], "tableView": true, "confirm_label": "Dizziness Timing:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.dizziness === true;"}, {"key": "fainting_loss_consciousness", "type": "radio", "input": true, "label": "Have you ever fully lost consciousness during one of these episodes?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Unsure", "value": "unsure"}], "tableView": true, "confirm_label": "Fainting or Loss of Consciousness:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.dizziness === true;"}, {"key": "heading_lung_health", "html": "</br><h4>Lung Health</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "respiratory_symptoms", "type": "selectboxes", "input": true, "label": "Do you have any of the following respiratory symptoms?", "values": [{"label": "<PERSON><PERSON>", "value": "cough"}, {"label": "Shortness of breath", "value": "shortness_of_breath"}, {"label": "Wheezing", "value": "wheezing"}], "adminFlag": true, "tableView": true, "confirm_label": "Respiratory Symptoms:", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_respiratory_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = !!data.none_of_the_above_respiratory_symptoms || _.some(_.values(data.respiratory_symptoms));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "respiratory_symptoms_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following respiratory symptoms:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "Respiratory symptoms", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.respiratory_symptoms, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "heading_cough", "html": "</br><h4>Cough</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.respiratory_symptoms && data.respiratory_symptoms.cough === true;"}, {"key": "cough_type", "type": "radio", "input": true, "label": "Is your cough dry or productive?", "values": [{"label": "Dry (no phlegm)", "value": "dry"}, {"label": "Productive (with phlegm)", "value": "productive"}, {"label": "Varies", "value": "varies"}, {"label": "Unsure", "value": "unsure"}], "tableView": true, "confirm_label": "Cough Type:", "customConditional": "show = data.respiratory_symptoms && data.respiratory_symptoms.cough === true;"}, {"key": "cough_duration", "data": {"values": [{"label": "Less than 1 week", "value": "lt_1wk"}, {"label": "1-2 weeks", "value": "1_2wk"}, {"label": "2-4 weeks", "value": "2_4wk"}, {"label": "More than 4 weeks", "value": "gt_4wk"}]}, "type": "select", "input": true, "label": "How long have you had the cough?", "widget": "html5", "tableView": true, "confirm_label": "Cough duration", "customConditional": "show = data.respiratory_symptoms && data.respiratory_symptoms.cough === true;"}, {"key": "cough_coughing_blood", "type": "radio", "input": true, "label": "Have you noticed any blood when coughing?", "values": [{"label": "Yes - bright red blood", "value": "bright_red"}, {"label": "Yes - dark or coffee ground appearance", "value": "coffee_ground"}, {"label": "I think so - unsure of colour or source", "value": "unsure_appearance"}, {"label": "No", "value": "no"}], "tableView": true, "confirm_label": "Coughing up blood:", "description": "This includes coughing up blood that appears bright red or looks like coffee grounds (which may suggest bleeding in the lungs or stomach).", "customConditional": "show = data.respiratory_symptoms && data.respiratory_symptoms.cough === true;"}, {"key": "cough_feeling_unwell", "type": "radio", "input": true, "label": "Do you feel generally unwell with your cough (e.g. fatigue, fever, weakness)?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "unsure"}], "tableView": true, "confirm_label": "Feeling unwell with cough:", "customConditional": "show = data.respiratory_symptoms && data.respiratory_symptoms.cough === true;"}, {"key": "cough_with_cold", "type": "radio", "input": true, "label": "Did your cough begin at the same time as a cold or viral illness (e.g. sore throat, congestion, fever)?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "unsure"}], "tableView": true, "confirm_label": "Cough with cold:", "customConditional": "show = data.respiratory_symptoms && data.respiratory_symptoms.cough === true;"}, {"key": "cough_progression", "type": "radio", "input": true, "label": "Is your cough improving, getting worse, or staying the same?", "values": [{"label": "Improving", "value": "improving"}, {"label": "Getting worse", "value": "worsening"}, {"label": "No change", "value": "no_change"}], "tableView": true, "confirm_label": "Cough Progression:", "customConditional": "show = data.cough_with_cold === 'yes';"}, {"key": "cough_urgent_warning", "html": "<div style='border-left: 4px solid #ffc107; background-color: #fff3cd; padding: 10px; margin-bottom: 10px;'><strong>Warning:</strong> If you are experiencing any of the following — worsening cough, shortness of breath, coughing up blood, feeling generally unwell, or if your cough has lasted more than 4 weeks — we advise seeking same-day care in an emergency department. These may be signs of a more serious condition that should not be delayed.</div>", "type": "content", "input": false, "label": "Content", "customConditional": "show = (data.cough_progression === 'worsening') || (data.respiratory_symptoms && data.respiratory_symptoms.shortness_of_breath === true) || (data.cough_coughing_blood === true) || (data.cough_feeling_unwell === true) || (data.cough_duration === 'gt_4wk');"}, {"key": "cough_urgent_warning_understanding", "type": "radio", "input": true, "label": "Do you understand this warning about when to seek emergency care?", "values": [{"label": "I understand", "value": "understand"}, {"label": "I do not understand", "value": "do_not_understand"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Understands Emergency Care Warning:", "customConditional": "show = (data.cough_progression === 'worsening') || (data.respiratory_symptoms && data.respiratory_symptoms.shortness_of_breath === true) || (data.cough_coughing_blood === true) || (data.cough_feeling_unwell === true) || (data.cough_duration === 'gt_4wk');"}, {"key": "heading_shortness_of_breath", "html": "</br><h4>Shortness of Breath</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.respiratory_symptoms && data.respiratory_symptoms.shortness_of_breath === true;"}, {"key": "sob_triggers", "type": "selectboxes", "input": true, "label": "When do you typically feel short of breath?", "values": [{"label": "At rest", "value": "rest"}, {"label": "With mild activity (e.g. walking)", "value": "mild_activity"}, {"label": "With moderate or strenuous activity", "value": "exercise"}, {"label": "While lying flat", "value": "lying_flat"}, {"label": "At night (waking from sleep)", "value": "nocturnal"}, {"label": "Unsure", "value": "unsure"}], "tableView": true, "confirm_label": "Shortness of Breath Triggers:", "customConditional": "show = data.respiratory_symptoms && data.respiratory_symptoms.shortness_of_breath === true;"}, {"key": "heading_wheezing", "html": "</br><h4>Wheezing</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.respiratory_symptoms && data.respiratory_symptoms.wheezing === true;"}, {"key": "wheezing_timing", "type": "selectboxes", "input": true, "label": "When is the wheezing most noticeable?", "values": [{"label": "During exercise", "value": "exercise"}, {"label": "At rest", "value": "rest"}, {"label": "At night", "value": "night"}, {"label": "In cold weather", "value": "cold_weather"}, {"label": "When lying down", "value": "lying_down"}, {"label": "When exposed to irritants (e.g. smoke, dust)", "value": "irritants"}], "tableView": true, "confirm_label": "Wheezing Timing:", "customConditional": "show = data.respiratory_symptoms && data.respiratory_symptoms.wheezing === true;"}, {"key": "wheezing_relief", "type": "radio", "input": true, "label": "Do you use any medications to relieve wheezing?", "values": [{"label": "Yes - inhaler or nebulizer", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Occasionally", "value": "occasional"}], "tableView": true, "confirm_label": "Wheezing Relief:", "customConditional": "show = data.respiratory_symptoms && data.respiratory_symptoms.wheezing === true;"}, {"key": "wheezing_asthma_history", "type": "radio", "input": true, "label": "Have you ever been diagnosed with asthma (currently or in the past)?", "values": [{"label": "Yes - currently diagnosed", "value": "current"}, {"label": "Yes - past diagnosis only", "value": "past"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "unsure"}], "tableView": true, "customConditional": "show = data.respiratory_symptoms && data.respiratory_symptoms.wheezing === true;"}, {"key": "heading_gastro_health", "html": "</br><h4>Gastrointestinal Health</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "abdominal_gastrointestinal_symptoms", "type": "selectboxes", "input": true, "label": "Do you have any of the following abdominal or gastrointestinal symptoms?", "values": [{"label": "Abdominal pain", "value": "abdominal_pain"}, {"label": "Bloating or gas", "value": "bloating_gas"}, {"label": "Constipation", "value": "constipation"}, {"label": "Diarrhea", "value": "diarrhea"}, {"label": "<PERSON><PERSON><PERSON>", "value": "nausea"}, {"label": "Rectal bleeding", "value": "rectal_bleeding"}, {"label": "Vomiting", "value": "vomiting"}], "adminFlag": true, "tableView": true, "confirm_label": "Abdominal or Gastrointestinal Symptoms:", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_abdominal_gastrointestinal_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = !!data.none_of_the_above_abdominal_gastrointestinal_symptoms || _.some(_.values(data.abdominal_gastrointestinal_symptoms));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "gastrointestinal_symptoms_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following gastrointestinal symptoms:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "Gastrointestinal symptoms", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.abdominal_gastrointestinal_symptoms, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "heading_rectal_bleeding", "html": "</br><h4>Rectal Bleeding</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.abdominal_gastrointestinal_symptoms && data.abdominal_gastrointestinal_symptoms.rectal_bleeding === true;"}, {"key": "onset_rectal_bleeding", "data": {"values": [{"label": "Today", "value": "today"}, {"label": "1-3 days ago", "value": "1_3_days"}, {"label": "4-7 days ago", "value": "4_7_days"}, {"label": "1-2 weeks ago", "value": "1_2_weeks"}, {"label": "2-4 weeks ago", "value": "2_4_weeks"}, {"label": "More than 1 month ago (continuous)", "value": "over_1_month"}, {"label": "Comes and goes over the past month", "value": "recurrent_1_month"}, {"label": "Comes and goes over the past year", "value": "recurrent_1_year"}, {"label": "Comes and goes for many years", "value": "recurrent_many_years"}]}, "type": "select", "input": true, "label": "When did the rectal bleeding begin?", "widget": "html5", "tableView": true, "confirm_label": "Rectal Bleeding Onset:", "customConditional": "show = data.abdominal_gastrointestinal_symptoms && data.abdominal_gastrointestinal_symptoms.rectal_bleeding === true;"}, {"key": "rectal_bleeding_appearance", "type": "radio", "input": true, "label": "What type of blood have you noticed?", "values": [{"label": "Bright red blood (e.g., on toilet paper or in toilet)", "value": "bright_red"}, {"label": "Dark or tarry stool (melena)", "value": "tarry"}, {"label": "Blood mixed with stool", "value": "mixed"}, {"label": "I think so - unsure of colour or source", "value": "unsure"}, {"label": "No", "value": "no"}], "tableView": true, "confirm_label": "Rectal Bleeding Appearance:", "description": "This may include bright red blood, dark or tarry stools, or blood mixed with stool. Please select the best match.", "customConditional": "show = data.abdominal_gastrointestinal_symptoms && data.abdominal_gastrointestinal_symptoms.rectal_bleeding === true;"}, {"key": "rectal_bleeding_typical_vs_new", "type": "radio", "input": true, "label": "Is this episode of rectal bleeding similar to past episodes, or is it different?", "values": [{"label": "Typical - feels the same as prior episodes", "value": "typical"}, {"label": "Different - new or unusual compared to prior", "value": "different"}, {"label": "Not sure", "value": "unsure"}], "tableView": true, "confirm_label": "Rectal Bleeding Typical vs New:", "customConditional": "show = data.onset_rectal_bleeding && (data.onset_rectal_bleeding === 'recurrent_1_month' || data.onset_rectal_bleeding === 'recurrent_1_year' || data.onset_rectal_bleeding === 'recurrent_many_years');"}, {"key": "rectal_bleeding_warning", "html": "<div style='border-left: 4px solid #f5c518; background-color: #fff3cd; padding: 10px; margin-bottom: 10px;'><strong>Recommendation:</strong> Rectal bleeding can sometimes be caused by small tears in the skin (anal fissures) or hemorrhoids, especially if related to constipation or straining. However, it can also be a sign of more serious gastrointestinal conditions such as inflammatory bowel disease or colorectal cancer. If you are experiencing rectal bleeding, a physical exam and further evaluation — including possible lab testing or colonoscopy — may be warranted to determine the cause and ensure appropriate treatment.</div>", "type": "content", "input": false, "customConditional": "show = data.abdominal_gastrointestinal_symptoms && data.abdominal_gastrointestinal_symptoms.rectal_bleeding === true;"}, {"key": "rectal_bleeding_warning_understanding", "type": "radio", "input": true, "label": "Do you understand the importance of having rectal bleeding evaluated by a healthcare provider?", "values": [{"label": "I understand", "value": "understand"}, {"label": "I do not understand", "value": "do_not_understand"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Understands Rectal Bleeding Recommendation:", "customConditional": "show = data.abdominal_gastrointestinal_symptoms && data.abdominal_gastrointestinal_symptoms.rectal_bleeding === true;"}, {"key": "heading_abdominal_pain", "html": "</br><h4>Abdominal Pain</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.abdominal_gastrointestinal_symptoms && data.abdominal_gastrointestinal_symptoms.abdominal_pain === true;"}, {"key": "abdominal_pain_location", "type": "selectboxes", "input": true, "label": "Where is your abdominal pain located?", "values": [{"label": "Upper abdomen", "value": "upper"}, {"label": "Lower abdomen", "value": "lower"}, {"label": "Right side", "value": "right"}, {"label": "Left side", "value": "left"}, {"label": "Around the belly button", "value": "periumbilical"}, {"label": "Pain moves around", "value": "diffuse"}], "tableView": true, "confirm_label": "Abdominal Pain Location:", "customConditional": "show = data.abdominal_gastrointestinal_symptoms && data.abdominal_gastrointestinal_symptoms.abdominal_pain === true;"}, {"key": "onset_abdominal_pain", "data": {"values": [{"label": "Today", "value": "today"}, {"label": "1-3 days ago", "value": "1_3_days"}, {"label": "4-7 days ago", "value": "4_7_days"}, {"label": "1-2 weeks ago", "value": "1_2_weeks"}, {"label": "2-4 weeks ago", "value": "2_4_weeks"}, {"label": "More than 1 month ago (continuous)", "value": "over_1_month"}, {"label": "Comes and goes over the past month", "value": "recurrent_1_month"}, {"label": "Comes and goes over the past year", "value": "recurrent_1_year"}, {"label": "Comes and goes for many years", "value": "recurrent_many_years"}]}, "type": "select", "input": true, "label": "When did the abdominal pain begin?", "widget": "html5", "tableView": true, "confirm_label": "Abdominal Pain Onset:", "customConditional": "show = data.abdominal_gastrointestinal_symptoms && data.abdominal_gastrointestinal_symptoms.abdominal_pain === true;"}, {"key": "abdominal_pain_typical_vs_new", "type": "radio", "input": true, "label": "Is this episode of abdominal pain similar to your past episodes, or is it different?", "values": [{"label": "Typical - feels the same as prior episodes", "value": "typical"}, {"label": "Different - new or unusual compared to prior", "value": "different"}, {"label": "Not sure", "value": "unsure"}], "tableView": true, "confirm_label": "Abdominal Pain Typical vs New:", "customConditional": "show = data.onset_abdominal_pain && (data.onset_abdominal_pain === 'recurrent_1_month' || data.onset_abdominal_pain === 'recurrent_1_year' || data.onset_abdominal_pain === 'recurrent_many_years');"}, {"key": "heading_nausea", "html": "</br><h4>Nausea</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.abdominal_gastrointestinal_symptoms && data.abdominal_gastrointestinal_symptoms.nausea === true;"}, {"key": "nausea_timing", "type": "radio", "input": true, "label": "When do you usually feel nauseated?", "values": [{"label": "In the morning", "value": "morning"}, {"label": "After eating", "value": "post_meal"}, {"label": "Randomly throughout the day", "value": "random"}, {"label": "Constant", "value": "constant"}], "tableView": true, "confirm_label": "<PERSON><PERSON><PERSON>:", "customConditional": "show = data.abdominal_gastrointestinal_symptoms && data.abdominal_gastrointestinal_symptoms.nausea === true;"}, {"key": "onset_nausea", "data": {"values": [{"label": "Today", "value": "today"}, {"label": "1-3 days ago", "value": "1_3_days"}, {"label": "4-7 days ago", "value": "4_7_days"}, {"label": "1-2 weeks ago", "value": "1_2_weeks"}, {"label": "2-4 weeks ago", "value": "2_4_weeks"}, {"label": "More than 1 month ago (continuous)", "value": "over_1_month"}, {"label": "Comes and goes over the past month", "value": "recurrent_1_month"}, {"label": "Comes and goes over the past year", "value": "recurrent_1_year"}, {"label": "Comes and goes for many years", "value": "recurrent_many_years"}]}, "type": "select", "input": true, "label": "When did the nausea begin?", "widget": "html5", "tableView": true, "confirm_label": "<PERSON><PERSON>a <PERSON>:", "customConditional": "show = data.abdominal_gastrointestinal_symptoms && data.abdominal_gastrointestinal_symptoms.nausea === true;"}, {"key": "nausea_typical_vs_new", "type": "radio", "input": true, "label": "Is this episode of nausea similar to past episodes, or is it different?", "values": [{"label": "Typical - feels the same as prior episodes", "value": "typical"}, {"label": "Different - new or unusual compared to prior", "value": "different"}, {"label": "Not sure", "value": "unsure"}], "tableView": true, "confirm_label": "Nausea Typical vs New:", "customConditional": "show = data.onset_nausea && (data.onset_nausea === 'recurrent_1_month' || data.onset_nausea === 'recurrent_1_year' || data.onset_nausea === 'recurrent_many_years');"}, {"key": "heading_vomiting", "html": "</br><h4>Vomiting</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.abdominal_gastrointestinal_symptoms && data.abdominal_gastrointestinal_symptoms.vomiting === true;"}, {"key": "vomit_appearance", "type": "radio", "input": true, "label": "What does the vomit look like?", "values": [{"label": "Undigested food or fluid", "value": "food"}, {"label": "Yellow or green (bile)", "value": "bile"}, {"label": "Dark or coffee-ground appearance", "value": "coffee_ground"}, {"label": "Bright red blood", "value": "bright_red"}, {"label": "Not sure", "value": "unsure"}], "tableView": true, "confirm_label": "Vomit Appearance:", "description": "This includes food, yellow/green bile, or signs of bleeding such as coffee ground appearance.", "customConditional": "show = data.abdominal_gastrointestinal_symptoms && data.abdominal_gastrointestinal_symptoms.vomiting === true;"}, {"key": "onset_vomiting", "data": {"values": [{"label": "Today", "value": "today"}, {"label": "1-3 days ago", "value": "1_3_days"}, {"label": "4-7 days ago", "value": "4_7_days"}, {"label": "1-2 weeks ago", "value": "1_2_weeks"}, {"label": "2-4 weeks ago", "value": "2_4_weeks"}, {"label": "More than 1 month ago (continuous)", "value": "over_1_month"}, {"label": "Comes and goes over the past month", "value": "recurrent_1_month"}, {"label": "Comes and goes over the past year", "value": "recurrent_1_year"}, {"label": "Comes and goes for many years", "value": "recurrent_many_years"}]}, "type": "select", "input": true, "label": "When did the vomiting begin?", "widget": "html5", "confirm_label": "Vomiting Onset:", "tableView": true, "customConditional": "show = data.abdominal_gastrointestinal_symptoms && data.abdominal_gastrointestinal_symptoms.vomiting === true;"}, {"key": "vomiting_typical_vs_new", "type": "radio", "input": true, "label": "Is this episode of vomiting similar to past episodes, or is it different?", "values": [{"label": "Typical - feels the same as prior episodes", "value": "typical"}, {"label": "Different - new or unusual compared to prior", "value": "different"}, {"label": "Not sure", "value": "unsure"}], "tableView": true, "confirm_label": "Vomiting Typical vs New:", "customConditional": "show = data.onset_vomiting && (data.onset_vomiting === 'recurrent_1_month' || data.onset_vomiting === 'recurrent_1_year' || data.onset_vomiting === 'recurrent_many_years');"}, {"key": "heading_diarrhea", "html": "</br><h4>Diarrhea</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.abdominal_gastrointestinal_symptoms && data.abdominal_gastrointestinal_symptoms.diarrhea === true;"}, {"key": "diarrhea_features", "type": "selectboxes", "input": true, "label": "What features describe your diarrhea?", "values": [{"label": "Watery", "value": "watery"}, {"label": "Contains mucus", "value": "mucus"}, {"label": "Contains blood", "value": "bloody"}, {"label": "<PERSON><PERSON> need to go", "value": "urgent"}, {"label": "Associated with cramping", "value": "cramping"}], "tableView": true, "confirm_label": "Diarrhea Features:", "customConditional": "show = data.abdominal_gastrointestinal_symptoms && data.abdominal_gastrointestinal_symptoms.diarrhea === true;"}, {"key": "onset_diarrhea", "data": {"values": [{"label": "Today", "value": "today"}, {"label": "1-3 days ago", "value": "1_3_days"}, {"label": "4-7 days ago", "value": "4_7_days"}, {"label": "1-2 weeks ago", "value": "1_2_weeks"}, {"label": "2-4 weeks ago", "value": "2_4_weeks"}, {"label": "More than 1 month ago (continuous)", "value": "over_1_month"}, {"label": "Comes and goes over the past month", "value": "recurrent_1_month"}, {"label": "Comes and goes over the past year", "value": "recurrent_1_year"}, {"label": "Comes and goes for many years", "value": "recurrent_many_years"}]}, "type": "select", "input": true, "label": "When did the diarrhea begin?", "widget": "html5", "tableView": true, "confirm_label": "Diarr<PERSON> On<PERSON>:", "customConditional": "show = data.abdominal_gastrointestinal_symptoms && data.abdominal_gastrointestinal_symptoms.diarrhea === true;"}, {"key": "diarrhea_typical_vs_new", "type": "radio", "input": true, "label": "Is this episode of diarrhea similar to your previous episodes, or is it different?", "values": [{"label": "Typical - feels the same as prior episodes", "value": "typical"}, {"label": "Different - new or unusual compared to prior", "value": "different"}, {"label": "Not sure", "value": "unsure"}], "tableView": true, "confirm_label": "Diarrhea Typical vs New:", "customConditional": "show = data.onset_diarrhea && (data.onset_diarrhea === 'recurrent_1_month' || data.onset_diarrhea === 'recurrent_1_year' || data.onset_diarrhea === 'recurrent_many_years');"}, {"key": "heading_constipation", "html": "</br><h4>Constipation</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.abdominal_gastrointestinal_symptoms && data.abdominal_gastrointestinal_symptoms.constipation === true;"}, {"key": "constipation_frequency", "type": "radio", "input": true, "label": "How often are your bowel movements?", "values": [{"label": "Every 1-2 days", "value": "daily"}, {"label": "Every 3-4 days", "value": "3_4_days"}, {"label": "Less than 2 times per week", "value": "lt_2_week"}, {"label": "Unsure", "value": "unsure"}], "tableView": true, "confirm_label": "Constipation Frequency:", "customConditional": "show = data.abdominal_gastrointestinal_symptoms && data.abdominal_gastrointestinal_symptoms.constipation === true;"}, {"key": "onset_constipation", "data": {"values": [{"label": "Today", "value": "today"}, {"label": "1-3 days ago", "value": "1_3_days"}, {"label": "4-7 days ago", "value": "4_7_days"}, {"label": "1-2 weeks ago", "value": "1_2_weeks"}, {"label": "2-4 weeks ago", "value": "2_4_weeks"}, {"label": "More than 1 month ago (continuous)", "value": "over_1_month"}, {"label": "Comes and goes over the past month", "value": "recurrent_1_month"}, {"label": "Comes and goes over the past year", "value": "recurrent_1_year"}, {"label": "Comes and goes for many years", "value": "recurrent_many_years"}]}, "type": "select", "input": true, "label": "When did the constipation begin?", "widget": "html5", "confirm_label": "Constipation Onset:", "tableView": true, "customConditional": "show = data.abdominal_gastrointestinal_symptoms && data.abdominal_gastrointestinal_symptoms.constipation === true;"}, {"key": "constipation_typical_vs_new", "type": "radio", "input": true, "label": "Is this episode of constipation similar to your past experiences, or is it different?", "values": [{"label": "Typical - feels the same as prior episodes", "value": "typical"}, {"label": "Different - new or unusual compared to prior", "value": "different"}, {"label": "Not sure", "value": "unsure"}], "tableView": true, "confirm_label": "Constipation Typical vs New:", "customConditional": "show = data.onset_constipation && (data.onset_constipation === 'recurrent_1_month' || data.onset_constipation === 'recurrent_1_year' || data.onset_constipation === 'recurrent_many_years');"}, {"key": "heading_bloating", "html": "</br><h4>Bloating or Gas</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.abdominal_gastrointestinal_symptoms && data.abdominal_gastrointestinal_symptoms.bloating_gas === true;"}, {"key": "bloating_pattern", "type": "radio", "input": true, "label": "When is bloating or gas most noticeable?", "values": [{"label": "After eating", "value": "after_meals"}, {"label": "In the evening", "value": "evening"}, {"label": "All day", "value": "all_day"}, {"label": "Varies", "value": "varies"}], "tableView": true, "confirm_label": "Bloating Pattern:", "customConditional": "show = data.abdominal_gastrointestinal_symptoms && data.abdominal_gastrointestinal_symptoms.bloating_gas === true;"}, {"key": "onset_bloating", "data": {"values": [{"label": "Today", "value": "today"}, {"label": "1-3 days ago", "value": "1_3_days"}, {"label": "4-7 days ago", "value": "4_7_days"}, {"label": "1-2 weeks ago", "value": "1_2_weeks"}, {"label": "2-4 weeks ago", "value": "2_4_weeks"}, {"label": "More than 1 month ago (continuous)", "value": "over_1_month"}, {"label": "Comes and goes over the past month", "value": "recurrent_1_month"}, {"label": "Comes and goes over the past year", "value": "recurrent_1_year"}, {"label": "Comes and goes for many years", "value": "recurrent_many_years"}]}, "type": "select", "input": true, "confirm_label": "Bloating Onset:", "label": "When did the bloating or gas begin?", "widget": "html5", "tableView": true, "customConditional": "show = data.abdominal_gastrointestinal_symptoms && data.abdominal_gastrointestinal_symptoms.bloating_gas === true;"}, {"key": "bloating_typical_vs_new", "type": "radio", "input": true, "label": "Is the bloating or gas similar to your previous episodes, or is it different this time?", "values": [{"label": "Typical - feels the same as prior episodes", "value": "typical"}, {"label": "Different - new or unusual compared to prior", "value": "different"}, {"label": "Not sure", "value": "unsure"}], "tableView": true, "confirm_label": "Bloating Typical vs New:", "customConditional": "show = data.onset_bloating && (data.onset_bloating === 'recurrent_1_month' || data.onset_bloating === 'recurrent_1_year' || data.onset_bloating === 'recurrent_many_years');"}, {"key": "liver_dysfunction_symptoms", "type": "selectboxes", "input": true, "label": "Do you have any of the following symptoms?", "values": [{"label": "Jaundice (yellowing of the skin or eyes)", "value": "jaundice"}, {"label": "Cola-colored urine", "value": "dark_urine"}, {"label": "Pale or clay-colored stools", "value": "pale_stools"}], "adminFlag": true, "confirm_label": "Liver Dysfunction Symptoms:", "tableView": true, "optionsLabelPosition": "right"}, {"key": "none_of_the_above_liver_dysfunction_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = !!data.none_of_the_above_liver_dysfunction_symptoms || _.some(_.values(data.liver_dysfunction_symptoms));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "liver_symptoms_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following liver symptoms:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "Liver symptoms", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.liver_dysfunction_symptoms, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "heading_mental_health", "html": "</br><h4>Mental Health</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "mental_health_symptoms", "type": "selectboxes", "confirm_label": "Mental Health Symptoms:", "input": true, "label": "Have you experienced any of the following symptoms?", "values": [{"label": "Low mood for the last 4 weeks", "value": "low_mood", "shortcut": ""}, {"label": "Increased anger or aggression", "value": "anger", "shortcut": ""}, {"label": "<PERSON>lt worried for the last 4 weeks", "value": "anxiety", "shortcut": ""}, {"label": "See things, hear voices, or feel things on my skin that others do not", "value": "psychosis", "shortcut": ""}], "adminFlag": true, "tableView": true, "optionsLabelPosition": "right"}, {"key": "none_of_the_above_mental_health_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "You must select at least one symptom or 'None of the above'."}, "validate": {"custom": "valid = !!data.none_of_the_above_mental_health_symptoms || _.some(_.values(data.mental_health_symptoms));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "mental_health_symptoms_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following mental health symptoms:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "Mental health symptoms", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.mental_health_symptoms, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "heading_mental_health_followup", "html": "</br><h4>Mental Health Symptoms</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.mental_health_symptoms && Object.values(data.mental_health_symptoms).includes(true);"}, {"key": "mental_health_prior_diagnosis", "confirm_label": "Prior Diagnosis:", "type": "radio", "input": true, "label": "Have you ever been diagnosed with or suspected of having depression or anxiety?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "unsure"}], "tableView": true, "customConditional": "show = data.mental_health_symptoms && Object.values(data.mental_health_symptoms).includes(true);"}, {"key": "mental_health_diagnosis_source", "type": "radio", "confirm_label": "Diagnosis Source:", "input": true, "label": "Who made the diagnosis?", "values": [{"label": "Family doctor or primary care provider", "value": "family_doctor"}, {"label": "Psychiatrist", "value": "psychiatrist"}, {"label": "Therapist or counselor", "value": "therapist"}, {"label": "Self-diagnosed or suspected", "value": "self_diagnosed"}, {"label": "Not sure", "value": "unsure"}], "tableView": true, "customConditional": "show = data.mental_health_prior_diagnosis === 'yes';"}, {"key": "mental_health_onset", "confirm_label": "Mental Health Symptoms Onset:", "data": {"values": [{"label": "Within the last week", "value": "under_1_week"}, {"label": "1–4 weeks ago", "value": "1_4_weeks"}, {"label": "1–3 months ago", "value": "1_3_months"}, {"label": "3–6 months ago", "value": "3-6 months ago"}, {"label": "6–12 months ago", "value": "6-12 months ago"}, {"label": "More than a year ago (continuous symptoms)", "value": "over_1_year_continuous"}, {"label": "Symptoms come and go over the past month", "value": "recurrent_1_month"}, {"label": "Symptoms come and go over the past year", "value": "recurrent_1_year"}, {"label": "Symptoms come and go for many years", "value": "recurrent_many_years"}, {"label": "Unsure", "value": "unsure"}]}, "type": "select", "input": true, "label": "When did these symptoms first begin?", "widget": "html5", "tableView": true, "customConditional": "show = data.mental_health_symptoms && Object.values(data.mental_health_symptoms).includes(true);"}, {"key": "mental_health_ped_relationship", "type": "selectboxes", "confirm_label": "PED Relationship:", "input": true, "label": "Which of the following best describes how your mental health symptoms relate to PED or testosterone use?", "values": [{"label": "Symptoms started after a dose change of my PEDs", "value": "Symptoms started after a dose change of my PEDs"}, {"label": "Symptoms started after adding or removing a PED agent", "value": "Symptoms started after adding or removing a PED agent"}, {"label": "Symptoms were present before starting PEDs and have stayed the same", "value": "Symptoms were present before starting PEDs and have stayed the same"}, {"label": "Symptoms were present before PEDs and improved after starting", "value": "Symptoms were present before PEDs and improved after starting"}, {"label": "Symptoms were present before PEDs and worsened after starting", "value": "Symptoms were present before PEDs and worsened after starting"}, {"label": "Symptoms started while on a PED, but unsure of timing", "value": "Symptoms started while on a PED, but unsure of timing"}, {"label": "Symptoms not related to PED or testosterone use", "value": "Symptoms not related to PED or testosterone use"}], "tableView": true, "customConditional": "show = data.mental_health_symptoms && Object.values(data.mental_health_symptoms).includes(true);"}, {"key": "mental_health_prior_treatment", "type": "selectboxes", "input": true, "label": "Have you ever been treated for these symptoms?", "values": [{"label": "Medication (e.g. antidepressants, antianxiety, antipsychotics)", "value": "medication"}, {"label": "Counseling or therapy", "value": "therapy"}, {"label": "Hospitalization or emergency care", "value": "hospitalization"}, {"label": "No prior treatment", "value": "none"}], "tableView": true, "confirm_label": "Prior Treatment:", "customConditional": "show = data.mental_health_symptoms && Object.values(data.mental_health_symptoms).includes(true);"}, {"key": "mental_health_therapy_discussion", "type": "radio", "input": true, "label": "Have you previously discussed these symptoms with a therapist or mental health professional?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "unsure"}], "tableView": true, "confirm_label": "Therapist Discussion:", "customConditional": "show = data.mental_health_symptoms && Object.values(data.mental_health_symptoms).includes(true);"}, {"key": "mental_health_suicidal_check", "type": "radio", "input": true, "label": "Have you had any thoughts of harming yourself or others?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Prefer not to answer", "value": "prefer_not_say"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Suicidal Thoughts Check:", "customConditional": "show = data.mental_health_symptoms && Object.values(data.mental_health_symptoms).includes(true);"}, {"key": "mental_health_si_hi_timing", "type": "radio", "input": true, "label": "Are you currently experiencing thoughts of harming yourself or others?", "values": [{"label": "No – I've had them in the past but not today or recently", "value": "past_only"}, {"label": "I had them recently, but not today", "value": "recent_but_not_today"}, {"label": "Yes – I have them today", "value": "today"}, {"label": "Yes – I have them frequently", "value": "frequent"}, {"label": "Unsure how to describe it", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Suicidal Thoughts Timing:", "customConditional": "show = data.mental_health_suicidal_check === 'yes';"}, {"key": "mental_health_emergency_warning", "html": "<div style='border-left: 4px solid #dc3545; background-color: #f8d7da; padding: 10px; margin-bottom: 10px;'><strong>Emergency Support:</strong> You indicated that you're currently experiencing thoughts of harming yourself or others. Please call <strong>911</strong> or go to the <strong>nearest emergency department</strong> immediately. Help is available right now and your safety matters.</div>", "type": "content", "input": false, "label": "Content", "customConditional": "show = data.mental_health_si_hi_timing === 'today' || data.mental_health_si_hi_timing === 'frequent';"}, {"key": "mental_health_safety_understanding", "type": "radio", "input": true, "label": "Do you understand when to seek emergency care if thoughts of harm arise?", "values": [{"label": "I understand", "value": "understand"}, {"label": "I do not understand", "value": "do_not_understand"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Understands Mental Health Safety Advice:", "customConditional": "show = data.mental_health_suicidal_check === 'no';"}, {"key": "mental_health_safety_warning", "html": "<div style='border-left: 4px solid #ffc107; background-color: #fff3cd; padding: 10px; margin-bottom: 10px;'><strong>Warning:</strong> Even if you are not currently experiencing suicidal or harmful thoughts, please seek same-day in-person care through an emergency department if this changes. Immediate help is available and critical when these symptoms occur.</div>", "type": "content", "input": false, "customConditional": "show = data.mental_health_suicidal_check === 'no';"}, {"key": "heading_safety", "html": "<h5>Safe Injection</h5>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "shared_needles_non_sterile_equipment", "type": "radio", "input": true, "label": "Have you ever shared needles or used non-sterile injection equipment for administering these substances?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "adminFlag": true, "tableView": true, "confirm_label": "Shared Needles or Non-Sterile Equipment:", "optionsLabelPosition": "right"}, {"key": "heading_imaging_tests", "html": "</br><h2><strong>Imaging & Cardiac Tests</strong></h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "prior_imaging_tests", "type": "selectboxes", "input": true, "label": "Have you had any of the following imaging or cardiac tests completed?", "values": [{"label": "Electrocardiogram (ECG/EKG)", "value": "ecg"}, {"label": "Stress Test (Exercise or Chemical)", "value": "stress_test"}, {"label": "Echocardiogram (Heart Ultrasound)", "value": "echocardiogram"}, {"label": "I have not had these tests completed", "value": "no_prior_imaging"}], "inputType": "checkbox", "tableView": true, "confirm_label": "Prior Imaging Tests:", "optionsLabelPosition": "right"}, {"key": "heading_ecg", "html": "<h3>Electrocardiogram (ECG/EKG)</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.prior_imaging_tests?.ecg;"}, {"key": "last_ecg_test", "data": {"values": [{"label": "<1 month ago", "value": "< 1 month ago"}, {"label": "1-2 months ago", "value": "1-2_months"}, {"label": "2-3 months ago", "value": "2-3_months"}, {"label": "3-6 months ago", "value": "3-6_months"}, {"label": "6-12 months ago", "value": "6-12_months"}, {"label": "1-2 years ago", "value": "1-2_years"}, {"label": "More than 2 years ago", "value": "2+_years"}, {"label": "Never had one", "value": "never_had"}]}, "type": "select", "input": true, "label": "When was your last ECG test completed?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Last ECG Test:", "customConditional": "show = data.prior_imaging_tests?.ecg;"}, {"key": "ecg_findings", "type": "selectboxes", "input": true, "label": "Were any of the following findings reported in your ECG?", "values": [{"label": "Normal ECG", "value": "normal_ecg"}, {"label": "Atrial fibrillation", "value": "afib"}, {"label": "Left ventricular hypertrophy (LVH)", "value": "lvh"}, {"label": "Bundle branch block", "value": "bbb"}, {"label": "ST-T wave abnormalities", "value": "st_t_wave"}, {"label": "I was told it was abnormal but don't know why", "value": "abnormal_unknown"}, {"label": "I don't remember, but I was told it was normal", "value": "dont_remember_normal"}, {"label": "Other", "value": "other"}], "tableView": true, "confirm_label": "ECG Findings:", "customConditional": "show = data.prior_imaging_tests?.ecg === true;"}, {"key": "ecg_other_findings", "type": "textarea", "input": true, "label": "Please describe any other ECG findings:", "tableView": true, "confirm_label": "ECG Other Findings:", "autoExpand": true, "customConditional": "show = data.ecg_findings?.other;"}, {"key": "heading_stress_test", "html": "<h3>Stress Test</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.prior_imaging_tests?.stress_test;"}, {"key": "stress_test_findings", "type": "selectboxes", "input": true, "label": "Were you told any of the following about your stress test results?", "values": [{"label": "It was normal", "value": "normal_stress_test"}, {"label": "There were signs of poor blood flow to the heart during activity", "value": "ischemia"}, {"label": "Your heart rhythm became irregular during the test", "value": "arrhythmia"}, {"label": "You were unable to exercise as long as expected", "value": "low_tolerance"}, {"label": "I was told it was abnormal but not given a clear reason", "value": "abnormal_unknown"}, {"label": "I don't remember the details, but I was told it was normal", "value": "dont_remember_normal"}, {"label": "Other", "value": "other"}], "tableView": true, "confirm_label": "Stress Test Findings:", "customConditional": "show = data.prior_imaging_tests?.stress_test === true;"}, {"key": "stress_test_other_findings", "type": "textarea", "input": true, "label": "Please describe any other stress test findings:", "tableView": true, "autoExpand": true, "confirm_label": "Stress Test Other Findings:", "customConditional": "show = data.stress_test_findings?.other;"}, {"key": "heading_echocardiogram", "html": "<h3>Echocardiogram</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.prior_imaging_tests?.echocardiogram;"}, {"key": "echocardiogram_findings", "type": "selectboxes", "input": true, "label": "Were any of the following findings reported in your echocardiogram?", "values": [{"label": "Normal echocardiogram", "value": "normal_echo"}, {"label": "Left ventricular hypertrophy (LVH)", "value": "lvh"}, {"label": "Reduced ejection fraction (EF)", "value": "low_ef"}, {"label": "Valve abnormalities", "value": "valve_disease"}, {"label": "I was told it was abnormal but don't know why", "value": "abnormal_unknown"}, {"label": "I don't remember, but I was told it was normal", "value": "dont_remember_normal"}, {"label": "Other", "value": "other"}], "tableView": true, "confirm_label": "Echocardiogram Findings:", "customConditional": "show = data.prior_imaging_tests?.echocardiogram === true;"}, {"key": "echocardiogram_other_findings", "type": "textarea", "input": true, "label": "Please describe any other echocardiogram findings:", "tableView": true, "autoExpand": true, "confirm_label": "Echocardiogram Other Findings:", "customConditional": "show = data.echocardiogram_findings?.other;"}, {"key": "bp_monitoring_frequency", "type": "radio", "input": true, "label": "How often do you check your blood pressure at home?", "values": [{"label": "Daily", "value": "daily"}, {"label": "A few times a week", "value": "A few times a week"}, {"label": "Once a week", "value": "weekly"}, {"label": "Occasionally / infrequently", "value": "infrequent"}, {"label": "Never check at home", "value": "never"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Blood Pressure Monitoring Frequency:"}, {"key": "bp_readings_per_sitting", "type": "radio", "input": true, "label": "How many blood pressure readings do you typically take in a single sitting?", "values": [{"label": "1 reading", "value": "1"}, {"label": "2 readings", "value": "2"}, {"label": "3 readings", "value": "3"}, {"label": "More than 3 readings", "value": "more_than_3"}, {"label": "Not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = !!data.bp_monitoring_frequency;"}, {"key": "heading_blood_pressure", "html": "<h2><strong>Blood Pressure Reading</strong></h2><p>Please enter your most recent blood pressure measurement (today, if possible).</p>", "type": "content", "input": false, "label": "Content", "tableView": false}, {"key": "blood_pressure_row", "type": "columns", "input": false, "label": "Blood Pressure Input", "columns": [{"width": 3, "components": [{"key": "systolic_bp", "type": "number", "input": true, "label": "Systolic (top number)", "validate": {"max": 250, "min": 50}, "tableView": true, "placeholder": "e.g., 130", "confirm_label": "Systolic BP:"}]}, {"width": 3, "components": [{"key": "diastolic_bp", "type": "number", "input": true, "label": "Diastolic (bottom number)", "validate": {"max": 150, "min": 30}, "tableView": true, "placeholder": "e.g., 80", "confirm_label": "Diastolic BP:"}]}], "tableView": true}, {"key": "bp_reading_date", "data": {"values": [{"label": "Today", "value": "today"}, {"label": "Within the last 48 hours", "value": "Within the last 48 hours"}, {"label": "Within the last 7 days", "value": "Within the last 7 days"}, {"label": "Within the last 14 days", "value": "Within the last 14 days"}, {"label": "More than 30 days ago", "value": "More than 30 days ago"}]}, "type": "select", "input": true, "label": "When was this blood pressure reading taken?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "BP Reading Date:", "customConditional": "show = !!data.systolic_bp || !!data.diastolic_bp;"}, {"key": "bp_reading_confirmed", "type": "radio", "input": true, "label": "Are these blood pressure values accurate (based on your monitor reading today)?", "values": [{"label": "Yes, this is my accurate reading", "value": "yes"}, {"label": "No, I made a mistake or I'm not sure", "value": "no"}], "validate": {"required": true}, "tableView": true, "confirm_label": "BP Reading Confirmed:", "customConditional": "show = data.systolic_bp > 180 || data.diastolic_bp > 100;"}, {"key": "bp_symptoms_check", "type": "selectboxes", "input": true, "label": "Are you currently experiencing any of the following symptoms?", "values": [{"label": "Chest pain or pressure", "value": "chest_pain"}, {"label": "Severe headache", "value": "headache"}, {"label": "Shortness of breath", "value": "shortness_of_breath"}, {"label": "Blurred vision", "value": "vision"}, {"label": "Trouble speaking or moving", "value": "neuro"}, {"label": "Dizziness or lightheadedness", "value": "dizziness"}], "tooltip": "These symptoms could indicate a hypertensive emergency.", "tableView": true, "confirm_label": "BP Symptoms Check:", "customConditional": "show = data.bp_reading_confirmed === 'yes' && ((data.systolic_bp > 180 && data.systolic_bp <= 200) || (data.diastolic_bp > 100 && data.diastolic_bp <= 110));"}, {"key": "bp_urgency_advice", "html": "<div style='background:#fff3cd; padding:10px; border-left:5px solid #ffeeba;'><strong>Important:</strong> Your blood pressure is elevated. Please visit a walk-in clinic or urgent care today to confirm your reading and ensure proper management.</div>", "type": "content", "input": false, "label": "BP Urgency Advisory", "customConditional": "show = data.bp_reading_confirmed === 'yes' && ((data.systolic_bp > 180 && data.systolic_bp <= 200) || (data.diastolic_bp > 100 && data.diastolic_bp <= 110)) && !Object.values(data.bp_symptoms_check || {}).includes(true);"}, {"key": "bp_urgency_advice_understanding", "type": "radio", "input": true, "label": "Do you understand the recommendation to follow up with a walk-in clinic or urgent care today?", "values": [{"label": "I understand", "value": "understand"}, {"label": "I do not understand", "value": "do_not_understand"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Understands BP Urgency Advisory:", "customConditional": "show = data.bp_reading_confirmed === 'yes' && ((data.systolic_bp > 180 && data.systolic_bp <= 200) || (data.diastolic_bp > 100 && data.diastolic_bp <= 110)) && !Object.values(data.bp_symptoms_check || {}).includes(true);"}, {"key": "bp_emergency_advice", "html": "<div style='background:#f8d7da; padding:10px; border-left:5px solid #f5c6cb;'><strong>Emergency Alert:</strong> Your blood pressure is elevated sufficient and/or symptoms suggest a medical emergency. Please go to the <strong>nearest emergency department immediately</strong> to have your blood pressure checked and treated safely. If your blood pressure values were entered in error, you can revise them</div>", "type": "content", "input": false, "label": "BP Emergency Advisory", "tableView": true, "customConditional": "show = data.bp_reading_confirmed === 'yes' && ((data.systolic_bp > 200 || data.diastolic_bp > 110) || Object.values(data.bp_symptoms_check || {}).includes(true));"}, {"key": "bp_emergency_advice_understanding", "type": "radio", "input": true, "label": "Do you understand this recommendation to go to the emergency department immediately?", "values": [{"label": "I understand", "value": "understand"}, {"label": "I do not understand", "value": "do_not_understand"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Understands BP Emergency Advisory:", "customConditional": "show = data.bp_reading_confirmed === 'yes' && ((data.systolic_bp > 200 || data.diastolic_bp > 110) || Object.values(data.bp_symptoms_check || {}).includes(true));"}, {"key": "bp_low_emergency_advice", "html": "<div style='background:#f8d7da; padding:10px; border-left:5px solid #f5c6cb;'><strong>Emergency Alert:</strong> Your blood pressure appears to be dangerously low. If you are feeling faint, dizzy, weak, or unwell, please go to the <strong>nearest emergency department immediately</strong>.</div>", "type": "content", "input": false, "label": "Low Blood Pressure Emergency Alert", "customConditional": "show = data.bp_reading_confirmed === 'yes' && (data.systolic_bp < 70 || data.diastolic_bp < 50);"}, {"key": "heading_in_office_monitoring", "html": "</br><h2><strong>In-Office Monitoring and Follow-Up</strong></h2><p>Please provide information about recent checkups and whether you have a regular healthcare provider.</p>", "type": "content", "input": false, "label": "Content", "tableView": false}, {"key": "last_office_bp_check", "data": {"values": [{"label": "< 1 month ago", "value": "< 1 month ago"}, {"label": "1-2 months ago", "value": "1-2 months ago"}, {"label": "2-3 months ago", "value": "2-3 months ago"}, {"label": "3-6 months ago", "value": "3-6 months ago"}, {"label": "6-12 months ago", "value": "6-12 months ago"}, {"label": "More than a year ago", "value": "More than a year ago"}, {"label": "Never / not sure", "value": "Never / not sure"}]}, "type": "select", "input": true, "label": "When was your last in-office blood pressure check (e.g. at a doctor's office or pharmacy)?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Last Office BP Check:"}, {"key": "clinic_vs_home_reading", "type": "radio", "input": true, "label": "Was the reading taken in clinic or pharmacy similar to your home readings?", "values": [{"label": "Yes - it was similar", "value": "similar"}, {"label": "No - it was higher", "value": "higher"}, {"label": "No - it was lower", "value": "lower"}, {"label": "I don't remember", "value": "dont_remember"}, {"label": "Not applicable", "value": "not_applicable"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Clinic vs Home Reading:", "customConditional": "show = data.last_office_bp_check && data.last_office_bp_check !== 'Never / not sure';"}, {"key": "last_physical_exam", "data": {"values": [{"label": "< 1 month ago", "value": "< 1 month ago"}, {"label": "1-2 months ago", "value": "1-2 months ago"}, {"label": "2-3 months ago", "value": "2-3 months ago"}, {"label": "3-6 months ago", "value": "3-6 months ago"}, {"label": "6-12 months ago", "value": "6-12 months ago"}, {"label": "More than a year ago", "value": "More than a year ago"}, {"label": "Never / not sure", "value": "Never / not sure"}]}, "type": "select", "input": true, "label": "When was your last in-office heart and lung physical exam (by a doctor or nurse practitioner)?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Last Physical Exam:"}, {"key": "has_regular_provider", "type": "radio", "input": true, "label": "Do you have a regular family doctor or nurse practitioner you see for exams?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Regular Provider:"}, {"key": "exam_interval_warning", "html": "<div style='background:#fff3cd; padding:10px; border-left:5px solid #ffeeba;'><strong>Reminder:</strong> It is recommended to have a heart and lung exam at least once every 6 months. If it's been more than 3 months, please consider scheduling a check-up with your doctor, nurse practitioner or visit a local walk-in clinic.</div>", "type": "content", "input": false, "label": "Exam Frequency Advice", "tableView": true, "customConditional": "show = data.last_physical_exam === '3-6 months ago' || data.last_physical_exam === '6-12 months ago' || data.last_physical_exam === 'More than a year ago' || data.last_physical_exam === 'Never / not sure';"}, {"key": "content2", "html": "<h2>Questions for the Doctor</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "any_other_questions", "type": "radio", "input": true, "label": "Do you have any specific questions for the healthcare provider?", "inline": false, "values": [{"label": "Yes, I have additional questions I would like to discuss", "value": true, "shortcut": ""}, {"label": "No, I am interested primarily in echocardiogram testing", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "defaultValue": false, "confirm_label": "Any Other Questions:", "optionsLabelPosition": "right"}, {"key": "stated_other_questions", "type": "textarea", "input": true, "label": "Please use this area to ask any questions that you might have for your health care provider:", "adminFlag": true, "tableView": true, "autoExpand": false, "customConditional": "show = data.any_other_questions === true;"}, {"key": "intake_kind", "type": "textfield", "input": true, "label": "Intake Kind", "hidden": true, "disabled": true, "tableView": true, "clearOnHide": false, "calculateValue": "value = !data.intake_denial_reasons?.length ? 'mail' : 'chat';"}, {"key": "intake_mt_key", "type": "textfield", "input": true, "label": "Intake Mail Template Key", "hidden": true, "disabled": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = 'intake-ped'"}]}