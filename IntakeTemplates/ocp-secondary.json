{"components": [{"key": "content", "html": "<h2 style=\"text-align:center;\">Intake History For OCP</h2><p><i>Please answer some questions about your medical history to ensure the doctor can provide you with birth control medication safely. &nbsp;At the time of your messaging conversation, you will be asked to provide a copy of your blood pressure reading.&nbsp;</i></p>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "doYouPlanOnBecomingPregnantWithinTheNextYear", "type": "radio", "input": true, "label": "Do you plan on becoming pregnant within the next year?", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "I don't know", "value": "iDontKnow", "shortcut": ""}], "tableView": false, "optionsLabelPosition": "right"}, {"key": "adviceIfYouArePlanningOnGettingPregnantWithinTheNextYearWeRecommendStoppingYourBirthControl3MonthsBeforeYouStartTryingUsingAnotherFormOfBirthControlIECondomsToAllowYourCyclesToReturnWeAlsoSuggestStartingAPrenatalVitamin3MonthsBeforeYouTryToConceive", "type": "radio", "input": true, "label": "Advice: If you are planning on getting pregnant within the next year, we recommend stopping your birth control 3 months before you start trying (while using another form of contraception i.e. condoms) to allow your cycles to return.  We also suggest starting a prenatal vitamin 3 months before you try to conceive. ", "inline": false, "values": [{"label": "I understand", "value": "iUnderstand", "shortcut": ""}, {"label": "I do not understand", "value": "iDoNotUnderstand", "shortcut": ""}], "tableView": false, "conditional": {"eq": "yes", "show": true, "when": "doYouPlanOnBecomingPregnantWithinTheNextYear"}, "optionsLabelPosition": "right"}, {"key": "pleaseSelectYourReasonSForBeingOnBirthControl", "type": "selectboxes", "input": true, "label": "Please select your reason(s) for being on birth control:", "values": [{"label": "Family planning (preventing pregnancy)", "value": "familyPlanningPreventingPregnancy", "shortcut": ""}, {"label": "Painful Menstrual Cramps", "value": "painfulMenstrualCramps", "shortcut": ""}, {"label": "Control Acne", "value": "controlAcne", "shortcut": ""}, {"label": "Regulate my menstrual cycles (i.e. make them predictable)", "value": "regulateMyMenstrualCycles", "shortcut": ""}, {"label": "Reduce menstrual flow", "value": "reduceFlow", "shortcut": ""}, {"label": "Manage my endometriosis", "value": "manageMyEndometriosis", "shortcut": ""}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON>st<PERSON><PERSON><PERSON><PERSON><PERSON>", "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}], "inputType": "checkbox", "tableView": false, "defaultValue": {"other": false, "reduceFlow": false, "controlAcne": false, "menstrualHeadache": false, "manageMyEndometriosis": false, "painfulMenstrualCramps": false, "regulateMyMenstrualCycles": false, "familyPlanningPreventingPregnancy": false}, "optionsLabelPosition": "right"}, {"key": "youSelectedOtherPleaseListYourReasonBelow", "type": "textarea", "input": true, "label": "You selected 'other', please list your reason below:", "tableView": true, "autoExpand": false, "conditional": {"eq": "other", "show": true, "when": "youSelectedOtherPleaseSpecifyYourReasonForWantingToChangeBirthControlPills"}}, {"key": "areYouCurrentlyOnABirthControlPill", "type": "radio", "input": true, "label": "Are you currently on a birth control pill?", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}], "tableView": false, "optionsLabelPosition": "right"}, {"key": "takingBirthControlPillsRequiresYouToTakeAPillEveryDayIfYouAreSomeoneWhoIsProneToForgettingOrMissingPillsBirthControlPillsAreNotTheRecommendedFormOfContraceptionAreYouSomeoneWhoCanReliablyTakePillsEveryDayWithoutMissingThem", "type": "radio", "input": true, "label": "Taking birth control pills requires you to take a pill every day.  If you are someone who is prone to forgetting or missing pills, birth control pills are not the recommended form of contraception.  Are you someone who can reliably take pills every day without missing them?", "inline": false, "values": [{"label": "Yes, I can take them daily", "value": "yesICanTakeThemDaily", "shortcut": ""}, {"label": "No, I'm prone to forgetting pills", "value": "noImProneToForgettingPills", "shortcut": ""}], "tableView": false, "conditional": {"eq": "no", "show": true, "when": "areYouCurrentlyOnABirthControlPill"}, "optionsLabelPosition": "right"}, {"key": "ifYouAreNotCurrentlyOnBirthControlHaveYouReviewedTheOtherOptionsThatAreAvailableIEIudHormoneShotEstrogenPatchEtcYouCanReadMoreAboutOtherOptionsHere", "type": "radio", "input": true, "label": "If you are not currently on birth control, have you reviewed the other options that are available (i.e. IUD, hormone shot, estrogen patch, etc)?  You can read more about other options here: https://docs.teletest.ca/order-guide-birth-control.", "inline": false, "values": [{"label": "Yes I have reviewed them and would like a pill option ", "value": "yesIHaveReviewedThemAndWouldLikeAPillOption", "shortcut": ""}, {"label": "Yes I have reviewed them and would like to talk to a doctor to clarify what is best for me.", "value": "yesIHaveReviewedThemAndWouldLikeToTalkToADoctorToClarifyWhatIsBestForMe", "shortcut": ""}, {"label": "No I have not reviewed them, and would like to talk with a doctor", "value": "noIHaveNotReviewedThemAndWouldLikeToTalkWithADoctor", "shortcut": ""}], "tableView": false, "conditional": {"eq": "no", "show": true, "when": "areYouCurrentlyOnABirthControlPill"}, "optionsLabelPosition": "right"}, {"key": "areYouHappyWithYourBirthControlPillOrWouldYouLikeToChangeItToAnotherBirthControlPillOrAnotherContraceptiveMethod", "type": "radio", "input": true, "label": "Are you happy with your birth control pill, or would you like to change it to another birth control pill or another contraceptive method?", "inline": false, "values": [{"label": "I am happy with my current pill", "value": "iAmHappyWithMyCurrentPill", "shortcut": ""}, {"label": "I would like to change to another pill", "value": "changePill", "shortcut": ""}, {"label": "I would like to trial another method ", "value": "iWouldLikeToTrialAnotherMethod", "shortcut": ""}], "tableView": false, "conditional": {"eq": "yes", "show": true, "when": "areYouCurrentlyOnABirthControlPill"}, "optionsLabelPosition": "right"}, {"key": "pleaseListAnyPreviousBirthControlBrandsYouveBeenOnAndIfYouDidntLikeAParticularOneIfYouDontRememberPleaseLeaveThisAreaBlank", "type": "textarea", "input": true, "label": "Please list any previous birth control brands you've been on, and if you didn't like a particular one.  If you don't remember, please leave this area blank.", "tableView": true, "autoExpand": false, "conditional": {"eq": "changePill", "show": true, "when": "areYouHappyWithYourBirthControlPillOrWouldYouLikeToChangeItToAnotherBirthControlPillOrAnotherContraceptiveMethod"}}, {"key": "youWantToChangeContraceptivesPleaseStateWhatSideEffectsYouAreHavingOrCheckOtherToProvideAFreeTextReasonForChanging", "type": "selectboxes", "input": true, "label": "You want to change contraceptives: please state what side effects you are having or check other to provide a free-text reason for changing:", "values": [{"label": "<PERSON><PERSON><PERSON>", "value": "nausea", "shortcut": ""}, {"label": "Breast Tenderness", "value": "breastTenderness", "shortcut": ""}, {"label": "Bloating and Fluid Retention", "value": "bloatingAndFluidRetention", "shortcut": ""}, {"label": "Headaches", "value": "headaches", "shortcut": ""}, {"label": "Painful Periods", "value": "painfulPeriods", "shortcut": ""}, {"label": "Low Libido", "value": "lowLibido", "shortcut": ""}, {"label": "Breakthrough Bleeding", "value": "breakthrough", "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}], "inputType": "checkbox", "tableView": false, "conditional": {"eq": "changePill", "show": true, "when": "areYouHappyWithYourBirthControlPillOrWouldYouLikeToChangeItToAnotherBirthControlPillOrAnotherContraceptiveMethod"}, "defaultValue": {"other": false, "nausea": false, "headaches": false, "lowLibido": false, "breakthrough": false, "painfulPeriods": false, "breastTenderness": false, "breakthroughBleeding": false, "bloatingAndFluidRetention": false}, "optionsLabelPosition": "right"}, {"key": "youSelectedOtherPleaseSpecifyYourReasonForWantingToChangeBirthControlPills", "type": "textarea", "input": true, "label": "You selected other, please specify your reason for wanting to change birth control pills:", "tableView": true, "autoExpand": false, "conditional": {"eq": "other", "show": true, "when": "youWantToChangeContraceptivesPleaseStateWhatSideEffectsYouAreHavingOrCheckOtherToProvideAFreeTextReasonForChanging"}}, {"key": "howLongHaveYouBeenOnTheSameBirthControlFor", "type": "selectboxes", "input": true, "label": "How long have you been on the same birth control for?", "values": [{"label": "3 months or less", "value": "3Months", "shortcut": ""}, {"label": "More than 3 months", "value": "3+months", "shortcut": ""}], "inputType": "checkbox", "tableView": false, "conditional": {"eq": "breakthrough", "show": true, "when": "youWantToChangeContraceptivesPleaseStateWhatSideEffectsYouAreHavingOrCheckOtherToProvideAFreeTextReasonForChanging"}, "defaultValue": {"3Months": false, "3+months": false, "36Months": false}, "optionsLabelPosition": "right"}, {"key": "ifYouAreOnTheSameBrandBirthControlForMoreThan3MonthsAndYouAreExperiencingNewBreakthroughBleedingItIsImportantToHaveACervicalExamAndOrPelvicUltrasound", "type": "radio", "input": true, "label": "If you are on the same brand birth control for more than 3 months, and you are experiencing new breakthrough bleeding, it is important to have a cervical exam and/or pelvic ultrasound.  ", "inline": false, "values": [{"label": "I understand", "value": "iUnderstand", "shortcut": ""}, {"label": "I do not understand", "value": "iDoNotUnderstand", "shortcut": ""}], "tableView": false, "conditional": {"eq": "3+months", "show": true, "when": "howLongHaveYouBeenOnTheSameBirthControlFor"}, "optionsLabelPosition": "right"}, {"key": "content1", "html": "<h2 style=\"text-align:center;\"><strong>Hormonal Contraception Safety Assessment</strong></h2><h4>Please answer a few questions about other health conditions. &nbsp;This information is medically necessary to determine if birth control pills are appropriate for you.</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "doAnyOfTheFollowingApplyToYou", "type": "selectboxes", "input": true, "label": "Do any of the following apply to you?", "values": [{"label": "I'm currently pregnant", "value": "am<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shortcut": ""}, {"label": "Have had a baby within the last 42 days", "value": "haveHadABabyWithinTheLast42Days", "shortcut": ""}, {"label": "None of the above", "value": "noneOfTheAbove", "shortcut": ""}], "inputType": "checkbox", "tableView": false, "defaultValue": {"noneOfTheAbove": false, "amCurrentlyPregnant": false, "haveHadABabyWithinTheLast42Days": false}, "optionsLabelPosition": "right"}, {"key": "doYouHaveAnyOfTheFollowing2", "type": "selectboxes", "input": true, "label": "Do you have any of the following?", "values": [{"label": "Diabetes", "value": "diabetes", "shortcut": ""}, {"label": "High Blood Pressure", "value": "highBloodPressure", "shortcut": ""}, {"label": "High Cholesterol", "value": "highCholesterol", "shortcut": ""}, {"label": "None of the above", "value": "noneOfTheAbove", "shortcut": ""}], "inputType": "checkbox", "tableView": false, "defaultValue": {"diabetes": false, "noneOfTheAbove": false, "highCholesterol": false, "highBloodPressure": false}, "optionsLabelPosition": "right"}, {"key": "ifYouHaveHadDiabetesHaveYouHad", "type": "selectboxes", "input": true, "label": "If you have had diabetes, have you had:", "values": [{"label": "Diabetes for more than 20 years", "value": "diabetesForMoreThan20Years", "shortcut": ""}, {"label": "Complications including diabetic eye disease, nerve damage (numbness), or kidney damage", "value": "complicationsIncludingDiabeticEyeDiseaseNerveDamageNumbnessOrKidneyDamage", "shortcut": ""}], "inputType": "checkbox", "tableView": false, "conditional": {"eq": "diabetes", "show": true, "when": "doYouHaveAnyOfTheFollowing2"}, "optionsLabelPosition": "right"}, {"key": "doYouHaveAnyOfTheFollowing3", "type": "selectboxes", "input": true, "label": "Have you had any of the following now or in the past?", "values": [{"label": "Gallstones", "value": "gallstones", "shortcut": ""}, {"label": "<PERSON><PERSON>", "value": "lupus", "shortcut": ""}, {"label": "Liver cirrhosis", "value": "liverCirrhosis", "shortcut": ""}, {"label": "Liver cancer", "value": "liverCancer", "shortcut": ""}, {"label": "Breast Cancer", "value": "breastCancer", "shortcut": ""}, {"label": "Hepatitis B or C", "value": "hepatitisBOrC", "shortcut": ""}, {"label": "Stroke or Mini-Stroke (TIA)", "value": "strokeOrMiniStrokeTia", "shortcut": ""}, {"label": "I don't understand", "value": "iDontUnderstand", "shortcut": ""}, {"label": "None of the above", "value": "noneOfTheAbove", "shortcut": ""}], "inputType": "checkbox", "tableView": false, "defaultValue": {"lupus": false, "gallstones": false, "liverCancer": false, "breastCancer": false, "hepatitisBOrC": false, "liverCirrhosis": false, "noneOfTheAbove": false, "iDontUnderstand": false, "strokeOrMiniStrokeTia": false}, "optionsLabelPosition": "right"}, {"key": "haveYouHadAnyOfTheFollowingCardiacConditions", "type": "selectboxes", "input": true, "label": "Have you had any of the following cardiac conditions diagnosed?", "values": [{"label": "Cardiomyopathy", "value": "cardiomyopathy", "shortcut": ""}, {"label": "Heart valve problems ", "value": "heartValveProblems", "shortcut": ""}, {"label": "Had cardiac stents or cardiac bypass ", "value": "hadCardiacStentsOrBypassProcecdure", "shortcut": ""}, {"label": "Told you have plaque buildup in you blood vessels (heart, brain, limbs)", "value": "toldYouHavePlaqueBuildupInYouBloodVesselsHeartBrainLimbs", "shortcut": ""}, {"label": "I don't understand", "value": "iDontUnderstand", "shortcut": ""}, {"label": "None of the above apply to me", "value": "noneOfTheAboveApplyToMe", "shortcut": ""}], "inputType": "checkbox", "tableView": false, "defaultValue": {"cardiomyopathy": false, "iDontUnderstand": false, "heartValveProblems": false, "noneOfTheAboveApplyToMe": false, "hadCardiacStentsOrBypassProcecdure": false, "toldYouHavePlaqueBuildupInYouBloodVesselsHeartBrainLimbs": false}, "optionsLabelPosition": "right"}, {"key": "haveYouEverHadBloodClotsInYourLungsLimbsOrOrgansIEPulmonaryEmbolusOrDeepVeinThrombosis", "type": "radio", "input": true, "label": "Have you ever had blood clots in your lungs, arms/legs or organs (i.e. pulmonary embolus or deep vein thrombosis)?  If you have, doctors usually start you on blood thinners for a minimum of 3 months.  ", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "I don't understand this question", "value": "iDontUnderstandThisQuestion", "shortcut": ""}], "tableView": false, "optionsLabelPosition": "right"}, {"key": "doYouHaveAnyOfTheFollowing", "type": "selectboxes", "input": true, "label": "Have you had any of the following surgeries:", "values": [{"label": "Organ transplant", "value": "hadAnOrganTransplant", "shortcut": ""}, {"label": "Bariatric surgery", "value": "hadBariatricSurgery", "shortcut": ""}, {"label": "Recent surgery requiring you to be bed or chair bound", "value": "recentSurgeryRequiringYouToBeBedOrChairBound", "shortcut": ""}, {"label": "I don't understand this question", "value": "iDontUnderstandThisQuestion", "shortcut": ""}, {"label": "None of the above", "value": "noneOfTheAbove", "shortcut": ""}], "inputType": "checkbox", "tableView": false, "conditional": {"eq": "yes", "show": true, "when": "haveYouEverBeenDiagnosedWithDiabetes"}, "defaultValue": {"haveLupus": false, "cardiomyopathy": false, "noneOfTheAbove": false, "hadBariatricSurgery": false, "hadAnOrganTransplant": false, "hadAHistoryOfStrokeOrTia": false, "haveGallstonesThatCausePain": false, "iDontUnderstandThisQuestion": false, "liverCirrhosisOrLiverCancer": false, "hadDiabetesForMoreThan20Years": false, "aHistoryOrCoronaryArteryDisease": false, "deliveredABabyWithinTheLast42Days": false, "recentSurgeryRequiringYouToBeBedOrChairBound": false, "haveBeenDiagnosedWithPeripheralVascularDisease": false, "haveADiagnosedOrSuspectedProblemWithMyHeartValves": false, "haveANewDiagnosisOfHepatitisCausedByAVirusIEHepatitisCOrB": false, "breastCancerInThePastAtPresentTimeOrBeingInvestigatedForIt": false, "haveHypertensionHighBloodPressureOrAreOnMedicationForBloodPressureControl": false, "haveComplicationsFromDiabetesIncludingKidneyDamageNerveDamageOrDamageToMyEyesRetinopathy": false, "haveAHistoryOfBloodClotsInYourBodyIEArmsLegsHeartLungsOrBrainIEDvtDeepVeinThrombosisOrPePulmonaryEmbolismThisDoesNotIncludePassingBloodClotsWithRegularVaginalBleeding": false}, "optionsLabelPosition": "right"}, {"key": "doYouGetHeadaches", "type": "radio", "input": true, "label": "Do you get headaches?", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "I don't understand this question", "value": "iDontUnderstandThisQuestion", "shortcut": ""}], "tableView": false, "optionsLabelPosition": "right"}, {"key": "ifYouGetHeadachesPleaseCheckAnyOfTheFollowingCharacteristicsThatApplyToYourHeadaches", "type": "selectboxes", "input": true, "label": "Please check any of the following characteristics that apply to your headaches:", "values": [{"label": "Band-like (i.e. someone is wrapping a band around my head)", "value": "bandLikeIESomeoneIsWrappingABandAroundYourHead", "shortcut": ""}, {"label": "Usually one sided", "value": "one-sided", "shortcut": ""}, {"label": "Dull ache", "value": "dull", "shortcut": ""}, {"label": "Head is being squeezed", "value": "someoneIsSqueezingMyHead", "shortcut": ""}, {"label": "Feels like a hat on my head", "value": "feelsLikeACapOnMyHead", "shortcut": ""}, {"label": "Head feels full", "value": "head<PERSON><PERSON><PERSON><PERSON><PERSON>", "shortcut": ""}, {"label": "My headache worsens with physical activity (i.e. working at a computer, going for a walk, etc)", "value": "headacheWorsensWithPhysicalActivity", "shortcut": ""}, {"label": "My headache is 'pulsatile' (i.e. it feels like a throbbing or beating sensation) ", "value": "pulsatileAThrobbingOrBeatingSensation", "shortcut": ""}, {"label": "I get nauseous (feel like throwing up) or vomit (throw up)", "value": "nauseaOrVomiting", "shortcut": ""}, {"label": "I am bothered by light or sound ", "value": "lightOrSoundSensitivity", "shortcut": ""}, {"label": "Head feels full of pressure", "value": "headFeelsFullOfPressure", "shortcut": ""}, {"label": "Pain is mild", "value": "painIsMild", "shortcut": ""}, {"label": "Pain is moderate", "value": "painIsModerate", "shortcut": ""}, {"label": "Pain is severe", "value": "painIsSevere", "shortcut": ""}, {"label": "None of the above", "value": "noneOfTheAbove", "shortcut": ""}], "inputType": "checkbox", "tableView": false, "conditional": {"eq": "yes", "show": true, "when": "doYouGetHeadaches"}, "defaultValue": {"dull": false, "one-sided": false, "painIsMild": false, "painIsSevere": false, "headFeelsFull": false, "noneOfTheAbove": false, "painIsModerate": false, "nauseaOrVomiting": false, "feelsLikeACapOnMyHead": false, "headFeelsFullOfPressure": false, "lightOrSoundSensitivity": false, "someoneIsSqueezingMyHead": false, "headacheWorsensWithPhysicalActivity": false, "pulsatileAThrobbingOrBeatingSensation": false, "bandLikeIESomeoneIsWrappingABandAroundYourHead": false}, "optionsLabelPosition": "right"}, {"key": "doYouGetAnyOfTheFollowingSymptomsWithAMigrane", "type": "selectboxes", "input": true, "label": "Do you get any of the following symptoms with a headache?", "values": [{"label": "Vision changes: any changes in your vision, zigzagging lines, loss of sight, a 'shimmering' sensation in your field of vision", "value": "a", "shortcut": ""}, {"label": "Sensory changes: tingling or a pins-and-needle feeling on your body, altered sensation on your tongue", "value": "b", "shortcut": ""}, {"label": "Muscle changes: weakness of your face, arms or legs ", "value": "c", "shortcut": ""}, {"label": "Language difficulties: difficulties with word-finding, difficulties speaking, selecting incorrect words when speaking", "value": "d", "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}], "inputType": "checkbox", "tableView": false, "defaultValue": {"a": false, "b": false, "c": false, "d": false, "other": false}, "optionsLabelPosition": "right"}, {"key": "whatOtherSymptomsDoYouGet", "type": "textarea", "input": true, "label": "What other symptoms do you get?", "tableView": true, "autoExpand": false, "conditional": {"eq": "other", "show": true, "when": "doYouGetAnyOfTheFollowingSymptomsWithAMigrane"}}, {"key": "doYouHaveAHistoryOfMigraines", "type": "radio", "input": true, "label": "Do you have a history of migraines?", "inline": false, "values": [{"label": "Yes I think I have had migranes in the past", "value": "yes", "shortcut": ""}, {"label": "I have headaches but don't know if they are migranes", "value": "iHaveHeadachesButDontKnowIfTheyAreMigrants", "shortcut": ""}, {"label": "I don't know what a migraine is", "value": "iDontKnowWhatAMigraineIs", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "I don't understand this question", "value": "iDontUnderstandThisQuestion", "shortcut": ""}], "tableView": false, "optionsLabelPosition": "right"}, {"key": "doYouHaveADiagnosisOfHighBloodPressureHypertension", "type": "radio", "input": true, "label": "Do you have a diagnosis of high blood pressure (hypertension)?", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "I don't know", "value": "iDontKnow", "shortcut": ""}], "tableView": false, "optionsLabelPosition": "right"}, {"key": "areYouOnBloodPressurePills", "type": "radio", "input": true, "label": "Are you on blood pressure pills?", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "I don't know", "value": "iDontKnow", "shortcut": ""}], "tableView": false, "optionsLabelPosition": "right"}, {"key": "doYouHaveAnyOfTheFollowing1", "type": "radio", "input": true, "label": "Do you have any of the following blood disorders?", "inline": false, "values": [{"label": "AT deficiency", "value": "atDeficiency", "shortcut": ""}, {"label": "Protein C deficiency", "value": "proteinCDeficiency", "shortcut": ""}, {"label": "Protein S deficiency", "value": "proteinSDeficiency", "shortcut": ""}, {"label": "Factor V Leiden", "value": "factorVLeiden", "shortcut": ""}, {"label": "Prothrombin Mutation", "value": "prothrombinMutation", "shortcut": ""}, {"label": "None of the above ", "value": "noneOfTheAbove", "shortcut": ""}], "tableView": false, "optionsLabelPosition": "right"}, {"key": "doYouCurrentlySmokeOrVape", "type": "selectboxes", "input": true, "label": "Do you currently smoke cigarettes?", "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "Don't know", "value": "dontKnow", "shortcut": ""}], "inputType": "checkbox", "tableView": false, "defaultValue": {"no": false, "yes": false, "dontKnow": false}, "optionsLabelPosition": "right"}, {"key": "howManyCigarettesOnAverageDoYouSmokeInADay", "type": "radio", "input": true, "label": "How many cigarettes on average do you smoke in a day?", "inline": false, "values": [{"label": "< 15 cigarette", "value": "15", "shortcut": ""}, {"label": "15 or more", "value": "15OrMore", "shortcut": ""}], "tableView": false, "conditional": {"eq": "yes", "show": true, "when": "doYouCurrentlySmokeOrVape"}, "optionsLabelPosition": "right"}, {"key": "areYouOnAnyOfTheFollowingMedications", "type": "radio", "input": true, "label": "Are you on any of the following medications?", "inline": false, "values": [{"label": "Rifampin", "value": "rifampin", "shortcut": ""}, {"label": "Seizure medication (i.e. Lamotrigine, phenytoin, carbamazepine, barbiturates, primidone, topiramate, oxcarbazepine)", "value": "seizureMedicationIELamotriginePhenytoinCarbamazepineBarbituratesPrimidoneTopiramateOxcarbazepine", "shortcut": ""}, {"label": "Anti-Viral Medication for HIV", "value": "antiViralsForPrepOrHiv", "shortcut": ""}, {"label": "PrEP", "value": "prEp", "shortcut": ""}, {"label": "None of the above", "value": "noneOfTheAbove", "shortcut": ""}], "tableView": false, "optionsLabelPosition": "right"}, {"key": "content9", "html": "<h2 style=\"text-align:center;\"><strong>Medical Advice</strong></h2><h4 style=\"text-align:center;\">The following section provides common advice a doctor would provide you at the time of renewing your medication. &nbsp;The counselling advice provided below can be found on our FAQ page about birth control.</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "willYouBeStartingYourBirthControlPillsForThe1stTime", "type": "radio", "input": true, "label": "Will you be starting your hormonal contraception for the 1st time?", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}], "tableView": false, "optionsLabelPosition": "right"}, {"key": "pleaseSelectHowYouWouldLikeToStartYourBirthControlPills", "type": "radio", "input": true, "label": "Please select how you would like to start your medication:", "inline": false, "values": [{"label": "Immediately", "value": "immediately", "shortcut": ""}, {"label": "1st day of my menstrual cycle", "value": "1stDay", "shortcut": ""}, {"label": "On a Sunday (avoids bleeding on weekends)", "value": "sundayStart", "shortcut": ""}], "tableView": false, "conditional": {"eq": "yes", "show": true, "when": "willYouBeStartingYourBirthControlPillsForThe1stTime"}, "optionsLabelPosition": "right"}, {"key": "startYourPillPackageOnThe1stDayOfYourMenstrualCycleBackUpBirthControlIsNotRequiredIECondoms", "type": "radio", "input": true, "label": "Start your medication (i.e. pill package, nuva ring, etc) on the 1st day of your menstrual cycle.  Back-up birth control is not required (i.e. condoms). ", "inline": false, "values": [{"label": "I understand", "value": "iUnderstand", "shortcut": ""}, {"label": "I do not understand", "value": "iDoNotUnderstand", "shortcut": ""}], "tableView": false, "conditional": {"eq": "1stDay", "show": true, "when": "pleaseSelectHowYouWouldLikeToStartYourBirthControlPills"}, "optionsLabelPosition": "right"}, {"key": "startYourPillsRightAwayIfItHasBeenMoreThan5DaysSinceThe1stDayOfYourLastMenstrualCycleUseABackUpBirthControlIECondomsForThe1st7DaysOnThePillsCompleteAPregnancyTest2WeeksIntoThePillPackage", "type": "radio", "input": true, "label": "Start your medication (i.e. pill package, nuva ring, etc) right away.  If it has been more than 5 days since the 1st day of your last menstrual cycle, use a back-up birth control (i.e. condoms) for the 1st 7 days on the pills.  Complete a pregnancy test 2 weeks into the pill package. ", "inline": false, "values": [{"label": "I understand", "value": "iUnderstand", "shortcut": ""}, {"label": "I do not understand", "value": "iDoNotUnderstand", "shortcut": ""}], "tableView": false, "conditional": {"eq": "immediately", "show": true, "when": "pleaseSelectHowYouWouldLikeToStartYourBirthControlPills"}, "optionsLabelPosition": "right"}, {"key": "startThePillsOnThe1stSundayAfterYourPeriodBeginsIfItHasBeenMoreThan5DaysSinceThe1stDayOfYourLastMenstrualCycleUseABackUpBirthControlIECondomsForThe1st7DaysOnThePillsCompleteAPregnancyTest2WeeksIntoThePillPackage", "type": "radio", "input": true, "label": "Start your medication (i.e. pill package, nuva ring, etc) on the 1st Sunday after your period begins. If it has been more than 5 days since the 1st day of your last menstrual cycle, use a back-up birth control (i.e. condoms) for the 1st 7 days on the pills.  Complete a pregnancy test 2 weeks into the pill package/nuva ring, etc ", "inline": false, "values": [{"label": "I understand", "value": "iUnderstand", "shortcut": ""}, {"label": "I do not understand", "value": "iDoNotUnderstand", "shortcut": ""}], "tableView": false, "conditional": {"eq": "sundayStart", "show": true, "when": "pleaseSelectHowYouWouldLikeToStartYourBirthControlPills"}, "optionsLabelPosition": "right"}, {"key": "content8", "html": "<h3>How to Use Packages</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "ifYouMissAnyPillsReadAboutWhenAndHowToTakeYourNextPillHereHttpsDocsTeletestCaOrderGuideBirthControl", "type": "radio", "input": true, "label": "If you are on birth control pills and miss any pills, read about when and how to take your next pill here: https://docs.teletest.ca/order-guide-birth-control.", "inline": false, "values": [{"label": "I understand", "value": "iUnderstand", "shortcut": ""}, {"label": "I don't understand", "value": "iDontUnderstand", "shortcut": ""}, {"label": "Doesn't apply to me", "value": "doesntApplyToMe", "shortcut": ""}], "tableView": false, "optionsLabelPosition": "right"}, {"key": "ifYouAreOnCombinedContraceptivePillsPillsWithEstrogenAndProgesteroneAndYourPackagesContainPlaceboPillsDelayingStartingYourPackageWhenYourCurrentPackageFinishesIncreasesYourChanceOfPregnancy", "type": "radio", "input": true, "label": "If you are on hormonal contraception (Nuva Ring, birth control pills, Evra Patch), waiting to start your next cycle (for example, skipping days between packages if you are taking pills) increases your risk of ovulation and pregnancy.  Start your next package promptly after finishing your previous package.", "inline": false, "values": [{"label": "I understand", "value": "iUnderstand", "shortcut": ""}, {"label": "I do not understand", "value": "iDoNotUnderstand", "shortcut": ""}, {"label": "Doesn't apply to me", "value": "doesntApplyToMe", "shortcut": ""}], "tableView": false, "optionsLabelPosition": "right"}, {"key": "content2", "html": "<h2>Questions for the Doctor</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "pleaseUseThisAreaToAskAnyQuestionsThatYouMightHaveForYourHealthCareProvider", "type": "textarea", "input": true, "label": "Please use this area to ask any questions that you might have for your health care provider:", "tableView": true, "autoExpand": false}, {"key": "submit", "type": "button", "input": true, "label": "Submit", "tableView": false, "disableOnInvalid": true}]}