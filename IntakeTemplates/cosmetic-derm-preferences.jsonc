{"components": [{"key": "medication_preferences_intro", "type": "content", "input": false, "html": "<h2><strong>Medication Preferences</strong></h2>", "tableView": false}, {"key": "know_treatment", "type": "radio", "input": true, "label": "A complete list of custom prescriptions currently offered by TeleTest is provided <a href='https://docs.teletest.ca/cosmetic-dermatology/medication-teletest-offered' target='_blank'>here</a>. Do you know what treatment you are interested in?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No, I need to speak with the doctor first", "value": "no"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "medication_preferences_heading", "type": "content", "input": false, "html": "<h2><strong>Medication Selection</strong></h2>", "customConditional": "show = _.get(data, 'know_treatment') === 'yes';"}, {"key": "medication_general_selection", "type": "selectboxes", "input": true, "label": "Please select the ingredients(s) you are interested in:", "values": [{"label": "Azelaic Acid", "value": "azelaic_acid"}, {"label": "Glycolic Acid", "value": "glycolic_acid"}, {"label": "Hydroquinone", "value": "hydroquinone"}, {"label": "Kojic Acid", "value": "kojic_acid"}, {"label": "Triple Creams (e.g., retinoid + hydroquinone + steroid)", "value": "triple_creams"}, {"label": "Vitamin C", "value": "vitamin_c"}, {"label": "Retinoids", "value": "retinoids"}, {"label": "Other", "value": "other"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = _.get(data, 'know_treatment') === 'yes';"}, {"key": "intake_kind", "type": "textfield", "input": true, "label": "Intake Kind", "hidden": true, "disabled": true, "tableView": true, "clearOnHide": false, "calculateValue": "value = 'mail';"}, {"key": "intake_mt_key", "type": "textfield", "input": true, "label": "Intake Mail Template Key", "hidden": true, "disabled": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = 'intake-cosmetic'"}, {"key": "allowed_rt_keys", "type": "textfield", "input": true, "label": "Allowed Rep<PERSON>", "hidden": true, "disabled": true, "multiple": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = []"}]}