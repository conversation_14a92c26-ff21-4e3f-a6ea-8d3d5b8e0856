{% load template_extras %}
{% with data=questionnaire.hpc.data %}
{% with summary=questionnaire.raw_formio_summary %}
{% with rxts=questionnaire.rxts.all %}

<!-- ─────────── INTRODUCTION ─────────── -->
<p>Hi {{ patient.name }},</p>
<p>
  This is {{ doctor.name }} (CPSO&nbsp;#{{ doctor.cpso_number }}) from TeleTest. We’ve reviewed your answers and summarised your treatment plan below.
  If anything looks off, or if your symptoms change, or you have more questions, we can schedule a real‑time messaging chat.
</p>

<!-- ─────────── YOUR ASTHMA HISTORY ─────────── -->
<h5>Your Asthma History</h5>
{% with sections=summary|confirm:"Consult Reason:consult_reason,consult_reason_other;Diagnosis & Testing:age_at_diagnosis_or_first_puffer,asthma_diagnosis_type,asthma_diagnosis_type_other,diagnosing_provider,diagnosing_provider_other,diagnostic_tests_completed,diagnostic_tests_completed_other,methacholine_challenge_test,testing_location_in_na;Current Control:rescue_puffs_week,rescue_max_day,rescue_canisters_year,inhaler_technique_confidence" %}
<ul class="list-unstyled mb-3">
  {% for heading, qas in sections.items %}
    <li class="mb-1">
      <strong>{{ heading }}:</strong>
      <ul class="mb-1">
        {% for qa in qas %}<li>{{ qa|safe }}</li>{% endfor %}
      </ul>
    </li>
  {% endfor %}
</ul>
{% endwith %}

{# ─────────── ASTHMA SYMPTOMS & TRIGGERS ─────────── #}
{% with symptom_sections=summary|confirm:"Symptoms Present:asthma_symptoms_present,confirm_no_asthma_symptoms;Symptoms NOT Present:asthma_symptoms_not_present;Related History:asthma_symptom_onset_pattern,all_asthma_symptoms_start,flare_similarity_to_prior,flare_severity_vs_prior,sleep_waking_nights_per_week;Rescue-Use Pattern:rescue_use_days_past_week,rescue_use_puffs_per_day,rescue_use_night_this_week;Triggers:asthma_symptom_triggers_present,none_of_the_above_asthma_symptom_triggers" %}
  {% if symptom_sections %}
    <h5 class="mt-4">Your Asthma Symptoms &amp; Triggers</h5>
    <ul class="list-unstyled mb-3">
      {% for heading, qas in symptom_sections.items %}
        {% if qas %}
          <li class="mb-1">
            <strong>{{ heading }}:</strong>
            <ul class="mb-1">
              {% for qa in qas %}<li>{{ qa|safe }}</li>{% endfor %}
            </ul>
          </li>
        {% endif %}
      {% endfor %}
    </ul>
  {% endif %}
{% endwith %}


<!-- ─────────── PAST ASTHMA TREATMENTS ─────────── -->
{% if data.past_short_acting_grid or data.past_maintenance_grid or data.past_oral_grid %}
<h5 class="fw-bold">Past Asthma Treatments</h5>

{# ---------- RESCUE PUFFERS ---------- #}
{% if data.past_short_acting_grid %}
<h6 class="mt-3">Rescue (Quick-relief) Puffers</h6>
<table class="table table-sm table-bordered">
  <thead class="table-light">
    <tr><th>Product</th><th>Dose</th><th>Puffs</th><th>Using</th><th>Effect</th><th>Schedule</th><th>Flare&nbsp;doses</th><th>Last&nbsp;on</th></tr>
  </thead>
  <tbody>
  {% for row in data.past_short_acting_grid %}
    <tr>
      <td>{% if row.rx_name == 'other' %}{{ row.rx_other_name }}{% else %}{{ row.rx_name|replace:"_| "|title }}{% endif %}</td>
      <td>{% if row.rx_name == 'other' %}{{ row.rx_other_dose|default:"—" }}{% else %}{{ row.rx_dose|default:"—" }}{% endif %}</td>
      <td>{{ row.rx_units|upper }}</td>
      <td>{% if row.rx_current == 'current' %}Current{% else %}Past{% endif %}</td>
      <td>{{ row.rx_effect|replace:"_| "|title }}</td>
      <td>
        {% if row.rx_freq == 'prn' %}PRN{% elif row.rx_freq == 'q4h' %}q4h{% elif row.rx_freq == 'q6h' %}q6h
        {% elif row.rx_freq == 'qid' %}4×/day{% elif row.rx_freq == 'bid' %}2×/day{% else %}N/S{% endif %}
      </td>
      <td>
        {% if row.rx_flare_times == 'same' %}None{% elif row.rx_flare_times == 'plus1' %}+1
        {% elif row.rx_flare_times == 'plus2' %}+2+{% else %}N/S{% endif %}
      </td>
      <td>
        {% with t=row.rx_last_used %}
          {% if t == 'today' %}Today{% elif t == 'yesterday' %}Y-day{% elif t == '2d' %}2 d
          {% elif t == '3d' %}3 d{% elif t == '4d' %}4 d{% elif t == '5d' %}5 d{% elif t == '6d' %}6 d
          {% elif t == '7d' %}7 d{% elif t == '7d_1m' %}≤ 1 m{% elif t == '1_3m' %}1-3 m
          {% elif t == '3_6m' %}3-6 m{% elif t == '6_12m' %}6-12 m{% elif t == '1_2y' %}1-2 y
          {% elif t == '2_5y' %}2-5 y{% elif t == '5y_plus' %}5 y+{% else %}N/S{% endif %}
        {% endwith %}
      </td>
    </tr>
  {% endfor %}
  </tbody>
</table>
{% endif %}

{# ---------- DAILY PREVENTERS ---------- #}
{% if data.past_maintenance_grid %}
<h6 class="mt-4">Daily Preventer Inhalers</h6>
<table class="table table-sm table-bordered">
  <thead class="table-light">
    <tr><th>Product</th><th>Dose</th><th>Puffs</th><th>Using</th><th>Effect</th><th>Schedule</th><th>Flare&nbsp;doses</th><th>Last&nbsp;on</th></tr>
  </thead>
  <tbody>
  {% for row in data.past_maintenance_grid %}
    <tr>
      <td>{% if row.rx_name == 'other' %}{{ row.rx_other_name }}{% else %}{{ row.rx_name|replace:"_| "|title }}{% endif %}</td>
      <td>{% if row.rx_name == 'other' %}{{ row.rx_other_dose|default:"—" }}{% else %}{{ row.rx_dose|default:"—" }}{% endif %}</td>
      <td>{{ row.rx_units|upper }}</td>
      <td>{% if row.rx_current == 'current' %}Current{% else %}Past{% endif %}</td>
      <td>{{ row.rx_effect|replace:"_| "|title }}</td>
      <td>
        {% if row.rx_freq == 'qd' %}1×/day{% elif row.rx_freq == 'bid' %}2×/day
        {% elif row.rx_freq == 'tid' %}3×/day{% elif row.rx_freq == 'qid' %}4×/day
        {% else %}N/S{% endif %}
      </td>
      <td>
        {% if row.rx_flare_times == 'same' %}None{% elif row.rx_flare_times == 'plus1' %}+1
        {% elif row.rx_flare_times == 'plus2' %}+2+{% else %}N/S{% endif %}
      </td>
      <td>{% include "partials/last_used_map.html" with t=row.rx_last_used %}</td>
    </tr>
  {% endfor %}
  </tbody>
</table>
{% endif %}

{# ---------- ORAL / TABLET MEDICINES ---------- #}
{% if data.past_oral_grid %}
<h6 class="mt-4">Oral / Tablet Medicines</h6>
<table class="table table-sm table-bordered">
  <thead class="table-light">
    <tr><th>Medication</th><th>Dose</th><th>Tabs</th><th>Using</th><th>Effect</th><th>Schedule</th><th>Flare&nbsp;doses</th><th>Last&nbsp;on</th></tr>
  </thead>
  <tbody>
  {% for row in data.past_oral_grid %}
    <tr>
      <td>{% if row.rx_name == 'other' %}{{ row.rx_other_name }}{% else %}{{ row.rx_name|replace:"_| "|title }}{% endif %}</td>
      <td>{{ row.rx_dose|default:"—" }}</td>
      <td>{{ row.rx_units|upper }}</td>
      <td>{% if row.rx_current == 'current' %}Current{% else %}Past{% endif %}</td>
      <td>{{ row.rx_effect|replace:"_| "|title }}</td>
      <td>
        {% if row.rx_freq == 'qd' %}1×/day{% elif row.rx_freq == 'bid' %}2×/day
        {% elif row.rx_freq == 'qod' %}qod{% else %}As&nbsp;directed{% endif %}
      </td>
      <td>
        {% if row.rx_flare_times == 'same' %}None{% elif row.rx_flare_times == 'plus1' %}+1
        {% elif row.rx_flare_times == 'plus2' %}+2+{% else %}N/S{% endif %}
      </td>
      <td>{% include "partials/last_used_map.html" with t=row.rx_last_used %}</td>
    </tr>
  {% endfor %}
  </tbody>
</table>
{% endif %}

{% endif %}
<!-- ─────────── CURRENT TREATMENT PLAN ─────────── -->
{% if data.asthma_plan_table %}
<h2 class="text-center">Current Treatment Plan</h2>
<div class="asthmatbl">
  <table class="table table-sm table-bordered">
    <thead class="table-light">
      <tr><th>Medication</th><th>How to use</th></tr>
    </thead>
    <tbody>
    {% for row in data.asthma_plan_table %}
      <tr>
        <td>{{ row.med|linebreaksbr }}</td>
        <td>{{ row.directions|linebreaksbr }}</td>
      </tr>
    {% endfor %}
    </tbody>
  </table>
</div>
{% endif %}

{% if rxts %}
<h5 class="mt-4">Prescriptions Issued</h5>
<ul>{% for rx in rxts %}<li>{{ rx.display_name }}</li>{% endfor %}</ul>
{% endif %}

<!-- ─────────── STEP-DOWN (MAINTENANCE) RULES ─────────── -->
<div class="card bg-light border-0 my-3">
  <div class="card-body py-2">
    <h6 class="mb-1"><strong>Step-down Rules (when asthma is well-controlled)</strong></h6>
    <ul class="mb-0">
      <li>Consider stepping down only after at least <strong>3&nbsp;months</strong> of stable control – no night-time symptoms, blue puffer needed ≤ 2&nbsp;times/week, and no oral-steroid bursts or ER visits.</li>
      <li>Reduce the <em>controller</em> dose in small, clear steps – e.g.&nbsp;from <em>2&nbsp;puffs twice daily → 1&nbsp;puff twice daily</em>, or from <em>1&nbsp;puff twice daily → 1&nbsp;puff once daily</em> – keeping the same inhaler whenever possible.</li>
      <li><strong>This applies to your maintenance/controller inhaler only.</strong> Your blue Ventolin®/salbutamol puffer stays rescue-only and should always be on hand for sudden tightness.</li>
      <li>Book a review (or send an e-visit log) <strong>6 – 12&nbsp;weeks</strong> after each reduction. If control slips, step back up to the last dose that kept you symptom-free.</li>
      <li><strong>Never stop abruptly.</strong> Most adults still need a very-low-dose controller (e.g.&nbsp;1&nbsp;puff once daily) to guard against severe attacks.</li>
      <li>At every visit, check inhaler technique, adherence, and continue carrying your blue puffer.</li>
    </ul>
    <p class="small text-muted mb-0">Adapted from GINA 2024 &amp; Canadian Thoracic Society asthma step-care guidance.</p>
  </div>
</div>
<!-- ─────────── WHEN TO SEEK IN-PERSON CARE ─────────── -->
<h5 class="mt-4">Asthma Safety — When to Get Checked in Person</h5>
<ol>
  <li><strong>Blue (rescue) puffer more than 4 times in one day,</strong> or relief lasts only a short time and tightness comes back quickly.</li>

  <li><strong>Waking at night from cough, wheeze, or chest tightness more than once a week.</strong></li>

  <li><strong>Still feeling short-of-breath after using the blue puffer.</strong> If you need to catch your breath before finishing a sentence, get seen the same day.</li>

  <li><strong>Had an oral-steroid burst or ER visit for asthma in the past year</strong> and symptoms are creeping up again — don’t wait for them to get bad.</li>

  <li><strong>Breathless while resting, ribs or neck pulling in with each breath,</strong> or you can’t speak in full sentences. Call&nbsp;9-1-1 or go to the nearest Emergency Department.</li>

  <li><strong>Lips or fingertips look blue or grey.</strong> This means oxygen is low — seek emergency help right away.</li>

  <li><strong>Signs of a chest infection</strong> (fever, thick green or bloody mucus) and your breathing is harder even after rescue puffs.</li>

  <li><strong>Side-effects you can’t tolerate</strong> (racing heart, strong shakes, mouth/throat yeast infection) — have your medicines and technique reviewed face-to-face.</li>

  <li><strong>If you’re unsure how to use your inhaler correctly.</strong> A quick in-person demo can make treatment work far better.</li>
</ol>
<p class="small text-muted mb-0">Always keep your rescue puffer with you. For sudden, severe breathing trouble, call&nbsp;9-1-1 — don’t wait for an online reply.</p>

<!-- ─────────── INHALER-TECHNIQUE REMINDERS ─────────── -->
<div class="alert alert-warning mt-4">
  <h5 class="mb-2"><strong>Inhaler Technique & Steroid-Rinse Tips</strong></h5>
  <ul class="mb-0">
    <li><strong>Shake, breathe out, seal lips.</strong> Press the canister once as you start a slow, deep breath—not before.</li>
    <li><strong>Hold your breath for 5–10&nbsp;seconds</strong> so the medicine reaches deep into your lungs.</li>
    <li><strong>Use a spacer if you have one.</strong> It makes timing easier and sends more drug to your airways, less to your mouth.</li>
    <li><strong>Always rinse, gargle, and spit after steroid puffs.</strong> This simple step prevents hoarse voice and mouth-thrush.</li>
    <li>If you taste a blast of medicine on your tongue, <em>reset your technique</em> or ask your pharmacist for a quick demo.</li>
  </ul>
</div>

<p style="margin-top:30px;">
    <strong>Confirmation:</strong> By completing and submitting this intake form, you confirm that the information provided is accurate to the best of your knowledge. You acknowledge that this intake does not replace the need for in-person care when symptoms arise, and that follow-up may be required based on lab results or clinical judgement. You agree to seek emergency or in-person medical care if symptoms worsen or new symptoms develop.
  </p>


<p class="mt-4">Best regards,<br>{{ doctor.name }}</p>

{% endwith %}
{% endwith %}
{% endwith %}