{"components": [{"key": "request_work_note", "type": "radio", "input": true, "label": "Are you requesting a work note?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "absence_duration", "type": "radio", "input": true, "label": "What is the duration of your absence?", "values": [{"label": "3 days or less", "value": "<3_days"}, {"label": "More than 3 days", "value": ">=3_days"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.request_work_note === 'yes';", "optionsLabelPosition": "right"}, {"key": "previous_extension_request", "type": "radio", "input": true, "label": "Is this request to extend a previous work note covering the same illness period?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.request_work_note === 'yes' && data.absence_duration === '<3_days';", "optionsLabelPosition": "right"}, {"key": "reason_for_absence", "type": "selectboxes", "input": true, "label": "What is the reason for your absence? (Select all that apply)", "values": [{"label": "Cold or Influenza", "value": "cold_flu"}, {"label": "<PERSON><PERSON><PERSON> illness", "value": "stomach_illness"}, {"label": "Flare of a health issue", "value": "flare_health_issue"}, {"label": "Menstrual pain", "value": "menstrual_discomfort", "customConditional": "show = data.sex === 'female';"}, {"label": "<PERSON><PERSON> / Migraine", "value": "headache"}, {"label": "Insomnia (i.e. difficulty sleeping)", "value": "insomnia"}, {"label": "Stress / Mental Health", "value": "stress"}], "inputType": "checkbox", "tableView": true, "customConditional": "show = data.request_work_note === 'yes' && data.absence_duration === '<3_days' && data.previous_extension_request === 'no';", "optionsLabelPosition": "right"}, {"key": "no_reason_for_absence", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a reason."}, "validate": {"custom": "valid = !!data.no_reason_for_absence || !!_.some(_.values(data.reason_for_absence));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.request_work_note === 'yes' && data.absence_duration === '<3_days' && data.previous_extension_request === 'no';"}, {"key": "red_flag_physical", "type": "selectboxes", "input": true, "label": "Are you currently experiencing any of the following:", "values": [{"label": "Feel unwell", "value": "feel_unwell"}, {"label": "Chest pain or heaviness", "value": "chest_pain"}, {"label": "Abdominal pain or cramping", "value": "abdominal_cramping"}, {"label": "Shortness of breath", "value": "dyspnea"}, {"label": "Feel lightheaded or faint", "value": "presy<PERSON><PERSON>"}, {"label": "Arm or leg swelling", "value": "limb_swelling"}, {"label": "Heart palpitations (slow or fast heartbeat)", "value": "palpitations"}, {"label": "Coughing up blood", "value": "hemoptysis"}, {"label": "Muscle weakness or cramping", "value": "muscle_cramps"}, {"label": "Pelvic pain", "value": "pelvic_pain", "customConditional": "show = data.sex === 'female';"}], "inputType": "checkbox", "tableView": true, "customConditional": "show = data.request_work_note === 'yes' && data.absence_duration === '<3_days' && data.previous_extension_request === 'no' && _.some(data.reason_for_absence, function(val, key) { return key !== 'stress' && val; }) && !data.no_reason_for_absence;", "optionsLabelPosition": "right"}, {"key": "no_red_flag_physical", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = !!data.no_red_flag_physical || !!_.some(_.values(data.red_flag_physical));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.request_work_note === 'yes' && data.absence_duration === '<3_days' && data.previous_extension_request === 'no' && _.some(data.reason_for_absence, function(val, key) { return key !== 'stress' && val; }) && !data.no_reason_for_absence;"}, {"key": "red_flag_mental", "type": "selectboxes", "input": true, "label": "Are you currently experiencing any of the following:", "values": [{"label": "Having thoughts about harming yourself, ending your life, or hurting others", "value": "si_hi_screen"}, {"label": "Hearing voices or seeing things that others do not", "value": "unusual_sensory_experiences"}, {"label": "Feeling extremely suspicious or fearful of others without clear reason", "value": "paranoid_fears"}, {"label": "Feeling very confused or like your thoughts are all mixed up", "value": "confused_thinking"}, {"label": "Experiencing a sudden, overwhelming sadness or hopelessness", "value": "overwhelming_sadness"}], "inputType": "checkbox", "tableView": true, "customConditional": "show = data.request_work_note === 'yes' && data.absence_duration === '<3_days' && data.previous_extension_request === 'no' && data.reason_for_absence && data.reason_for_absence.stress && !data.no_reason_for_absence;", "optionsLabelPosition": "right"}, {"key": "no_red_flag_mental", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = !!data.no_red_flag_mental || !!_.some(_.values(data.red_flag_mental));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.request_work_note === 'yes' && data.absence_duration === '<3_days' && data.previous_extension_request === 'no' && data.reason_for_absence && data.reason_for_absence.stress && !data.no_reason_for_absence;"}, {"key": "symptoms_improving", "type": "radio", "input": true, "label": "Do you feel your symptoms are improving?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.request_work_note === 'yes' && data.absence_duration === '<3_days' && data.previous_extension_request === 'no' && data.no_red_flag_physical === true && ((data.reason_for_absence && data.reason_for_absence.stress) ? data.no_red_flag_mental === true : true);", "optionsLabelPosition": "right"}, {"key": "no_indications", "type": "textfield", "input": true, "label": "No Indication", "hidden": true, "disabled": true, "multiple": false, "tableView": true, "clearOnHide": false, "defaultValue": true, "calculateValue": "value = (data.request_work_note !== 'yes' || data.absence_duration !== '<3_days' || data.previous_extension_request === 'yes' || data.no_reason_for_absence === true || !_.some(_.values(data.reason_for_absence))) ? true : false;", "refreshOnChange": true}, {"key": "contraindication_keys", "type": "textfield", "input": true, "label": "Contraindication keys", "hidden": true, "disabled": true, "multiple": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "value = _.union(\n  (data.previous_extension_request === 'yes' ? ['extension_request'] : []),\n  (data.absence_duration === '>=3_days' ? ['long_absence'] : []),\n  _.keys(_.pickBy(data.red_flag_physical || {})),\n  _.keys(_.pickBy(data.red_flag_mental || {})),\n  (data.symptoms_improving === 'no' ? ['symptoms_not_improving'] : [])\n);", "refreshOnChange": true}, {"key": "approved_html", "type": "content", "input": true, "tableView": false, "clearOnHide": false, "defaultValue": "<h3 class='text-green'>You're eligible for a work note for your absence.</h3><p>A TeleTest physician may provide a note covering your illness for up to 3 days. You'll answer a few follow-up questions to qualify the nature of your illness.</p>"}, {"key": "rejected_html", "type": "content", "input": true, "tableView": false, "clearOnHide": false, "defaultValue": "<h3 class='text-red'>Based on your responses, we're unable to provide a TeleTest work note at this time.</h3><ul class='text-red'><li>We can only provide work notes for absences <strong>up to 3&nbsp;days</strong>. Please seek in-person care if your absence is longer.</li><li>We do not issue <strong>extensions of previous work notes</strong>; ongoing or recurrent illness requires an in-person assessment.</li></ul><p>Please visit a local clinic for further evaluation.</p>"}]}