{"components": [{"key": "glp1_medication_header", "html": "</br><h2 style=\"text-align:center;\">Weight-Loss Medication Information</h2><p>Please answer the questions below so we can understand how you’re using your medication, any side-effects, and your preferred dose. Your answers will help us tailor your treatment plan.</p>", "type": "content", "input": false, "label": "Content", "tableView": false}, {"key": "dose_change_callout", "tag": "div", "type": "htmlelement", "input": false, "label": "Dose-Change Call-out", "content": "<div style=\"border:1px solid #dee2e6;border-radius:6px;padding:0.9rem;margin-bottom:1rem;background:#f8f9fa;\">\n  <ul style=\"margin:0 0 0 1rem;line-height:1.5;\">\n    <li>Select the option that fits <em>your</em> goals and comfort level.</li>\n    <li><strong>Rapid loss</strong> (&gt; 2&nbsp;lb / week) can increase the risk of <strong>gallstones</strong>—small, hard deposits that form in the gallbladder and can cause severe abdominal pain or require surgery. Let us know if you’re losing weight this quickly so we can slow the dose and reduce that risk.</li>\n    <li>You can open a <strong>secure chat</strong> with your TeleTest physician after submitting this form to discuss any concerns.</li>\n  </ul>\n</div>", "tableView": false}, {"key": "dose_change_faq_accordion", "tag": "div", "type": "htmlelement", "input": false, "content": "<details style=\"border:1px solid #dee2e6;border-radius:6px;padding:0.75rem;margin-bottom:0.75rem;\"><summary style=\"font-weight:600;cursor:pointer;\">🔼 When might my dose go up?</summary><p style=\"margin-top:0.5rem;\">Think about a higher dose only after you’ve been on your current step for at least 4&nbsp;weeks, weight-loss has slowed to less than 1&nbsp;lb (0.5&nbsp;kg) a week, and side-effects feel mild.</p><ul style=\"line-height:1.5;\"><li>We will move you up <em>one</em> step at a time—never faster.</li><li>For the first few days after a change, stick with small, protein-rich meals and sip water often to ease queasy feelings.</li></ul></details>\n\n<details style=\"border:1px solid #dee2e6;border-radius:6px;padding:0.75rem;margin-bottom:0.75rem;\"><summary style=\"font-weight:600;cursor:pointer;\">⏸️ Why might I stay at the same dose?</summary><p style=\"margin-top:0.5rem;\">Staying put is the right move when you’re losing a steady 1–2&nbsp;lb (0.5–1&nbsp;kg) each week and any side-effects are easy to handle.</p><ul style=\"line-height:1.5;\"><li>Holding steady lets your body adjust.</li><li>Plateaus that last less than a month often sort themselves out with small tweaks to food or activity—no dose change needed.</li></ul></details>\n\n<details style=\"border:1px solid #dee2e6;border-radius:6px;padding:0.75rem;margin-bottom:0.75rem;\"><summary style=\"font-weight:600;cursor:pointer;\">🔽 When would my dose go down?</summary><p style=\"margin-top:0.5rem;\">We drop to a lower step if side-effects make daily life tough (for example, repeated vomiting) or if weight is coming off faster than 2&nbsp;lb (1&nbsp;kg) per week.</p><ul style=\"line-height:1.5;\"><li>Your prescriber will guide the change—usually just one step lower.</li><li>After you reach your goal weight, we can test smaller doses every 12&nbsp;weeks and watch for regain.</li><li>If you notice hunger or pounds creeping back, reach out so we can adjust sooner.</li></ul></details>\n\n<details style=\"border:1px solid #dee2e6;border-radius:6px;padding:0.75rem;\"><summary style=\"font-weight:600;cursor:pointer;\">🎯 What is a maintenance dose?</summary><p style=\"margin-top:0.5rem;\">A maintenance dose is the <strong>smallest (or no) weekly amount</strong> that keeps your weight within about 3&nbsp;% of your goal for at least 3&nbsp;months while you feel well.</p><ul style=\"line-height:1.5;\"><li>You can pause or stop injections any time - and if you wish to taper (gradually reduce) your dose, we can do so at any time.</li><li>If your weight drops <em>below</em> your target, we’ll reduce the dose.</li><li>Weigh yourself once a month; if you regain more than 3&nbsp;% from goal, it may be reasonable to re-raise your dose.</li><li>High-protein meals, strength training, and 7+ hours of sleep help keep muscle during maintenance.</li></ul></details>"}, {"key": "currentMeasurementsHeading", "html": "</br><h3 style=\"text-align:center;\">Today's Weight, Height &amp; BMI</h3>", "type": "content", "input": false, "label": "Current Measurements Heading", "tableView": false}, {"key": "currentWeightPrompt", "html": "Please enter your <strong>most recent</strong> weight and height measurements.", "type": "content", "input": false, "label": "Current Weight Prompt", "tableView": false}, {"key": "heightRow", "type": "columns", "input": false, "label": "Height Entry", "columns": [{"width": 2, "components": [{"key": "heightFeet", "data": {"values": [{"label": "4'", "value": 4}, {"label": "5'", "value": 5}, {"label": "6'", "value": 6}, {"label": "7'", "value": 7}]}, "type": "select", "input": true, "label": "Feet", "widget": "html5", "tableView": true, "confirm_label": "Height (feet):", "labelPosition": "top"}]}, {"width": 2, "components": [{"key": "heightInches", "data": {"values": [{"label": "0\"", "value": 0}, {"label": "1\"", "value": 1}, {"label": "2\"", "value": 2}, {"label": "3\"", "value": 3}, {"label": "4\"", "value": 4}, {"label": "5\"", "value": 5}, {"label": "6\"", "value": 6}, {"label": "7\"", "value": 7}, {"label": "8\"", "value": 8}, {"label": "9\"", "value": 9}, {"label": "10\"", "value": 10}, {"label": "11\"", "value": 11}]}, "type": "select", "input": true, "label": "Inches", "widget": "html5", "tableView": true, "confirm_label": "Height (inches):", "labelPosition": "top"}]}, {"width": 1, "components": [{"key": "heightSeparator", "html": "<div style='text-align:center; font-weight:bold; margin-top:30px;'>or</div>", "type": "content", "input": false}]}, {"width": 2, "components": [{"key": "heightValueCM", "type": "number", "input": true, "label": "Height (cm)", "validate": {"max": 300, "min": 50, "custom": "if (input && ((data.heightFeet && data.heightFeet !== '') || (data.heightInches && data.heightInches !== ''))) { valid = 'Please select height in either feet/inches or centimeters, but not both.'; } else { valid = true; }"}, "tableView": true, "placeholder": "cm", "confirm_label": "Height (cm):", "labelPosition": "top"}]}]}, {"key": "weightBmiRow", "type": "columns", "input": false, "label": "Weight and BMI Entry", "columns": [{"width": 2, "components": [{"key": "weightValue", "type": "number", "input": true, "label": "Weight", "validate": {"max": 500, "min": 35}, "tableView": true, "placeholder": "Enter weight", "confirm_label": "Current Weight:", "labelPosition": "top"}]}, {"width": 2, "components": [{"key": "weightUnits", "data": {"values": [{"label": "kg", "value": "kg"}, {"label": "lbs", "value": "lbs"}]}, "type": "select", "input": true, "label": "Units", "widget": "html5", "tableView": true, "defaultValue": "lbs", "confirm_label": "Units:", "labelPosition": "top"}]}, {"width": 1, "components": []}, {"width": 2, "components": [{"key": "bmi", "type": "textfield", "input": true, "label": "BMI", "disabled": true, "tableView": true, "confirm_label": "BMI:", "labelPosition": "top", "calculateValue": "const cmProvided = data.heightValueCM && data.heightValueCM !== ''; const feetProvided = data.heightFeet && data.heightFeet !== ''; const inchesProvided = data.heightInches && data.heightInches !== ''; if ((cmProvided && (feetProvided || inchesProvided))) { value = '0'; } else { const heightM = cmProvided ? (data.heightValueCM / 100) : ((data.heightFeet * 0.3048) + (data.heightInches * 0.0254)); const weightKg = (data.weightUnits === 'lbs') ? (data.weightValue / 2.20462) : data.weightValue; value = (weightKg && heightM) ? (weightKg / Math.pow(heightM, 2)).toFixed(1) : '0'; }"}]}]}, {"key": "currentWeightDate", "type": "datetime", "input": true, "label": "Date this weight was taken:", "format": "yyyy-MM-dd", "widget": {"mode": "single", "type": "calendar", "language": "en", "allowInput": true, "dateFormat": "Y-m-d", "enableTime": false, "noCalendar": false, "displayInTimezone": "viewer"}, "validate": {"required": true}, "tableView": true, "confirm_label": "Date your current weight was taken:"}, {"key": "weightDateWarning", "html": "<div style=\"color:#b40000; border:1px solid #b40000; background:#ffe5e5; padding:10px; border-radius:4px;\"><strong>Reminder:</strong> Please ensure your weight was measured within the last <strong>7 days</strong> before you submit.</div>", "type": "content", "input": false, "label": "Weight Date Warning", "tableView": false, "customConditional": "show = data.currentWeightDate && ((new Date()) - (new Date(data.currentWeightDate)) > 7 * 24 * 60 * 60 * 1000);"}, {"key": "heightConflictErrorGlobal", "html": "<p style='color:red;font-weight:bold;'>Please select height in either feet/inches or centimeters, but not both.</p>", "type": "content", "input": false, "customConditional": "show = ((data.heightValueCM && data.heightValueCM !== '') && ((data.heightFeet && data.heightFeet !== '') || (data.heightInches && data.heightInches !== '')));"}, {"key": "bmi_warning_panel", "type": "panel", "theme": "danger", "title": "", "tableView": false, "components": [{"key": "bmi_confirmation", "type": "radio", "input": true, "label": "Your BMI is greater than 50. Is the weight unit selection (lbs or kg) correct?", "values": [{"label": "Yes, it's the correct weight", "value": "yes_correct"}, {"label": "No, it's not the correct weight", "value": "no_incorrect"}], "validate": {"required": true}, "tableView": true, "confirm_label": "BMI confirmation:"}], "collapsible": false, "customClass": "mb-3 p-3", "customConditional": "show = parseFloat(data.bmi) > 50;"}, {"key": "goals_section", "html": "<h3>Progress &amp; Goals</h3><p>Tell us about your recent weight-loss and where you’d like to go next. We’ll use this to guide your dose.</p>", "type": "content", "input": false, "label": "Goals Heading", "tableView": false}, {"key": "weight_loss_last_12_weeks_prompt", "html": "<strong>In the last 12 weeks, please enter how much weight you have lost:</strong>", "type": "content", "input": false, "label": "Weight Loss Prompt"}, {"key": "weight_loss_last_12_weeks_row", "type": "columns", "input": false, "tableView": true, "label": "Weight Lost (Last 12 Weeks)", "columns": [{"width": 2, "components": [{"key": "weight_loss_last_12_weeks_value", "type": "number", "input": true, "label": "Weight", "validate": {"min": 0, "required": true}, "tableView": true, "placeholder": "Enter weight lost", "confirm_label": "Weight Lost (Last 12 Weeks):", "labelPosition": "top"}]}, {"width": 2, "components": [{"key": "weight_loss_last_12_weeks_units", "data": {"values": [{"label": "kg", "value": "kg"}, {"label": "lbs", "value": "lbs"}]}, "type": "select", "input": true, "label": "Units", "widget": "html5", "validate": {"required": true}, "tableView": true, "defaultValue": "lbs", "confirm_label": "Weight Loss Units:", "labelPosition": "top"}]}]}, {"key": "weight_loss_last_4_weeks_prompt", "html": "<strong>In the last 4 weeks, please enter how much weight you have lost:</strong>", "type": "content", "input": false, "label": "Weight Loss Prompt (4 Weeks)", "tableView": false}, {"key": "weight_loss_last_4_weeks_row", "type": "columns", "input": false, "tableView": true, "label": "Weight Lost (Last 4 Weeks)", "columns": [{"width": 2, "components": [{"key": "weight_loss_last_4_weeks_value", "type": "number", "input": true, "label": "Weight", "validate": {"min": 0, "required": true}, "tableView": true, "placeholder": "Enter weight lost", "confirm_label": "Weight Lost (Last 4 Weeks):", "labelPosition": "top"}]}, {"width": 2, "components": [{"key": "weight_loss_last_4_weeks_units", "data": {"values": [{"label": "kg", "value": "kg"}, {"label": "lbs", "value": "lbs"}]}, "type": "select", "input": true, "label": "Units", "widget": "html5", "validate": {"required": true}, "tableView": true, "defaultValue": "lbs", "confirm_label": "Weight Loss Units:", "labelPosition": "top"}]}]}, {"key": "weight_loss_last_4_weeks_echo", "html": "<p><strong>You reported losing {{data.weight_loss_last_4_weeks_value || '___'}} {{data.weight_loss_last_4_weeks_units || '___'}} in the last&nbsp;4 weeks.</strong></p>", "type": "content", "input": false, "label": "Weight-Loss Echo (4 wks)", "tableView": false, "refreshOnChange": true, "customConditional": "show = !!data.weight_loss_last_4_weeks_value;"}, {"key": "weight_loss_last_4_weeks_unit_confirm", "type": "radio", "input": true, "label": "Is that <strong>unit</strong> correct (pounds vs kilogram)?", "values": [{"label": "Yes, that unit is correct", "value": "yes"}, {"label": "No, I need to change the unit", "value": "no"}], "validate": {"required": true}, "tableView": true, "confirm_label": "4-Week Unit Confirm:", "customConditional": "show = !!data.weight_loss_last_4_weeks_value && !!data.weight_loss_last_4_weeks_units;"}, {"key": "weight_loss_last_4_weeks_confirm", "type": "radio", "input": true, "label": "Is that total <strong>amount of weight</strong> lost correct?", "values": [{"label": "Yes, that’s correct", "value": "yes"}, {"label": "No, I need to edit it", "value": "no"}], "validate": {"required": true}, "tableView": true, "confirm_label": "4-Week Weight-Loss Confirm:", "customConditional": "show = !!data.weight_loss_last_4_weeks_value;"}, {"key": "weekly_loss_lbs_per_week", "type": "textfield", "input": true, "label": "Weekly loss (lb / wk)", "hidden": true, "readonly": true, "tableView": false, "clearOnHide": false, "calculateValue": "if (data.weight_loss_last_4_weeks_value && data.weight_loss_last_4_weeks_units) {\n  const lbs = Number(data.weight_loss_last_4_weeks_units === 'kg'\n    ? data.weight_loss_last_4_weeks_value * 2.20462\n    : data.weight_loss_last_4_weeks_value);\n  value = (lbs / 4).toFixed(1);\n}", "refreshOnChange": true}, {"key": "current_medication_heading", "html": "<h3>Current Medication Details</h3>", "type": "content", "input": false, "label": "Current Medication Heading"}, {"key": "current_glp1_medication", "data": {"values": [{"label": "Wegovy", "value": "wegovy"}, {"label": "Ozempic", "value": "ozempic"}, {"label": "<PERSON><PERSON><PERSON>", "value": "sa<PERSON><PERSON>"}, {"label": "R<PERSON>bel<PERSON>", "value": "ry<PERSON><PERSON>"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "mou<PERSON><PERSON>"}, {"label": "None of these", "value": "none"}]}, "type": "select", "input": true, "label": "Which GLP-1 medication are you currently taking or have you taken in the past?", "widget": "html5", "validate": {"required": true}, "tableView": true, "placeholder": "Select a medication", "confirm_label": "Current Medication:"}, {"key": "wegovy_section", "html": "<h3>Wegovy (Semaglutide)</h3>", "type": "content", "input": false, "customConditional": "show = data.current_glp1_medication === 'wegovy';"}, {"key": "wegovy_use", "type": "radio", "input": true, "label": "Are you currently using Wegovy or considering restarting?", "values": [{"label": "I am currently using Wegovy", "value": "currently_using"}, {"label": "It has been more than 1 week since my last dose of Wegovy", "value": "past_user_restart"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Wegovy Use:", "customConditional": "show = data.current_glp1_medication === 'wegovy';"}, {"key": "wegovy_current_dose", "type": "radio", "input": true, "label": "What is your <strong>current dose</strong> of Wegovy?", "values": [{"label": "0.25 mg weekly", "value": "wegovy-starting-12-weeks-025mg"}, {"label": "0.5 mg weekly", "value": "wegovy-starting-12-weeks-05mg"}, {"label": "1.0 mg weekly", "value": "wegovy-starting-12-weeks-1mg"}, {"label": "1.7 mg weekly", "value": "wegovy-starting-12-weeks-17mg"}, {"label": "2.4 mg weekly (maximum dose)", "value": "wegovy-starting-12-weeks-24mg"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Wegovy Current Dose:", "customConditional": "show = data.wegovy_use === 'currently_using';"}, {"key": "wegovy_duration", "data": {"values": [{"label": "1 week", "value": "1 week"}, {"label": "2 weeks", "value": "2 weeks"}, {"label": "3 weeks", "value": "3 weeks"}, {"label": "4 weeks", "value": "4 weeks"}, {"label": "5-12 weeks", "value": "5-12 weeks"}, {"label": "3-6 months", "value": "3-6 months"}, {"label": "6-9 months", "value": "6-9 months"}, {"label": "9-12 months", "value": "9-12 months"}, {"label": "12+ months", "value": "12+ months"}]}, "type": "select", "input": true, "label": "How long have you been using Wegovy <strong> since you first started on therapy</strong>?", "widget": "html5", "validate": {"required": true}, "tableView": true, "placeholder": "Select the time range", "confirm_label": "Time on Wegovy:", "customConditional": "show = data.wegovy_use === 'currently_using';"}, {"key": "wegovy_duration_current_dose", "data": {"values": [{"label": "1 week", "value": "1 week"}, {"label": "2 weeks", "value": "2 weeks"}, {"label": "3 weeks", "value": "3 weeks"}, {"label": "4 weeks", "value": "4 weeks"}, {"label": "5-12 weeks", "value": "5-12 weeks"}, {"label": "3-6 months", "value": "3-6 months"}, {"label": "6-9 months", "value": "6-9 months"}, {"label": "9-12 months", "value": "9-12 months"}, {"label": "12+ months", "value": "12+ months"}]}, "type": "select", "input": true, "label": "How long have you been at your <strong>current dose</strong>?", "widget": "html5", "validate": {"required": true}, "tableView": true, "placeholder": "Select the time range", "confirm_label": "Wegovy Duration at Current Dose:", "customConditional": "show = data.wegovy_use === 'currently_using';"}, {"key": "wegovy_last_dose", "type": "radio", "input": true, "label": "What was the last dose of Wegovy you were prescribed?", "values": [{"label": "0.25 mg weekly", "value": "wegovy-starting-12-weeks-025mg"}, {"label": "0.5 mg weekly", "value": "wegovy-starting-12-weeks-05mg"}, {"label": "1.0 mg weekly", "value": "wegovy-starting-12-weeks-1mg"}, {"label": "1.7 mg weekly", "value": "wegovy-starting-12-weeks-17mg"}, {"label": "2.4 mg weekly", "value": "wegovy-starting-12-weeks-24mg"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Wegovy Last Dose:", "customConditional": "show = data.wegovy_use === 'past_user_restart';"}, {"key": "wegovy_last_dose_duration", "data": {"values": [{"label": "1 week", "value": "1 week"}, {"label": "2 weeks", "value": "2 weeks"}, {"label": "3 weeks", "value": "3 weeks"}, {"label": "4 weeks", "value": "4 weeks"}, {"label": "5-12 weeks", "value": "5-12 weeks"}, {"label": "3-6 months", "value": "3-6 months"}, {"label": "6-9 months", "value": "6-9 months"}, {"label": "9-12 months", "value": "9-12 months"}, {"label": "12+ months", "value": "12+ months"}]}, "type": "select", "input": true, "label": "How long were you at this dose?", "widget": "html5", "validate": {"required": true}, "tableView": true, "placeholder": "Select the time range", "confirm_label": "Wegovy Last Do<PERSON> Duration:", "customConditional": "show = data.wegovy_use === 'past_user_restart' && data.wegovy_last_dose;"}, {"key": "wegovy_reason_for_stopping", "type": "selectboxes", "input": true, "label": "Why did you stop <PERSON><PERSON><PERSON>? (Select all that apply)", "values": [{"label": "Side effects", "value": "side_effects"}, {"label": "Cost or insurance issues", "value": "cost_issues"}, {"label": "Ran out or delay in refill", "value": "ran_out"}, {"label": "Not effective", "value": "not_effective"}, {"label": "Switched to another medication", "value": "different_medication"}, {"label": "Trouble following schedule", "value": "adherence_difficulty"}, {"label": "Doctor's recommendation", "value": "doctors_recommendation"}, {"label": "Pregnancy or planning to", "value": "pregnancy", "customConditional": "show = data.sex === 'female';"}, {"label": "Emotional challenges", "value": "emotional_challenges"}, {"label": "Lifestyle changes", "value": "lifestyle_changes"}, {"label": "Other (please specify)", "value": "other"}], "validate": {"required": true}, "tableView": true, "confirm_label": "<PERSON><PERSON>vy Reason for Stopping:", "customConditional": "show = data.wegovy_use === 'past_user_restart';"}, {"key": "wegovy_reason_for_stopping_other", "type": "textfield", "input": true, "label": "Please specify your reason for stopping:", "validate": {"required": true}, "tableView": true, "placeholder": "Enter your reason here", "customConditional": "show = data.wegovy_reason_for_stopping?.other;"}, {"key": "wegovy_time_since_last", "data": {"values": [{"label": "Less than 1 week", "value": "less_than_1 week"}, {"label": "1-2 weeks", "value": "1-2 weeks"}, {"label": "3-4 weeks", "value": "3-4 weeks"}, {"label": "1-3 months", "value": "1-3 months"}, {"label": "3-6 months", "value": "3-6 months"}, {"label": "6-9 months", "value": "6-9 months"}, {"label": "9-12 months", "value": "9-12 months"}, {"label": "12+ months", "value": "12+ months"}]}, "type": "select", "input": true, "label": "How long ago did you stop using Wegovy?", "widget": "html5", "validate": {"required": true}, "tableView": true, "placeholder": "Select the time range", "confirm_label": "Wegovy Time Since Last:", "customConditional": "show = data.wegovy_use === 'past_user_restart';"}, {"key": "ozempic_section", "html": "<h3>Ozempic (Semaglutide)</h3>", "type": "content", "input": false, "customConditional": "show = data.current_glp1_medication === 'ozempic';"}, {"key": "ozempic_use", "type": "radio", "input": true, "label": "Are you currently using Ozempic or considering restarting?", "values": [{"label": "I am currently using Ozempic", "value": "currently_using"}, {"label": "It has been more than 1 week since my last dose of Ozempic", "value": "past_user_restart"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Ozempic Use:", "customConditional": "show = data.current_glp1_medication === 'ozempic';"}, {"key": "ozempic_current_dose", "type": "radio", "input": true, "label": "What is your <strong>current dose</strong> of Ozempic?", "values": [{"label": "0.25 mg weekly", "value": "ozempic-025mg-12-weeks"}, {"label": "0.5 mg weekly", "value": "ozempic-05mg-12-weeks"}, {"label": "1.0 mg weekly", "value": "ozempic-1mg-12-weeks"}, {"label": "2.0 mg weekly (maximum dose)", "value": "ozempic-2mg-12-weeks"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Ozempic Current Dose:", "customConditional": "show = data.ozempic_use === 'currently_using';"}, {"key": "ozempic_duration", "data": {"values": [{"label": "1 week", "value": "1 week"}, {"label": "2 weeks", "value": "2 weeks"}, {"label": "3 weeks", "value": "3 weeks"}, {"label": "4 weeks", "value": "4 weeks"}, {"label": "5-12 weeks", "value": "5-12 weeks"}, {"label": "3-6 months", "value": "3-6 months"}, {"label": "6-9 months", "value": "6-9 months"}, {"label": "9-12 months", "value": "9-12 months"}, {"label": "12+ months", "value": "12+ months"}]}, "type": "select", "input": true, "label": "How long have you been using Ozempic <strong> since you first started on therapy</strong>?", "widget": "html5", "validate": {"required": true}, "tableView": true, "placeholder": "Select the time range", "confirm_label": "Ozempic Duration:", "customConditional": "show = data.ozempic_use === 'currently_using';"}, {"key": "ozempic_duration_current_dose", "data": {"values": [{"label": "1 week", "value": "1 week"}, {"label": "2 weeks", "value": "2 weeks"}, {"label": "3 weeks", "value": "3 weeks"}, {"label": "4 weeks", "value": "4 weeks"}, {"label": "5-12 weeks", "value": "5-12 weeks"}, {"label": "3-6 months", "value": "3-6 months"}, {"label": "6-9 months", "value": "6-9 months"}, {"label": "9-12 months", "value": "9-12 months"}, {"label": "12+ months", "value": "12+ months"}]}, "type": "select", "input": true, "label": "How long have you been at your <strong>current dose</strong>?", "widget": "html5", "validate": {"required": true}, "tableView": true, "placeholder": "Select the time range", "confirm_label": "Ozempic Duration at Current Dose:", "customConditional": "show = data.ozempic_use === 'currently_using';"}, {"key": "ozempic_last_dose", "type": "radio", "input": true, "label": "What was the last dose of Ozempic you were prescribed?", "values": [{"label": "0.25 mg weekly", "value": "ozempic-025mg-12-weeks"}, {"label": "0.5 mg weekly", "value": "ozempic-05mg-12-weeks"}, {"label": "1.0 mg weekly", "value": "ozempic-1mg-12-weeks"}, {"label": "2.0 mg weekly", "value": "ozempic-2mg-12-weeks"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Ozempic Last Dose:", "customConditional": "show = data.ozempic_use === 'past_user_restart';"}, {"key": "ozempic_last_dose_duration", "data": {"values": [{"label": "1 week", "value": "1 week"}, {"label": "2 weeks", "value": "2 weeks"}, {"label": "3 weeks", "value": "3 weeks"}, {"label": "4 weeks", "value": "4 weeks"}, {"label": "5-12 weeks", "value": "5-12 weeks"}, {"label": "3-6 months", "value": "3-6 months"}, {"label": "6-9 months", "value": "6-9 months"}, {"label": "9-12 months", "value": "9-12 months"}, {"label": "12+ months", "value": "12+ months"}]}, "type": "select", "input": true, "label": "How long were you at this dose?", "widget": "html5", "validate": {"required": true}, "tableView": true, "placeholder": "Select the time range", "confirm_label": "Ozempic Last Do<PERSON> Duration:", "customConditional": "show = data.ozempic_use === 'past_user_restart' && data.ozempic_last_dose;"}, {"key": "ozempic_reason_for_stopping", "type": "selectboxes", "input": true, "label": "Why did you stop <PERSON><PERSON><PERSON>? (Select all that apply)", "values": [{"label": "Side effects", "value": "side_effects"}, {"label": "Cost or insurance issues", "value": "cost_issues"}, {"label": "Ran out or delay in refill", "value": "ran_out"}, {"label": "Not effective", "value": "not_effective"}, {"label": "Switched to another medication", "value": "different_medication"}, {"label": "Trouble following schedule", "value": "adherence_difficulty"}, {"label": "Doctor's recommendation", "value": "doctors_recommendation"}, {"label": "Pregnancy or planning to", "value": "pregnancy", "customConditional": "show = data.sex === 'female';"}, {"label": "Emotional challenges", "value": "emotional_challenges"}, {"label": "Lifestyle changes", "value": "lifestyle_changes"}, {"label": "Other (please specify)", "value": "other"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Ozempic Reason for Stopping:", "customConditional": "show = data.ozempic_use === 'past_user_restart';"}, {"key": "ozempic_reason_for_stopping_other", "type": "textfield", "input": true, "label": "Please specify your reason for stopping:", "validate": {"required": true}, "tableView": true, "placeholder": "Enter your reason here", "customConditional": "show = data.ozempic_reason_for_stopping?.other;"}, {"key": "ozempic_time_since_last", "data": {"values": [{"label": "Less than 1 week", "value": "less_than_1 week"}, {"label": "1-2 weeks", "value": "1-2 weeks"}, {"label": "3-4 weeks", "value": "3-4 weeks"}, {"label": "1-3 months", "value": "1-3 months"}, {"label": "3-6 months", "value": "3-6 months"}, {"label": "6-9 months", "value": "6-9 months"}, {"label": "9-12 months", "value": "9-12 months"}, {"label": "12+ months", "value": "12+ months"}]}, "type": "select", "input": true, "label": "How long ago did you stop using Ozempic?", "widget": "html5", "validate": {"required": true}, "tableView": true, "placeholder": "Select the time range", "confirm_label": "Ozempic Time Since Last:", "customConditional": "show = data.ozempic_use === 'past_user_restart';"}, {"key": "rybelsus_section", "html": "<h3><PERSON><PERSON><PERSON><PERSON> (Semaglutide)</h3>", "type": "content", "input": false, "customConditional": "show = data.current_glp1_medication === 'rybelsus';"}, {"key": "rybelsus_use", "type": "radio", "input": true, "label": "Are you currently using Rybelsus or considering restarting?", "values": [{"label": "I am currently using Rybelsus", "value": "currently_using"}, {"label": "It has been more than 1 week since my last dose of Rybelsus", "value": "past_user_restart"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Rybelsus Use:", "customConditional": "show = data.current_glp1_medication === 'rybelsus';"}, {"key": "rybelsus_current_dose", "type": "radio", "input": true, "label": "What is your <strong>current dose</strong> of Rybelsus?", "values": [{"label": "3 mg daily", "value": "rybelsus-3mg-12-weeks"}, {"label": "7 mg daily", "value": "rybelsus-7mg-12-weeks"}, {"label": "14 mg daily (maximum dose)", "value": "rybelsus-14mg-12-weeks"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Rybelsus Current Dose:", "customConditional": "show = data.rybelsus_use === 'currently_using';"}, {"key": "rybelsus_duration", "data": {"values": [{"label": "1 week", "value": "1 week"}, {"label": "2 weeks", "value": "2 weeks"}, {"label": "3 weeks", "value": "3 weeks"}, {"label": "4 weeks", "value": "4 weeks"}, {"label": "5-12 weeks", "value": "5-12 weeks"}, {"label": "3-6 months", "value": "3-6 months"}, {"label": "6-9 months", "value": "6-9 months"}, {"label": "9-12 months", "value": "9-12 months"}, {"label": "12+ months", "value": "12+ months"}]}, "type": "select", "input": true, "label": "How long have you been using Rybelsus <strong> since you first started on therapy</strong>?", "widget": "html5", "validate": {"required": true}, "tableView": true, "placeholder": "Select the time range", "confirm_label": "R<PERSON>belsus Duration:", "customConditional": "show = data.rybelsus_use === 'currently_using';"}, {"key": "rybelsus_duration_current_dose", "data": {"values": [{"label": "1 week", "value": "1 week"}, {"label": "2 weeks", "value": "2 weeks"}, {"label": "3 weeks", "value": "3 weeks"}, {"label": "4 weeks", "value": "4 weeks"}, {"label": "5-12 weeks", "value": "5-12 weeks"}, {"label": "3-6 months", "value": "3-6 months"}, {"label": "6-9 months", "value": "6-9 months"}, {"label": "9-12 months", "value": "9-12 months"}, {"label": "12+ months", "value": "12+ months"}]}, "type": "select", "input": true, "label": "How long have you been at your <strong>current dose</strong>?", "widget": "html5", "validate": {"required": true}, "tableView": true, "placeholder": "Select the time range", "confirm_label": "R<PERSON><PERSON><PERSON> Duration at Current Dose:", "customConditional": "show = data.rybelsus_use === 'currently_using';"}, {"key": "rybelsus_last_dose", "type": "radio", "input": true, "label": "What was the last dose of Rybelsus you were prescribed?", "values": [{"label": "3 mg daily", "value": "rybelsus-3mg-12-weeks"}, {"label": "7 mg daily", "value": "rybelsus-7mg-12-weeks"}, {"label": "14 mg daily", "value": "rybelsus-14mg-12-weeks"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Rybelsus Last Dose:", "customConditional": "show = data.rybelsus_use === 'past_user_restart';"}, {"key": "rybelsus_last_dose_duration", "data": {"values": [{"label": "1 week", "value": "1 week"}, {"label": "2 weeks", "value": "2 weeks"}, {"label": "3 weeks", "value": "3 weeks"}, {"label": "4 weeks", "value": "4 weeks"}, {"label": "5-12 weeks", "value": "5-12 weeks"}, {"label": "3-6 months", "value": "3-6 months"}, {"label": "6-9 months", "value": "6-9 months"}, {"label": "9-12 months", "value": "9-12 months"}, {"label": "12+ months", "value": "12+ months"}]}, "type": "select", "input": true, "label": "How long were you at this dose?", "widget": "html5", "validate": {"required": true}, "tableView": true, "placeholder": "Select the time range", "confirm_label": "R<PERSON>belsus Last Do<PERSON> Duration:", "customConditional": "show = data.rybelsus_use === 'past_user_restart' && data.rybelsus_last_dose;"}, {"key": "rybelsus_reason_for_stopping", "type": "selectboxes", "input": true, "label": "Why did you stop <PERSON><PERSON><PERSON><PERSON>? (Select all that apply)", "values": [{"label": "Side effects", "value": "side_effects"}, {"label": "Cost or insurance issues", "value": "cost_issues"}, {"label": "Ran out or delay in refill", "value": "ran_out"}, {"label": "Not effective", "value": "not_effective"}, {"label": "Switched to another medication", "value": "different_medication"}, {"label": "Trouble following schedule", "value": "adherence_difficulty"}, {"label": "Doctor's recommendation", "value": "doctors_recommendation"}, {"label": "Pregnancy or planning to", "value": "pregnancy", "customConditional": "show = data.sex === 'female';"}, {"label": "Emotional challenges", "value": "emotional_challenges"}, {"label": "Lifestyle changes", "value": "lifestyle_changes"}, {"label": "Other (please specify)", "value": "other"}], "validate": {"required": true}, "tableView": true, "confirm_label": "<PERSON><PERSON><PERSON><PERSON> Reason for Stopping:", "customConditional": "show = data.rybelsus_use === 'past_user_restart';"}, {"key": "rybelsus_reason_for_stopping_other", "type": "textfield", "input": true, "label": "Please specify your reason for stopping:", "validate": {"required": true}, "tableView": true, "placeholder": "Enter your reason here", "customConditional": "show = data.rybelsus_reason_for_stopping?.other;"}, {"key": "rybelsus_time_since_last", "data": {"values": [{"label": "Less than 1 week", "value": "less_than_1 week"}, {"label": "1-2 weeks", "value": "1-2 weeks"}, {"label": "3-4 weeks", "value": "3-4 weeks"}, {"label": "1-3 months", "value": "1-3 months"}, {"label": "3-6 months", "value": "3-6 months"}, {"label": "6-9 months", "value": "6-9 months"}, {"label": "9-12 months", "value": "9-12 months"}, {"label": "12+ months", "value": "12+ months"}]}, "type": "select", "input": true, "label": "How long ago did you stop using Rybelsus?", "widget": "html5", "validate": {"required": true}, "tableView": true, "placeholder": "Select the time range", "confirm_label": "Rybelsus Time Since Last:", "customConditional": "show = data.rybelsus_use === 'past_user_restart';"}, {"key": "mounjaro_section", "html": "<h3><PERSON><PERSON><PERSON><PERSON> (Tirzepatide)</h3>", "type": "content", "input": false, "customConditional": "show = data.current_glp1_medication === 'mounjaro';"}, {"key": "mounjaro_use", "type": "radio", "input": true, "label": "Are you currently using Mounjaro or considering restarting?", "values": [{"label": "I am currently using Mounjaro", "value": "currently_using"}, {"label": "It has been more than 1 week since my last dose of Mounjaro", "value": "past_user_restart"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Mounjaro Use:", "customConditional": "show = data.current_glp1_medication === 'mounjaro';"}, {"key": "mounjaro_current_dose", "type": "radio", "input": true, "label": "What is your <strong>current dose</strong> of Mounjaro?", "values": [{"label": "2.5 mg weekly", "value": "mounjaro-25mg-12-weeks"}, {"label": "5 mg weekly", "value": "mounjaro-5mg-12-weeks"}, {"label": "7.5 mg weekly", "value": "mounjaro-75mg-12-weeks"}, {"label": "10 mg weekly", "value": "mounjaro-10mg-12-weeks"}, {"label": "12.5 mg weekly", "value": "mounjaro-125mg-12-weeks"}, {"label": "15 mg weekly (maximum dose)", "value": "mounjaro-15mg-12-weeks"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Mounjaro Current Dose:", "customConditional": "show = data.mounjaro_use === 'currently_using';"}, {"key": "mounjaro_duration", "data": {"values": [{"label": "1 week", "value": "1 week"}, {"label": "2 weeks", "value": "2 weeks"}, {"label": "3 weeks", "value": "3 weeks"}, {"label": "4 weeks", "value": "4 weeks"}, {"label": "5-12 weeks", "value": "5-12 weeks"}, {"label": "3-6 months", "value": "3-6 months"}, {"label": "6-9 months", "value": "6-9 months"}, {"label": "9-12 months", "value": "9-12 months"}, {"label": "12+ months", "value": "12+ months"}]}, "type": "select", "input": true, "label": "How long have you been using Mounjaro <strong> since you first started on therapy</strong>?", "widget": "html5", "validate": {"required": true}, "tableView": true, "placeholder": "Select the time range", "confirm_label": "<PERSON><PERSON><PERSON><PERSON>:", "customConditional": "show = data.mounjaro_use === 'currently_using';"}, {"key": "mounjaro_duration_current_dose", "data": {"values": [{"label": "1 week", "value": "1 week"}, {"label": "2 weeks", "value": "2 weeks"}, {"label": "3 weeks", "value": "3 weeks"}, {"label": "4 weeks", "value": "4 weeks"}, {"label": "5-12 weeks", "value": "5-12 weeks"}, {"label": "3-6 months", "value": "3-6 months"}, {"label": "6-9 months", "value": "6-9 months"}, {"label": "9-12 months", "value": "9-12 months"}, {"label": "12+ months", "value": "12+ months"}]}, "type": "select", "input": true, "label": "How long have you been at your <strong>current dose</strong>?", "widget": "html5", "validate": {"required": true}, "tableView": true, "placeholder": "Select the time range", "confirm_label": "<PERSON><PERSON><PERSON><PERSON> at Current Dose:", "customConditional": "show = data.mounjaro_use === 'currently_using';"}, {"key": "mounja<PERSON>_last_dose", "type": "radio", "input": true, "label": "What was the last dose of Mounjaro you were prescribed?", "values": [{"label": "2.5 mg weekly", "value": "mounjaro-25mg-12-weeks"}, {"label": "5 mg weekly", "value": "mounjaro-5mg-12-weeks"}, {"label": "7.5 mg weekly", "value": "mounjaro-75mg-12-weeks"}, {"label": "10 mg weekly", "value": "mounjaro-10mg-12-weeks"}, {"label": "12.5 mg weekly", "value": "mounjaro-125mg-12-weeks"}, {"label": "15 mg weekly", "value": "mounjaro-15mg-12-weeks"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Mounjaro Last Dose:", "customConditional": "show = data.mounjaro_use === 'past_user_restart';"}, {"key": "mounjaro_last_dose_duration", "data": {"values": [{"label": "1 week", "value": "1 week"}, {"label": "2 weeks", "value": "2 weeks"}, {"label": "3 weeks", "value": "3 weeks"}, {"label": "4 weeks", "value": "4 weeks"}, {"label": "5-12 weeks", "value": "5-12 weeks"}, {"label": "3-6 months", "value": "3-6 months"}, {"label": "6-9 months", "value": "6-9 months"}, {"label": "9-12 months", "value": "9-12 months"}, {"label": "12+ months", "value": "12+ months"}]}, "type": "select", "input": true, "label": "How long were you at this dose?", "widget": "html5", "validate": {"required": true}, "tableView": true, "placeholder": "Select the time range", "confirm_label": "Mounjaro Last Do<PERSON> Duration:", "customConditional": "show = data.mounjaro_use === 'past_user_restart' && data.mounjaro_last_dose;"}, {"key": "mounja<PERSON>_reason_for_stopping", "type": "selectboxes", "input": true, "label": "Why did you stop <PERSON><PERSON><PERSON><PERSON>? (Select all that apply)", "values": [{"label": "Side effects", "value": "side_effects"}, {"label": "Cost or insurance issues", "value": "cost_issues"}, {"label": "Ran out or delay in refill", "value": "ran_out"}, {"label": "Not effective", "value": "not_effective"}, {"label": "Switched to another medication", "value": "different_medication"}, {"label": "Trouble following schedule", "value": "adherence_difficulty"}, {"label": "Doctor's recommendation", "value": "doctors_recommendation"}, {"label": "Pregnancy or planning to", "value": "pregnancy", "customConditional": "show = data.sex === 'female';"}, {"label": "Emotional challenges", "value": "emotional_challenges"}, {"label": "Lifestyle changes", "value": "lifestyle_changes"}, {"label": "Other (please specify)", "value": "other"}], "validate": {"required": true}, "tableView": true, "confirm_label": "<PERSON><PERSON><PERSON><PERSON> Reason for Stopping:", "customConditional": "show = data.mounjaro_use === 'past_user_restart';"}, {"key": "mounjaro_reason_for_stopping_other", "type": "textfield", "input": true, "label": "Please specify your reason for stopping:", "validate": {"required": true}, "tableView": true, "placeholder": "Enter your reason here", "customConditional": "show = data.mounjaro_reason_for_stopping?.other;"}, {"key": "mounjaro_time_since_last", "data": {"values": [{"label": "Less than 1 week", "value": "less_than_1 week"}, {"label": "1-2 weeks", "value": "1-2 weeks"}, {"label": "3-4 weeks", "value": "3-4 weeks"}, {"label": "1-3 months", "value": "1-3 months"}, {"label": "3-6 months", "value": "3-6 months"}, {"label": "6-9 months", "value": "6-9 months"}, {"label": "9-12 months", "value": "9-12 months"}, {"label": "12+ months", "value": "12+ months"}]}, "type": "select", "input": true, "label": "How long ago did you stop using Mounjaro?", "widget": "html5", "validate": {"required": true}, "tableView": true, "placeholder": "Select the time range", "confirm_label": "Mounjaro Time Since Last:", "customConditional": "show = data.mounjaro_use === 'past_user_restart';"}, {"key": "saxenda_section", "html": "<h3><PERSON><PERSON><PERSON> (Liraglutide)</h3>", "type": "content", "input": false, "customConditional": "show = data.current_glp1_medication === 'saxenda';"}, {"key": "saxenda_use", "type": "radio", "input": true, "label": "Are you currently using Saxenda or considering restarting?", "values": [{"label": "I am currently using Saxenda", "value": "currently_using"}, {"label": "It has been more than 1 week since my last dose of <PERSON><PERSON><PERSON>", "value": "past_user_restart"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Saxenda Use:", "customConditional": "show = data.current_glp1_medication === 'saxenda';"}, {"key": "saxenda_current_dose", "type": "radio", "input": true, "label": "What is your <strong>current dose</strong> of <PERSON>xen<PERSON>?", "values": [{"label": "0.6 mg daily", "value": "saxenda-6mg-12-weeks"}, {"label": "1.2 mg daily", "value": "saxenda-12mg-12-weeks"}, {"label": "1.8 mg daily", "value": "saxenda-18mg-12-weeks"}, {"label": "2.4 mg daily", "value": "saxenda-24mg-12-weeks"}, {"label": "3.0 mg daily (maximum dose)", "value": "saxenda-3mg-12-weeks"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Saxenda Current Dose:", "customConditional": "show = data.saxenda_use === 'currently_using';"}, {"key": "saxenda_duration", "data": {"values": [{"label": "1 week", "value": "1 week"}, {"label": "2 weeks", "value": "2 weeks"}, {"label": "3 weeks", "value": "3 weeks"}, {"label": "4 weeks", "value": "4 weeks"}, {"label": "5-12 weeks", "value": "5-12 weeks"}, {"label": "3-6 months", "value": "3-6 months"}, {"label": "6-9 months", "value": "6-9 months"}, {"label": "9-12 months", "value": "9-12 months"}, {"label": "12+ months", "value": "12+ months"}]}, "type": "select", "input": true, "label": "How long have you been using <PERSON><PERSON><PERSON> <strong> since you first started on therapy</strong>?", "widget": "html5", "validate": {"required": true}, "tableView": true, "placeholder": "Select the time range", "confirm_label": "<PERSON><PERSON><PERSON> Duration:", "customConditional": "show = data.saxenda_use === 'currently_using';"}, {"key": "saxenda_duration_current_dose", "data": {"values": [{"label": "1 week", "value": "1 week"}, {"label": "2 weeks", "value": "2 weeks"}, {"label": "3 weeks", "value": "3 weeks"}, {"label": "4 weeks", "value": "4 weeks"}, {"label": "5-12 weeks", "value": "5-12 weeks"}, {"label": "3-6 months", "value": "3-6 months"}, {"label": "6-9 months", "value": "6-9 months"}, {"label": "9-12 months", "value": "9-12 months"}, {"label": "12+ months", "value": "12+ months"}]}, "type": "select", "input": true, "label": "How long have you been at your <strong>current dose</strong>?", "widget": "html5", "validate": {"required": true}, "tableView": true, "placeholder": "Select the time range", "confirm_label": "<PERSON><PERSON>da Duration at Current Dose:", "customConditional": "show = data.saxenda_use === 'currently_using';"}, {"key": "side_effect_tolerance", "type": "radio", "input": true, "label": "Overall, how tolerable are your side-effects at your current dose?", "values": [{"label": "No / mild side-effects", "value": "tolerable"}, {"label": "Moderate but manageable", "value": "borderline"}, {"label": "Severe / unacceptable", "value": "intolerable"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Side-Effect Tolerance:"}, {"key": "desired_weight_achieved", "type": "radio", "input": true, "label": "Have you already reached the weight goal you set for yourself?<ul style=\"margin:0.5rem 0 0 1.25rem;line-height:1.4;\">\n  <li>A goal weight should be realistic—something you can keep for 6&nbsp;+&nbsp;months without extreme dieting or workouts.</li>\n  <li>You should feel energetic, sleep well, and move comfortably in daily life at this weight.</li>\n  <li>Muscle mass, body-frame size, age, and activity level all affect what “healthy” looks like for you.</li>\n  <li>It’s okay to adjust your target as your lifestyle or priorities change.</li>\n</ul>", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Goal Weight Achieved:"}, {"key": "desired_weight_plan", "type": "radio", "input": true, "label": "Now that you’re at goal, would you like to:", "values": [{"label": "Try a gradual taper (reduce every 12 weeks)", "value": "taper"}, {"label": "Stay on a maintenance dose", "value": "maintain"}, {"label": "I’m not sure yet", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Goal-Weight Plan:", "customConditional": "show = data.desired_weight_achieved === 'yes';"}, {"key": "saxenda_last_dose", "type": "radio", "input": true, "label": "What was the last dose of <PERSON><PERSON><PERSON> you were prescribed?", "values": [{"label": "0.6 mg daily", "value": "saxenda-6mg-12-weeks"}, {"label": "1.2 mg daily", "value": "saxenda-12mg-12-weeks"}, {"label": "1.8 mg daily", "value": "saxenda-18mg-12-weeks"}, {"label": "2.4 mg daily", "value": "saxenda-24mg-12-weeks"}, {"label": "3.0 mg daily", "value": "saxenda-3mg-12-weeks"}], "validate": {"required": true}, "tableView": true, "confirm_label": "<PERSON><PERSON>da Last Dose:", "customConditional": "show = data.saxenda_use === 'past_user_restart';"}, {"key": "saxenda_last_dose_duration", "data": {"values": [{"label": "1 week", "value": "1 week"}, {"label": "2 weeks", "value": "2 weeks"}, {"label": "3 weeks", "value": "3 weeks"}, {"label": "4 weeks", "value": "4 weeks"}, {"label": "5-12 weeks", "value": "5-12 weeks"}, {"label": "3-6 months", "value": "3-6 months"}, {"label": "6-9 months", "value": "6-9 months"}, {"label": "9-12 months", "value": "9-12 months"}, {"label": "12+ months", "value": "12+ months"}]}, "type": "select", "input": true, "label": "How long were you at this dose?", "widget": "html5", "validate": {"required": true}, "tableView": true, "placeholder": "Select the time range", "confirm_label": "<PERSON><PERSON>da Last Dose Duration:", "customConditional": "show = data.saxenda_use === 'past_user_restart' && data.saxenda_last_dose;"}, {"key": "saxenda_reason_for_stopping", "type": "selectboxes", "input": true, "label": "Why did you stop <PERSON><PERSON><PERSON>? (Select all that apply)", "values": [{"label": "Side effects", "value": "side_effects"}, {"label": "Cost or insurance issues", "value": "cost_issues"}, {"label": "Ran out or delay in refill", "value": "ran_out"}, {"label": "Not effective", "value": "not_effective"}, {"label": "Switched to another medication", "value": "different_medication"}, {"label": "Trouble following schedule", "value": "adherence_difficulty"}, {"label": "Doctor's recommendation", "value": "doctors_recommendation"}, {"label": "Pregnancy or planning to", "value": "pregnancy", "customConditional": "show = data.sex === 'female';"}, {"label": "Emotional challenges", "value": "emotional_challenges"}, {"label": "Lifestyle changes", "value": "lifestyle_changes"}, {"label": "Other (please specify)", "value": "other"}], "validate": {"required": true}, "tableView": true, "confirm_label": "<PERSON><PERSON><PERSON> Reason for Stopping:", "customConditional": "show = data.saxenda_use === 'past_user_restart';"}, {"key": "saxenda_time_since_last", "data": {"values": [{"label": "Less than 1 week", "value": "less_than_1 week"}, {"label": "1-2 weeks", "value": "1-2 weeks"}, {"label": "3-4 weeks", "value": "3-4 weeks"}, {"label": "1-3 months", "value": "1-3 months"}, {"label": "3-6 months", "value": "3-6 months"}, {"label": "6-9 months", "value": "6-9 months"}, {"label": "9-12 months", "value": "9-12 months"}, {"label": "12+ months", "value": "12+ months"}]}, "type": "select", "input": true, "label": "How long ago did you stop using Saxenda?", "widget": "html5", "validate": {"required": true}, "tableView": true, "placeholder": "Select the time range", "confirm_label": "Saxenda Time Since Last:", "customConditional": "show = data.saxenda_use === 'past_user_restart';"}, {"key": "side_effects_details", "html": "</br><h2 style=\"text-align:center;\">Side Effects</h2>", "type": "content", "input": false, "customConditional": "show = data.current_glp1_medication !== 'none';"}, {"key": "experienced_side_effects", "type": "selectboxes", "input": true, "label": "Have you experienced any side effects on medication? (Select all that apply)", "values": [{"label": "<PERSON><PERSON><PERSON>", "value": "nausea", "shortcut": ""}, {"label": "Vomiting", "value": "vomiting", "shortcut": ""}, {"label": "Diarrhea", "value": "diarrhea", "shortcut": ""}, {"label": "Constipation", "value": "constipation", "shortcut": ""}, {"label": "Appetite loss", "value": "appetite_loss", "shortcut": ""}, {"label": "Reaction at the injection site (e.g., redness, swelling, or pain)", "value": "injection_site_reaction", "shortcut": ""}, {"label": "Other (please specify)", "value": "other", "shortcut": ""}], "adminFlag": true, "tableView": true, "confirm_label": "Experienced Side Effects:", "customConditional": "show = data.current_glp1_medication !== 'none';", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_side_effects", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "Please select a side effect or 'None of the above'."}, "validate": {"custom": "valid = !!data.none_of_the_above_side_effects || _.some(_.values(data.experienced_side_effects));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.current_glp1_medication !== 'none';"}, {"key": "side_effects_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following side effects:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "I DO NOT have the following side effects:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.experienced_side_effects, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "nausea_details", "html": "<h3>Nausea</h3>", "type": "content", "input": false, "customConditional": "show = data.experienced_side_effects?.nausea;"}, {"key": "nausea_relation", "type": "radio", "input": true, "label": "Did you find your nausea started after using your medication, or was it unrelated?", "values": [{"label": "Started after medication", "value": "related"}, {"label": "Unrelated to medication", "value": "unrelated"}, {"label": "I am not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Nausea Relation:", "customConditional": "show = data.experienced_side_effects?.nausea;"}, {"key": "nausea_frequency", "type": "selectboxes", "input": true, "label": "How often do you experience nausea after your injection? (Select all that apply)", "values": [{"label": "On the day of the injection", "value": "on_injection_day"}, {"label": "For a few days afterwards", "value": "few_days_after"}, {"label": "The entire week", "value": "entire week"}, {"label": "Randomly, with no clear pattern", "value": "random"}, {"label": "Only after eating certain foods", "value": "after_certain_foods"}, {"label": "Other (please specify)", "value": "other"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Nausea Frequency:", "customConditional": "show = data.experienced_side_effects?.nausea;"}, {"key": "nausea_duration", "type": "radio", "input": true, "label": "How long does the nausea last?", "values": [{"label": "Less than an hour", "value": "less_than_hour"}, {"label": "A few hours", "value": "few_hours"}, {"label": "Most of the day", "value": "most_day"}, {"label": "It lasts all day", "value": "all_day"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Nausea Duration:", "customConditional": "show = _.some(data.nausea_frequency, Boolean);"}, {"key": "nausea_severity_and_dose_preference", "type": "radio", "input": true, "label": "How would you describe your nausea severity, and would you like to lower your dose?", "values": [{"label": "Mild, and I do not wish to lower my dose", "value": "mild_no_dose_change"}, {"label": "Moderate, and I would consider lowering my dose", "value": "moderate_consider_dose_change"}, {"label": "Severe, and I would prefer to lower my dose", "value": "severe_prefer_dose_change"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Nausea Severity and Dose Preference:", "customConditional": "show = _.some(data.nausea_frequency, Boolean);"}, {"key": "nausea_reduction_strategies", "type": "radio", "input": true, "label": "Here are strategies to reduce nausea associated with your medication use:<ul><li>Take your injection at the same time each week.</li><li>Stay hydrated throughout the day.</li><li>Eat small, frequent meals instead of large meals.</li><li>Avoid greasy, spicy, or heavily processed foods.</li><li>Consider taking your injection in the evening to sleep through initial side effects.</li></ul>", "values": [{"label": "I understand", "value": "understand"}, {"label": "I do not understand", "value": "do_not_understand"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Nausea Reduction Strategies:", "customConditional": "show = _.some(data.nausea_frequency, Boolean);"}, {"key": "vomiting_details", "html": "<h3>Vomiting</h3>", "type": "content", "input": false, "customConditional": "show = data.experienced_side_effects?.vomiting;"}, {"key": "vomiting_relation", "type": "selectboxes", "input": true, "label": "Did you find your vomiting started after using your medication, or was it unrelated? (Select all that apply)", "values": [{"label": "Started after medication", "value": "related"}, {"label": "Unrelated to medication", "value": "unrelated"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Vomiting Relation:", "customConditional": "show = data.experienced_side_effects?.vomiting;"}, {"key": "vomiting_frequency", "type": "selectboxes", "input": true, "label": "How often do you experience vomiting? (Select all that apply)", "values": [{"label": "Rarely (once or twice a month)", "value": "rarely"}, {"label": "Occasionally (1-2 times per week)", "value": "occasionally"}, {"label": "Frequently (most days)", "value": "frequently"}, {"label": "Every day", "value": "every_day"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Vomiting Frequency:", "customConditional": "show = data.experienced_side_effects?.vomiting;"}, {"key": "vomiting_trigger", "type": "selectboxes", "input": true, "label": "What triggers the vomiting? (Select all that apply)", "values": [{"label": "After eating certain foods", "value": "after_eating_certain_foods"}, {"label": "On an empty stomach", "value": "empty_stomach"}, {"label": "Immediately after the injection", "value": "after_injection"}, {"label": "During physical activity", "value": "physical_activity"}, {"label": "After taking other medications or supplements", "value": "after_other_medications"}, {"label": "Randomly, with no clear pattern", "value": "random"}, {"label": "Other (please specify)", "value": "other"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Vomiting Trigger:", "customConditional": "show = data.experienced_side_effects?.vomiting;"}, {"key": "vomiting_other_details", "type": "textfield", "input": true, "label": "Please specify the trigger:", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.vomiting_trigger?.other;"}, {"key": "diarrhea_details", "html": "<h3>Diarrhea</h3>", "type": "content", "input": false, "customConditional": "show = data.experienced_side_effects?.diarrhea;"}, {"key": "diarrhea_relation", "type": "selectboxes", "input": true, "label": "Did you find diarrhea started after using your medication, or was it unrelated? (Select all that apply)", "values": [{"label": "Started after medication", "value": "related"}, {"label": "Unrelated to medication", "value": "unrelated"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Diarrhea Relation:", "customConditional": "show = data.experienced_side_effects?.diarrhea;"}, {"key": "diarrhea_severity", "type": "selectboxes", "input": true, "label": "How severe is the diarrhea? (Select all that apply)", "values": [{"label": "Mild (manageable, doesn't disrupt daily activities)", "value": "mild"}, {"label": "Moderate (requires frequent bathroom breaks)", "value": "moderate"}, {"label": "Severe (disrupts daily activities)", "value": "severe"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Diarrhea Severity:", "customConditional": "show = data.experienced_side_effects?.diarrhea;"}, {"key": "diarrhea_timing", "type": "selectboxes", "input": true, "label": "When do you experience diarrhea most often? (Select all that apply)", "values": [{"label": "Right after eating", "value": "after_eating"}, {"label": "In the morning", "value": "morning"}, {"label": "Randomly throughout the day", "value": "random"}, {"label": "Other (please specify)", "value": "other"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Diarrhea Tim<PERSON>:", "customConditional": "show = data.experienced_side_effects?.diarrhea;"}, {"key": "diarrhea_other_details", "type": "textfield", "input": true, "label": "Please specify when you experience diarrhea:", "validate": {"required": true}, "tableView": true, "confirm_label": "Diarrhea Other Details:", "customConditional": "show = data.diarrhea_timing?.other;"}, {"key": "constipation_details", "html": "<h3>Constipation</h3>", "type": "content", "input": false, "customConditional": "show = data.experienced_side_effects?.constipation;"}, {"key": "constipation_relation", "type": "radio", "input": true, "label": "Did you find your constipation started after using your medication, or was it unrelated?", "values": [{"label": "Started after medication", "value": "related"}, {"label": "Unrelated to medication", "value": "unrelated"}, {"label": "I am not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Constipation Relationship to Medication:", "customConditional": "show = data.experienced_side_effects?.constipation;"}, {"key": "constipation_frequency", "type": "selectboxes", "input": true, "label": "How often do you experience constipation? (Select all that apply)", "values": [{"label": "Occasionally (less than once a week)", "value": "occasional"}, {"label": "Frequently (1-3 times per week)", "value": "frequent"}, {"label": "Most of the time (4-6 times per week)", "value": "most_time"}, {"label": "Every day", "value": "daily"}, {"label": "Other (please specify)", "value": "other"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Constipation Frequency:", "customConditional": "show = data.experienced_side_effects?.constipation;"}, {"key": "constipation_duration", "type": "radio", "input": true, "label": "How long does the constipation usually last?", "values": [{"label": "Less than a day", "value": "less_than_day"}, {"label": "1-2 days", "value": "one_two_days"}, {"label": "3-5 days", "value": "three_five_days"}, {"label": "More than 5 days", "value": "more_than_five_days"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Constipation Duration:", "customConditional": "show = _.some(data.constipation_frequency, Boolean);"}, {"key": "rectal_bleeding_symptoms", "type": "selectboxes", "input": true, "label": "Do you have any of the following symptoms of rectal bleeding?", "values": [{"label": "Black, tarry stools", "value": "black_tarry_stools", "shortcut": ""}, {"label": "Bright red blood in stool", "value": "bright_red_blood", "shortcut": ""}, {"label": "Dark blood or clots", "value": "dark_blood_clots", "shortcut": ""}, {"label": "Bleeding during bowel movements", "value": "bleeding_bowel_movements", "shortcut": ""}], "adminFlag": true, "tableView": true, "confirm_label": "Rectal Bleeding Symptoms:", "customConditional": "show = _.some(data.constipation_frequency, Boolean);", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_rectal_bleeding_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "Please select a symptom or 'None of the above'."}, "validate": {"custom": "valid = !!data.none_of_the_above_rectal_bleeding_symptoms || _.some(_.values(data.rectal_bleeding_symptoms));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = _.some(data.constipation_frequency, Boolean);"}, {"key": "rectal_bleeding_advice", "type": "radio", "input": true, "label": "Based on your symptoms, you are advised to have an in-person anal exam and speak with your doctor about a referral for a colonoscopy after the exam is completed. Do you understand this advice?", "values": [{"label": "Yes, I understand", "value": "understand"}, {"label": "No, I do not understand", "value": "do_not_understand"}], "tooltip": "Rectal bleeding can be a symptom of serious underlying conditions such as colorectal cancer or gastrointestinal disorders. Prompt medical evaluation is essential to determine the cause and ensure appropriate treatment.", "validate": {"required": true}, "tableView": true, "confirm_label": "Rectal Bleeding Advice:", "customConditional": "show = _.some(data.rectal_bleeding_symptoms, Boolean);"}, {"key": "rectal_bleeding_symptoms_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following rectal bleeding symptoms:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "I DO NOT have the following rectal bleeding symptoms:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.rectal_bleeding_symptoms, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "other_health_conditions_section", "html": "</br><h3>Other Health Conditions</h3>", "type": "content", "input": false}, {"key": "contraindications", "type": "selectboxes", "input": true, "label": "Do any of these apply to you (or an immediate family member)?  Select all that apply:", "values": [{"label": "I've had pancreatitis", "value": "pancreatitis"}, {"label": "I've had thyroid cancer", "value": "thyroid_cancer"}, {"label": "I have been diagnosed with Multiple Endocrine Neoplasia (MEN-2)", "value": "diagnosis_men2"}, {"label": "I have a family member with medullary thyroid cancer", "value": "family_medullary_thyroid_cancer"}, {"label": "I have a family member with Multiple Endocrine Neoplasia (MEN-2)", "value": "family_men2"}], "adminFlag": true, "tableView": true, "confirm_label": "Contra-indications:", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_contraindications", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "Please select a condition or choose 'None of the above'."}, "validate": {"custom": "valid = !!data.none_of_the_above_contraindications || _.some(_.values(data.contraindications));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "contraindications_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following conditions:", "hidden": true, "disabled": true, "clearOnHide": false, "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.contraindications, _.negate(_.identity))), _.startCase), ', '), /_/g, ' ');"}, {"key": "intake_denial_reasons", "type": "textfield", "input": true, "label": "Intake Denial Reasons:", "hidden": true, "disabled": true, "multiple": true, "adminFlag": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "value = [];", "refreshOnChange": true}, {"key": "intake_kind", "type": "textfield", "input": true, "label": "Intake Kind", "hidden": true, "disabled": true, "tableView": true, "clearOnHide": false, "calculateValue": "value = !data.intake_denial_reasons?.length ? 'mail' : 'chat';"}, {"key": "intake_mt_key", "type": "textfield", "input": true, "label": "Intake Mail Template Key", "hidden": true, "disabled": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.intake_kind=='mail'?'intake-glp-renewal':'intake-denial'"}, {"key": "allowed_rt_keys", "type": "textfield", "input": true, "label": "Allowed Rep<PERSON>", "hidden": true, "disabled": true, "multiple": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.intake_mt_key=='intake-denial'?[]:['get-req','get-rx','appointment-intake','edit-intake']"}]}