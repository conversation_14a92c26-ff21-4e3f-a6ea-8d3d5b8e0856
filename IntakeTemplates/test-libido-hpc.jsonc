{"components": [{"key": "heading_low_testosterone", "html": "<h1><center><strong>Low Testosterone Assessment</strong></center></h1><p>To provide the best care, please answer the following questions regarding your symptoms and medical history related to low testosterone.</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "low_testosterone_intro_text", "html": "<p>Low testosterone can cause a variety of symptoms that can affect your energy, mood, and overall health. Please answer the following questions to help us better understand your symptoms and determine if further evaluation is needed.</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "reason_for_testosterone_testing", "type": "selectboxes", "input": true, "label": "What is your reason for requesting testosterone testing?", "values": [{"label": "I'm curious about my levels", "value": "curious"}, {"label": "I've had low levels in the past", "value": "prior_low"}, {"label": "I have symptoms that might be related to low testosterone", "value": "symptoms"}, {"label": "I'm trying to conceive and want to check my hormones", "value": "fertility"}, {"label": "I have concerns about erectile function or sexual performance", "value": "erectile"}, {"label": "I'm monitoring my levels while on testosterone or after steroid use", "value": "on_trt_or_steroids"}, {"label": "I'm interested in hormone optimization / longevity medicine", "value": "optimization"}, {"label": "Other", "value": "other"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Reason for Testosterone Testing:"}, {"key": "reason_for_testosterone_testing_other", "type": "textfield", "input": true, "label": "Please describe your reason for testing:", "tableView": true, "customConditional": "show = data.reason_for_testosterone_testing === 'other';"}, {"key": "heading_fertility", "html": "</br><h4>Fertility</h4><p>We’ll ask a few questions to better understand your fertility goals and whether hormone testing is appropriate at this stage.</p>", "type": "content", "input": false, "tableView": false, "customConditional": "show = data.reason_for_testosterone_testing?.fertility === true"}, {"key": "prior_semen_analysis", "type": "radio", "input": true, "label": "Have you ever had a semen analysis test done?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Prior semen analysis completed:", "customConditional": "show = data.reason_for_testosterone_testing?.fertility === true"}, {"key": "semen_analysis_result", "type": "radio", "input": true, "label": "Were the results of your semen analysis normal or abnormal?", "values": [{"label": "Normal", "value": "normal"}, {"label": "Abnormal", "value": "abnormal"}, {"label": "I don't remember", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Semen analysis result:", "customConditional": "show = data.prior_semen_analysis === 'yes'"}, {"key": "fertility_testing_recommendation_no_semen", "html": "<div style='border-left: 4px solid #0d6efd; background-color: #e7f1ff; padding: 10px; margin-bottom: 10px;'><strong>Recommendation:</strong> Hormone testing is usually only recommended if a semen analysis shows low sperm count or poor motility. If you haven't had a semen test yet, this is typically the first step in male fertility evaluation. Hormone testing alone does not provide enough information to assess fertility without a semen analysis.</div>", "type": "content", "input": false, "customConditional": "show = data.prior_semen_analysis === 'no' || data.prior_semen_analysis === 'unsure'"}, {"key": "fertility_testing_understanding_no_semen", "type": "radio", "input": true, "label": "Do you understand that hormone testing is only recommended if semen analysis shows abnormal results? Do you still wish to proceed?", "values": [{"label": "I understand and still wish to proceed", "value": "understand"}, {"label": "I do not understand and have questions", "value": "do_not_understand"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Understands hormone testing recommendation without semen analysis:", "customConditional": "show = data.prior_semen_analysis === 'no' || data.prior_semen_analysis === 'unsure'"}, {"key": "fertility_testing_recommendation_normal_semen", "html": "<div style='border-left: 4px solid #0d6efd; background-color: #e7f1ff; padding: 10px; margin-bottom: 10px;'><strong>Recommendation:</strong> Since your semen analysis was normal, hormone testing is typically not required. However, if you still wish to proceed, we can continue with hormone evaluation upon your confirmation.</div>", "type": "content", "input": false, "customConditional": "show = data.semen_analysis_result === 'normal'"}, {"key": "fertility_testing_understanding_normal_semen", "type": "radio", "input": true, "label": "Do you understand that hormone testing is not usually needed with normal semen analysis, but still wish to proceed?", "values": [{"label": "I understand and still wish to proceed", "value": "understand"}, {"label": "I do not understand and have questions", "value": "do_not_understand"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Understands hormone testing recommendation with normal semen:", "customConditional": "show = data.semen_analysis_result === 'normal'"}, {"key": "header_symptoms", "html": "<h2>Symptoms</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "low_testosterone_symptoms", "type": "selectboxes", "input": true, "label": "Are you currently experiencing any of the following symptoms?", "values": [{"label": "Low energy or fatigue", "value": "low_energy", "shortcut": ""}, {"label": "Reduced libido (sexual desire)", "value": "reduced_libido", "shortcut": ""}, {"label": "Erectile dysfunction or difficulty maintaining erections", "value": "ed", "shortcut": ""}, {"label": "Brain fog or difficulty concentrating", "value": "brain_fog", "shortcut": ""}, {"label": "Loss of muscle mass or strength", "value": "muscle_loss", "shortcut": ""}], "adminFlag": true, "tableView": true, "confirm_label": "Testosterone-Related Symptoms:", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_low_testosterone_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = !!data.none_of_the_above_low_testosterone_symptoms || _.some(_.values(data.low_testosterone_symptoms));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "low_testosterone_symptoms_present", "type": "textfield", "input": true, "label": "Patient indicated they have the following testosterone-related symptoms:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "I have the following testosterone-related symptoms:", "calculateValue": "value = _.join(_.map(_.filter([ { label: 'Low energy or fatigue', value: 'low_energy' }, { label: 'Reduced libido (sexual desire)', value: 'reduced_libido' }, { label: 'Erectile dysfunction or difficulty maintaining erections', value: 'ed' }, { label: 'Brain fog or difficulty concentrating', value: 'brain_fog' }, { label: 'Loss of muscle mass or strength', value: 'muscle_loss' } ], function(option) { return data.low_testosterone_symptoms && data.low_testosterone_symptoms[option.value]; }), 'label'), ', ');"}, {"key": "low_testosterone_symptoms_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following testosterone-related symptoms:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "I DO NOT have the following testosterone-related symptoms:", "calculateValue": "value = _.join(_.map(_.filter([ { label: 'Low energy or fatigue', value: 'low_energy' }, { label: 'Reduced libido (sexual desire)', value: 'reduced_libido' }, { label: 'Erectile dysfunction or difficulty maintaining erections', value: 'ed' }, { label: 'Brain fog or difficulty concentrating', value: 'brain_fog' }, { label: 'Loss of muscle mass or strength', value: 'muscle_loss' } ], function(option) { return !(data.low_testosterone_symptoms && data.low_testosterone_symptoms[option.value]); }), 'label'), ', ');"}, {"key": "heading_testosterone_deficiency_symptoms", "html": "</br><h2 class='text-center'>Testosterone Deficiency Related Symptoms</h2>", "type": "content", "input": false, "tableView": false, "customConditional": "show = data.low_testosterone_symptoms && Object.values(data.low_testosterone_symptoms).includes(true);"}, {"key": "testosterone_symptom_onset_pattern", "type": "radio", "input": true, "label": "Did your symptoms all start around the same time, or did they develop on separate days?", "values": [{"label": "All symptoms started around the same time", "value": "same_time"}, {"label": "Symptoms started on separate days", "value": "separate_days"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Onset pattern of symptoms:", "customConditional": "show = data.low_testosterone_symptoms && Object.values(data.low_testosterone_symptoms).filter(Boolean).length > 1"}, {"key": "durations_testosterone", "type": "textfield", "input": true, "label": "Durations:", "hidden": true, "disabled": true, "multiple": true, "tableView": false, "clearOnHide": false, "calculateValue": [{"label": "Less than 2 weeks ago", "value": "lt_2_weeks"}, {"label": "2-4 weeks ago", "value": "2_4_weeks"}, {"label": "1 month ago", "value": "1_month"}, {"label": "2 months ago", "value": "2_months"}, {"label": "3 months ago", "value": "3_months"}, {"label": "4 months ago", "value": "4_months"}, {"label": "5 months ago", "value": "5_months"}, {"label": "6 months ago", "value": "6_months"}, {"label": "7-12 months ago", "value": "7_12_months"}, {"label": "1-2 years ago", "value": "1_2_years"}, {"label": "2-3 years ago", "value": "2_3_years"}, {"label": "More than 3 years ago", "value": "gt_3_years"}]}, {"key": "all_testosterone_symptoms_start", "data": {"custom": "values = data.durations_testosterone"}, "type": "select", "input": true, "label": "When did your testosterone-related symptoms start?", "widget": "html5", "dataSrc": "custom", "validate": {"required": true}, "tableView": true, "confirm_label": "All symptoms started:", "customConditional": "show = data.low_testosterone_symptoms && Object.values(data.low_testosterone_symptoms).filter(Boolean).length >= 2 && data.testosterone_symptom_onset_pattern === 'same_time';", "optionsLabelPosition": "right"}, {"key": "heading_testosterone_triggers", "html": "</br><h4>Potential Triggers for Symptom Onset</h4>", "type": "content", "input": false, "tableView": false, "customConditional": "show = data.low_testosterone_symptoms && Object.values(data.low_testosterone_symptoms).includes(true) && (!data.testosterone_symptom_onset_pattern || data.testosterone_symptom_onset_pattern === 'same_time');"}, {"key": "heading_testosterone_triggers_life", "html": "</br><h4>Work, School, and Personal Life Events</h4><p>Stressful life changes can affect your hormones. Please let us know if any of the following occured to you <strong> before or around </strong>the time your symptoms began.</p>", "type": "content", "input": false, "tableView": false, "customConditional": "show = data.low_testosterone_symptoms && Object.values(data.low_testosterone_symptoms).includes(true) && (!data.testosterone_symptom_onset_pattern || data.testosterone_symptom_onset_pattern === 'same_time');"}, {"key": "testosterone_triggers_life", "type": "selectboxes", "input": true, "label": "Work, School, and Personal Stressors (select all that apply)", "values": [{"label": "Increased stress at work or school", "value": "work_stress"}, {"label": "New job or career change", "value": "new_job"}, {"label": "Relationship problems, break-up or divorce", "value": "relationship_issues"}, {"label": "Financial stress", "value": "financial_stress"}, {"label": "Death or illness of a loved one", "value": "grief"}, {"label": "Travel or relocation", "value": "relocation"}], "adminFlag": true, "tableView": true, "confirm_label": "Work/School/Personal Triggers:", "customConditional": "show = data.low_testosterone_symptoms && Object.values(data.low_testosterone_symptoms).includes(true) && (!data.testosterone_symptom_onset_pattern || data.testosterone_symptom_onset_pattern === 'same_time');", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_testosterone_triggers_life", "type": "checkbox", "input": true, "label": "None of the above", "validate": {"custom": "valid = !!data.none_of_the_above_testosterone_triggers_life || _.some(_.values(data.testosterone_triggers_life));"}, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.low_testosterone_symptoms && Object.values(data.low_testosterone_symptoms).includes(true) && (!data.testosterone_symptom_onset_pattern || data.testosterone_symptom_onset_pattern === 'same_time');"}, {"key": "testosterone_triggers_life_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following life-related triggers:", "hidden": true, "disabled": true, "tableView": true, "confirm_label": "I DO NOT have the following life-related triggers:", "calculateValue": "value = _.replace(_.join(_.map(_.difference(_.keys(_.pickBy(data.testosterone_triggers_life, _.negate(_.identity))), ['other_life']), _.startCase), ', '), /_/g, ' ');"}, {"key": "heading_testosterone_triggers_health", "html": "</br><h4>Health, Illness, and Medical History</h4><p>Certain health events or treatments can contribute to hormonal changes. Please let us know if any of the following occured to you <strong> before or around </strong>the time your symptoms began.</p>", "type": "content", "input": false, "tableView": false, "customConditional": "show = data.low_testosterone_symptoms && Object.values(data.low_testosterone_symptoms).includes(true) && (!data.testosterone_symptom_onset_pattern || data.testosterone_symptom_onset_pattern === 'same_time');"}, {"key": "testosterone_triggers_health", "type": "selectboxes", "input": true, "label": "Health and Medical Triggers (select all that apply)", "values": [{"label": "Recent illness or cold", "value": "recent_illness"}, {"label": "Major surgery or hospitalization", "value": "surgery"}, {"label": "Chronic pain or injury", "value": "chronic_pain"}, {"label": "COVID-19 infection", "value": "covid"}, {"label": "Mental health changes (e.g. depression, anxiety)", "value": "mental_health_shift"}, {"label": "Use of new medications or supplements", "value": "medications"}, {"label": "Start or recent use of testosterone/steroids", "value": "testosterone_use"}], "adminFlag": true, "tableView": true, "confirm_label": "Health or Medical Triggers:", "customConditional": "show = data.low_testosterone_symptoms && Object.values(data.low_testosterone_symptoms).includes(true) && (!data.testosterone_symptom_onset_pattern || data.testosterone_symptom_onset_pattern === 'same_time');", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_testosterone_triggers_health", "type": "checkbox", "input": true, "label": "None of the above", "validate": {"custom": "valid = !!data.none_of_the_above_testosterone_triggers_health || _.some(_.values(data.testosterone_triggers_health));"}, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.low_testosterone_symptoms && Object.values(data.low_testosterone_symptoms).includes(true) && (!data.testosterone_symptom_onset_pattern || data.testosterone_symptom_onset_pattern === 'same_time');"}, {"key": "testosterone_triggers_health_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following health-related triggers:", "hidden": true, "disabled": true, "tableView": true, "confirm_label": "I DO NOT have the following health-related triggers:", "calculateValue": "value = _.replace(_.join(_.map(_.difference(_.keys(_.pickBy(data.testosterone_triggers_health, _.negate(_.identity))), ['other_health']), _.startCase), ', '), /_/g, ' ');"}, {"key": "heading_testosterone_triggers_lifestyle", "html": "</br><h4>Lifestyle and Environmental Factors</h4><p>Changes in routine or lifestyle can affect testosterone levels. Please let us know if any of the following occured to you <strong> before or around </strong>the time your symptoms began.</p>", "type": "content", "input": false, "tableView": false, "customConditional": "show = data.low_testosterone_symptoms && Object.values(data.low_testosterone_symptoms).includes(true) && (!data.testosterone_symptom_onset_pattern || data.testosterone_symptom_onset_pattern === 'same_time');"}, {"key": "testosterone_triggers_lifestyle", "type": "selectboxes", "input": true, "label": "Lifestyle or Environmental Triggers (select all that apply)", "values": [{"label": "Changes in sleep schedule or insomnia", "value": "sleep_issues"}, {"label": "New intense training regimen", "value": "intense_training"}, {"label": "Stopped regular exercise", "value": "stopped_exercise"}, {"label": "Keto or low-carb diet", "value": "keto_lowcarb"}, {"label": "Calorie restriction / intermittent fasting", "value": "calorie_restriction"}, {"label": "Weight gain or body fat increase", "value": "weight_gain"}, {"label": "Poor nutrition or unbalanced diet", "value": "poor_diet"}, {"label": "Other", "value": "other_lifestyle"}], "tableView": true, "confirm_label": "Lifestyle or Environmental Triggers:", "customConditional": "show = data.reason_for_testosterone_testing?.symptoms === true && data.low_testosterone_symptoms && Object.values(data.low_testosterone_symptoms).includes(true) && (!data.testosterone_symptom_onset_pattern || data.testosterone_symptom_onset_pattern === 'same_time');", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_testosterone_triggers_lifestyle", "type": "checkbox", "input": true, "label": "None of the above", "validate": {"custom": "valid = !!data.none_of_the_above_testosterone_triggers_lifestyle || _.some(_.values(data.testosterone_triggers_lifestyle));"}, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.reason_for_testosterone_testing?.symptoms === true && data.testosterone_symptom_onset_pattern === 'same_time' && data.low_testosterone_symptoms && Object.values(data.low_testosterone_symptoms).includes(true);"}, {"key": "testosterone_triggers_lifestyle_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following lifestyle or environmental triggers:", "hidden": true, "disabled": true, "tableView": true, "confirm_label": "I DO NOT have the following lifestyle-related triggers:", "calculateValue": "value = _.replace(_.join(_.map(_.difference(_.keys(_.pickBy(data.testosterone_triggers_lifestyle, _.negate(_.identity))), ['other_lifestyle']), _.startCase), ', '), /_/g, ' ');"}, {"key": "heading_fatigue_symptom_details", "html": "</br><h4 class='mb-n2'>Low Energy or Fatigue</h4>", "type": "content", "input": false, "label": "Fatigue Details", "tableView": false, "refreshOnChange": true, "customConditional": "show = data.low_testosterone_symptoms && data.low_testosterone_symptoms.low_energy === true"}, {"key": "symptom_start_fatigue", "data": {"custom": "values = [{label: `I've always had low energy and this isn't new for me`, value:`always`}].concat(data.durations_testosterone)"}, "type": "select", "input": true, "label": "When did your low energy or fatigue start?", "widget": "html5", "dataSrc": "custom", "validate": {"required": true}, "tableView": true, "confirm_label": "Fatigue started:", "customConditional": "show = !!data.low_testosterone_symptoms && data.low_testosterone_symptoms.low_energy === true && (!data.testosterone_symptom_onset_pattern || data.testosterone_symptom_onset_pattern === 'separate_days')", "optionsLabelPosition": "right"}, {"key": "fatigue_description", "type": "selectboxes", "input": true, "label": "How would you describe your fatigue or low energy? Please select all that apply:", "values": [{"label": "Physically tired (e.g. muscle fatigue, heavy limbs)", "value": "physical"}, {"label": "Mentally tired or foggy", "value": "mental"}, {"label": "Lack of motivation or drive", "value": "motivation"}, {"label": "Sleepy or needing naps during the day", "value": "sleepy"}, {"label": "Energy drops as the day goes on", "value": "evening_worse"}, {"label": "Fatigue despite good sleep", "value": "despite_sleep"}, {"label": "Other", "value": "other"}], "tableView": true, "confirm_label": "Fatigue description:", "customConditional": "show = data.low_testosterone_symptoms && data.low_testosterone_symptoms.low_energy === true", "optionsLabelPosition": "right"}, {"key": "heading_reduced_libido", "html": "</br><h4 class='mb-n2'>Reduced <PERSON><PERSON><PERSON> (Sexual Desire)</h4>", "type": "content", "input": false, "label": "Libido Details", "tableView": false, "refreshOnChange": true, "customConditional": "show = data.low_testosterone_symptoms?.reduced_libido === true"}, {"key": "symptom_start_libido", "data": {"custom": "values = [{label: `I've always had lower sexual desire and this isn't new for me`, value:`always`}].concat(data.durations_testosterone)"}, "type": "select", "input": true, "label": "When did you first notice a decrease in your sexual desire?", "widget": "html5", "dataSrc": "custom", "validate": {"required": true}, "tableView": true, "confirm_label": "Reduced libido started:", "customConditional": "show = data.low_testosterone_symptoms?.reduced_libido === true && (!data.testosterone_symptom_onset_pattern || data.testosterone_symptom_onset_pattern === 'separate_days')"}, {"key": "libido_pattern", "type": "radio", "input": true, "label": "Is the decrease in your sexual desire constant or does it come and go?", "values": [{"label": "It's constant — low libido all the time", "value": "constant"}, {"label": "It comes and goes", "value": "fluctuating"}, {"label": "Unsure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Pattern of libido change:", "customConditional": "show = data.low_testosterone_symptoms?.reduced_libido === true;"}, {"key": "libido_distress_level", "type": "radio", "input": true, "label": "How much is this change in libido affecting your mood, relationship, or quality of life?", "values": [{"label": "Not at all", "value": "none"}, {"label": "A little", "value": "mild"}, {"label": "A moderate amount", "value": "moderate"}, {"label": "A lot", "value": "severe"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Impact of libido change:", "customConditional": "show = data.low_testosterone_symptoms?.reduced_libido === true;"}, {"key": "heading_ed", "html": "</br><h4 class='mb-n2'>Erectile Dysfunction</h4>", "type": "content", "input": false, "label": "ED Details", "tableView": false, "refreshOnChange": true, "customConditional": "show = data.low_testosterone_symptoms?.ed === true"}, {"key": "symptom_start_ed", "data": {"custom": "values = [{label: `This has always been an issue for me`, value:`always`}].concat(data.durations_testosterone)"}, "type": "select", "input": true, "label": "When did you start experiencing difficulty achieving or maintaining an erection?", "widget": "html5", "dataSrc": "custom", "validate": {"required": true}, "tableView": true, "confirm_label": "Erectile dysfunction started:", "customConditional": "show = data.low_testosterone_symptoms?.ed === true && (!data.testosterone_symptom_onset_pattern || data.testosterone_symptom_onset_pattern === 'separate_days')"}, {"key": "ed_symptom_types", "type": "selectboxes", "input": true, "label": "What types of erection-related issues have you experienced? (Select all that apply)", "values": [{"label": "I cannot get an erection at all", "value": "cannot_get"}, {"label": "I can get an erection but not maintain it", "value": "cannot_maintain"}, {"label": "My erections are less firm than before", "value": "weaker"}, {"label": "Takes longer to get an erection", "value": "takes_longer"}, {"label": "I no longer wake up with erections", "value": "no_morning"}, {"label": "My erection curves or bends abnormally", "value": "curvature"}, {"label": "Other", "value": "other"}], "tableView": true, "confirm_label": "Types of erectile dysfunction symptoms:", "customConditional": "show = data.low_testosterone_symptoms?.ed === true", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_ed_symptom_types", "type": "checkbox", "input": true, "label": "None of the above", "validate": {"custom": "valid = !!data.none_of_the_above_ed_symptom_types || _.some(_.values(data.ed_symptom_types));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.low_testosterone_symptoms?.ed === true"}, {"key": "ed_symptom_types_other_description", "type": "textarea", "input": true, "label": "Please describe your erectile concern:", "tableView": true, "autoExpand": false, "placeholder": "e.g., erections are painful, very short-lived, or inconsistent.", "customConditional": "show = data.ed_symptom_types?.other === true"}, {"key": "ed_severity", "type": "radio", "input": true, "label": "How would you describe your current erectile function?", "values": [{"label": "I can’t get an erection at all", "value": "none"}, {"label": "I can get an erection but cannot maintain it", "value": "cannot_maintain"}, {"label": "I can maintain an erection, but it's inconsistent", "value": "inconsistent"}, {"label": "Mostly normal, just not as strong as before", "value": "mild"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Erectile function severity:", "customConditional": "show = data.ed_symptom_types && (data.ed_symptom_types.cannot_get || data.ed_symptom_types.cannot_maintain || data.ed_symptom_types.weaker)"}, {"key": "ed_morning_erections", "type": "radio", "input": true, "label": "Do you still get spontaneous morning erections?", "values": [{"label": "Yes — most mornings", "value": "regular"}, {"label": "Occasionally", "value": "occasional"}, {"label": "Rarely or never", "value": "absent"}, {"label": "Not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Morning erections:", "customConditional": "show = data.ed_symptom_types && (data.ed_symptom_types.no_morning || data.ed_symptom_types.cannot_get)"}, {"key": "ed_curvature_severity", "type": "radio", "input": true, "label": "Does the curvature affect your ability to have sex?", "values": [{"label": "Yes — it's painful or prevents intercourse", "value": "interferes"}, {"label": "No — it's minor and doesn’t interfere", "value": "minor"}, {"label": "Not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Severity of curvature:", "customConditional": "show = data.ed_symptom_types?.curvature === true"}, {"key": "recommendation_penile_doppler_due_to_curvature", "html": "<div style='border-left: 4px solid #0d6efd; background-color: #e7f1ff; padding: 10px; margin-bottom: 10px;'><strong>Recommendation:</strong> Because the curvature is affecting your ability to have sex, we recommend seeing a physician in-person for further evaluation. A <strong>penile Doppler ultrasound</strong> may be suggested. This is a painless test that assesses blood flow and structure in the penis, helping determine if <PERSON><PERSON><PERSON><PERSON>’s disease or another cause is involved.</div>", "type": "content", "input": false, "customConditional": "show = data.ed_symptom_types?.curvature === true && data.ed_curvature_severity === 'interferes';"}, {"key": "penile_doppler_curvature_understanding", "type": "radio", "input": true, "label": "Do you understand this recommendation for a penile Doppler ultrasound?", "values": [{"label": "I understand", "value": "understand"}, {"label": "I do not understand", "value": "do_not_understand"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Understands penile Doppler recommendation:", "customConditional": "show = data.ed_symptom_types?.curvature === true && data.ed_curvature_severity === 'interferes';"}, {"key": "heading_brain_fog", "html": "</br><h4 class='mb-n2'>Brain Fog or Difficulty Concentrating</h4>", "type": "content", "input": false, "label": "Cognitive Symptoms", "tableView": false, "refreshOnChange": true, "customConditional": "show = data.low_testosterone_symptoms?.brain_fog === true"}, {"key": "symptom_start_brain_fog", "data": {"custom": "values = [{label: `I've always had some level of brain fog`, value:`always`}].concat(data.durations_testosterone)"}, "type": "select", "input": true, "label": "When did your brain fog or trouble focusing begin?", "widget": "html5", "dataSrc": "custom", "validate": {"required": true}, "tableView": true, "confirm_label": "Brain fog started:", "customConditional": "show = data.low_testosterone_symptoms?.brain_fog === true && (!data.testosterone_symptom_onset_pattern || data.testosterone_symptom_onset_pattern === 'separate_days')"}, {"key": "brain_fog_description", "type": "selectboxes", "input": true, "label": "How would you describe the brain fog or difficulty concentrating? (Select all that apply)", "values": [{"label": "Difficulty concentrating", "value": "concentration"}, {"label": "Easily distracted", "value": "distracted"}, {"label": "Forgetting tasks or conversations", "value": "forgetfulness"}, {"label": "Slower thinking or processing", "value": "slowed_thinking"}, {"label": "Feeling mentally 'foggy' or unclear", "value": "fuzzy_feeling"}, {"label": "Other", "value": "other"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Description of cognitive symptoms:", "customConditional": "show = data.low_testosterone_symptoms?.brain_fog === true", "optionsLabelPosition": "right"}, {"key": "brain_fog_timing", "type": "radio", "input": true, "label": "When is the brain fog most noticeable?", "values": [{"label": "In the morning", "value": "morning"}, {"label": "In the afternoon", "value": "afternoon"}, {"label": "Evening", "value": "evening"}, {"label": "All day", "value": "constant"}, {"label": "Varies", "value": "varies"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Brain fog timing:", "customConditional": "show = data.low_testosterone_symptoms?.brain_fog === true"}, {"key": "heading_muscle_loss", "html": "<h4 class='mb-n2'>Loss of Muscle Mass or Strength</h4>", "type": "content", "input": false, "label": "Muscle Loss Details", "tableView": false, "refreshOnChange": true, "customConditional": "show = data.low_testosterone_symptoms?.muscle_loss === true"}, {"key": "symptom_start_muscle_loss", "data": {"custom": "values = [{label: `I've always had low strength or muscle mass`, value:`always`}].concat(data.durations_testosterone)"}, "type": "select", "input": true, "label": "When did you start noticing a reduction in muscle mass or strength?", "widget": "html5", "dataSrc": "custom", "validate": {"required": true}, "tableView": true, "confirm_label": "Muscle loss started:", "customConditional": "show = data.low_testosterone_symptoms?.muscle_loss === true && (!data.testosterone_symptom_onset_pattern || data.testosterone_symptom_onset_pattern === 'separate_days')"}, {"key": "muscle_loss_chronicity", "type": "radio", "input": true, "label": "Which of the following best describes your experience with muscle mass or strength?", "values": [{"label": "I've always had trouble gaining muscle, even with exercise", "value": "chronic_difficulty"}, {"label": "I used to build muscle easily, but have lost mass recently", "value": "recent_loss"}, {"label": "I’ve lost strength or size without changing my training", "value": "unexpected_loss"}, {"label": "I'm not sure / haven't noticed a clear pattern", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Muscle issue description:", "customConditional": "show = data.low_testosterone_symptoms?.muscle_loss === true"}, {"key": "muscle_loss_what_changed", "type": "selectboxes", "input": true, "label": "Have you noticed a change in any of the following? (Select all that apply)", "values": [{"label": "Muscle size (e.g. arms, chest, legs look smaller)", "value": "size"}, {"label": "Strength or endurance (lifting less weight, fatiguing faster)", "value": "strength"}, {"label": "Both strength and size", "value": "both"}, {"label": "Neither — just feels different", "value": "neither"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Changes noticed:", "customConditional": "show = data.low_testosterone_symptoms?.muscle_loss === true"}, {"key": "muscle_loss_training_consistency", "type": "radio", "input": true, "label": "Have you been consistently training or exercising during this time?", "values": [{"label": "Yes — my workouts have been consistent", "value": "consistent"}, {"label": "No — I’ve been less active", "value": "less_active"}, {"label": "I stopped training entirely", "value": "stopped"}, {"label": "Not applicable — I don’t train regularly", "value": "not_applicable"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Training consistency:", "customConditional": "show = data.low_testosterone_symptoms?.muscle_loss === true"}, {"key": "heading_diet", "html": "</br><h2>Diet and Supplement Intake</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "current_dietary_substances", "type": "selectboxes", "input": true, "label": "Do you currently consume any of the following?", "values": [{"label": "<PERSON><PERSON><PERSON>", "value": "creatine"}, {"label": "Alcohol", "value": "alcohol"}, {"label": "Caffeine (e.g. coffee, energy drinks, pre-workout)", "value": "caffeine"}, {"label": "Protein powders or supplements", "value": "protein"}, {"label": "Over-the-counter fat burners", "value": "fat_burners"}, {"label": "Other", "value": "other"}, {"label": "Prefer not to disclose", "value": "prefer_not_disclose"}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "confirm_label": "Dietary substances currently consumed:"}, {"key": "heading_symptoms", "html": "</br><h2>General Symptoms</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "heading_heart_health", "html": "</br><h4>Heart Health</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "cardiovascular_symptoms", "type": "selectboxes", "input": true, "label": "Do you have any of the following cardiovascular symptoms?", "values": [{"label": "Chest pain, tightness or discomfort", "value": "chest_pain", "shortcut": ""}, {"label": "Palpitations", "value": "palpitations", "shortcut": ""}, {"label": "Swelling in the legs, ankles, or feet", "value": "swelling", "shortcut": ""}, {"label": "Dizziness or fainting", "value": "dizziness", "shortcut": ""}], "adminFlag": true, "tableView": true, "confirm_label": "Cardiovascular symptoms:", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_cardiovascular_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = !!data.none_of_the_above_cardiovascular_symptoms || _.some(_.values(data.cardiovascular_symptoms));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "cardiac_symptoms_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following cardiac symptoms:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "I DO NOT have the following heart related symptoms:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.cardiovascular_symptoms, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "heading_chest_pain", "html": "</br><h4>Chest Pain</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.chest_pain === true;"}, {"key": "chest_pain_onset", "data": {"values": [{"label": "Today", "value": "today"}, {"label": "1 day ago", "value": "1_day"}, {"label": "2 days ago", "value": "2_days"}, {"label": "3 days ago", "value": "3_days"}, {"label": "4 days ago", "value": "4_days"}, {"label": "5 days ago", "value": "5_days"}, {"label": "6 days ago", "value": "6_days"}, {"label": "7 days ago", "value": "7_days"}, {"label": "1-2 weeks ago", "value": "1_2_weeks"}, {"label": "2-4 weeks ago", "value": "2_4_weeks"}, {"label": "1-3 months ago", "value": "1_3_months"}, {"label": "More than 3 months ago", "value": "3_plus_months"}]}, "type": "select", "input": true, "label": "When did the chest pain or discomfort start?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Chest pain onset:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.chest_pain === true;"}, {"key": "chest_pain_triggers", "type": "selectboxes", "input": true, "label": "What brings the chest pain on or makes it worse?", "values": [{"label": "Physical activity or exertion", "value": "exertion"}, {"label": "Stress or anxiety", "value": "stress"}, {"label": "Eating a heavy meal", "value": "meal"}, {"label": "Lying down", "value": "lying"}, {"label": "Breathing deeply or coughing", "value": "breathing"}, {"label": "Unknown or unpredictable", "value": "unknown"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Chest pain triggers:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.chest_pain === true;"}, {"key": "chest_pain_relievers", "type": "selectboxes", "input": true, "label": "What makes the chest pain better?", "values": [{"label": "Rest", "value": "rest"}, {"label": "Lying down", "value": "lying"}, {"label": "Standing upright", "value": "standing"}, {"label": "Medication (e.g., nitroglycerin, pain relievers)", "value": "medication"}, {"label": "Nothing helps", "value": "nothing"}, {"label": "Unsure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Chest pain relief methods:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.chest_pain === true;"}, {"key": "chest_pain_character", "type": "radio", "input": true, "label": "How would you describe the pain?", "values": [{"label": "<PERSON>", "value": "sharp"}, {"label": "Dull/aching", "value": "dull"}, {"label": "Tight/pressure-like", "value": "pressure"}, {"label": "Burning", "value": "burning"}, {"label": "Stabbing", "value": "stabbing"}, {"label": "Other", "value": "other"}], "confirm_label": "Chest pain character:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.chest_pain === true;"}, {"key": "chest_pain_location", "type": "selectboxes", "input": true, "label": "Where is the pain located?", "values": [{"label": "Centre of chest", "value": "centre_chest"}, {"label": "Left side of chest", "value": "left_chest"}, {"label": "Right side of chest", "value": "right_chest"}, {"label": "Upper chest or sternum", "value": "upper_chest"}, {"label": "Other", "value": "other"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Chest pain location:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.chest_pain === true;"}, {"key": "chest_pain_radiation", "type": "selectboxes", "input": true, "label": "Does the pain radiate to any of the following areas?", "values": [{"label": "Left arm", "value": "left_arm"}, {"label": "Right arm", "value": "right_arm"}, {"label": "<PERSON><PERSON>", "value": "jaw"}, {"label": "Neck", "value": "neck"}, {"label": "Back", "value": "back"}, {"label": "No radiation", "value": "none"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Chest pain radiation:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.chest_pain === true;"}, {"key": "chest_pain_pattern", "type": "radio", "input": true, "label": "Is the chest pain constant or does it come and go?", "values": [{"label": "Constant", "value": "constant"}, {"label": "Comes and goes (intermittent)", "value": "intermittent"}, {"label": "Unsure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Chest pain pattern:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.chest_pain === true;"}, {"key": "heading_palpitations", "html": "</br><h4>Palpitations</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.palpitations === true;"}, {"key": "palpitations_onset", "data": {"values": [{"label": "Today", "value": "today"}, {"label": "1 day ago", "value": "1_day"}, {"label": "2-3 days ago", "value": "2_3_days"}, {"label": "4-7 days ago", "value": "4_7_days"}, {"label": "1-2 weeks ago", "value": "1_2_weeks"}, {"label": "2-4 weeks ago", "value": "2_4_weeks"}, {"label": "More than a month ago", "value": "1_month_plus"}]}, "type": "select", "input": true, "label": "When did your palpitations begin?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Palpitations onset:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.palpitations === true;"}, {"key": "palpitations_rhythm", "type": "radio", "input": true, "label": "How would you describe the rhythm of your palpitations?", "values": [{"label": "Regular and fast", "value": "regular_fast"}, {"label": "Irregular and fast", "value": "irregular_fast"}, {"label": "Skipped beats", "value": "skipped_beats"}, {"label": "Flutters", "value": "flutters"}, {"label": "Other", "value": "other"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Palpitations rhythm:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.palpitations === true;"}, {"key": "palpitations_triggers", "type": "selectboxes", "input": true, "label": "What triggers the palpitations?", "values": [{"label": "Exercise or physical activity", "value": "exercise"}, {"label": "Stress or anxiety", "value": "stress"}, {"label": "Caffeine or stimulants", "value": "caffeine"}, {"label": "Alcohol", "value": "alcohol"}, {"label": "Occur at rest", "value": "rest"}, {"label": "Occur at night", "value": "night"}, {"label": "No clear trigger", "value": "unknown"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Palpitations triggers:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.palpitations === true;"}, {"key": "palpitations_duration", "data": {"values": [{"label": "A few seconds", "value": "seconds"}, {"label": "Less than 5 minutes", "value": "less_5_min"}, {"label": "5-30 minutes", "value": "5_30_min"}, {"label": "30 minutes to a few hours", "value": "30min_hours"}, {"label": "More than a few hours", "value": "many_hours"}, {"label": "Constant", "value": "constant"}, {"label": "Varies", "value": "varies"}]}, "type": "select", "input": true, "label": "How long do the palpitations usually last?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Palpitations duration:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.palpitations === true;"}, {"key": "palpitations_associated_symptoms", "type": "selectboxes", "input": true, "label": "Do you experience any of the following during palpitations?", "values": [{"label": "Dizziness or lightheadedness", "value": "dizziness"}, {"label": "Shortness of breath", "value": "sob"}, {"label": "Chest pain", "value": "chest_pain"}, {"label": "Sweating", "value": "sweating"}, {"label": "Fainting or near-fainting", "value": "fainting"}, {"label": "No other symptoms", "value": "none"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Palpitations associated symptoms:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.palpitations === true;"}, {"key": "heading_swelling", "html": "</br><h4>Swelling</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.swelling === true;"}, {"key": "swelling_location", "type": "selectboxes", "input": true, "label": "Where is the swelling located?", "values": [{"label": "Feet", "value": "feet"}, {"label": "<PERSON><PERSON>", "value": "ankles"}, {"label": "Lower legs", "value": "lower_legs"}, {"label": "Thighs", "value": "thighs"}, {"label": "Abdomen", "value": "abdomen"}, {"label": "Other", "value": "other"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Swelling location:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.swelling === true;"}, {"key": "swelling_sidedness", "type": "radio", "input": true, "label": "Is the swelling on one side or both sides?", "values": [{"label": "Both sides", "value": "bilateral"}, {"label": "One side only)", "value": "unilateral"}, {"label": "Varies", "value": "varies"}, {"label": "Unsure", "value": "unsure"}], "tableView": true, "confirm_label": "Swelling sidedness:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.swelling === true;"}, {"key": "swelling_onset", "data": {"values": [{"label": "Today", "value": "today"}, {"label": "1 day ago", "value": "1_day"}, {"label": "2-3 days ago", "value": "2_3_days"}, {"label": "4-7 days ago", "value": "4_7_days"}, {"label": "1-2 weeks ago", "value": "1_2_weeks"}, {"label": "More than 2 weeks ago", "value": "2_plus_weeks"}]}, "type": "select", "input": true, "label": "When did the swelling begin?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Swelling onset:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.swelling === true;"}, {"key": "swelling_timing", "type": "radio", "input": true, "label": "When is the swelling most noticeable?", "values": [{"label": "By the end of the day", "value": "evening"}, {"label": "In the morning", "value": "morning"}, {"label": "Constant throughout the day", "value": "constant"}, {"label": "Varies day to day", "value": "varies"}, {"label": "Unsure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Swelling timing:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.swelling === true;"}, {"key": "swelling_pitting", "type": "radio", "input": true, "label": "When you press on the swollen area, does it leave a dent (pitting)?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "unsure"}], "tableView": true, "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.swelling === true;"}, {"key": "heading_dizziness", "html": "</br><h4>Dizziness or Fainting</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.dizziness === true;"}, {"key": "dizziness_onset", "data": {"values": [{"label": "Today", "value": "today"}, {"label": "1 day ago", "value": "1_day"}, {"label": "2-3 days ago", "value": "2_3_days"}, {"label": "4-7 days ago", "value": "4_7_days"}, {"label": "1-2 weeks ago", "value": "1_2_weeks"}, {"label": "More than 2 weeks ago", "value": "2_plus_weeks"}]}, "type": "select", "input": true, "label": "When did the dizziness or fainting episodes begin?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Dizziness onset:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.dizziness === true;"}, {"key": "dizziness_frequency", "type": "radio", "input": true, "label": "How often do you experience dizziness or fainting?", "values": [{"label": "Once", "value": "once"}, {"label": "Occasionally (less than once a week)", "value": "occasional"}, {"label": "Frequently (once or more per week)", "value": "frequent"}, {"label": "Daily or almost daily", "value": "daily"}, {"label": "Unsure", "value": "unsure"}], "tableView": true, "confirm_label": "Dizziness frequency:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.dizziness === true;"}, {"key": "dizziness_character", "type": "radio", "input": true, "label": "How would you describe the dizziness?", "values": [{"label": "Lightheadedness or feeling faint", "value": "lightheaded"}, {"label": "Spinning or vertigo", "value": "spinning"}, {"label": "Unsteady or off balance", "value": "unsteady"}, {"label": "Hard to describe", "value": "hard_to_describe"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Dizziness character:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.dizziness === true;"}, {"key": "dizziness_timing", "type": "selectboxes", "input": true, "label": "When does the dizziness or fainting usually happen?", "values": [{"label": "After standing up", "value": "standing_up"}, {"label": "After exertion or exercise", "value": "exertion"}, {"label": "At rest", "value": "rest"}, {"label": "With dehydration or hunger", "value": "dehydration"}, {"label": "Without a clear trigger", "value": "unknown"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Dizziness timing:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.dizziness === true;"}, {"key": "fainting_loss_consciousness", "type": "radio", "input": true, "label": "Have you ever fully lost consciousness during one of these episodes?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Unsure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Fainting/loss of consciousness:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.dizziness === true;"}, {"key": "heading_lung_health", "html": "</br><h4>Lung Health</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "respiratory_symptoms", "type": "selectboxes", "input": true, "label": "Do you have any of the following respiratory symptoms?", "values": [{"label": "<PERSON><PERSON>", "value": "cough", "shortcut": ""}, {"label": "Shortness of breath", "value": "shortness_of_breath", "shortcut": ""}, {"label": "Wheezing", "value": "wheezing", "shortcut": ""}], "adminFlag": true, "tableView": true, "confirm_label": "Respiratory symptoms:", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_respiratory_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = !!data.none_of_the_above_respiratory_symptoms || _.some(_.values(data.respiratory_symptoms));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "respiratory_symptoms_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following respiratory symptoms:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "I DO NOT have the following breathing related symptoms:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.respiratory_symptoms, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "heading_cough", "html": "</br><h4>Cough</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.respiratory_symptoms && data.respiratory_symptoms.cough === true;"}, {"key": "cough_type", "type": "radio", "input": true, "label": "Is your cough dry or productive?", "values": [{"label": "Dry (no phlegm)", "value": "dry"}, {"label": "Productive (with phlegm)", "value": "productive"}, {"label": "Varies", "value": "varies"}, {"label": "Unsure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Cough type:", "customConditional": "show = data.respiratory_symptoms && data.respiratory_symptoms.cough === true;"}, {"key": "cough_duration", "data": {"values": [{"label": "Less than 1 week", "value": "lt_1wk"}, {"label": "1-2 weeks", "value": "1_2wk"}, {"label": "2-4 weeks", "value": "2_4wk"}, {"label": "More than 4 weeks", "value": "gt_4wk"}]}, "type": "select", "input": true, "label": "How long have you had the cough?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Cough duration:", "customConditional": "show = data.respiratory_symptoms && data.respiratory_symptoms.cough === true;"}, {"key": "cough_coughing_blood", "type": "radio", "input": true, "label": "Have you noticed any blood when coughing?", "values": [{"label": "Yes - bright red blood", "value": "bright_red"}, {"label": "Yes - dark or coffee ground appearance", "value": "coffee_ground"}, {"label": "I think so - unsure of colour or source", "value": "unsure_appearance"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "description": "This includes coughing up blood that appears bright red or looks like coffee grounds (which may suggest bleeding in the lungs or stomach).", "confirm_label": "Coughing up blood:", "customConditional": "show = data.respiratory_symptoms && data.respiratory_symptoms.cough === true;"}, {"key": "cough_feeling_unwell", "type": "radio", "input": true, "label": "Do you feel generally unwell with your cough (e.g. fatigue, fever, weakness)?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Feeling unwell with cough:", "customConditional": "show = data.respiratory_symptoms && data.respiratory_symptoms.cough === true;"}, {"key": "cough_with_cold", "type": "radio", "input": true, "label": "Did your cough begin at the same time as a cold or viral illness (e.g. sore throat, congestion, fever)?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Cough with cold:", "customConditional": "show = data.respiratory_symptoms && data.respiratory_symptoms.cough === true;"}, {"key": "cough_progression", "type": "radio", "input": true, "label": "Is your cough improving, getting worse, or staying the same?", "values": [{"label": "Improving", "value": "improving"}, {"label": "Getting worse", "value": "worsening"}, {"label": "No change", "value": "no_change"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Cough progression:", "customConditional": "show = data.cough_with_cold === 'yes';"}, {"key": "cough_urgent_warning", "html": "<div style='border-left: 4px solid #ffc107; background-color: #fff3cd; padding: 10px; margin-bottom: 10px;'><strong>Warning:</strong> If you are experiencing any of the following — worsening cough, shortness of breath, coughing up blood, feeling generally unwell, or if your cough has lasted more than 4 weeks — we advise seeking same-day care in an emergency department. These may be signs of a more serious condition that should not be delayed.</div>", "type": "content", "input": false, "label": "Content", "customConditional": "show = (data.cough_progression === 'worsening') || (data.respiratory_symptoms && data.respiratory_symptoms.shortness_of_breath === true) || (data.cough_coughing_blood === true) || (data.cough_feeling_unwell === true) || (data.cough_duration === 'gt_4wk');"}, {"key": "cough_urgent_warning_understanding", "type": "radio", "input": true, "label": "Do you understand this warning about when to seek emergency care?", "values": [{"label": "I understand", "value": "understand"}, {"label": "I do not understand", "value": "do_not_understand"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Understands Emergency Care Warning:", "customConditional": "show = (data.cough_progression === 'worsening') || (data.respiratory_symptoms && data.respiratory_symptoms.shortness_of_breath === true) || (data.cough_coughing_blood === true) || (data.cough_feeling_unwell === true) || (data.cough_duration === 'gt_4wk');"}, {"key": "heading_shortness_of_breath", "html": "</br><h4>Shortness of Breath</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.respiratory_symptoms && data.respiratory_symptoms.shortness_of_breath === true;"}, {"key": "sob_triggers", "type": "selectboxes", "input": true, "label": "When do you typically feel short of breath?", "values": [{"label": "At rest", "value": "rest"}, {"label": "With mild activity (e.g. walking)", "value": "mild_activity"}, {"label": "With moderate or strenuous activity", "value": "exercise"}, {"label": "While lying flat", "value": "lying_flat"}, {"label": "At night (waking from sleep)", "value": "nocturnal"}, {"label": "Unsure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Shortness of breath triggers:", "customConditional": "show = data.respiratory_symptoms && data.respiratory_symptoms.shortness_of_breath === true;"}, {"key": "heading_wheezing", "html": "</br><h4>Wheezing</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.respiratory_symptoms && data.respiratory_symptoms.wheezing === true;"}, {"key": "wheezing_timing", "type": "selectboxes", "input": true, "label": "When is the wheezing most noticeable?", "values": [{"label": "During exercise", "value": "exercise"}, {"label": "At rest", "value": "rest"}, {"label": "At night", "value": "night"}, {"label": "In cold weather", "value": "cold_weather"}, {"label": "When lying down", "value": "lying_down"}, {"label": "When exposed to irritants (e.g. smoke, dust)", "value": "irritants"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Wheezing timing:", "customConditional": "show = data.respiratory_symptoms && data.respiratory_symptoms.wheezing === true;"}, {"key": "wheezing_relief", "type": "radio", "input": true, "label": "Do you use any medications to relieve wheezing?", "values": [{"label": "Yes - inhaler or nebulizer", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Occasionally", "value": "occasional"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Wheezing relief medications:", "customConditional": "show = data.respiratory_symptoms && data.respiratory_symptoms.wheezing === true;"}, {"key": "wheezing_asthma_history", "type": "radio", "input": true, "label": "Have you ever been diagnosed with asthma (currently or in the past)?", "values": [{"label": "Yes - currently diagnosed", "value": "current"}, {"label": "Yes - past diagnosis only", "value": "past"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Wheezing asthma history:", "customConditional": "show = data.respiratory_symptoms && data.respiratory_symptoms.wheezing === true;"}, {"key": "heading_gastro_health", "html": "</br><h4>Gastrointestinal Health</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "abdominal_gastrointestinal_symptoms", "type": "selectboxes", "input": true, "label": "Do you have any of the following abdominal or gastrointestinal symptoms?", "values": [{"label": "Abdominal pain", "value": "abdominal_pain", "shortcut": ""}, {"label": "<PERSON><PERSON><PERSON>", "value": "nausea", "shortcut": ""}, {"label": "Vomiting", "value": "vomiting", "shortcut": ""}, {"label": "Diarrhea", "value": "diarrhea", "shortcut": ""}, {"label": "Constipation", "value": "constipation", "shortcut": ""}, {"label": "Bloating or gas", "value": "bloating_gas", "shortcut": ""}, {"label": "Rectal bleeding", "value": "rectal_bleeding", "shortcut": ""}], "adminFlag": true, "tableView": true, "confirm_label": "Gastrointestinal symptoms:", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_abdominal_gastrointestinal_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = !!data.none_of_the_above_abdominal_gastrointestinal_symptoms || _.some(_.values(data.abdominal_gastrointestinal_symptoms));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "gastrointestinal_symptoms_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following gastrointestinal symptoms:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "I DO NOT have the following gastrointestinal symptoms:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.abdominal_gastrointestinal_symptoms, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "heading_rectal_bleeding", "html": "</br><h4>Rectal Bleeding</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.abdominal_gastrointestinal_symptoms && data.abdominal_gastrointestinal_symptoms.rectal_bleeding === true;"}, {"key": "onset_rectal_bleeding", "data": {"values": [{"label": "Today", "value": "today"}, {"label": "1-3 days ago", "value": "1_3_days"}, {"label": "4-7 days ago", "value": "4_7_days"}, {"label": "1-2 weeks ago", "value": "1_2_weeks"}, {"label": "2-4 weeks ago", "value": "2_4_weeks"}, {"label": "More than 1 month ago (continuous)", "value": "over_1_month"}, {"label": "Comes and goes over the past month", "value": "recurrent_1_month"}, {"label": "Comes and goes over the past year", "value": "recurrent_1_year"}, {"label": "Comes and goes for many years", "value": "recurrent_many_years"}]}, "type": "select", "input": true, "label": "When did the rectal bleeding begin?", "widget": "html5", "tableView": true, "confirm_label": "Rectal bleeding onset:", "customConditional": "show = data.abdominal_gastrointestinal_symptoms && data.abdominal_gastrointestinal_symptoms.rectal_bleeding === true;"}, {"key": "rectal_bleeding_appearance", "type": "radio", "input": true, "label": "What type of blood have you noticed?", "values": [{"label": "Bright red blood (e.g., on toilet paper or in toilet)", "value": "bright_red"}, {"label": "Dark or tarry stool (melena)", "value": "tarry"}, {"label": "Blood mixed with stool", "value": "mixed"}, {"label": "I think so - unsure of colour or source", "value": "unsure"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "description": "This may include bright red blood, dark or tarry stools, or blood mixed with stool. Please select the best match.", "confirm_label": "Rectal bleeding appearance:", "customConditional": "show = data.abdominal_gastrointestinal_symptoms && data.abdominal_gastrointestinal_symptoms.rectal_bleeding === true;"}, {"key": "rectal_bleeding_typical_vs_new", "type": "radio", "input": true, "label": "Is this episode of rectal bleeding similar to past episodes, or is it different?", "values": [{"label": "Typical - feels the same as prior episodes", "value": "typical"}, {"label": "Different - new or unusual compared to prior", "value": "different"}, {"label": "Not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Rectal bleeding typical vs new:", "customConditional": "show = data.onset_rectal_bleeding && (data.onset_rectal_bleeding === 'recurrent_1_month' || data.onset_rectal_bleeding === 'recurrent_1_year' || data.onset_rectal_bleeding === 'recurrent_many_years');"}, {"key": "rectal_bleeding_warning", "html": "<div style='border-left: 4px solid #f5c518; background-color: #fff3cd; padding: 10px; margin-bottom: 10px;'><strong>Recommendation:</strong> Rectal bleeding can sometimes be caused by small tears in the skin (anal fissures) or hemorrhoids, especially if related to constipation or straining. However, it can also be a sign of more serious gastrointestinal conditions such as inflammatory bowel disease or colorectal cancer. If you are experiencing rectal bleeding, a physical exam and further evaluation — including possible lab testing or colonoscopy — may be warranted to determine the cause and ensure appropriate treatment.</div>", "type": "content", "input": false, "customConditional": "show = data.abdominal_gastrointestinal_symptoms && data.abdominal_gastrointestinal_symptoms.rectal_bleeding === true;"}, {"key": "rectal_bleeding_warning_understanding", "type": "radio", "input": true, "label": "Do you understand the importance of having rectal bleeding evaluated by a healthcare provider?", "values": [{"label": "I understand", "value": "understand"}, {"label": "I do not understand", "value": "do_not_understand"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Understands Rectal Bleeding Recommendation:", "customConditional": "show = data.abdominal_gastrointestinal_symptoms && data.abdominal_gastrointestinal_symptoms.rectal_bleeding === true;"}, {"key": "heading_abdominal_pain", "html": "</br><h4>Abdominal Pain</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.abdominal_gastrointestinal_symptoms && data.abdominal_gastrointestinal_symptoms.abdominal_pain === true;"}, {"key": "abdominal_pain_location", "type": "selectboxes", "input": true, "label": "Where is your abdominal pain located?", "values": [{"label": "Upper abdomen", "value": "upper"}, {"label": "Lower abdomen", "value": "lower"}, {"label": "Right side", "value": "right"}, {"label": "Left side", "value": "left"}, {"label": "Around the belly button", "value": "periumbilical"}, {"label": "Pain moves around", "value": "diffuse"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Abdominal pain location:", "customConditional": "show = data.abdominal_gastrointestinal_symptoms && data.abdominal_gastrointestinal_symptoms.abdominal_pain === true;"}, {"key": "onset_abdominal_pain", "data": {"values": [{"label": "Today", "value": "today"}, {"label": "1-3 days ago", "value": "1_3_days"}, {"label": "4-7 days ago", "value": "4_7_days"}, {"label": "1-2 weeks ago", "value": "1_2_weeks"}, {"label": "2-4 weeks ago", "value": "2_4_weeks"}, {"label": "More than 1 month ago (continuous)", "value": "over_1_month"}, {"label": "Comes and goes over the past month", "value": "recurrent_1_month"}, {"label": "Comes and goes over the past year", "value": "recurrent_1_year"}, {"label": "Comes and goes for many years", "value": "recurrent_many_years"}]}, "type": "select", "input": true, "label": "When did the abdominal pain begin?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Abdominal pain onset:", "customConditional": "show = data.abdominal_gastrointestinal_symptoms && data.abdominal_gastrointestinal_symptoms.abdominal_pain === true;"}, {"key": "abdominal_pain_typical_vs_new", "type": "radio", "input": true, "label": "Is this episode of abdominal pain similar to your past episodes, or is it different?", "values": [{"label": "Typical - feels the same as prior episodes", "value": "typical"}, {"label": "Different - new or unusual compared to prior", "value": "different"}, {"label": "Not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Abdominal pain typical vs new:", "customConditional": "show = data.onset_abdominal_pain && (data.onset_abdominal_pain === 'recurrent_1_month' || data.onset_abdominal_pain === 'recurrent_1_year' || data.onset_abdominal_pain === 'recurrent_many_years');"}, {"key": "heading_nausea", "html": "</br><h4>Nausea</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.abdominal_gastrointestinal_symptoms && data.abdominal_gastrointestinal_symptoms.nausea === true;"}, {"key": "nausea_timing", "type": "radio", "input": true, "label": "When do you usually feel nauseated?", "values": [{"label": "In the morning", "value": "morning"}, {"label": "After eating", "value": "post_meal"}, {"label": "Randomly throughout the day", "value": "random"}, {"label": "Constant", "value": "constant"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Nausea timing:", "customConditional": "show = data.abdominal_gastrointestinal_symptoms && data.abdominal_gastrointestinal_symptoms.nausea === true;"}, {"key": "onset_nausea", "data": {"values": [{"label": "Today", "value": "today"}, {"label": "1-3 days ago", "value": "1_3_days"}, {"label": "4-7 days ago", "value": "4_7_days"}, {"label": "1-2 weeks ago", "value": "1_2_weeks"}, {"label": "2-4 weeks ago", "value": "2_4_weeks"}, {"label": "More than 1 month ago (continuous)", "value": "over_1_month"}, {"label": "Comes and goes over the past month", "value": "recurrent_1_month"}, {"label": "Comes and goes over the past year", "value": "recurrent_1_year"}, {"label": "Comes and goes for many years", "value": "recurrent_many_years"}]}, "type": "select", "input": true, "label": "When did the nausea begin?", "widget": "html5", "tableView": true, "confirm_label": "Nausea onset:", "customConditional": "show = data.abdominal_gastrointestinal_symptoms && data.abdominal_gastrointestinal_symptoms.nausea === true;"}, {"key": "nausea_typical_vs_new", "type": "radio", "input": true, "label": "Is this episode of nausea similar to past episodes, or is it different?", "values": [{"label": "Typical - feels the same as prior episodes", "value": "typical"}, {"label": "Different - new or unusual compared to prior", "value": "different"}, {"label": "Not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Nausea typical vs new:", "customConditional": "show = data.onset_nausea && (data.onset_nausea === 'recurrent_1_month' || data.onset_nausea === 'recurrent_1_year' || data.onset_nausea === 'recurrent_many_years');"}, {"key": "heading_vomiting", "html": "</br><h4>Vomiting</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.abdominal_gastrointestinal_symptoms && data.abdominal_gastrointestinal_symptoms.vomiting === true;"}, {"key": "vomit_appearance", "type": "radio", "input": true, "label": "What does the vomit look like?", "values": [{"label": "Undigested food or fluid", "value": "food"}, {"label": "Yellow or green (bile)", "value": "bile"}, {"label": "Dark or coffee-ground appearance", "value": "coffee_ground"}, {"label": "Bright red blood", "value": "bright_red"}, {"label": "Not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "description": "This includes food, yellow/green bile, or signs of bleeding such as coffee ground appearance.", "confirm_label": "Vomiting appearance:", "customConditional": "show = data.abdominal_gastrointestinal_symptoms && data.abdominal_gastrointestinal_symptoms.vomiting === true;"}, {"key": "onset_vomiting", "data": {"values": [{"label": "Today", "value": "today"}, {"label": "1-3 days ago", "value": "1_3_days"}, {"label": "4-7 days ago", "value": "4_7_days"}, {"label": "1-2 weeks ago", "value": "1_2_weeks"}, {"label": "2-4 weeks ago", "value": "2_4_weeks"}, {"label": "More than 1 month ago (continuous)", "value": "over_1_month"}, {"label": "Comes and goes over the past month", "value": "recurrent_1_month"}, {"label": "Comes and goes over the past year", "value": "recurrent_1_year"}, {"label": "Comes and goes for many years", "value": "recurrent_many_years"}]}, "type": "select", "input": true, "label": "When did the vomiting begin?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Vomiting onset:", "customConditional": "show = data.abdominal_gastrointestinal_symptoms && data.abdominal_gastrointestinal_symptoms.vomiting === true;"}, {"key": "vomiting_typical_vs_new", "type": "radio", "input": true, "label": "Is this episode of vomiting similar to past episodes, or is it different?", "values": [{"label": "Typical - feels the same as prior episodes", "value": "typical"}, {"label": "Different - new or unusual compared to prior", "value": "different"}, {"label": "Not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Vomiting typical vs new:", "customConditional": "show = data.onset_vomiting && (data.onset_vomiting === 'recurrent_1_month' || data.onset_vomiting === 'recurrent_1_year' || data.onset_vomiting === 'recurrent_many_years');"}, {"key": "heading_diarrhea", "html": "</br><h4>Diarrhea</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.abdominal_gastrointestinal_symptoms && data.abdominal_gastrointestinal_symptoms.diarrhea === true;"}, {"key": "diarrhea_features", "type": "selectboxes", "input": true, "label": "What features describe your diarrhea?", "values": [{"label": "Watery", "value": "watery"}, {"label": "Contains mucus", "value": "mucus"}, {"label": "Contains blood", "value": "bloody"}, {"label": "<PERSON><PERSON> need to go", "value": "urgent"}, {"label": "Associated with cramping", "value": "cramping"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Diarrhea features:", "customConditional": "show = data.abdominal_gastrointestinal_symptoms && data.abdominal_gastrointestinal_symptoms.diarrhea === true;"}, {"key": "onset_diarrhea", "data": {"values": [{"label": "Today", "value": "today"}, {"label": "1-3 days ago", "value": "1_3_days"}, {"label": "4-7 days ago", "value": "4_7_days"}, {"label": "1-2 weeks ago", "value": "1_2_weeks"}, {"label": "2-4 weeks ago", "value": "2_4_weeks"}, {"label": "More than 1 month ago (continuous)", "value": "over_1_month"}, {"label": "Comes and goes over the past month", "value": "recurrent_1_month"}, {"label": "Comes and goes over the past year", "value": "recurrent_1_year"}, {"label": "Comes and goes for many years", "value": "recurrent_many_years"}]}, "type": "select", "input": true, "label": "When did the diarrhea begin?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Diarrhea onset:", "customConditional": "show = data.abdominal_gastrointestinal_symptoms && data.abdominal_gastrointestinal_symptoms.diarrhea === true;"}, {"key": "diarrhea_typical_vs_new", "type": "radio", "input": true, "label": "Is this episode of diarrhea similar to your previous episodes, or is it different?", "values": [{"label": "Typical - feels the same as prior episodes", "value": "typical"}, {"label": "Different - new or unusual compared to prior", "value": "different"}, {"label": "Not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Diarrhea typical vs new:", "customConditional": "show = data.onset_diarrhea && (data.onset_diarrhea === 'recurrent_1_month' || data.onset_diarrhea === 'recurrent_1_year' || data.onset_diarrhea === 'recurrent_many_years');"}, {"key": "heading_constipation", "html": "</br><h4>Constipation</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.abdominal_gastrointestinal_symptoms && data.abdominal_gastrointestinal_symptoms.constipation === true;"}, {"key": "constipation_frequency", "type": "radio", "input": true, "label": "How often are your bowel movements?", "values": [{"label": "Every 1-2 days", "value": "daily"}, {"label": "Every 3-4 days", "value": "3_4_days"}, {"label": "Less than 2 times per week", "value": "lt_2_week"}, {"label": "Unsure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Constipation frequency:", "customConditional": "show = data.abdominal_gastrointestinal_symptoms && data.abdominal_gastrointestinal_symptoms.constipation === true;"}, {"key": "onset_constipation", "data": {"values": [{"label": "Today", "value": "today"}, {"label": "1-3 days ago", "value": "1_3_days"}, {"label": "4-7 days ago", "value": "4_7_days"}, {"label": "1-2 weeks ago", "value": "1_2_weeks"}, {"label": "2-4 weeks ago", "value": "2_4_weeks"}, {"label": "More than 1 month ago (continuous)", "value": "over_1_month"}, {"label": "Comes and goes over the past month", "value": "recurrent_1_month"}, {"label": "Comes and goes over the past year", "value": "recurrent_1_year"}, {"label": "Comes and goes for many years", "value": "recurrent_many_years"}]}, "type": "select", "input": true, "label": "When did the constipation begin?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Constipation onset:", "customConditional": "show = data.abdominal_gastrointestinal_symptoms && data.abdominal_gastrointestinal_symptoms.constipation === true;"}, {"key": "constipation_typical_vs_new", "type": "radio", "input": true, "label": "Is this episode of constipation similar to your past experiences, or is it different?", "values": [{"label": "Typical - feels the same as prior episodes", "value": "typical"}, {"label": "Different - new or unusual compared to prior", "value": "different"}, {"label": "Not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Constipation typical vs new:", "customConditional": "show = data.onset_constipation && (data.onset_constipation === 'recurrent_1_month' || data.onset_constipation === 'recurrent_1_year' || data.onset_constipation === 'recurrent_many_years');"}, {"key": "heading_bloating", "html": "</br><h4>Bloating or Gas</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.abdominal_gastrointestinal_symptoms && data.abdominal_gastrointestinal_symptoms.bloating_gas === true;"}, {"key": "bloating_pattern", "type": "radio", "input": true, "label": "When is bloating or gas most noticeable?", "values": [{"label": "After eating", "value": "after_meals"}, {"label": "In the evening", "value": "evening"}, {"label": "All day", "value": "all_day"}, {"label": "Varies", "value": "varies"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Bloating or gas pattern:", "customConditional": "show = data.abdominal_gastrointestinal_symptoms && data.abdominal_gastrointestinal_symptoms.bloating_gas === true;"}, {"key": "onset_bloating", "data": {"values": [{"label": "Today", "value": "today"}, {"label": "1-3 days ago", "value": "1_3_days"}, {"label": "4-7 days ago", "value": "4_7_days"}, {"label": "1-2 weeks ago", "value": "1_2_weeks"}, {"label": "2-4 weeks ago", "value": "2_4_weeks"}, {"label": "More than 1 month ago (continuous)", "value": "over_1_month"}, {"label": "Comes and goes over the past month", "value": "recurrent_1_month"}, {"label": "Comes and goes over the past year", "value": "recurrent_1_year"}, {"label": "Comes and goes for many years", "value": "recurrent_many_years"}]}, "type": "select", "input": true, "label": "When did the bloating or gas begin?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Bloating or gas onset:", "customConditional": "show = data.abdominal_gastrointestinal_symptoms && data.abdominal_gastrointestinal_symptoms.bloating_gas === true;"}, {"key": "bloating_typical_vs_new", "type": "radio", "input": true, "label": "Is the bloating or gas similar to your previous episodes, or is it different this time?", "values": [{"label": "Typical - feels the same as prior episodes", "value": "typical"}, {"label": "Different - new or unusual compared to prior", "value": "different"}, {"label": "Not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Bloating or gas typical vs new:", "customConditional": "show = data.onset_bloating && (data.onset_bloating === 'recurrent_1_month' || data.onset_bloating === 'recurrent_1_year' || data.onset_bloating === 'recurrent_many_years');"}, {"key": "heading_mental_health", "html": "</br><h4>Mental Health</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "mental_health_symptoms", "type": "selectboxes", "input": true, "label": "Have you experienced any of the following symptoms?", "values": [{"label": "Feeling down, sad, or depressed most days", "value": "low_mood", "shortcut": ""}, {"label": "Easily irritated, angry, or short-tempered", "value": "anger", "shortcut": ""}, {"label": "Feeling nervous, anxious, or unable to relax", "value": "anxiety", "shortcut": ""}, {"label": "Seeing or hearing things others do not (hallucinations or unusual sensations)", "value": "psychosis", "shortcut": ""}], "adminFlag": true, "tableView": true, "confirm_label": "Mental health symptoms:", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_mental_health_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "You must select at least one symptom or 'None of the above'."}, "validate": {"custom": "valid = !!data.none_of_the_above_mental_health_symptoms || _.some(_.values(data.mental_health_symptoms));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "mental_health_symptoms_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following mental health symptoms:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "I DO NOT have the following mental health symptoms:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.mental_health_symptoms, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "heading_mental_health_followup", "html": "</br><h4>Mental Health Symptoms</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.mental_health_symptoms && Object.values(data.mental_health_symptoms).includes(true);"}, {"key": "mental_health_prior_diagnosis", "type": "radio", "input": true, "label": "Have you ever been diagnosed with or suspected of having depression or anxiety?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "unsure"}], "tableView": true, "confirm_label": "Prior mental health diagnosis:", "customConditional": "show = data.mental_health_symptoms && Object.values(data.mental_health_symptoms).includes(true);"}, {"key": "mental_health_diagnosis_source", "type": "radio", "input": true, "label": "Who made the diagnosis?", "values": [{"label": "Family doctor or primary care provider", "value": "family_doctor"}, {"label": "Psychiatrist", "value": "psychiatrist"}, {"label": "Therapist or counselor", "value": "therapist"}, {"label": "Self-diagnosed or suspected", "value": "self_diagnosed"}, {"label": "Not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Mental health diagnosis source:", "customConditional": "show = data.mental_health_prior_diagnosis === 'yes';"}, {"key": "mental_health_onset", "data": {"values": [{"label": "Within the last week", "value": "under_1_week"}, {"label": "1-4 weeks ago", "value": "1_4_weeks"}, {"label": "1-3 months ago", "value": "1_3_months"}, {"label": "3-6 months ago", "value": "3_6_months"}, {"label": "6-12 months ago", "value": "6_12_months"}, {"label": "More than a year ago (continuous symptoms)", "value": "over_1_year_continuous"}, {"label": "Symptoms come and go over the past month", "value": "recurrent_1_month"}, {"label": "Symptoms come and go over the past year", "value": "recurrent_1_year"}, {"label": "Symptoms come and go for many years", "value": "recurrent_many_years"}, {"label": "Unsure", "value": "unsure"}]}, "type": "select", "input": true, "label": "When did these symptoms first begin?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Mental health symptoms onset:", "customConditional": "show = data.mental_health_symptoms && Object.values(data.mental_health_symptoms).includes(true);"}, {"key": "mental_health_prior_treatment", "type": "selectboxes", "input": true, "label": "Have you ever been treated for these symptoms?", "values": [{"label": "Medication (e.g. antidepressants, antianxiety, antipsychotics)", "value": "medication"}, {"label": "Counseling or therapy", "value": "therapy"}, {"label": "Hospitalization or emergency care", "value": "hospitalization"}, {"label": "No prior treatment", "value": "none"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Prior mental health treatment:", "customConditional": "show = data.mental_health_symptoms && Object.values(data.mental_health_symptoms).includes(true);"}, {"key": "mental_health_therapy_discussion", "type": "radio", "input": true, "label": "Have you previously discussed these symptoms with a therapist or mental health professional?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Prior mental health therapy discussion:", "customConditional": "show = data.mental_health_symptoms && Object.values(data.mental_health_symptoms).includes(true);"}, {"key": "mental_health_suicidal_check", "type": "radio", "input": true, "label": "Have you had any thoughts of harming yourself or others?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Prefer not to answer", "value": "prefer_not_say"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Has had thoughts of harming self or others:", "customConditional": "show = data.mental_health_symptoms && Object.values(data.mental_health_symptoms).includes(true);"}, {"key": "mental_health_si_hi_timing", "type": "radio", "input": true, "label": "Are you currently experiencing thoughts of harming yourself or others?", "values": [{"label": "No - I've had them in the past but not today or recently", "value": "past_only"}, {"label": "I had them recently, but not today", "value": "recent_but_not_today"}, {"label": "Yes - I have them today", "value": "today"}, {"label": "Yes - I have them frequently", "value": "frequent"}, {"label": "Unsure how to describe it", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Current thoughts of harming self or others:", "customConditional": "show = data.mental_health_suicidal_check === 'yes';"}, {"key": "mental_health_emergency_warning", "html": "<div style='border-left: 4px solid #dc3545; background-color: #f8d7da; padding: 10px; margin-bottom: 10px;'><strong>Emergency Support:</strong> You indicated that you're currently experiencing thoughts of harming yourself or others. Please call <strong>911</strong> or go to the <strong>nearest emergency department</strong> immediately. Help is available right now and your safety matters.</div>", "type": "content", "input": false, "label": "Content", "customConditional": "show = data.mental_health_si_hi_timing === 'today' || data.mental_health_si_hi_timing === 'frequent';"}, {"key": "mental_health_safety_warning", "html": "<div style='border-left: 4px solid #ffc107; background-color: #fff3cd; padding: 10px; margin-bottom: 10px;'><strong>Warning:</strong> Even if you are not currently experiencing suicidal or harmful thoughts, please seek same-day in-person care through an emergency department if this changes. Immediate help is available and critical when these symptoms occur.</div>", "type": "content", "input": false, "customConditional": "show = data.mental_health_suicidal_check === 'no';"}, {"key": "mental_health_safety_understanding", "type": "radio", "input": true, "label": "Do you understand when to seek emergency care if thoughts of harm arise?", "values": [{"label": "I understand", "value": "understand"}, {"label": "I do not understand", "value": "do_not_understand"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Understands Mental Health Safety Advice:", "customConditional": "show = data.mental_health_suicidal_check === 'no';"}, {"key": "header_prior_testing", "html": "<h2></br>Prior Testosterone Testing</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "previous_testosterone_test", "type": "radio", "input": true, "label": "Have you ever had your testosterone levels tested?", "values": [{"label": "Yes", "value": true}, {"label": "No", "value": false}, {"label": "Not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Prior testosterone testing:", "optionsLabelPosition": "right"}, {"key": "prior_testosterone_levels", "data": {"values": [{"label": "Less than 5 nmol/L", "value": "less_than_5"}, {"label": "5-8 nmol/L", "value": "5_to_8"}, {"label": "8-12 nmol/L", "value": "8_to_12"}, {"label": "12-15 nmol/L", "value": "12_to_15"}, {"label": "Above 15 nmol/L", "value": "above_15"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "If known, what was your last testosterone level (total testosterone)?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Prior testosterone levels:", "customConditional": "show = data.previous_testosterone_test === true"}, {"key": "time_of_previous_test", "data": {"values": [{"label": "Morning (before 10 AM)", "value": "morning"}, {"label": "Afternoon", "value": "afternoon"}, {"label": "Evening", "value": "evening"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "Do you remember the time of day the test was performed?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Time of previous testosterone test:", "customConditional": "show = data.previous_testosterone_test === true"}, {"key": "alcohol_prior_test", "type": "radio", "input": true, "label": "Did you consume alcohol the evening before your testosterone test?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Alcohol consumption prior to test:", "customConditional": "show = data.previous_testosterone_test === true"}, {"key": "sleep_quality_prior_test", "type": "radio", "input": true, "label": "Did you get a good night's sleep the night before your testosterone test?", "values": [{"label": "Yes, I slept well", "value": "good_sleep"}, {"label": "No, my sleep was disrupted", "value": "poor_sleep"}, {"label": "Not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Sleep quality prior to test:", "customConditional": "show = data.previous_testosterone_test === true"}, {"key": "sleep_schedule_prior_test", "type": "radio", "input": true, "label": "Were you consistently sleeping well in the weeks leading up to your testosterone test?", "values": [{"label": "Yes, I was sleeping well", "value": "consistent_good_sleep"}, {"label": "No, my sleep was irregular or poor", "value": "irregular_poor_sleep"}, {"label": "Not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Sleep schedule prior to test:", "customConditional": "show = data.previous_testosterone_test === true"}, {"key": "header_medical_history", "html": "<h2>Medical History</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "medical_conditions", "type": "selectboxes", "input": true, "label": "Do you have any of the following medical conditions? (Select all that apply)", "values": [{"label": "Diabetes", "value": "diabetes", "shortcut": ""}, {"label": "High blood pressure", "value": "high_blood_pressure", "shortcut": ""}, {"label": "High cholesterol", "value": "high_cholesterol", "shortcut": ""}, {"label": "Heart disease", "value": "heart_disease", "shortcut": ""}, {"label": "Kidney or Liver disease", "value": "kidney_liver_disease", "shortcut": ""}, {"label": "Thyroid disease", "value": "thyroid_disease", "shortcut": ""}], "adminFlag": true, "tableView": true, "confirm_label": "Medical conditions:", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_medical_conditions", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "Please select at least one option or 'None of the above.'"}, "validate": {"custom": "valid = !!data.none_of_the_above_medical_conditions || _.some(_.values(data.medical_conditions));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "medical_conditions_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following medical conditions:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "I DO NOT have the following medical conditions:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.medical_conditions, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "header_medications", "html": "<h2>Medications</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "medications", "type": "selectboxes", "input": true, "label": "Are you currently taking any of the following medications? (Select all that apply)", "values": [{"label": "Blood pressure medications", "value": "blood_pressure_meds", "shortcut": ""}, {"label": "Cholesterol-lowering medications", "value": "cholesterol_meds", "shortcut": ""}, {"label": "Diabetes medications", "value": "diabetes_meds", "shortcut": ""}, {"label": "Thyroid medications", "value": "thyroid_meds", "shortcut": ""}, {"label": "Antidepressants", "value": "antidepressants", "shortcut": ""}], "adminFlag": true, "tableView": true, "confirm_label": "Medications:", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_medications", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "Please select at least one option or 'None of the above.'"}, "validate": {"custom": "valid = !!data.none_of_the_above_medications || _.some(_.values(data.medications));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "medications_not_present", "type": "textfield", "input": true, "label": "Patient indicated they are NOT taking the following medications:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "I am NOT taking the following medications:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.medications, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "heading_sleep", "html": "</br><h4>Sleep</h4><p>Some symptoms like fatigue, brain fog, or low libido can be linked to untreated sleep apnea or poor sleep quality.</p>", "type": "content", "input": false, "tableView": false, "customConditional": "show = data.low_testosterone_symptoms?.low_energy === true || data.low_testosterone_symptoms?.brain_fog === true"}, {"key": "osa_symptoms", "type": "selectboxes", "input": true, "label": "Have you experienced any of the following sleep-related symptoms? (Select all that apply)", "values": [{"label": "Loud snoring", "value": "snoring"}, {"label": "Waking up gasping or choking", "value": "gasping"}, {"label": "Witnessed pauses in breathing during sleep", "value": "apnea_pauses"}, {"label": "Morning headaches", "value": "morning_headache"}, {"label": "Feeling unrefreshed after sleep", "value": "unrefreshed"}, {"label": "Daytime sleepiness or dozing off unintentionally", "value": "daytime_sleepiness"}, {"label": "None of the above", "value": "none"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Sleep-related symptoms:", "customConditional": "show = data.low_testosterone_symptoms?.low_energy === true || data.low_testosterone_symptoms?.brain_fog === true", "optionsLabelPosition": "right"}, {"key": "sleep_study_done", "type": "radio", "input": true, "label": "Have you ever had a sleep study to test for sleep apnea?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "unsure"}], "tableView": true, "confirm_label": "Prior sleep study completed:", "customConditional": "show = data.osa_symptoms && Object.values(data.osa_symptoms).some(v => v === true && v !== 'none')"}, {"key": "sleep_study_result", "type": "radio", "input": true, "label": "Did the sleep study show that you have sleep apnea (OSA)?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Sleep study result:", "customConditional": "show = data.sleep_study_done === 'yes'"}, {"key": "cpap_use", "type": "radio", "input": true, "label": "Are you currently using a CPAP machine or other treatment for sleep apnea?", "values": [{"label": "Yes — I use CPAP regularly", "value": "yes"}, {"label": "No — I have a machine but don't use it", "value": "non_compliant"}, {"label": "No — I never received treatment", "value": "never"}, {"label": "Not applicable", "value": "not_applicable"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Current CPAP/treatment use:", "customConditional": "show = data.sleep_study_result === 'yes'"}, {"key": "recommendation_sleep_study", "html": "<div style='border-left: 4px solid #0d6efd; background-color: #e7f1ff; padding: 10px; margin-bottom: 10px;'><strong>Recommendation:</strong> Because you're experiencing fatigue or brain fog with signs of possible sleep apnea, we recommend discussing a <strong>sleep study</strong> with your doctor. Sleep apnea is a treatable condition that can cause low testosterone, poor energy, weight gain, and reduced libido. A sleep study helps identify if your oxygen levels or breathing are disrupted at night and whether CPAP or other treatment could help you feel more rested.</div>", "type": "content", "input": false, "customConditional": "show = (data.sleep_study_done === 'no' || data.sleep_study_done === 'unsure') && data.osa_symptoms && Object.values(data.osa_symptoms).some(v => v === true && v !== 'none')"}, {"key": "sleep_study_understanding", "type": "radio", "input": true, "label": "Do you understand this recommendation about sleep testing and its role in symptoms like fatigue and low testosterone?", "values": [{"label": "I understand", "value": "understand"}, {"label": "I do not understand", "value": "do_not_understand"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Understands sleep testing recommendation:", "customConditional": "show = (data.sleep_study_done === 'no' || data.sleep_study_done === 'unsure') && data.osa_symptoms && Object.values(data.osa_symptoms).some(v => v === true && v !== 'none')"}, {"key": "header_prior_tests", "html": "<h2>Prior Lab Testing</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "prior_tests_completed", "type": "selectboxes", "input": true, "label": "Have you had any of the following tests completed <strong>after</strong> your symptoms started?", "values": [{"label": "Kidney function (eGFR)", "value": "egfr"}, {"label": "Lipid Profile (Cholesterol levels)", "value": "lipid_profile"}, {"label": "Diabetes Testing (HbA1c)", "value": "a1c"}, {"label": "Fasting Blood Glucose (FBG)", "value": "fasting_glucose"}, {"label": "CBC (Complete Blood Count)", "value": "cbc"}, {"label": "Vitamin B12", "value": "b12"}, {"label": "TSH (Thyroid Stimulating Hormone)", "value": "tsh"}, {"label": "I have not had these tests completed", "value": "no_prior_tests"}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "confirm_label": "Prior Tests Completed:", "optionsLabelPosition": "right"}, {"key": "last_known_lab_timing", "data": {"values": [{"label": "<1 month ago", "value": "Less than 1 month ago"}, {"label": "1-2 months ago", "value": "1-2 months ago"}, {"label": "2-3 months ago", "value": "2-3 months ago"}, {"label": "3-4 months ago", "value": "3-4 months ago"}, {"label": "4-5 months ago", "value": "4-5 months ago"}, {"label": "5-6 months ago", "value": "5-6 months ago"}, {"label": "6-12 months ago", "value": "6-12 months ago"}, {"label": "12-24 months ago", "value": "12-24 months ago"}, {"label": "24+ months ago", "value": "More than 24 months ago"}, {"label": "I haven't had lab testing", "value": "Haven't had lab testing"}]}, "type": "select", "input": true, "label": "Do you recall when your last set of lab tests (bloodwork) was completed?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Last Known Lab Timing:", "customConditional": "show = data.prior_tests_completed?.no_prior_tests === true && Object.values(data.prior_tests_completed).filter(v => v === true).length === 1"}, {"key": "heading_kidney_function", "html": "<h3>Kidney Function (eGFR)</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.prior_tests_completed?.egfr;"}, {"key": "last_kidney_function_test", "data": {"values": [{"label": "<1 month ago", "value": "Less than 1 month ago"}, {"label": "1-2 months ago", "value": "1-2 months ago"}, {"label": "2-3 months ago", "value": "2-3 months ago"}, {"label": "3-4 months ago", "value": "3-4 months ago"}, {"label": "4-5 months ago", "value": "4-5 months ago"}, {"label": "5-6 months ago", "value": "5-6 months ago"}, {"label": "6-12 months ago", "value": "6-12 months ago"}, {"label": "12-24 months ago", "value": "12-24 months ago"}, {"label": "24+ months ago", "value": "More than 24 months ago"}, {"label": "Never had one", "value": "Never had one"}]}, "type": "select", "input": true, "label": "When was your last kidney function test completed?", "widget": "html5", "validate": {"required": true}, "tableView": true, "placeholder": "Select the time range", "confirm_label": "Last Kidney Function Test:", "customConditional": "show = data.prior_tests_completed?.egfr;"}, {"key": "prior_kidney_function_value", "data": {"values": [{"label": "I don't remember my result", "value": "I don't remember my result"}, {"label": "eGFR > 90", "value": "eGFR > 90"}, {"label": "eGFR 85-89", "value": "eGFR 85-89"}, {"label": "eGFR 80-84", "value": "eGFR 80-84"}, {"label": "eGFR 75-79", "value": "eGFR 75-79"}, {"label": "eGFR 70-74", "value": "eGFR 70-74"}, {"label": "eGFR 65-69", "value": "eGFR 65-69"}, {"label": "eGFR 60-64", "value": "eGFR 60-64"}, {"label": "eGFR 55-59", "value": "eGFR 55-59"}, {"label": "eGFR 50-54", "value": "eGFR 50-54"}, {"label": "eGFR 45-49", "value": "eGFR 45-49"}, {"label": "eGFR < 45", "value": "eGFR < 45"}]}, "type": "select", "input": true, "label": "What was your most recent eGFR measurement?", "widget": "html5", "validate": {"required": true}, "tableView": true, "placeholder": "Select the value range", "confirm_label": "eGFR Value Range:", "customConditional": "show = data.prior_tests_completed?.egfr;"}, {"key": "heading_urine_acr", "html": "</br><h3>Urine Albumin-to-Creatinine Ratio (ACR)</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.prior_tests_completed?.urine_acr;"}, {"key": "last_urine_acr_test", "data": {"values": [{"label": "<1 month ago", "value": "Less than 1 month ago"}, {"label": "1-2 months ago", "value": "1-2 months ago"}, {"label": "2-3 months ago", "value": "2-3 months ago"}, {"label": "3-4 months ago", "value": "3-4 months ago"}, {"label": "4-5 months ago", "value": "4-5 months ago"}, {"label": "5-6 months ago", "value": "5-6 months ago"}, {"label": "6-12 months ago", "value": "6-12 months ago"}, {"label": "12-24 months ago", "value": "12-24 months ago"}, {"label": "24+ months ago", "value": "More than 24 months ago"}, {"label": "Never had one", "value": "Never had one"}]}, "type": "select", "input": true, "label": "When was your last urine ACR test completed?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Last Urine ACR Test:", "customConditional": "show = data.prior_tests_completed?.urine_acr;"}, {"key": "heading_lipid_profile", "html": "</br><h3>Lipid Profile (Cholesterol)</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.prior_tests_completed?.lipid_profile;"}, {"key": "last_lipid_profile_test", "data": {"values": [{"label": "<1 month ago", "value": "Less than 1 month ago"}, {"label": "1-2 months ago", "value": "1-2 months ago"}, {"label": "2-3 months ago", "value": "2-3 months ago"}, {"label": "3-4 months ago", "value": "3-4 months ago"}, {"label": "4-5 months ago", "value": "4-5 months ago"}, {"label": "5-6 months ago", "value": "5-6 months ago"}, {"label": "6-12 months ago", "value": "6-12 months ago"}, {"label": "12-24 months ago", "value": "12-24 months ago"}, {"label": "24+ months ago", "value": "More than 24 months ago"}, {"label": "Never had one", "value": "Never had one"}]}, "type": "select", "input": true, "label": "When was your last lipid profile test completed?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Last Lipid Profile Test:", "customConditional": "show = data.prior_tests_completed?.lipid_profile;"}, {"key": "heading_lipid_profile_results", "html": "</br><h3>Lipid Profile Results</h3><p>Select any abnormalities found in your test results.</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.last_lipid_profile_test !== undefined;"}, {"key": "lipid_profile_abnormalities", "type": "selectboxes", "input": true, "label": "Were any of the following findings reported in your lipid profile?", "values": [{"label": "High LDL", "value": "high_ldl"}, {"label": "High Triglycerides", "value": "high_triglycerides"}, {"label": "High HDL", "value": "high_hdl"}, {"label": "Normal HDL", "value": "normal_hdl"}, {"label": "Normal lipid profile", "value": "normal_profile"}, {"label": "I don't remember my values", "value": "dont_remember"}, {"label": "I don't remember them but was told they were normal", "value": "dont_remember_normal"}], "tooltip": "Select all that apply based on your most recent test results.", "validate": {"required": true}, "tableView": true, "customClass": "mt-n3", "confirm_label": "Lipid Profile Abnormalities:", "customConditional": "show = data.last_lipid_profile_test !== undefined;"}, {"key": "heading_diabetes_tests", "html": "</br><h3>Diabetes Testing (HbA1c or Fasting Blood Glucose)</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.prior_tests_completed?.a1c === true || data.prior_tests_completed?.fasting_glucose === true;"}, {"key": "last_a1c_test", "data": {"values": [{"label": "<1 month ago", "value": "Less than 1 month ago"}, {"label": "1-2 months ago", "value": "1-2 months ago"}, {"label": "2-3 months ago", "value": "2-3 months ago"}, {"label": "3-4 months ago", "value": "3-4 months ago"}, {"label": "4-5 months ago", "value": "4-5 months ago"}, {"label": "5-6 months ago", "value": "5-6 months ago"}, {"label": "6-12 months ago", "value": "6-12 months ago"}, {"label": "12-24 months ago", "value": "12-24 months ago"}, {"label": "24+ months ago", "value": "More than 24 months ago"}, {"label": "Never had one", "value": "Never had one"}]}, "type": "select", "input": true, "label": "When was your last HbA1c test completed?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Last A1c Test:", "customConditional": "show = data.prior_tests_completed?.a1c === true;"}, {"key": "recent_a1c_value", "data": {"values": [{"label": "I don't remember my result", "value": "dont_remember"}, {"label": "Below 5.7%", "value": "below_5_7"}, {"label": "5.7% - 6.4%", "value": "5_7_6_4"}, {"label": "6.5% - 6.9%", "value": "6_5_6_9"}, {"label": "7.0% - 7.9%", "value": "7_0_7_9"}, {"label": "8.0% - 8.9%", "value": "8_0_8_9"}, {"label": "9.0% - 9.9%", "value": "9_0_9_9"}, {"label": "10.0% or higher", "value": "10_or_higher"}]}, "type": "select", "input": true, "label": "What was your most recent HbA1c result?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Recent A1c Value:", "customConditional": "show = data.prior_tests_completed?.a1c === true;"}, {"key": "last_fasting_glucose_test", "data": {"values": [{"label": "<1 month ago", "value": "Less than 1 month ago"}, {"label": "1-2 months ago", "value": "1-2 months ago"}, {"label": "2-3 months ago", "value": "2-3 months ago"}, {"label": "3-4 months ago", "value": "3-4 months ago"}, {"label": "4-5 months ago", "value": "4-5 months ago"}, {"label": "5-6 months ago", "value": "5-6 months ago"}, {"label": "6-12 months ago", "value": "6-12 months ago"}, {"label": "12-24 months ago", "value": "12-24 months ago"}, {"label": "24+ months ago", "value": "More than 24 months ago"}, {"label": "Never had one", "value": "Never had one"}]}, "type": "select", "input": true, "label": "When was your last fasting blood glucose test completed?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Last Fasting Glucose Test:", "customConditional": "show = data.prior_tests_completed?.fasting_glucose === true;"}, {"key": "recent_fbg_value", "data": {"values": [{"label": "I don't remember my result", "value": "dont_remember"}, {"label": "Below 5.6 mmol/L [<100 mg/dL]", "value": "Below 5.6 mmol/L"}, {"label": "5.6 - 6.9 mmol/L [100-125 mg/dL]", "value": "5.6 - 6.9 mmol/L"}, {"label": "7.0 - 7.9 mmol/L [126-142 mg/dL]", "value": "7.0 - 7.9 mmol/L"}, {"label": "8.0 - 8.9 mmol/L [143-160 mg/dL]", "value": "8.0 - 8.9 mmol/L"}, {"label": "9.0 - 9.9 mmol/L [161-178 mg/dL]", "value": "9.0 - 9.9 mmol/L"}, {"label": "10.0 - 11.9 mmol/L [179-214 mg/dL]", "value": "10.0 - 11.9 mmol/L"}, {"label": "12.0+ mmol/L [215+ mg/dL]", "value": "12.0+ mmol/L"}]}, "type": "select", "input": true, "label": "What was your most recent <strong>fasting blood glucose</strong> (FBG) result?", "widget": "html5", "validate": {"required": true}, "tableView": true, "description": "Select the range that matches your lab result. Canadian units shown (mmol/L), with U.S. units in [mg/dL].", "confirm_label": "Recent FBG Value:", "customConditional": "show = data.prior_tests_completed?.fasting_glucose === true && data.last_fasting_glucose_test !== 'never_had';"}, {"key": "heading_cbc", "html": "<h3>CBC (Complete Blood Count)</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.prior_tests_completed?.cbc;"}, {"key": "last_cbc_test", "data": {"values": [{"label": "<1 month ago", "value": "Less than 1 month ago"}, {"label": "1-2 months ago", "value": "1-2 months ago"}, {"label": "2-3 months ago", "value": "2-3 months ago"}, {"label": "3-4 months ago", "value": "3-4 months ago"}, {"label": "4-5 months ago", "value": "4-5 months ago"}, {"label": "5-6 months ago", "value": "5-6 months ago"}, {"label": "6-12 months ago", "value": "6-12 months ago"}, {"label": "12-24 months ago", "value": "12-24 months ago"}, {"label": "24+ months ago", "value": "More than 24 months ago"}, {"label": "Never had one", "value": "Never had one"}]}, "type": "select", "input": true, "label": "When was your last CBC test completed?", "widget": "html5", "validate": {"required": true}, "tableView": true, "placeholder": "Select the time range", "confirm_label": "Last CBC Test:", "customConditional": "show = data.prior_tests_completed?.cbc;"}, {"key": "prior_cbc_value", "data": {"values": [{"label": "I don't remember my result", "value": "I don't remember my result"}, {"label": "< 70 g/L", "value": "Less than 70 g/L"}, {"label": "70-100 g/L", "value": "70-100 g/L"}, {"label": "101-120 g/L", "value": "101-120 g/L"}, {"label": "121-150 g/L", "value": "121-150 g/L"}, {"label": "> 150 g/L", "value": "Greater than 150 g/L"}]}, "type": "select", "input": true, "label": "What was your most recent hemoglobin level (CBC result)?", "widget": "html5", "validate": {"required": true}, "tableView": true, "placeholder": "Select your hemoglobin level", "confirm_label": "Hemoglobin Level:", "customConditional": "show = data.prior_tests_completed?.cbc;"}, {"key": "heading_b12", "html": "</br><h3>Vitamin B12</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.prior_tests_completed?.b12;"}, {"key": "last_b12_test", "data": {"values": [{"label": "<1 month ago", "value": "Less than 1 month ago"}, {"label": "1-2 months ago", "value": "1-2 months ago"}, {"label": "2-3 months ago", "value": "2-3 months ago"}, {"label": "3-4 months ago", "value": "3-4 months ago"}, {"label": "4-5 months ago", "value": "4-5 months ago"}, {"label": "5-6 months ago", "value": "5-6 months ago"}, {"label": "6-12 months ago", "value": "6-12 months ago"}, {"label": "12-24 months ago", "value": "12-24 months ago"}, {"label": "24+ months ago", "value": "More than 24 months ago"}, {"label": "Never had one", "value": "Never had one"}]}, "type": "select", "input": true, "label": "When was your last B12 test completed?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Last B12 Test:", "customConditional": "show = data.prior_tests_completed?.b12;"}, {"key": "prior_b12_value", "data": {"values": [{"label": "I don't remember my result", "value": "dont_remember"}, {"label": "< 150 pmol/L", "value": "lt_150"}, {"label": "150-200 pmol/L", "value": "150_200"}, {"label": "200-300 pmol/L", "value": "200_300"}, {"label": "> 300 pmol/L", "value": "gt_300"}]}, "type": "select", "input": true, "label": "What was your most recent B12 level?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "B12 Level:", "customConditional": "show = data.prior_tests_completed?.b12;"}, {"key": "heading_tsh", "html": "</br><h3>TSH (Thyroid Stimulating Hormone)</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.prior_tests_completed?.tsh;"}, {"key": "last_tsh_test", "data": {"values": [{"label": "<1 month ago", "value": "Less than 1 month ago"}, {"label": "1-2 months ago", "value": "1-2 months ago"}, {"label": "2-3 months ago", "value": "2-3 months ago"}, {"label": "3-4 months ago", "value": "3-4 months ago"}, {"label": "4-5 months ago", "value": "4-5 months ago"}, {"label": "5-6 months ago", "value": "5-6 months ago"}, {"label": "6-12 months ago", "value": "6-12 months ago"}, {"label": "12-24 months ago", "value": "12-24 months ago"}, {"label": "24+ months ago", "value": "More than 24 months ago"}, {"label": "Never had one", "value": "Never had one"}]}, "type": "select", "input": true, "label": "When was your last TSH test completed?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Last TSH Test:", "customConditional": "show = data.prior_tests_completed?.tsh;"}, {"key": "prior_tsh_value", "data": {"values": [{"label": "I don't remember my result", "value": "dont_remember"}, {"label": "Below 0.1 mIU/L (very low)", "value": "lt_0.1"}, {"label": "0.1 - 0.3 mIU/L (low)", "value": "0.1_0.3"}, {"label": "0.4 - 4.0 mIU/L (normal range)", "value": "0.4_4.0"}, {"label": "4.1 - 5.0 mIU/L (mildly elevated)", "value": "4.1_5.0"}, {"label": "5.1 - 7.5 mIU/L (borderline high)", "value": "5.1_7.5"}, {"label": "7.6 - 10 mIU/L (moderately high)", "value": "7.6_10"}, {"label": "Above 10 mIU/L (high, likely hypothyroidism)", "value": "gt_10"}]}, "type": "select", "input": true, "label": "What was your most recent TSH level?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "TSH Level:", "customConditional": "show = data.prior_tests_completed?.tsh;"}, {"key": "additional_information", "type": "textarea", "input": true, "label": "Please provide any additional details about your symptoms, medical history, or medications that you think are relevant.", "tableView": true, "autoExpand": false}, {"key": "intake_denial_reasons", "type": "textfield", "input": true, "label": "Intake Denial Reasons:", "hidden": true, "disabled": true, "multiple": true, "adminFlag": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "value = [];", "refreshOnChange": true}, {"key": "intake_kind", "type": "textfield", "input": true, "label": "Intake Kind", "hidden": true, "disabled": true, "tableView": true, "clearOnHide": false, "calculateValue": "value = !data.intake_denial_reasons?.length ? 'mail' : 'chat';"}, {"key": "intake_mt_key", "type": "textfield", "input": true, "label": "Intake Mail Template Key", "hidden": true, "disabled": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.intake_kind=='mail'?'intake-trt':'intake-denial'"}, {"key": "allowed_rt_keys", "type": "textfield", "input": true, "label": "Allowed Rep<PERSON>", "hidden": true, "disabled": true, "multiple": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.intake_mt_key=='intake-denial'?[]:['get-req','appointment-intake','edit-intake']"}]}