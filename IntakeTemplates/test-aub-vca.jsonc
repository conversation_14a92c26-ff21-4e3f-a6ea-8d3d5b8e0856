{"components": [{"key": "biological_sex_birth", "type": "radio", "input": true, "label": "What is your biolgoical sex (i.e. sex at birth)?", "inline": false, "values": [{"label": "Male", "value": "male", "shortcut": ""}, {"label": "Female", "value": "female", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "diagnosis_dysmenorrhea_menorrhagia", "type": "radio", "input": true, "label": "Are you experiencing painful menstraul cycles or heavy bleeding with your menstrual cycles?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.biological_sex_birth == 'female';", "optionsLabelPosition": "right"}, {"key": "symptom_duration", "type": "radio", "input": true, "label": "Have your symptoms been going on for more or less than 6 months?", "inline": false, "values": [{"label": "More than 6 months", "value": "chronic", "shortcut": ""}, {"label": "Less than 6 months", "value": "sub-acute", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.diagnosis_dysmenorrhea_menorrhagia == true;", "optionsLabelPosition": "right"}, {"key": "gynecological_cancer", "type": "radio", "input": true, "label": "Do you currently have a diagnosis of endometrial, ovarian or cervical cancer?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.diagnosis_dysmenorrhea_menorrhagia == 'yes';", "optionsLabelPosition": "right"}, {"key": "aub_contraindication", "label": "Are you currently experiencing any of the following symptoms:", "values": [{"label": "Chest pain or heaviness", "value": "chest_pain", "shortcut": ""}, {"label": "Shortness of breath", "value": "dyspnea", "shortcut": ""}, {"label": "Feel lightheaded or faint", "value": "presy<PERSON><PERSON>", "shortcut": ""}, {"label": "Arm or leg swelling", "value": "limb_swelling", "shortcut": ""}, {"label": "Heart palpitations (slow or fast heart beat)", "value": "palpitations", "shortcut": ""}], "input": true, "inputType": "checkbox", "customConditional": "show = data.biological_sex_birth == 'female' && data.diagnosis_dysmenorrhea_menorrhagia == true && data.gynecological_cancer == false;", "optionsLabelPosition": "right", "tableView": true, "type": "selectboxes"}, {"key": "no_gyne_contraindications", "customClass": "mt-n3", "defaultValue": false, "input": true, "label": "None of the above", "tableView": true, "type": "checkbox", "customConditional": "show = data.biological_sex_birth == 'female' && data.diagnosis_dysmenorrhea_menorrhagia == true && data.gynecological_cancer == false;", "validate": {"custom": "valid = !!data.no_gyne_contraindications || !!_.some(_.values(data.aub_contraindication));"}, "errors": {"custom": "required, or select a symptom."}}]}