{"components": [{"key": "cholesterol_header", "html": "<h2><strong>NMR Lipoprotein Testing (Advanced Lipid Panel)</strong></h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "cholesterol_consultation_indication", "type": "selectboxes", "input": true, "label": "Please select your reason(s) for this NMR / advanced lipid consultation:", "values": [{"label": "Renew my cholesterol medication (e.g., statin, ezetimibe, PCSK9 inhibitor)", "value": "renew_chol_medication", "shortcut": ""}, {"label": "Review or start new cholesterol treatment", "value": "new_chol_treatment", "shortcut": ""}, {"label": "Request NMR (advanced) lipid profile (e.g., LDL-P, small LDL-P, HDL-P, LP-IR)", "value": "nmr_testing_request", "shortcut": ""}, {"label": "Follow-up on abnormal cholesterol or triglyceride results", "value": "followup_abnormal_results", "shortcut": ""}, {"label": "Compare NMR with a prior standard lipid panel", "value": "compare_to_standard_panel", "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "confirm_label": "Reason(s) for NMR consultation:", "optionsLabelPosition": "right"}, {"key": "cholesterol_consultation_other", "type": "textarea", "input": true, "label": "Please state your other reason(s) for this NMR / advanced lipid consultation:", "tableView": true, "autoExpand": true, "confirm_label": "Other reason for NMR consultation:", "customConditional": "show = (data.cholesterol_consultation_indication && data.cholesterol_consultation_indication.other);"}, {"key": "heading_diagnosis", "html": "<h2><strong>Diagnosis</strong></h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "prior_cholesterol_diagnosis", "type": "radio", "input": true, "label": "Has a healthcare professional (doctor, nurse practitioner, cardiologist) ever told you that you have high cholesterol or a related blood fat problem?", "inline": false, "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "I'm not sure / I don't remember", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Told about cholesterol problem:", "optionsLabelPosition": "right"}, {"key": "cholesterol_diagnosis_age", "type": "number", "input": true, "label": "How old were you when you were first told about a cholesterol or blood fat problem?", "validate": {"max": 120, "min": 0, "required": true}, "tableView": true, "confirm_label": "Age when first told:", "customConditional": "show = (data.prior_cholesterol_diagnosis === 'yes');"}, {"key": "cholesterol_issue_type", "type": "selectboxes", "input": true, "label": "What specifically were you told about your cholesterol or blood fats? (Select all that apply)", "values": [{"label": "High LDL (often called 'bad' cholesterol)", "value": "high_ldl"}, {"label": "Low HDL (often called 'good' cholesterol)", "value": "low_hdl"}, {"label": "High triglycerides (a type of blood fat)", "value": "high_triglycerides"}, {"label": "High total cholesterol", "value": "high_total"}, {"label": "High Lipoprotein(a)", "value": "high_lpa"}, {"label": "High Apo-B (Apolipoprotein B)", "value": "high_apob"}, {"label": "Not sure / can't remember", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Cholesterol issue(s):", "customConditional": "show = (data.prior_cholesterol_diagnosis === 'yes');", "optionsLabelPosition": "right"}, {"key": "heading_personal_heart_history", "html": "<h2><strong>Your Heart and Blood Vessel Health</strong></h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "ascvd_history", "type": "selectboxes", "input": true, "label": "Have <strong>you</strong> ever had any of the following heart or blood vessel problems?", "values": [{"label": "Heart attack", "value": "mi"}, {"label": "<PERSON><PERSON> (chest pain from reduced blood flow to the heart)", "value": "angina"}, {"label": "Stent procedure (to open blocked heart arteries)", "value": "stent"}, {"label": "Heart bypass surgery (for blocked arteries)", "value": "bypass_surgery"}, {"label": "Stroke", "value": "stroke"}, {"label": "Mini-stroke (TIA)", "value": "tia"}, {"label": "Poor blood flow to the legs/feet (sometimes called peripheral artery disease or PAD)", "value": "pad"}], "tableView": true, "confirm_label": "Your heart/blood vessel problems:", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_ascvd_history", "type": "checkbox", "input": true, "label": "None of the above", "validate": {"custom": "valid = !!data.none_of_the_above_ascvd_history || _.some(_.values(data.ascvd_history || {}));", "customMessage": "Required: select at least one condition or choose “None of the above”."}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "ascvd_history_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following heart or blood vessel problems:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "Conditions you do not have:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.ascvd_history || {}, _.negate(_.identity))), _.startCase), ', '), /_/g, ' ');"}, {"key": "mi_heading", "html": "<h3><strong>Heart attack</strong></h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = !!(data.ascvd_history && data.ascvd_history.mi === true);"}, {"key": "mi_when_occurred", "data": {"values": [{"label": "In the last 3 months", "value": "lt_3mo"}, {"label": "3-12 months ago", "value": "3_12mo"}, {"label": "1-5 years ago", "value": "1_5y"}, {"label": "More than 5 years ago", "value": "gt_5y"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "When did your most recent heart attack happen?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Most recent heart attack:", "customConditional": "show = !!(data.ascvd_history && data.ascvd_history.mi === true);"}, {"key": "mi_number_events", "type": "radio", "input": true, "label": "How many heart attacks have you had?", "values": [{"label": "One", "value": "one"}, {"label": "More than one", "value": "multiple"}, {"label": "Not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Number of heart attacks:", "customConditional": "show = !!(data.ascvd_history && data.ascvd_history.mi === true);", "optionsLabelPosition": "right"}, {"key": "mi_severity", "type": "radio", "input": true, "label": "If you were told, how severe was your heart attack?", "values": [{"label": "Mild", "value": "mild"}, {"label": "Moderate", "value": "moderate"}, {"label": "Severe / major", "value": "severe"}, {"label": "Not sure / wasn't told", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Severity of heart attack:", "customConditional": "show = !!(data.ascvd_history && data.ascvd_history.mi === true);"}, {"key": "mi_type", "type": "radio", "input": true, "label": "If known, what type of heart attack did you have?", "values": [{"label": "STEMI (full blockage)", "value": "stemi"}, {"label": "NSTEMI (partial blockage)", "value": "nstemi"}, {"label": "Not sure / wasn't told", "value": "not_sure"}], "tableView": true, "confirm_label": "Type of heart attack:", "customConditional": "show = !!(data.ascvd_history && data.ascvd_history.mi === true);"}, {"key": "angina_heading", "html": "<h3><strong><PERSON><PERSON></strong></h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = !!(data.ascvd_history && data.ascvd_history.angina === true);"}, {"key": "angina_first_told", "data": {"values": [{"label": "In the last year", "value": "lt_1y"}, {"label": "1-5 years ago", "value": "1_5y"}, {"label": "More than 5 years ago", "value": "gt_5y"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "When were you first told you had angina?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "First told about angina:", "customConditional": "show = !!(data.ascvd_history && data.ascvd_history.angina === true);"}, {"key": "angina_ongoing", "type": "radio", "input": true, "label": "Do you still have angina, or were you told it has resolved?", "values": [{"label": "Yes, I still have it", "value": "ongoing"}, {"label": "No, I was told it has resolved", "value": "resolved"}, {"label": "Not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Angina status now:", "customConditional": "show = !!(data.ascvd_history && data.ascvd_history.angina === true);", "optionsLabelPosition": "right"}, {"key": "angina_symptoms", "type": "selectboxes", "input": true, "label": "When you were told you had angina, what symptoms did you have?", "values": [{"label": "Chest pain or pressure", "value": "chest_pain"}, {"label": "Pain in the arm, jaw, neck, or back", "value": "radiation"}, {"label": "Shortness of breath with activity", "value": "shortness_breath"}, {"label": "Other symptoms", "value": "other"}], "tableView": true, "confirm_label": "Angina symptoms noted:", "refreshOnChange": true, "customConditional": "show = !!(data.ascvd_history && data.ascvd_history.angina === true);", "optionsLabelPosition": "right"}, {"key": "angina_symptoms_other_text", "type": "textfield", "input": true, "label": "Please describe the other symptoms:", "tableView": true, "customConditional": "show = !!(data.ascvd_history && data.ascvd_history.angina === true && data.angina_symptoms && data.angina_symptoms.other === true);"}, {"key": "angina_frequency", "type": "radio", "input": true, "label": "How often do your angina symptoms occur now?", "values": [{"label": "Daily or almost daily", "value": "daily"}, {"label": "Weekly", "value": "weekly"}, {"label": "Monthly or less", "value": "monthly"}, {"label": "Haven't had symptoms for a long time", "value": "none_recent"}, {"label": "Not sure", "value": "not_sure"}], "tableView": true, "confirm_label": "Angina frequency:", "customConditional": "show = !!(data.ascvd_history && data.ascvd_history.angina === true);", "optionsLabelPosition": "right"}, {"key": "angina_stability_plain", "type": "radio", "input": true, "label": "Do your angina symptoms happen in a predictable way (like only with exercise or stress), or do they sometimes happen at rest?", "values": [{"label": "Predictable (only with exercise, stress, or heavy activity)", "value": "predictable"}, {"label": "Sometimes happen at rest or without warning", "value": "unpredictable"}, {"label": "Not sure", "value": "not_sure"}], "tableView": true, "confirm_label": "Pattern of angina symptoms:", "customConditional": "show = !!(data.ascvd_history && data.ascvd_history.angina === true);", "optionsLabelPosition": "right"}, {"key": "stent_heading", "html": "<h3><strong>Stent procedure</strong></h3><p>A stent is a small tube placed in a heart artery to keep it open.</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = !!(data.ascvd_history && data.ascvd_history.stent === true);"}, {"key": "stent_when", "data": {"values": [{"label": "Within the last 2 weeks", "value": "lt_2w"}, {"label": "2-4 weeks ago", "value": "2_4w"}, {"label": "1-3 months ago", "value": "1_3mo"}, {"label": "3-6 months ago", "value": "3_6mo"}, {"label": "6-12 months ago", "value": "6_12mo"}, {"label": "1-5 years ago", "value": "1_5y"}, {"label": "More than 5 years ago", "value": "gt_5y"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "When was your most recent stent procedure?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Most recent stent:", "customConditional": "show = !!(data.ascvd_history && data.ascvd_history.stent === true);"}, {"key": "stent_number", "type": "radio", "input": true, "label": "How many stents have you had placed (in total)?", "values": [{"label": "One", "value": "one"}, {"label": "Two", "value": "two"}, {"label": "Three or more", "value": "three_plus"}, {"label": "Not sure", "value": "not_sure"}], "tableView": true, "confirm_label": "Number of stents:", "customConditional": "show = !!(data.ascvd_history && data.ascvd_history.stent === true);"}, {"key": "stent_reason", "type": "radio", "input": true, "label": "Why was the stent procedure done (if you were told)?", "values": [{"label": "After a heart attack", "value": "after_mi"}, {"label": "For angina or chest pain", "value": "angina"}, {"label": "For another reason", "value": "other"}, {"label": "Not sure / don't remember", "value": "not_sure"}], "tableView": true, "confirm_label": "Reason for stent:", "customConditional": "show = !!(data.ascvd_history && data.ascvd_history.stent === true);"}, {"key": "bypass_heading", "html": "<h3><strong>Heart bypass surgery</strong></h3><p>Bypass surgery uses blood vessels to go around blocked heart arteries.</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = !!(data.ascvd_history && data.ascvd_history.bypass_surgery === true);"}, {"key": "bypass_when", "data": {"values": [{"label": "Within the last 2 weeks", "value": "lt_2w"}, {"label": "2-4 weeks ago", "value": "2_4w"}, {"label": "1-3 months ago", "value": "1_3mo"}, {"label": "3-6 months ago", "value": "3_6mo"}, {"label": "6-12 months ago", "value": "6_12mo"}, {"label": "1-5 years ago", "value": "1_5y"}, {"label": "More than 5 years ago", "value": "gt_5y"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "When was your most recent bypass surgery?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Most recent bypass:", "customConditional": "show = !!(data.ascvd_history && data.ascvd_history.bypass_surgery === true);"}, {"key": "bypass_grafts_number", "data": {"values": [{"label": "One (single bypass)", "value": "1"}, {"label": "Two (double bypass)", "value": "2"}, {"label": "Three (triple bypass)", "value": "3"}, {"label": "Four or more", "value": "4_plus"}, {"label": "Not sure / wasn't told", "value": "not_sure"}]}, "type": "select", "input": true, "label": "How many bypasses (grafts) did you have (if you were told)?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Number of grafts:", "customConditional": "show = !!(data.ascvd_history && data.ascvd_history.bypass_surgery === true);"}, {"key": "bypass_reason", "type": "radio", "input": true, "label": "Why was the bypass surgery done (if you were told)?", "values": [{"label": "After a heart attack", "value": "after_mi"}, {"label": "For chest pain/angina", "value": "angina"}, {"label": "For another reason", "value": "other"}, {"label": "Not sure / don't remember", "value": "not_sure"}], "tableView": true, "confirm_label": "Reason for bypass:", "customConditional": "show = !!(data.ascvd_history && data.ascvd_history.bypass_surgery === true);"}, {"key": "stroke_heading", "html": "<h3><strong>Stroke</strong></h3><p>A stroke happens when blood flow to part of the brain is blocked or reduced.</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = !!(data.ascvd_history && data.ascvd_history.stroke === true);"}, {"key": "stroke_when", "data": {"values": [{"label": "Within the last 2 weeks", "value": "lt_2w"}, {"label": "2-4 weeks ago", "value": "2_4w"}, {"label": "1-3 months ago", "value": "1_3mo"}, {"label": "3-6 months ago", "value": "3_6mo"}, {"label": "6-12 months ago", "value": "6_12mo"}, {"label": "1-5 years ago", "value": "1_5y"}, {"label": "More than 5 years ago", "value": "gt_5y"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "When did your most recent stroke happen?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Most recent stroke:", "customConditional": "show = !!(data.ascvd_history && data.ascvd_history.stroke === true);"}, {"key": "stroke_number", "type": "radio", "input": true, "label": "How many strokes have you had in total?", "values": [{"label": "One", "value": "one"}, {"label": "Two", "value": "two"}, {"label": "Three or more", "value": "three_plus"}, {"label": "Not sure", "value": "not_sure"}], "tableView": true, "confirm_label": "Number of strokes:", "customConditional": "show = !!(data.ascvd_history && data.ascvd_history.stroke === true);"}, {"key": "stroke_type", "type": "radio", "input": true, "label": "If you were told, what type of stroke did you have?", "values": [{"label": "Ischemic (caused by a clot)", "value": "ischemic"}, {"label": "Hemorrhagic (caused by bleeding)", "value": "hemorrhagic"}, {"label": "Not sure / wasn't told", "value": "not_sure"}], "tableView": true, "confirm_label": "Type of stroke:", "customConditional": "show = !!(data.ascvd_history && data.ascvd_history.stroke === true);"}, {"key": "tia_heading", "html": "<h3><strong>Mini-stroke (TIA)</strong></h3><p>A TIA is a short-lasting stroke-like event that gets better within a day.</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = !!(data.ascvd_history && data.ascvd_history.tia === true);"}, {"key": "tia_when", "data": {"values": [{"label": "Within the last 2 weeks", "value": "lt_2w"}, {"label": "2-4 weeks ago", "value": "2_4w"}, {"label": "1-3 months ago", "value": "1_3mo"}, {"label": "3-6 months ago", "value": "3_6mo"}, {"label": "6-12 months ago", "value": "6_12mo"}, {"label": "1-5 years ago", "value": "1_5y"}, {"label": "More than 5 years ago", "value": "gt_5y"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "When did your most recent TIA happen?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Most recent TIA:", "customConditional": "show = !!(data.ascvd_history && data.ascvd_history.tia === true);"}, {"key": "tia_number", "type": "radio", "input": true, "label": "How many TIAs have you had in total?", "values": [{"label": "One", "value": "one"}, {"label": "Two", "value": "two"}, {"label": "Three or more", "value": "three_plus"}, {"label": "Not sure", "value": "not_sure"}], "tableView": true, "confirm_label": "Number of TIAs:", "customConditional": "show = !!(data.ascvd_history && data.ascvd_history.tia === true);"}, {"key": "tia_confirmed", "type": "radio", "input": true, "label": "Were you told by a doctor or clinic that this was a TIA?", "values": [{"label": "Yes, confirmed as a TIA", "value": "confirmed"}, {"label": "Told it might be a TIA (not sure)", "value": "possible"}, {"label": "Not sure / wasn't told", "value": "not_sure"}], "tableView": true, "confirm_label": "TIA confirmation:", "customConditional": "show = !!(data.ascvd_history && data.ascvd_history.tia === true);"}, {"key": "pad_heading", "html": "<h3><strong>Peripheral artery disease (PAD)</strong></h3><p>PAD means reduced blood flow to the legs or feet because of narrowed arteries.</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = !!(data.ascvd_history && data.ascvd_history.pad === true);"}, {"key": "pad_when", "data": {"values": [{"label": "Within the last 2 weeks", "value": "lt_2w"}, {"label": "2-4 weeks ago", "value": "2_4w"}, {"label": "1-3 months ago", "value": "1_3mo"}, {"label": "3-6 months ago", "value": "3_6mo"}, {"label": "6-12 months ago", "value": "6_12mo"}, {"label": "1-5 years ago", "value": "1_5y"}, {"label": "More than 5 years ago", "value": "gt_5y"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "When were you most recently told you had PAD?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Most recent PAD diagnosis:", "customConditional": "show = !!(data.ascvd_history && data.ascvd_history.pad === true);"}, {"key": "pad_location", "type": "selectboxes", "input": true, "label": "Which areas have been affected by PAD? (Select all that apply)", "values": [{"label": "Left leg", "value": "left_leg"}, {"label": "Right leg", "value": "right_leg"}, {"label": "Both legs", "value": "both_legs"}, {"label": "Feet or toes", "value": "feet_toes"}], "tableView": true, "confirm_label": "PAD location(s):", "customConditional": "show = !!(data.ascvd_history && data.ascvd_history.pad === true);", "optionsLabelPosition": "right"}, {"key": "pad_procedures", "type": "selectboxes", "input": true, "label": "Have you had any procedures for PAD (if you were told)?", "values": [{"label": "Angioplasty or stent in the leg", "value": "angioplasty_stent"}, {"label": "Bypass surgery in the leg", "value": "bypass_leg"}, {"label": "None / not sure", "value": "none_or_unsure"}], "tableView": true, "confirm_label": "PAD procedures:", "customConditional": "show = !!(data.ascvd_history && data.ascvd_history.pad === true);", "optionsLabelPosition": "right"}, {"key": "ascvd_any_selected", "type": "textfield", "input": true, "label": "ASCVD any selected (helper)", "hidden": true, "disabled": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = !!(data.ascvd_history && (data.ascvd_history.mi || data.ascvd_history.angina || data.ascvd_history.stent || data.ascvd_history.bypass_surgery || data.ascvd_history.stroke || data.ascvd_history.tia || data.ascvd_history.pad));"}, {"key": "specialist_followup_heading", "html": "<h3><strong>Specialist follow-up</strong></h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = (data.ascvd_any_selected === true || data.ascvd_any_selected === 'true');"}, {"key": "ascvd_specialist_followup_status", "type": "radio", "input": true, "label": "Do you currently see a specialist for any of the conditions you selected?", "values": [{"label": "Yes, regularly", "value": "regular"}, {"label": "Yes, once in a while", "value": "occasional"}, {"label": "No", "value": "none"}, {"label": "Not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Specialist follow-up status:", "refreshOnChange": true, "customConditional": "show = (data.ascvd_any_selected === true || data.ascvd_any_selected === 'true');"}, {"key": "ascvd_specialist_types", "type": "selectboxes", "input": true, "label": "Which specialist(s) do you see? (Select all that apply)", "values": [{"label": "Cardiologist (heart specialist)", "value": "cardiologist"}, {"label": "Neurologist / stroke clinic", "value": "neurologist_stroke"}, {"label": "Vascular surgeon / vascular clinic", "value": "vascular"}], "validate": {"custom": "valid = (data.ascvd_specialist_followup_status === 'regular' || data.ascvd_specialist_followup_status === 'occasional') ? _.some(_.values(input || {})) : true;", "customMessage": "Select at least one specialist type."}, "tableView": true, "confirm_label": "Specialist types:", "customConditional": "show = (data.ascvd_any_selected === true || data.ascvd_any_selected === 'true') && (data.ascvd_specialist_followup_status === 'regular' || data.ascvd_specialist_followup_status === 'occasional');", "optionsLabelPosition": "right"}, {"key": "ascvd_next_followup", "data": {"values": [{"label": "Within the next week", "value": "lt_1w"}, {"label": "In 1-2 weeks", "value": "1_2w"}, {"label": "In 2-4 weeks", "value": "2_4w"}, {"label": "In 1-3 months", "value": "1_3mo"}, {"label": "In 3-6 months", "value": "3_6mo"}, {"label": "In 6-12 months", "value": "6_12mo"}, {"label": "More than 12 months from now", "value": "gt_12mo"}, {"label": "Not scheduled / don't know", "value": "not_scheduled"}]}, "type": "select", "input": true, "label": "When is your next specialist appointment (if known)?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Next specialist appointment:", "customConditional": "show = (data.ascvd_any_selected === true || data.ascvd_any_selected === 'true') && (data.ascvd_specialist_followup_status === 'regular' || data.ascvd_specialist_followup_status === 'occasional');"}, {"key": "heading_family_history", "html": "<h2><strong>Family History</strong></h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "family_history_premature_ascvd", "type": "radio", "input": true, "label": "Has anyone in your family had heart disease or stroke at a young age? (Men <55, Women <65)", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "I'm not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Family history of early heart problems:"}, {"key": "family_members_with_early_ascvd", "type": "selectboxes", "input": true, "label": "Which family member(s)?", "values": [{"label": "Father", "value": "father"}, {"label": "Mother", "value": "mother"}, {"label": "Brother", "value": "brother"}, {"label": "Sister", "value": "sister"}, {"label": "Son", "value": "son"}, {"label": "Daughter", "value": "daughter"}, {"label": "Other close relative", "value": "other"}], "tableView": true, "confirm_label": "Relatives with early heart problems:", "customConditional": "show = (data.family_history_premature_ascvd === 'yes');"}, {"key": "heading_family_early_ascvd_details", "html": "<h3><strong>Family member details</strong></h3><p>Add the condition and the age when it happened. If you don't know the age, enter <strong>00</strong>.</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = _.some(_.values(data.family_members_with_early_ascvd || {}));"}, {"key": "family_early_ascvd_details", "type": "datagrid", "input": true, "label": "Relatives with early heart/blood vessel problems", "validate": {"custom": "valid = !_.some(_.values(data.family_members_with_early_ascvd || {})) || (input && input.length > 0);", "customMessage": "Please add at least one relative (or unselect family members above)."}, "tableView": true, "addAnother": "Add another relative", "components": [{"key": "relative", "data": {"values": [{"label": "Father", "value": "father"}, {"label": "Mother", "value": "mother"}, {"label": "Brother", "value": "brother"}, {"label": "Sister", "value": "sister"}, {"label": "Son", "value": "son"}, {"label": "Daughter", "value": "daughter"}, {"label": "Other close relative", "value": "other"}]}, "type": "select", "input": true, "label": "Relative", "widget": "html5", "validate": {"custom": "valid = !!(data.family_members_with_early_ascvd && data.family_members_with_early_ascvd[input]);", "required": true, "customMessage": "Choose a relative that you selected above."}, "tableView": true, "description": "Pick one of the relatives you selected above."}, {"key": "condition", "data": {"values": [{"label": "Heart attack", "value": "mi"}, {"label": "<PERSON><PERSON> (chest pain from reduced blood flow to the heart)", "value": "angina"}, {"label": "Stent procedure (to open blocked heart arteries)", "value": "stent"}, {"label": "Heart bypass surgery (for blocked arteries)", "value": "bypass_surgery"}, {"label": "Stroke", "value": "stroke"}, {"label": "Mini-stroke (TIA)", "value": "tia"}, {"label": "Poor blood flow to the legs/feet (peripheral artery disease / PAD)", "value": "pad"}]}, "type": "select", "input": true, "label": "Condition", "widget": "html5", "validate": {"required": true}, "tableView": true}, {"key": "age_at_diagnosis", "type": "number", "input": true, "label": "Age at diagnosis/event (enter 00 if unknown)", "validate": {"max": 120, "min": 0, "required": true}, "tableView": true, "placeholder": "e.g., 52 or 00"}], "confirm_label": "Family early heart problems details:", "openWhenEmpty": true, "customConditional": "show = _.some(_.values(data.family_members_with_early_ascvd || {}));"}, {"key": "family_history_lipid_markers", "type": "radio", "input": true, "label": "Has anyone in your family been told they have cholesterol problems or related markers?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "I'm not sure", "value": "not_sure"}], "tableView": true, "clearOnHide": true, "confirm_label": "Family cholesterol/marker history:", "customConditional": "show = !( (data.family_history_premature_ascvd === 'yes') || _.some(_.values(data.family_members_with_early_ascvd || {})) || (Array.isArray(data.family_early_ascvd_details) && data.family_early_ascvd_details.length > 0) );"}, {"key": "family_members_with_lipid_markers", "type": "selectboxes", "input": true, "label": "Which family member(s)?", "values": [{"label": "Father", "value": "father"}, {"label": "Mother", "value": "mother"}, {"label": "Brother", "value": "brother"}, {"label": "Sister", "value": "sister"}, {"label": "Son", "value": "son"}, {"label": "Daughter", "value": "daughter"}, {"label": "Other close relative", "value": "other"}], "tableView": true, "confirm_label": "Relatives with cholesterol/marker issues:", "customConditional": "show = (data.family_history_lipid_markers === 'yes');", "optionsLabelPosition": "right"}, {"key": "heading_family_lipid_marker_details", "html": "<h3><strong>Family cholesterol/marker details</strong></h3><p>Add the age when it was found. If you don't know the age, enter <strong>00</strong>.</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = _.some(_.values(data.family_members_with_lipid_markers || {}));"}, {"key": "family_lipid_marker_details", "type": "datagrid", "input": true, "label": "Relatives with cholesterol/marker issues", "validate": {"custom": "valid = !_.some(_.values(data.family_members_with_lipid_markers || {})) || (input && input.length > 0);", "customMessage": "Please add at least one relative (or unselect family members above)."}, "tableView": true, "addAnother": "Add another relative", "components": [{"key": "relative", "data": {"values": [{"label": "Father", "value": "father"}, {"label": "Mother", "value": "mother"}, {"label": "Brother", "value": "brother"}, {"label": "Sister", "value": "sister"}, {"label": "Son", "value": "son"}, {"label": "Daughter", "value": "daughter"}, {"label": "Other close relative", "value": "other"}]}, "type": "select", "input": true, "label": "Relative", "widget": "html5", "validate": {"custom": "valid = !!(data.family_members_with_lipid_markers && data.family_members_with_lipid_markers[input]);", "required": true, "customMessage": "Choose a relative that you selected above."}, "tableView": true, "description": "Pick one of the relatives you selected above."}, {"key": "age_at_diagnosis", "type": "number", "input": true, "label": "Age at diagnosis/findings (enter 00 if unknown)", "validate": {"max": 120, "min": 0, "required": true}, "tableView": true, "placeholder": "e.g., 52 or 00"}], "confirm_label": "Family cholesterol/marker details:", "openWhenEmpty": true, "customConditional": "show = _.some(_.values(data.family_members_with_lipid_markers || {}));"}, {"key": "heading_genetic_testing", "html": "<h2><strong>Genetic Testing</strong></h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "prior_genetic_testing", "type": "radio", "input": true, "label": "Have you ever had <strong>genetic testing</strong> related to cholesterol or blood fats?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "I'm not sure / I don't remember", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Prior genetic testing:"}, {"key": "genetic_test_types", "type": "selectboxes", "input": true, "label": "Which test(s) did you have? (Select all that apply)", "values": [{"label": "LDLR gene test", "value": "ldlr"}, {"label": "APOB gene test", "value": "apob"}, {"label": "PCSK9 gene test", "value": "pcsk9"}, {"label": "LDLRAP1 gene test", "value": "ldlrap1"}, {"label": "Panel for familial hypercholesterolemia (FH)", "value": "fh_panel"}, {"label": "Genetic test for Lipoprotein(a) [Lp(a)]", "value": "lpa_genetic"}, {"label": "I'm not sure which test", "value": "unknown_test"}, {"label": "Other genetic test", "value": "other"}], "tableView": true, "confirm_label": "Genetic test(s):", "customConditional": "show = (data.prior_genetic_testing === 'yes');", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_genetic_tests", "type": "checkbox", "input": true, "label": "None of the above", "validate": {"custom": "valid = (data.prior_genetic_testing !== 'yes') || !!data.none_of_the_above_genetic_tests || _.some(_.values(data.genetic_test_types || {}));", "customMessage": "Required: select at least one test or choose “None of the above”."}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = (data.prior_genetic_testing === 'yes');"}, {"key": "genetic_test_other_text", "type": "textfield", "input": true, "label": "Please specify the other genetic test:", "tableView": true, "customConditional": "show = (data.prior_genetic_testing === 'yes') && (data.genetic_test_types && data.genetic_test_types.other === true);"}, {"key": "heading_symptoms_intro", "html": "</br><h2><strong>Symptoms</strong></h2><p>The following questions ask about any current symptoms you may have that are related to your heart or circulation.</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "heading_heart_health", "html": "</br><h4>Heart Health</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "cardiovascular_symptoms", "type": "selectboxes", "input": true, "label": "Do you have any of the following cardiovascular symptoms?", "values": [{"label": "Chest pain, tightness or discomfort", "value": "chest_pain"}, {"label": "Palpitations", "value": "palpitations"}, {"label": "Swelling in the legs, ankles, or feet", "value": "swelling"}, {"label": "Dizziness or fainting", "value": "dizziness"}], "adminFlag": true, "tableView": true, "confirm_label": "Cardiovascular Symptoms:", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_cardiovascular_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = !!data.none_of_the_above_cardiovascular_symptoms || _.some(_.values(data.cardiovascular_symptoms));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "cardiac_symptoms_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following cardiac symptoms:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "Heart related symptoms", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.cardiovascular_symptoms, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "heading_chest_pain", "html": "</br><h4>Chest Pain</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.chest_pain === true;"}, {"key": "chest_pain_onset", "data": {"values": [{"label": "Today", "value": "today"}, {"label": "1 day ago", "value": "1_day"}, {"label": "2 days ago", "value": "2_days"}, {"label": "3 days ago", "value": "3_days"}, {"label": "4 days ago", "value": "4_days"}, {"label": "5 days ago", "value": "5_days"}, {"label": "6 days ago", "value": "6_days"}, {"label": "7 days ago", "value": "7_days"}, {"label": "1-2 weeks ago", "value": "1_2_weeks"}, {"label": "2-4 weeks ago", "value": "2_4_weeks"}, {"label": "1-3 months ago", "value": "1_3_months"}, {"label": "More than 3 months ago", "value": "3_plus_months"}]}, "type": "select", "input": true, "label": "When did the chest pain or discomfort start?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Chest Pain Onset:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.chest_pain === true;"}, {"key": "chest_pain_triggers", "type": "selectboxes", "input": true, "label": "What brings the chest pain on or makes it worse?", "values": [{"label": "Physical activity or exertion", "value": "exertion"}, {"label": "Stress or anxiety", "value": "stress"}, {"label": "Eating a heavy meal", "value": "meal"}, {"label": "Lying down", "value": "lying"}, {"label": "Breathing deeply or coughing", "value": "breathing"}, {"label": "Unknown or unpredictable", "value": "unknown"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Chest Pain Triggers:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.chest_pain === true;"}, {"key": "chest_pain_relievers", "type": "selectboxes", "input": true, "label": "What makes the chest pain better?", "values": [{"label": "Rest", "value": "rest"}, {"label": "Lying down", "value": "lying"}, {"label": "Standing upright", "value": "standing"}, {"label": "Medication (e.g., nitroglycerin, pain relievers)", "value": "medication"}, {"label": "Nothing helps", "value": "nothing"}, {"label": "Unsure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Chest Pain Relievers:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.chest_pain === true;"}, {"key": "chest_pain_character", "type": "radio", "input": true, "label": "How would you describe the pain?", "values": [{"label": "<PERSON>", "value": "sharp"}, {"label": "Dull/aching", "value": "dull"}, {"label": "Tight/pressure-like", "value": "pressure"}, {"label": "Burning", "value": "burning"}, {"label": "Stabbing", "value": "stabbing"}, {"label": "Other", "value": "other"}], "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.chest_pain === true;"}, {"key": "chest_pain_location", "type": "selectboxes", "input": true, "label": "Where is the pain located?", "values": [{"label": "Centre of chest", "value": "centre_chest"}, {"label": "Left side of chest", "value": "left_chest"}, {"label": "Right side of chest", "value": "right_chest"}, {"label": "Upper chest or sternum", "value": "upper_chest"}, {"label": "Other", "value": "other"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Chest Pain Location:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.chest_pain === true;"}, {"key": "chest_pain_radiation", "type": "selectboxes", "input": true, "label": "Does the pain radiate to any of the following areas?", "values": [{"label": "Left arm", "value": "left_arm"}, {"label": "Right arm", "value": "right_arm"}, {"label": "<PERSON><PERSON>", "value": "jaw"}, {"label": "Neck", "value": "neck"}, {"label": "Back", "value": "back"}, {"label": "No radiation", "value": "none"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Chest Pain Radiation:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.chest_pain === true;"}, {"key": "chest_pain_pattern", "type": "radio", "input": true, "label": "Is the chest pain constant or does it come and go?", "values": [{"label": "Constant", "value": "constant"}, {"label": "Comes and goes (intermittent)", "value": "intermittent"}, {"label": "Unsure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Chest Pain Pattern:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.chest_pain === true;"}, {"key": "heading_palpitations", "html": "</br><h4>Palpitations</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.palpitations === true;"}, {"key": "palpitations_onset", "data": {"values": [{"label": "Today", "value": "today"}, {"label": "1 day ago", "value": "1_day"}, {"label": "2-3 days ago", "value": "2_3_days"}, {"label": "4-7 days ago", "value": "4_7_days"}, {"label": "1-2 weeks ago", "value": "1_2_weeks"}, {"label": "2-4 weeks ago", "value": "2_4_weeks"}, {"label": "More than a month ago", "value": "1_month_plus"}]}, "type": "select", "input": true, "label": "When did your palpitations begin?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Palpitations Onset:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.palpitations === true;"}, {"key": "palpitations_rhythm", "type": "radio", "input": true, "label": "How would you describe the rhythm of your palpitations?", "values": [{"label": "Regular and fast", "value": "regular_fast"}, {"label": "Irregular and fast", "value": "irregular_fast"}, {"label": "Skipped beats", "value": "skipped_beats"}, {"label": "Flutters", "value": "flutters"}, {"label": "Other", "value": "other"}], "tableView": true, "confirm_label": "Palpitations Rhythm:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.palpitations === true;"}, {"key": "palpitations_triggers", "type": "selectboxes", "input": true, "label": "What triggers the palpitations?", "values": [{"label": "Exercise or physical activity", "value": "exercise"}, {"label": "Stress or anxiety", "value": "stress"}, {"label": "Caffeine or stimulants", "value": "caffeine"}, {"label": "Alcohol", "value": "alcohol"}, {"label": "Occur at rest", "value": "rest"}, {"label": "Occur at night", "value": "night"}, {"label": "No clear trigger", "value": "unknown"}], "tableView": true, "confirm_label": "Palpitations Triggers:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.palpitations === true;"}, {"key": "palpitations_duration", "data": {"values": [{"label": "A few seconds", "value": "seconds"}, {"label": "Less than 5 minutes", "value": "less_5_min"}, {"label": "5-30 minutes", "value": "5_30_min"}, {"label": "30 minutes to a few hours", "value": "30min_hours"}, {"label": "More than a few hours", "value": "many_hours"}, {"label": "Constant", "value": "constant"}, {"label": "Varies", "value": "varies"}]}, "type": "select", "input": true, "label": "How long do the palpitations usually last?", "widget": "html5", "tableView": true, "confirm_label": "Palpitations Duration:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.palpitations === true;"}, {"key": "palpitations_associated_symptoms", "type": "selectboxes", "input": true, "label": "Do you experience any of the following during palpitations?", "values": [{"label": "Dizziness or lightheadedness", "value": "dizziness"}, {"label": "Shortness of breath", "value": "sob"}, {"label": "Chest pain", "value": "chest_pain"}, {"label": "Sweating", "value": "sweating"}, {"label": "Fainting or near-fainting", "value": "fainting"}, {"label": "No other symptoms", "value": "none"}], "tableView": true, "confirm_label": "Palpitations Associated Symptoms:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.palpitations === true;"}, {"key": "heading_swelling", "html": "</br><h4>Swelling</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.swelling === true;"}, {"key": "swelling_location", "type": "selectboxes", "input": true, "label": "Where is the swelling located?", "values": [{"label": "Feet", "value": "feet"}, {"label": "<PERSON><PERSON>", "value": "ankles"}, {"label": "Lower legs", "value": "lower_legs"}, {"label": "Thighs", "value": "thighs"}, {"label": "Abdomen", "value": "abdomen"}, {"label": "Other", "value": "other"}], "tableView": true, "confirm_label": "Swelling Location:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.swelling === true;"}, {"key": "swelling_sidedness", "type": "radio", "input": true, "label": "Is the swelling on one side or both sides?", "values": [{"label": "Both sides", "value": "bilateral"}, {"label": "One side only", "value": "unilateral"}, {"label": "Varies", "value": "varies"}, {"label": "Unsure", "value": "unsure"}], "tableView": true, "confirm_label": "Swelling Sidedness:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.swelling === true;"}, {"key": "swelling_onset", "data": {"values": [{"label": "Today", "value": "today"}, {"label": "1 day ago", "value": "1_day"}, {"label": "2-3 days ago", "value": "2_3_days"}, {"label": "4-7 days ago", "value": "4_7_days"}, {"label": "1-2 weeks ago", "value": "1_2_weeks"}, {"label": "More than 2 weeks ago", "value": "2_plus_weeks"}]}, "type": "select", "input": true, "label": "When did the swelling begin?", "widget": "html5", "tableView": true, "confirm_label": "Swelling Onset:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.swelling === true;"}, {"key": "swelling_timing", "type": "radio", "input": true, "label": "When is the swelling most noticeable?", "values": [{"label": "By the end of the day", "value": "evening"}, {"label": "In the morning", "value": "morning"}, {"label": "Constant throughout the day", "value": "constant"}, {"label": "Varies day to day", "value": "varies"}, {"label": "Unsure", "value": "unsure"}], "tableView": true, "confirm_label": "Swelling Timing:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.swelling === true;"}, {"key": "swelling_pitting", "type": "radio", "input": true, "label": "When you press on the swollen area, does it leave a dent (pitting)?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "unsure"}], "tableView": true, "confirm_label": "Swelling Pitting:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.swelling === true;"}, {"key": "heading_dizziness", "html": "</br><h4>Dizziness or Fainting</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.dizziness === true;"}, {"key": "dizziness_onset", "data": {"values": [{"label": "Today", "value": "today"}, {"label": "1 day ago", "value": "1_day"}, {"label": "2-3 days ago", "value": "2_3_days"}, {"label": "4-7 days ago", "value": "4_7_days"}, {"label": "1-2 weeks ago", "value": "1_2_weeks"}, {"label": "More than 2 weeks ago", "value": "2_plus_weeks"}]}, "type": "select", "input": true, "label": "When did the dizziness or fainting episodes begin?", "widget": "html5", "tableView": true, "confirm_label": "Dizziness Onset:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.dizziness === true;"}, {"key": "dizziness_frequency", "type": "radio", "input": true, "label": "How often do you experience dizziness or fainting?", "values": [{"label": "Once", "value": "once"}, {"label": "Occasionally (less than once a week)", "value": "occasional"}, {"label": "Frequently (once or more per week)", "value": "frequent"}, {"label": "Daily or almost daily", "value": "daily"}, {"label": "Unsure", "value": "unsure"}], "tableView": true, "confirm_label": "Dizziness Frequency:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.dizziness === true;"}, {"key": "dizziness_character", "type": "radio", "input": true, "label": "How would you describe the dizziness?", "values": [{"label": "Lightheadedness or feeling faint", "value": "lightheaded"}, {"label": "Spinning or vertigo", "value": "spinning"}, {"label": "Unsteady or off balance", "value": "unsteady"}, {"label": "Hard to describe", "value": "hard_to_describe"}], "tableView": true, "confirm_label": "Dizziness Character:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.dizziness === true;"}, {"key": "dizziness_timing", "type": "selectboxes", "input": true, "label": "When does the dizziness or fainting usually happen?", "values": [{"label": "After standing up", "value": "standing_up"}, {"label": "After exertion or exercise", "value": "exertion"}, {"label": "At rest", "value": "rest"}, {"label": "With dehydration or hunger", "value": "dehydration"}, {"label": "Without a clear trigger", "value": "unknown"}], "tableView": true, "confirm_label": "Dizziness Timing:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.dizziness === true;"}, {"key": "fainting_loss_consciousness", "type": "radio", "input": true, "label": "Have you ever fully lost consciousness during one of these episodes?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Unsure", "value": "unsure"}], "tableView": true, "confirm_label": "Fainting or Loss of Consciousness:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.dizziness === true;"}, {"key": "heading_lung_health", "html": "</br><h4>Lung Health</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "respiratory_symptoms", "type": "selectboxes", "input": true, "label": "Do you have any of the following respiratory symptoms?", "values": [{"label": "<PERSON><PERSON>", "value": "cough"}, {"label": "Shortness of breath", "value": "shortness_of_breath"}, {"label": "Wheezing", "value": "wheezing"}], "adminFlag": true, "tableView": true, "confirm_label": "Respiratory Symptoms:", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_respiratory_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = !!data.none_of_the_above_respiratory_symptoms || _.some(_.values(data.respiratory_symptoms));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "respiratory_symptoms_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following respiratory symptoms:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "Respiratory symptoms", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.respiratory_symptoms, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "heading_cough", "html": "</br><h4>Cough</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.respiratory_symptoms && data.respiratory_symptoms.cough === true;"}, {"key": "cough_type", "type": "radio", "input": true, "label": "Is your cough dry or productive?", "values": [{"label": "Dry (no phlegm)", "value": "dry"}, {"label": "Productive (with phlegm)", "value": "productive"}, {"label": "Varies", "value": "varies"}, {"label": "Unsure", "value": "unsure"}], "tableView": true, "confirm_label": "Cough Type:", "customConditional": "show = data.respiratory_symptoms && data.respiratory_symptoms.cough === true;"}, {"key": "cough_duration", "data": {"values": [{"label": "Less than 1 week", "value": "lt_1wk"}, {"label": "1-2 weeks", "value": "1_2wk"}, {"label": "2-4 weeks", "value": "2_4wk"}, {"label": "More than 4 weeks", "value": "gt_4wk"}]}, "type": "select", "input": true, "label": "How long have you had the cough?", "widget": "html5", "tableView": true, "confirm_label": "Cough duration", "customConditional": "show = data.respiratory_symptoms && data.respiratory_symptoms.cough === true;"}, {"key": "cough_coughing_blood", "type": "radio", "input": true, "label": "Have you noticed any blood when coughing?", "values": [{"label": "Yes - bright red blood", "value": "bright_red"}, {"label": "Yes - dark or coffee ground appearance", "value": "coffee_ground"}, {"label": "I think so - unsure of colour or source", "value": "unsure_appearance"}, {"label": "No", "value": "no"}], "tableView": true, "description": "This includes coughing up blood that appears bright red or looks like coffee grounds (which may suggest bleeding in the lungs or stomach).", "confirm_label": "Coughing up blood:", "customConditional": "show = data.respiratory_symptoms && data.respiratory_symptoms.cough === true;"}, {"key": "cough_feeling_unwell", "type": "radio", "input": true, "label": "Do you feel generally unwell with your cough (e.g. fatigue, fever, weakness)?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "unsure"}], "tableView": true, "confirm_label": "Feeling unwell with cough:", "customConditional": "show = data.respiratory_symptoms && data.respiratory_symptoms.cough === true;"}, {"key": "cough_with_cold", "type": "radio", "input": true, "label": "Did your cough begin at the same time as a cold or viral illness (e.g. sore throat, congestion, fever)?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "unsure"}], "tableView": true, "confirm_label": "Cough with cold:", "customConditional": "show = data.respiratory_symptoms && data.respiratory_symptoms.cough === true;"}, {"key": "cough_progression", "type": "radio", "input": true, "label": "Is your cough improving, getting worse, or staying the same?", "values": [{"label": "Improving", "value": "improving"}, {"label": "Getting worse", "value": "worsening"}, {"label": "No change", "value": "no_change"}], "tableView": true, "confirm_label": "Cough Progression:", "customConditional": "show = data.cough_with_cold === 'yes';"}, {"key": "cough_urgent_warning", "html": "<div style='border-left: 4px solid #ffc107; background-color: #fff3cd; padding: 10px; margin-bottom: 10px;'><strong>Warning:</strong> If you are experiencing any of the following — worsening cough, shortness of breath, coughing up blood, feeling generally unwell, or if your cough has lasted more than 4 weeks — we advise seeking same-day care in an emergency department. These may be signs of a more serious condition that should not be delayed.</div>", "type": "content", "input": false, "label": "Content", "customConditional": "show = (data.cough_progression === 'worsening') || (data.respiratory_symptoms && data.respiratory_symptoms.shortness_of_breath === true) || (data.cough_coughing_blood === true) || (data.cough_feeling_unwell === true) || (data.cough_duration === 'gt_4wk');"}, {"key": "cough_urgent_warning_understanding", "type": "radio", "input": true, "label": "Do you understand this warning about when to seek emergency care?", "values": [{"label": "I understand", "value": "understand"}, {"label": "I do not understand", "value": "do_not_understand"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Understands Emergency Care Warning:", "customConditional": "show = (data.cough_progression === 'worsening') || (data.respiratory_symptoms && data.respiratory_symptoms.shortness_of_breath === true) || (data.cough_coughing_blood === true) || (data.cough_feeling_unwell === true) || (data.cough_duration === 'gt_4wk');"}, {"key": "heading_shortness_of_breath", "html": "</br><h4>Shortness of Breath</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.respiratory_symptoms && data.respiratory_symptoms.shortness_of_breath === true;"}, {"key": "sob_triggers", "type": "selectboxes", "input": true, "label": "When do you typically feel short of breath?", "values": [{"label": "At rest", "value": "rest"}, {"label": "With mild activity (e.g. walking)", "value": "mild_activity"}, {"label": "With moderate or strenuous activity", "value": "exercise"}, {"label": "While lying flat", "value": "lying_flat"}, {"label": "At night (waking from sleep)", "value": "nocturnal"}, {"label": "Unsure", "value": "unsure"}], "tableView": true, "confirm_label": "Shortness of Breath Triggers:", "customConditional": "show = data.respiratory_symptoms && data.respiratory_symptoms.shortness_of_breath === true;"}, {"key": "heading_wheezing", "html": "</br><h4>Wheezing</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.respiratory_symptoms && data.respiratory_symptoms.wheezing === true;"}, {"key": "wheezing_timing", "type": "selectboxes", "input": true, "label": "When is the wheezing most noticeable?", "values": [{"label": "During exercise", "value": "exercise"}, {"label": "At rest", "value": "rest"}, {"label": "At night", "value": "night"}, {"label": "In cold weather", "value": "cold_weather"}, {"label": "When lying down", "value": "lying_down"}, {"label": "When exposed to irritants (e.g. smoke, dust)", "value": "irritants"}], "tableView": true, "confirm_label": "Wheezing Timing:", "customConditional": "show = data.respiratory_symptoms && data.respiratory_symptoms.wheezing === true;"}, {"key": "wheezing_relief", "type": "radio", "input": true, "label": "Do you use any medications to relieve wheezing?", "values": [{"label": "Yes - inhaler or nebulizer", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Occasionally", "value": "occasional"}], "tableView": true, "confirm_label": "Wheezing Relief:", "customConditional": "show = data.respiratory_symptoms && data.respiratory_symptoms.wheezing === true;"}, {"key": "wheezing_asthma_history", "type": "radio", "input": true, "label": "Have you ever been diagnosed with asthma (currently or in the past)?", "values": [{"label": "Yes - currently diagnosed", "value": "current"}, {"label": "Yes - past diagnosis only", "value": "past"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "unsure"}], "tableView": true, "customConditional": "show = data.respiratory_symptoms && data.respiratory_symptoms.wheezing === true;"}, {"key": "intake_kind", "type": "textfield", "input": true, "label": "Intake Kind", "hidden": true, "disabled": true, "tableView": true, "clearOnHide": false, "calculateValue": "value = !(data.intake_denial_reasons && data.intake_denial_reasons.length) ? 'mail' : 'chat';"}, {"key": "intake_mt_key", "type": "textfield", "input": true, "label": "Intake Mail Template Key", "hidden": true, "disabled": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.intake_kind=='mail'?'intake-nmr':'intake-denial'"}, {"key": "allowed_rt_keys", "type": "textfield", "input": true, "label": "Allowed Rep<PERSON>", "hidden": true, "disabled": true, "multiple": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.intake_mt_key=='intake-denial'?[]:['get-req','appointment-intake','edit-intake']"}]}