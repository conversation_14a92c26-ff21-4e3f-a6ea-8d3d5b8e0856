{"components": [{"key": "dukoral_pregnancy", "type": "radio", "input": true, "label": "Are you currently pregnant?", "inline": false, "customConditional": "show = data.sex == 'female';", "values": [{"label": "No/I don't know", "value": false, "shortcut": ""}, {"label": "Yes", "value": true, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "vaccine_allergy", "type": "radio", "input": true, "label": "Have you ever had an allergic reaction to a previous dose of <PERSON><PERSON><PERSON>?", "inline": false, "values": [{"label": "No", "value": false, "shortcut": ""}, {"label": "Yes/I don't know", "value": true, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = (data.sex == 'female' & data.dukoral_pregnancy == false) || data.sex == 'male';", "optionsLabelPosition": "right"}, {"key": "acte_illness_contraindication", "type": "radio", "input": true, "label": "Are you currently suffering from a diarrheal illness, have a fever or are unwell?", "inline": false, "values": [{"label": "No", "value": false, "shortcut": ""}, {"label": "Yes/I don't know", "value": true, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.vaccine_allergy == false;", "optionsLabelPosition": "right"}]}