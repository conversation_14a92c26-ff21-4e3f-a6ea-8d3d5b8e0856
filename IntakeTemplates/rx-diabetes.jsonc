{"components": [{"key": "content", "html": "<div style='text-align: center;'><h2>Diabetes Intake Form</h2><p>We kindly ask you to complete the following questionnaire to help us better manage your diabetes.</p></div>", "type": "content", "input": false, "label": "Instructions", "tableView": false, "refreshOnChange": false}, {"key": "basic_history_header", "html": "<h3>Diabetes History</h3>", "type": "content", "input": false, "label": "Basic History", "tableView": false, "refreshOnChange": false}, {"key": "diabetes_diagnosis", "type": "radio", "input": true, "label": "Have you been diagnosed with diabetes?", "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "diabetes_type", "type": "radio", "input": true, "label": "What type of diabetes have you been diagnosed with?", "values": [{"label": "Type 1", "value": "type_1", "shortcut": ""}, {"label": "Type 2", "value": "type_2", "shortcut": ""}, {"label": "Gestational Diabetes", "value": "gestational", "shortcut": ""}, {"label": "Pre-Diabetes", "value": "pre-diabetes", "shortcut": ""}, {"label": "PCOS", "value": "pcos", "shortcut": ""}, {"label": "I don't know", "value": "doesnt_know", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.diabetes_diagnosis === 'yes'"}, {"key": "diagnosis_age", "type": "number", "input": true, "label": "At what age were you diagnosed?", "validate": {"required": true}, "tableView": true, "customConditional": "show = !!data.diabetes_type"}, {"key": "diabetes_management_followup", "type": "selectboxes", "input": true, "label": "Do you have a medical professional that follows for your diabetes?", "values": [{"label": "I have a medical professional in Canada", "value": "professional_in_canada", "shortcut": ""}, {"label": "I have a medical professional outside of Canada", "value": "professional_outside_canada", "shortcut": ""}, {"label": "I don't have follow-up with any medical professional", "value": "no_follow_up", "shortcut": ""}], "tableView": true, "customConditional": "show = !!data.diagnosis_age", "optionsLabelPosition": "right"}, {"key": "medication_renewals", "type": "selectboxes", "input": true, "label": "Who do you see for your medication renewals?", "values": [{"label": "Family doctor", "value": "family_doctor", "shortcut": ""}, {"label": "Endocrinologist/Internist", "value": "endocrinologist_internist", "shortcut": ""}, {"label": "Nurse practitioner", "value": "nurse_practitioner", "shortcut": ""}, {"label": "Walk-in Clinic Physician", "value": "walk_in_clinic", "shortcut": ""}, {"label": "I'm not currently on medication", "value": "not_currently_on_medication", "shortcut": ""}, {"label": "Virtual Care Physician", "value": "virtual_care_physician", "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}], "tableView": true, "customConditional": "show = !!data.diabetes_management_followup && !data.diabetes_management_followup.no_follow_up", "optionsLabelPosition": "right"}, {"key": "other_medication_renewal", "type": "textfield", "input": true, "label": "Please specify other professional for medication renewals", "tableView": true, "customConditional": "show = !!data.medication_renewals && data.medication_renewals.other"}, {"key": "physical_exam_header", "html": "<h3>Physical Exam</h3>", "type": "content", "input": false, "label": "Physical Exam", "tableView": false, "refreshOnChange": false}, {"key": "last_heart_lungs_bp_exam", "data": {"values": [{"label": "<1 month ago", "value": "less_1_month"}, {"label": "1-3 months ago", "value": "1_3_months"}, {"label": "3-6 months ago", "value": "3_6_months"}, {"label": "6-12 months ago", "value": "6_12_months"}, {"label": "1-2 years ago", "value": "1_2_years"}, {"label": "2+ years ago", "value": "2_plus_years"}, {"label": "I don't remember", "value": "doesn't_remember"}, {"label": "I have never had this done", "value": "never_had"}]}, "type": "select", "input": true, "label": "When was the last time a doctor listened to your heart, lungs and examined your blood pressure?", "widget": "html5", "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "diabetic_eye_exam", "type": "radio", "input": true, "label": "Have you ever had a diabetic eye exam (different than your usual vision eye-check up)?", "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = !!data.last_heart_lungs_bp_exam", "optionsLabelPosition": "right"}, {"key": "last_diabetic_eye_exam", "data": {"values": [{"label": "<1 year ago", "value": "less_1_year"}, {"label": "1-2 years ago", "value": "1_2_years_ago"}, {"label": "2-5 years ago", "value": "2_5_years_ago"}, {"label": "5+ years ago", "value": "more_5_years_ago"}]}, "type": "select", "input": true, "label": "When was your last diabetic eye exam?", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.diabetic_eye_exam === 'yes'"}, {"key": "diabetic_changes_identified", "type": "radio", "input": true, "label": "Do you recall if you had any diabetic changes identified on the exam?", "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "Not sure", "value": "not_sure", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = !!data.last_diabetic_eye_exam"}, {"key": "home_monitoring_header", "html": "<h3>Home Monitoring</h3>", "type": "content", "input": false, "label": "Home Monitoring", "tableView": false, "refreshOnChange": false}, {"key": "monitor_blood_pressure", "type": "radio", "input": true, "label": "Do you regularly monitor your blood pressure at home?", "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "monitor_blood_pressure_frequency", "type": "radio", "input": true, "label": "If yes, how frequently do you monitor your blood pressure?", "values": [{"label": "Daily", "value": "daily", "shortcut": ""}, {"label": "A few times per week", "value": "few_times_per_week", "shortcut": ""}, {"label": "Once a week", "value": "once_a_week", "shortcut": ""}, {"label": "Once a month", "value": "once_a_month", "shortcut": ""}, {"label": "Rarely", "value": "rarely", "shortcut": ""}, {"label": "Never", "value": "never", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.monitor_blood_pressure === 'yes'"}, {"key": "systolic_blood_pressure_range", "data": {"values": [{"label": "Below 90", "value": "below_90"}, {"label": "90-94", "value": "90_94"}, {"label": "95-99", "value": "95_99"}, {"label": "100-104", "value": "100_104"}, {"label": "105-109", "value": "105_109"}, {"label": "110-114", "value": "110_114"}, {"label": "115-119", "value": "115_119"}, {"label": "120-124", "value": "120_124"}, {"label": "125-129", "value": "125_129"}, {"label": "130-134", "value": "130_134"}, {"label": "135-139", "value": "135_139"}, {"label": "140-144", "value": "140_144"}, {"label": "145-149", "value": "145_149"}, {"label": "150-154", "value": "150_154"}, {"label": "155-159", "value": "155_159"}, {"label": "160-164", "value": "160_164"}, {"label": "165-169", "value": "165_169"}, {"label": "170-174", "value": "170_174"}, {"label": "175-179", "value": "175_179"}, {"label": "180+", "value": "180_plus"}]}, "type": "select", "input": true, "label": "Systolic (top number) blood pressure range (i.e. Blood pressure is written as 135/75 - your Systolic is '135')", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.monitor_blood_pressure === 'yes'"}, {"key": "diastolic_blood_pressure_range", "data": {"values": [{"label": "below_60", "value": "below_60"}, {"label": "60-64", "value": "60_64"}, {"label": "65-69", "value": "65_69"}, {"label": "70-74", "value": "70_74"}, {"label": "75-79", "value": "75_79"}, {"label": "80-84", "value": "80_84"}, {"label": "85-89", "value": "85_89"}, {"label": "90-94", "value": "90_94"}, {"label": "95-99", "value": "95_99"}, {"label": "100+", "value": "100_plus"}]}, "type": "select", "input": true, "label": "Diastolic (bottom number) blood pressure range (i.e. Blood pressure is written as 135/75 - your diastolic is '75')", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.monitor_blood_pressure === 'yes'"}, {"key": "monitor_blood_sugar", "type": "radio", "input": true, "label": "Do you monitor your blood sugar at home?", "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = !!data.monitor_blood_pressure", "optionsLabelPosition": "right"}, {"key": "blood_sugar_device", "type": "selectboxes", "input": true, "label": "If yes, what device do you use?", "values": [{"label": "Dexcom G7", "value": "dexcom_g7", "shortcut": ""}, {"label": "Free-style Libre", "value": "free_style_libre", "shortcut": ""}, {"label": "<PERSON><PERSON><PERSON>", "value": "insulin_pump", "shortcut": ""}, {"label": "Finger Prick Testing", "value": "finger_prick_testing", "shortcut": ""}], "tableView": true, "customConditional": "show = data.monitor_blood_sugar === 'yes'"}, {"key": "blood_sugar_frequency", "type": "radio", "input": true, "label": "If yes, how frequently do you monitor your blood sugar?", "values": [{"label": "Daily", "value": "daily", "shortcut": ""}, {"label": "A few times per week", "value": "few_times_per_week", "shortcut": ""}, {"label": "Once a week", "value": "once_a_week", "shortcut": ""}, {"label": "Once a month", "value": "once_a_month", "shortcut": ""}, {"label": "Rarely", "value": "rarely", "shortcut": ""}, {"label": "Never", "value": "never", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.monitor_blood_sugar === 'yes'"}, {"key": "current_medications_header", "html": "<h3>Current Medications</h3>", "type": "content", "input": false, "label": "Current Medications", "tableView": false, "refreshOnChange": false}, {"key": "on_insulin", "type": "radio", "input": true, "label": "Are you on insulin?", "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "on_oral_medications", "type": "radio", "input": true, "label": "Are you currently on oral medications (pills) for your diabetes (i.e. metformin)?", "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = !!data.on_insulin"}, {"key": "on_injectable_medications", "type": "radio", "input": true, "label": "Are you on any non-insulin injectable diabetic medication (i.e. Ozempic)?", "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = !!data.on_oral_medications'"}, {"key": "insulin_delivery_method", "type": "selectboxes", "input": true, "label": "Please select which of the following insulin delivery methods you use:", "values": [{"label": "Insulin pump", "value": "pump", "shortcut": ""}, {"label": "Injection", "value": "injection", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.on_insulin === 'yes'", "optionsLabelPosition": "right"}, {"key": "insulin_pump_header", "html": "<h3>Insulin Pump</h3>", "type": "content", "input": false, "label": "<PERSON><PERSON><PERSON>", "tableView": false, "customConditional": "show = !!data.insulin_delivery_method && data.insulin_delivery_method.pump"}, {"key": "insulin_pump_type", "data": {"values": [{"label": "<PERSON><PERSON><PERSON> (NovoLog)", "value": "aspart"}, {"label": "<PERSON><PERSON><PERSON> (Humalog)", "value": "lispro"}, {"label": "In<PERSON><PERSON> (Apidra)", "value": "glulisine"}, {"label": "Other", "value": "other"}]}, "type": "select", "input": true, "label": "What type of insulin do you use in your pump?", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = !!data.insulin_delivery_method && data.insulin_delivery_method.pump"}, {"key": "daily_insulin_units", "type": "textfield", "input": true, "label": "On average, how many units of this insulin do you go through in a day?", "validate": {"custom": "valid = (data.insulin_pump_type !== undefined && data.insulin_delivery_method === 'pump')", "required": true}, "tableView": true, "customConditional": "show = data.insulin_delivery_method === 'pump' && !!data.insulin_pump_type"}, {"key": "long_acting_basal_insulin", "type": "radio", "input": true, "label": "Do you use a long-acting basal insulin in addition to your insulin pump?", "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.insulin_delivery_method === 'pump'"}, {"key": "oral_medications_header", "type": "content", "html": "<h3>Oral Medications</h3>", "input": false, "label": "Oral Medications Header", "tableView": false, "customConditional": "show = data.on_oral_medications === 'yes'"}, {"key": "combination_pills", "type": "selectboxes", "input": true, "label": "Are you on any of the following combination pills (i.e., two medications in one)?", "values": [{"label": "Metformin/Sit<PERSON>tin (Janumet)", "value": "metformin_sitagliptin_janumet", "shortcut": ""}, {"label": "Metformin/Empagliflozin (Synjardy)", "value": "metformin_empagliflozin_synjardy", "shortcut": ""}, {"label": "Metformin/Canagliflozin (Invokamet)", "value": "metformin_canagliflozin_invokamet", "shortcut": ""}, {"label": "Metformin/<PERSON>tin (Jentadueto)", "value": "metformin_linagliptin_jent<PERSON><PERSON>to", "shortcut": ""}, {"label": "Metformin/Pioglitazone (Actoplus Met)", "value": "metformin_pioglitazone_actoplus_met", "shortcut": ""}, {"label": "Other", "value": "other_combination", "shortcut": ""}], "tableView": true, "customConditional": "show = data.on_oral_medications === 'yes'", "optionsLabelPosition": "right"}, {"key": "oral_medication_options", "type": "selectboxes", "input": true, "label": "Please select the oral medications you are currently taking:", "values": [{"label": "Acarbose (Glucobay)", "value": "acarbose_glucobay", "shortcut": ""}, {"label": "<PERSON><PERSON><PERSON><PERSON> (Nesina)", "value": "alogliptin_nesina", "shortcut": ""}, {"label": "Canagliflozin (Invokana)", "value": "canagliflozin_invokana", "shortcut": ""}, {"label": "Dapagliflozin (Forxiga)", "value": "dapagliflozin_forxiga", "shortcut": ""}, {"label": "Empagliflozin (Jardiance)", "value": "empagliflozin_jardiance", "shortcut": ""}, {"label": "Gliclazide (Diamicron)", "value": "gliclazide_diamicron", "shortcut": ""}, {"label": "Glyburide (Diabeta)", "value": "glyburide_diabeta", "shortcut": ""}, {"label": "<PERSON><PERSON><PERSON><PERSON> (Trajenta)", "value": "linagliptin_trajenta", "shortcut": ""}, {"label": "Metformin (Glucophage)", "value": "metformin_glucophage", "shortcut": ""}, {"label": "Pioglitazone (Actos)", "value": "pioglitazone_actos", "shortcut": ""}, {"label": "Saxagliptin (Onglyza)", "value": "saxagliptin_onglyza", "shortcut": ""}, {"label": "<PERSON><PERSON><PERSON><PERSON> (Januvia)", "value": "sitagliptin_januvia", "shortcut": ""}, {"label": "Other", "value": "other_oral", "shortcut": ""}], "tableView": true, "customConditional": "show = data.on_oral_medications === 'yes'", "optionsLabelPosition": "right"}, {"key": "other_oral_medication", "type": "textfield", "input": true, "label": "Please specify other oral medication", "tableView": true, "customConditional": "show = data.oral_medication_options.other_oral"}, {"key": "non_insulin_injectable_medications_header", "type": "content", "html": "<h3>Non-Insulin Injectable Medications</h3>", "input": false, "label": "Injectable Medications Header", "tableView": false, "customConditional": "show = data.on_injectable_medications === 'yes'"}, {"key": "injectable_medication_options", "type": "selectboxes", "input": true, "label": "Please select the injectable medications you are currently taking:", "values": [{"label": "Dulaglutide (Trulicity)", "value": "dulaglutide_trulicity", "shortcut": ""}, {"label": "Exenatide (Byetta)", "value": "exenatide_byetta", "shortcut": ""}, {"label": "Liraglutide (Victoza)", "value": "liraglutide_victoza", "shortcut": ""}, {"label": "Lixisenatide (Adlyxine)", "value": "lixisenatide_adlyxine", "shortcut": ""}, {"label": "Semaglutide (Ozempic)", "value": "semaglutide_ozempic", "shortcut": ""}, {"label": "Other", "value": "other_injectable", "shortcut": ""}], "tableView": true, "customConditional": "show = data.on_injectable_medications === 'yes'", "optionsLabelPosition": "right"}, {"key": "previous_medications_header", "html": "<h3>Previous Medications</h3>", "type": "content", "input": false, "label": "Previous Medications", "tableView": false, "refreshOnChange": false}, {"key": "current_medications", "type": "textfield", "input": true, "label": "What medications are you currently taking for diabetes?", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.diabetes_diagnosis === 'yes'"}, {"key": "diabetes_control", "type": "radio", "input": true, "label": "How well is your diabetes controlled?", "values": [{"label": "Well controlled", "value": "well_controlled", "shortcut": ""}, {"label": "Moderately controlled", "value": "moderately_controlled", "shortcut": ""}, {"label": "Poorly controlled", "value": "poorly_controlled", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.diabetes_diagnosis === 'yes'"}, {"key": "family_history", "type": "radio", "input": true, "label": "Do you have a family history of diabetes?", "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "family_member_diabetes", "type": "textfield", "input": true, "label": "Which family member(s) have diabetes?", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.family_history === 'yes'"}, {"key": "symptoms_experienced", "type": "selectboxes", "input": true, "label": "What symptoms have you experienced related to diabetes?", "values": [{"label": "Increased thirst", "value": "increased_thirst", "shortcut": ""}, {"label": "Frequent urination", "value": "frequent_urination", "shortcut": ""}, {"label": "Unexplained weight loss", "value": "weight_loss", "shortcut": ""}, {"label": "Fatigue", "value": "fatigue", "shortcut": ""}, {"label": "Blurred vision", "value": "blurred_vision", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.diabetes_diagnosis === 'yes'"}, {"key": "no_symptoms_experienced", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = !!data.no_symptoms_experienced || !!_.some(_.values(data.symptoms_experienced));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}]}