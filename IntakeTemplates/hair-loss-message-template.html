{% load template_extras %}
{% with questionnaire.hpc.data as data %}
{% with questionnaire.raw_formio_summary as summary %}
{% with questionnaire.insured_assays.all as insured %}
{% with questionnaire.uninsured_assays.all as uninsured %}

<p>Hi {{ patient.name }},</p>

<p>This is {{ doctor.name }} (CPSO #{{ doctor.cpso_number }}) &amp; clinic contact phone {{ doctor.phone }}.</p>

<p>I've reviewed your hair-loss screening intake. If you spot any mistakes—or recall extra details—please message us in the portal so we can update your record before proceeding. Otherwise, follow the plan below: the investigations requested screen for the most common (and often reversible) contributors to hair loss, while optional micronutrient panels are listed separately for your consideration.</p>

<hr class="my-4"/>

<!-- =================  LAB TESTS FIRST  ================= -->
<h2 class="text-center">Lab-Tests-Requested</h2>

{% if insured %}
<h5>Insured (covered by OHIP)</h5>
<ul class="mb-3">
  {% for assay in insured %}
     <li>{{ assay.name }}</li>
  {% endfor %}
</ul>
{% endif %}

{% if uninsured %}
<h5>Uninsured / Out-of-pocket</h5>
<ul class="mb-3">
  {% for assay in uninsured %}
     <li>{{ assay.name }}</li>
  {% endfor %}
</ul>
<p><em>Uninsured tests incur additional charges payable directly to the laboratory. You may decline any or all of them if you prefer not to proceed.</em></p>
{% endif %}

{% if not insured and not uninsured %}
<p><em>No laboratory tests have been selected yet.</em></p>
{% endif %}

<hr class="my-4"/>

<!-- =================  MEDICAL SUMMARY  ================= -->
<h2 class="text-center">Medical-Summary</h2>

<!-- Hair-loss history -->
<h4>Hair-loss history-&amp; key symptoms</h4>
<ul>
  {% for qa in summary|confirm:"mphl_age,aa_sudden_hair_loss,telogen_effluvium_shedding,traction_alopecia_hairline_temples,male_pattern_baldness" %}
    <li>{{ qa|replace:": |: <strong>"|safe }}</strong></li>
  {% endfor %}
</ul>

<!-- Associated symptoms -->
<h4>Associated symptoms</h4>
<ul>
  <!-- Hyper-androgenism umbrella -->
  <li>
    {% for qa in summary|confirm:"hyperandrogenism_signs" %}{% if forloop.first %}{{ qa|replace:": |: <strong>"|safe }}</strong>{% endif %}{% endfor %}
    {% if data.hyperandrogenism_signs == 'yes' %}
    <ul>
      {% for qa in summary|confirm:"hyperandrogenism_menstrual_cycle,deepening_voice,increased_muscle_mass,clitoromegaly" %}
        <li>{{ qa|replace:": |: <strong>"|safe }}</strong></li>
      {% endfor %}
    </ul>
    {% endif %}
  </li>
  <!-- Other stand-alone associated symptoms -->
  {% for qa in summary|confirm:"traction_alopecia_scalp_pain_itching_redness,aa_graying_hair" %}
    <li>{{ qa|replace:": |: <strong>"|safe }}</strong></li>
  {% endfor %}
</ul>

<!-- Triggers & lifestyle factors -->
<h4>Triggers-&amp; lifestyle factors</h4>
<ul>
  {% for qa in summary|confirm:"te_recent_birth_or_pregnancy,te_recent_acute_event,te_new_product_hair_care_routine" %}
    <li>{{ qa|replace:": |: <strong>"|safe }}</strong></li>
  {% endfor %}
  {% for qa in summary|confirm:"tractional_tight_hairstyles,traction_alopecia_extensions_weaves_wigs" %}
    <li>{{ qa|replace:": |: <strong>"|safe }}</strong></li>
  {% endfor %}
</ul>

<!-- Family history -->
<h4>Family history</h4>
<ul>
  {% for qa in summary|confirm:"mphl_family_history,aa_fmhx" %}
    <li>{{ qa|replace:": |: <strong>"|safe }}</strong></li>
  {% endfor %}
</ul>

<!-- Existing conditions -->
<h4>Existing medical conditions</h4>
<ul>
  {% for qa in summary|confirm:"hx_pcos_cah_cushing_tumor,aa_hx_dm_vitiligo_ra_hypothroidism_sle,te_hx_vitamin_minteral_def,abnormal_cbc_risk_factors" %}
    <li>{{ qa|replace:": |: <strong>"|safe }}</strong></li>
  {% endfor %}
</ul>

<!-- Recent Lab Values -->
{% comment %}Render only rows for assays that have recorded results{% endcomment %}
<h4>Recent Lab Tests</h4>
{% with summary|confirm:"prior_cbc_value,recent_ferritin_value,prior_tsh_value,recent_zinc_value,prior_b12_value,recent_selenium_value,recent_folate_value" as any_labs %}
{% if any_labs %}
<table class="table table-sm mb-4">
  <thead>
    <tr><th>Test</th><th>Most-recent-result</th><th>When-tested</th></tr>
  </thead>
  <tbody>
    {% with summary|confirm:"prior_cbc_value" as val %}{% if val %}
      <tr>
        <td>CBC-(Hemoglobin)</td>
        <td><strong>{{ val|first|replace:"Hemoglobin Level: |"|safe }}</strong></td>
        <td>{% with summary|confirm:"last_cbc_test" as when %}{% if when %}<strong>{{ when|first|replace:"Last CBC Test: |"|safe }}</strong>{% endif %}{% endwith %}</td>
      </tr>
    {% endif %}{% endwith %}

    {% with summary|confirm:"recent_ferritin_value" as val %}{% if val %}
      <tr>
        <td>Ferritin</td>
        <td><strong>{{ val|first|replace:"Ferritin Level: |"|safe }}</strong></td>
        <td>{% with summary|confirm:"last_ferritin_test" as when %}{% if when %}<strong>{{ when|first|replace:"Last Ferritin Test: |"|safe }}</strong>{% endif %}{% endwith %}</td>
      </tr>
    {% endif %}{% endwith %}

    {% with summary|confirm:"prior_tsh_value" as val %}{% if val %}
      <tr>
        <td>TSH</td>
        <td><strong>{{ val|first|replace:"TSH Level: |"|safe }}</strong></td>
        <td>{% with summary|confirm:"last_tsh_test" as when %}{% if when %}<strong>{{ when|first|replace:"Last TSH Test: |"|safe }}</strong>{% endif %}{% endwith %}</td>
      </tr>
    {% endif %}{% endwith %}

    {% with summary|confirm:"recent_zinc_value" as val %}{% if val %}
      <tr>
        <td>Zinc</td>
        <td><strong>{{ val|first|replace:"Zinc Level: |"|safe }}</strong></td>
        <td>{% with summary|confirm:"last_zinc_test" as when %}{% if when %}<strong>{{ when|first|replace:"Last Zinc Test: |"|safe }}</strong>{% endif %}{% endwith %}</td>
      </tr>
    {% endif %}{% endwith %}

    {% with summary|confirm:"prior_b12_value" as val %}{% if val %}
      <tr>
        <td>Vitamin-B12</td>
        <td><strong>{{ val|first|replace:"B12 Level: |"|safe }}</strong></td>
        <td>{% with summary|confirm:"last_b12_test" as when %}{% if when %}<strong>{{ when|first|replace:"Last B12 Test: |"|safe }}</strong>{% endif %}{% endwith %}</td>
      </tr>
    {% endif %}{% endwith %}

    {% with summary|confirm:"recent_selenium_value" as val %}{% if val %}
      <tr>
        <td>Selenium</td>
        <td><strong>{{ val|first|replace:"Selenium Level: |"|safe }}</strong></td>
        <td>{% with summary|confirm:"last_selenium_test" as when %}{% if when %}<strong>{{ when|first|replace:"Last Selenium Test: |"|safe }}</strong>{% endif %}{% endwith %}</td>
      </tr>
    {% endif %}{% endwith %}

    {% with summary|confirm:"recent_folate_value" as val %}{% if val %}
      <tr>
        <td>Folate</td>
        <td><strong>{{ val|first|replace:"Folate Level: |"|safe }}</strong></td>
        <td>{% with summary|confirm:"last_folate_test" as when %}{% if when %}<strong>{{ when|first|replace:"Last Folate Test: |"|safe }}</strong>{% endif %}{% endwith %}</td>
      </tr>
    {% endif %}{% endwith %}
  </tbody>
</table>
{% else %}
<p><em>No prior lab values were provided.</em></p>
{% endif %}
{% endwith %}

<!-- Prior hair-loss treatments -->
<h4>Prior hair-loss treatments</h4>
<ul>
  <li>
    {% for qa in summary|confirm:"prior_hl_treatments" %}{% if forloop.first %}{{ qa|replace:": |: <strong>"|safe }}</strong>{% endif %}{% endfor %}
    {% if not data.prior_hl_treatments.none %}
      <ul>
        {% for qa in summary|confirm:"hair_loss_treatments" %}
          <li>{{ qa|replace:": |: <strong>"|safe }}</strong></li>
        {% endfor %}
      </ul>
    {% endif %}
  </li>
  {% for qa in summary|confirm:"skin_biopsy" %}
    <li>{{ qa|replace:": |: <strong>"|safe }}</strong></li>
  {% endfor %}
</ul>

<p>Although vitamin or mineral deficiencies explain only a minority of hair-loss cases, we include screening for these in your bloodwork. If any abnormal values are identified, you will be notified and given options for follow-up. Most patients ultimately benefit more from evidence-based treatments—such as topical minoxidil (Rogaine®), platelet-rich plasma (PRP) therapy, or, for men, oral finasteride—which help reduce shedding and promote regrowth. Once your bloodwork is complete, the next step is a brief in-person scalp examination so a clinician can assess your follicles and tailor the treatment plan accordingly.</p>

<p class="mt-4">If any information above is incorrect, please you can do so below.</p>


{% endwith %}
{% endwith %}
{% endwith %}
{% endwith %}