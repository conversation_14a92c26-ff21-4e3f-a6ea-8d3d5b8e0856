{"components": [{"key": "heading_asthma_symptoms", "html": "<h2 class='text-center'>Asthma Symptoms</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "changes_asthma", "type": "radio", "input": true, "label": "Have you noticed any of the following asthma symptoms?<ul><li>Shortness of breath or trouble breathing</li><li>Wheezing or a whistling sound when you breathe out</li><li>Coughing, especially at night or early morning</li><li>Chest tightness or pressure</li></ul>", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Noticed asthma symptoms:", "optionsLabelPosition": "right"}, {"key": "asthma_symptom_overview", "type": "selectboxes", "input": true, "label": "Are you currently experiencing any of these symptoms?", "values": [{"label": "Shortness of breath", "value": "sob"}, {"label": "Wheezing / whistling when breathing", "value": "wheeze"}, {"label": "Coughing (night-time or early morning)", "value": "cough"}, {"label": "Chest tightness or pressure", "value": "chest_tightness"}, {"label": "Need to use rescue inhaler more than usual", "value": "increased_rescue"}, {"label": "Sleep disturbed by breathing problems (night-wakening)", "value": "sleep_disturbance"}], "adminFlag": true, "inputType": "checkbox", "tableView": true, "confirm_label": "Current asthma symptoms:", "optionsLabelPosition": "right", "customConditional": "show = !!data.changes_asthma"}, {"key": "none_of_the_above_asthma_symptom_overview", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "Please select at least one symptom or check 'None of the above.'"}, "validate": {"custom": "valid = !!data.none_of_the_above_asthma_symptom_overview || _.some(_.values(data.asthma_symptom_overview));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = !!data.changes_asthma"}, {"key": "asthma_symptoms_present", "type": "textfield", "input": true, "label": "Patient indicated they have the following asthma symptoms:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "You have the following asthma symptoms:", "calculateValue": "value = _.join(_.map(_.filter([ { label: 'Shortness of breath', value: 'sob' }, { label: 'Wheezing / whistling when breathing', value: 'wheeze' }, { label: 'Coughing (night-time or early morning)', value: 'cough' }, { label: 'Chest tightness or pressure', value: 'chest_tightness' }, { label: 'Need to use rescue inhaler more than usual', value: 'increased_rescue' }, { label: 'Sleep disturbed by breathing problems (night-wakening)', value: 'sleep_disturbance' }, { label: 'Other asthma-related symptom', value: 'other' } ], option => data.asthma_symptom_overview && data.asthma_symptom_overview[option.value] ), 'label'), ', ');"}, {"key": "asthma_symptoms_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following asthma symptoms:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "You DO NOT have the following asthma symptoms:", "calculateValue": "value = _.join(_.map(_.filter([ { label: 'Shortness of breath', value: 'sob' }, { label: 'Wheezing / whistling when breathing', value: 'wheeze' }, { label: 'Coughing (night-time or early morning)', value: 'cough' }, { label: 'Chest tightness or pressure', value: 'chest_tightness' }, { label: 'Need to use rescue inhaler more than usual', value: 'increased_rescue' }, { label: 'Sleep disturbed by breathing problems (night-wakening)', value: 'sleep_disturbance' } ], option => !(data.asthma_symptom_overview && data.asthma_symptom_overview[option.value]) ), 'label'), ', ');"}, {"key": "stated_other_symptoms_asthma", "type": "textarea", "input": true, "label": "Please describe any other asthma-related symptoms you have that are not listed above.<br>Leave blank if none.", "adminFlag": true, "tableView": true, "autoExpand": false, "placeholder": "No additional symptoms", "customConditional": "show = data.asthma_symptom_overview?.other === true"}, {"key": "asthma_symptom_contradiction_warning", "type": "content", "input": false, "html": "<div style='border-left: 4px solid #dc3545; background-color: #f8d7da; padding: 10px; margin-bottom: 10px;'><strong>Notice:</strong> You indicated that you have asthma symptoms, but then selected \"None of the above.\" Please review your answers above and make sure they reflect your current symptoms accurately.</div>", "customConditional": "show = data.changes_asthma === 'yes' && data.none_of_the_above_asthma_symptom_overview === true"}, {"key": "confirm_no_asthma_symptoms", "type": "radio", "input": true, "label": "You reported that you are not currently experiencing any asthma symptoms. Please confirm:", "values": [{"label": "Correct - I have no asthma symptoms right now", "value": "no_symptoms"}, {"label": "Actually, I do have other asthma-related symptoms", "value": "other_symptoms"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Symptom-free confirmation:", "customConditional": "show = data.changes_asthma === 'no' && data.none_of_the_above_asthma_symptom_overview === true", "optionsLabelPosition": "right"}, {"key": "other_asthma_symptom_description", "type": "textarea", "input": true, "label": "Please describe the other symptom(s) you are experiencing:", "tableView": true, "autoExpand": false, "placeholder": "e.g. occasional throat tightness, post-exercise cough…", "customConditional": "show = data.confirm_no_asthma_symptoms === 'other_symptoms'"}, {"key": "heading_asthma_triggers", "html": "<h4 class='mb-2'>Asthma Triggers</h4>", "type": "content", "input": false, "label": "<PERSON><PERSON><PERSON>ing", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.asthma_symptom_overview && _.some(_.values(data.asthma_symptom_overview)) && !data.none_of_the_above_asthma_symptom_overview"}, {"key": "asthma_symptom_triggers", "type": "selectboxes", "input": true, "label": "Have any of these situations triggered or worsened your asthma symptoms? (Select all that apply)", "values": [{"label": "Exercise or physical activity", "value": "exercise"}, {"label": "Exposure to cold air", "value": "cold_air"}, {"label": "Cold/flu-like symptoms", "value": "infection"}, {"label": "Allergens (pollen, dust, pets)", "value": "allergens"}, {"label": "Smoke, pollution, or strong odours", "value": "smoke_pollution"}, {"label": "Weather changes or humidity", "value": "weather"}, {"label": "Other (please specify)", "value": "other"}], "adminFlag": true, "inputType": "checkbox", "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = data.asthma_symptom_overview && _.some(_.values(data.asthma_symptom_overview)) && !data.none_of_the_above_asthma_symptom_overview"}, {"key": "none_of_the_above_asthma_symptom_triggers", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "Required: pick at least one trigger or check 'None of the above'."}, "validate": {"custom": "valid = !!data.none_of_the_above_asthma_symptom_triggers || _.some(_.values(data.asthma_symptom_triggers));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.asthma_symptom_overview && _.some(_.values(data.asthma_symptom_overview)) && !data.none_of_the_above_asthma_symptom_overview"}, {"key": "asthma_symptom_triggers_other", "type": "textarea", "input": true, "label": "Please describe any other asthma symptom triggers you have experienced:", "tableView": true, "autoExpand": false, "placeholder": "e.g. strong perfumes, dust, etc.", "customConditional": "show = data.asthma_symptom_triggers?.other === true"}, {"key": "asthma_symptom_triggers_present", "type": "textfield", "input": true, "label": "Patient indicated they have the following asthma symptom triggers:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "I have the following asthma symptom triggers:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.asthma_symptom_triggers, _.identity)), _.capitalize), ', '), /_/g, ' ');"}, {"key": "asthma_symptom_triggers_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following asthma symptom triggers:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "I DO NOT have the following asthma symptom triggers:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.asthma_symptom_triggers, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "asthma_symptom_onset_pattern", "type": "radio", "input": true, "label": "Did your asthma symptoms start around the same time or on separate days?", "values": [{"label": "All symptoms started around the same time", "value": "same_time"}, {"label": "Symptoms started on separate days", "value": "separate_days"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.asthma_symptom_overview && _.sum(_.values(data.asthma_symptom_overview).map(Number)) >= 2", "optionsLabelPosition": "right"}, {"key": "durations_asthma", "type": "textfield", "input": true, "label": "Durations:", "hidden": true, "disabled": true, "multiple": true, "tableView": false, "clearOnHide": false, "calculateValue": [{"label": "Doesn't apply", "value": "doesn't_apply"}, {"label": "Last night", "value": "last_night"}, {"label": "Today", "value": "today"}, {"label": "1 day ago", "value": "1_day_ago"}, {"label": "2 days ago", "value": "2_days_ago"}, {"label": "3 days ago", "value": "3_days_ago"}, {"label": "4 days ago", "value": "4_days_ago"}, {"label": "5 days ago", "value": "5_days_ago"}, {"label": "6 days ago", "value": "6_days_ago"}, {"label": "7 days ago", "value": "7_days_ago"}, {"label": "8 days ago", "value": "8_days_ago"}, {"label": "9 days ago", "value": "9_days_ago"}, {"label": "10 days ago", "value": "10_days_ago"}, {"label": "11 days ago", "value": "11_days_ago"}, {"label": "12 days ago", "value": "12_days_ago"}, {"label": "13 days ago", "value": "13_days_ago"}, {"label": "14 days ago", "value": "14_days_ago"}, {"label": "14-21 days ago", "value": "14-21_days"}, {"label": "21-28 days ago", "value": "21-28_days"}, {"label": "28-60 days ago", "value": "28-60_days"}, {"label": "60-90 days ago", "value": "60-90_days"}, {"label": "90 days - 1 year ago", "value": "90_days-1_year"}, {"label": "1+ year ago", "value": "1+_year"}]}, {"key": "all_asthma_symptoms_start", "data": {"custom": "values = data.durations_asthma"}, "type": "select", "input": true, "label": "When did your asthma symptoms start?", "widget": "html5", "dataSrc": "custom", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.asthma_symptom_overview && data.asthma_symptom_onset_pattern == 'same_time' && !data.none_of_the_above_asthma_symptom_overview", "optionsLabelPosition": "right"}, {"key": "heading_sob", "html": "<h4 class='mb-n2'>Shortness of Breath</h4>", "type": "content", "input": false, "label": "SOB Details", "tableView": false, "refreshOnChange": true, "customConditional": "show = data.asthma_symptom_overview?.sob === true"}, {"key": "symptom_start_sob", "data": {"custom": "values = [{label: `I've always had some shortness of breath and this isn't new for me`, value:`always`}].concat(data.durations_asthma)"}, "type": "select", "input": true, "label": "When did your shortness of breath start?", "widget": "html5", "dataSrc": "custom", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.asthma_symptom_overview?.sob && (_.sum(_.values(data.asthma_symptom_overview).map(Number)) < 2 || data.asthma_symptom_onset_pattern === 'separate_days')", "optionsLabelPosition": "right"}, {"key": "sob_activity_threshold", "type": "radio", "input": true, "label": "Which statement best describes how easily you become short of breath?", "values": [{"label": "Only with heavy exercise (e.g. running, intense sports)", "value": "heavy_exertion"}, {"label": "When walking up a slight hill or single flight of stairs", "value": "hill_one_flight"}, {"label": "When walking on level ground but keeping pace with others", "value": "level_with_others"}, {"label": "When walking on level ground at my own pace (one room to another)", "value": "level_own_pace"}, {"label": "Short of breath even while at rest / sitting", "value": "at_rest"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Breathlessness occurs:", "customConditional": "show = data.asthma_symptom_overview?.sob === true", "optionsLabelPosition": "right"}, {"key": "sob_orthopnea", "type": "radio", "input": true, "label": "Do you become short of breath when lying flat?", "values": [{"label": "No", "value": "no"}, {"label": "Yes - I need extra pillows", "value": "orthopnea"}, {"label": "I wake up gasping for air", "value": "pnd"}, {"label": "Not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Breathlessness when lying:", "customConditional": "show = data.asthma_symptom_overview?.sob === true", "optionsLabelPosition": "right"}, {"key": "sob_relief", "type": "radio", "input": true, "label": "What helps relieve the shortness of breath?", "values": [{"label": "Rescue inhaler", "value": "rescue"}, {"label": "Rest / sitting upright", "value": "rest"}, {"label": "Breathing techniques", "value": "breathing_technique"}, {"label": "Nothing brings relief", "value": "nothing"}, {"label": "Haven't tried anything", "value": "not_tried"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Relief measures:", "customConditional": "show = data.asthma_symptom_overview?.sob === true", "optionsLabelPosition": "right"}, {"key": "heading_wheeze", "html": "<h4 class='mb-n2'>Wheezing</h4>", "type": "content", "input": false, "label": "Wheeze Details", "tableView": false, "refreshOnChange": true, "customConditional": "show = data.asthma_symptom_overview?.wheeze === true"}, {"key": "symptom_start_wheeze", "data": {"custom": "values = [{label: `I've always wheezed a bit and this isn't new for me`, value:`always`}].concat(data.durations_asthma)"}, "type": "select", "input": true, "label": "When did your wheezing start?", "widget": "html5", "dataSrc": "custom", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.asthma_symptom_overview?.wheeze && (_.sum(_.values(data.asthma_symptom_overview).map(Number)) < 2 || data.asthma_symptom_onset_pattern === 'separate_days')", "optionsLabelPosition": "right"}, {"key": "wheeze_frequency_current", "type": "radio", "input": true, "label": "Right now, how often are you wheezing?", "values": [{"label": "Rarely (less than once a week)", "value": "rare"}, {"label": "Several days a week", "value": "several_days"}, {"label": "Daily", "value": "daily"}, {"label": "Almost constantly", "value": "constant"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Current wheeze frequency:", "customConditional": "show = data.asthma_symptom_overview?.wheeze === true", "optionsLabelPosition": "right"}, {"key": "wheeze_character", "type": "selectboxes", "input": true, "label": "How would you describe the sound? (Select all that apply)", "values": [{"label": "High-pitched / whistling", "value": "high"}, {"label": "Low-pitched / rattling", "value": "low"}, {"label": "Musical or squeaky", "value": "musical"}, {"label": "Other", "value": "other"}], "tableView": true, "confirm_label": "Wheeze character:", "customConditional": "show = data.asthma_symptom_overview?.wheeze === true", "optionsLabelPosition": "right"}, {"key": "wheeze_character_other_description", "type": "textarea", "input": true, "label": "Describe the wheeze in your own words:", "tableView": true, "autoExpand": false, "placeholder": "e.g. 'like air escaping a balloon'", "customConditional": "show = data.wheeze_character?.other === true"}, {"key": "wheeze_rescue_relief", "type": "radio", "input": true, "label": "Does your rescue inhaler relieve the wheeze?", "values": [{"label": "Yes, completely", "value": "complete"}, {"label": "Partially", "value": "partial"}, {"label": "No relief", "value": "none"}, {"label": "Haven't tried it for wheeze", "value": "not_tried"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Rescue inhaler effect on wheeze:", "customConditional": "show = data.asthma_symptom_overview?.wheeze === true", "optionsLabelPosition": "right"}, {"key": "heading_cough", "html": "<h4 class='mb-n2'>Coughing</h4>", "type": "content", "input": false, "label": "Cough Details", "tableView": false, "refreshOnChange": true, "customConditional": "show = data.asthma_symptom_overview?.cough === true"}, {"key": "symptom_start_cough", "data": {"custom": "values = [{label: `I've always coughed a bit and this isn't new for me`, value:`always`}].concat(data.durations_asthma)"}, "type": "select", "input": true, "label": "When did your coughing start?", "widget": "html5", "dataSrc": "custom", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.asthma_symptom_overview?.cough && (_.sum(_.values(data.asthma_symptom_overview).map(Number)) < 2 || data.asthma_symptom_onset_pattern === 'separate_days')", "optionsLabelPosition": "right"}, {"key": "cough_character", "type": "selectboxes", "input": true, "label": "How would you describe the cough? (Select all that apply)", "values": [{"label": "Dry / non-productive", "value": "dry"}, {"label": "Wet or brings up phlegm", "value": "wet"}, {"label": "Tickly / throat-clearing", "value": "tickly"}, {"label": "Barking or harsh", "value": "barking"}, {"label": "Other", "value": "other"}], "tableView": true, "confirm_label": "Your cough feels:", "customConditional": "show = data.asthma_symptom_overview?.cough === true", "optionsLabelPosition": "right"}, {"key": "cough_character_other_description", "type": "textarea", "input": true, "label": "Describe the cough in your own words:", "tableView": true, "autoExpand": false, "placeholder": "e.g. 'sounds like a goose honk', 'metallic', etc.", "customConditional": "show = data.cough_character?.other === true"}, {"key": "cough_frequency", "type": "radio", "input": true, "label": "How often are you coughing right now?", "values": [{"label": "Occasionally (a few times a day)", "value": "occasional"}, {"label": "Several times an hour", "value": "hourly"}, {"label": "Almost constantly", "value": "constant"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Current cough frequency:", "customConditional": "show = data.asthma_symptom_overview?.cough === true", "optionsLabelPosition": "right"}, {"key": "cough_improves_with_rescue", "type": "radio", "input": true, "label": "Does your rescue inhaler (blue puffer) help the cough?", "values": [{"label": "Yes - it eases the cough", "value": "yes"}, {"label": "No - no noticeable change", "value": "no"}, {"label": "Haven't tried the inhaler for cough", "value": "not_tried"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Rescue inhaler effect on cough:", "customConditional": "show = data.asthma_symptom_overview?.cough === true", "optionsLabelPosition": "right"}, {"key": "cough_sputum_presence", "type": "radio", "input": true, "label": "Are you currently coughing up phlegm or mucus?", "values": [{"label": "No - it's dry", "value": "dry"}, {"label": "Yes - small amounts", "value": "small_amount"}, {"label": "Yes - large amounts", "value": "large_amount"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Phlegm with cough:", "customConditional": "show = data.asthma_symptom_overview?.cough === true", "optionsLabelPosition": "right"}, {"key": "heading_chest_tightness", "html": "<h4 class='mb-n2'>Chest Tightness or Pressure</h4>", "type": "content", "input": false, "label": "Chest-Tightness Details", "tableView": false, "refreshOnChange": true, "customConditional": "show = data.asthma_symptom_overview?.chest_tightness === true"}, {"key": "symptom_start_chest_tightness", "data": {"custom": "values = [{label: `I've always felt some chest tightness and this isn't new for me`, value:`always`}].concat(data.durations_asthma)"}, "type": "select", "input": true, "label": "When did your chest tightness or pressure start?", "widget": "html5", "dataSrc": "custom", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.asthma_symptom_overview?.chest_tightness && (_.sum(_.values(data.asthma_symptom_overview).map(Number)) < 2 || data.asthma_symptom_onset_pattern === 'separate_days')", "optionsLabelPosition": "right"}, {"key": "chest_tightness_character", "type": "selectboxes", "input": true, "label": "How would you describe the chest tightness? (Select all that apply)", "values": [{"label": "Pressure / heaviness", "value": "pressure"}, {"label": "Band-like squeezing", "value": "squeezing"}, {"label": "Constriction around the chest", "value": "band"}, {"label": "Sharp or stabbing pain", "value": "sharp"}, {"label": "Burning / warmth", "value": "burning"}, {"label": "Other feeling", "value": "other"}], "tableView": true, "confirm_label": "Chest tightness feels:", "customConditional": "show = data.asthma_symptom_overview?.chest_tightness === true", "optionsLabelPosition": "right"}, {"key": "chest_tightness_character_other_description", "type": "textarea", "input": true, "label": "Describe the sensation in your own words:", "tableView": true, "autoExpand": false, "placeholder": "e.g. 'like a weight on my chest'", "customConditional": "show = data.chest_tightness_character?.other === true"}, {"key": "chest_tightness_time_pattern", "type": "radio", "input": true, "label": "When is the chest tightness WORST?", "values": [{"label": "Overnight / wakes me up", "value": "night"}, {"label": "Early morning", "value": "morning"}, {"label": "During the day", "value": "day"}, {"label": "Evening", "value": "evening"}, {"label": "No predictable pattern", "value": "no_pattern"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Tightness worst at:", "customConditional": "show = data.asthma_symptom_overview?.chest_tightness === true", "optionsLabelPosition": "right"}, {"key": "chest_tightness_frequency", "type": "radio", "input": true, "label": "How often do you feel this tightness?", "values": [{"label": "Occasional episodes", "value": "occasional"}, {"label": "Several episodes per day", "value": "frequent"}, {"label": "Almost constant", "value": "constant"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Chest-tightness frequency:", "customConditional": "show = data.asthma_symptom_overview?.chest_tightness === true", "optionsLabelPosition": "right"}, {"key": "chest_tightness_triggers", "type": "selectboxes", "input": true, "label": "Do any of these things trigger or worsen the tightness? (Select all that apply)", "values": [{"label": "Exercise / physical activity", "value": "exercise"}, {"label": "Cold air", "value": "cold_air"}, {"label": "Emotional stress or anxiety", "value": "stress"}, {"label": "Strong smells / fumes", "value": "smells"}, {"label": "After lying down", "value": "lying_down"}, {"label": "Allergens (pollen, pets, etc.)", "value": "allergens"}, {"label": "Other trigger", "value": "other"}], "tableView": true, "confirm_label": "Tightness triggers:", "customConditional": "show = data.asthma_symptom_overview?.chest_tightness === true", "optionsLabelPosition": "right"}, {"key": "chest_tightness_triggers_other_description", "type": "textarea", "input": true, "label": "Describe the other trigger:", "tableView": true, "autoExpand": false, "placeholder": "e.g. cold drinks, singing, strong perfume…", "customConditional": "show = data.chest_tightness_triggers?.other === true"}, {"key": "chest_tightness_relief", "type": "radio", "input": true, "label": "Does anything relieve the tightness?", "values": [{"label": "Rescue inhaler helps", "value": "rescue_inhaler"}, {"label": "Rest or sitting upright", "value": "rest"}, {"label": "Nothing seems to help", "value": "nothing"}, {"label": "Haven't tried anything yet", "value": "not_tried"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Relief of tightness:", "customConditional": "show = data.asthma_symptom_overview?.chest_tightness === true", "optionsLabelPosition": "right"}, {"key": "chest_tightness_alarm_features", "type": "selectboxes", "input": true, "label": "While you feel chest tightness, do you notice any of the following? (Select all that apply)", "values": [{"label": "Pain spreading to arm, jaw, or neck", "value": "radiating_pain"}, {"label": "Sudden pounding, racing, or irregular heartbeat", "value": "palpitations"}, {"label": "Sweating or clamminess", "value": "sweats"}, {"label": "Nausea or vomiting", "value": "nausea_vomiting"}, {"label": "Feeling faint, dizzy, or light-headed", "value": "dizzy"}, {"label": "Shortness of breath when sitting or resting", "value": "sob_rest"}], "adminFlag": true, "confirm_label": "Alarm features present:", "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = data.asthma_symptom_overview?.chest_tightness === true"}, {"key": "none_of_the_above_chest_tightness_alarm_features", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a feature."}, "validate": {"custom": "valid = !!data.none_of_the_above_chest_tightness_alarm_features || _.some(_.values(data.chest_tightness_alarm_features));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.asthma_symptom_overview?.chest_tightness === true"}, {"key": "chest_tightness_alarm_features_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following cardiac alarm features:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "Alarm features NOT present:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.chest_tightness_alarm_features, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "heading_increased_rescue", "html": "</br><h4 class='mb-n2'>Using Rescue Inhaler More Often</h4>", "type": "content", "input": false, "label": "Rescue-Use Details", "tableView": false, "refreshOnChange": true, "customConditional": "show = data.asthma_symptom_overview?.increased_rescue === true"}, {"key": "symptom_start_increased_rescue", "data": {"custom": "values = [{label: `I've always needed my rescue inhaler this often`, value:`always`}].concat(data.durations_asthma)"}, "type": "select", "input": true, "label": "When did you start needing your rescue inhaler more than usual?", "widget": "html5", "dataSrc": "custom", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.asthma_symptom_overview?.increased_rescue && (_.sum(_.values(data.asthma_symptom_overview).map(Number)) < 2 || data.asthma_symptom_onset_pattern === 'separate_days')", "optionsLabelPosition": "right"}, {"key": "rescue_use_days_past_week", "type": "radio", "input": true, "label": "In the past 7 days, on how many days did you need your rescue inhaler?", "values": [{"label": "0 days", "value": "0"}, {"label": "1-2 days", "value": "1_2"}, {"label": "3-6 days", "value": "3_6"}, {"label": "Every day", "value": "7"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Rescue-inhaler days this week:", "customConditional": "show = data.asthma_symptom_overview?.increased_rescue === true", "optionsLabelPosition": "right"}, {"key": "rescue_use_puffs_per_day", "type": "radio", "input": true, "label": "On a day when you do use your rescue inhaler, about how many total puffs do you take?", "values": [{"label": "1-2 puffs", "value": "1_2"}, {"label": "3-5 puffs", "value": "3_5"}, {"label": "6-8 puffs", "value": "6_8"}, {"label": "More than 8 puffs", "value": "8_plus"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Puffs per rescue-day:", "customConditional": "show = data.asthma_symptom_overview?.increased_rescue === true", "optionsLabelPosition": "right"}, {"key": "rescue_use_night_this_week", "type": "radio", "input": true, "label": "In the past 7 days, did you need your rescue inhaler during the night?", "values": [{"label": "No, not at all", "value": "none"}, {"label": "Yes, 1-2 nights", "value": "1_2"}, {"label": "Yes, 3 or more nights", "value": "3_plus"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Night-time rescue use:", "customConditional": "show = data.asthma_symptom_overview?.increased_rescue === true", "optionsLabelPosition": "right"}, {"key": "rescue_inhaler_relief_quality", "type": "radio", "input": true, "label": "When you take the rescue inhaler, how much relief do you feel?", "values": [{"label": "Complete relief within minutes", "value": "complete"}, {"label": "Partial relief", "value": "partial"}, {"label": "Little or no relief", "value": "none"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Relief from rescue inhaler:", "customConditional": "show = data.asthma_symptom_overview?.increased_rescue === true", "optionsLabelPosition": "right"}, {"key": "rescue_canisters_past_year", "type": "select", "input": true, "label": "About how many full rescue inhaler canisters have you used in the past 12 months?", "data": {"values": [{"label": "0-1 canister", "value": "0_1"}, {"label": "2 canisters", "value": "2"}, {"label": "3-5 canisters", "value": "3_5"}, {"label": "6 or more", "value": "6_plus"}, {"label": "Not sure", "value": "not_sure"}]}, "dataSrc": "values", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Canisters in last year:", "customConditional": "show = data.asthma_symptom_overview?.increased_rescue === true", "optionsLabelPosition": "right"}, {"key": "heading_sleep_disturbance", "html": "<h4 class='mb-n2'>Night-time Sleep Disturbance</h4>", "type": "content", "input": false, "label": "Sleep-Disturbance Details", "tableView": false, "refreshOnChange": true, "customConditional": "show = data.asthma_symptom_overview?.sleep_disturbance === true"}, {"key": "symptom_start_sleep_disturbance", "data": {"custom": "values = [{label: `Night-time waking from breathing issues isn't new for me`, value:`always`}].concat(data.durations_asthma)"}, "type": "select", "input": true, "label": "When did you first start waking at night because of breathing problems?", "widget": "html5", "dataSrc": "custom", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.asthma_symptom_overview?.sleep_disturbance && (_.sum(_.values(data.asthma_symptom_overview).map(Number)) < 2 || data.asthma_symptom_onset_pattern === 'separate_days')", "optionsLabelPosition": "right"}, {"key": "sleep_waking_nights_per_week", "type": "radio", "input": true, "label": "In the past 7 days, on how many nights were you woken up by breathing problems?", "values": [{"label": "1 night", "value": "1"}, {"label": "2-3 nights", "value": "2_3"}, {"label": "4-6 nights", "value": "4_6"}, {"label": "Every night", "value": "7"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Nights woken this week:", "customConditional": "show = data.asthma_symptom_overview?.sleep_disturbance === true", "optionsLabelPosition": "right"}, {"key": "sleep_waking_trigger_symptoms", "type": "selectboxes", "input": true, "label": "What usually wakes you? (Select all that apply)", "values": [{"label": "Sudden shortness of breath", "value": "sob"}, {"label": "Coughing fit", "value": "cough"}, {"label": "Chest tightness", "value": "chest_tightness"}, {"label": "Wheezing sound", "value": "wheeze"}, {"label": "Unknown - I just wake up", "value": "unknown"}], "tableView": true, "confirm_label": "Triggering symptom(s):", "customConditional": "show = data.asthma_symptom_overview?.sleep_disturbance === true", "optionsLabelPosition": "right"}, {"key": "sleep_wake_use_rescue_inhaler", "type": "radio", "input": true, "label": "When you wake up at night, do you usually take your rescue inhaler?", "values": [{"label": "Yes, most of the time", "value": "yes"}, {"label": "Only occasionally", "value": "sometimes"}, {"label": "No, I don't need it", "value": "no"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Rescue inhaler on waking:", "customConditional": "show = data.asthma_symptom_overview?.sleep_disturbance === true", "optionsLabelPosition": "right"}, {"key": "heading_trigger_main", "html": "</br><h1 class='mb-2'>Asthma Trigger Details</h1>", "type": "content", "input": false, "label": "<PERSON><PERSON> Main Heading", "tableView": false, "refreshOnChange": false, "customConditional": "show = _.some(_.values(data.asthma_symptom_triggers))"}, {"key": "heading_trigger_exercise", "html": "</br><h4 class='mb-n2'>Trigger: Exercise / Physical Activity</h4>", "type": "content", "input": false, "label": "Exercise Trigger Heading", "tableView": false, "refreshOnChange": true, "customConditional": "show = data.asthma_symptom_triggers?.exercise === true"}, {"key": "exercise_trigger_activity_type", "type": "selectboxes", "input": true, "label": "Which activities usually bring on your asthma symptoms? (Select all that apply)", "values": [{"label": "Running / jogging", "value": "running"}, {"label": "Team sports (soccer, basketball)", "value": "team_sport"}, {"label": "High-intensity interval training", "value": "hiit"}, {"label": "Cycling / spin class", "value": "cycling"}, {"label": "Swimming", "value": "swimming"}, {"label": "Weight-lifting / strength work", "value": "weights"}, {"label": "Other activity", "value": "other"}], "tableView": true, "confirm_label": "Exercise types triggering asthma:", "customConditional": "show = data.asthma_symptom_triggers?.exercise === true", "optionsLabelPosition": "right"}, {"key": "exercise_trigger_activity_other", "type": "textarea", "input": true, "label": "Describe the other activity:", "tableView": true, "autoExpand": false, "placeholder": "e.g. hiking, dancing, skiing…", "customConditional": "show = data.exercise_trigger_activity_type?.other === true"}, {"key": "exercise_symptom_timing", "type": "radio", "input": true, "label": "When do the asthma symptoms typically start?", "values": [{"label": "During the first few minutes of exercise", "value": "early"}, {"label": "After 5-10 minutes of sustained exercise", "value": "middle"}, {"label": "Right after stopping exercise", "value": "post"}, {"label": "5-15 minutes AFTER finishing exercise", "value": "delayed"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Symptom timing:", "customConditional": "show = data.asthma_symptom_triggers?.exercise === true", "optionsLabelPosition": "right"}, {"key": "exercise_symptom_duration", "type": "radio", "input": true, "label": "How long do the breathing symptoms last once they start?", "values": [{"label": "Less than 5 minutes", "value": "under_5"}, {"label": "5-15 minutes", "value": "5_15"}, {"label": "15-30 minutes", "value": "15_30"}, {"label": "More than 30 minutes", "value": "over_30"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Symptom duration:", "customConditional": "show = data.asthma_symptom_triggers?.exercise === true", "optionsLabelPosition": "right"}, {"key": "exercise_pre_treat", "type": "radio", "input": true, "label": "Do you take your rescue inhaler BEFORE exercising?", "values": [{"label": "Yes - every workout", "value": "always"}, {"label": "Sometimes, for harder days", "value": "sometimes"}, {"label": "No - never or rarely", "value": "never"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Pre-exercise rescue use:", "customConditional": "show = data.asthma_symptom_triggers?.exercise === true", "optionsLabelPosition": "right"}, {"key": "exercise_pre_treat_recommendation", "type": "content", "input": false, "html": "<div style='border-left: 4px solid #0d6efd; background-color: #e7f1ff; padding: 10px; margin-bottom: 10px;'><strong>Recommendation:</strong> To help prevent exercise-induced asthma symptoms, use <strong>2&nbsp;puffs of your rescue inhaler 10-15&nbsp;minutes before you start exercising</strong>. This pre-treatment opens the airways and usually protects for about 3-4&nbsp;hours...</div>", "customConditional": "show = data.exercise_pre_treat && data.exercise_pre_treat !== 'always'"}, {"key": "exercise_pre_treat_recommendation_understanding", "type": "radio", "input": true, "label": "Do you understand this recommendation?", "values": [{"label": "I understand", "value": "understand"}, {"label": "I do not understand", "value": "do_not_understand"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.exercise_pre_treat && data.exercise_pre_treat !== 'always'", "optionsLabelPosition": "right"}, {"key": "exercise_environment", "type": "radio", "input": true, "label": "Where do you MOST OFTEN notice exercise-related asthma symptoms?", "values": [{"label": "Outdoors in cold or dry air", "value": "outdoor_cold"}, {"label": "Outdoors in warm/humid air", "value": "outdoor_humid"}, {"label": "Indoors (gym, arena, pool)", "value": "indoor"}, {"label": "Same in any setting", "value": "anywhere"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Environment where symptoms occur:", "customConditional": "show = data.asthma_symptom_triggers?.exercise === true", "optionsLabelPosition": "right"}, {"key": "heading_trigger_cold_air", "html": "</br><h4 class='mb-n2'>Trigger: Cold Air Exposure</h4>", "type": "content", "input": false, "label": "Cold-Air Trigger Heading", "tableView": false, "refreshOnChange": true, "customConditional": "show = data.asthma_symptom_triggers?.cold_air === true"}, {"key": "heading_trigger_infection", "html": "</br><h4 class='mb-n2'>Trigger: Cold / Flu-like Illness</h4>", "type": "content", "input": false, "label": "Infection Trigger Heading", "tableView": false, "refreshOnChange": true, "customConditional": "show = data.asthma_symptom_triggers?.infection === true"}, {"key": "cold_flu_symptom_list", "type": "selectboxes", "input": true, "label": "Which cold / flu-like symptoms have you had? (Select all that apply)", "values": [{"label": "Runny or stuffy nose", "value": "runny_nose"}, {"label": "Sore throat", "value": "sore_throat"}, {"label": "Headache", "value": "headache"}, {"label": "Muscle aches / fatigue", "value": "aches"}, {"label": "Chills or shivering", "value": "chills"}, {"label": "Nausea or upset stomach", "value": "gi"}, {"label": "Other cold / flu symptom", "value": "other"}], "tableView": true, "confirm_label": "Cold / flu symptoms:", "customConditional": "show = data.asthma_symptom_triggers?.infection === true", "optionsLabelPosition": "right"}, {"key": "cold_flu_symptom_other_description", "type": "textarea", "input": true, "label": "Describe the other symptom:", "tableView": true, "autoExpand": false, "placeholder": "e.g. sinus pressure, ear pain…", "customConditional": "show = data.cold_flu_symptom_list?.other === true"}, {"key": "cold_cough_onset", "type": "radio", "input": true, "label": "Did your cough start at the same time as your cold / flu symptoms?", "values": [{"label": "Yes - started together", "value": "same_time"}, {"label": "No - cough started later", "value": "later"}, {"label": "I didn't develop a cough", "value": "no_cough"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Cough onset vs cold:", "customConditional": "show = data.asthma_symptom_triggers?.infection === true", "optionsLabelPosition": "right"}, {"key": "cold_cough_progression", "type": "radio", "input": true, "label": "Since it began, is the cough:", "values": [{"label": "Getting better", "value": "better"}, {"label": "About the same", "value": "same"}, {"label": "Getting worse", "value": "worse"}, {"label": "Not applicable (no cough)", "value": "na"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Cough progression:", "customConditional": "show = data.asthma_symptom_triggers?.infection === true", "optionsLabelPosition": "right"}, {"key": "cold_fever_presence", "type": "radio", "input": true, "label": "Have you had a fever during this cold / flu episode?", "values": [{"label": "Yes - measured ≥38 °C / 100.4 °F", "value": "measured"}, {"label": "Yes - felt feverish but not measured", "value": "felt_only"}, {"label": "No fever", "value": "none"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Fever during cold:", "customConditional": "show = data.asthma_symptom_triggers?.infection === true", "optionsLabelPosition": "right"}, {"key": "cold_fever_current_status", "type": "radio", "input": true, "label": "Are you still having fever or chills now?", "values": [{"label": "No - fever resolved", "value": "resolved"}, {"label": "Yes - ongoing fever", "value": "ongoing"}, {"label": "Unsure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Current fever status:", "customConditional": "show = data.cold_fever_presence !== 'none' && data.asthma_symptom_triggers?.infection === true", "optionsLabelPosition": "right"}, {"key": "cold_fever_duration", "type": "radio", "input": true, "label": "<PERSON><PERSON><PERSON>, how long has (or did) the fever last?", "values": [{"label": "Less than 24 hours", "value": "under_1d"}, {"label": "1-2 days", "value": "1_2d"}, {"label": "3-4 days", "value": "3_4d"}, {"label": "5-7 days", "value": "5_7d"}, {"label": "More than a week", "value": "over_7d"}, {"label": "Still ongoing", "value": "ongoing"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Fever duration:", "customConditional": "show = data.cold_fever_presence !== 'none' && data.asthma_symptom_triggers?.infection === true", "optionsLabelPosition": "right"}, {"key": "heading_flare_context", "type": "content", "input": false, "label": "Flare Context Heading", "html": "</br><h4 class='mb-n2'>Current Flare Symptoms</h4>", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.asthma_symptom_overview && _.some(_.values(data.asthma_symptom_overview)) && !data.none_of_the_above_asthma_symptom_overview"}, {"key": "flare_similarity_to_prior", "type": "radio", "input": true, "label": "Do these symptoms feel similar to your past asthma flare-ups?", "values": [{"label": "Yes – this feels like my usual flare", "value": "typical"}, {"label": "Somewhat – similar but not identical", "value": "somewhat"}, {"label": "No – this feels different or unusual", "value": "different"}, {"label": "I'm not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Similarity to past flares:", "optionsLabelPosition": "right", "customConditional": "show = data.asthma_symptom_overview && _.some(_.values(data.asthma_symptom_overview)) && !data.none_of_the_above_asthma_symptom_overview"}, {"key": "flare_severity_vs_prior", "type": "radio", "input": true, "label": "Compared with your usual flares, is this one:", "values": [{"label": "Milder than usual", "value": "milder"}, {"label": "About the same", "value": "same"}, {"label": "Worse / more severe", "value": "worse"}], "validate": {"required": true}, "tableView": true, "confirm_label": "How this flare compares:", "optionsLabelPosition": "right", "customConditional": "show = data.flare_similarity_to_prior !== undefined && data.asthma_symptom_overview && _.some(_.values(data.asthma_symptom_overview)) && !data.none_of_the_above_asthma_symptom_overview"}, {"key": "rescue_inhaler_supply_status", "type": "radio", "input": true, "label": "Do you have a rescue inhaler (blue puffer) on hand right now?", "values": [{"label": "Yes – plenty left in the canister", "value": "full"}, {"label": "Yes – but it's almost empty", "value": "low"}, {"label": "No – I have run out", "value": "out"}, {"label": "I don't have a rescue inhaler", "value": "none_owned"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Rescue inhaler supply:", "optionsLabelPosition": "right", "customConditional": "show = data.flare_severity_vs_prior !== undefined && data.asthma_symptom_overview && _.some(_.values(data.asthma_symptom_overview)) && !data.none_of_the_above_asthma_symptom_overview"}, {"key": "rescue_used_this_flare", "type": "radio", "input": true, "label": "Since these symptoms began, have you taken your rescue inhaler?", "values": [{"label": "Yes – at least once", "value": "yes"}, {"label": "No – I haven’t used it", "value": "no"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Rescue inhaler used:", "optionsLabelPosition": "right", "customConditional": "show = (data.rescue_inhaler_supply_status === 'full' || data.rescue_inhaler_supply_status === 'low')"}, {"key": "rescue_effect_during_flare", "type": "radio", "input": true, "label": "If you used it, did it help?", "values": [{"label": "Yes – symptoms eased quickly", "value": "relief"}, {"label": "Only partial relief", "value": "partial"}, {"label": "No improvement", "value": "none"}, {"label": "Not applicable – didn’t use", "value": "na"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Rescue inhaler effect:", "optionsLabelPosition": "right", "customConditional": "show = data.rescue_used_this_flare !== undefined"}, {"key": "maintenance_inhaler_current", "type": "radio", "input": true, "label": "Do you take a daily **preventer** inhaler?", "values": [{"label": "Yes – steroid-only inhaler", "value": "ics_only"}, {"label": "Yes – two-in-one inhaler (steroid + long-acting)", "value": "ics_laba_combo"}, {"label": "Yes – long-acting relaxer **without** steroid", "value": "laba_lama_only"}, {"label": "Yes – another preventer (injection, pill, etc.)", "value": "other_controller"}, {"label": "I'm not sure what type it is", "value": "unknown"}, {"label": "No – I’m not on a daily preventer", "value": "none"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Current daily preventer:", "optionsLabelPosition": "right", "customConditional": "show = data.rescue_effect_during_flare !== undefined"}, {"key": "maintenance_adherence", "type": "radio", "input": true, "label": "In the past month, how often did you remember your daily preventer?", "values": [{"label": "Every day / never miss", "value": "daily"}, {"label": "Most days (missed 1–3)", "value": "most"}, {"label": "Some days (missed >3)", "value": "some"}, {"label": "Rarely / stopped completely", "value": "rare"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Preventer adherence:", "optionsLabelPosition": "right", "customConditional": "show = data.maintenance_inhaler_current && data.maintenance_inhaler_current !== 'none'"}, {"key": "maintenance_worsening_despite_use", "type": "radio", "input": true, "label": "Even with your daily preventer, are these symptoms getting worse?", "values": [{"label": "Yes – clearly worsening", "value": "worse"}, {"label": "No – staying about the same", "value": "same"}, {"label": "Not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Worsening despite preventer:", "optionsLabelPosition": "right", "customConditional": "show = data.maintenance_adherence !== undefined"}]}