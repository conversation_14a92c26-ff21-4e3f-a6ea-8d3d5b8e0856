{"components": [{"key": "content", "html": "<h2>Instructions</h2><p>Please complete the following questionnaire about your interest in Twinrix Vaccine.</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "header_vaccine_records", "html": "<h4>General Questions&nbsp;</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "vaccine_records", "type": "radio", "input": true, "label": "Do you have access to or can you obtain access to your vaccine records?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "vaccine_indication", "type": "radio", "input": true, "label": "What reason do you want <PERSON>rix vaccination?", "inline": false, "values": [{"label": "Protect against <strong>Hepatitis A & B</strong>", "value": "protect_hep_a_b", "shortcut": ""}, {"label": "Protect against <strong>Hepatitis A</strong>", "value": "protect_hep_a", "shortcut": ""}, {"label": "Protect against <strong>Hepatitis B</strong>", "value": "protect_hep_b", "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "vaccine_indication_other", "type": "textarea", "input": true, "label": "You selected 'other' - please specify your reason for Twinrix vaccination:", "tableView": true, "autoExpand": false, "customConditional": "show = data.vaccine_indication == 'other';"}, {"key": "hep_b_header", "html": "<h4>Hepatitis B Vaccination&nbsp;</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "hep_b_vaccinated", "type": "radio", "input": true, "label": "Have you had Hepatitis B Vaccination before?", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "I don't know", "value": "doesnt_know", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "review_record_prompt", "html": "Please review your vaccine records before continuing and confirm your vaccination history.", "type": "content", "input": false, "label": "Content", "tableView": true, "customConditional": "show = data.hep_b_vaccinated == 'doesnt_know' && data.vaccine_records == 'true';", "refreshOnChange": false}, {"key": "number_of_hep_b_vaccines", "type": "radio", "input": true, "label": "Do you know how many doses of Hepatitis B vaccine you recieved?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No/I don't know", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.hep_b_vaccinated == 'yes';", "optionsLabelPosition": "right"}, {"key": "quantity_hep_b_doses", "type": "radio", "input": true, "label": "How many doses of Hepatitis B Vaccine have you recieved?", "inline": false, "values": [{"label": "1 injection", "value": "1_dose", "shortcut": ""}, {"label": "2 injections", "value": "2_doses", "shortcut": ""}, {"label": "3 injections", "value": "3_doses", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.number_of_hep_b_vaccines == true;", "optionsLabelPosition": "right"}, {"key": "two_doses_before_16", "type": "radio", "input": true, "label": "Did you have 2 doses of hepatitis B completed before age 16?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No/Don't Know", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.quantity_hep_b_doses != '1_dose' && data.hep_b_vaccinated == 'yes' && data.number_of_hep_b_vaccines == true;", "optionsLabelPosition": "right"}, {"key": "hep_b_seperated_4_months", "type": "radio", "input": true, "label": "Were your two doses of Hepatitis B vaccine seperated by 4 months or more?", "inline": false, "values": [{"label": "Yes", "value": "hep_b_not_indicated", "shortcut": ""}, {"label": "No", "value": "booster_hep_b_indicated", "shortcut": ""}, {"label": "Don't Know", "value": "dont_know", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.two_doses_before_16 == true;", "optionsLabelPosition": "right"}, {"key": "hep_a_header", "html": "<h3>Hepatitis A Vaccination&nbsp;</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "prior_hep_a_vaccination", "type": "radio", "input": true, "label": "Have you had any doses of a Hepatitis A vaccine before?", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "Don't Know", "value": "dont_know", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "quantity_hep_a_doses", "type": "radio", "input": true, "label": "How many doses of Hepatitis A Vaccine have you recieved?", "inline": false, "values": [{"label": "1 injection", "value": "1_dose", "shortcut": ""}, {"label": "2 injections", "value": "2_doses", "shortcut": ""}, {"label": "3 injections", "value": "3_doses", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.prior_hep_a_vaccination == 'yes';", "optionsLabelPosition": "right"}, {"key": "current_vaccination_contraindications", "html": "<h3>Other Considerations&nbsp;</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "vaccine_contraindication", "type": "radio", "input": true, "label": "Do you have a fever or are recovering from a recent illness?", "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}]}