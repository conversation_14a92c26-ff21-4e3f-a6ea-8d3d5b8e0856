{"components": [{"key": "heading_master_symptom_list", "html": "<h1><center><strong>Symptom Review</strong></h1><center><p>To expedite your care, please answer the following questions to the best of your abilities. &nbsp;We have a few follow up questions about symptoms you might be experiencing.&nbsp;</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "heading_associated_symptoms", "html": "</br><h4>General Symptoms&nbsp;</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "calculateValue": "value = data.sku && (!_.includes(['fem_pn_vswb', 'fem_pn_uti', 'std_pn_uti', 'asthma_medication_beta'], data.sku) || _.includes(['add_urine_bloodwork_sti', 'add_urine_sti'], data.request_sti_testing));", "refreshOnChange": false}, {"key": "red_flag_general_symptoms", "type": "selectboxes", "input": true, "label": "Do you currently have any of the following symptoms?", "values": [{"label": "Fever", "value": "fever", "shortcut": ""}, {"label": "Lost My Appetite", "value": "appetite_loss", "shortcut": ""}, {"label": "<PERSON>well", "value": "unwell", "shortcut": ""}, {"label": "Nausea or vomiting", "value": "nausea_vomiting", "shortcut": ""}], "adminFlag": true, "inputType": "checkbox", "tableView": true, "optionsLabelPosition": "right"}, {"key": "no_red_flag_general_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = !!data.no_red_flag_general_symptoms || !!_.some(_.values(data.red_flag_general_symptoms));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "general_symptoms_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following general symptoms:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "I DO NOT have the following general symptoms:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.red_flag_general_symptoms, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "heading_fever", "html": "<h4>Fever Details</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.red_flag_general_symptoms && data.red_flag_general_symptoms.fever"}, {"key": "fever_duration", "data": {"values": [{"label": "Less than 24 hours", "value": "less_than_24_hours"}, {"label": "1 day", "value": "1_day"}, {"label": "2 days", "value": "2_days"}, {"label": "3 days", "value": "3_days"}, {"label": "4 days", "value": "4_days"}, {"label": "5 days", "value": "5_days"}, {"label": "6 days", "value": "6_days"}, {"label": "7 days", "value": "7_days"}, {"label": "8 days", "value": "8_days"}, {"label": "9 days", "value": "9_days"}, {"label": "10 days", "value": "10_days"}, {"label": "More than 10 days", "value": "more_than_10_days"}]}, "type": "select", "input": true, "label": "How long have you had the fever?", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.red_flag_general_symptoms && data.red_flag_general_symptoms.fever", "optionsLabelPosition": "right"}, {"key": "fever_measured", "type": "radio", "input": true, "label": "Please select which best describes your fever:", "values": [{"label": "I definitely have a fever and measured it with a thermometer", "value": "definite_fever_measured"}, {"label": "I definitely have a fever but haven't measured it", "value": "definitely_unmeasured"}, {"label": "I think I have a fever but haven't measured it", "value": "suspected_unmeasured"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.red_flag_general_symptoms && data.red_flag_general_symptoms.fever && !!data.fever_duration", "optionsLabelPosition": "right"}, {"key": "current_temperature", "data": {"values": [{"label": "I haven't measured it today", "value": "haven't_measured_today"}, {"label": "I haven't measured it at all", "value": "never_measured"}, {"label": "Less than 35.0°C / 95.0°F", "value": "Below_35.0C_95.0F"}, {"label": "35.0-35.4°C / 95.0-95.6°F", "value": "35.0-35.4C_95.0-95.6F"}, {"label": "35.5-35.9°C / 95.7-96.6°F", "value": "35.5-35.9C_95.7-96.6F"}, {"label": "36.0-36.4°C / 96.7-97.5°F", "value": "36.0-36.4C_96.7-97.5F"}, {"label": "36.5-36.9°C / 97.6-98.4°F", "value": "36.5-36.9C_97.6-98.4F"}, {"label": "37.0-37.4°C / 98.5-99.3°F", "value": "37.0-37.4C_98.5-99.3F"}, {"label": "37.5-37.9°C / 99.4-100.2°F", "value": "37.5-37.9C_99.4-100.2F"}, {"label": "38.0-38.4°C / 100.3-101.1°F", "value": "38.0-38.4C_100.3-101.1F"}, {"label": "38.5-38.9°C / 101.2-102.0°F", "value": "38.5-38.9C_101.2-102.0F"}, {"label": "39.0-39.4°C / 102.1-102.9°F", "value": "39.0-39.4C_102.1-102.9F"}, {"label": "39.5-39.9°C / 103.0-103.8°F", "value": "39.5-39.9C_103.0-103.8F"}, {"label": "40.0-40.4°C / 103.9-104.7°F", "value": "40.0-40.4C_103.9-104.7F"}, {"label": "40.5-40.9°C / 104.8-105.6°F", "value": "40.5-40.9C_104.8-105.6F"}, {"label": "41.0°C / 105.7°F and above", "value": "Above_41.0C_105.7F"}]}, "type": "select", "input": true, "label": "What is your current temperature?", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.red_flag_general_symptoms && data.red_flag_general_symptoms.fever && !!data.fever_measured", "optionsLabelPosition": "right"}, {"key": "maximum_temperature", "data": {"values": [{"label": "I haven't measured it at all", "value": "never_measured"}, {"label": "I don't remember", "value": "doesn't_remember"}, {"label": "Less than 35.0°C / 95.0°F", "value": "Below_35.0C_95.0F"}, {"label": "35.0-35.4°C / 95.0-95.6°F", "value": "35.0-35.4C_95.0-95.6F"}, {"label": "35.5-35.9°C / 95.7-96.6°F", "value": "35.5-35.9C_95.7-96.6F"}, {"label": "36.0-36.4°C / 96.7-97.5°F", "value": "36.0-36.4C_96.7-97.5F"}, {"label": "36.5-36.9°C / 97.6-98.4°F", "value": "36.5-36.9C_97.6-98.4F"}, {"label": "37.0-37.4°C / 98.5-99.3°F", "value": "37.0-37.4C_98.5-99.3F"}, {"label": "37.5-37.9°C / 99.4-100.2°F", "value": "37.5-37.9C_99.4-100.2F"}, {"label": "38.0-38.4°C / 100.3-101.1°F", "value": "38.0-38.4C_100.3-101.1F"}, {"label": "38.5-38.9°C / 101.2-102.0°F", "value": "38.5-38.9C_101.2-102.0F"}, {"label": "39.0-39.4°C / 102.1-102.9°F", "value": "39.0-39.4C_102.1-102.9F"}, {"label": "39.5-39.9°C / 103.0-103.8°F", "value": "39.5-39.9C_103.0-103.8F"}, {"label": "40.0-40.4°C / 103.9-104.7°F", "value": "40.0-40.4C_103.9-104.7F"}, {"label": "40.5-40.9°C / 104.8-105.6°F", "value": "40.5-40.9C_104.8-105.6F"}, {"label": "41.0°C / 105.7°F and above", "value": "Above_41.0C_105.7F"}]}, "type": "select", "input": true, "label": "What was your highest temperature?", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.red_flag_general_symptoms && data.red_flag_general_symptoms.fever && !!data.current_temperature", "optionsLabelPosition": "right"}, {"key": "taken_medication", "type": "radio", "input": true, "label": "Have you taken Tylenol (acetaminophen) or Advil (ibuprofen) or similar cold medication (i.e. Tylenol Complete) within the last 12 hours?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.red_flag_general_symptoms && data.red_flag_general_symptoms.fever && !!data.maximum_temperature", "optionsLabelPosition": "right"}, {"key": "fever_status", "type": "radio", "input": true, "label": "How would you describe your fever?", "values": [{"label": "It's getting better with time", "value": "improving"}, {"label": "It's not going away", "value": "persistent"}, {"label": "It's getting worse", "value": "worsening"}, {"label": "It's staying the same", "value": "unchanged"}, {"label": "I'm not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.red_flag_general_symptoms && data.red_flag_general_symptoms.fever && !!data.taken_medication", "optionsLabelPosition": "right"}, {"key": "fever_accompanying_symptoms", "type": "selectboxes", "input": true, "label": "Are you experiencing any of the following symptoms along with the fever?", "values": [{"label": "<PERSON><PERSON>", "value": "chills"}, {"label": "Sweating", "value": "sweating"}, {"label": "Headache", "value": "headache"}, {"label": "Muscle aches", "value": "muscle_aches"}, {"label": "Fatigue", "value": "fatigue"}], "inputType": "checkbox", "tableView": true, "customConditional": "show = data.red_flag_general_symptoms && data.red_flag_general_symptoms.fever && !!data.fever_status", "optionsLabelPosition": "right"}, {"key": "no_fever_accompanying_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = !!data.no_fever_accompanying_symptoms || !!_.some(_.values(data.fever_accompanying_symptoms));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.red_flag_general_symptoms && data.red_flag_general_symptoms.fever  && !!data.fever_status"}, {"key": "heading_appetite_loss", "html": "</br><h4>Appetite Changes</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.red_flag_general_symptoms && data.red_flag_general_symptoms.appetite_loss"}, {"key": "appetite_loss_duration", "data": {"values": [{"label": "Less than 24 hours", "value": "less_than_24_hours"}, {"label": "1 day", "value": "1_day"}, {"label": "2 days", "value": "2_days"}, {"label": "3 days", "value": "3_days"}, {"label": "4 days", "value": "4_days"}, {"label": "5 days", "value": "5_days"}, {"label": "6 days", "value": "6_days"}, {"label": "7 days", "value": "7_days"}, {"label": "8 days", "value": "8_days"}, {"label": "9 days", "value": "9_days"}, {"label": "10 days", "value": "10_days"}, {"label": "More than 10 days", "value": "more_than_10_days"}]}, "type": "select", "input": true, "label": "How long have you experienced loss of appetite?", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.red_flag_general_symptoms && data.red_flag_general_symptoms.appetite_loss"}, {"key": "appetite_loss_severity", "type": "radio", "input": true, "label": "How would you describe your change in appetite?", "values": [{"label": "My appetite is always low and this isn't new", "value": "longstanding_not_new"}, {"label": "I'm drinking fluids and eating some food but less than normal", "value": "fluids_less_solids"}, {"label": "I'm drinking fluids and not eating any food", "value": "fluids_no_solids"}, {"label": "I'm not drinking fluids or eating any food", "value": "no_fluids_or_solids"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.red_flag_general_symptoms && data.red_flag_general_symptoms.appetite_loss && !!data.appetite_loss_duration"}, {"key": "heading_nausea_details", "html": "</br><h4>Na<PERSON>a Details</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.red_flag_general_symptoms && data.red_flag_general_symptoms.nausea_vomiting"}, {"key": "nausea_duration", "data": {"values": [{"label": "Less than 24 hours", "value": "less_than_24_hours"}, {"label": "1 day", "value": "1_day"}, {"label": "2 days", "value": "2_days"}, {"label": "3 days", "value": "3_days"}, {"label": "4 days", "value": "4_days"}, {"label": "5 days", "value": "5_days"}, {"label": "6 days", "value": "6_days"}, {"label": "7 days", "value": "7_days"}, {"label": "8 days", "value": "8_days"}, {"label": "9 days", "value": "9_days"}, {"label": "10 days", "value": "10_days"}, {"label": "More than 10 days", "value": "more_than_10_days"}, {"label": "Months", "value": "months"}, {"label": "Years", "value": "years"}]}, "type": "select", "input": true, "label": "How long have you been experiencing nausea?", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.red_flag_general_symptoms && data.red_flag_general_symptoms.nausea_vomiting"}, {"key": "nausea_severity", "type": "radio", "input": true, "label": "How would you rate the severity of your nausea?", "values": [{"label": "Mild", "value": "mild"}, {"label": "Moderate", "value": "moderate"}, {"label": "Severe", "value": "severe"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.red_flag_general_symptoms && data.red_flag_general_symptoms.nausea_vomiting && !!data.nausea_duration"}, {"key": "nausea_triggers", "type": "selectboxes", "input": true, "label": "What triggers your nausea?", "values": [{"label": "Eating food", "value": "eating_food"}, {"label": "Stress", "value": "stress"}, {"label": "Motion (i.e. walking, driving, rolling in bed)", "value": "motion"}, {"label": "I can't seem to identify a trigger", "value": "no_clear_trigger"}], "tableView": true, "customConditional": "show = data.red_flag_general_symptoms && data.red_flag_general_symptoms.nausea_vomiting && !!data.nausea_severity"}, {"key": "no_nausea_triggers", "type": "checkbox", "input": true, "label": "None of the above", "validate": {"custom": "valid = !!data.no_nausea_triggers || !!_.some(_.values(data.nausea_triggers));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.red_flag_general_symptoms && data.red_flag_general_symptoms.nausea_vomiting && !!data.nausea_severity"}, {"key": "nausea_associated_symptoms", "type": "selectboxes", "input": true, "label": "Are you experiencing any of the following symptoms along with nausea?", "values": [{"label": "Headache", "value": "headache"}, {"label": "Dizziness", "value": "dizziness"}, {"label": "Abdominal pain", "value": "abdominal_pain"}, {"label": "Vomiting", "value": "vomiting"}], "tableView": true, "customConditional": "show = data.red_flag_general_symptoms && data.red_flag_general_symptoms.nausea_vomiting && (!!data.nausea_triggers || data.no_nausea_triggers === true)"}, {"key": "no_nausea_associated_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = !!data.no_nausea_associated_symptoms || !!_.some(_.values(data.nausea_associated_symptoms));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.red_flag_general_symptoms && data.red_flag_general_symptoms.nausea_vomiting && (!!data.nausea_triggers || data.no_nausea_triggers === true)"}, {"key": "abdominal_pelvic_symptoms_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following abdominal/pelvic symptoms:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "I DO NOT have the following abdominal/pelvic symptoms:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.abdominal_pelvic_symptom_overview, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "motion_specific_triggers", "type": "selectboxes", "input": true, "label": "What specific movements make you feel nauseous?", "values": [{"label": "Rolling over in bed", "value": "rolling_over_in_bed"}, {"label": "Getting up from lying down to sitting", "value": "lying_to_sitting"}, {"label": "Standing up from a sitting position", "value": "sitting_to_standing"}, {"label": "Driving a vehicle", "value": "while_driving"}, {"label": "Walking", "value": "while_walking"}, {"label": "Turning my head quickly", "value": "turning_head"}], "tableView": true, "customConditional": "show = data.red_flag_general_symptoms && data.red_flag_general_symptoms.nausea_vomiting && !!data.nausea_associated_symptoms && data.nausea_triggers.motion"}, {"key": "vomiting_blood_presence", "type": "radio", "input": true, "label": "Did you notice any blood in your vomit?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "I don't think so", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.red_flag_general_symptoms && data.red_flag_general_symptoms.nausea_vomiting && !!data.nausea_associated_symptoms && data.nausea_associated_symptoms.vomiting"}, {"key": "type_of_blood_in_vomit", "type": "selectboxes", "input": true, "label": "Did your vomit look like it contained any of the following?", "values": [{"label": "Bright red blood", "value": "bright_red"}, {"label": "Dark red blood", "value": "dark_red"}, {"label": "Black coffee grounds", "value": "coffee_ground"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.red_flag_general_symptoms && data.red_flag_general_symptoms.nausea_vomiting && !!data.vomiting_blood_presence && data.vomiting_blood_presence == 'yes'", "optionsLabelPosition": "right"}, {"key": "no_blood_in_vomit", "type": "checkbox", "input": true, "label": "None/Not sure", "validate": {"custom": "valid = !!data.no_blood_in_vomit || !!_.some(_.values(data.type_of_blood_in_vomit));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.red_flag_general_symptoms && data.red_flag_general_symptoms.nausea_vomiting && !!data.vomiting_blood_presence && data.vomiting_blood_presence == 'yes'"}, {"key": "heading_urinary symptoms", "html": "</br><h4>Urinary Symptoms&nbsp;</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "calculateValue": "value = data.sku && (!_.includes(['fem_pn_vswb', 'fem_pn_uti', 'std_pn_uti', 'asthma_medication_beta'], data.sku) || _.includes(['add_urine_bloodwork_sti', 'add_urine_sti'], data.request_sti_testing));", "refreshOnChange": false}, {"key": "changes_urination", "type": "radio", "input": true, "label": "Have you noticed any changes in how you're urinating (discomfort, urinating frequently, getting up at nighttime, or feeling that you haven't properly emptied your bladder)?", "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "urinary_symptom_overview", "type": "selectboxes", "input": true, "label": "Are you currently experiencing any of the following symptoms:", "values": [{"label": "Pain or discomfort with urination", "value": "dysuria", "shortcut": ""}, {"label": "Urinating more frequently than my normal", "value": "urinary_frequency", "shortcut": ""}, {"label": "Urgency to urinate", "value": "urinary_urgency", "shortcut": ""}, {"label": "Stream is weaker than normal", "value": "urinary_stream", "shortcut": ""}, {"label": "Leaking urine or unable to urinate", "value": "incontinent", "shortcut": ""}, {"label": "Noticed blood in my urine", "value": "hematuria", "shortcut": ""}, {"label": "Haven't fully emptied my bladder", "value": "incomplete_bladder_emptying", "shortcut": ""}, {"label": "Discharge from my penis", "value": "penile_discharge", "shortcut": "", "customConditional": "show = data.sex == 'male'"}, {"label": "Other urinary symptoms", "value": "other", "shortcut": ""}], "adminFlag": true, "inputType": "checkbox", "tableView": true, "customConditional": "show = data.changes_urination == 'yes'", "optionsLabelPosition": "right"}, {"key": "no_urinary_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = !!data.no_urinary_symptoms || !!_.some(_.values(data.urinary_symptom_overview));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.changes_urination == 'yes'"}, {"key": "urinary_symptoms_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following urinary symptoms:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "I DO NOT have the following urinary symptoms:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.urinary_symptom_overview, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "urinary_symptom_onset_pattern", "type": "radio", "input": true, "label": "Did your urinary symptoms start around the same time or over separate days?", "values": [{"label": "All symptoms started around the same time", "value": "same_time", "shortcut": ""}, {"label": "Symptoms started on separate days", "value": "separate_days", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.changes_urination == 'yes' && data.urinary_symptom_overview && _.sum(_.values(data.urinary_symptom_overview).map(Number)) >= 2", "optionsLabelPosition": "right"}, {"key": "all_urinary_symptoms_start", "data": {"values": [{"label": "Doesn't apply", "value": "doesn't_apply"}, {"label": "Last night", "value": "last_night"}, {"label": "Today", "value": "today"}, {"label": "1 day ago", "value": "1_day_ago"}, {"label": "2 days ago", "value": "2_days_ago"}, {"label": "3 days ago", "value": "3_days_ago"}, {"label": "4 days ago", "value": "4_days_ago"}, {"label": "5 days ago", "value": "5_days_ago"}, {"label": "6 days ago", "value": "6_days_ago"}, {"label": "7 days ago", "value": "7_days_ago"}, {"label": "8 days ago", "value": "8_days_ago"}, {"label": "9 days ago", "value": "9_days_ago"}, {"label": "10 days ago", "value": "10_days_ago"}, {"label": "11 days ago", "value": "11_days_ago"}, {"label": "12 days ago", "value": "12_days_ago"}, {"label": "13 days ago", "value": "13_days_ago"}, {"label": "14 days ago", "value": "14_days_ago"}, {"label": "14-21 days ago", "value": "14-21_days"}, {"label": "21-28 days ago", "value": "21-28_days"}, {"label": "28-60 days ago", "value": "28-60_days"}, {"label": "60-90 days ago", "value": "60-90_days"}, {"label": "90 days - 1 year ago", "value": "90_days-1_year"}, {"label": "1+ year ago", "value": "1+_year"}]}, "type": "select", "input": true, "label": "When did your urinary symptoms start?", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.changes_urination == 'yes' && data.urinary_symptom_overview && data.urinary_symptom_onset_pattern == 'same_time' && !data.no_urinary_symptoms", "optionsLabelPosition": "right"}, {"key": "heading_dysuria", "html": "</br><h4>Discomfort with Urination</h4>", "type": "content", "input": false, "label": "<PERSON><PERSON><PERSON>", "tableView": false, "refreshOnChange": true, "customConditional": "show = data.changes_urination == 'yes' && data.urinary_symptom_overview.dysuria"}, {"key": "symptom_start_dysuria", "data": {"values": [{"label": "Doesn't apply", "value": "doesn't_apply"}, {"label": "Last night", "value": "last_night"}, {"label": "Today", "value": "today"}, {"label": "1 day ago", "value": "1_day_ago"}, {"label": "2 days ago", "value": "2_days_ago"}, {"label": "3 days ago", "value": "3_days_ago"}, {"label": "4 days ago", "value": "4_days_ago"}, {"label": "5 days ago", "value": "5_days_ago"}, {"label": "6 days ago", "value": "6_days_ago"}, {"label": "7 days ago", "value": "7_days_ago"}, {"label": "8 days ago", "value": "8_days_ago"}, {"label": "9 days ago", "value": "9_days_ago"}, {"label": "10 days ago", "value": "10_days_ago"}, {"label": "11 days ago", "value": "11_days_ago"}, {"label": "12 days ago", "value": "12_days_ago"}, {"label": "13 days ago", "value": "13_days_ago"}, {"label": "14 days ago", "value": "14_days_ago"}, {"label": "14-21 days ago", "value": "14-21_days"}, {"label": "21-28 days ago", "value": "21-28_days"}, {"label": "28-60 days ago", "value": "28-60_days"}, {"label": "60-90 days ago", "value": "60-90_days"}, {"label": "90 days - 1 year ago", "value": "90_days-1_year"}, {"label": "1+ year ago", "value": "1+_year"}]}, "type": "select", "input": true, "label": "When did your pain/discomfort with urination start?", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.changes_urination == 'yes' && data.urinary_symptom_overview.dysuria && (_.sum(_.values(data.urinary_symptom_overview).map(Number)) < 2 || data.urinary_symptom_onset_pattern == 'separate_days')", "optionsLabelPosition": "right"}, {"key": "pain_description_urination", "type": "selectboxes", "input": true, "label": "Please describe how you experience pain or discomfort during urination. Select all that apply:", "values": [{"label": "Burning sensation", "value": "burning"}, {"label": "<PERSON><PERSON>", "value": "stings"}, {"label": "Sharp or stabbing pain", "value": "sharp_stabbing"}, {"label": "Dull ache", "value": "dull_ache"}, {"label": "Pressure or heaviness", "value": "pressure_heaviness"}, {"label": "Cramping", "value": "cramping"}, {"label": "Throbbing pain", "value": "throbbing"}], "inputType": "checkbox", "tableView": true, "customConditional": "show = data.changes_urination == 'yes' && data.urinary_symptom_overview.dysuria", "optionsLabelPosition": "right"}, {"key": "heading_urinary_frequency", "html": "</br><h4>Frequent Urination</h4>", "type": "content", "input": false, "label": "Urinary Frequency Details", "tableView": false, "refreshOnChange": true, "customConditional": "show = data.changes_urination == 'yes' && data.urinary_symptom_overview.urinary_frequency"}, {"key": "symptom_start_urinary_frequency", "data": {"values": [{"label": "I've always gone frequently and there has been no change with my current symptoms", "value": "no_change"}, {"label": "Last night", "value": "last_night"}, {"label": "Today", "value": "today"}, {"label": "1 day ago", "value": "1_day_ago"}, {"label": "2 days ago", "value": "2_days_ago"}, {"label": "3 days ago", "value": "3_days_ago"}, {"label": "4 days ago", "value": "4_days_ago"}, {"label": "5 days ago", "value": "5_days_ago"}, {"label": "6 days ago", "value": "6_days_ago"}, {"label": "7 days ago", "value": "7_days_ago"}, {"label": "8 days ago", "value": "8_days_ago"}, {"label": "9 days ago", "value": "9_days_ago"}, {"label": "10 days ago", "value": "10_days_ago"}, {"label": "11 days ago", "value": "11_days_ago"}, {"label": "12 days ago", "value": "12_days_ago"}, {"label": "13 days ago", "value": "13_days_ago"}, {"label": "14 days ago", "value": "14_days_ago"}, {"label": "14-21 days ago", "value": "14-21_days"}, {"label": "21-28 days ago", "value": "21-28_days"}, {"label": "28-60 days ago", "value": "28-60_days"}, {"label": "60-90 days ago", "value": "60-90_days"}, {"label": "90 days - 1 year ago", "value": "90_days-1_year"}, {"label": "1+ year ago", "value": "1+_year"}]}, "type": "select", "input": true, "label": "When did you notice you were going more frequently than normal?", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.changes_urination == 'yes' && data.urinary_symptom_overview.urinary_frequency && (_.sum(_.values(data.urinary_symptom_overview).map(Number)) < 2 || data.urinary_symptom_onset_pattern == 'separate_days')", "optionsLabelPosition": "right"}, {"key": "normal_nighttime_urination", "type": "radio", "input": true, "label": "Do you <strong>normally</strong> get up at night to urinate?", "values": [{"label": "Yes, regularly", "value": "yes_regularly"}, {"label": "Occasionally", "value": "occasionally"}, {"label": "Rarely", "value": "rarely"}, {"label": "Never", "value": "never"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.changes_urination == 'yes' && data.urinary_symptom_overview.urinary_frequency", "optionsLabelPosition": "right"}, {"key": "increased_nighttime_urination", "type": "radio", "input": true, "label": "Since your symptoms started, are you getting up more frequently than usual to urinate at night?", "values": [{"label": "Yes, much more frequently", "value": "much_more_frequently"}, {"label": "Yes, somewhat more frequently", "value": "somewhat_more_frequently"}, {"label": "No change", "value": "no_change"}, {"label": "I do not get up at night to urinate", "value": "never_night"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.changes_urination == 'yes' && data.urinary_symptom_overview.urinary_frequency", "optionsLabelPosition": "right"}, {"key": "nighttime_urination_pattern", "type": "selectboxes", "input": true, "label": "Since your symptoms started, please describe your pattern of urinating:", "values": [{"label": "I urinate a larger amount and am drinking more fluid than usual", "value": "large_volume_more_fluids"}, {"label": "I urinate a normal amount and drink the same amount of fluid as before", "value": "normal_volume_similar_fluids"}, {"label": "I urinate less than usual and haven't changed how much I drink", "value": "small_quantities"}, {"label": "I feel a strong need to urinate when I wake up but don't urinate much", "value": "urgent_need_normal_or_less"}, {"label": "Other (please specify in your next response)", "value": "other"}], "inputType": "checkbox", "tableView": true, "customConditional": "show = data.changes_urination == 'yes' && data.urinary_symptom_overview.urinary_frequency && (data.increased_nighttime_urination == 'much_more_frequently' || data.increased_nighttime_urination == 'somewhat_more_frequently')", "optionsLabelPosition": "right"}, {"key": "heading_urinary_urgency", "html": "</br><h4>Urgency to Urinate</h4>", "type": "content", "input": false, "label": "Urinary Urgency Details", "tableView": false, "refreshOnChange": true, "customConditional": "show = data.changes_urination == 'yes' && data.urinary_symptom_overview.urinary_urgency"}, {"key": "symptom_start_urinary_urgency", "data": {"values": [{"label": "Doesn't apply", "value": "doesn't_apply"}, {"label": "Last night", "value": "last_night"}, {"label": "Today", "value": "today"}, {"label": "1 day ago", "value": "1_day_ago"}, {"label": "2 days ago", "value": "2_days_ago"}, {"label": "3 days ago", "value": "3_days_ago"}, {"label": "4 days ago", "value": "4_days_ago"}, {"label": "5 days ago", "value": "5_days_ago"}, {"label": "6 days ago", "value": "6_days_ago"}, {"label": "7 days ago", "value": "7_days_ago"}, {"label": "8 days ago", "value": "8_days_ago"}, {"label": "9 days ago", "value": "9_days_ago"}, {"label": "10 days ago", "value": "10_days_ago"}, {"label": "11 days ago", "value": "11_days_ago"}, {"label": "12 days ago", "value": "12_days_ago"}, {"label": "13 days ago", "value": "13_days_ago"}, {"label": "14 days ago", "value": "14_days_ago"}, {"label": "14-21 days ago", "value": "14-21_days"}, {"label": "21-28 days ago", "value": "21-28_days"}, {"label": "28-60 days ago", "value": "28-60_days"}, {"label": "60-90 days ago", "value": "60-90_days"}, {"label": "90 days - 1 year ago", "value": "90_days-1_year"}, {"label": "1+ year ago", "value": "1+_year"}]}, "type": "select", "input": true, "label": "When did your urgency to urinate start?", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.changes_urination == 'yes' && data.urinary_symptom_overview.urinary_urgency && (_.sum(_.values(data.urinary_symptom_overview).map(Number)) < 2 || data.urinary_symptom_onset_pattern == 'separate_days')", "optionsLabelPosition": "right"}, {"key": "heading_urinary_stream", "html": "</br><h4>Weaker Stream</h4>", "type": "content", "input": false, "label": "Urinary Stream Details", "tableView": false, "refreshOnChange": true, "customConditional": "show = data.changes_urination == 'yes' && data.urinary_symptom_overview.urinary_stream"}, {"key": "symptom_start_urinary_stream", "data": {"values": [{"label": "Doesn't apply", "value": "doesn't_apply"}, {"label": "Last night", "value": "last_night"}, {"label": "Today", "value": "today"}, {"label": "1 day ago", "value": "1_day_ago"}, {"label": "2 days ago", "value": "2_days_ago"}, {"label": "3 days ago", "value": "3_days_ago"}, {"label": "4 days ago", "value": "4_days_ago"}, {"label": "5 days ago", "value": "5_days_ago"}, {"label": "6 days ago", "value": "6_days_ago"}, {"label": "7 days ago", "value": "7_days_ago"}, {"label": "8 days ago", "value": "8_days_ago"}, {"label": "9 days ago", "value": "9_days_ago"}, {"label": "10 days ago", "value": "10_days_ago"}, {"label": "11 days ago", "value": "11_days_ago"}, {"label": "12 days ago", "value": "12_days_ago"}, {"label": "13 days ago", "value": "13_days_ago"}, {"label": "14 days ago", "value": "14_days_ago"}, {"label": "14-21 days ago", "value": "14-21_days"}, {"label": "21-28 days ago", "value": "21-28_days"}, {"label": "28-60 days ago", "value": "28-60_days"}, {"label": "60-90 days ago", "value": "60-90_days"}, {"label": "90 days - 1 year ago", "value": "90_days-1_year"}, {"label": "1+ year ago", "value": "1+_year"}]}, "type": "select", "input": true, "label": "When did you notice you had a weaker stream?", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.changes_urination == 'yes' && data.urinary_symptom_overview.urinary_stream && (_.sum(_.values(data.urinary_symptom_overview).map(Number)) < 2 || data.urinary_symptom_onset_pattern == 'separate_days')", "optionsLabelPosition": "right"}, {"key": "enlarged_prostate_inquiry", "type": "radio", "input": true, "label": "Have you been told by a medical professional that you have an enlarged prostate?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "I'm not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.changes_urination == 'yes' && data.urinary_symptom_overview.urinary_stream", "optionsLabelPosition": "right"}, {"key": "prostate_cancer_family_history", "type": "radio", "input": true, "label": "Do you have a family history of prostate cancer?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "I'm not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.changes_urination == 'yes' && data.urinary_symptom_overview.urinary_stream", "optionsLabelPosition": "right"}, {"key": "past_prostatitis_diagnosis", "type": "radio", "input": true, "label": "Have you been diagnosed with prostatitis in the past?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "I'm not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.changes_urination == 'yes' && data.urinary_symptom_overview.urinary_stream", "optionsLabelPosition": "right"}, {"key": "prostate_exam_post_symptoms", "type": "radio", "input": true, "label": "Since the start of your symptoms, have you had a prostate exam?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "I'm not sure", "value": "not_sure"}], "tooltip": "A prostate exam, or Digital Rectal Exam (DRE), involves a doctor inserting a gloved, lubricated finger into the rectum to check the size, shape, and texture of the prostate.", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.changes_urination == 'yes' && data.urinary_symptom_overview.urinary_stream", "optionsLabelPosition": "right"}, {"key": "heading_incontinence", "html": "</br><h4>Leaking Urine or Unable to Urinate</h4>", "type": "content", "input": false, "label": "Incontinence Details", "tableView": false, "refreshOnChange": true, "customConditional": "show = data.changes_urination == 'yes' && data.urinary_symptom_overview.incontinent"}, {"key": "have_incontinence", "type": "radio", "input": true, "label": "<strong>Leaking urine</strong> means you are not able to control your bladder when you're not in the bathroom. <ul><li>You might find small quantities of urine in your underwear throughout the day.</li><li>You may feel the need to change your underwear periodically.</li><li>You might have a sudden and intense urge to urinate and lose control of your bladder.</li></ul>Based on this, do you truly feel you're leaking urine?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "I don't know", "value": "unknown"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.changes_urination == 'yes' && data.urinary_symptom_overview.incontinent", "optionsLabelPosition": "right"}, {"key": "symptom_start_incontinence", "data": {"values": [{"label": "Doesn't apply", "value": "doesn't_apply"}, {"label": "Last night", "value": "last_night"}, {"label": "Today", "value": "today"}, {"label": "1 day ago", "value": "1_day_ago"}, {"label": "2 days ago", "value": "2_days_ago"}, {"label": "3 days ago", "value": "3_days_ago"}, {"label": "4 days ago", "value": "4_days_ago"}, {"label": "5 days ago", "value": "5_days_ago"}, {"label": "6 days ago", "value": "6_days_ago"}, {"label": "7 days ago", "value": "7_days_ago"}, {"label": "8 days ago", "value": "8_days_ago"}, {"label": "9 days ago", "value": "9_days_ago"}, {"label": "10 days ago", "value": "10_days_ago"}, {"label": "11 days ago", "value": "11_days_ago"}, {"label": "12 days ago", "value": "12_days_ago"}, {"label": "13 days ago", "value": "13_days_ago"}, {"label": "14 days ago", "value": "14_days_ago"}, {"label": "14-21 days ago", "value": "14-21_days"}, {"label": "21-28 days ago", "value": "21-28_days"}, {"label": "28-60 days ago", "value": "28-60_days"}, {"label": "60-90 days ago", "value": "60-90_days"}, {"label": "90 days - 1 year ago", "value": "90_days-1_year"}, {"label": "1+ year ago", "value": "1+_year"}]}, "type": "select", "input": true, "label": "When did you first experience leaking urine or being unable to urinate?", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.changes_urination == 'yes' && data.urinary_symptom_overview.incontinent && (_.sum(_.values(data.urinary_symptom_overview).map(Number)) < 2 || data.urinary_symptom_onset_pattern == 'separate_days') && data.have_incontinence == 'yes'", "optionsLabelPosition": "right"}, {"key": "cause_of_leakage", "type": "selectboxes", "input": true, "label": "Please select when you notice leakage:", "values": [{"label": "Immediately after I urinate", "value": "post-void_dribble"}, {"label": "It happens when coughing, sneezing, or during exercising", "value": "stress"}, {"label": "It happens when I have a strong, sudden urge to urinate that can't be delayed", "value": "urge"}, {"label": "It happens at random", "value": "functional"}], "adminFlag": true, "inputType": "checkbox", "tableView": true, "customConditional": "show = data.changes_urination == 'yes' && data.urinary_symptom_overview.incontinent && data.have_incontinence == 'yes'", "optionsLabelPosition": "right"}, {"key": "previous_pelvic_therapy_options", "type": "selectboxes", "input": true, "label": "Have you done any of the following:", "values": [{"label": "Kegels exercises", "value": "kegels", "shortcut": ""}, {"label": "Pelvic floor physiotherapy", "value": "pelvic_physio", "shortcut": ""}, {"label": "Urospot", "value": "urospot", "shortcut": ""}], "inputType": "checkbox", "tableView": true, "customConditional": "show = data.changes_urination == 'yes' && data.urinary_symptom_overview.incontinent && data.have_incontinence == 'yes'", "optionsLabelPosition": "right"}, {"key": "no_previous_pelvic_therapy", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a therapy."}, "validate": {"custom": "valid = (!!data.no_previous_pelvic_therapy || !!_.some(_.values(data.previous_pelvic_therapy_options)));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.changes_urination == 'yes' && data.urinary_symptom_overview.incontinent && data.have_incontinence == 'yes'"}, {"key": "duration_pelvic_therapy", "type": "radio", "input": true, "label": "How long have you been engaging in Kegels exercises or pelvic floor physiotherapy?", "values": [{"label": "I don't do them anymore but did them in the past", "value": "don't_do_kegels_anymore"}, {"label": "Less than 2 weeks", "value": "less_than_2_weeks"}, {"label": "2 to 6 weeks", "value": "2_to_6_weeks"}, {"label": "6 weeks to 3 months", "value": "6_weeks_to_3_months"}, {"label": "More than 3 months", "value": "more_than_3_months"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.changes_urination == 'yes' && data.urinary_symptom_overview.incontinent && data.have_incontinence == 'yes' && (data.previous_pelvic_therapy_options.kegels || data.previous_pelvic_therapy_options.pelvic_physio)", "optionsLabelPosition": "right"}, {"key": "heading_hematuria", "html": "</br><h4>Blood in my Urine</h4>", "type": "content", "input": false, "label": "<PERSON><PERSON><PERSON>", "tableView": false, "refreshOnChange": true, "customConditional": "show = data.changes_urination == 'yes' && data.urinary_symptom_overview.hematuria"}, {"key": "symptom_start_hematuria", "data": {"values": [{"label": "Doesn't apply", "value": "doesn't_apply"}, {"label": "Last night", "value": "last_night"}, {"label": "Today", "value": "today"}, {"label": "1 day ago", "value": "1_day_ago"}, {"label": "2 days ago", "value": "2_days_ago"}, {"label": "3 days ago", "value": "3_days_ago"}, {"label": "4 days ago", "value": "4_days_ago"}, {"label": "5 days ago", "value": "5_days_ago"}, {"label": "6 days ago", "value": "6_days_ago"}, {"label": "7 days ago", "value": "7_days_ago"}, {"label": "8 days ago", "value": "8_days_ago"}, {"label": "9 days ago", "value": "9_days_ago"}, {"label": "10 days ago", "value": "10_days_ago"}, {"label": "11 days ago", "value": "11_days_ago"}, {"label": "12 days ago", "value": "12_days_ago"}, {"label": "13 days ago", "value": "13_days_ago"}, {"label": "14 days ago", "value": "14_days_ago"}, {"label": "14-21 days ago", "value": "14-21_days"}, {"label": "21-28 days ago", "value": "21-28_days"}, {"label": "28-60 days ago", "value": "28-60_days"}, {"label": "60-90 days ago", "value": "60-90_days"}, {"label": "90 days - 1 year ago", "value": "90_days-1_year"}, {"label": "1+ year ago", "value": "1+_year"}]}, "type": "select", "input": true, "label": "When did you first notice blood in your urine?", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.changes_urination == 'yes' && data.urinary_symptom_overview.hematuria && (_.sum(_.values(data.urinary_symptom_overview).map(Number)) < 2 || data.urinary_symptom_onset_pattern == 'separate_days')", "optionsLabelPosition": "right"}, {"key": "nature_of_hematuria", "type": "radio", "input": true, "label": "Please descrbe the nature of the blood in your urine:", "values": [{"label": "My urine is pink/red-tinged", "value": "red_tinged"}, {"label": "Urinating blood", "value": "urinating_blood"}, {"label": "Urinating blood clots", "value": "blood_clots"}, {"label": "I selected this in error - I don't have blood in my urine", "value": "clarifies_no_blood"}, {"label": "I'm not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.changes_urination == 'yes' && data.urinary_symptom_overview.hematuria", "optionsLabelPosition": "right"}, {"key": "blood_during_uti", "type": "radio", "input": true, "label": "Do you typically notice blood in your urine when you experience a urinary tract infection (UTI)?", "values": [{"label": "Yes, I typically see blood", "value": "yes_blood"}, {"label": "No, I do not typically see blood", "value": "no_blood"}, {"label": "Sometimes I see blood", "value": "sometimes_blood"}, {"label": "I don't know because I've never had a UTI before", "value": "never_had_uti"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.changes_urination == 'yes' && data.urinary_symptom_overview.hematuria && ['red_tinged', 'urinating_blood', 'blood_clots'].includes(data.nature_of_hematuria)", "optionsLabelPosition": "right"}, {"key": "source_of_bleeding", "type": "radio", "input": true, "label": "Are you sure the blood is coming from where the urine comes from, and not from the vagina?", "values": [{"label": "Yes, I'm sure it's from the urinary tract", "value": "urinary_tract"}, {"label": "No, it might be from the vagina", "value": "vagina"}, {"label": "I'm not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.changes_urination == 'yes' && data.sex == 'female' && data.urinary_symptom_overview.hematuria", "optionsLabelPosition": "right"}, {"key": "urinary_tear", "type": "radio", "input": true, "label": "Have you observed any tears or injury around the area where you urinate?", "values": [{"label": "I have looked and I see a tear", "value": "yes_tear"}, {"label": "I have looked and I do not see a tear", "value": "no_tear"}, {"label": "I'm not sure, I have not checked", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.changes_urination == 'yes' && data.sex == 'female' && data.urinary_symptom_overview.hematuria", "optionsLabelPosition": "right"}, {"key": "heading_incomplete_bladder_emptying", "html": "</br><h4>Bladder Emptying</h4>", "type": "content", "input": false, "label": "Incomplete Bladder Emptying Details", "tableView": false, "refreshOnChange": true, "customConditional": "show = data.changes_urination == 'yes' && data.urinary_symptom_overview.incomplete_bladder_emptying"}, {"key": "symptom_start_incomplete_bladder_emptying", "data": {"values": [{"label": "Doesn't apply", "value": "doesn't_apply"}, {"label": "I've always felt this way", "value": "always_felt_incompelete_emptying"}, {"label": "Last night", "value": "last_night"}, {"label": "Today", "value": "today"}, {"label": "1 day ago", "value": "1_day_ago"}, {"label": "2 days ago", "value": "2_days_ago"}, {"label": "3 days ago", "value": "3_days_ago"}, {"label": "4 days ago", "value": "4_days_ago"}, {"label": "5 days ago", "value": "5_days_ago"}, {"label": "6 days ago", "value": "6_days_ago"}, {"label": "7 days ago", "value": "7_days_ago"}, {"label": "8 days ago", "value": "8_days_ago"}, {"label": "9 days ago", "value": "9_days_ago"}, {"label": "10 days ago", "value": "10_days_ago"}, {"label": "11 days ago", "value": "11_days_ago"}, {"label": "12 days ago", "value": "12_days_ago"}, {"label": "13 days ago", "value": "13_days_ago"}, {"label": "14 days ago", "value": "14_days_ago"}, {"label": "14-21 days ago", "value": "14-21_days"}, {"label": "21-28 days ago", "value": "21-28_days"}, {"label": "28-60 days ago", "value": "28-60_days"}, {"label": "60-90 days ago", "value": "60-90_days"}, {"label": "90 days - 1 year ago", "value": "90_days-1_year"}, {"label": "1+ year ago", "value": "1+_year"}]}, "type": "select", "input": true, "label": "When did you start feeling that you haven't fully emptied your bladder?", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.changes_urination == 'yes' && data.urinary_symptom_overview.incomplete_bladder_emptying && (_.sum(_.values(data.urinary_symptom_overview).map(Number)) < 2 || data.urinary_symptom_onset_pattern == 'separate_days')", "optionsLabelPosition": "right"}, {"key": "investigated_kidney_bladder_ultrasound", "type": "radio", "input": true, "label": "Have you had this investigated in the past with a kidney or bladder ultrasound?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.changes_urination == 'yes' && data.urinary_symptom_overview.incomplete_bladder_emptying"}, {"key": "heading_penile_discharge", "html": "</br><h4>Penile Discharge</h4>", "type": "content", "input": false, "label": "Penile Discharge Details", "tableView": false, "refreshOnChange": true, "customConditional": "show = data.changes_urination == 'yes' && data.urinary_symptom_overview.penile_discharge"}, {"key": "symptom_penile_discharge", "data": {"values": [{"label": "Doesn't apply", "value": "doesn't_apply"}, {"label": "Last night", "value": "last_night"}, {"label": "Today", "value": "today"}, {"label": "1 day ago", "value": "1_day_ago"}, {"label": "2 days ago", "value": "2_days_ago"}, {"label": "3 days ago", "value": "3_days_ago"}, {"label": "4 days ago", "value": "4_days_ago"}, {"label": "5 days ago", "value": "5_days_ago"}, {"label": "6 days ago", "value": "6_days_ago"}, {"label": "7 days ago", "value": "7_days_ago"}, {"label": "8 days ago", "value": "8_days_ago"}, {"label": "9 days ago", "value": "9_days_ago"}, {"label": "10 days ago", "value": "10_days_ago"}, {"label": "11 days ago", "value": "11_days_ago"}, {"label": "12 days ago", "value": "12_days_ago"}, {"label": "13 days ago", "value": "13_days_ago"}, {"label": "14 days ago", "value": "14_days_ago"}, {"label": "14-21 days ago", "value": "14-21_days"}, {"label": "21-28 days ago", "value": "21-28_days"}, {"label": "28-60 days ago", "value": "28-60_days"}, {"label": "60-90 days ago", "value": "60-90_days"}, {"label": "90 days - 1 year ago", "value": "90_days-1_year"}, {"label": "1+ year ago", "value": "1+_year"}]}, "type": "select", "input": true, "label": "When did you notice discharge from the penis?", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.changes_urination == 'yes' && data.urinary_symptom_overview.penile_discharge && (_.sum(_.values(data.urinary_symptom_overview).map(Number)) < 2 || data.urinary_symptom_onset_pattern == 'separate_days')", "optionsLabelPosition": "right"}, {"key": "diagnosed_balanitis_yeast_infection", "type": "radio", "input": true, "label": "Have you ever been diagnosed with balanitis or a yeast infection of the penis?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "I'm not sure", "value": "not_sure"}], "tooltip": "Balanitis is inflammation of the foreskin and head of the penis, commonly due to infection.", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.changes_urination == 'yes' && data.urinary_symptom_overview.penile_discharge", "optionsLabelPosition": "right"}, {"key": "discharge_location", "type": "selectboxes", "input": true, "label": "Where do you notice the discharge primarily?", "values": [{"label": "Built up on underwear", "value": "underwear"}, {"label": "Present under the foreskin", "value": "under_foreskin"}, {"label": "Coming directly from the urethra (the opening where urine comes from)", "value": "from_urethra"}, {"label": "I'm not sure", "value": "not_sure"}], "tableView": true, "customConditional": "show = data.changes_urination == 'yes' && data.urinary_symptom_overview.penile_discharge && data.diagnosed_balanitis_yeast_infection", "optionsLabelPosition": "right"}, {"key": "penis_rash_description", "type": "selectboxes", "input": true, "label": "Have you noticed of the following changes on the head of your penis?", "values": [{"label": "Head is covered in a white film", "value": "white_film"}, {"label": "There are small red dots", "value": "red_dots"}, {"label": "Cut", "value": "cut_abrasion"}, {"label": "Skin looks like it's been scrapped off", "value": "abrasion"}, {"label": "Sore", "value": "sore"}], "tableView": true, "customConditional": "show = !!data.discharge_location && data.changes_urination == 'yes' && data.urinary_symptom_overview.penile_discharge", "optionsLabelPosition": "right"}, {"key": "no_penis_rash_description", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = !!data.no_penis_rash_description || !!_.some(_.values(data.penis_rash_description));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.changes_urination == 'yes' && data.urinary_symptom_overview.penile_discharge && data.diagnosed_balanitis_yeast_infection"}, {"key": "penis_rash_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following penile rash symptoms:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "I DO NOT have the following symptoms on my penis:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.penis_rash_description, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "ability_to_milk_discharge", "type": "radio", "input": true, "label": "Can you squeeze or press the discharge out of your penis?", "values": [{"label": "Yes, I can squeeze discharge out.", "value": "yes"}, {"label": "No, I can't squeeze discharge out.", "value": "no"}, {"label": "I'm not sure what this means.", "value": "not_sure"}], "tooltip": "This means pressing or massaging the penis to push out discharge. It's often used in medical checks to understand the discharge.", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.changes_urination == 'yes' && data.urinary_symptom_overview.penile_discharge && data.diagnosed_balanitis_yeast_infection && (data.penis_rash_description || data.no_penis_rash_description)", "optionsLabelPosition": "right"}, {"key": "heading_testicular_symptoms", "html": "</br><h4>Testicular Symptoms&nbsp;</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.sex == 'male'"}, {"key": "changes_testicles", "type": "radio", "input": true, "label": "Have you noticed any changes in your testicles (discomfort, felt a lump, or noticed a change in size)?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.sex == 'male'"}, {"key": "testicular_symptom_overview", "type": "selectboxes", "input": true, "label": "Please clarify which of the following symptoms you've noticed:", "values": [{"label": "Pain or discomfort in the testicles", "value": "testicular_pain"}, {"label": "Noticed swelling of testicles", "value": "testicular_swelling"}, {"label": "Felt a lump or mass in the testicle", "value": "testicular_lump"}, {"label": "Pain with ejaculation", "value": "painful_ejaculation"}, {"label": "Blood in my semen", "value": "hematospermia"}, {"label": "Other symptoms", "value": "other_testicular_symptoms"}], "tableView": true, "customConditional": "show = data.sex == 'male' && !!data.changes_testicles && data.changes_testicles == 'yes'"}, {"key": "no_testicular_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = !!data.no_testicular_symptoms || !!_.some(_.values(data.testicular_symptom_overview));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.sex == 'male' && data.changes_testicles == 'yes'"}, {"key": "testicular_symptom_onset_pattern", "type": "radio", "input": true, "label": "Did your testicular symptoms start around the same time or on separate days?", "values": [{"label": "All symptoms started around the same time", "value": "same_time"}, {"label": "Symptoms started on separate days", "value": "separate_days"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.sex == 'male' && data.changes_testicles == 'yes' && !!data.testicular_symptom_overview && _.sum(_.values(data.testicular_symptom_overview).map(Number)) >= 2"}, {"key": "all_testicular_symptoms_start", "data": {"values": [{"label": "Doesn't apply", "value": "doesn't_apply"}, {"label": "Last night", "value": "last_night"}, {"label": "Today", "value": "today"}, {"label": "1 day ago", "value": "1_day_ago"}, {"label": "2 days ago", "value": "2_days_ago"}, {"label": "3 days ago", "value": "3_days_ago"}, {"label": "4 days ago", "value": "4_days_ago"}, {"label": "5 days ago", "value": "5_days_ago"}, {"label": "6 days ago", "value": "6_days_ago"}, {"label": "7 days ago", "value": "7_days_ago"}, {"label": "8 days ago", "value": "8_days_ago"}, {"label": "9 days ago", "value": "9_days_ago"}, {"label": "10 days ago", "value": "10_days_ago"}, {"label": "11 days ago", "value": "11_days_ago"}, {"label": "12 days ago", "value": "12_days_ago"}, {"label": "13 days ago", "value": "13_days_ago"}, {"label": "14 days ago", "value": "14_days_ago"}, {"label": "14-21 days ago", "value": "14-21_days"}, {"label": "21-28 days ago", "value": "21-28_days"}, {"label": "28-60 days ago", "value": "28-60_days"}, {"label": "60-90 days ago", "value": "60-90_days"}, {"label": "90 days - 1 year ago", "value": "90_days-1_year"}, {"label": "1+ year ago", "value": "1+_year"}]}, "type": "select", "input": true, "label": "When did your testicular symptoms start?", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show =  !!data.testicular_symptom_onset_pattern && data.changes_testicles == 'yes' && data.testicular_symptom_overview && data.testicular_symptom_onset_pattern == 'same_time' && !data.no_testicular_symptoms", "optionsLabelPosition": "right"}, {"key": "heading_pain_discomfort", "html": "</br><h5>Pain or Discomfort in the Testicles</h5>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.sex == 'male' && data.changes_testicles == 'yes' && data.testicular_symptom_overview.testicular_pain"}, {"key": "symptom_start_testicular_discomfort", "data": {"values": [{"label": "Doesn't apply", "value": "doesn't_apply"}, {"label": "Last night", "value": "last_night"}, {"label": "Today", "value": "today"}, {"label": "1 day ago", "value": "1_day_ago"}, {"label": "2 days ago", "value": "2_days_ago"}, {"label": "3 days ago", "value": "3_days_ago"}, {"label": "4 days ago", "value": "4_days_ago"}, {"label": "5 days ago", "value": "5_days_ago"}, {"label": "6 days ago", "value": "6_days_ago"}, {"label": "7 days ago", "value": "7_days_ago"}, {"label": "8 days ago", "value": "8_days_ago"}, {"label": "9 days ago", "value": "9_days_ago"}, {"label": "10 days ago", "value": "10_days_ago"}, {"label": "11 days ago", "value": "11_days_ago"}, {"label": "12 days ago", "value": "12_days_ago"}, {"label": "13 days ago", "value": "13_days_ago"}, {"label": "14 days ago", "value": "14_days_ago"}, {"label": "14-21 days ago", "value": "14-21_days"}, {"label": "21-28 days ago", "value": "21-28_days"}, {"label": "28-60 days ago", "value": "28-60_days"}, {"label": "60-90 days ago", "value": "60-90_days"}, {"label": "90 days - 1 year ago", "value": "90_days-1_year"}, {"label": "1+ year ago", "value": "1+_year"}]}, "type": "select", "input": true, "label": "When did your testicular discomfort start?", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.changes_testicles == 'yes' && data.testicular_symptom_overview.testicular_pain && (_.sum(_.values(data.testicular_symptom_overview).map(Number)) < 2 || data.testicular_symptom_onset_pattern == 'separate_days')", "optionsLabelPosition": "right"}, {"key": "which_testicle_affected", "type": "radio", "input": true, "label": "Which testicle is bothering you?", "values": [{"label": "Left", "value": "left"}, {"label": "Right", "value": "right"}, {"label": "Both", "value": "both"}], "tableView": true, "customConditional": "show = data.sex == 'male' && !!data.testicular_symptom_overview && data.testicular_symptom_overview.testicular_pain"}, {"key": "previous_discomfort", "type": "radio", "input": true, "label": "Have you had similar discomfort in the past?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "tableView": true, "customConditional": "show = data.sex == 'male' && !!data.which_testicle_affected"}, {"key": "past_testicular_diagnosis", "type": "selectboxes", "input": true, "label": "What was the diagnosis when you had similar symptoms?", "values": [{"label": "I don't know - I never saw a doctor/nurse practitioner", "value": "no_diagnosis_no_assessment"}, {"label": "We never figured it out", "value": "no_diagnosis"}, {"label": "Hernia", "value": "hernia"}, {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "epididymitis"}, {"label": "Groin sprain", "value": "groin_sprain"}, {"label": "Varicocele", "value": "varicocele"}, {"label": "Other", "value": "other"}], "tableView": true, "customConditional": "show = data.sex == 'male' && !!data.previous_discomfort && data.previous_discomfort == 'yes'"}, {"key": "no_past_testicular_diagnosis", "type": "checkbox", "input": true, "label": "None of the above", "validate": {"custom": "valid = !!data.no_past_testicular_diagnosis || !!_.some(_.values(data.past_testicular_diagnosis));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.sex == 'male' && !!data.previous_discomfort && data.previous_discomfort == 'yes'"}, {"key": "description_of_other_diagnosis", "type": "textarea", "input": true, "label": "Please describe the other diagnosis:", "tableView": true, "customConditional": "show = !!data.past_testicular_diagnosis && data.past_testicular_diagnosis.other"}, {"key": "testicular_diagnosis_tests", "type": "selectboxes", "input": true, "label": "At the time, did you have any of the following tests done?", "values": [{"label": "Ultrasound", "value": "ultrasound"}, {"label": "STI testing", "value": "sti_testing"}, {"label": "Referral to a urologist", "value": "referral_to_urologist"}], "tableView": true, "customConditional": "show = data.sex == 'male' && !!data.previous_discomfort && data.previous_discomfort == 'yes' && !!_.some(_.values(data.past_testicular_diagnosis))"}, {"key": "pain_onset", "data": {"values": [{"label": "Doesn't apply", "value": "doesn't_apply"}, {"label": "Last night", "value": "last_night"}, {"label": "Today", "value": "today"}, {"label": "1 day ago", "value": "1_day_ago"}, {"label": "2 days ago", "value": "2_days_ago"}, {"label": "3 days ago", "value": "3_days_ago"}, {"label": "4 days ago", "value": "4_days_ago"}, {"label": "5 days ago", "value": "5_days_ago"}, {"label": "6 days ago", "value": "6_days_ago"}, {"label": "7 days ago", "value": "7_days_ago"}, {"label": "8 days ago", "value": "8_days_ago"}, {"label": "9 days ago", "value": "9_days_ago"}, {"label": "10 days ago", "value": "10_days_ago"}, {"label": "11 days ago", "value": "11_days_ago"}, {"label": "12 days ago", "value": "12_days_ago"}, {"label": "13 days ago", "value": "13_days_ago"}, {"label": "14 days ago", "value": "14_days_ago"}, {"label": "14-21 days ago", "value": "14-21_days"}, {"label": "21-28 days ago", "value": "21-28_days"}, {"label": "28-60 days ago", "value": "28-60_days"}, {"label": "60-90 days ago", "value": "60-90_days"}, {"label": "90 days - 1 year ago", "value": "90_days-1_year"}, {"label": "1+ year ago", "value": "1+_year"}]}, "type": "select", "input": true, "label": "When did your current episode of discomfort start?", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.sex == 'male' && !!data.past_testicular_diagnosis"}, {"key": "pain_progress", "type": "radio", "input": true, "label": "Is the pain getting better, worse, or staying the same?", "values": [{"label": "Getting better", "value": "better"}, {"label": "Getting worse", "value": "worse"}, {"label": "Staying the same", "value": "same"}], "tableView": true, "customConditional": "show = data.sex == 'male' && !!data.pain_onset"}, {"key": "trauma_injury", "type": "radio", "input": true, "label": "Was there any trauma or injury to the testicle?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "tableView": true, "customConditional": "show = data.sex == 'male' && !!data.pain_onset"}, {"key": "heading_swelling", "html": "</br><h5>Swelling of Testicles</h5>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.sex == 'male' && data.changes_testicles == 'yes' && data.testicular_symptom_overview.testicular_swelling"}, {"key": "heading_lump_mass", "html": "</br><h5>Lump or Mass in the Testicle</h5>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.sex == 'male' && data.changes_testicles == 'yes' && data.testicular_symptom_overview.testicular_lump"}, {"key": "heading_pain_ejaculation", "html": "</br><h5>Pain with Ejaculation</h5>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.sex == 'male' && data.changes_testicles == 'yes' && data.testicular_symptom_overview.painful_ejaculation"}, {"key": "heading_blood_semen", "html": "</br><h5>Blood in Semen</h5>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.sex == 'male' && data.changes_testicles == 'yes' && data.testicular_symptom_overview.hematospermia"}, {"key": "heading_abdominal_pelvic_pain", "html": "</br><h4>Abdominal and Pelvic Symptoms&nbsp;</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "calculateValue": "value = data.sku && (!_.includes(['fem_pn_vswb', 'fem_pn_uti', 'std_pn_uti', 'asthma_medication_beta'], data.sku) || _.includes(['add_urine_bloodwork_sti', 'add_urine_sti'], data.request_sti_testing));", "refreshOnChange": false}, {"key": "changes_abdo_pelvis_back", "type": "radio", "input": true, "label": "Have you noticed you're experiencing any abdominal, pelvic or back discomfort that is new (i.e. cramping, aching or general discomfort)?", "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "abdominal_pelvic_symptom_overview", "type": "selectboxes", "input": true, "label": "Please select from the following list of symptoms:", "values": [{"label": "Abdominal pain, discomfort or cramping", "value": "abdominal_pain", "shortcut": ""}, {"label": "Pelvic pain, discomfort or cramping", "value": "pelvic_pain", "shortcut": ""}, {"label": "Back Pain", "value": "back_pain", "shortcut": ""}], "adminFlag": true, "inputType": "checkbox", "tableView": true, "customConditional": "show = data.changes_abdo_pelvis_back == 'yes'", "optionsLabelPosition": "right"}, {"key": "no_abdominal_pelvic_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = !!data.no_abdominal_pelvic_symptoms || !!_.some(_.values(data.abdominal_pelvic_overview));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.changes_abdo_pelvis_back == 'yes'"}, {"key": "abdo_pelvic_symptoms_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following abdominal/pelvic symptoms:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "I DO NOT have the following abdominal/pelvic symptoms:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.abdominal_pelvic_symptom_overview, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "heading_abdominal_pain", "html": "<h4>Abdominal Pain</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.changes_abdo_pelvis_back == 'yes' && data.abdominal_pelvic_symptom_overview.abdominal_pain"}, {"key": "symptom_start_abdominal_pain", "data": {"values": [{"label": "Doesn't apply", "value": "doesn't_apply"}, {"label": "Last night", "value": "last_night"}, {"label": "Today", "value": "today"}, {"label": "1 day ago", "value": "1_day_ago"}, {"label": "2 days ago", "value": "2_days_ago"}, {"label": "3 days ago", "value": "3_days_ago"}, {"label": "4 days ago", "value": "4_days_ago"}, {"label": "5 days ago", "value": "5_days_ago"}, {"label": "6 days ago", "value": "6_days_ago"}, {"label": "7 days ago", "value": "7_days_ago"}, {"label": "8 days ago", "value": "8_days_ago"}, {"label": "9 days ago", "value": "9_days_ago"}, {"label": "10 days ago", "value": "10_days_ago"}, {"label": "11 days ago", "value": "11_days_ago"}, {"label": "12 days ago", "value": "12_days_ago"}, {"label": "13 days ago", "value": "13_days_ago"}, {"label": "14 days ago", "value": "14_days_ago"}, {"label": "14-21 days ago", "value": "14-21_days"}, {"label": "21-28 days ago", "value": "21-28_days"}, {"label": "28-60 days ago", "value": "28-60_days"}, {"label": "60-90 days ago", "value": "60-90_days"}, {"label": "90 days - 1 year ago", "value": "90_days-1_year"}, {"label": "1+ year ago", "value": "1+_year"}]}, "type": "select", "input": true, "label": "When did your abdominal discomfort start?", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.changes_abdo_pelvis_back == 'yes' && data.abdominal_pelvic_symptom_overview.abdominal_pain", "optionsLabelPosition": "right"}, {"key": "previous_abdominal_pain", "type": "radio", "input": true, "label": "Have you experienced similar abdominal pain in the past?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "I'm not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.changes_abdo_pelvis_back == 'yes' && data.abdominal_pelvic_symptom_overview.abdominal_pain", "optionsLabelPosition": "right"}, {"key": "previous_abdominal_pain_timeframe", "data": {"values": [{"label": "Days ago", "value": "days_ago"}, {"label": "Weeks ago", "value": "weeks_ago"}, {"label": "Months ago", "value": "months_ago"}, {"label": "Years ago", "value": "years_ago"}]}, "type": "select", "input": true, "label": "When did you experience this pain before?", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.changes_abdo_pelvis_back == 'yes' && data.abdominal_pelvic_symptom_overview.abdominal_pain && data.previous_abdominal_pain == 'yes'"}, {"key": "previous_abdominal_pain_diagnosis", "type": "textfield", "input": true, "label": "What was the diagnosis at that time?", "validate": {"required": true}, "tableView": true, "customConditional": "show = !!data.previous_abdominal_pain_timeframe && data.previous_abdominal_pain == 'yes'"}, {"key": "quality_of_abdominal_pain", "type": "selectboxes", "input": true, "label": "Please describe the quality of your abdominal pain:", "values": [{"label": "Comes and goes in waves", "value": "colicky"}, {"label": "Constant", "value": "constant"}, {"label": "Worsens after eating", "value": "post_prandial"}, {"label": "Worsens after urinating", "value": "post_void"}, {"label": "Occurs during or after intercourse", "value": "with_intercourse"}, {"label": "Other (please specify)", "value": "other"}], "tableView": true, "customConditional": "show = data.changes_abdo_pelvis_back == 'yes' && !!data.previous_abdominal_pain", "optionsLabelPosition": "right"}, {"key": "heading_pelvic_pain", "html": "<h4>P<PERSON><PERSON> Pain</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.changes_abdo_pelvis_back == 'yes' && data.abdominal_pelvic_symptom_overview.pelvic_pain"}, {"key": "heading_back_pain", "html": "<h4>Back Pain</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.changes_abdo_pelvis_back == 'yes' && data.abdominal_pelvic_symptom_overview.back_pain"}, {"key": "onset_back_pain", "data": {"values": [{"label": "I've always had back pain - this isn't new for me", "value": "always_not_new"}, {"label": "Last night", "value": "last_night"}, {"label": "Today", "value": "today"}, {"label": "1 day ago", "value": "1_day_ago"}, {"label": "2 days ago", "value": "2_days_ago"}, {"label": "3 days ago", "value": "3_days_ago"}, {"label": "4 days ago", "value": "4_days_ago"}, {"label": "5 days ago", "value": "5_days_ago"}, {"label": "6 days ago", "value": "6_days_ago"}, {"label": "7 days ago", "value": "7_days_ago"}, {"label": "8 days ago", "value": "8_days_ago"}, {"label": "9 days ago", "value": "9_days_ago"}, {"label": "10 days ago", "value": "10_days_ago"}, {"label": "11 days ago", "value": "11_days_ago"}, {"label": "12 days ago", "value": "12_days_ago"}, {"label": "13 days ago", "value": "13_days_ago"}, {"label": "14 days ago", "value": "14_days_ago"}, {"label": "14-21 days ago", "value": "14-21_days"}, {"label": "21-28 days ago", "value": "21-28_days"}, {"label": "28-60 days ago", "value": "28-60_days"}, {"label": "60-90 days ago", "value": "60-90_days"}, {"label": "90 days - 1 year ago", "value": "90_days-1_year"}, {"label": "1+ year ago", "value": "1+_year"}]}, "type": "select", "input": true, "label": "When did your back pain start?", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.changes_abdo_pelvis_back == 'yes' && data.abdominal_pelvic_symptom_overview.back_pain"}, {"key": "pattern_back_pain", "type": "selectboxes", "input": true, "label": "How would you describe the pattern of your back pain?", "values": [{"label": "Always there", "value": "constant"}, {"label": "Comes and goes", "value": "intermittent"}, {"label": "Gets worse with activity", "value": "worsens_with_activity"}, {"label": "Gets better with rest", "value": "improves_with_rest"}], "tableView": true, "customConditional": "show = !!data.onset_back_pain && data.changes_abdo_pelvis_back == 'yes' && data.abdominal_pelvic_symptom_overview.back_pain", "optionsLabelPosition": "right"}, {"key": "quality_of_back_pain", "type": "selectboxes", "input": true, "label": "Please describe the quality of your back pain:", "values": [{"label": "<PERSON>", "value": "sharp"}, {"label": "<PERSON><PERSON>", "value": "dull"}, {"label": "A<PERSON>", "value": "aching"}, {"label": "Burning", "value": "burning"}, {"label": "Throbbing", "value": "throbbing"}, {"label": "Other (please specify)", "value": "other"}], "tableView": true, "customConditional": "show = !!data.pattern_back_pain && _.some(_.values(data.pattern_back_pain))", "optionsLabelPosition": "right"}, {"key": "palliating_factors_back_pain", "type": "selectboxes", "input": true, "label": "What makes your back pain feel better?", "values": [{"label": "Rest", "value": "rest"}, {"label": "Movement", "value": "movement"}, {"label": "Heat", "value": "heat"}, {"label": "Ice", "value": "ice"}, {"label": "Medication", "value": "medication"}, {"label": "Other (please specify)", "value": "other"}, {"label": "None of the above", "value": "none_of_the_above"}], "tableView": true, "customConditional": "show = !!data.quality_of_back_pain && _.some(_.values(data.quality_of_back_pain))", "optionsLabelPosition": "right"}, {"key": "provoking_factors_back_pain", "type": "selectboxes", "input": true, "label": "What makes your back pain worse?", "values": [{"label": "Movement", "value": "movement"}, {"label": "Sitting", "value": "sitting"}, {"label": "Standing", "value": "standing"}, {"label": "Lying down", "value": "lying_down"}, {"label": "Bending", "value": "bending"}, {"label": "Other (please specify)", "value": "other"}, {"label": "None of the above", "value": "none_of_the_above"}], "tableView": true, "customConditional": "show = !!data.palliating_factors_back_pain && _.some(_.values(data.palliating_factors_back_pain))", "optionsLabelPosition": "right"}, {"key": "common_conditions_back_pain", "type": "selectboxes", "input": true, "label": "Have you been diagnosed with any of the following conditions related to your back pain?", "values": [{"label": "Herniated disc", "value": "herniated_disc"}, {"label": "Sciatica", "value": "sciatica"}, {"label": "Spinal stenosis", "value": "spinal_stenosis"}, {"label": "Arthritis", "value": "arthritis"}, {"label": "<PERSON><PERSON><PERSON>", "value": "scoliosis"}, {"label": "Osteoporosis", "value": "osteoporosis"}, {"label": "Muscle strain", "value": "muscle_strain"}, {"label": "Other (please specify)", "value": "other"}, {"label": "None of the above", "value": "none_of_the_above"}], "tableView": true, "customConditional": "show = !!data.provoking_factors_back_pain && _.some(_.values(data.provoking_factors_back_pain))", "optionsLabelPosition": "right"}, {"key": "heading_penile_symptoms", "html": "</br><h4>Penile Symptoms&nbsp;</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "calculateValue": "value = data.sku && (!_.includes(['fem_pn_vswb', 'fem_pn_uti', 'std_pn_uti', 'asthma_medication_beta'], data.sku) || _.includes(['add_urine_bloodwork_sti', 'add_urine_sti'], data.request_sti_testing));", "refreshOnChange": false, "customConditional": "show = data.sex == 'male'"}, {"key": "penile_symptoms", "type": "selectboxes", "input": true, "label": "Are you currently experiencing any of the following symptoms <strong>that are new</strong>:", "values": [{"label": "Discomfort at the tip of the penis", "value": "penile_discomfort", "shortcut": ""}, {"label": "Discomfort along the shaft of the penis", "value": "shaft_discomfort", "shortcut": ""}, {"label": "Curvature to my penis that is new", "value": "penile_curvature", "shortcut": ""}, {"label": "Pain with erections", "value": "erectile_pain", "shortcut": ""}], "adminFlag": true, "inputType": "checkbox", "tableView": true, "customConditional": "show = data.sex == 'male'", "optionsLabelPosition": "right"}, {"key": "no_penile_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = !!data.no_penile_symptoms || !!_.some(_.values(data.penile_symptoms));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.sex == 'male'"}, {"key": "heading_vaginal_symptoms", "html": "</br><h4>Vaginal Symptoms&nbsp;</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "calculateValue": "value = data.sku && (!_.includes(['fem_pn_vswb', 'fem_pn_uti', 'std_pn_uti', 'asthma_medication_beta'], data.sku) || _.includes(['add_urine_bloodwork_sti', 'add_urine_sti'], data.request_sti_testing));", "refreshOnChange": false, "customConditional": "show = data.sex == 'female'"}, {"key": "changes_vaginal", "type": "radio", "input": true, "label": "Have you noticed any vaginal changes (discomfort, dryness, discharge, odour, itchiness or vaginal lumps or bleeding after sex)?", "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.sex == 'female'", "optionsLabelPosition": "right"}, {"key": "vaginal_symptom_triggers", "type": "selectboxes", "input": true, "label": "Did these symptoms start after any of the following triggers?", "values": [{"label": "Shaving or waxing", "value": "shaving_waxing", "shortcut": ""}, {"label": "New lubricant during intercourse", "value": "new_lubricant", "shortcut": ""}, {"label": "Use of a sex toy", "value": "sex_toy", "shortcut": ""}, {"label": "New hygiene product (e.g., vaginal soap)", "value": "new_hygiene_product", "shortcut": ""}, {"label": "Douching", "value": "douching", "shortcut": ""}, {"label": "Using a new condom brand", "value": "new_condom_brand", "shortcut": ""}, {"label": "New brand of tampon or liner", "value": "new_tampon_liner", "shortcut": ""}, {"label": "New sexual partner", "value": "new_sexual_partner", "shortcut": ""}, {"label": "None of the above", "value": "none_of_the_above", "shortcut": ""}], "inputType": "checkbox", "tableView": true, "customConditional": "show = data.sex == 'female' && !!data.changes_vaginal && data.changes_vaginal == 'yes'", "optionsLabelPosition": "right"}, {"key": "vaginal_symptom_overview", "type": "selectboxes", "input": true, "label": "Are you currently experiencing any of the following symptoms:", "values": [{"label": "Vaginal Itchiness", "value": "vaginal_pruritis", "shortcut": ""}, {"label": "Vaginal Discharge", "value": "vaginal_discharge", "shortcut": ""}, {"label": "Vaginal Odour", "value": "vaginal_odour", "shortcut": ""}, {"label": "Vaginal Dryness", "value": "vaginal_dryness", "shortcut": ""}, {"label": "Bleeding after sex", "value": "coital_bleeding", "shortcut": ""}, {"label": "Spotting between my cycles unrelated to sex", "value": "mid_cycle_spotting", "shortcut": ""}, {"label": "Lump around my vagina", "value": "vaginal_lump", "shortcut": ""}], "adminFlag": true, "inputType": "checkbox", "tableView": true, "customConditional": "show = data.sex == 'female' && data.changes_vaginal == 'yes' && _.some(_.values(data.vaginal_symptom_triggers))", "optionsLabelPosition": "right"}, {"key": "no_vaginal_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = !!data.no_vaginal_symptoms || !!_.some(_.values(data.vaginal_symptom_overview));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.sex == 'female' && data.changes_vaginal == 'yes' && _.some(_.values(data.vaginal_symptom_triggers))"}, {"key": "vaginal_symptoms_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following vaginal symptoms:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "I DO NOT have the following vaginal symptoms:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.vaginal_symptom_overview, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "vaginal_symptom_onset_pattern", "type": "radio", "input": true, "label": "Did your vaginal symptoms start around the same time or over separate days?", "values": [{"label": "All symptoms started around the same time", "value": "same_time", "shortcut": ""}, {"label": "Symptoms started on separate days", "value": "separate_days", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.changes_vaginal == 'yes' && data.vaginal_symptom_overview && _.sum(_.values(data.vaginal_symptom_overview).map(Number)) >= 2", "optionsLabelPosition": "right"}, {"key": "all_vaginal_symptoms_start", "data": {"values": [{"label": "Doesn't apply", "value": "doesn't_apply"}, {"label": "Last night", "value": "last_night"}, {"label": "Today", "value": "today"}, {"label": "1 day ago", "value": "1_day_ago"}, {"label": "2 days ago", "value": "2_days_ago"}, {"label": "3 days ago", "value": "3_days_ago"}, {"label": "4 days ago", "value": "4_days_ago"}, {"label": "5 days ago", "value": "5_days_ago"}, {"label": "6 days ago", "value": "6_days_ago"}, {"label": "7 days ago", "value": "7_days_ago"}, {"label": "8 days ago", "value": "8_days_ago"}, {"label": "9 days ago", "value": "9_days_ago"}, {"label": "10 days ago", "value": "10_days_ago"}, {"label": "11 days ago", "value": "11_days_ago"}, {"label": "12 days ago", "value": "12_days_ago"}, {"label": "13 days ago", "value": "13_days_ago"}, {"label": "14 days ago", "value": "14_days_ago"}, {"label": "14-21 days ago", "value": "14-21_days"}, {"label": "21-28 days ago", "value": "21-28_days"}, {"label": "28-60 days ago", "value": "28-60_days"}, {"label": "60-90 days ago", "value": "60-90_days"}, {"label": "90 days - 1 year ago", "value": "90_days-1_year"}, {"label": "1+ year ago", "value": "1+_year"}]}, "type": "select", "input": true, "label": "When did your vaginal symptoms start?", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.changes_vaginal == 'yes' && data.vaginal_symptom_overview && data.vaginal_symptom_onset_pattern == 'same_time' && !data.no_vaginal_symptoms", "optionsLabelPosition": "right"}, {"key": "heading_vaginal_pruritis", "html": "</br><h4>Vaginal Itchiness</h4>", "type": "content", "input": false, "label": "<PERSON><PERSON><PERSON>", "tableView": false, "refreshOnChange": true, "customConditional": "show = data.sex == 'female' && data.vaginal_symptom_overview && data.vaginal_symptom_overview.vaginal_pruritis == true"}, {"key": "symptom_start_vaginal_pruritis", "data": {"values": [{"label": "Doesn't apply", "value": "doesn't_apply"}, {"label": "I've always had vaginal itchiness and this isn't new for me", "value": "always"}, {"label": "Last night", "value": "last_night"}, {"label": "Today", "value": "today"}, {"label": "1 day ago", "value": "1_day_ago"}, {"label": "2 days ago", "value": "2_days_ago"}, {"label": "3 days ago", "value": "3_days_ago"}, {"label": "4 days ago", "value": "4_days_ago"}, {"label": "5 days ago", "value": "5_days_ago"}, {"label": "6 days ago", "value": "6_days_ago"}, {"label": "7 days ago", "value": "7_days_ago"}, {"label": "8 days ago", "value": "8_days_ago"}, {"label": "9 days ago", "value": "9_days_ago"}, {"label": "10 days ago", "value": "10_days_ago"}, {"label": "11 days ago", "value": "11_days_ago"}, {"label": "12 days ago", "value": "12_days_ago"}, {"label": "13 days ago", "value": "13_days_ago"}, {"label": "14 days ago", "value": "14_days_ago"}, {"label": "14-21 days ago", "value": "14-21_days"}, {"label": "21-28 days ago", "value": "21-28_days"}, {"label": "28-60 days ago", "value": "28-60_days"}, {"label": "60-90 days ago", "value": "60-90_days"}, {"label": "90 days - 1 year ago", "value": "90_days-1_year"}, {"label": "1+ year ago", "value": "1+_year"}]}, "type": "select", "input": true, "label": "When did your vaginal itchiness start?", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.sex == 'female' && !!data.vaginal_symptom_overview && data.vaginal_symptom_overview.vaginal_pruritis && (_.sum(_.values(data.vaginal_symptom_overview).map(Number)) < 2 || data.vaginal_symptom_onset_pattern == 'separate_days')", "optionsLabelPosition": "right"}, {"key": "vaginal_pruritis_location", "type": "selectboxes", "input": true, "label": "Please describe where you experience vaginal itchiness. Select all that apply:", "values": [{"label": "Vaginal opening", "value": "vaginal_opening"}, {"label": "Inside the vagina", "value": "inside_vagina"}, {"label": "Outer labia", "value": "outer_labia"}, {"label": "Inner labia", "value": "inner_labia"}, {"label": "Other", "value": "other"}, {"label": "I don't understand", "value": "doesnt_understand"}], "tooltip": "The outer labia (labia majora) are the larger, fleshy folds of skin that surround the more delicate structures of the vaginal opening. The inner labia (labia minora) are the thinner, more delicate folds of skin located just inside the outer labia, surrounding the vaginal opening.", "inputType": "checkbox", "tableView": true, "customConditional": "show = data.sex == 'female' && !!data.vaginal_symptom_overview && data.vaginal_symptom_overview.vaginal_pruritis", "optionsLabelPosition": "right"}, {"key": "heading_discharge_quality", "html": "</br><h4>Vaginal Discharge</h4>", "type": "content", "input": false, "label": "Vaginal Discharge Details", "tableView": false, "refreshOnChange": true, "customConditional": "show = data.changes_vaginal == 'yes' && data.vaginal_symptom_overview && data.vaginal_symptom_overview.vaginal_discharge === true"}, {"key": "symptom_start_vaginal_discharge", "data": {"values": [{"label": "I always have vaginal discharge and this isn't new for me", "value": "no_change"}, {"label": "Last night", "value": "last_night"}, {"label": "Today", "value": "today"}, {"label": "1 day ago", "value": "1_day_ago"}, {"label": "2 days ago", "value": "2_days_ago"}, {"label": "3 days ago", "value": "3_days_ago"}, {"label": "4 days ago", "value": "4_days_ago"}, {"label": "5 days ago", "value": "5_days_ago"}, {"label": "6 days ago", "value": "6_days_ago"}, {"label": "7 days ago", "value": "7_days_ago"}, {"label": "8 days ago", "value": "8_days_ago"}, {"label": "9 days ago", "value": "9_days_ago"}, {"label": "10 days ago", "value": "10_days_ago"}, {"label": "11 days ago", "value": "11_days_ago"}, {"label": "12 days ago", "value": "12_days_ago"}, {"label": "13 days ago", "value": "13_days_ago"}, {"label": "14 days ago", "value": "14_days_ago"}, {"label": "14-21 days ago", "value": "14-21_days"}, {"label": "21-28 days ago", "value": "21-28_days"}, {"label": "28-60 days ago", "value": "28-60_days"}, {"label": "60-90 days ago", "value": "60-90_days"}, {"label": "90 days - 1 year ago", "value": "90_days-1_year"}, {"label": "1+ year ago", "value": "1+_year"}]}, "type": "select", "input": true, "label": "When did you start experiencing discharge?", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.sex == 'female' && data.changes_vaginal == 'yes' && !!data.vaginal_symptom_overview && data.vaginal_symptom_overview.vaginal_discharge && (_.sum(_.values(data.vaginal_symptom_overview).map(Number)) < 2 || data.vaginal_symptom_onset_pattern == 'separate_days')", "optionsLabelPosition": "right"}, {"key": "vaginal_discharge_color", "type": "selectboxes", "input": true, "label": "What is the color of your discharge? Please select all that apply:", "values": [{"label": "Curdish-white", "value": "curdish_white"}, {"label": "Yellow", "value": "yellow"}, {"label": "Pink", "value": "pink"}, {"label": "Bloody", "value": "bloody"}, {"label": "Green", "value": "green"}, {"label": "Grey", "value": "grey"}], "inputType": "checkbox", "tableView": true, "customConditional": "show = data.sex == 'female' && (!!data.symptom_start_vaginal_discharge || !!data.all_vaginal_symptoms_start)", "optionsLabelPosition": "right"}, {"key": "vaginal_discharge_consistency", "type": "selectboxes", "input": true, "label": "What is the consistency of your discharge? Please select all that apply:", "values": [{"label": "<PERSON><PERSON><PERSON>", "value": "thick"}, {"label": "Thin", "value": "thin"}, {"label": "Watery", "value": "watery"}, {"label": "Streaks of blood", "value": "streaks_of_blood"}], "inputType": "checkbox", "tableView": true, "customConditional": "show = data.sex == 'female' && data.vaginal_discharge_color && _.some(_.values(data.vaginal_discharge_color)) && data.vaginal_symptom_overview && data.vaginal_symptom_overview.vaginal_discharge", "optionsLabelPosition": "right"}, {"key": "heading_vaginal_odour", "html": "</br><h4>Vaginal Odour</h4>", "type": "content", "input": false, "label": "Vaginal Odor Details", "tableView": false, "refreshOnChange": true, "customConditional": "show = data.sex == 'female' && data.changes_vaginal == 'yes' && data.vaginal_symptom_overview && data.vaginal_symptom_overview.vaginal_odour === true"}, {"key": "symptom_start_vaginal_odour", "data": {"values": [{"label": "Doesn't apply", "value": "doesn't_apply"}, {"label": "I've always noticed an odour and this isn't new for me", "value": "always"}, {"label": "Always with my menstrul cycles, but never in between", "value": "with_menses"}, {"label": "Last night", "value": "last_night"}, {"label": "Today", "value": "today"}, {"label": "1 day ago", "value": "1_day_ago"}, {"label": "2 days ago", "value": "2_days_ago"}, {"label": "3 days ago", "value": "3_days_ago"}, {"label": "4 days ago", "value": "4_days_ago"}, {"label": "5 days ago", "value": "5_days_ago"}, {"label": "6 days ago", "value": "6_days_ago"}, {"label": "7 days ago", "value": "7_days_ago"}, {"label": "8 days ago", "value": "8_days_ago"}, {"label": "9 days ago", "value": "9_days_ago"}, {"label": "10 days ago", "value": "10_days_ago"}, {"label": "11 days ago", "value": "11_days_ago"}, {"label": "12 days ago", "value": "12_days_ago"}, {"label": "13 days ago", "value": "13_days_ago"}, {"label": "14 days ago", "value": "14_days_ago"}, {"label": "14-21 days ago", "value": "14-21_days"}, {"label": "21-28 days ago", "value": "21-28_days"}, {"label": "28-60 days ago", "value": "28-60_days"}, {"label": "60-90 days ago", "value": "60-90_days"}, {"label": "90 days - 1 year ago", "value": "90_days-1_year"}, {"label": "1+ year ago", "value": "1+_year"}]}, "type": "select", "input": true, "label": "When did you first notice your vaginal odour?", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.sex == 'female' && data.changes_vaginal == 'yes' && !!data.vaginal_symptom_overview && data.vaginal_symptom_overview.vaginal_odour && (_.sum(_.values(data.vaginal_symptom_overview).map(Number)) < 2 || data.vaginal_symptom_onset_pattern == 'separate_days')", "optionsLabelPosition": "right"}, {"key": "vaginal_odour_description", "type": "selectboxes", "input": true, "label": "What describes the odour of your vaginal discharge? Please select all that apply:", "values": [{"label": "<PERSON><PERSON>", "value": "fishy"}, {"label": "Off-odour", "value": "off-odour"}, {"label": "Yeast-like", "value": "yeast_like"}, {"label": "Metallic", "value": "metallic"}, {"label": "No Odour", "value": "no_odour"}], "inputType": "checkbox", "tableView": true, "customConditional": "show = data.sex == 'female' && data.vaginal_symptom_overview && data.vaginal_symptom_overview.vaginal_odour", "optionsLabelPosition": "right"}, {"key": "heading_vaginal_dryness", "html": "</br><h4>Vaginal Dryness</h4>", "type": "content", "input": false, "label": "Vaginal Dryness Details", "tableView": false, "refreshOnChange": true, "customConditional": "show = data.changes_vaginal == 'yes' && data.vaginal_symptom_overview && data.vaginal_symptom_overview.vaginal_dryness === true"}, {"key": "symptom_start_vaginal_dryness", "data": {"values": [{"label": "Doesn't apply", "value": "doesn't_apply"}, {"label": "I've always had dryness and this isn't new for me", "value": "always"}, {"label": "I only experience dryness with intercourse", "value": "with_intercourse"}, {"label": "Last night", "value": "last_night"}, {"label": "Today", "value": "today"}, {"label": "1 day ago", "value": "1_day_ago"}, {"label": "2 days ago", "value": "2_days_ago"}, {"label": "3 days ago", "value": "3_days_ago"}, {"label": "4 days ago", "value": "4_days_ago"}, {"label": "5 days ago", "value": "5_days_ago"}, {"label": "6 days ago", "value": "6_days_ago"}, {"label": "7 days ago", "value": "7_days_ago"}, {"label": "8 days ago", "value": "8_days_ago"}, {"label": "9 days ago", "value": "9_days_ago"}, {"label": "10 days ago", "value": "10_days_ago"}, {"label": "11 days ago", "value": "11_days_ago"}, {"label": "12 days ago", "value": "12_days_ago"}, {"label": "13 days ago", "value": "13_days_ago"}, {"label": "14 days ago", "value": "14_days_ago"}, {"label": "14-21 days ago", "value": "14-21_days"}, {"label": "21-28 days ago", "value": "21-28_days"}, {"label": "28-60 days ago", "value": "28-60_days"}, {"label": "60-90 days ago", "value": "60-90_days"}, {"label": "90 days - 1 year ago", "value": "90_days-1_year"}, {"label": "1+ year ago", "value": "1+_year"}]}, "type": "select", "input": true, "label": "When did you first notice your vaginal dryness?", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.sex == 'female' && data.changes_vaginal == 'yes' && !!data.vaginal_symptom_overview && data.vaginal_symptom_overview.vaginal_dryness && (_.sum(_.values(data.vaginal_symptom_overview).map(Number)) < 2 || data.vaginal_symptom_onset_pattern == 'separate_days')", "optionsLabelPosition": "right"}, {"key": "menopause_perimenopausal_status", "type": "radio", "input": true, "label": "Have you gone through menopause or been told by your doctor that you are perimenopausal?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "I don't think so", "value": "doesn't_think_so"}, {"label": "I don't know", "value": "doesn't_know"}], "tableView": true, "customConditional": "show = data.vaginal_symptom_overview && data.vaginal_symptom_overview.vaginal_dryness"}, {"key": "dryness_relationship_intercourse", "type": "radio", "input": true, "label": "Is your dryness related only to intercourse, or is it present all the time?", "values": [{"label": "Only during intercourse", "value": "intercourse_only"}, {"label": "Present all the time", "value": "all_time"}], "tableView": true, "customConditional": "show = data.vaginal_symptom_overview && data.vaginal_symptom_overview.vaginal_dryness && !!data.menopause_perimenopausal_status"}, {"key": "vaginal_moisturizers_use", "type": "radio", "input": true, "label": "Have you used over-the-counter (i.e. non-prescription) vaginal moisturizers from the pharmacy, like Replens?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "tableView": true, "customConditional": "show = data.vaginal_symptom_overview && data.vaginal_symptom_overview.vaginal_dryness && !!data.dryness_relationship_intercourse"}, {"key": "vaginal_estrogen_prescription", "type": "radio", "input": true, "label": "Have you ever been prescribed vaginal estrogen?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "tableView": true, "customConditional": "show = data.vaginal_symptom_overview && data.vaginal_symptom_overview.vaginal_dryness && !!data.vaginal_moisturizers_use"}, {"key": "birth_control_relation", "type": "radio", "input": true, "label": "Did you find your symptoms started after taking birth control?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "tooltip": "Higher estrogen dose birth control can resolve vaginal dryness.", "tableView": true, "customConditional": "show = data.vaginal_symptom_overview && data.vaginal_symptom_overview.vaginal_dryness && !!data.vaginal_estrogen_prescription"}, {"key": "heading_bleeding_after_sex", "html": "</br><h4>Bleeding after Sex</h4>", "type": "content", "input": false, "label": "Vaginal Dryness Details", "tableView": false, "refreshOnChange": true, "customConditional": "show = data.sex == 'female' && data.changes_vaginal == 'yes' && data.vaginal_symptom_overview && data.vaginal_symptom_overview.coital_bleeding == true"}, {"key": "symptom_start_coital_bleeding", "data": {"values": [{"label": "Doesn't apply", "value": "doesn't_apply"}, {"label": "I've always had bleeding after sex, and this isn't new for me", "value": "always"}, {"label": "Last night", "value": "last_night"}, {"label": "Today", "value": "today"}, {"label": "1 day ago", "value": "1_day_ago"}, {"label": "2 days ago", "value": "2_days_ago"}, {"label": "3 days ago", "value": "3_days_ago"}, {"label": "4 days ago", "value": "4_days_ago"}, {"label": "5 days ago", "value": "5_days_ago"}, {"label": "6 days ago", "value": "6_days_ago"}, {"label": "7 days ago", "value": "7_days_ago"}, {"label": "8 days ago", "value": "8_days_ago"}, {"label": "9 days ago", "value": "9_days_ago"}, {"label": "10 days ago", "value": "10_days_ago"}, {"label": "11 days ago", "value": "11_days_ago"}, {"label": "12 days ago", "value": "12_days_ago"}, {"label": "13 days ago", "value": "13_days_ago"}, {"label": "14 days ago", "value": "14_days_ago"}, {"label": "14-21 days ago", "value": "14-21_days"}, {"label": "21-28 days ago", "value": "21-28_days"}, {"label": "28-60 days ago", "value": "28-60_days"}, {"label": "60-90 days ago", "value": "60-90_days"}, {"label": "90 days - 1 year ago", "value": "90_days-1_year"}, {"label": "1+ year ago", "value": "1+_year"}]}, "type": "select", "input": true, "label": "When did you first notice you were bleeding after sex?", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.sex == 'female' && data.changes_vaginal == 'yes' && !!data.vaginal_symptom_overview && data.vaginal_symptom_overview.coital_bleeding && (_.sum(_.values(data.vaginal_symptom_overview).map(Number)) < 2 || data.vaginal_symptom_onset_pattern == 'separate_days')", "optionsLabelPosition": "right"}, {"key": "bleeding_correlation", "type": "radio", "input": true, "label": "Is the bleeding correlated to intercourse (i.e., happens the day or two after sex) or occurs even without intercourse as a trigger?", "values": [{"label": "Seems to happen only after sex", "value": "correlated_to_intercourse"}, {"label": "Doesn't seem to happen with sex", "value": "occurs_without_intercourse"}, {"label": "Seems to occur sometimes after sex, and sometimes without", "value": "with_or_without_intercourse"}], "tableView": true, "customConditional": "show = data.sex == 'female' && data.changes_vaginal == 'yes' && data.vaginal_symptom_overview && data.vaginal_symptom_overview.coital_bleeding === true"}, {"key": "number_coital_bleeding_episodes", "data": {"values": [{"label": "I haven't had any bleeding episodes", "value": "none"}, {"label": "1 episode", "value": "1"}, {"label": "2 episodes", "value": "2"}, {"label": "3 episodes", "value": "3"}, {"label": "4 episodes", "value": "4"}, {"label": "5 episodes", "value": "5"}, {"label": "6 episodes", "value": "6"}, {"label": "7 episodes", "value": "7"}, {"label": "8 episodes", "value": "8"}, {"label": "9 episodes", "value": "9"}, {"label": "10 episodes", "value": "10"}, {"label": "11-15 episodes", "value": "11-15"}, {"label": "15+ episodes", "value": "15_plus"}]}, "type": "select", "input": true, "label": "How many episodes of bleeding after sex have you experienced?", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.sex == 'female' && data.changes_vaginal == 'yes' && !!data.vaginal_symptom_overview && data.vaginal_symptom_overview.coital_bleeding && !!data.bleeding_correlation", "optionsLabelPosition": "right"}, {"key": "iud_usage", "type": "radio", "input": true, "label": "Do you have an IUD?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "tableView": true, "customConditional": "show = data.sex == 'female' && data.vaginal_symptom_overview && data.vaginal_symptom_overview.coital_bleeding && !!data.number_coital_bleeding_episodes"}, {"key": "iud_type", "type": "radio", "input": true, "label": "Which type of IUD do you have?", "values": [{"label": "<PERSON><PERSON> (Hormonal) - 8 years", "value": "<PERSON><PERSON>"}, {"label": "<PERSON><PERSON> (Hormonal) - 5 years", "value": "kyleena"}, {"label": "<PERSON> (Hormonal) - 3 years", "value": "skyla"}, {"label": "<PERSON> (Non-hormonal) - 5 years", "value": "mona_lisa"}, {"label": "<PERSON><PERSON><PERSON> (Non-hormonal) - 5 years", "value": "liverte_copper_iud"}, {"label": "Nova-T (Non-hormonal) - 5 years", "value": "nova_copper_iud"}, {"label": "Flexi-T (Non-hormonal) - 5 years", "value": "flexi_copper_iud"}], "tableView": true, "customConditional": "show = data.sex == 'female' && data.vaginal_symptom_overview && data.vaginal_symptom_overview.coital_bleeding && data.iud_usage === 'yes' && !!data.number_coital_bleeding_episodes"}, {"key": "iud_symptom_relation", "type": "radio", "input": true, "label": "Did you find the symptoms started after the IUD was inserted?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "tableView": true, "customConditional": "show = data.vaginal_symptom_overview && data.vaginal_symptom_overview.coital_bleeding && !!data.iud_usage && data.iud_usage === 'yes'"}, {"key": "iud_string_check", "type": "radio", "input": true, "label": "Have you had the IUD string length checked with a speculum exam since this started?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "tableView": true, "customConditional": "show = data.vaginal_symptom_overview && data.vaginal_symptom_overview.coital_bleeding && !!data.iud_symptom_relation && data.iud_usage === 'yes'"}, {"key": "heading_discharge_quality", "html": "</br><h4>Mid-Cycle Spotting</h4>", "type": "content", "input": false, "label": "Vaginal Discharge Details", "tableView": false, "refreshOnChange": true, "customConditional": "show = data.changes_vaginal == 'yes' && data.vaginal_symptom_overview && data.vaginal_symptom_overview.mid_cycle_spotting === true"}, {"key": "symptom_start_mid_cycle_spotting", "data": {"values": [{"label": "I've always had bleeding between periods and this isn't new for me", "value": "no_change"}, {"label": "< 1 month ago", "value": "less_than_1_month"}, {"label": "1-2 months ago", "value": "1-2_months"}, {"label": "2-3 months ago", "value": "2-3_months"}, {"label": "3-4 months ago", "value": "3-4_months"}, {"label": "5-6 months ago", "value": "5-6_months"}, {"label": "6-12 months ago", "value": "6-12_months"}, {"label": "12+ months ago", "value": "12+_months"}]}, "type": "select", "input": true, "label": "When did you start noticing you were bleeding between cycles?", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.sex == 'female' && data.changes_vaginal == 'yes' && !!data.vaginal_symptom_overview && data.vaginal_symptom_overview.mid_cycle_spotting && (_.sum(_.values(data.vaginal_symptom_overview).map(Number)) < 2 || data.vaginal_symptom_onset_pattern == 'separate_days')", "optionsLabelPosition": "right"}, {"key": "menstrual_cycle_regularity", "type": "radio", "input": true, "label": "Do you have regular menstrual cycles? Here's how to tell: <br><ul><li><strong>Regular Cycles:</strong> Characterized by minor variations in length and flow. <ul><li><em>Example:</em> Cycles vary slightly, such as 25, 27, and 30 days between periods, with each period lasting 3-5 days.</li></ul></li><li><strong>Irregular Cycles:</strong> Marked by significant variability in cycle length and flow. <ul><li><em>Example:</em> Cycles might vary widely, such as 14, 34, and 50 days between periods, with flow duration changing drastically from 1 to 7 days.</li></ul></li></ul>", "values": [{"label": "Yes, my cycles are regular", "value": "regular"}, {"label": "No, my cycles are irregular", "value": "irregular"}, {"label": "Not sure", "value": "not_sure"}], "tableView": true, "customConditional": "show = data.sex == 'female' && data.changes_vaginal == 'yes' && !!data.vaginal_symptom_overview && data.vaginal_symptom_overview.mid_cycle_spotting && (_.sum(_.values(data.vaginal_symptom_overview).map(Number)) < 2 || data.vaginal_symptom_onset_pattern == 'separate_days')", "optionsLabelPosition": "right"}, {"key": "menstrual_cycle_days_of_flow", "data": {"values": [{"label": "1 day", "value": "1_day"}, {"label": "2 days", "value": "2_days"}, {"label": "3 days", "value": "3_days"}, {"label": "4 days", "value": "4_days"}, {"label": "5 or more days", "value": "5+_days"}]}, "type": "select", "input": true, "label": "How many days of flow do you typically experience during your menstrual cycle?", "widget": "html5", "tableView": true, "customConditional": "show = data.sex == 'female' && data.menstrual_cycle_regularity && data.vaginal_symptom_overview && data.vaginal_symptom_overview.mid_cycle_spotting && !!data.menstrual_cycle_regularity", "optionsLabelPosition": "right"}, {"key": "menstrual_flow_quality", "type": "selectboxes", "input": true, "label": "How would you describe the quality of your flow during your menstrual cycle?", "values": [{"label": "Light spotting", "value": "light_spotting"}, {"label": "Moderate", "value": "moderate"}, {"label": "Heavy bleeding", "value": "heavy_bleeding"}], "inputType": "checkbox", "tableView": true, "customConditional": "show = data.sex == 'female' && data.menstrual_cycle_days_of_flow && data.vaginal_symptom_overview && data.vaginal_symptom_overview.vaginal_discharge", "optionsLabelPosition": "right"}, {"key": "additional_questions", "type": "textarea", "input": true, "label": "Please use this area to ask any questions that you might have for your health care provider:", "tableView": true, "autoExpand": false}]}