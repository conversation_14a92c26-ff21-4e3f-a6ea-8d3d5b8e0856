{"name": "Female Fertility", "type": "form", "title": "Female Fertility", "display": "form", "components": [{"key": "pregnancyTestReasons", "type": "selectboxes", "input": true, "label": "I am seeking testing for the following reasons:", "values": [{"label": "Confirmation of pregnancy with a positive home pregnancy test", "value": "confirmation", "shortcut": ""}, {"label": "Seeking access to abortion and require proof of pregnancy", "value": "abortion", "shortcut": ""}, {"label": "Negative home pregnancy test but late menstrual cycle", "value": "lateCycle", "shortcut": ""}], "inputType": "checkbox", "tableView": false, "optionsLabelPosition": "right"}, {"key": "ectopicPregnancy", "type": "checkbox", "input": true, "label": "Vaginal bleeding, abdominal pain, concerned about an ectopic pregnancy", "tableView": false, "customClass": "mt-n3", "defaultValue": false}, {"key": "noneOfTheAbovePregnancy", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = !!data.ectopicPregnancy || !!_.some(_.values(data.pregnancyTestReasons)) || !!data.noneOfTheAbovePregnancy;"}, "tableView": false, "customClass": "mt-n3", "defaultValue": false}, {"key": "noTest", "type": "textfield", "input": true, "label": "No Test List", "hidden": true, "disabled": true, "multiple": true, "tableView": true, "spellcheck": false, "clearOnHide": false, "calculateValue": [{"!!": {"var": "data.ectopicPregnancy"}}]}, {"key": "recTestFreqMonths_HCG", "type": "textfield", "input": true, "label": "", "hidden": true, "disabled": true, "tableView": true, "spellcheck": false, "clearOnHide": false, "calculateValue": {"if": [{"var": "data.ectopicPregnancy"}, 101, {"if": [{"_some": {"_values": {"var": "data.pregnancyTestReasons"}}}, 1, 101]}]}}]}