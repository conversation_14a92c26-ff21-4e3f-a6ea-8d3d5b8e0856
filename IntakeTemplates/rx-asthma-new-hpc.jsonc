{"components": [{"key": "heading_asthma", "html": "<h1><strong>Asthma Management</strong></h1><p>Please answer the following questions to assist us in providing you with the best care for your asthma. Your responses are vital for a tailored treatment plan.</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "heading_diagnosis", "html": "<h2><strong>Diagnosis and History</strong></h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "prior_asthma_diagnosis", "type": "radio", "input": true, "label": "Have you been previously diagnosed with asthma by a healthcare professional (e.g., doctor, respiratory therapist, nurse practitioner)?", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "last_pulmonary_function_test", "type": "radio", "input": true, "label": "When was your last Pulmonary Function Test (PFT)?", "inline": false, "values": [{"label": "Within the last 6 months", "value": "last_6_months", "shortcut": ""}, {"label": "6-12 months ago", "value": "6_to_12_months_ago", "shortcut": ""}, {"label": "1-2 years ago", "value": "1_to_2_years_ago", "shortcut": ""}, {"label": "More than 2 years ago", "value": "more_than_2_years", "shortcut": ""}, {"label": "I have never had a PFT", "value": "never_had_pft", "shortcut": ""}], "tooltip": "A Pulmonary Function Test (PFT) involves a series of easy breathing exercises that you do with the help of a machine. During the test, you'll breathe into a tube connected to the machine, which measures your breath. The test includes taking deep breaths and exhaling quickly, as well as breathing normally. It's painless and doesn't involve any needles or medication. The results show how much air your lungs can hold and how quickly you can move air in and out.", "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "asthma_symptoms_frequency", "type": "radio", "input": true, "label": "How often do you experience asthma symptoms?", "inline": false, "values": [{"label": "Daily", "value": "daily", "shortcut": ""}, {"label": "Several times a week", "value": "several_times_week", "shortcut": ""}, {"label": "Occasionally", "value": "occasionally", "shortcut": ""}, {"label": "Rarely", "value": "rarely", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "asthma_triggers", "type": "selectboxes", "input": true, "label": "What triggers your asthma symptoms? (Select all that apply)", "inline": false, "values": [{"label": "Allergies (e.g., pollen, dust mites)", "value": "allergies"}, {"label": "Physical activity", "value": "physical_activity"}, {"label": "Cold air", "value": "cold_air"}, {"label": "Stress", "value": "stress"}, {"label": "Smoke or pollution", "value": "smoke_pollution"}, {"label": "Other", "value": "other"}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "optionsLabelPosition": "right"}, {"key": "asthma_attack_frequency", "type": "radio", "input": true, "label": "How frequently do you have asthma attacks?", "inline": false, "values": [{"label": "More than once a week", "value": "more_than_once_week", "shortcut": ""}, {"label": "A few times a month", "value": "few_times_month", "shortcut": ""}, {"label": "Less than once a month", "value": "less_than_once_month", "shortcut": ""}, {"label": "Rarely", "value": "rarely", "shortcut": ""}], "tooltip": "An asthma attack occurs when symptoms are worse than usual. It can involve coughing, wheezing, shortness of breath, and tightness in the chest. During an attack, the airways become swollen and inflamed, making it hard to breathe.", "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "asthma_hospitalization", "type": "radio", "input": true, "label": "Have you ever been hospitalized due to asthma?", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "currently_pregnant", "type": "radio", "input": true, "label": "Are you currently pregnant?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}, {"label": "I don't know", "value": "i_dont_know", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.sex == 'female';", "optionsLabelPosition": "right"}, {"key": "heading_asthmatic_medications", "html": "<h2><strong>Asthmatic Medications</strong></h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "heading_reliever_inhalers", "html": "<h3><strong>Reliever Inhalers</strong></h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "saba_anticholinergics", "type": "selectboxes", "input": true, "label": "Let us know if you are using any reliever inhalers below. These are usually used for quick relief when you have asthma symptoms like wheezing, coughing, or shortness of breath.", "inline": false, "values": [{"label": "Salbutamol (e.g., <strong>Ventolin</strong>, <strong>Albuterol</strong>)", "value": "albuterol"}, {"label": "Ipratropium (e.g., <strong>Atrovent</strong>)", "value": "ipratropium"}, {"label": "Terbutaline (e.g., <strong>Bricanyl</strong>)", "value": "terbutaline"}, {"label": "None of the above", "value": "none"}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "optionsLabelPosition": "right"}, {"key": "salbutamol_usage_frequency", "data": {"values": [{"label": "Less than once a week", "value": "less_than_once_week"}, {"label": "1 to 2 times a week", "value": "1_to_2_times_week"}, {"label": "3 to 4 times a week", "value": "3_to_4_times_week"}, {"label": "5 to 6 times a week", "value": "5_to_6_times_week"}, {"label": "Every day", "value": "every_day"}, {"label": "Multiple times a day", "value": "multiple_times_day"}, {"label": "Only when needed", "value": "when_needed"}]}, "type": "select", "input": true, "label": "How often do you use Salbutamol in a typical week?", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.saba_anticholinergics.albuterol == true;", "optionsLabelPosition": "right"}, {"key": "heading_combination_inhalers", "html": "<h4><strong>Combination Inhalers</strong></h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "combination_inhalers", "type": "selectboxes", "input": true, "label": "If you use combination inhalers, please indicate which ones. Combination inhalers typically contain more than one type of medicine to help manage asthma symptoms.", "inline": false, "values": [{"label": "Fluticasone/Salmeterol (e.g., <strong><PERSON><PERSON>ir</strong>)", "value": "fluticasone_salmeterol"}, {"label": "Budesonide/Formoterol (e.g., <strong>Symbicort</strong>)", "value": "budesonide_formoterol"}, {"label": "Fluticasone/Vilanterol (e.g., <strong><PERSON><PERSON><PERSON></strong>)", "value": "fluticasone_vilanterol"}, {"label": "Mometasone/Formoterol (e.g., <strong>Zenhale</strong>)", "value": "mometasone_formoterol"}, {"label": "Umeclidinium/Vilanterol/Fluticasone (e.g., <strong>Trelegy Ellip<PERSON></strong>)", "value": "umeclidinium_vilanterol_fluticasone"}, {"label": "None of the above", "value": "none"}], "inputType": "checkbox", "tableView": true}, {"key": "combination_inhaler_usage_frequency", "data": {"values": [{"label": "Less than once a week", "value": "less_than_once_week"}, {"label": "1 to 2 times a week", "value": "1_to_2_times_week"}, {"label": "3 to 4 times a week", "value": "3_to_4_times_week"}, {"label": "5 to 6 times a week", "value": "5_to_6_times_week"}, {"label": "Every day", "value": "every_day"}, {"label": "Only when needed", "value": "when_needed"}]}, "type": "select", "input": true, "label": "How often do you use your combination inhaler in a typical week?", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.combination_inhalers.fluticasone_salmeterol == true || data.combination_inhalers.budesonide_formoterol == true || data.combination_inhalers.fluticasone_vilanterol == true || data.combination_inhalers.mometasone_formoterol == true || data.combination_inhalers.umeclidinium_vilanterol_fluticasone == true;", "optionsLabelPosition": "right"}, {"key": "heading_steroid_only_inhalers", "html": "<h4><strong>Steroid-Only Inhalers</strong></h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "ics_monotherapy", "type": "selectboxes", "input": true, "label": "Are you using any of these inhalers for asthma? (These are the types that contain only one medicine, not a mix.)", "inline": false, "values": [{"label": "Fluticasone (e.g., <strong>Flovent</strong>)", "value": "fluticasone"}, {"label": "Budesonide (e.g., <strong>Pulmicort</strong>)", "value": "budesonide"}, {"label": "Mometasone (e.g., <strong>Asmanex</strong>)", "value": "mometasone"}, {"label": "Ciclesonide (e.g., <strong>Alvesco</strong>)", "value": "ciclesonide"}, {"label": "Beclomethasone (e.g., <strong>Qvar</strong>)", "value": "beclomethasone"}, {"label": "None of the above", "value": "none"}], "inputType": "checkbox", "tableView": true}, {"key": "steroid_inhaler_usage_frequency", "data": {"values": [{"label": "Less than once a week", "value": "less_than_once_week"}, {"label": "1 to 2 times a week", "value": "1_to_2_times_week"}, {"label": "3 to 4 times a week", "value": "3_to_4_times_week"}, {"label": "5 to 6 times a week", "value": "5_to_6_times_week"}, {"label": "Every day", "value": "every_day"}, {"label": "Only when needed", "value": "when_needed"}]}, "type": "select", "input": true, "label": "How often do you use your steroid-only inhaler in a typical week?", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.ics_monotherapy.fluticasone == true || data.ics_monotherapy.budesonide == true || data.ics_monotherapy.mometasone == true || data.ics_monotherapy.ciclesonide == true || data.ics_monotherapy.beclomethasone == true;", "optionsLabelPosition": "right"}, {"key": "heading_singulair", "html": "<h3><strong>Oral Medications</strong></h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "montelukast_singulair_use", "type": "radio", "input": true, "label": "Are you currently taking <PERSON><PERSON><PERSON> (also known as Singulair)?", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}], "tooltip": "Montelukast (Singulair) is an oral medication often used to help with breathing problems like asthma.", "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "oral_steroids_use", "type": "radio", "input": true, "label": "Are you currently taking any oral steroids for asthma, such as prednisone?", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}], "tooltip": "Oral steroids, like prednisone, are sometimes prescribed for asthma to reduce inflammation and swelling in the airways.", "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "heading_other_asthma_medications", "html": "<h3><strong>Other Asthma Medications</strong></h3><p>Let us know if you're taking any other asthma medications that haven't been mentioned above.</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "other_asthma_medications", "type": "textarea", "input": true, "label": "Please list any other asthma medications you are currently taking:", "tableView": true, "autoExpand": true}, {"key": "heading_medication_administration", "html": "<h3><strong>Medication Use</strong></h3><p>Please answer the following questions about how you take your asthma medications. This helps us understand how well your current treatment is working and if any adjustments are needed.</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "inhaler_technique", "type": "radio", "input": true, "label": "Do you feel confident that you are using your inhaler correctly?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Unsure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "medication_usage_frequency", "type": "radio", "input": true, "label": "Do you take your asthma medication as prescribed?", "values": [{"label": "Always", "value": "always"}, {"label": "Sometimes", "value": "sometimes"}, {"label": "Rarely", "value": "rarely"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "medication_schedule_understanding", "type": "radio", "input": true, "label": "Do you understand when to take each of your asthma medications?", "values": [{"label": "Yes, I understand completely", "value": "yes_completely"}, {"label": "Somewhat, but could use more clarity", "value": "somewhat_clarity"}, {"label": "No, I'm often confused", "value": "no_confused"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "heading_core_asthma_symptoms", "html": "<h2><strong> Asthma Symptoms</strong></h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "core_asthma_symptoms", "type": "selectboxes", "input": true, "label": "Which of these common asthma symptoms are you currently experiencing? (Check all that apply)", "inline": false, "values": [{"label": "Coughing, especially at night or early morning", "value": "coughing"}, {"label": "Wheezing or a whistling sound when breathing", "value": "wheezing"}, {"label": "Shortness of breath or trouble breathing", "value": "shortness_of_breath"}, {"label": "Chest tightness or pain", "value": "chest_tightness"}, {"label": "Increased need to use a rescue inhaler", "value": "increased_rescue_inhaler_use"}, {"label": "None of the above", "value": "none"}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "optionsLabelPosition": "right"}, {"key": "red_flag_asthma_symptoms", "type": "selectboxes", "input": true, "label": "Are you experiencing any of these symptoms? (Please check all that apply)", "inline": false, "values": [{"label": "Trouble speaking because you're too out of breath", "value": "difficulty_speaking"}, {"label": "Sudden or quick increase in asthma symptoms", "value": "quick_increase_symptoms"}, {"label": "Chest pain or tightness", "value": "chest_pain_tightness"}, {"label": "Coughing up blood", "value": "hemoptysis"}, {"label": "Feel ligheaded or dizzy", "value": "presy<PERSON><PERSON>"}, {"label": "Feeling unwell or nauseaous", "value": "malaise"}, {"label": "Fever or chills", "value": "fever_chills"}, {"label": "None of the above", "value": "none_red_flag"}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "optionsLabelPosition": "right"}, {"tab_name": "preferences", "intake_template_key": "rx-asthma-hpc-preferences"}, {"key": "content2", "html": "<h2>Questions for the Doctor</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"intake_template_key": "hx-any-questions"}, {"key": "intake_denial_reasons", "type": "textfield", "input": true, "label": "Intake Denial Reasons:", "hidden": true, "disabled": true, "multiple": true, "adminFlag": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "value=_.concat((data.any_other_questions === true && !!data.stated_other_questions && !/^\\s*$/.test(data.stated_other_questions)?['stated_other_questions.not_blank']:[]));", "refreshOnChange": true}, {"key": "intake_kind", "type": "textfield", "input": true, "label": "Intake Kind", "hidden": true, "disabled": true, "tableView": true, "clearOnHide": false, "calculateValue": "value = !data.intake_denial_reasons?.length ? 'mail' : 'chat';"}, {"key": "intake_mt_key", "type": "textfield", "input": true, "label": "Intake Mail Template Key", "hidden": true, "disabled": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.intake_kind=='mail'?'intake-asthma-rx':'intake-denial'"}, {"key": "allowed_rt_keys", "type": "textfield", "input": true, "label": "Allowed Rep<PERSON>", "hidden": true, "disabled": true, "multiple": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.intake_mt_key=='intake-denial'?[]:['get-rx','appointment-intake','edit-intake']"}]}