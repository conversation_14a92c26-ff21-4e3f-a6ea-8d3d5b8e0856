{"components": [{"key": "heading_main_mgen", "html": "<h1><strong>Mycoplasma Genitalium Testing and Treatment</strong></h1><p>To expedite your care, please answer the following questions to the best of your abilities. &nbsp;We ask these questions to provide the best possible care. <strong>Mycoplasma Genitalium will be abbreviated to M. Gen</strong>.&nbsp;</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "sex", "type": "radio", "input": true, "label": "What is your biological sex (i.e. sex assigned at birth)?", "inline": false, "values": [{"label": "Male", "value": "male", "shortcut": ""}, {"label": "Female", "value": "female", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "heading_medical_indication", "html": "<h4>Reason for Testing&nbsp;</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "consultation_indication", "type": "selectboxes", "input": true, "label": "Please select your reason(s) for your consultation:", "values": [{"label": "Partner tested positive for M. Gen", "value": "exposed_positive_result", "shortcut": ""}, {"label": "Requirement for Adult Film Work", "value": "adult_film_work", "shortcut": ""}, {"label": "Symptoms", "value": "current_symptoms", "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "confirm_label": "Reason(s) for testing:", "optionsLabelPosition": "right"}, {"key": "consultation_indication_other", "type": "textarea", "input": true, "label": "Please state your “other” reason(s) for M. <PERSON> assessment:", "tableView": true, "autoExpand": false, "customConditional": "show = data.consultation_indication.other;"}, {"key": "heading_testing", "html": "<h4>Previous Lab Testing&nbsp;</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "previous_mgen_test", "type": "radio", "input": true, "label": "Have you been tested for M. Gen in the past?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No/Don't Know", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "last_mgen_test", "data": {"values": [{"label": "<6 weeks", "value": "less_6_weeks"}, {"label": "6 weeks - 3 months", "value": "6_weeks-3_months"}, {"label": "3-6 months", "value": "3-6_months"}, {"label": "6-12 months", "value": "6_12_months"}, {"label": "12-24 months", "value": "12_24_months"}, {"label": "24+ months", "value": "24+_months"}]}, "type": "select", "input": true, "label": "When was your last M. Gen test outside of TeleTest completed?", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.previous_mgen_test == true;", "optionsLabelPosition": "right"}, {"key": "last_mgen_results", "type": "radio", "input": true, "label": "What did your last set of test results show?", "inline": false, "values": [{"label": "Positive for M. Gen (i.e. infection)", "value": "had_mgen_infection", "shortcut": ""}, {"label": "Negative for M. Gen (i.e. no infection)", "value": "no_mgen_infection", "shortcut": ""}, {"label": "Don't Know", "value": "doesn't_know", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.previous_mgen_test == true;", "optionsLabelPosition": "right"}, {"key": "prior_sti_tests", "type": "selectboxes", "input": true, "label": "Please select if any of following tests were completed <strong>after</strong> developing symptoms completed:", "values": [{"label": "Chlamydia", "value": "chlamydia", "shortcut": ""}, {"label": "Gonorrhea", "value": "gonorrhea", "shortcut": ""}, {"label": "Trichomonas", "value": "trichomonas", "shortcut": ""}, {"label": "Urinary Tract Infection (UTI)", "value": "uti", "shortcut": ""}, {"label": "Bacterial Vaginosis/Yeast (swab)", "value": "bv-yeast", "customConditional": "show = data.sex == 'female';", "shortcut": ""}, {"label": "I haven't been tested for STIs since developing symptoms", "value": "no_sti_testing", "shortcut": ""}], "inputType": "checkbox", "tableView": true, "customConditional": "show = data.consultation_indication.current_symptoms;", "optionsLabelPosition": "right"}, {"key": "heading_current_symptoms", "html": "<h4>Symptoms&nbsp;</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.consultation_indication.current_symptoms;", "refreshOnChange": false}, {"key": "list_current_symptoms", "label": "Please select any symptoms that apply to you:", "values": [{"label": "Pain/discomfort with urination", "value": "dysuria", "shortcut": ""}, {"label": "Feeling of incomplete bladder emptying", "value": "incomplete_bladder_emptying", "shortcut": ""}, {"label": "Frequent Urination", "value": "urinary_frequency", "shortcut": ""}, {"label": "Penile discharge", "value": "penile_discharge", "customConditional": "show = data.sex == 'male';", "shortcut": ""}, {"label": "Vaginal discharge", "customConditional": "show = data.sex == 'female';", "value": "vaginal_discharge", "shortcut": ""}, {"label": "Vaginal itchiness", "customConditional": "show = data.sex == 'female';", "value": "vaginal_pruritis", "shortcut": ""}, {"label": "Bleeding after sex", "customConditional": "show = data.sex == 'female';", "value": "vaginal_bleeding", "shortcut": ""}, {"label": "Abdominal or pelvic pain/cramping", "value": "abdo_pelvic_pain", "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}], "input": true, "inputType": "checkbox", "customConditional": "show = data.consultation_indication.current_symptoms;", "optionsLabelPosition": "right", "tableView": true, "type": "selectboxes"}, {"key": "symptom_other", "type": "textarea", "input": true, "label": "Please state your “other” symptoms:", "tableView": true, "autoExpand": false, "customConditional": "show = data.list_current_symptoms && data.list_current_symptoms.other;"}, {"key": "symptom_duration", "data": {"values": [{"label": "1 day", "value": "1_Day"}, {"label": "2 days", "value": "2_Days"}, {"label": "3 days", "value": "3_Days"}, {"label": "4 days", "value": "4_Days"}, {"label": "5 days", "value": "5_Days"}, {"label": "6 days", "value": "6_Days"}, {"label": "7 days", "value": "7_Days"}, {"label": "8 days", "value": "8_Days"}, {"label": "9 days", "value": "9_Days"}, {"label": "10-30 days", "value": "10-30_Days"}, {"label": "30+ days", "value": "30+_Days"}]}, "type": "select", "input": true, "label": "How long have you had your symptoms?", "customConditional": "show = data.consultation_indication.current_symptoms;", "widget": "html5", "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "vag_quality", "type": "selectboxes", "input": true, "label": "How would you describe your discharge?", "inline": false, "values": [{"label": "Thin ", "value": "thin", "shortcut": ""}, {"label": "<PERSON><PERSON><PERSON>", "value": "thick", "shortcut": ""}, {"label": "Watery", "value": "watery", "shortcut": ""}, {"label": "Yellow", "value": "yellow", "shortcut": ""}, {"label": "Greyish", "value": "grey", "shortcut": ""}, {"label": "<PERSON>", "value": "brown", "shortcut": ""}, {"label": "Cottage-Cheese Like", "value": "curdish", "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.list_current_symptoms && data.list_current_symptoms.vaginal_discharge;", "optionsLabelPosition": "right"}, {"key": "pap_past", "type": "radio", "input": true, "label": "Have you ever had a PAP test in the past?", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "I don't know what a PAP test is", "value": "doesn't_know_about_paps", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.sex == 'female';", "optionsLabelPosition": "right"}, {"key": "last_pap_date", "data": {"values": [{"label": "<1 year ago", "value": "<1_year"}, {"label": "1-2 years ago", "value": "1-2_years"}, {"label": "2-3 years ago", "value": "2-3_years"}, {"label": "3-4 years ago", "value": "3-4_years"}, {"label": "4-5 years ago", "value": "4-5_years"}, {"label": "5+ years ago", "value": "5+_years"}]}, "type": "select", "input": true, "label": "When was your last PAP test completed?", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.pap_past == 'yes';", "optionsLabelPosition": "right"}, {"key": "pap_past_normal", "type": "radio", "input": true, "label": "Was your last PAP test normal and have you ever had abnormal PAP testing?", "inline": false, "values": [{"label": "My last PAP test was normal, and I've never had an abnormal PAP before.", "value": "normal_pap_lifetime", "shortcut": ""}, {"label": "My last PAP test was normal, but I had an abnormal PAP in the past.", "value": "normal_pap_abnormal_past", "shortcut": ""}, {"label": "My last PAP test was abnormal and I <strong>have follow up scheduled</strong>.", "value": "abnormal_pap_has_fu", "shortcut": ""}, {"label": "My last PAP test was abnormal and I <strong>do not have follow up scheduled</strong>.", "value": "abnormal_pap_needs_fu", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.pap_past == 'yes';", "optionsLabelPosition": "right"}, {"key": "heading_medication", "html": "<h3>Previous Treatment&nbsp;</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "treated_past", "type": "radio", "input": true, "label": "Have you been treated for M. Gen in the past?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "previous_treatments", "type": "radio", "input": true, "label": "Please select which of the following medication you tried in the past:", "inline": false, "values": [{"label": "Doxycyline 100mg twice daily for 7 days <strong>THEN </strong>  azithromycin 1 g orally initial dose, followed by 500 mg orally once daily for 3 additional days", "value": "doxy-azithro", "shortcut": ""}, {"label": "Doxycycline 100 mg orally 2 times/day for 7 days <strong>THEN</strong> Moxifloxacin 400 mg orally once daily for 7 days", "value": "doxy-moxi", "shortcut": ""}, {"label": "Ceftriaxone 0.25g injection and Azithromycin (1g) (i.e. an injection with 4 pills)", "value": "ceftriaxone_azithromycin", "shortcut": ""}, {"label": "I don't know", "value": "doesn't_know", "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.treated_past == true;", "optionsLabelPosition": "right"}, {"key": "other_medication", "type": "textarea", "input": true, "label": "Please state your “other” medication:", "tableView": true, "autoExpand": false, "customConditional": "show = data.previous_treatments == 'other';"}, {"key": "last_mgen_treatment", "data": {"values": [{"label": "<1 week ago", "value": "less_1_weeks"}, {"label": "2-4 weeks ago", "value": "2-4_weeks_ago"}, {"label": "4 weeks - 6 weeks ago", "value": "4_weeks-6_weeks"}, {"label": "6 weeks - 3 months ago", "value": "6_weeks-3_months"}, {"label": "3-6 months", "value": "3-6_months"}, {"label": "6-12 months", "value": "6_12_months"}, {"label": "12-24 months", "value": "12_24_months"}, {"label": "24+ months", "value": "24+_months"}]}, "type": "select", "input": true, "label": "When were you last treated for M. Gen?", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.treated_past == true;", "optionsLabelPosition": "right"}, {"key": "content2", "html": "<h2>Questions for the Doctor</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "additional_questions", "type": "textarea", "input": true, "label": "Please use this area to ask any questions that you might have for your health care provider:", "tableView": true, "autoExpand": false}]}