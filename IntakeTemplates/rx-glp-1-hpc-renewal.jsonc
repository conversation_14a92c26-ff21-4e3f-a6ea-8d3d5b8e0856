{"components": [{"key": "heading_glp1_agonists", "html": "<h1><strong>Weight Loss Medication Renewal</strong></h1><p>Please answer the following questions to help us understand your treatment history and any side effects you may have experienced.</p>", "type": "content", "input": false, "label": "Content"}, {"key": "glp1_diagnosis_header", "html": "<h2>Weight Loss History</h2>", "type": "content", "input": false, "label": "Content"}, {"key": "obesityDuration", "type": "radio", "input": true, "label": "How long have you struggled with weight?", "values": [{"label": "1-3 years", "value": "1-3_years"}, {"label": "3-5 years", "value": "3-5_years"}, {"label": "5-10 years", "value": "5-10_years"}, {"label": "10+ years", "value": "10+_years"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "weight_loss_attempts", "type": "radio", "input": true, "label": "How many times have you tried to lose weight?", "values": [{"label": "1-3 times", "value": "1-3_times"}, {"label": "3-5 times", "value": "3-5_times"}, {"label": "5-10 times", "value": "5-10_times"}, {"label": "10+ times", "value": "10+_times"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "weight_loss_methods", "type": "selectboxes", "input": true, "label": "What methods have you tried to lose weight? (Select all that apply)", "values": [{"label": "Diet", "value": "diet"}, {"label": "Exercise", "value": "exercise"}, {"label": "Weight loss medication", "value": "weight_loss_medication"}, {"label": "Bariatric surgery", "value": "bariatric_surgery"}, {"label": "Other", "value": "other"}], "tableView": true, "optionsLabelPosition": "right"}, {"key": "weight_loss_medication_attempts", "type": "radio", "input": true, "label": "How many times have you tried weight loss medication?", "values": [{"label": "Never tried", "value": "never_tried"}, {"label": "Once", "value": "once"}, {"label": "2-3 times", "value": "2-3_times"}, {"label": "4+ times", "value": "4+_times"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "weight_loss_methods", "type": "selectboxes", "input": true, "label": "What methods have you tried to lose weight? (Select all that apply)", "values": [{"label": "Diet", "value": "diet"}, {"label": "Exercise", "value": "exercise"}, {"label": "Weight loss medication", "value": "weight_loss_medication"}, {"label": "Bariatric surgery", "value": "bariatric_surgery"}, {"label": "Other", "value": "other"}], "tableView": true, "optionsLabelPosition": "right"}, {"key": "header_lifestyle_factors", "html": "<h2>Lifestyle</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "physical_activity_level", "type": "radio", "input": true, "tooltip": "Physical activity includes exercise and other activities that require movement, like walking, gardening, or playing a sport.", "label": "How would you describe your level of physical activity?", "values": [{"label": "Sedentary (little or no exercise)", "value": "sedentary"}, {"label": "Lightly active (1-3 days/week)", "value": "lightly_active"}, {"label": "Moderately active (3-5 days/week)", "value": "moderately_active"}, {"label": "Very active (6-7 days a week)", "value": "very_active"}], "tableView": true, "optionsLabelPosition": "right"}, {"key": "sleep_habits", "type": "radio", "input": true, "label": "On average, how many hours of sleep do you get per night?", "values": [{"label": "Less than 4 hours", "value": "less_than_4"}, {"label": "4-6 hours", "value": "4_to_6"}, {"label": "7-9 hours (recommended)", "value": "7_to_9"}, {"label": "More than 9 hours", "value": "more_than_9"}], "tableView": true, "optionsLabelPosition": "right"}, {"key": "nutrition_eating_habits", "type": "selectboxes", "input": true, "label": "Which of the following best describe your eating habits? (Select all that apply)", "values": [{"label": "Skip meals regularly", "value": "skip_meals"}, {"label": "Eat out frequently", "value": "eat_out_frequently"}, {"label": "Cook most of my meals", "value": "cook_meals"}, {"label": "Follow a specific diet plan", "value": "specific_diet"}, {"label": "Eat fruits and vegetables daily", "value": "fruits_vegetables"}, {"label": "Often eat fast food", "value": "fast_food"}, {"label": "Monitor my portion sizes", "value": "portion_sizes"}, {"label": "Other", "value": "other"}], "tableView": true, "optionsLabelPosition": "right"}, {"key": "working_with_dietitian", "type": "radio", "input": true, "label": "Have you worked with a dietitian for nutrition advice or meal planning?", "values": [{"label": "Yes, currently working with one", "value": "yes_currently"}, {"label": "Yes, but in the past", "value": "yes_past"}, {"label": "No", "value": "no"}, {"label": "Considering it", "value": "considering"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "working_with_fitness_trainer", "type": "radio", "input": true, "label": "Have you worked with a fitness trainer for physical activity or exercise plans?", "values": [{"label": "Yes, currently working with one", "value": "yes_currently"}, {"label": "Yes, but in the past", "value": "yes_past"}, {"label": "No", "value": "no"}, {"label": "Considering it", "value": "considering"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "drink_alcohol", "type": "radio", "input": true, "label": "Do you drink alcohol?", "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}], "validate": {"required": true}, "tableView": true}, {"key": "type_of_alcohol", "type": "selectboxes", "validate": {"required": true}, "input": true, "label": "What type of alcohol do you usually drink? (Select all that apply)", "values": [{"label": "Beer", "value": "beer"}, {"label": "Wine", "value": "wine"}, {"label": "Spirits", "value": "spirits"}, {"label": "Other", "value": "other"}], "tableView": true, "customConditional": "show = data.drink_alcohol === 'yes';", "optionsLabelPosition": "right"}, {"key": "header_beer_details", "html": "<h4>Beer Details</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.drink_alcohol === 'yes' && data.type_of_alcohol.beer;", "refreshOnChange": false}, {"key": "beer_format", "type": "radio", "input": true, "validate": {"required": true}, "label": "What is the typical size of the bottle/can you drink?", "values": [{"label": "473ml", "value": "473ml"}, {"label": "355ml", "value": "355ml"}, {"label": "500ml", "value": "500ml"}, {"label": "650ml", "value": "650ml"}], "tableView": true, "customConditional": "show = data.drink_alcohol === 'yes' && data.type_of_alcohol.beer;"}, {"key": "beer_abv_content", "type": "radio", "input": true, "validate": {"required": true}, "label": "What is the typical Alcohol By Volume (ABV) content of the beer you drink?", "values": [{"label": "Non-alcoholic (<0.5%)", "value": "non_alcoholic"}, {"label": "Low (0.5% - 3.5%)", "value": "low"}, {"label": "Standard (3.5% - 5.5%)", "value": "standard"}, {"label": "High (>5.5%)", "value": "high"}], "tableView": true, "customConditional": "show = data.drink_alcohol === 'yes' && data.type_of_alcohol.beer;"}, {"key": "number_of_beers_per_day", "type": "radio", "input": true, "validate": {"required": true}, "label": "How many beers do you typically drink in a day?", "tooltip": "Please calculate the average number of beers you drink per day. For example, if you drink 24 beers a week, this would average out to approximately 3 beers per day.", "values": [{"label": "None", "value": "none"}, {"label": "Less than 1 beer", "value": "less_than_1_beer"}, {"label": "1 beer", "value": "1_beer"}, {"label": "2 beers", "value": "2_beers"}, {"label": "3-4 beers", "value": "3_4_beers"}, {"label": "5 or more beers", "value": "5_more_beers"}], "tableView": true, "customConditional": "show = data.drink_alcohol === 'yes' && data.type_of_alcohol.beer;"}, {"key": "header_wine_details", "html": "<h4>Wine Details</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.drink_alcohol === 'yes' && data.type_of_alcohol.wine;", "refreshOnChange": false}, {"key": "wine_bottle_size", "type": "radio", "input": true, "label": "What is the typical size of the wine bottle you drink?", "validate": {"required": true}, "values": [{"label": "375ml (Half bottle)", "value": "375ml"}, {"label": "750ml (Standard bottle)", "value": "750ml"}, {"label": "1 Liter", "value": "1liter"}, {"label": "1.5 Liters (Magnum)", "value": "1.5liters"}], "tableView": true, "customConditional": "show = data.drink_alcohol === 'yes' && data.type_of_alcohol.wine;"}, {"key": "wine_abv_content", "type": "radio", "validate": {"required": true}, "input": true, "label": "What is the typical Alcohol By Volume (ABV) content of the wine you drink?", "values": [{"label": "Low (<8%)", "value": "low"}, {"label": "Medium (8% - 14%)", "value": "medium"}, {"label": "High (>14%)", "value": "high"}], "tableView": true, "customConditional": "show = data.drink_alcohol === 'yes' && data.type_of_alcohol.wine;"}, {"key": "wine_bottle_duration", "type": "radio", "input": true, "validate": {"required": true}, "label": "If you're drinking wine by yourself, how long does a bottle typically last?", "values": [{"label": "Less than a day", "value": "less_than_a_day"}, {"label": "1 day", "value": "1_day"}, {"label": "2 days", "value": "2_days"}, {"label": "3 days", "value": "3_days"}, {"label": "4 days", "value": "4_days"}, {"label": "5 days", "value": "5_days"}, {"label": "More than 5 days", "value": "more_than_5_days"}, {"label": "I rarely finish a bottle", "value": "rarely_finish"}], "tableView": true, "customConditional": "show = data.drink_alcohol === 'yes' && data.type_of_alcohol.wine;"}, {"key": "header_spirits_details", "html": "<h4>Spirits Details</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.drink_alcohol === 'yes' && data.type_of_alcohol.spirits;", "refreshOnChange": false}, {"key": "spirits_bottle_size", "type": "radio", "input": true, "validate": {"required": true}, "label": "What is the typical size of the spirits bottle you drink?", "values": [{"label": "0.375 Liters (12.7 oz)", "value": "375ml_12.7oz"}, {"label": "0.75 Liters (25.4 oz)", "value": "750ml_25.4oz"}, {"label": "1 Liter (33.8 oz)", "value": "1liter_33.8oz"}, {"label": "1.14 Liters (38.5 oz)", "value": "1.14liter_38.5oz"}, {"label": "1.75 Liters (59.2 oz)", "value": "1.75liters_59.2oz"}], "tableView": true, "customConditional": "show = data.drink_alcohol === 'yes' && data.type_of_alcohol.spirits;"}, {"key": "spirits_abv_content", "type": "radio", "input": true, "validate": {"required": true}, "label": "What is the typical Alcohol By Volume (ABV) content of the spirits you drink?", "values": [{"label": "Low (<20%)", "value": "low"}, {"label": "Medium (20% - 40%)", "value": "medium"}, {"label": "High (>40%)", "value": "high"}], "tableView": true, "customConditional": "show = data.drink_alcohol === 'yes' && data.type_of_alcohol.spirits;"}, {"key": "spirits_bottle_duration", "type": "radio", "input": true, "validate": {"required": true}, "label": "If you're drinking spirits by yourself, how long does a bottle typically last?", "values": [{"label": "Less than a day", "value": "less_than_a_day"}, {"label": "1-2 days", "value": "1_2_days"}, {"label": "3-4 days", "value": "3_4_days"}, {"label": "5-9 days", "value": "5_9_days"}, {"label": "10-14 days", "value": "10_14_days"}, {"label": "15-19 days", "value": "15_19_days"}, {"label": "20-24 days", "value": "20_24_days"}, {"label": "25-29 days", "value": "25_29_days"}, {"label": "More than 30 days", "value": "more_than_30_days"}, {"label": "I rarely finish a bottle", "value": "rarely_finish"}], "tableView": true, "customConditional": "show = data.drink_alcohol === 'yes' && data.type_of_alcohol.spirits;"}, {"key": "weight_parameters_header", "html": "<h2>Weight Measurements</h2>", "type": "content", "input": false, "label": "Content"}, {"type": "columns", "columns": [{"components": [{"key": "highest_weight_value", "type": "number", "input": true, "label": "Heaviest weight", "tooltip": "Please enter the highest weight you have been in the past", "placeholder": "Enter weight", "tableView": true}], "width": 6, "offset": 0, "push": 0, "pull": 0}, {"components": [{"key": "highest_weight_unit", "type": "select", "widget": "html5", "input": true, "label": "Unit", "defaultValue": "lbs", "data": {"values": [{"label": "Pounds (lbs)", "value": "lbs"}, {"label": "Kilograms (kg)", "value": "kg"}]}, "tableView": true}], "width": 6, "offset": 0, "push": 0, "pull": 0}], "key": "highest_weight"}, {"type": "columns", "columns": [{"components": [{"key": "current_weight_value", "type": "number", "input": true, "label": "Current Weight", "placeholder": "Enter weight", "tableView": true}], "width": 6, "offset": 0, "push": 0, "pull": 0}, {"components": [{"key": "current_weight_unit", "type": "select", "widget": "html5", "input": true, "label": "Unit", "defaultValue": "lbs", "data": {"values": [{"label": "Pounds (lbs)", "value": "lbs"}, {"label": "Kilograms (kg)", "value": "kg"}]}, "tableView": true}], "width": 6, "offset": 0, "push": 0, "pull": 0}], "key": "current_weight"}, {"type": "columns", "columns": [{"components": [{"key": "current_weight_value", "type": "number", "input": true, "label": "Weight before starting medication", "placeholder": "Enter weight", "tableView": true}], "width": 6, "offset": 0, "push": 0, "pull": 0}, {"components": [{"key": "weight_onset_therapy_unit", "type": "select", "widget": "html5", "input": true, "label": "Unit", "defaultValue": "lbs", "data": {"values": [{"label": "Pounds (lbs)", "value": "lbs"}, {"label": "Kilograms (kg)", "value": "kg"}]}, "tableView": true}], "width": 6, "offset": 0, "push": 0, "pull": 0}], "key": "onset_therapy_weight"}, {"key": "glp1_diagnosis_header", "html": "<h2>Weight Loss Medications</h2>", "type": "content", "input": false, "label": "Content"}, {"key": "status_on_medication", "type": "radio", "input": true, "label": "Are you currently or have you previously been on weight loss medication?", "values": [{"label": "Currently on medication", "value": "current"}, {"label": "Previously on medication", "value": "previous"}, {"label": "Never been on medication", "value": "never"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "glp1_indication", "type": "radio", "input": true, "label": "What is the primary reason you were prescribed your medication?", "values": [{"label": "Diabetes", "value": "diabetes_management"}, {"label": "Pre-diabetes", "value": "pre_diabetes"}, {"label": "Weight loss", "value": "weight_loss"}, {"label": "Other", "value": "other"}], "customConditional": "show = data.currently_on_glp1 == 'yes';", "validate": {"required": true}}, {"key": "current_weight_loss_medication", "type": "radio", "input": true, "label": "Please specify the weight loss medication you have been on:", "values": [{"label": "<strong><PERSON><PERSON><PERSON></strong> (Generic Name: Semaglutide)", "value": "wegovy", "shortcut": ""}, {"label": "<strong>Ozempic</strong> (Generic: Semaglutide)", "value": "ozempic", "shortcut": ""}, {"label": "<strong><PERSON><PERSON><PERSON><PERSON></strong> (Generic: Semaglutide)", "value": "ry<PERSON><PERSON>", "shortcut": ""}, {"label": "<strong><PERSON><PERSON><PERSON></strong> (Generic: Liraglutide)", "value": "sa<PERSON><PERSON>", "shortcut": ""}, {"label": "<strong>Trulicity</strong> (Generic: Dulaglutide)", "value": "trulicity", "shortcut": ""}, {"label": "<strong><PERSON><PERSON><PERSON><PERSON></strong> (Generic: Tirzepatide)", "value": "mou<PERSON><PERSON>", "shortcut": ""}, {"label": "Other", "value": "other"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.status_on_medication == 'current' || data.status_on_medication == 'previous';", "optionsLabelPosition": "right"}, {"key": "other_weight_loss_medication", "type": "textfield", "input": true, "label": "Please specify your weight loss medication:", "validate": {"required": true, "custom": "valid = data.current_weight_loss_medication !== 'other' || !!data.other_weight_loss_medication;", "customMessage": "This field is required when 'Other' is selected for weight loss medication."}, "customConditional": "show = data.current_weight_loss_medication === 'other';", "placeholder": "Enter medication name", "tableView": true}, {"key": "glp1_current_prescription_header", "html": "<h2>Current Prescriptions</h2>", "customConditional": "show = data.status_on_medication == 'current' || data.status_on_medication == 'previous';", "type": "content", "input": false, "label": "Content"}, {"key": "wegovy_prescription_header", "html": "<h3>Wegovy (Semaglutide)</h3>", "customConditional": "show = data.current_weight_loss_medication == 'wegovy';", "type": "content", "input": false, "label": "Content"}, {"key": "current_medication_dose_wegovy", "type": "radio", "input": true, "label": "What is your current dose?", "values": [{"label": "0.25 mg weekly", "value": "0.25_mg_weekly"}, {"label": "0.5 mg weekly", "value": "0.5_mg_weekly"}, {"label": "1 mg weekly", "value": "1_mg_weekly"}, {"label": "1.7 mg weekly", "value": "1.7_mg_weekly"}, {"label": "2 mg weekly", "value": "2_mg_weekly"}, {"label": "2.4 mg weekly", "value": "2.4_mg_weekly"}, {"label": "2.5 mg weekly", "value": "2.5_mg_weekly"}, {"label": "Other", "value": "other"}], "validate": {"required": true}, "customConditional": "show = data.current_weight_loss_medication == 'wegovy';", "tableView": true, "optionsLabelPosition": "right"}, {"key": "specify_other_dose_wegovy", "type": "textfield", "input": true, "label": "Please specify your Wegovy dose:", "conditional": {"show": true, "when": "current_medication_dose_wegovy", "eq": "other"}, "validate": {"required": true}, "tableView": true}, {"key": "ozempic_prescription_header", "html": "<h3>Ozempic (Semaglutide)</h3>", "customConditional": "show = data.current_weight_loss_medication == 'ozempic';", "type": "content", "input": false, "label": "Content"}, {"key": "current_medication_dose_ozempic", "type": "radio", "input": true, "label": "What is your current dose of Ozempic?", "values": [{"label": "0.25 mg weekly (starting dose)", "value": "0.25_mg_weekly"}, {"label": "0.5 mg weekly", "value": "0.5_mg_weekly"}, {"label": "1 mg weekly", "value": "1_mg_weekly"}, {"label": "2 mg weekly", "value": "2_mg_weekly"}, {"label": "Other", "value": "other"}], "validate": {"required": true}, "customConditional": "show = data.current_weight_loss_medication == 'ozempic';", "tableView": true, "optionsLabelPosition": "right"}, {"key": "specify_other_dose_ozempic", "type": "textfield", "input": true, "label": "Please specify your Ozempic dose:", "conditional": {"show": true, "when": "current_medication_dose_ozempic", "eq": "other"}, "validate": {"required": true}, "tableView": true}, {"key": "rybelsus_prescription_header", "html": "<h3><PERSON><PERSON><PERSON><PERSON> (Semaglutide)</h3>", "customConditional": "show = data.current_weight_loss_medication == 'rybelsus';", "type": "content", "input": false, "label": "Content"}, {"key": "current_medication_dose_rybelsus", "type": "radio", "input": true, "label": "What is your current dose of Rybelsus?", "values": [{"label": "3 mg daily", "value": "3_mg_daily"}, {"label": "7 mg daily", "value": "7_mg_daily"}, {"label": "14 mg daily", "value": "14_mg_daily"}, {"label": "Other (please specify)", "value": "other"}, {"label": "I'm not sure", "value": "not_sure"}, {"label": "I haven't started yet", "value": "not_started"}], "validate": {"required": true}, "customConditional": "show = data.current_weight_loss_medication == 'rybelsus';", "tableView": true, "optionsLabelPosition": "right"}, {"key": "specify_other_dose_rybelsus", "type": "textfield", "input": true, "label": "Please specify your Rybelsus dose:", "conditional": {"show": true, "when": "current_medication_dose_rybelsus", "eq": "other"}, "validate": {"required": true}, "tableView": true}, {"key": "saxenda_prescription_header", "html": "<h3><PERSON><PERSON><PERSON> (Liraglutide)</h3>", "customConditional": "show = data.current_weight_loss_medication == 'saxenda';", "type": "content", "input": false, "label": "Content"}, {"key": "current_medication_dose_saxenda", "type": "radio", "input": true, "label": "What is your current dose of <PERSON><PERSON><PERSON>?", "values": [{"label": "0.6 mg daily", "value": "0.6_mg_daily"}, {"label": "1.2 mg daily", "value": "1.2_mg_daily"}, {"label": "1.8 mg daily", "value": "1.8_mg_daily"}, {"label": "2.4 mg daily", "value": "2.4_mg_daily"}, {"label": "3.0 mg daily (maximum dose)", "value": "3.0_mg_daily"}, {"label": "Other (please specify)", "value": "other"}, {"label": "I'm not sure", "value": "not_sure"}, {"label": "I haven't started yet", "value": "not_started"}], "validate": {"required": true}, "customConditional": "show = data.current_weight_loss_medication == 'saxenda';", "tableView": true, "optionsLabelPosition": "right"}, {"key": "specify_other_dose_saxenda", "type": "textfield", "input": true, "label": "Please specify your Saxenda dose:", "conditional": {"show": true, "when": "current_medication_dose_saxenda", "eq": "other"}, "validate": {"required": true}, "tableView": true}, {"key": "trulicity_prescription_header", "html": "<h3>Trulicity (Dulaglutide)</h3>", "customConditional": "show = data.current_weight_loss_medication == 'trulicity';", "type": "content", "input": false, "label": "Content"}, {"key": "current_medication_dose_trulicity", "type": "radio", "input": true, "label": "What is your current dose of Trulicity?", "values": [{"label": "0.75 mg weekly", "value": "0.75_mg_weekly"}, {"label": "1.5 mg weekly", "value": "1.5_mg_weekly"}, {"label": "3.0 mg weekly", "value": "3.0_mg_weekly"}, {"label": "4.5 mg weekly (maximum dose)", "value": "4.5_mg_weekly"}, {"label": "Other (please specify)", "value": "other"}, {"label": "I'm not sure", "value": "not_sure"}, {"label": "I haven't started yet", "value": "not_started"}], "validate": {"required": true}, "customConditional": "show = data.current_weight_loss_medication == 'trulicity';", "tableView": true, "optionsLabelPosition": "right"}, {"key": "specify_other_dose_trulicity", "type": "textfield", "input": true, "label": "Please specify your Trulicity dose:", "conditional": {"show": true, "when": "current_medication_dose_trulicity", "eq": "other"}, "validate": {"required": true}, "tableView": true}, {"key": "mounjaro_prescription_header", "html": "<h3><PERSON><PERSON><PERSON><PERSON> (Tirzepatide)</h3>", "customConditional": "show = data.current_weight_loss_medication == 'mounjaro';", "type": "content", "input": false, "label": "Content"}, {"key": "current_medication_dose_mounjaro", "type": "radio", "input": true, "label": "What is your current dose of <PERSON><PERSON><PERSON><PERSON>?", "values": [{"label": "2.5 mg weekly", "value": "2.5_mg_weekly"}, {"label": "5 mg weekly", "value": "5_mg_weekly"}, {"label": "7.5 mg weekly", "value": "7.5_mg_weekly"}, {"label": "10 mg weekly", "value": "10_mg_weekly"}, {"label": "12.5 mg weekly", "value": "12.5_mg_weekly"}, {"label": "15 mg weekly", "value": "15_mg_weekly"}, {"label": "Other (please specify)", "value": "other"}, {"label": "I'm not sure", "value": "not_sure"}, {"label": "I haven't started yet", "value": "not_started"}], "validate": {"required": true}, "customConditional": "show = data.current_weight_loss_medication == 'mounjaro';", "tableView": true, "optionsLabelPosition": "right"}, {"key": "specify_other_dose_mounjaro", "type": "textfield", "input": true, "label": "Please specify your Mounjaro dose:", "conditional": {"show": true, "when": "current_medication_dose_mounjaro", "eq": "other"}, "validate": {"required": true}, "tableView": true}, {"key": "start_weight_loss_medication", "type": "select", "input": true, "label": "When did you start your current <strong>weight loss medication</strong>?", "data": {"values": [{"label": "I haven't started yet", "value": "not_started"}, {"label": "<1 week ago", "value": "less_1_week"}, {"label": "2-4 weeks ago", "value": "2-4_weeks_ago"}, {"label": "4 weeks - 6 weeks ago", "value": "4-6_weeks_ago"}, {"label": "6 weeks - 3 months ago", "value": "6_weeks-3_months_ago"}, {"label": "3-6 months ago", "value": "3-6_months_ago"}, {"label": "6-12 months ago", "value": "6-12_months_ago"}, {"label": "12-24 months ago", "value": "12-24_months_ago"}, {"label": "24+ months ago", "value": "24+_months_ago"}]}, "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.status_on_medication == 'current' || data.status_on_medication == 'previous';", "optionsLabelPosition": "right"}, {"key": "glp1_side_effects", "html": "<h2>Side Effects</h2>", "type": "content", "input": false, "label": "Content", "customConditional": "show = data.currently_on_glp1 == 'yes';"}, {"key": "experienced_side_effects", "type": "selectboxes", "input": true, "label": "Have you experienced any of the following side effects?", "values": [{"label": "<PERSON><PERSON><PERSON>", "value": "nausea"}, {"label": "Vomiting", "value": "vomiting"}, {"label": "Diarrhea", "value": "diarrhea"}, {"label": "Constipation", "value": "constipation"}, {"label": "Appetite loss", "value": "appetite_loss"}, {"label": "Injection site reaction", "value": "injection_site_reaction"}, {"label": "Other", "value": "other"}, {"label": "I have not experienced any side effects", "value": "none"}], "customConditional": "show = data.status_on_medication == 'current' || data.status_on_medication == 'previous';", "validate": {"required": true}}, {"key": "glp1_medication_contraindications", "html": "<h2>Other Health Conditions and Medications</h2>", "type": "content", "input": false, "label": "Content"}, {"key": "eating_disorder_detail", "type": "selectboxes", "input": true, "label": "Have you ever been diagnosed with or suspected of having any of the following eating disorders? (Select all that apply)", "values": [{"label": "Anorexia Nervosa", "value": "anorexia_nervosa", "shortcut": ""}, {"label": "Bulimia Nervosa", "value": "bulimia_nervosa", "shortcut": ""}, {"label": "Binge Eating Disorder", "value": "binge_eating_disorder", "shortcut": ""}, {"label": "Avoidant/Restrictive Food Intake Disorder (ARFID)", "value": "arfid", "shortcut": ""}, {"label": "Other Eating Disorder", "value": "other_eating_disorder", "shortcut": ""}], "placeholder": "Select an option", "tableView": true, "optionsLabelPosition": "right"}, {"key": "none_of_the_above_eating_disorders", "type": "checkbox", "input": true, "label": "None of the above", "tableView": false, "defaultValue": false, "customClass": "mt-n3", "validate": {"custom": "valid = _.some(_.values(data.eating_disorder_detail)) || data.none_of_the_above_eating_disorders;", "customMessage": "Please select at least one type of eating disorder, or confirm that none apply."}}, {"key": "eating_disorder_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following eating disorders:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": true, "tableView": true, "spellcheck": false, "clearOnHide": false, "calculateValue": {"custom": "value = _.join(_.map(_.keys(_.omitBy(data.eating_disorder_detail, _.identity)), function(key) { return _.startCase(_.replace(key, /_/g, ' ')); }), ', ');"}}, {"key": "contraindicationsMEN2", "type": "selectboxes", "input": true, "label": "Do you have any of the following conditions? (Select all that apply)", "values": [{"label": "Medullary Thyroid Cancer (MTC)", "value": "MTC", "shortcut": ""}, {"label": "Pheochromocytoma", "value": "pheochromocytoma", "shortcut": ""}, {"label": "Primary Parathyroid Hyperplasia", "value": "primary_parathyroid_disease", "shortcut": ""}, {"label": "Irregular heart rhythm", "value": "arrhythmia", "shortcut": ""}], "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = !data.current_medication.none && (data.sex == 'male' || (data.sex == 'female' && data.currently_pregnant == 'no'));"}, {"key": "contraindicationsMEN2_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following diagnosed contraindications:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "calculateValue": {"custom": "value = _.join(_.map(_.keys(_.omitBy(data.contraindicationsMEN2, _.identity)), function(key) { return _.startCase(_.replace(key, /_/g, ' ')); }), ', ');"}}, {"key": "none_of_the_above_contraindicationsMEN2", "type": "checkbox", "input": true, "label": "None of the above", "validate": {"custom": "valid = _.some(_.values(data.contraindicationsMEN2)) || data.none_of_the_above_contraindicationsMEN2;"}, "tableView": false, "defaultValue": false, "customConditional": "show = !data.current_medication.none && (data.sex == 'male' || (data.sex == 'female' && data.currently_pregnant == 'no'));", "customClass": "mt-n3", "errors": {"custom": "Please select at least one condition you have, or select 'None of the above'."}}, {"key": "current_DPP4_inhibitors_insulin", "type": "selectboxes", "input": true, "label": "Are you currently taking any of the following medications? (Select all that apply)", "values": [{"label": "<PERSON><PERSON><PERSON><PERSON> (Januvia)", "value": "sitagliptin_januvia", "shortcut": ""}, {"label": "Saxagliptin (Onglyza)", "value": "saxagliptin_onglyza", "shortcut": ""}, {"label": "<PERSON><PERSON><PERSON><PERSON> (Tradjenta)", "value": "linagliptin_tradjenta", "shortcut": ""}, {"label": "<PERSON><PERSON><PERSON>", "value": "insulin", "shortcut": ""}], "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = !data.current_medication.none && (data.sex == 'male' || (data.sex == 'female' && data.currently_pregnant == 'no'));"}, {"key": "none_of_the_above_DPP4_inhibitors_insulin", "type": "checkbox", "input": true, "label": "None of the above", "validate": {"custom": "valid = _.some(_.values(data.current_DPP4_inhibitors_insulin)) || data.none_of_the_above_DPP4_inhibitors_insulin;"}, "tableView": false, "defaultValue": false, "customConditional": "show = !data.current_medication.none && (data.sex == 'male' || (data.sex == 'female' && data.currently_pregnant == 'no'));", "customClass": "mt-n3", "errors": {"custom": "Please select at least one medication you are currently taking, or select 'None of the above'."}}, {"key": "DPP4_inhibitors_insulin_not_present", "type": "textfield", "input": true, "label": "Patient indicated they ARE NOT currently taking any of the following medications:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "calculateValue": {"custom": "value = _.join(_.map(_.keys(_.omitBy(data.current_DPP4_inhibitors_insulin, _.identity)), function(key) { return _.startCase(_.replace(key, /_/g, ' ')); }), ', ');"}}, {"key": "heart_block_tachyarrhythmias_select", "type": "selectboxes", "input": true, "label": "Have you ever been diagnosed with heart block (2nd or 3rd degree) or tachyarrhythmias?", "values": [{"label": "2nd degree heart block", "value": "2nd_degree_heart_block"}, {"label": "3rd degree heart block", "value": "3rd_degree_heart_block"}, {"label": "I do not have any known irregular heart rhythms", "value": "none"}, {"label": "Other irregular rhythm", "value": "other"}], "validate": {"required": true}}, {"key": "semaglutide_method", "type": "radio", "input": true, "label": "Select your Semaglutide dosing method:", "values": [{"label": "Tablets (Oral)", "value": "oral"}, {"label": "Injection (Injectable)", "value": "injectable"}], "customConditional": "show = data.glp1_medication_name == 'semaglutide';", "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "semaglutide_brand", "type": "select", "input": true, "label": "Which brand of Semaglutide are you currently using?", "values": [{"label": "Ozempic", "value": "ozempic"}, {"label": "R<PERSON>bel<PERSON>", "value": "ry<PERSON><PERSON>"}, {"label": "Wegovy", "value": "wegovy"}], "customConditional": "show = data.semaglutide_method == 'injectable';", "validate": {"required": true}}, {"key": "ozempic_dosing_schedule", "type": "radio", "input": true, "label": "If you are on Ozempic, select your dosing schedule:", "values": [{"label": "0.25 mg weekly (starting dose)", "value": "0.25mg_weekly"}, {"label": "0.5 mg weekly", "value": "0.5mg_weekly"}, {"label": "1 mg weekly", "value": "1mg_weekly"}, {"label": "2 mg weekly", "value": "2mg_weekly"}, {"label": "Other", "value": "other"}], "conditional": {"show": true, "when": "glp1_medication_name", "eq": "semaglutide", "and": [{"show": true, "when": "semaglutide_method", "eq": "injectable"}]}, "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "ozempic_dosing_schedule_other", "type": "textfield", "input": true, "label": "Please specify your Ozempic dose if 'Other':", "conditional": {"show": true, "when": "ozempic_dosing_schedule", "eq": "other"}, "validate": {"required": true, "customMessage": "Please specify your Ozempic dose."}}, {"key": "wegovy_dosing_schedule", "type": "radio", "input": true, "label": "If you are on Wegovy, select your dosing schedule:", "values": [{"label": "0.25 mg weekly (starting dose)", "value": "0.25mg_weekly"}, {"label": "0.5 mg weekly", "value": "0.5mg_weekly"}, {"label": "1 mg weekly", "value": "1mg_weekly"}, {"label": "1.7 mg weekly", "value": "1.7mg_weekly"}, {"label": "2.4 mg weekly (maximum dose)", "value": "2.4mg_weekly"}, {"label": "Other", "value": "other"}], "conditional": {"show": true, "when": "glp1_medication_name", "eq": "semaglutide", "and": [{"show": true, "when": "semaglutide_method", "eq": "injectable"}]}, "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "wegovy_dosing_schedule_other", "type": "textfield", "input": true, "label": "Please specify your Wegovy dose if 'Other':", "conditional": {"show": true, "when": "wegovy_dosing_schedule", "eq": "other"}, "validate": {"required": true, "customMessage": "Please specify your Wegovy dose."}}, {"key": "saxenda_dosing_schedule", "type": "radio", "input": true, "label": "If you are on Saxenda, select your dosing schedule:", "values": [{"label": "0.6 mg daily (starting dose)", "value": "0.6mg_daily"}, {"label": "1.2 mg daily", "value": "1.2mg_daily"}, {"label": "1.8 mg daily", "value": "1.8mg_daily"}, {"label": "2.4 mg daily", "value": "2.4mg_daily"}, {"label": "3.0 mg daily (maximum dose)", "value": "3.0mg_daily"}, {"label": "Other", "value": "other"}], "conditional": {"show": true, "when": "glp1_medication_name", "eq": "liraglutide", "and": [{"show": true, "when": "semaglutide_method", "eq": "injectable"}]}, "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "saxenda_dosing_schedule_other", "type": "textfield", "input": true, "label": "Please specify your Saxenda dose if 'Other':", "conditional": {"show": true, "when": "saxenda_dosing_schedule", "eq": "other"}, "validate": {"required": true, "customMessage": "Please specify your Saxenda dose."}}, {"key": "dulaglutide_dosing_schedule", "type": "radio", "input": true, "label": "If you are on Dulaglutide (Trulicity), select your dosing schedule:", "values": [{"label": "0.75 mg weekly (starting dose)", "value": "0.75mg_weekly"}, {"label": "1.5 mg weekly", "value": "1.5mg_weekly"}, {"label": "3.0 mg weekly", "value": "3.0mg_weekly"}, {"label": "4.5 mg weekly (maximum dose)", "value": "4.5mg_weekly"}, {"label": "Other", "value": "other"}], "conditional": {"show": true, "when": "glp1_medication_name", "eq": "dulaglutide"}, "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "dulaglutide_dosing_schedule_other", "type": "textfield", "input": true, "label": "Please specify your Dulaglutide (Trulicity) dose if 'Other':", "conditional": {"show": true, "when": "dulaglutide_dosing_schedule", "eq": "other"}, "validate": {"required": true, "customMessage": "Please specify your Dulaglutide dose."}}, {"key": "obesity_causing_medication", "type": "selectboxes", "input": true, "label": "Are you currently taking any of the following medication that may contribute to weight gain?", "values": [{"label": "Corticosteroids (e.g., prednisone)", "value": "corticosteroids", "shortcut": ""}, {"label": "Antidepressants (e.g., SSRIs such as fluoxetine, sertraline)", "value": "antidepressants", "shortcut": ""}, {"label": "Antipsychotic medications (e.g., olanzapine, risperidone)", "value": "antipsychotics", "shortcut": ""}, {"label": "Diabetes medications (e.g., insulin)", "value": "diabetic_meds", "shortcut": ""}], "tableView": true, "optionsLabelPosition": "right"}, {"key": "none_of_the_above_obesity_medication", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a medication."}, "validate": {"custom": "valid = !!data.none_of_the_above_obesity_medication || _.some(_.values(data.obesity_causing_medication));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "obesity_medication_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT take the following medications that may contribute to weight gain:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "I DO NOT take the following medications that may contribute to weight gain:", "calculateValue": {"custom": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.obesity_causing_medication, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}}, {"key": "lab_testing_header", "type": "content", "html": "<h2>Lab Testing</h2><p>Please let us know when you last had the following lab tests. This information is important for understanding your current health status.</p>", "input": false, "tableView": false}, {"key": "last_cbc_test", "type": "select", "input": true, "label": "When was your last <strong>CBC (Complete Blood Count)</strong> test?", "data": {"values": [{"label": "I don't know", "value": "doesn't_know"}, {"label": "I've never had one", "value": "never_had"}, {"label": "<1 week ago", "value": "less_1_week"}, {"label": "2-4 weeks ago", "value": "2-4_weeks_ago"}, {"label": "4 weeks - 6 weeks ago", "value": "4-6_weeks"}, {"label": "6 weeks - 3 months ago", "value": "6_weeks-3_months"}, {"label": "3-6 months", "value": "3-6_months"}, {"label": "6-12 months", "value": "6-12_months"}, {"label": "12-24 months", "value": "12-24_months"}, {"label": "24+ months", "value": "24+_months"}]}, "widget": "html5", "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "last_cholesterol_test", "type": "select", "input": true, "label": "When was your last <strong>cholesterol profile</strong> test?", "data": {"values": [{"label": "Never had one", "value": "never_had"}, {"label": "<1 week ago", "value": "less_1_week"}, {"label": "2-4 weeks ago", "value": "2-4_weeks_ago"}, {"label": "4 weeks - 6 weeks ago", "value": "4-6_weeks"}, {"label": "6 weeks - 3 months ago", "value": "6_weeks-3_months"}, {"label": "3-6 months", "value": "3-6_months"}, {"label": "6-12 months", "value": "6-12_months"}, {"label": "12-24 months", "value": "12-24_months"}, {"label": "24+ months", "value": "24+_months"}]}, "widget": "html5", "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "ldl_cholesterol_levels_mmol_adjusted", "type": "select", "input": true, "label": "Please select the range that includes your most recent LDL cholesterol level (in mmol/L and equivalent mg/dL):", "data": {"values": [{"label": "I don't know/Not sure", "value": "dont_know"}, {"label": "< 2 mmol/L (< 77 mg/dL)", "value": "less_than_2"}, {"label": "2 to 2.4 mmol/L (77 to 92 mg/dL)", "value": "2_to_2.4"}, {"label": "2.5 to 2.9 mmol/L (97 to 112 mg/dL)", "value": "2.5_to_2.9"}, {"label": "3 to 3.4 mmol/L (116 to 131 mg/dL)", "value": "3_to_3.4"}, {"label": "3.5 to 3.9 mmol/L (135 to 150 mg/dL)", "value": "3.5_to_3.9"}, {"label": "4 to 4.4 mmol/L (154 to 170 mg/dL)", "value": "4_to_4.4"}, {"label": "4.5 to 4.9 mmol/L (174 to 189 mg/dL)", "value": "4.5_to_4.9"}, {"label": "5 to 7.4 mmol/L (193 to 286 mg/dL)", "value": "5_to_7.4"}, {"label": "7.5 to 9.9 mmol/L (290 to 383 mg/dL)", "value": "7.5_to_9.9"}, {"label": "10+ mmol/L (> 386 mg/dL)", "value": "10_plus"}]}, "widget": "html5", "customConditional": "show = data.last_cholesterol_test != 'never_had';", "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "last_diabetic_test", "type": "select", "input": true, "label": "When was your last <strong>diabetic testing</strong> (e.g., HbA1c)?", "data": {"values": [{"label": "Never had one", "value": "never_had"}, {"label": "<1 week ago", "value": "less_1_week"}, {"label": "2-4 weeks ago", "value": "2-4_weeks_ago"}, {"label": "4 weeks - 6 weeks ago", "value": "4-6_weeks"}, {"label": "6 weeks - 3 months ago", "value": "6_weeks-3_months"}, {"label": "3-6 months", "value": "3-6_months"}, {"label": "6-12 months", "value": "6-12_months"}, {"label": "12-24 months", "value": "12-24_months"}, {"label": "24+ months", "value": "24+_months"}]}, "widget": "html5", "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "last_hba1c_level", "type": "select", "input": true, "label": "Please select the range that includes your most recent HbA1c level:", "data": {"values": [{"label": "I don't know/Not sure", "value": "dont_know"}, {"label": "Less than 5.2%", "value": "less_than_5.2"}, {"label": "5.3% to 5.5%", "value": "5.3_to_5.5"}, {"label": "5.6% to 6%", "value": "5.6_to_6"}, {"label": "6% to 6.5%", "value": "6_to_6.5"}, {"label": "6.5% to 7%", "value": "6.5_to_7"}, {"label": "7% to 7.5%", "value": "7_to_7.5"}, {"label": "7.5% to 8%", "value": "7.5_to_8"}, {"label": "8% to 8.5%", "value": "8_to_8.5"}, {"label": "8.5% to 9%", "value": "8.5_to_9"}, {"label": "9% to 9.5%", "value": "9_to_9.5"}, {"label": "9.5% to 10%", "value": "9.5_to_10"}, {"label": "10% and above", "value": "10_and_above"}]}, "widget": "html5", "customConditional": "show = data.last_diabetic_test != 'never_had';", "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}]}