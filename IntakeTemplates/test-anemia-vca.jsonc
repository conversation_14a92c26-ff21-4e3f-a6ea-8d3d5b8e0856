{"components": [{"key": "hx_anemia", "type": "radio", "input": true, "label": "Have you been diagnosed with anemia in the past?", "inline": false, "values": [{"label": "No", "value": false, "shortcut": ""}, {"label": "Yes/Don't Know", "value": true, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "anemia_in_person", "type": "selectboxes", "input": true, "label": "Are you experiencing any of the following symptoms:", "values": [{"label": "Chest pain or pressure", "value": "chest_pain", "shortcut": ""}, {"label": "Feel lightheaded or faint", "value": "presy<PERSON><PERSON>", "shortcut": ""}, {"label": "Palpitations (i.e. rapid, slow or irregular heart beat)", "value": "palpitations", "shortcut": ""}, {"label": "Shortness of breath or difficulty breathing", "value": "dyspnea", "shortcut": ""}, {"label": "Feel unwell", "value": "malaise", "shortcut": ""}, {"label": "Rectal bleeding (i.e. blood in stool)", "value": "rectal_bleeding", "shortcut": ""}], "inputType": "checkbox", "tableView": true, "customConditional": "show = data.hx_anemia == true;", "optionsLabelPosition": "right"}, {"key": "asymptomatic", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = _.some(_.values(data.anemia_in_person)) || data.asymptomatic;"}, "tableView": false, "customConditional": "show = data.hx_anemia == true;", "customClass": "mt-n3", "defaultValue": false}, {"key": "menstrual_cycle_changes", "type": "radio", "input": true, "label": "Have you experienced a change in your menstrual cycles recently (i.e heavier flow, longer flow, or irregular cycles)?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = !_.some(_.values(data.anemia_in_person)) && (data.hx_anemia == true);", "optionsLabelPosition": "right"}, {"key": "no_indications", "type": "textfield", "input": true, "label": "No Indication:", "hidden": true, "disabled": true, "multiple": false, "tableView": true, "clearOnHide": false, "defaultValue": true, "calculateValue": "value = !data.hx_anemia;", "refreshOnChange": true}, {"key": "contraindication_keys", "type": "textfield", "input": true, "label": "Contraindication keys:", "hidden": true, "disabled": true, "multiple": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "value = _.concat((_.some(_.values(data.anemia_in_person)) ? ['anemia_in_person'] : []), (data.menstrual_cycle_changes ? ['menstrual_cycle_changes'] : []));", "refreshOnChange": true}]}