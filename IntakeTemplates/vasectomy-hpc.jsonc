{"components": [{"key": "vasectomy_intro", "type": "content", "input": false, "html": "<h2>Vasectomy Referral Request</h2><p>Please complete the following questionnaire to help us process your referral request for a vasectomy. A vasectomy is a permanent form of birth control.</p>", "tableView": false}, {"key": "vasectomy_confirmation", "type": "radio", "input": true, "confirm_label": "You are requesting a referral for a vasectomy:", "label": "Are you requesting a referral for a vasectomy at this time?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true}, {"key": "vasectomy_reason", "type": "selectboxes", "input": true, "label": "What are your main reasons for requesting a vasectomy? (Select all that apply)", "values": [{"label": "We have completed our family", "value": "family_complete"}, {"label": "I do not want children in the future", "value": "no_children"}, {"label": "My partner cannot tolerate hormonal birth control", "value": "partner_cannot_tolerate_hormones"}, {"label": "My partner cannot have children for medical reasons", "value": "partner_medical_infertility"}, {"label": "I want a reliable long-term birth control method", "value": "reliable_long_term_method"}, {"label": "We want to avoid unintended pregnancy permanently", "value": "avoid_unintended_pregnancy"}, {"label": "Financial or personal reasons for avoiding future children", "value": "financial_or_personal_reason"}, {"label": "Other", "value": "other"}], "customConditional": "show = data.vasectomy_confirmation === 'yes';", "confirm_label": "Your reason(s) for vasectomy:", "tableView": true}, {"key": "partner_infertility_doctor_confirmed", "type": "radio", "input": true, "label": "Has a doctor confirmed that your partner shouldn't have children for medical reasons?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "customConditional": "show = data.vasectomy_reason?.partner_medical_infertility;", "confirm_label": "Doctor has confirmed partner cannot have children:", "validate": {"required": true}, "tableView": true}, {"key": "vasectomy_reason_other", "type": "textfield", "input": true, "label": "Please explain your other reason:", "confirm_label": "Other reason for vasectomy:", "customConditional": "show = data.vasectomy_reason?.other;", "tableView": true}, {"key": "vasectomy_children", "type": "radio", "input": true, "label": "Do you currently have any children?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "customConditional": "show = data.vasectomy_confirmation === 'yes';", "confirm_label": "You currently have children:", "validate": {"required": true}, "tableView": true}, {"key": "number_of_children", "type": "radio", "input": true, "label": "How many children do you currently have?", "values": [{"label": "1", "value": "1"}, {"label": "2", "value": "2"}, {"label": "3", "value": "3"}, {"label": "4", "value": "4"}, {"label": "5 or more", "value": "5_plus"}], "customConditional": "show = data.vasectomy_children === 'yes';", "confirm_label": "Number of children you have:", "validate": {"required": true}, "tableView": true}, {"key": "vasectomy_discussed_with_partner", "type": "radio", "input": true, "label": "Have you discussed this decision with your current partner (if applicable)?", "confirm_label": "You've discussed this decision with your partner:", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not applicable", "value": "n/a"}], "customConditional": "show = data.vasectomy_confirmation === 'yes';", "validate": {"required": true}, "tableView": true}, {"key": "vasectomy_understand_permanent", "confirm_label": "You understand that vasectomy is permanent:", "type": "radio", "input": true, "label": "Do you understand that vasectomy is intended as a permanent method of birth control?", "values": [{"label": "Yes, I understand", "value": "yes"}, {"label": "No, I would like to learn more", "value": "no"}], "customConditional": "show = data.vasectomy_confirmation === 'yes';", "validate": {"required": true}, "tableView": true}, {"key": "vasectomy_provider_selection", "type": "select", "input": true, "label": "Please select your preferred vasectomy provider:", "confirm_label": "Preferred vasectomy provider:", "widget": "html5", "data": {"values": [{"label": "Ancaster - <PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> - Suite 105, 323 Wilson Street East", "value": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> | Suite 105, 323 Wilson Street East, Ancaster"}, {"label": "Brantford - <PERSON><PERSON> <PERSON> - 353 Saint Paul Avenue", "value": "<PERSON>. <PERSON> | 353 Saint Paul Avenue, Brantford"}, {"label": "Brantford - <PERSON><PERSON> <PERSON> - 99 Wayne <PERSON>z<PERSON> Parkway", "value": "<PERSON>. <PERSON> | 99 Wayne Gretzky Parkway, Brantford"}, {"label": "Brantford - Gentle Procedures Clinic - 195 Henry Street, Unit 05", "value": "Gentle Procedures Clinic | 195 Henry Street, Unit 05, Brantford"}, {"label": "Burlington - Gentle Procedures Clinic - 672 Brant Street, Suite 200", "value": "Gentle Procedures Clinic | 672 Brant Street, Suite 200, Burlington"}, {"label": "Cambridge - Dr. <PERSON> - 350 Conestoga Blvd", "value": "<PERSON>. <PERSON> | 350 Conestoga Blvd, Cambridge"}, {"label": "Hamilton - Gentle Procedures Clinic - 77 Hunter Street East", "value": "Gentle Procedures Clinic | 77 Hunter Street East, Hamilton"}, {"label": "Hawkesbury - Dr. <PERSON> - 361 Main Street East", "value": "<PERSON>. <PERSON> | 361 Main Street East, Hawkesbury"}, {"label": "Kitchener - Dr. <PERSON> - 51 Benton Street", "value": "<PERSON>. <PERSON> | 51 Benton Street, Kitchener"}, {"label": "Kitchener - Dr. <PERSON><PERSON><PERSON> - 535 Park Street, Unit 4", "value": "Dr. <PERSON><PERSON><PERSON> | 535 Park Street, Unit 4, <PERSON><PERSON>"}, {"label": "Kitchener - Gentle Procedures Clinic - 51 Benton Street", "value": "Gentle Procedures Clinic | 51 Benton Street, Kitchener"}, {"label": "London - <PERSON><PERSON> <PERSON><PERSON> - 130 Thompson Road", "value": "<PERSON><PERSON> <PERSON><PERSON> | 130 Thompson Road, London"}, {"label": "London - <PERSON><PERSON> <PERSON> - 11 Base Line Road East, Unit 1", "value": "<PERSON>. <PERSON> | 11 Base Line Road East, Unit 1, London"}, {"label": "London - Gentle Procedures Clinic - 272 Oxford Street West", "value": "Gentle Procedures Clinic | 272 Oxford Street West, London"}, {"label": "Mississauga - <PERSON><PERSON> <PERSON> - 77 Queensway West, Suite 310", "value": "<PERSON>. <PERSON> | 77 Queensway West, Suite 310, Mississauga"}, {"label": "Newmarket - Dr. <PERSON> - 16700 Bayview Avenue, Suite 218", "value": "Dr. <PERSON> | 16700 Bayview Avenue, Suite 218, Newmarket"}, {"label": "Oakville - Dr. <PERSON> - 1235 Trafalgar Road, Suite 407", "value": "<PERSON>. <PERSON> | 1235 Trafalgar Road, Suite 407, Oakville"}, {"label": "Oakville - Dr. <PERSON> - 1235 Trafalgar Road, Suite 412", "value": "<PERSON>. <PERSON> | 1235 Trafalgar Road, Suite 412, Oakville"}, {"label": "Ottawa - Dr. <PERSON><PERSON><PERSON> - 29 Clemow Avenue", "value": "Dr. <PERSON><PERSON><PERSON> | 29 Clemow Avenue, Ottawa"}, {"label": "Richmond Hill - Dr. <PERSON> - 13291 Yonge Street, Suite 404", "value": "<PERSON>. <PERSON> | 13291 Yonge Street, Suite 404, Richmond Hill"}, {"label": "Scarborough - Dr<PERSON> <PERSON><PERSON><PERSON> - 520 Ellesmere Road, Suite 603", "value": "Dr. <PERSON><PERSON><PERSON> | 520 Ellesmere Road, Suite 603, Scarborough"}, {"label": "St. Catharines - Gentle Procedures Clinic - 261 Martindale Road, Unit 6", "value": "Gentle Procedures Clinic | 261 Martindale Road, Unit 6, St. Catharines"}, {"label": "Sudbury - <PERSON><PERSON> - 1760 Regent Street", "value": "<PERSON><PERSON> <PERSON> | 1760 Regent Street, Sudbury"}, {"label": "Toronto - Central East Urology - Suite 402, 2863 Ellesmere Road", "value": "Central East Urology | Suite 402, 2863 Ellesmere Road, Toronto"}, {"label": "Toronto - Canadian Men's Clinic - 1030 Sheppard Avenue West, Unit 5", "value": "Canadian Men's Clinic | 1030 Sheppard Avenue West, Unit 5, Toronto"}, {"label": "Toronto - Toronto West Urology Associates - 2425 Bloor Street West, Suite 501", "value": "Toronto West Urology Associates | 2425 Bloor Street West, Suite 501, Toronto"}, {"label": "Other provider (not listed)", "value": "other"}]}, "validate": {"required": true}, "tableView": true}, {"key": "other_provider_details", "type": "textfield", "input": true, "label": "Please enter the name and address of the provider:", "confirm_label": "Name and address of the other provider:", "customConditional": "show = data.vasectomy_provider_selection === 'other';", "validate": {"required": true}, "tableView": true}, {"key": "stated_other_questions", "type": "textarea", "input": true, "label": "Please use this area to ask any questions that you might have for your health care provider:", "adminFlag": true, "tableView": true, "autoExpand": false, "customConditional": "show = data.any_other_questions === true;"}, {"key": "intake_denial_reasons", "type": "textfield", "input": true, "label": "Intake Denial Reasons:", "hidden": true, "disabled": true, "multiple": true, "adminFlag": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "value = [];", "refreshOnChange": true}, {"key": "intake_kind", "type": "textfield", "input": true, "label": "Intake Kind", "hidden": true, "disabled": true, "tableView": true, "clearOnHide": false, "calculateValue": "value = !data.intake_denial_reasons?.length ? 'mail' : 'chat';"}, {"key": "intake_mt_key", "type": "textfield", "input": true, "label": "Intake Mail Template Key", "hidden": true, "disabled": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.intake_kind=='mail'?'intake-vasectomy':'intake-denial'"}, {"key": "allowed_rt_keys", "type": "textfield", "input": true, "label": "Allowed Rep<PERSON>", "hidden": true, "disabled": true, "multiple": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.intake_mt_key=='intake-denial'?[]:['appointment-intake','edit-intake']"}]}