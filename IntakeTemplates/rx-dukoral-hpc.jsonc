{"components": [{"key": "content", "html": "<h2>Instructions</h2><p>Please complete the following questionnaire about your interest in the  DUKORAL® Vaccine.</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "header_vaccine_records", "html": "<h4>General Questions&nbsp;</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "vaccine_records", "type": "radio", "input": true, "label": "Do you have access to or can you obtain access to your vaccine records?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "vaccine_indication", "type": "radio", "input": true, "label": "What is your reason for getting the DUKORAL® vaccine?", "inline": false, "values": [{"label": "Protect against <strong>Cholera</strong>", "value": "protect_cholera", "shortcut": ""}, {"label": "Protect against <strong>Traveller's Diarrhea</strong> caused by Enterotoxigenic Escherichia coli (ETEC)", "value": "protect_etec", "shortcut": ""}, {"label": "Protect against <strong>Cholera & Travellers Diarrhea</strong>", "value": "both", "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "vaccine_indication_other", "type": "textarea", "input": true, "label": "You selected 'other' - please specify your reason for Dukoral vaccination:", "tableView": true, "autoExpand": false, "customConditional": "show = data.vaccine_indication == 'other';"}, {"key": "shingles_header", "html": "<h4>DUKORAL® Vaccination&nbsp;</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "dukoral_vaccinated", "type": "radio", "input": true, "label": "Have you had DUKORAL® vaccination before?", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "I don't know", "value": "doesnt_know", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "review_record_prompt", "html": "Please review your vaccine records before continuing and confirm your vaccination history.", "type": "content", "input": false, "label": "Content", "tableView": true, "customConditional": "show = data.dukoral_vaccinated == 'doesnt_know' && data.vaccine_records == 'true';", "refreshOnChange": false}, {"key": "number_of_dukoral_vaccines", "type": "radio", "input": true, "label": "Do you know how many doses of the dukoral vaccine you previously recieved?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No/I Don't Know", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.dukoral_vaccinated == 'yes';", "optionsLabelPosition": "right"}, {"key": "quantity_dukoral_doses", "type": "radio", "input": true, "label": "How many doses of the DUKORAL® Vaccine have you recieved?", "inline": false, "values": [{"label": "1 dose", "value": "1_dose", "shortcut": ""}, {"label": "2 doses ", "value": "2_doses", "shortcut": ""}, {"label": "3 doses ", "value": "3_doses", "shortcut": ""}, {"label": "4 or more doses ", "value": "4+_doses", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.number_of_dukoral_vaccines == true;", "optionsLabelPosition": "right"}, {"key": "current_vaccination_contraindications", "html": "<h3>Other Considerations&nbsp;</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "vaccine_contraindication", "type": "radio", "input": true, "label": "Do you have a fever, diarrheal illness or are recovering from a recent illness and feel unwell?", "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "pregnant_breast_feeding", "type": "radio", "input": true, "label": "Are you currently pregnant or?", "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "content2", "html": "<h2>Questions for the Doctor</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "additional_questions", "type": "textarea", "input": true, "label": "Please use this area to ask any questions that you might have for your health care provider:", "tableView": true, "autoExpand": false}]}