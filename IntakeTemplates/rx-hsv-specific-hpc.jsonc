{"components": [{"key": "heading_herpes_treatment", "html": "<h1><center><strong><PERSON><PERSON> Treatment</strong></h1><center><p>To expedite your care, please answer the following questions to the best of your abilities. &nbsp;We ask these questions to provide the best possible care.&nbsp;</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "heading_medical_indication", "html": "<h4>Indication for Treatment&nbsp;</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "treatment_indication", "type": "selectboxes", "input": true, "label": "Please clarify why you are interested in herpes treatment:", "values": [{"label": "I have cold sores", "value": "cold_sores_treatment", "shortcut": ""}, {"label": "I have genital herpes", "value": "genital_herpes_treatment", "shortcut": ""}, {"label": "I have a rash that I suspect is herpes", "value": "suspects_herpes", "shortcut": ""}, {"label": "Other (please specify below)", "value": "other_treatment_indication", "shortcut": ""}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "confirm_label": "Reason(s) for treatment:", "optionsLabelPosition": "right"}, {"key": "other_treatment_indication", "type": "textarea", "input": true, "label": "Please state your “other” reason(s) for HSV testing:", "tableView": true, "autoExpand": false, "confirm_label": "“Other” reason(s) for HSV treatment:", "customConditional": "show = data.treatment_indication.other;"}, {"key": "cold_sores_differentiation_heading", "html": "<h5><strong>About Cold Sores</strong></h5>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.treatment_indication && data.treatment_indication.cold_sores_treatment;"}, {"key": "sores_present", "type": "radio", "input": true, "label": "Do you get sores in or around the mouth, lips, or nose?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "I'm not sure", "value": "not_sure"}], "tooltip": "Sores can include open, painful spots or wounds that may look round or have a yellow or white center with a red edge. These might show up on your skin or inside your mouth and can make it uncomfortable to eat or talk.", "validate": {"required": true}, "tableView": true, "confirm_label": "Previous oral sores:", "customConditional": "show = data.treatment_indication && data.treatment_indication.cold_sores_treatment;"}, {"key": "skin_change_locations", "type": "selectboxes", "input": true, "label": "Where do you notice sores or skin changes (select all that apply):", "values": [{"label": "Outer lips (e.g., border of the lips)", "value": "outer_lips"}, {"label": "Inner lips (inside the lip area)", "value": "inner_lips"}, {"label": "Corners of the mouth", "value": "mouth_corners"}, {"label": "Inside the cheeks", "value": "inside_cheeks"}, {"label": "Roof of the mouth", "value": "roof_of_mouth"}, {"label": "Gums (around or near teeth)", "value": "gums"}, {"label": "Tongue (top or underside)", "value": "tongue"}, {"label": "Inside the nose", "value": "nose_inside"}, {"label": "Tip of the nose", "value": "nose_outside"}, {"label": "Chin (below the lips)", "value": "chin"}, {"label": "I haven't noticed any sores or skin changes", "value": "no_changes"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.sores_present === 'yes';"}, {"key": "sensations_before", "type": "radio", "input": true, "label": "Before the sore appears, do you notice any sensations like itching, tingling, or burning?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "I'm not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Sensations before sores:", "customConditional": "show = !!data.skin_change_locations;"}, {"key": "sores_appearance", "type": "selectboxes", "input": true, "label": "What do your sores usually look like? (You can select more than one option):", "values": [{"label": "Small fluid-filled blisters that may burst and scab over", "value": "blisters_scab"}, {"label": "Painful spots with a white or yellow center and red edges", "value": "white_yellow_center"}, {"label": "Flat, red patches that may or may not hurt", "value": "flat_red_patches"}, {"label": "Itchy bumps without fluid", "value": "itchy_bumps"}, {"label": "Cracks or splits in the skin that sting or hurt", "value": "cracks_splits"}, {"label": "None of these descriptions fit", "value": "none"}, {"label": "I'm not sure", "value": "not_sure"}, {"label": "Other", "value": "other"}], "validate": {"required": true}, "tableView": true, "confirm_label": "<PERSON>res appearance:", "customConditional": "show = !!data.sensations_before;"}, {"key": "sores_appearance_other", "type": "textfield", "input": true, "label": "Please describe what your sores look like:", "validate": {"required": false}, "tableView": true, "placeholder": "Describe your sores", "customConditional": "show = data.sores_appearance && data.sores_appearance.other;"}, {"key": "cold_sores_physician_confirmation", "type": "radio", "input": true, "label": "Has a physician ever confirmed your diagnosis as a cold sore?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "I'm not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Physician confirmed cold sore diagnosis:", "customConditional": "show = !!data.sores_appearance;"}, {"key": "cold_sores_age_diagnosis", "type": "textfield", "input": true, "label": "How old were you when you first noticed these sores? (If you're unsure, feel free to estimate.)", "validate": {"custom": "valid = input >= 0;", "required": true}, "tableView": true, "placeholder": "Enter your age or best estimate", "customConditional": "show = !!data.cold_sores_physician_confirmation;"}, {"key": "cold_sores_past_medication", "type": "radio", "input": true, "label": "Have you taken medication in the past to treat cold sores?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = !!data.cold_sores_age_diagnosis;"}, {"key": "cold_sores_outbreak_frequency", "data": {"values": [{"label": "I haven't had one in many years", "value": "remote_past"}, {"label": "I get them once every few years", "value": "once_every_few_years"}, {"label": "1 outbreak per year", "value": "1_per_year"}, {"label": "2-3 outbreaks per year", "value": "2-3_per_year"}, {"label": "More than 3 outbreaks per year", "value": "more_than_3_per_year"}]}, "type": "select", "input": true, "label": "How many cold sore outbreaks do you typically experience in a year?", "widget": "html5", "validate": {"required": true}, "tableView": true, "placeholder": "Select the frequency", "customConditional": "show = !!data.cold_sores_past_medication;"}, {"key": "cold_sores_swab_confirmation", "type": "radio", "input": true, "label": "Have you ever had a swab taken of the cold sore to confirm the diagnosis?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Previous Oral HSV Swab Completed:", "customConditional": "show = !!data.cold_sores_outbreak_frequency;"}, {"key": "cold_sores_serotype_confirmation", "type": "radio", "input": true, "label": "Was the herpes virus type (serotype) identified through the test?", "values": [{"label": "Confirmed HSV-1", "value": "hsv1"}, {"label": "Confirmed HSV-2", "value": "hsv2"}, {"label": "The test did not confirm the serotype", "value": "negative"}, {"label": "I don't know", "value": "unknown"}], "tooltip": "Serotype testing can determine if the herpes simplex virus is HSV-1 or HSV-2.", "validate": {"required": true}, "tableView": true, "confirm_label": "Oral Swab Result:", "customConditional": "show = data.cold_sores_swab_confirmation === 'yes';"}, {"key": "does_rash_feel_typical", "type": "radio", "input": true, "label": "Does your current rash feel like a typical outbreak for you?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "This is my first outbreak", "value": "first_outbreak"}, {"label": "I'm not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "<PERSON><PERSON> feels typical:", "customConditional": "show = data.treatment_indication && data.treatment_indication.cold_sores_treatment;"}, {"key": "symptoms_start_timeframe", "data": {"values": [{"label": "Less than 12 hours ago", "value": "<12_hours"}, {"label": "12-24 hours ago", "value": "12-24_hours"}, {"label": "1-3 days ago", "value": "1-3_days"}, {"label": "4-7 days ago", "value": "4-7_days"}, {"label": "1-2 weeks ago", "value": "1-2_weeks"}, {"label": "2-4 weeks ago", "value": "2-4_weeks"}, {"label": "More than 1 month ago", "value": ">1_month"}, {"label": "I'm not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "How long ago did your symptoms start?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Timeframe of symptom onset:", "customConditional": "show = data.treatment_indication && data.treatment_indication.cold_sores_treatment;"}, {"key": "photo_upload_header", "html": "<h2>Photo Upload</h2>", "type": "content", "input": false, "label": "Content", "customConditional": "show = data.treatment_indication && data.treatment_indication.cold_sores_treatment;"}, {"key": "uploadUrl", "url": "/app/q/{qpk}/formio-files/{pk}/", "type": "file", "image": true, "input": true, "label": "Please share a photo of your current rash or sores:", "webcam": true, "capture": "user", "storage": "url", "multiple": true, "fileTypes": [{"label": "", "value": ""}], "tableView": true, "customConditional": "show = data.treatment_indication && data.treatment_indication.cold_sores_treatment;", "validateWhenHidden": false}, {"key": "genital_herpes_differentiation_heading", "html": "<h5><strong>About Genital Herpes</strong></h5>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.treatment_indication && data.treatment_indication.genital_herpes_treatment;"}, {"key": "genital_sores_present", "type": "radio", "input": true, "label": "Do you get sores, bumps, or rashes in your genital or anal area?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "I'm not sure", "value": "not_sure"}], "tooltip": "These can include painful spots, red bumps, or rashes near your genital or anal area. Select 'Yes' if you've noticed any of these symptoms.", "validate": {"required": true}, "tableView": true, "confirm_label": "Previous genital sores:", "customConditional": "show = data.treatment_indication && data.treatment_indication.genital_herpes_treatment;"}, {"key": "genital_sores_sensations", "type": "radio", "input": true, "label": "Before the sores or bumps appear, do you feel sensations like itching, tingling, or burning?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "I'm not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Sensations before genital sores:", "customConditional": "show = data.genital_sores_present === 'yes';"}, {"key": "genital_sores_locations", "type": "selectboxes", "input": true, "label": "Where do you notice sores, blisters, or skin changes (select all that apply):", "values": [{"label": "Vagina (including outer and inner areas)", "value": "vagina"}, {"label": "Around the vaginal opening", "value": "vaginal_opening"}, {"label": "On or around the anus", "value": "anus"}, {"label": "Inner thighs (near the genital area)", "value": "inner_thighs"}, {"label": "On the penis (any part)", "value": "penis"}, {"label": "On or around the scrotum", "value": "scrotum"}, {"label": "Between the genitals and anus", "value": "perineum"}, {"label": "Nowhere, I haven't noticed any sores or skin changes", "value": "no_changes"}, {"label": "Other", "value": "other"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Genital sore location:", "customConditional": "show = !!data.genital_sores_sensations;"}, {"key": "genital_sores_appearance", "type": "selectboxes", "input": true, "label": "What do your sores, bumps, or rashes usually look like? (You can select more than one):", "values": [{"label": "Fluid-filled blisters that burst and scab over", "value": "blisters_scab"}, {"label": "Small red or white bumps, possibly with hair in the center", "value": "folliculitis"}, {"label": "Flat, itchy patches that are red or irritated", "value": "contact_dermatitis"}, {"label": "Painful open sores that take time to heal", "value": "painful_sores"}, {"label": "None of these descriptions fit", "value": "none"}, {"label": "I'm not sure", "value": "not_sure"}, {"label": "Other", "value": "other"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Genital sores appearance:", "customConditional": "show = !!data.genital_sores_locations;"}, {"key": "genital_sores_appearance_other", "type": "textfield", "input": true, "label": "Please describe your sores, bumps, or rashes if none of the above fits:", "validate": {"required": false}, "tableView": true, "placeholder": "Describe your symptoms", "customConditional": "show = data.genital_sores_appearance && data.genital_sores_appearance.other;"}, {"key": "genital_sores_duration", "type": "radio", "input": true, "label": "How long do these sores or bumps typically last?", "values": [{"label": "A few days to a week", "value": "short_duration"}, {"label": "1-2 weeks", "value": "medium_duration"}, {"label": "Longer than 2 weeks", "value": "long_duration"}, {"label": "I'm not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = !!data.genital_sores_locations && !!data.genital_sores_appearance;"}, {"key": "genital_sores_triggers", "type": "radio", "input": true, "label": "Have you noticed any triggers for your sores or bumps?", "values": [{"label": "Yes, they occur after shaving, waxing, or hair removal", "value": "shaving_triggers"}, {"label": "Yes, they occur after using new products (e.g., soap, creams, or detergents)", "value": "product_triggers"}, {"label": "Yes, but I'm not sure what the trigger is", "value": "unknown_trigger"}, {"label": "No, I haven't noticed any triggers", "value": "no_triggers"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Genital sore location:", "customConditional": "show = !!data.genital_sores_duration;"}, {"key": "genital_herpes_physician_confirmation", "type": "radio", "input": true, "label": "Has a physician ever confirmed your diagnosis of genital herpes?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Physician confirmed genital herpes diagnosis:", "customConditional": "show = !!data.genital_sores_duration && !!data.genital_sores_triggers;"}, {"key": "genital_herpes_outbreak_frequency", "data": {"values": [{"label": "I haven't had one in many years", "value": "remote_past"}, {"label": "I get them once every few years", "value": "remote_past"}, {"label": "1 outbreak per year", "value": "1_per_year"}, {"label": "2-3 outbreaks per year", "value": "2-3_per_year"}, {"label": "More than 3 outbreaks per year", "value": "more_than_3_per_year"}]}, "type": "select", "input": true, "label": "How many episodes do you typically experience in a year?", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.genital_herpes_physician_confirmation === 'yes';"}, {"key": "genital_sores_swab_confirmation", "type": "radio", "input": true, "label": "Have you ever had a swab taken of the genital sores to confirm the diagnosis?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Previous HSV Swab Completed:", "customConditional": "show = !!data.genital_herpes_outbreak_frequency;"}, {"key": "does_rash_feel_typical", "type": "radio", "input": true, "label": "Does your current rash feel like a typical outbreak for you?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "This is my first outbreak", "value": "first_outbreak"}, {"label": "I'm not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "<PERSON><PERSON> feels typical:", "customConditional": "show = data.treatment_indication && data.treatment_indication.genital_herpes_treatment;"}, {"key": "symptoms_start_timeframe", "data": {"values": [{"label": "Less than 12 hours ago", "value": "<12_hours"}, {"label": "12-24 hours ago", "value": "12-24_hours"}, {"label": "1-3 days ago", "value": "1-3_days"}, {"label": "4-7 days ago", "value": "4-7_days"}, {"label": "1-2 weeks ago", "value": "1-2_weeks"}, {"label": "2-4 weeks ago", "value": "2-4_weeks"}, {"label": "More than 1 month ago", "value": ">1_month"}, {"label": "I'm not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "How long ago did your symptoms start?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Timeframe of symptom onset:", "customConditional": "show = data.treatment_indication && data.treatment_indication.genital_herpes_treatment;"}, {"key": "suspected_herpes_rash_heading", "html": "<h5><strong>About Your Suspected Herpes Rash</strong></h5>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.treatment_indication && data.treatment_indication.suspects_herpes;"}, {"key": "rash_present", "type": "radio", "input": true, "label": "Do you currently have a rash or skin changes that you suspect might be herpes?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "I'm not sure", "value": "not_sure"}], "tooltip": "A suspected herpes rash could include blisters, red patches, or irritated skin. Select 'Yes' if you've noticed any of these symptoms.", "validate": {"required": true}, "tableView": true, "confirm_label": "Current rash presence:", "customConditional": "show = data.treatment_indication && data.treatment_indication.suspects_herpes;"}, {"key": "rash_locations", "type": "selectboxes", "input": true, "label": "Where do you notice the rash or skin changes? (Select all that apply):", "values": [{"label": "Face (excluding lips and mouth)", "value": "face_excluding_mouth"}, {"label": "Lips or mouth", "value": "lips_mouth"}, {"label": "Chest or torso", "value": "chest_torso"}, {"label": "Arms or hands", "value": "arms_hands"}, {"label": "Legs or feet", "value": "legs_feet"}, {"label": "Neck", "value": "neck"}, {"label": "Back", "value": "back"}, {"label": "<PERSON><PERSON><PERSON>", "value": "scalp"}, {"label": "Genital or anal area", "value": "genital_anal_area"}, {"label": "Inner thighs", "value": "inner_thighs"}, {"label": "Other (please specify below)", "value": "other"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Rash location(s):", "customConditional": "show = data.rash_present === 'yes';"}, {"key": "rash_description", "type": "selectboxes", "input": true, "label": "What does your rash or skin changes look like? (Select all that apply):", "values": [{"label": "Fluid-filled blisters that burst and scab over", "value": "blisters_scab"}, {"label": "Itchy, red, or irritated patches", "value": "itchy_red_patches"}, {"label": "Painful open sores that take time to heal", "value": "painful_sores"}, {"label": "Flat, dry, or scaly patches", "value": "dry_scaly_patches"}, {"label": "Other (please specify below)", "value": "other"}], "validate": {"required": true}, "tableView": true, "confirm_label": "<PERSON><PERSON> appearance:", "customConditional": "show = !!data.rash_locations;"}, {"key": "rash_description_other", "type": "textfield", "input": true, "label": "Please describe your rash or skin changes if none of the above fits:", "validate": {"required": false}, "tableView": true, "placeholder": "Describe your symptoms", "customConditional": "show = data.rash_description && data.rash_description.other;"}, {"key": "rash_duration", "type": "radio", "input": true, "label": "How long has this rash lasted?", "values": [{"label": "A few days to a week", "value": "short_duration"}, {"label": "1-2 weeks", "value": "medium_duration"}, {"label": "Longer than 2 weeks", "value": "long_duration"}, {"label": "I'm not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = !!data.rash_description;"}, {"key": "rash_triggers", "type": "radio", "input": true, "label": "Have you noticed any triggers for your rash?", "values": [{"label": "Yes, it occurs after using new products (e.g., soap, creams, or detergents)", "value": "product_triggers"}, {"label": "Yes, but I'm not sure what the trigger is", "value": "unknown_trigger"}, {"label": "No, I haven't noticed any triggers", "value": "no_triggers"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Rash triggers:", "customConditional": "show = !!data.rash_duration;"}, {"key": "rash_physician_confirmation", "type": "radio", "input": true, "label": "Has a physician ever evaluated or confirmed your rash as herpes?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "I'm not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Physician evaluation of rash:", "customConditional": "show = !!data.rash_triggers;"}, {"key": "rash_age_symptom_onset", "type": "textfield", "input": true, "label": "How old were you when you first noticed this rash? (If you're unsure, feel free to estimate.)", "validate": {"custom": "valid = input >= 0;", "required": true}, "tableView": true, "placeholder": "Enter your age or best estimate", "customConditional": "show = data.rash_physician_confirmation === 'yes' || data.rash_physician_confirmation === 'no';"}, {"key": "rash_outbreak_frequency", "data": {"values": [{"label": "I haven't had one in many years", "value": "remote_past"}, {"label": "I get them once every few years", "value": "once_every_few_years"}, {"label": "None in the past year", "value": "none"}, {"label": "1 outbreak per year", "value": "1_per_year"}, {"label": "2-3 outbreaks per year", "value": "2-3_per_year"}, {"label": "More than 3 outbreaks per year", "value": "more_than_3_per_year"}]}, "type": "select", "input": true, "label": "How many episodes of this rash have you experienced in the past year?", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.rash_physician_confirmation === 'yes';"}, {"key": "rash_swab_confirmation", "type": "radio", "input": true, "label": "Have you ever had a swab taken of this rash to confirm the diagnosis?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Previous HSV Swab Completed:", "customConditional": "show = !!data.rash_outbreak_frequency;"}, {"key": "rash_serotype_confirmation", "type": "radio", "input": true, "label": "Was the herpes virus type (serotype) identified through the test?", "values": [{"label": "Yes, the test confirmed HSV-1", "value": "hsv1"}, {"label": "Yes, the test confirmed HSV-2", "value": "hsv2"}, {"label": "No, the test did not confirm the serotype", "value": "negative"}, {"label": "I don't know", "value": "unknown"}], "tooltip": "Serotype testing can determine if the herpes simplex virus is HSV-1 or HSV-2.", "validate": {"required": true}, "tableView": true, "confirm_label": "HSV Serotype Results:", "customConditional": "show = data.rash_swab_confirmation === 'yes';"}, {"key": "photo_upload_header", "html": "<h2>Photo Upload</h2>", "type": "content", "input": false, "label": "Content", "customConditional": "show = data.treatment_indication && data.treatment_indication.suspects_herpes;"}, {"key": "uploadUrl", "url": "/app/q/{qpk}/formio-files/{pk}/", "type": "file", "image": true, "input": true, "label": "Please share a photo of your current rash or sores:", "webcam": true, "capture": "user", "storage": "url", "multiple": true, "fileTypes": [{"label": "", "value": ""}], "tableView": true, "customConditional": "show = data.treatment_indication && data.treatment_indication.suspects_herpes;", "validateWhenHidden": false}, {"key": "swab_advice_heading", "html": "<h5><strong>Advisory on Swab Testing</strong></h5>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.rash_present === 'yes';"}, {"key": "swab_advice", "html": "<p>For accurate diagnosis, it is highly recommended to have a swab test performed within 72 hours of the eruption of a suspected herpes rash. Testing within this time frame increases the likelihood of detecting the virus if it is present, which can help confirm or clarify the diagnosis. Please consult a healthcare provider promptly if you develop a new rash.</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.rash_present === 'yes';"}, {"key": "swab_advice_understanding", "type": "radio", "input": true, "label": "Do you understand the importance of seeking swab testing within 72 hours of a suspected herpes rash eruption?", "values": [{"label": "I understand", "value": "understand"}, {"label": "I do not understand", "value": "do_not_understand"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Understanding of swab testing importance:", "customConditional": "show = data.rash_present === 'yes';"}, {"key": "medication_usage_heading", "html": "<h5><strong>Medication Usage</strong></h5>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.treatment_indication && (data.treatment_indication.cold_sores_treatment || data.treatment_indication.genital_herpes_treatment);"}, {"key": "past_medications_used", "type": "selectboxes", "input": true, "label": "Which of the following medications have you used in the past? (Select all that apply):", "values": [{"label": "Oral: <PERSON><PERSON><PERSON><PERSON><PERSON> (Zovirax)", "value": "oral_acyclovir"}, {"label": "Oral: <PERSON><PERSON><PERSON><PERSON><PERSON> (Valtrex)", "value": "oral_valacyclovir"}, {"label": "Oral: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Famvir)", "value": "oral_famciclovir"}, {"label": "Topical: Acyclovir cream", "value": "topical_acyclovir"}, {"label": "Topical: Over-the-counter treatments (e.g., creams, patches)", "value": "otc_treatments"}, {"label": "Natural or home remedies", "value": "natural_remedies"}, {"label": "I have not used any medications in the past", "value": "no_medications"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Past medications used:", "customConditional": "show = data.treatment_indication && (data.treatment_indication.cold_sores_treatment || data.treatment_indication.genital_herpes_treatment);"}, {"key": "medication_side_effects", "type": "selectboxes", "input": true, "label": "Have you experienced any of the following side effects from past anti-viral medication? (Select all that apply):", "values": [{"label": "Nausea or upset stomach", "value": "nausea"}, {"label": "Headache", "value": "headache"}, {"label": "Fatigue", "value": "fatigue"}, {"label": "Skin irritation (from topical treatments)", "value": "skin_irritation"}, {"label": "Allergic reaction", "value": "allergic_reaction"}, {"label": "Other (please specify below)", "value": "other"}, {"label": "No side effects experienced", "value": "no_side_effects"}, {"label": "I've never used these medications", "value": "never_used_them"}], "tableView": true, "confirm_label": "Medication side effects:", "customConditional": "show = data.past_medications_used && Object.values(data.past_medications_used).some(value => value);"}, {"key": "heading_additional_questions", "html": "<h4>Additional Questions&nbsp;</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"intake_template_key": "hx-any-questions"}, {"key": "intake_denial_reasons", "type": "textfield", "input": true, "label": "Intake Denial Reasons:", "hidden": true, "disabled": true, "multiple": true, "adminFlag": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "value=_.concat((data.any_other_questions === true && !!data.stated_other_questions && !/^\\s*$/.test(data.stated_other_questions)?['stated_other_questions.not_blank']:[]));", "refreshOnChange": true}, {"key": "intake_kind", "type": "textfield", "input": true, "label": "Intake Kind", "hidden": true, "disabled": true, "tableView": true, "clearOnHide": false, "calculateValue": "value = !data.intake_denial_reasons?.length ? 'mail' : 'chat';"}, {"key": "intake_mt_key", "type": "textfield", "input": true, "label": "Intake Mail Template Key", "hidden": true, "disabled": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.intake_kind=='mail'?'intake-hsv-rx':'intake-denial'"}, {"key": "allowed_rt_keys", "type": "textfield", "input": true, "label": "Allowed Rep<PERSON>", "hidden": true, "disabled": true, "multiple": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.intake_mt_key=='intake-denial'?[]:['get-rx','appointment-intake','edit-intake']"}]}