{"components": [{"key": "heading_testosterone_section", "html": "<h1><center><strong>Testosterone Monitoring Bloodwork</strong></h1><center><p>To expedite your care, please answer the following questions to the best of your abilities. &nbsp;We ask these questions to provide the best possible care.&nbsp;</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "diagnosis_testosterone_deficiency", "type": "radio", "input": true, "label": "Have you been diagnosed with testosterone deficiency in the past?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "confirm_label": "Diagnosed with Testosterone Deficiency:", "optionsLabelPosition": "right"}, {"key": "heading_trt_therapy", "html": "<h4>Testosterone Therapy&nbsp;</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "duration_testosterone_deficiency", "data": {"values": [{"label": "<3 months", "value": "<3_months"}, {"label": "3-6 months", "value": "3-6_months"}, {"label": "6-12 months", "value": "6-12_months"}, {"label": "1-2 years", "value": "1-2_years"}, {"label": "2-5 years", "value": "2-5_years"}, {"label": "5+ years", "value": "5+_years"}, {"label": "I haven't been diagnosed with testosterone deficiency", "value": "not_diagnosed"}]}, "type": "select", "input": true, "label": "How many years ago were you diagnosed with testosterone deficiency?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Years since Diagnosis:", "optionsLabelPosition": "right"}, {"key": "currently_on_trt", "type": "radio", "input": true, "label": "Are you currently on Testosterone Replacement Therapy (TRT)?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "confirm_label": "Currently on TRT:", "optionsLabelPosition": "right"}, {"key": "duration_trt_therapy", "data": {"values": [{"label": "<1 months", "value": "<1_months"}, {"label": "1-3 months", "value": "1-3_months"}, {"label": "3-6 months", "value": "3-6_months"}, {"label": "6-12 months", "value": "6-12_months"}, {"label": "1-3 years", "value": "1-3_years"}, {"label": "3+ years", "value": "3+_years"}, {"label": "I haven't been diagnosed with testosterone deficiency", "value": "not_diagnosed"}]}, "type": "select", "input": true, "label": "How long have you been on TRT?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Duration on TRT:", "customConditional": "show = data.currently_on_trt == true;", "optionsLabelPosition": "right"}, {"key": "duration_current_dose", "data": {"values": [{"label": "<1 months", "value": "<1_months"}, {"label": "1-3 months", "value": "1-3_months"}, {"label": "3-6 months", "value": "3-6_months"}, {"label": "6-12 months", "value": "6-12_months"}, {"label": "1-3 years", "value": "1-3_years"}, {"label": "3+ years", "value": "3+_years"}, {"label": "Not applicable", "value": "not_applicable"}]}, "type": "select", "input": true, "label": "How long have you been on your current dose?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Duration on Current Dose:", "customConditional": "show = data.currently_on_trt == true;", "optionsLabelPosition": "right"}, {"key": "previously_on_trt", "type": "radio", "input": true, "label": "Have you been on Testosterone Replacement Therapy (TRT) in the past?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "confirm_label": "Previously on TRT:", "customConditional": "show = data.currently_on_trt == false;", "optionsLabelPosition": "right"}, {"key": "duration_trt_therapy_past", "data": {"values": [{"label": "<1 months", "value": "<1_months"}, {"label": "1-3 months", "value": "1-3_months"}, {"label": "3-6 months", "value": "3-6_months"}, {"label": "6-12 months", "value": "6-12_months"}, {"label": "1-3 years", "value": "1-3_years"}, {"label": "3+ years", "value": "3+_years"}, {"label": "I haven't been diagnosed with testosterone deficiency", "value": "not_diagnosed"}]}, "type": "select", "input": true, "label": "What duration of time (i.e. how many months/years) were you on TRT in the past?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Duration on Past TRT:", "customConditional": "show = data.previously_on_trt == true;", "optionsLabelPosition": "right"}, {"key": "when_was_trt_stopped", "data": {"values": [{"label": "<1 months", "value": "<1_months"}, {"label": "1-3 months", "value": "1-3_months"}, {"label": "3-6 months", "value": "3-6_months"}, {"label": "6-12 months", "value": "6-12_months"}, {"label": "1-3 years", "value": "1-3_years"}, {"label": "3+ years", "value": "3+_years"}, {"label": "I haven't been diagnosed with testosterone deficiency", "value": "not_diagnosed"}]}, "type": "select", "input": true, "label": "How long ago did you stop TRT?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Stopped TRT:", "customConditional": "show = data.previously_on_trt == true;", "optionsLabelPosition": "right"}, {"key": "reason_stopping_trt", "type": "radio", "input": true, "label": "What was your reason for stopping TRT?", "inline": false, "values": [{"label": "Couldn't get a prescription", "value": "cannot_obtain_prescription", "shortcut": ""}, {"label": "Decided to stop at my own direction", "value": "self-discontinued", "shortcut": ""}, {"label": "Medication side effects", "value": true, "shortcut": ""}, {"label": "Didn't like injections", "value": true, "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "confirm_label": "Reason for Stopping TRT:", "customConditional": "show = data.previously_on_trt == true;", "optionsLabelPosition": "right"}, {"key": "other_reason_stopping_trt", "type": "textarea", "input": true, "label": "Please state your “other” reason for stopping your TRT:", "tableView": true, "autoExpand": false, "customConditional": "show = data.reason_stopping_trt == 'other';"}, {"key": "heading_t2_administration", "html": "<h4>Testosterone Administration&nbsp;</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "dose_t2", "type": "textarea", "input": true, "label": "Please state your current or most recent testosterone dose (i.e. IM injection 200mg every 2 weeks):", "tableView": true, "autoExpand": false, "confirm_label": "Current Dose:"}, {"key": "current_trt_formulation", "type": "radio", "input": true, "label": "What testosterone formulation are you currently taking or used most recently?", "inline": false, "values": [{"label": "Testosterone Cypionate <strong> (injectable)</strong>", "value": "testosterone_cypionate", "shortcut": ""}, {"label": "Testosterone Enanthate <strong>(injectable)</strong>", "value": "testosterone_enanthate", "shortcut": ""}, {"label": "Testosterone Propionate <strong> (injectable)</strong>", "value": "testosterone_propionate", "shortcut": ""}, {"label": "Testosterone Sustanon <strong> (injectable)</strong>", "value": "testosterone_sustanon", "shortcut": ""}, {"label": "Testosterone Undecanoate <strong> (oral)</strong>", "value": "testosterone_undecanoate", "shortcut": ""}, {"label": "Testosterone Patch <strong> (topical)</strong>", "value": "testosterone_patch", "shortcut": ""}, {"label": "Testosterone Gel <strong>(topical)</strong>", "value": "testosterone_gel", "shortcut": ""}, {"label": "Testosterone Trans-nasal Gel <strong> (topical)</strong>", "value": "testosterone_nasal_gel", "shortcut": ""}, {"label": "None of the above", "value": "none_of_the_above", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "confirm_label": "Current TRT Formulation:", "optionsLabelPosition": "right"}, {"key": "unsafe_injection", "type": "radio", "input": true, "label": "Do you feel that any of your injections have been unsafe (i.e. clean needles were not used)?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}, {"label": "I don't understand this question", "value": "did_not_understand"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Unsafe Injections:", "customConditional": "show = data.current_trt_formulation == 'testosterone_cypionate' || data.current_trt_formulation == 'testosterone_enanthate' || data.current_trt_formulation == 'testosterone_propionate' || data.current_trt_formulation == 'testosterone_sustanon';", "optionsLabelPosition": "right"}, {"key": "last_blood_test_stis", "type": "radio", "input": true, "label": "When was your last blood test for STIs (Hepatitis C, HIV, syphilis)?", "inline": false, "values": [{"label": "<1 month", "value": "<1_month", "shortcut": ""}, {"label": "1-3 months", "value": "1-3_months", "shortcut": ""}, {"label": "3-6 months", "value": "3-6_months", "shortcut": ""}, {"label": "6-12 months", "value": "6-12_months", "shortcut": ""}, {"label": "12+ months", "value": "12+_months", "shortcut": ""}, {"label": "Never", "value": "never", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "confirm_label": "Last STI Blood Test:", "customConditional": "show = data.unsafe_injection !== false;", "optionsLabelPosition": "right"}, {"key": "injection_provider", "type": "radio", "input": true, "label": "Who does your injections?", "inline": false, "values": [{"label": "Medical Professional", "value": "medical_professional"}, {"label": "Self-Injection", "value": "self_injection"}, {"label": "Other", "value": "other"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Injection Provider:", "customConditional": "show = data.current_trt_formulation == 'testosterone_cypionate' || data.current_trt_formulation == 'testosterone_enanthate' || data.current_trt_formulation == 'testosterone_propionate'|| data.current_trt_formulation == 'testosterone_sustanon';", "optionsLabelPosition": "right"}, {"key": "other_injection_provider", "type": "textarea", "input": true, "label": "Please state your “other” injection provider:", "tableView": true, "autoExpand": false, "confirm_label": "Injection Provider:", "customConditional": "show = data.injection_provider == 'other';"}, {"key": "heading_diagnostic_history", "html": "<h4>Diagnostic History&nbsp;</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "original_trt_diagnosis", "type": "radio", "input": true, "label": "Who diagnosed you with testosterone deficiency originally?", "inline": false, "values": [{"label": "Family Doctor / General Practitioner", "value": "general_practitioner", "shortcut": ""}, {"label": "Nurse practitioner", "value": "nurse_practitioner", "shortcut": ""}, {"label": "<PERSON><PERSON><PERSON>", "value": "naturopath", "shortcut": ""}, {"label": "Self-Diagnosis", "value": "self-diagnosis", "shortcut": ""}, {"label": "Prefer not to disclose", "value": "prefer_not_to_disclose", "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "confirm_label": "Diagnosed by:", "optionsLabelPosition": "right"}, {"key": "diagnosis_other_provider", "type": "textarea", "input": true, "label": "Please state who diagnosed you with testosterone deficiency:", "tableView": true, "autoExpand": false, "confirm_label": "Diagnosed by:", "customConditional": "show = data.original_trt_diagnosis == 'other';"}, {"key": "current_t2_prescriber", "type": "selectboxes", "input": true, "label": "Where do you currently obtain your testosterone:", "values": [{"label": "Prefer not to disclose", "value": "prefer_not_to_disclose", "shortcut": ""}, {"label": "General practitioner / family doctor", "value": "general_practitioner", "shortcut": ""}, {"label": "Endocrinologist", "value": "endocrinologist", "shortcut": ""}, {"label": "Nurse Practitioner", "value": "nurse_practitioner", "shortcut": ""}, {"label": "Online Pharmacy", "value": "online_pharmacy", "shortcut": ""}, {"label": "Overseas", "value": "overseas", "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "confirm_label": "Diagnostician:", "optionsLabelPosition": "right"}, {"key": "other_source_of_testosterone", "type": "textarea", "input": true, "label": "Please state where you obtained your testosterone:", "tableView": true, "autoExpand": false, "confirm_label": "Source of Testosterone:", "customConditional": "show = data.current_t2_prescriber.other;"}, {"key": "original_diagnostic_tests", "type": "selectboxes", "input": true, "label": "What tests were originally used to confirm a diagnosis of testosterone deficiency?", "values": [{"label": "Free Testosterone (FT)", "value": "free_testosterone", "shortcut": ""}, {"label": "Total Testosterone (TT)", "value": "total_testosterone", "shortcut": ""}, {"label": "Thyroid Stimulating Hormone (TSH)", "value": "tsh", "shortcut": ""}, {"label": "Luteinizing hormone (LH)", "value": "lh", "shortcut": ""}, {"label": "Follicular simulating hormone (FSH)", "value": "fsh", "shortcut": ""}, {"label": "Prolactin (PRL)", "value": "prolactin", "shortcut": ""}, {"label": "Sex Hormone Binding Globulin (SHBG)", "value": "shbg", "shortcut": ""}, {"label": "No testing was Completed", "value": "no_testing", "shortcut": ""}, {"label": "I don't remember", "value": "doesn't_know", "shortcut": ""}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "confirm_label": "Diagnostic Tests at Diagnosis:", "optionsLabelPosition": "right"}, {"key": "physical_exam", "type": "radio", "input": true, "label": "Did you have a physical examination by a Physician/Nurse Practitioner prior to starting testosterone therapy?", "inline": false, "values": [{"label": "Yes", "value": "exam_completed", "shortcut": ""}, {"label": "No", "value": "no_exam_completed", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "diagnose_number_testosterone_tests", "type": "radio", "input": true, "label": "How many testosterone tests did you have before you were diagnosed with testosterone deficiency?", "inline": false, "values": [{"label": "One", "value": "one", "shortcut": ""}, {"label": "Two", "value": "two", "shortcut": ""}, {"label": "More than Two", "value": "two+", "shortcut": ""}, {"label": "No Previous Testosterone Testing", "value": "no_prior_testing", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "heading_symptom_review", "html": "<h4>Symptom Review&nbsp;</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "specific_symptoms", "type": "selectboxes", "input": true, "label": "Prior to starting testosterone, did you have any of the following specific symptoms:", "values": [{"label": "Loss of hair in groin, face, or pubic area (not scalp)", "value": "hair_loss"}, {"label": "Smaller testicles", "value": "smaller_testicles"}], "inputType": "checkbox", "tableView": true, "confirm_label": "Specific symptoms present before starting testosterone:", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_specific_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = !!data.none_of_the_above_specific_symptoms || _.some(_.values(data.specific_symptoms));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "specific_symptoms_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following specific symptoms:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "Specific symptoms not present:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.specific_symptoms, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "supportive_symptoms", "type": "selectboxes", "input": true, "label": "Prior to starting testosterone, did you have any of the following symptoms:", "values": [{"label": "Breast discomfort, breast development (gynecomastia)", "value": "breast_discomfort"}, {"label": "Low sperm count", "value": "low_sperm_count"}, {"label": "Hot flushes / sweating", "value": "hot_flushes_sweating"}, {"label": "Low libido", "value": "low_libido"}, {"label": "Erectile dysfunction (difficulty getting or maintaining erections)", "value": "erectile_dysfunction"}, {"label": "Loss of morning erections", "value": "no_am_erections"}], "inputType": "checkbox", "tableView": true, "confirm_label": "Symptoms present before starting testosterone:", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_supportive_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = !!data.none_of_the_above_supportive_symptoms || _.some(_.values(data.supportive_symptoms));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "supportive_symptoms_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following pre-testosterone symptoms:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "Pre-testosterone symptoms not present:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.supportive_symptoms, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "non_specific_symptoms", "type": "selectboxes", "input": true, "label": "Prior to starting testosterone, did you have any of the following symptoms:", "values": [{"label": "Fatigue", "value": "fatigue"}, {"label": "Low mood", "value": "low_mood"}, {"label": "Poor concentration / memory", "value": "poor_concentration_memory"}, {"label": "Anemia", "value": "anemia"}, {"label": "Decreased muscle strength", "value": "decreased_muscle_strength"}, {"label": "Increased body fat and muscle gain", "value": "high_body_fat"}], "inputType": "checkbox", "tableView": true, "confirm_label": "Non-specific symptoms present before starting testosterone:", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_non_specific_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = !!data.none_of_the_above_non_specific_symptoms || _.some(_.values(data.non_specific_symptoms));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "non_specific_symptoms_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following non-specific symptoms:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "Non-specific symptoms not present:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.non_specific_symptoms, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "symptom_improvement_on_trt", "type": "radio", "input": true, "label": "Did you notice an improvement in your symptoms on TRT?", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "I'm not sure", "value": "i'm_not_sure", "shortcut": ""}, {"label": "Doesn't Apply", "value": "doesn't_apply", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "confirm_label": "Symptom Improvement on TRT:", "optionsLabelPosition": "right"}, {"key": "heading_previous_testing", "html": "<h4>Previous Testing&nbsp;</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "last_t2_test", "data": {"values": [{"label": "<30 days", "value": "<30_days"}, {"label": "1-3 months", "value": "1-3_months"}, {"label": "3-6 months", "value": "3-6_months"}, {"label": "6-12 months", "value": "6-12_months"}, {"label": "1-3 years", "value": "1-3_years"}, {"label": "3+ years", "value": "3+_years"}, {"label": "I don't know", "value": "doesn't_know"}, {"label": "I haven't been tested", "value": "not_tested"}]}, "type": "select", "input": true, "label": "When was your last testosterone blood test outside of using TeleTest??", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Last Testosterone Test:", "optionsLabelPosition": "right"}, {"key": "last_cbc_test", "data": {"values": [{"label": "<30 days", "value": "<30_days"}, {"label": "1-3 months", "value": "1-3_months"}, {"label": "3-6 months", "value": "3-6_months"}, {"label": "6-12 months", "value": "6-12_months"}, {"label": "1-3 years", "value": "1-3_years"}, {"label": "3+ years", "value": "3+_years"}, {"label": "Doesn't know", "value": "doesn't_know"}, {"label": "I haven't been tested", "value": "not_tested"}]}, "type": "select", "input": true, "label": "When was your last Complete Blood Count (CBC) outside of using TeleTest??", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Last CBC Test:", "optionsLabelPosition": "right"}, {"intake_template_key": "hx-polycythemia"}, {"key": "last_t2_measurement", "type": "radio", "input": true, "label": "Was your last testosterone measurement <strong>high, normal or low</strong>?", "inline": false, "values": [{"label": "High", "value": "high", "shortcut": ""}, {"label": "Normal", "value": "normal", "shortcut": ""}, {"label": "Low", "value": "low", "shortcut": ""}, {"label": "I Don't Know", "value": "doesn't_know", "shortcut": ""}, {"label": "I Don't Understand This Question", "value": "doesnt_understand", "shortcut": ""}, {"label": "Not Applicable", "value": "not_applicable", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "confirm_label": "Last Testosterone Measurement:", "optionsLabelPosition": "right"}, {"key": "directing_dose_changes", "type": "radio", "input": true, "label": "Who adjusts your testosterone dose?", "inline": false, "values": [{"label": "Physician", "value": "physician", "shortcut": ""}, {"label": "Nurse Practitioner", "value": "nurse_practitioner", "shortcut": ""}, {"label": "I Changed the Dose Myself", "value": "self-directed", "shortcut": ""}, {"label": "I Don't Understand This Question", "value": "doesnt_understand", "shortcut": ""}, {"label": "Not Applicable", "value": "not_applicable", "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "confirm_label": "Dose Changes Directed By:", "optionsLabelPosition": "right"}, {"key": "other_directing_dose_changes", "type": "textarea", "input": true, "label": "Please state where you obtained your testosterone:", "tableView": true, "autoExpand": false, "confirm_label": "Directing Dose Changes:", "customConditional": "show = data.directing_dose_changes == 'other';"}, {"key": "heading_adverse_reaction_history", "html": "<h4>Testosterone Safety&nbsp;</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "bad_sleep_on_trt", "type": "radio", "input": true, "label": "Have you noticed you have worse sleep on testosterone replacement?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "confirm_label": "Sleep on TRT:", "optionsLabelPosition": "right"}, {"key": "polycythemia_symptoms", "type": "selectboxes", "input": true, "label": "Do any of the following symptoms apply to you:", "values": [{"label": "Unexplained Bruising (i.e. bruising that occurs without injury)", "value": "bruising", "shortcut": ""}, {"label": "Unexplained fevers (i.e. fever without cold symptoms)", "value": "fevers", "shortcut": ""}, {"label": "Night sweats (i.e. soaking the bed in sweat)", "value": "night_sweats", "shortcut": ""}, {"label": "Generalized whole body itchiness", "value": "pruritis", "shortcut": ""}, {"label": "Chest pain / tightness", "value": "chest_pain", "shortcut": ""}, {"label": "Shortness of breath", "value": "shortness_of_breath", "shortcut": ""}, {"label": "Blurry vision (doesn't improve with glasses)", "value": "blurry_vision", "shortcut": ""}], "adminFlag": true, "inputType": "checkbox", "tableView": true, "confirm_label": "Symptoms of High Red Blood Cells:", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_polycythemia_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = !!data.none_of_the_above_polycythemia_symptoms || _.some(_.values(data.polycythemia_symptoms));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "polycythemia_symptoms_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following high red blood cell symptoms:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "High red blood cell symptoms not present:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.polycythemia_symptoms, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "luts_symptoms", "type": "selectboxes", "input": true, "label": "Do any of the following apply to you:", "values": [{"label": "Weakened urinary stream", "value": "weak_stream", "shortcut": ""}, {"label": "Urinary stream that starts and stops", "value": "intermittent_stream", "shortcut": ""}, {"label": "Feel like I haven't emptied my bladder", "value": "incomplete_emptying", "shortcut": ""}, {"label": "Urgency to urinate or frequent urination (that is new)", "value": "urinary_urgency", "shortcut": ""}], "inputType": "checkbox", "tableView": true, "confirm_label": "Symptoms of Lower Urinary Tract Symptoms (LUTS):", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_luts_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = !!data.none_of_the_above_luts_symptoms || _.some(_.values(data.luts_symptoms));"}, "adminFlag": true, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "luts_symptoms_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following urinary tract symptoms:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "Lower urinary tract symptoms not present:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.luts_symptoms, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "content2", "html": "<h2>Questions for the Doctor</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "any_other_questions", "type": "radio", "input": true, "label": "Do you have any specific questions for the healthcare provider?", "inline": false, "values": [{"label": "Yes, I have additional questions I would like to discuss", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "defaultValue": false, "confirm_label": "Any Other Questions:", "optionsLabelPosition": "right"}, {"key": "stated_other_questions", "type": "textarea", "input": true, "label": "Please use this area to ask any questions that you might have for your health care provider:", "adminFlag": true, "tableView": true, "autoExpand": false, "customConditional": "show = data.any_other_questions === true;"}, {"key": "intake_denial_reasons", "type": "textfield", "input": true, "label": "Intake Denial Reasons:", "hidden": true, "disabled": true, "multiple": true, "adminFlag": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "value = [];", "refreshOnChange": true}, {"key": "intake_kind", "type": "textfield", "input": true, "label": "Intake Kind", "hidden": true, "disabled": true, "tableView": true, "clearOnHide": false, "calculateValue": "value = !data.intake_denial_reasons?.length ? 'mail' : 'chat';"}, {"key": "intake_mt_key", "type": "textfield", "input": true, "label": "Intake Mail Template Key", "hidden": true, "disabled": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = 'intake-mens-trt'"}]}