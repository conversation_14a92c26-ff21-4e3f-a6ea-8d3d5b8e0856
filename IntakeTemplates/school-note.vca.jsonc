{"components": [{"key": "request_school_note", "type": "radio", "input": true, "label": "Are you requesting a school note?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "absence_duration", "type": "radio", "input": true, "label": "What is the duration of your absence?", "values": [{"label": "Less than 7 days", "value": "<7_days"}, {"label": "7 days or more", "value": ">=7_days"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.request_school_note === 'yes';", "optionsLabelPosition": "right"}, {"key": "long_absence_disqualified", "html": "<h3 class='text-red'>We can only provide school notes for absences under 7 days. Please seek in-person care if your absence is 7 days or more.</h3>", "type": "content", "input": true, "tableView": false, "clearOnHide": false, "customConditional": "show = data.request_school_note === 'yes' && data.absence_duration === '>=7_days';"}, {"key": "reason_for_absence", "type": "selectboxes", "input": true, "label": "What is the reason for your absence? (Select all that apply)", "values": [{"label": "Cold or Influenza", "value": "cold_flu"}, {"label": "<PERSON><PERSON><PERSON> illness", "value": "stomach_illness"}, {"label": "Flare of a health issue", "value": "flare_health_issue"}, {"label": "Menstrual pain", "value": "menstrual_discomfort", "customConditional": "show = data.sex === 'female';"}, {"label": "<PERSON><PERSON> / Migraine", "value": "headache"}, {"label": "Insomnia (i.e. difficulty sleeping)", "value": "insomnia"}, {"label": "Stress / Mental Health", "value": "stress"}], "inputType": "checkbox", "tableView": true, "customConditional": "show = data.request_school_note === 'yes' && data.absence_duration === '<7_days';", "optionsLabelPosition": "right"}, {"key": "no_reason_for_absence", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a reason."}, "validate": {"custom": "valid = !!data.no_reason_for_absence || !!_.some(_.values(data.reason_for_absence));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.request_school_note === 'yes' && data.absence_duration === '<7_days';"}, {"key": "red_flag_physical", "type": "selectboxes", "input": true, "label": "Are you currently experiencing any of the following:", "values": [{"label": "Feel unwell", "value": "feel_unwell"}, {"label": "Chest pain or heaviness", "value": "chest_pain"}, {"label": "Abdominal pain or cramping", "value": "abdominal_cramping"}, {"label": "Shortness of breath", "value": "dyspnea"}, {"label": "Feel lightheaded or faint", "value": "presy<PERSON><PERSON>"}, {"label": "Arm or leg swelling", "value": "limb_swelling"}, {"label": "Heart palpitations (slow or fast heart beat)", "value": "palpitations"}, {"label": "Coughing up blood", "value": "hemoptysis"}, {"label": "Muscle weakness or cramping", "value": "muscle_cramps"}, {"label": "Pelvic pain", "value": "pelvic_pain", "customConditional": "show = data.sex === 'female';"}], "inputType": "checkbox", "tableView": true, "customConditional": "show = data.request_school_note === 'yes' && data.absence_duration === '<7_days' && _.some(data.reason_for_absence, function(val, key) { return key !== 'stress' && val; }) && !data.no_reason_for_absence;", "optionsLabelPosition": "right"}, {"key": "no_red_flag_physical", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = !!data.no_red_flag_physical || !!_.some(_.values(data.red_flag_physical));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.request_school_note === 'yes' && data.absence_duration === '<7_days' && _.some(data.reason_for_absence, function(val, key) { return key !== 'stress' && val; }) && !data.no_reason_for_absence;"}, {"key": "red_flag_mental", "type": "selectboxes", "input": true, "label": "Are you currently experiencing any of the following:", "values": [{"label": "Having thoughts about harming yourself, ending your life, or hurting others", "value": "si_hi_screen"}, {"label": "Hearing voices or seeing things that others do not", "value": "unusual_sensory_experiences"}, {"label": "Feeling extremely suspicious or fearful of others without a clear reason", "value": "paranoid_fears"}, {"label": "Feeling very confused or like your thoughts are all mixed up", "value": "confused_thinking"}, {"label": "Experiencing a sudden, overwhelming sadness or hopelessness", "value": "overwhelming_sadness"}], "inputType": "checkbox", "tableView": true, "customConditional": "show = data.request_school_note === 'yes' && data.absence_duration === '<7_days' && data.reason_for_absence && data.reason_for_absence.stress && !data.no_reason_for_absence;", "optionsLabelPosition": "right"}, {"key": "no_red_flag_mental", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = !!data.no_red_flag_mental || !!_.some(_.values(data.red_flag_mental));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.request_school_note === 'yes' && data.absence_duration === '<7_days' && data.reason_for_absence && data.reason_for_absence.stress && !data.no_reason_for_absence;"}, {"key": "symptoms_improving", "type": "radio", "input": true, "label": "Do you feel your symptoms are improving?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.request_school_note === 'yes' && data.absence_duration === '<7_days' && data.no_red_flag_physical === true && ((data.reason_for_absence && data.reason_for_absence.stress) ? data.no_red_flag_mental === true : true);", "optionsLabelPosition": "right"}, {"key": "no_indications", "type": "textfield", "input": true, "label": "No Indication:", "hidden": true, "disabled": true, "multiple": false, "tableView": true, "clearOnHide": false, "defaultValue": true, "calculateValue": "value = (data.request_school_note !== 'yes' || data.absence_duration !== '<7_days' || data.no_reason_for_absence === true || !_.some(_.values(data.reason_for_absence))) ? true : false;", "refreshOnChange": true}, {"key": "contraindication_keys", "type": "textfield", "input": true, "label": "Contraindication keys:", "hidden": true, "disabled": true, "multiple": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "value = _.union(_.keys(_.pickBy(data.red_flag_physical || {})), _.keys(_.pickBy(data.red_flag_mental || {})), (data.symptoms_improving === 'no' ? ['symptoms_not_improving'] : []));", "refreshOnChange": true}, {"key": "approved_html", "type": "content", "input": true, "tableView": false, "clearOnHide": false, "defaultValue": "<h3 class='text-green'>You're eligible for a school note for your absence.</h3></br><p>A TeleTest physician will provide a note covering your illness up to a maximum of 7 days.</p>", "refreshOnChange": true}, {"key": "rejected_html", "type": "content", "input": true, "tableView": false, "clearOnHide": false, "defaultValue": "<h3 class='text-red'>You're ineligible for a school note at this time. Please seek in-person care with a physician to discuss your concerns.</h3>"}]}