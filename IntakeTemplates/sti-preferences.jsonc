{"components": [{"key": "chief_complaint", "type": "radio", "input": true, "label": "I am interested in the following:", "inline": false, "values": [{"label": "Testing only", "value": "testing"}, {"label": "Testing and treatment", "value": "testing_and_treatment"}, {"label": "Treatment (i.e. medication) only", "value": "treatment_only"}, {"label": "Other", "value": "other"}], "validate": {"required": true}, "tableView": true, "defaultValue": "testing", "confirm_label": "Interested in:", "optionsLabelPosition": "right"}, {"key": "stated_reason_for_interested_in_other", "type": "textarea", "input": true, "label": "As you selected other, please specify your reason below:", "validate": {"required": true}, "tableView": true, "autoExpand": false, "customConditional": "show = data.chief_complaint == 'other';"}, {"intake_template_key": "hpc-sti-hepatitis-rf"}, {"key": "testing_preferences_heading", "html": "<h2 class='text-center'><strong>Testing</strong></h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.show_all && data.chief_complaint != 'treatment_only'"}, {"key": "recommend_uti_note", "html": "<p>Based on your symptoms we recommend a urine culture culture (UTI) test. This has been added to your test panel.</p>", "type": "content", "input": true, "tableView": true, "clearOnHide": false, "refreshOnChange": true, "customConditional": "show = data.chief_complaint != 'treatment_only' && !!data.recommend_uti"}, {"key": "recommend_vswb_note", "html": "<p>Based on your symptoms we recommend a vaginal swab test. This has been added to your test panel.</p><p>A vaginal swab is a self-administered test completed at a lab facility which checks for yeast infections and bacterial vaginosis (BV).</p>", "type": "content", "input": true, "tableView": true, "clearOnHide": false, "refreshOnChange": true, "customConditional": "show = data.chief_complaint != 'treatment_only' && !!data.recommend_vswb"}, {"key": "recommend_any", "html": "<p>You may opt-out of tests below, however this is not recommended as it makes it more difficult for your doctor to diagnose and treat you.</p>", "type": "content", "input": true, "tableView": true, "clearOnHide": false, "calculateValue": "value = data.chief_complaint != 'treatment_only' && (!!data.recommend_uti || !!data.recommend_vswb)", "refreshOnChange": true, "customConditional": "show = data.recommend_any"}, {"key": "all_assays", "type": "textfield", "input": true, "label": "Panel + Recommended Assays:", "hidden": true, "disabled": true, "multiple": true, "hideLabel": true, "tableView": true, "spellcheck": false, "clearOnHide": false, "calculateValue": "value = data.chief_complaint=='treatment_only'?[] : _.uniq(_.difference(_.concat(data.originalAssays,(data.recommend_uti?['UTI']:[]),(data.recommend_vswb?['VSWB']:[]),(data.recommend_hepb?['HEPB-CAB','HEPB-SAB']:[]),(data.recommend_hepc?['HCV-AB']:[]),((data.sti_testing_requested)?['CT','GC','TRICH','VDRL','HIV']:[])),(data.sex=='male'?['TRICH']:[]),(data.previous_hiv_positive===true?['HIV']:[]),(data.opt_out_only_oral_anal=='no_testing'?['CT','GC','TRICH']:[])))", "optionsLabelPosition": "right"}, {"key": "opt_out_gate", "type": "radio", "input": true, "label": "Do you wish to opt-out (skip) any testing panel or recommended assays?", "inline": false, "values": [{"label": "Yes", "value": true}, {"label": "No", "value": false}], "validate": {"required": true}, "tableView": false, "defaultValue": false, "customConditional": "show = data.chief_complaint != 'treatment_only'", "optionsLabelPosition": "right"}, {"key": "opt_out_assays", "type": "selectboxes", "input": true, "label": "Please select the tests you wish to opt-out of (skip):", "hidden": false, "values": [{"label": "Urine culture (UTI) test", "value": "UTI", "customConditional": "show = !!data.all_assays.includes('UTI')"}, {"label": "BV/Yeast Swab (Life Labs Only)", "value": "VSWB", "customConditional": "show = !!data.all_assays.includes('VSWB')"}, {"label": "Chlam<PERSON>ia (urine test)", "value": "CT", "customConditional": "show = !!data.all_assays.includes('CT')"}, {"label": "Gonorrhea (urine test)", "value": "GC", "customConditional": "show = !!data.all_assays.includes('GC')"}, {"label": "Trichomoniasis (urine test)", "value": "TRICH", "customConditional": "show = !!data.all_assays.includes('TRICH')"}, {"label": "HIV (blood test)", "value": "HIV", "customConditional": "show = !!data.all_assays.includes('HIV')"}, {"label": "Syphilis (blood test)", "value": "VDRL", "customConditional": "show = !!data.all_assays.includes('VDRL')"}], "disabled": false, "hideLabel": false, "inputType": "checkbox", "tableView": false, "spellcheck": false, "clearOnHide": true, "defaultValue": {"CT": false, "GC": false, "HIV": false, "UTI": false, "VDRL": false, "VSWB": false, "TRICH": false}, "customConditional": "show = !!data.opt_out_gate && data.chief_complaint != 'treatment_only'", "optionsLabelPosition": "right"}, {"key": "opt_out_names", "type": "textfield", "input": true, "label": "<PERSON><PERSON> opted out of:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "clearOnHide": false, "calculateValue": "value = _.join(_.keys(_.pickBy(data.opt_out_assays)).map(k=>data.bulletDict[k].name),', ')"}, {"key": "userBulletKeys", "type": "textfield", "input": true, "label": "User Bullets:", "hidden": true, "disabled": true, "multiple": true, "hideLabel": true, "tableView": true, "spellcheck": false, "clearOnHide": false, "calculateValue": "value = _.difference(data.all_assays, _.keys(_.pickBy(data.opt_out_assays)))", "optionsLabelPosition": "right"}, {"key": "show_all", "type": "textfield", "input": true, "label": "Show STI Testing Questions?", "hidden": true, "disabled": true, "multiple": false, "hideLabel": true, "tableView": false, "clearOnHide": false, "defaultValue": true, "calculateValue": "value = data.sku == 'std_pn_cgtvh' || data.sti_testing_requested || !!_.intersection(data.userBulletKeys, ['CT','GC','TRICH','VDRL','HIV']).length", "refreshOnChange": true}, {"key": "first_prefer", "type": "radio", "input": true, "label": "<p>There may be risks and/or side effects associated with treatments such as prescription antibiotics. TeleTest physicians generally offer treatment before test results in a few scenarios: <li>You have symptoms following a new exposure</li><li>You have a confirmed STI exposure (i.e. a partner is positive for chlamydia)</li><li>You have a high risk encounter</li></p>Which option would you prefer:", "inline": false, "values": [{"label": "Immediate testing and treatment based on results", "value": "testing"}, {"label": "I would like treatment before test results are available", "value": "treatment"}, {"label": "I would like to discuss options with the doctor", "value": "chat"}], "validate": {"required": true}, "tableView": true, "defaultValue": "testing", "confirm_label": "Interested in:", "customConditional": "show = data.chief_complaint == 'testing_and_treatment'", "optionsLabelPosition": "right"}, {"key": "vdrl_only", "html": "<h2 style='text-align:center;'><strong>Syphilis Only Panel</strong></h2>", "type": "content", "input": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = _.isEqual(data.userBulletKeys, ['VDRL']);", "customConditional": "show = !!data.vdrl_only;"}, {"key": "vdrl_only_reason", "type": "radio", "input": true, "label": "By opting out of the standard STI screening panel and only selecting Syphilis, you are not being screened for HIV, Hepatitis C, and urine transmissible infections. Not testing for these infections carries a risk of having an undiagnosed sexually transmitted infection. Please specify your reason for opting out:", "inline": false, "values": [{"label": "I am monitoring my syphilis levels following treatment", "value": "monitoring_syphilis_levels"}, {"label": "Other", "value": "other"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = !!data.vdrl_only", "optionsLabelPosition": "right"}, {"key": "standard_sti_opt_out", "html": "<h2 class='text-center'><strong>Standard STI Panel Opt-Out</strong></h2>", "type": "content", "input": true, "label": "Opt-Out", "tableView": false, "clearOnHide": false, "calculateValue": "value = !data.vdrl_only && _.difference(_.keys(_.pickBy(data.opt_out_assays)),['UTI','VSWB'])?.length", "customConditional": "show = data.show_all && !!data.standard_sti_opt_out;"}, {"key": "standard_sti_opt_out_reason", "type": "radio", "input": true, "label": "By opting out of the standard STI screening panel, there is a risk of having an undiagnosed sexually transmitted infection. Please specify you reason for opting out:", "inline": false, "values": [{"label": "I like to test with bloodwork less frequently than urine testing.", "value": "prefer_less_frequent_bloodwork"}, {"label": "I will get bloodwork done with my next set of routine tests.", "value": "will_complete_with_future_routine_bloodwork"}, {"label": "I do not want HIV testing at this time.", "value": "declines_hiv_testing"}, {"label": "I do not want syphilis testing at this time.", "value": "declines_vdrl_testing"}, {"label": "I already completed HIV and Syphilis testing recently. I'm aware I need new bloodwork following new partner exposure.", "value": "completed_hiv_syphilis_recently"}, {"label": "I already completed urine STI testing recently.", "value": "completed_urine_sti_recently"}, {"label": "I will get urine STI testing done with my next set of routine tests.", "value": "will_complete_urine_sti_with_future_testing"}, {"label": "Other", "value": "other"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.show_all && !!data.standard_sti_opt_out;", "optionsLabelPosition": "right"}, {"key": "standard_sti_opt_out_reason_stated", "type": "textarea", "input": true, "label": "Please specify your reason for opting out of Standard STI screening:", "tableView": true, "autoExpand": false, "customConditional": "show = data.standard_sti_opt_out_reason == 'other'"}, {"key": "vdrl_only_reason_stated", "type": "textarea", "input": true, "label": "Please specify your reason here:", "tableView": true, "autoExpand": false, "customConditional": "show = data.vdrl_only_reason == 'other';"}, {"key": "treatment_preferences_heading", "html": "<h2 class='text-center'><strong>Treatment</strong></h2>", "type": "content", "input": false, "tableView": false, "customConditional": "show = data.show_all && data.chief_complaint != 'testing'"}, {"key": "exposure_keys", "type": "textfield", "input": true, "label": "Confirmed Exposure Keys:", "hidden": true, "disabled": true, "multiple": true, "adminFlag": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "value = _.keys(_.pickBy(data.list_of_confirmed_exposures))", "refreshOnChange": true}, {"key": "ask_ct_preferences", "html": "<h3 class='text-center'><strong>Chlamydia</strong></h3>You indicated that you were exposed to chlamydia which is treated with antibiotics, either:<ul><li>Doxycycline for 7 days (preferred due to a higher cure rate)</li><li>Azithromycin (4 tablets) as a single dose (preferred if you're forgetful with pills)</li></ul>Our preference is to treat with doxycycline, but both are effective options.", "type": "content", "input": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.show_all && data.chief_complaint != 'testing' && data.list_of_confirmed_exposures && data.list_of_confirmed_exposures.chlamydia", "customConditional": "show = data.ask_ct_preferences"}, {"key": "chlamydia_rx", "type": "radio", "input": true, "label": "If treatment is provided, which chlamydia treatment option do you prefer?", "inline": false, "values": [{"label": "<strong>Doxycycline</strong>: Twice daily for 7 days", "value": "doxycycline"}, {"label": "<strong>Azithromycin</strong>: Four tablets as a single dose", "value": "azithromycin"}, {"label": "I will seek treatment on my own (no treatment from TeleTest)", "value": "no_treatment"}, {"label": "I will wait for test results", "value": "wait"}, {"label": "I was already provided treatment - this doesn't apply", "value": "already_treated"}], "validate": {"required": true}, "tableView": true, "clearOnHide": true, "customConditional": "show = data.ask_ct_preferences", "optionsLabelPosition": "right"}, {"key": "ask_gc_preferences", "html": "<h3 class='text-center'><strong>Gonorrhea</strong></h3>You indicated that you were exposed to gonorrhea which is treated with antibiotics, either:<ul><li>An injection of Ceftriaxone & 4 tablets of Azithromycin</li><li>Tablets only: 2 tablets of Cefixime & 4 tablets of Azithromycin</li></ul> Our recommendation is injection, which requires visiting a local walk-in clinic or emergency room. Alternatively, you can opt for oral medication, which is available immediately at any pharmacy. Injection is preferred as there is a lower rate of treatment failure compared to oral medications. However, if injection leads to a significant delay in treatment, it's better to go with oral tablets.", "type": "content", "input": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.show_all && data.chief_complaint != 'testing' && data.list_of_confirmed_exposures && data.list_of_confirmed_exposures.gonorrhea", "customConditional": "show = data.ask_gc_preferences"}, {"key": "gonorrhea_rx", "type": "radio", "input": true, "label": "Which gonorrhea treatment option do you prefer?", "inline": false, "values": [{"label": "Injection: I will visit a walk-in clinic or emergency room (preferred)", "value": "injection"}, {"label": "Tablets only", "value": "tablets"}, {"label": "I will seek treatment on my own (no treatment from TeleTest)", "value": "no_treatment"}, {"label": "I will wait for test results", "value": "wait"}], "validate": {"required": true}, "tableView": true, "clearOnHide": true, "customConditional": "show = data.ask_gc_preferences", "optionsLabelPosition": "right"}, {"key": "rxts", "type": "textfield", "input": true, "label": "Rx Templates:", "hidden": true, "disabled": true, "multiple": true, "adminFlag": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "value = data.gonorrhea_rx=='injection'?['ag-azithromycin-250mg']:(data.gonorrhea_rx=='tablets'?['ag-azithromycin-250mg','auro-cefixime-400mg']:(data.chlamydia_rx=='azithromycin'?['ag-azithromycin-250mg']:(data.chlamydia_rx=='doxycycline'?['doxycycline-tablets-100mg']:[])))", "refreshOnChange": true}, {"key": "exposure_idrs", "type": "textfield", "input": true, "label": "Known Exposure Intake Denial Reasons:", "hidden": true, "disabled": true, "multiple": true, "adminFlag": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "value = !data.exposure_keys?[]:data.concerned_about_known_exposure=='did_not_understand'?['concerned_about_known_exposure.did_not_understand']:(data.exposure_keys.includes('chlamydia')&&data.exposure_keys.includes('gonorrhea')?['exposure.ct_and_gc']:(!_.isEqual(data.exposure_keys,['chlamydia'])&&!_.isEqual(data.exposure_keys,['gonorrhea'])?data.exposure_keys.map(k=>`exposure.${k}`):[]));", "refreshOnChange": true}]}