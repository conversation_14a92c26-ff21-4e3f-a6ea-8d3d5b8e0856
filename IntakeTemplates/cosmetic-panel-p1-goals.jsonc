{"components": [{"key": "heading_cosmetic_dermatology", "html": "<h1><strong>Cosmetic Dermatology Consult</strong></h1><p>Please complete the following questionnaire to help us understand your skin concerns, treatment history, and goals.</p>", "type": "content", "input": false, "label": "Content"}, {"key": "photo_upload_header", "html": "<h2>Photo Upload</h2>", "type": "content", "input": false, "label": "Content"}, {"key": "uploadUrl", "url": "/app/q/{qpk}/formio-files/{pk}/", "type": "file", "image": true, "input": true, "label": "Upload: URL", "webcam": true, "capture": "user", "storage": "url", "multiple": true, "fileTypes": [{"label": "", "value": ""}], "tableView": true, "validate": {"required": true}, "validateWhenHidden": false}, {"key": "general_skin_health_header", "html": "<h2>General Skin Health</h2>", "type": "content", "input": false, "label": "Content"}, {"key": "treatment_goals", "type": "selectboxes", "input": true, "label": "What are your primary treatment goals for today's consultation? (Select all that apply)", "values": [{"label": "Reduce acne", "value": "reduce_acne"}, {"label": "Minimize fine lines and wrinkles", "value": "minimize_wrinkles"}, {"label": "Improve skin texture and smoothness", "value": "improve_texture"}, {"label": "Help with acne scarring", "value": "acne_scarring"}, {"label": "Reduce hyperpigmentation or dark spots", "value": "diminish_hyperpigmentation"}, {"label": "Reduce melasma", "value": "address_melasma"}, {"label": "Reduce rosacea or redness", "value": "reduce_rosacea"}, {"label": "<PERSON><PERSON><PERSON>", "value": "keratosis_pilaris"}, {"label": "Minimize enlarged pores", "value": "minimize_pores"}, {"label": "Enhance skin tone and radiance", "value": "enhance_tone"}, {"label": "Other", "value": "other"}], "tableView": true, "optionsLabelPosition": "right"}, {"key": "treatment_goals_other", "type": "textfield", "input": true, "label": "Please specify your other treatment goal", "tableView": true, "customConditional": "show = _.get(data, 'treatment_goals.other');"}, {"key": "prior_consultation_header", "html": "<h2>Prior Consultation</h2>", "type": "content", "input": false, "label": "Content", "customConditional": "show = _.some(_.values(data.treatment_goals));"}, {"key": "prior_consultation", "type": "radio", "input": true, "label": "Have you previously consulted a skin specialist for this concern?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "tableView": true, "customConditional": "show = _.some(_.values(data.treatment_goals));", "optionsLabelPosition": "right"}, {"key": "consultation_type", "type": "selectboxes", "input": true, "label": "What type of specialist did you consult? (Select all that apply)", "values": [{"label": "Medical dermatologist", "value": "medical_dermatologist"}, {"label": "Medi-spa or aesthetic clinic", "value": "medi_spa"}, {"label": "General practitioner", "value": "general_practitioner"}, {"label": "Plastic surgeon", "value": "plastic_surgeon"}, {"label": "Cosmetic nurse", "value": "cosmetic_nurse"}, {"label": "Other", "value": "other"}], "tableView": true, "customConditional": "show = data.prior_consultation === 'yes';", "optionsLabelPosition": "right"}, {"key": "prior_skin_dignosis_header", "html": "<h2>Other Medical Skin Conditions</h2>", "type": "content", "input": false, "label": "Content"}, {"key": "diagnosed_conditions", "type": "selectboxes", "input": true, "label": "Have you been diagnosed with any of the following skin conditions? (Select all that apply)", "values": [{"label": "<PERSON><PERSON><PERSON>", "value": "eczema"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "psoriasis"}, {"label": "Vitiligo", "value": "vitiligo"}], "tableView": true, "optionsLabelPosition": "right"}, {"key": "none_of_the_above_diagnosed_conditions", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a condition."}, "validate": {"custom": "valid = !!data.none_of_the_above_diagnosed_conditions || _.some(_.values(data.diagnosed_conditions));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "acne_history_heading", "html": "<h3>Acne History</h3>", "type": "content", "input": false, "label": "Acne History", "customConditional": "show = _.get(data.treatment_goals, 'reduce_acne', false) || _.get(data.treatment_goals, 'acne_scarring', false);"}, {"key": "acne_onset", "type": "textfield", "input": true, "label": "When did your acne first start?", "placeholder": "e.g., Teenage years, early 20s, etc.", "customConditional": "show = (_.get(data.treatment_goals, 'reduce_acne', false) || _.get(data.treatment_goals, 'acne_scarring', false));"}, {"key": "acne_triggers", "type": "selectboxes", "input": true, "label": "Have you noticed any triggers that worsen your acne? (Select all that apply)", "values": [{"label": "Hormonal changes (e.g., menstrual cycle, pregnancy)", "value": "hormonal_changes", "customConditional": "show = data.sex === 'female';"}, {"label": "Stress", "value": "stress"}, {"label": "Diet (e.g., dairy, sugar, high glycemic foods)", "value": "diet"}, {"label": "Cosmetic or skincare products (comedogenic)", "value": "cosmetic_products"}, {"label": "Certain medications (e.g., birth control, steroids)", "value": "medications"}, {"label": "Weather or humidity", "value": "weather"}, {"label": "Other", "value": "other"}], "tableView": true, "customConditional": "show = (_.get(data.treatment_goals, 'reduce_acne', false) || _.get(data.treatment_goals, 'acne_scarring', false)) && !!data.acne_onset;", "optionsLabelPosition": "right"}, {"key": "acne_triggers_other", "type": "textfield", "input": true, "label": "Please specify other triggers:", "tableView": true, "placeholder": "Describe other triggers here...", "customConditional": "show = _.get(data.acne_triggers, 'other', false);"}, {"key": "acne_severity_indicators", "type": "selectboxes", "input": true, "label": "Which of the following apply to your acne?", "values": [{"label": "Occasional pimples or whiteheads", "value": "occasional_breakouts"}, {"label": "Frequent breakouts affecting multiple areas", "value": "frequent_breakouts"}, {"label": "Cystic or painful acne lesions", "value": "cystic_acne"}, {"label": "Scarring or dark spots after breakouts", "value": "acne_scarring"}, {"label": "Inflamed or red lesions", "value": "inflamed_acne"}, {"label": "I don't get acne anymore", "value": "no_longer_acne"}], "tableView": true, "customConditional": "show = (_.get(data.treatment_goals, 'reduce_acne', false) || _.get(data.treatment_goals, 'acne_scarring', false)) && !!data.acne_triggers;", "optionsLabelPosition": "right"}, {"key": "rosacea_history_heading", "html": "<h3>Rosacea History</h3>", "type": "content", "input": false, "label": "<PERSON><PERSON> History", "customConditional": "show = _.get(data.treatment_goals, 'reduce_rosacea', false);"}, {"key": "rosacea_onset", "type": "textfield", "input": true, "label": "When did your rosacea symptoms first appear? (Onset)", "placeholder": "e.g., Early adulthood, after a specific event, etc.", "customConditional": "show = _.get(data.treatment_goals, 'reduce_rosacea', false);"}, {"key": "rosacea_triggers", "type": "selectboxes", "input": true, "label": "Have you noticed any triggers that worsen your rosacea? (Select all that apply)", "values": [{"label": "Heat or hot weather", "value": "heat"}, {"label": "Spicy foods", "value": "spicy_foods"}, {"label": "Alcohol", "value": "alcohol"}, {"label": "Stress", "value": "stress"}, {"label": "Skincare or cosmetic products", "value": "cosmetic_products"}, {"label": "Other", "value": "other"}], "tableView": true, "customConditional": "show = _.get(data.treatment_goals, 'reduce_rosacea', false);", "optionsLabelPosition": "right"}, {"key": "rosacea_triggers_other", "type": "textfield", "input": true, "label": "Please specify other triggers:", "tableView": true, "placeholder": "Describe other triggers here...", "customConditional": "show = _.get(data.rosacea_triggers, 'other', false);"}, {"key": "rosacea_indicators", "type": "selectboxes", "input": true, "label": "Which of the following describe your rosacea symptoms?", "values": [{"label": "Facial redness or flushing", "value": "facial_redness"}, {"label": "Visible blood vessels", "value": "visible_blood_vessels"}, {"label": "Acne-like breakouts", "value": "acne_like_breakouts"}, {"label": "Thickened or swollen skin", "value": "thickened_skin"}, {"label": "Burning or stinging sensations", "value": "burning_stinging"}], "tableView": true, "customConditional": "show = _.get(data.treatment_goals, 'reduce_rosacea', false);", "optionsLabelPosition": "right"}, {"key": "keratosis_pilaris_heading", "html": "<h3>Keratosis Pilaris History</h3>", "type": "content", "input": false, "label": "<PERSON><PERSON><PERSON> History", "customConditional": "show = _.get(data.treatment_goals, 'keratosis_pilaris', false);"}, {"key": "kp_onset", "type": "textfield", "input": true, "label": "When did you first notice symptoms of keratosis pilaris? (Onset)", "placeholder": "e.g., Childhood, teenage years, early adulthood, etc.", "customConditional": "show = _.get(data.treatment_goals, 'keratosis_pilaris', false);"}, {"key": "kp_symptom_indicators", "type": "selectboxes", "input": true, "label": "Which of the following describe your keratosis pilaris symptoms?", "values": [{"label": "Rough, dry patches", "value": "rough_patches"}, {"label": "Small, hard bumps", "value": "small_hard_bumps"}, {"label": "Redness or inflammation", "value": "redness_inflammation"}, {"label": "Itchiness or irritation", "value": "itchiness"}], "customConditional": "show = !!data.kp_onset;", "optionsLabelPosition": "right"}, {"key": "kp_treatment_history", "type": "selectboxes", "input": true, "label": "Have you tried any treatments for keratosis pilaris? (Select all that apply)", "values": [{"label": "Moisturizers with urea or lactic acid", "value": "urea_or_lactic_acid"}, {"label": "Exfoliating treatments (e.g., salicylic acid, glycolic acid)", "value": "exfoliating_treatments"}, {"label": "Topical retinoids (e.g., tretinoin, adapalene)", "value": "topical_retinoids"}, {"label": "Hydrating creams or lotions", "value": "hydrating_creams"}, {"label": "Laser therapy", "value": "laser_therapy"}, {"label": "Other", "value": "other"}], "tableView": true, "customConditional": "show = !!data.kp_symptom_indicators;", "optionsLabelPosition": "right"}, {"key": "kp_treatment_other", "type": "textfield", "input": true, "label": "Please specify other treatments you have tried:", "placeholder": "Describe additional treatments here...", "customConditional": "show = _.get(data.kp_treatment_history, 'other', false);"}, {"key": "hyperpigmentation_heading", "html": "<h3>Hyperpigmentation and Melasma History</h3>", "type": "content", "input": false, "label": "Hyperpigmentation and Melasma History", "customConditional": "show = _.get(data.treatment_goals, 'diminish_hyperpigmentation', false) || _.get(data.treatment_goals, 'address_melasma', false);"}, {"key": "hp_onset", "type": "textfield", "input": true, "label": "When did you first notice hyperpigmentation or melasma? (Onset)", "placeholder": "e.g., After pregnancy, sun exposure, etc.", "customConditional": "show = _.get(data.treatment_goals, 'diminish_hyperpigmentation', false) || _.get(data.treatment_goals, 'address_melasma', false);"}, {"key": "hp_triggers", "type": "selectboxes", "input": true, "label": "Have you identified any triggers for your hyperpigmentation or melasma? (Select all that apply)", "values": [{"label": "Sun exposure", "value": "sun_exposure"}, {"label": "Hormonal changes (e.g., pregnancy, birth control)", "value": "hormonal_changes"}, {"label": "Skin injuries or inflammation (post-inflammatory hyperpigmentation)", "value": "skin_injury"}, {"label": "Certain medications", "value": "medications"}, {"label": "Other", "value": "other"}], "customConditional": "show = _.get(data.treatment_goals, 'diminish_hyperpigmentation', false) || _.get(data.treatment_goals, 'address_melasma', false);", "optionsLabelPosition": "right"}, {"key": "hp_triggers_other", "type": "textfield", "input": true, "label": "Please specify other triggers:", "placeholder": "Describe other triggers here...", "customConditional": "show = _.get(data.hp_triggers, 'other', false);"}, {"key": "hp_affected_areas", "type": "selectboxes", "input": true, "label": "Which areas of your skin are affected? (Select all that apply)", "values": [{"label": "Face", "value": "face"}, {"label": "Neck", "value": "neck"}, {"label": "Chest", "value": "chest"}, {"label": "Arms or hands", "value": "arms_hands"}, {"label": "Other", "value": "other"}], "customConditional": "show = _.get(data.treatment_goals, 'diminish_hyperpigmentation', false) || _.get(data.treatment_goals, 'address_melasma', false);", "optionsLabelPosition": "right"}, {"key": "hp_affected_areas_other", "type": "textfield", "input": true, "label": "Please specify other affected areas:", "placeholder": "Describe other areas here...", "customConditional": "show = _.get(data.hp_affected_areas, 'other', false);"}, {"key": "hp_symptom_indicators", "type": "selectboxes", "input": true, "label": "Which of the following describe your hyperpigmentation or melasma?", "values": [{"label": "Small, localized patches", "value": "localized_patches"}, {"label": "Large, widespread areas", "value": "widespread_areas"}, {"label": "Brown or black discolouration", "value": "brown_black"}, {"label": "Red or pink discolouration", "value": "red_pink"}, {"label": "Blotchy or uneven skin tone", "value": "blotchy_uneven"}], "customConditional": "show = _.get(data.treatment_goals, 'diminish_hyperpigmentation', false) || _.get(data.treatment_goals, 'address_melasma', false);", "optionsLabelPosition": "right"}, {"key": "hp_treatment_history", "type": "textfield", "input": true, "label": "Have you used any treatments for hyperpigmentation or melasma? (e.g., creams, chemical peels, laser therapy)", "placeholder": "List previous treatments here...", "customConditional": "show = _.get(data.treatment_goals, 'diminish_hyperpigmentation', false) || _.get(data.treatment_goals, 'address_melasma', false);"}, {"key": "keloids_and_healing_header", "html": "<h2>How Your Skin Heals</h2><p>Keloids are thick, raised scars that grow larger than the original injury and can be itchy or uncomfortable.</p>", "type": "content", "input": false, "label": "Content"}, {"key": "abnormal_scarring", "type": "radio", "input": true, "label": "Have you had surgery, tattoos, or piercings that resulted in abnormal scarring?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No, I haven't noticed", "value": "no_noticed"}, {"label": "No, I haven't had surgery, tattoos, or piercings to notice", "value": "no_none"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "avoid_procedures_scarring", "type": "radio", "input": true, "label": "Do you avoid certain procedures due to concerns about scarring?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = !!data.abnormal_scarring;", "optionsLabelPosition": "right"}, {"key": "keloids_after_injuries", "type": "radio", "input": true, "label": "Have you experienced thickened or raised scars (keloids) after minor injuries such as cuts or burns?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = !!data.avoid_procedures_scarring;", "optionsLabelPosition": "right"}, {"key": "family_history_keloids", "type": "radio", "input": true, "label": "Do you have a family history of keloid formation or hypertrophic scarring?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "not_sure"}], "tableView": true, "customConditional": "show = !!data.keloids_after_injuries;", "optionsLabelPosition": "right"}, {"key": "biopsy_or_surgical_scarring", "type": "radio", "input": true, "label": "Have you had a skin biopsy or surgical excision that healed poorly or resulted in unusual scarring?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = !!data.family_history_keloids;", "optionsLabelPosition": "right"}, {"key": "treatment_for_keloids", "type": "selectboxes", "input": true, "label": "Have you sought medical treatment for keloids or abnormal scars in the past?", "values": [{"label": "Silicone gels", "value": "silicone_gels"}, {"label": "Steroid injections", "value": "steroid_injections"}, {"label": "Laser therapy", "value": "laser_therapy"}, {"label": "None of the above", "value": "none"}], "tableView": true, "customConditional": "show = !!data.biopsy_or_surgical_scarring;", "optionsLabelPosition": "right"}, {"key": "scar_colour_change", "type": "radio", "input": true, "label": "Do you have scars that are darker or lighter than your normal skin tone?", "values": [{"label": "Darker", "value": "darker"}, {"label": "Lighter", "value": "lighter"}, {"label": "No noticeable change", "value": "no_change"}], "tableView": true, "customConditional": "show = _.some(_.values(data.treatment_for_keloids));", "optionsLabelPosition": "right"}]}