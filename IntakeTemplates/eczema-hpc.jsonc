{"components": [{"key": "heading_intro_title", "html": "<h1><strong>Skin Rash Intake - Your Current Concerns</strong></h1><p>Please answer a few quick questions about your rash so we can confirm the cause and recommend the best care.</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "heading_reason_consult", "html": "</br><h4>Why You’re Reaching Out Today</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "consult_reason", "type": "selectboxes", "input": true, "label": "I would like help with:", "inline": false, "values": [{"label": "Renewal of a previous prescription", "value": "renewal"}, {"label": "Treatment for my current flare", "value": "current_flare"}, {"label": "Preventive maintenance advice", "value": "maintenance"}, {"label": "Clarification of my diagnosis", "value": "diagnostic_clarity"}, {"label": "Managing side-effects of treatment", "value": "side_effects"}, {"label": "Possible skin infection", "value": "infection_concern"}, {"label": "Stronger medication options", "value": "stronger_medication"}, {"label": "Other", "value": "other"}, {"label": "None of the above", "value": "none", "customClass": "mt-n3"}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "optionsLabelPosition": "right", "confirm_label": "Reasons for consultation:"}, {"key": "consult_reason_other", "type": "textarea", "input": true, "label": "Please tell us more:", "tableView": true, "autoExpand": false, "customConditional": "show = data.consult_reason && (data.consult_reason.other === true || data.consult_reason.none === true);", "confirm_label": "Other reason:"}, {"key": "heading_diagnosis_status", "html": "</br><h4>Have You Been Diagnosed?</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "skin_diagnosis_known", "type": "radio", "input": true, "label": "Has a healthcare provider ever told you what your rash is?", "inline": false, "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "I’m not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "confirm_label": "Told a diagnosis:"}, {"key": "skin_diagnosis_type", "type": "select", "input": true, "label": "Which diagnosis were you given?", "widget": "html5", "data": {"values": [{"label": "Eczema (atopic dermatitis)", "value": "eczema"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "psoriasis"}, {"label": "Contact dermatitis", "value": "contact_dermatitis"}, {"label": "Seborrhoeic dermatitis", "value": "seborrhoeic"}, {"label": "Other skin condition", "value": "other"}]}, "validate": {"required": true}, "tableView": true, "customConditional": "show = data.skin_diagnosis_known === 'yes';", "confirm_label": "Diagnosis given:"}, {"key": "skin_diagnosis_type_other", "type": "textarea", "input": true, "label": "Please specify the diagnosis:", "tableView": true, "autoExpand": false, "customConditional": "show = data.skin_diagnosis_type === 'other';", "confirm_label": "Other diagnosis:"}, {"key": "diagnosing_provider", "type": "radio", "input": true, "label": "Who provided this diagnosis?", "inline": false, "values": [{"label": "Family doctor", "value": "family_doctor"}, {"label": "Skin specialist (dermatologist)", "value": "dermatologist"}, {"label": "Nurse practitioner", "value": "nurse_practitioner"}, {"label": "<PERSON><PERSON><PERSON>", "value": "naturopath"}, {"label": "Self-diagnosis", "value": "self"}, {"label": "Other", "value": "other"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.skin_diagnosis_known === 'yes';", "optionsLabelPosition": "right", "confirm_label": "Diagnosing provider:"}, {"key": "diagnosing_provider_other", "type": "textarea", "input": true, "label": "Please specify who diagnosed you:", "tableView": true, "autoExpand": false, "customConditional": "show = data.diagnosing_provider === 'other';", "confirm_label": "Other provider:"}, {"key": "heading_symptom_onset_course", "html": "</br><h4>Current Rash Details</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "rash_start_time", "type": "select", "input": true, "label": "When did this rash (or current flare) begin?", "widget": "html5", "data": {"values": [{"label": "< 2 days ago", "value": "duration_days_lt2"}, {"label": "3-7 days ago", "value": "duration_days_3_7"}, {"label": "1-2 weeks ago", "value": "duration_weeks_1_2"}, {"label": "2-4 weeks ago", "value": "duration_weeks_2_4"}, {"label": "1-3 months ago", "value": "duration_months_1_3"}, {"label": "> 3 months ago", "value": "duration_months_gt3"}, {"label": "I’m not sure", "value": "duration_unknown"}]}, "validate": {"required": true}, "tableView": true, "confirm_label": "<PERSON><PERSON> started:"}, {"key": "rash_first_or_repeat", "type": "radio", "input": true, "label": "Is this your first rash of this kind, or have you had similar rashes before?", "inline": false, "values": [{"label": "First time", "value": "first"}, {"label": "I’ve had similar rashes before", "value": "repeat"}, {"label": "Not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "confirm_label": "First or repeat:", "customConditional": "show = !!data.rash_start_time;"}, {"key": "rash_onset_speed", "type": "radio", "input": true, "label": "Did the rash appear suddenly or build up gradually?", "inline": false, "values": [{"label": "<PERSON><PERSON>", "value": "sudden"}, {"label": "Gradual", "value": "gradual"}, {"label": "Unsure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "confirm_label": "Onset speed:", "customConditional": "show = !!data.rash_first_or_repeat;"}, {"key": "rash_course_since_onset", "type": "select", "input": true, "label": "Since it began, has your rash mostly:", "widget": "html5", "data": {"values": [{"label": "Been getting better", "value": "better"}, {"label": "Been getting worse", "value": "worse"}, {"label": "Gone up and down", "value": "fluctuating"}, {"label": "Stayed about the same", "value": "same"}]}, "validate": {"required": true}, "tableView": true, "confirm_label": "Course since onset:", "customConditional": "show = !!data.rash_onset_speed;"}, {"key": "rash_frequency_year", "type": "select", "input": true, "label": "In a typical year, how many times do you get this kind of rash?", "widget": "html5", "data": {"values": [{"label": "< 1 time", "value": "freq_year_lt1"}, {"label": "1-2 times", "value": "freq_year_1_2"}, {"label": "3-6 times", "value": "freq_year_3_6"}, {"label": "Monthly", "value": "freq_monthly"}, {"label": "Almost all the time", "value": "freq_constant"}, {"label": "I don’t know", "value": "freq_unknown"}]}, "validate": {"required": true}, "tableView": true, "confirm_label": "Frequency per year:", "customConditional": "show = !!data.rash_course_since_onset;"}, {"key": "rash_duration_typical", "type": "select", "input": true, "label": "When you get a rash flare, how long does it usually last?", "widget": "html5", "data": {"values": [{"label": "A few days", "value": "few_days"}, {"label": "About 1 week", "value": "week_1"}, {"label": "2–4 weeks", "value": "weeks_2_4"}, {"label": "1–3 months", "value": "months_1_3"}, {"label": "More than 3 months", "value": "months_gt3"}, {"label": "I'm not sure", "value": "unknown"}]}, "validate": {"required": true}, "tableView": true, "customConditional": "show = data.rash_first_or_repeat === 'repeat' && !!data.rash_frequency_year;", "confirm_label": "Typical flare duration:"}, {"key": "flare_treatment_dependency", "type": "radio", "input": true, "label": "What best describes how your flares usually resolve?", "inline": false, "values": [{"label": "They only improve with prescription treatment", "value": "requires_prescription"}, {"label": "They improve faster with prescription treatment", "value": "faster_with_prescription"}, {"label": "They sometimes improve on their own", "value": "sometimes_spontaneous"}, {"label": "They usually improve on their own, even without meds", "value": "usually_spontaneous"}, {"label": "I'm not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = data.rash_duration_typical && data.rash_duration_typical !== 'unknown';", "confirm_label": "Flare resolution pattern:"}, {"key": "non_prescription_helpful", "type": "radio", "input": true, "label": "Do over-the-counter or non-prescription products help your flares?", "inline": false, "values": [{"label": "Yes – they usually help", "value": "yes_often"}, {"label": "Sometimes – depends on the product", "value": "sometimes"}, {"label": "No – they don't seem to help", "value": "no_effect"}, {"label": "I haven't really tried", "value": "not_tried"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = data.flare_treatment_dependency && data.flare_treatment_dependency !== 'unsure';", "confirm_label": "Non-prescription product effect:"}, {"key": "heading_severity_distribution", "html": "</br><h4>Severity &amp; Body Areas</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "itch_severity", "type": "select", "input": true, "label": "How itchy is the rash right now?", "widget": "html5", "data": {"values": [{"label": "Mild", "value": "itch_mild"}, {"label": "Moderate", "value": "itch_moderate"}, {"label": "Severe", "value": "itch_severe"}, {"label": "Very severe", "value": "itch_very_severe"}]}, "validate": {"required": true}, "tableView": true, "confirm_label": "Itch severity:"}, {"key": "visible_skin_changes", "type": "selectboxes", "input": true, "label": "Which skin changes do you see right now? (Select all that apply)", "inline": false, "values": [{"label": "Redness", "value": "redness"}, {"label": "Scaling / flaking", "value": "scaling"}, {"label": "Oozing or weeping", "value": "oozing"}, {"label": "Crusting", "value": "crusting"}, {"label": "Thickened skin", "value": "thickened"}, {"label": "Colour change", "value": "colour_change"}, {"label": "Painful cracks", "value": "cracks"}, {"label": "Signs of infection", "value": "infection"}, {"label": "Other", "value": "other"}, {"label": "None of the above", "value": "none", "customClass": "mt-n3"}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "optionsLabelPosition": "right", "confirm_label": "Visible changes:", "customConditional": "show = !!data.itch_severity;"}, {"key": "visible_skin_changes_other", "type": "textarea", "input": true, "label": "Please describe other skin changes:", "tableView": true, "autoExpand": false, "customConditional": "show = data.visible_skin_changes && data.visible_skin_changes.other;", "confirm_label": "Other skin changes:"}, {"key": "heading_rash_area_face_neck", "type": "content", "input": false, "label": "Content", "html": "</br><h4>Face, Eyelids, and Neck</h4>", "tableView": false, "refreshOnChange": false}, {"key": "rash_site_face_group", "type": "selectboxes", "input": true, "label": "Is the rash currently affecting any of the following areas?", "values": [{"label": "Face (cheeks, forehead, jaw)", "value": "face"}, {"label": "Eyelids", "value": "eyelids"}, {"label": "Neck", "value": "neck"}], "confirm_label": "Face / neck areas:", "tableView": true, "optionsLabelPosition": "right"}, {"key": "none_of_the_above_rash_site_face_group", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "Select at least one option or choose 'None of the above'."}, "validate": {"custom": "valid = !!data.none_of_the_above_rash_site_face_group || _.some(_.values(data.rash_site_face_group));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "rash_face_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following face/neck rash sites:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "Face/neck areas not affected:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.rash_site_face_group, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "heading_rash_area_folds", "type": "content", "input": false, "label": "Content", "html": "</br><h4>Body Folds and Creases</h4>", "tableView": false, "refreshOnChange": false}, {"key": "rash_site_flexures", "type": "selectboxes", "input": true, "label": "Is the rash currently affecting any skin fold or creases?", "values": [{"label": "Elbow creases", "value": "elbow_flexures"}, {"label": "Behind the knees", "value": "knee_flexures"}, {"label": "Armpits", "value": "axillae"}, {"label": "<PERSON><PERSON><PERSON>", "value": "groin"}, {"label": "Under the breasts", "value": "inframammary"}], "confirm_label": "Flexural areas affected:", "tableView": true, "optionsLabelPosition": "right"}, {"key": "none_of_the_above_rash_site_flexures", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "Select at least one option or choose 'None of the above'."}, "validate": {"custom": "valid = !!data.none_of_the_above_rash_site_flexures || _.some(_.values(data.rash_site_flexures));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "rash_flexures_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following flexural sites affected:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "Flexural areas not affected:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.rash_site_flexures, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "heading_rash_area_palms_soles", "type": "content", "input": false, "label": "Content", "html": "</br><h4>Palms and Soles</h4>", "tableView": false, "refreshOnChange": false}, {"key": "rash_site_palms_soles", "type": "selectboxes", "input": true, "label": "Is the rash currently affecting the palms or soles?", "values": [{"label": "Palms (thick skin)", "value": "palms"}, {"label": "Soles (bottom of feet)", "value": "soles"}], "confirm_label": "Palms / soles affected:", "tableView": true, "optionsLabelPosition": "right"}, {"key": "none_of_the_above_rash_site_palms_soles", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "Select at least one option or choose 'None of the above'."}, "validate": {"custom": "valid = !!data.none_of_the_above_rash_site_palms_soles || _.some(_.values(data.rash_site_palms_soles));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "rash_palms_soles_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following palm/sole sites affected:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "Palms/soles not affected:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.rash_site_palms_soles, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "heading_rash_area_dorsal", "type": "content", "input": false, "label": "Content", "html": "</br><h4>Backs of Hands and Top of the Feet</h4>", "tableView": false, "refreshOnChange": false}, {"key": "rash_site_dorsal", "type": "selectboxes", "input": true, "label": "Is the rash currently on the back of your hands or top of feet?", "values": [{"label": "Back of hands (knuckles)", "value": "dorsum_hands"}, {"label": "Top of the feet", "value": "dorsum_feet"}], "confirm_label": "Dorsal hands/feet affected:", "tableView": true, "optionsLabelPosition": "right"}, {"key": "none_of_the_above_rash_site_dorsal", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "Select at least one option or choose 'None of the above'."}, "validate": {"custom": "valid = !!data.none_of_the_above_rash_site_dorsal || _.some(_.values(data.rash_site_dorsal));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "rash_dorsal_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following dorsal areas affected:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "Dorsal areas not affected:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.rash_site_dorsal, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "current_rash_distribution_other", "type": "textarea", "input": true, "label": "Please specify other area(s):", "tableView": true, "autoExpand": false, "customConditional": "show = data.current_rash_distribution && data.current_rash_distribution.other;", "confirm_label": "Other current areas:"}, {"key": "heading_triggers_exacerbating_factors", "html": "</br><h4>Triggers &amp; Flare Factors</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "environmental_triggers", "type": "selectboxes", "input": true, "label": "Which environmental factors seem to trigger or worsen your rash? (Select all that apply)", "inline": false, "values": [{"label": "Dry air", "value": "dry_air"}, {"label": "Cold weather", "value": "cold_weather"}, {"label": "Heat or sweating", "value": "heat_sweat"}, {"label": "Low indoor humidity", "value": "low_humidity"}, {"label": "Dust mites", "value": "dust_mites"}, {"label": "<PERSON><PERSON>", "value": "pollens"}, {"label": "Pet dander", "value": "pet_dander"}, {"label": "<PERSON><PERSON>", "value": "mould"}, {"label": "Wool fabrics", "value": "wool"}, {"label": "Fragranced products", "value": "fragrances"}, {"label": "Chlorine pools", "value": "chlorine"}, {"label": "Other", "value": "other"}, {"label": "None of the above", "value": "none", "customClass": "mt-n3"}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "optionsLabelPosition": "right", "confirm_label": "Environmental triggers:"}, {"key": "environmental_triggers_other", "type": "textarea", "input": true, "label": "Please specify other environmental triggers:", "tableView": true, "autoExpand": false, "customConditional": "show = data.environmental_triggers && data.environmental_triggers.other;", "confirm_label": "Other environmental triggers:"}, {"key": "lifestyle_triggers", "type": "selectboxes", "input": true, "label": "Which lifestyle factors make the rash worse? (Select all that apply)", "inline": false, "values": [{"label": "Long, hot showers", "value": "hot_showers"}, {"label": "Harsh soaps or detergents", "value": "harsh_soaps"}, {"label": "Fragranced laundry products", "value": "fragrance_detergents"}, {"label": "Alcohol use", "value": "alcohol"}, {"label": "Stress", "value": "stress"}, {"label": "Poor sleep", "value": "poor_sleep"}, {"label": "Certain foods", "value": "diet"}, {"label": "Other", "value": "other"}, {"label": "None of the above", "value": "none", "customClass": "mt-n3"}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "optionsLabelPosition": "right", "confirm_label": "Lifestyle triggers:", "customConditional": "show = data.environmental_triggers && Object.values(data.environmental_triggers).some(<PERSON><PERSON><PERSON>);"}, {"key": "lifestyle_triggers_other", "type": "textarea", "input": true, "label": "Please specify other lifestyle triggers:", "tableView": true, "autoExpand": false, "customConditional": "show = data.lifestyle_triggers && data.lifestyle_triggers.other;", "confirm_label": "Other lifestyle triggers:"}, {"key": "allergen_triggers", "type": "selectboxes", "input": true, "label": "Have confirmed or suspected allergies triggered your rash? (Select all that apply)", "inline": false, "values": [{"label": "Food allergy (confirmed)", "value": "food_confirmed"}, {"label": "Food allergy (suspected)", "value": "food_suspected"}, {"label": "Contact allergy (nickel, latex)", "value": "contact_allergy"}, {"label": "Preservatives / fragrances", "value": "chemical_allergy"}, {"label": "Aero-allergens (pollen, dust)", "value": "aero_allergen"}, {"label": "Other", "value": "other"}, {"label": "None of the above", "value": "none", "customClass": "mt-n3"}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "optionsLabelPosition": "right", "confirm_label": "Allergy triggers:", "customConditional": "show = data.lifestyle_triggers && Object.values(data.lifestyle_triggers).some(<PERSON><PERSON><PERSON>);"}, {"key": "allergen_triggers_other", "type": "textarea", "input": true, "label": "Please specify other allergy-related triggers:", "tableView": true, "autoExpand": false, "customConditional": "show = data.allergen_triggers && data.allergen_triggers.other;", "confirm_label": "Other allergy triggers:"}, {"key": "trigger_avoidance_attempted", "type": "radio", "input": true, "label": "Have you tried avoiding any of the triggers you selected?", "inline": false, "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Partially", "value": "partially"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "confirm_label": "Avoidance attempted:", "customConditional": "show = data.allergen_triggers && Object.values(data.allergen_triggers).some(<PERSON><PERSON><PERSON>);"}, {"key": "trigger_avoidance_effect", "type": "select", "input": true, "label": "If you tried avoidance, what effect did it have?", "widget": "html5", "data": {"values": [{"label": "No change", "value": "no_change"}, {"label": "Slight improvement", "value": "slight_improvement"}, {"label": "Marked improvement", "value": "marked_improvement"}]}, "validate": {"required": true}, "tableView": true, "customConditional": "show = data.trigger_avoidance_attempted === 'yes' || data.trigger_avoidance_attempted === 'partially';", "confirm_label": "Avoidance effect:"}, {"key": "heading_diagnostic_tests", "html": "</br><h4>Tests &amp; Previous Checks</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "tests_done", "type": "radio", "input": true, "label": "Have you had any tests or investigations for this rash?", "inline": false, "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "confirm_label": "Tests done:"}, {"key": "tests_types", "type": "selectboxes", "input": true, "label": "Which tests have you had? (Select all that apply)", "inline": false, "values": [{"label": "Skin biopsy", "value": "biopsy"}, {"label": "Patch testing (contact allergy)", "value": "patch"}, {"label": "Skin-prick allergy test", "value": "skin_prick"}, {"label": "Blood IgE test", "value": "blood_ige"}, {"label": "Blood test for celiac disease", "value": "celiac_test"}, {"label": "Other", "value": "other"}, {"label": "None of the above", "value": "none", "customClass": "mt-n3"}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "optionsLabelPosition": "right", "confirm_label": "Tests performed:", "customConditional": "show = data.tests_done === 'yes';"}, {"key": "tests_types_other", "type": "textarea", "input": true, "label": "Please specify other test(s):", "tableView": true, "autoExpand": false, "customConditional": "show = data.tests_types && data.tests_types.other;", "confirm_label": "Other tests:"}, {"key": "coeliac_test_result", "type": "select", "input": true, "label": "What was the result of the celiac disease test?", "widget": "html5", "data": {"values": [{"label": "Positive", "value": "positive"}, {"label": "Negative", "value": "negative"}, {"label": "Inconclusive", "value": "inconclusive"}, {"label": "Awaiting results", "value": "awaiting"}]}, "validate": {"required": true}, "tableView": true, "customConditional": "show = data.tests_types && data.tests_types.celiac_test;", "confirm_label": "celiac test result:"}, {"key": "heading_otc_skin_care_routine", "html": "</br><h4>Everyday Skin Care &amp; Non-Prescription Products</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "uses_moisturizer", "type": "radio", "input": true, "label": "Do you use a moisturizer (emollient) on your skin?", "inline": false, "values": [{"label": "Yes, daily", "value": "daily"}, {"label": "Yes, but not every day", "value": "sometimes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "confirm_label": "Moisturizer use:"}, {"key": "moisturizer_type", "type": "select", "input": true, "label": "What kind of moisturizer do you usually use?", "data": {"values": [{"label": "Thick ointment (e.g., petroleum jelly, CeraVe Healing Ointment)", "value": "ointment"}, {"label": "Cream (e.g., CeraVe, Glaxal Base)", "value": "cream"}, {"label": "Lotion (thin/runny texture)", "value": "lotion"}, {"label": "Not sure", "value": "unsure"}]}, "validate": {"required": true}, "tableView": true, "widget": "html5", "customConditional": "show = data.uses_moisturizer === 'daily' || data.uses_moisturizer === 'sometimes';", "confirm_label": "Moisturizer type:"}, {"key": "applies_after_bathing", "type": "radio", "input": true, "label": "Do you usually apply moisturizer right after bathing or showering?", "inline": false, "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.moisturizer_type;", "optionsLabelPosition": "right", "confirm_label": "Applies after bathing:"}, {"key": "uses_bath_oil", "type": "radio", "input": true, "label": "Do you use bath oil or a non-soap cleanser (e.g., CeraVe wash, Spectro Jel)?", "inline": false, "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.applies_after_bathing;", "optionsLabelPosition": "right", "confirm_label": "Uses non-soap cleanser:"}, {"key": "daily_showers", "type": "radio", "input": true, "label": "Do you usually shower or bathe every day?", "inline": false, "values": [{"label": "Yes", "value": "yes"}, {"label": "Every other day or less", "value": "less_often"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.uses_bath_oil;", "optionsLabelPosition": "right", "confirm_label": "Bathing frequency:"}, {"key": "hot_water_use", "type": "radio", "input": true, "label": "Do you usually use hot water when showering or bathing?", "inline": false, "values": [{"label": "Yes - hot water", "value": "hot"}, {"label": "Warm or lukewarm water", "value": "warm"}, {"label": "Cold water", "value": "cold"}, {"label": "Not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.daily_showers;", "optionsLabelPosition": "right", "confirm_label": "Water temperature:"}, {"key": "photo_upload_header", "html": "</br><h2>Photo Upload</h2><p>Please upload a clear photo of the affected area(s). This can help us better understand the cause and suggest the right treatment. Good lighting is helpful.</p>", "type": "content", "input": false, "label": "Content"}, {"key": "uploadUrl", "url": "/app/q/{qpk}/formio-files/{pk}/", "type": "file", "image": true, "input": true, "label": "Upload: URL", "webcam": true, "capture": "user", "storage": "url", "multiple": true, "fileTypes": [{"label": "", "value": ""}], "tableView": true, "validateWhenHidden": false}]}