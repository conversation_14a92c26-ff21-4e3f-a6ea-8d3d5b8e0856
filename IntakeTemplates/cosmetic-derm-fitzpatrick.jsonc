{"_vid": 0, "name": "fitzpatrickSkinTypeTool", "path": "fitzpatrickskintypetool", "type": "form", "title": "<PERSON><PERSON><PERSON> Skin Type Quiz", "display": "form", "components": [{"key": "fitzpatrickQuiz", "type": "panel", "label": "<PERSON><PERSON><PERSON> Skin Type Quiz", "title": "<PERSON><PERSON><PERSON> Skin Type Quiz", "collapsible": false, "components": [{"key": "eyeColor", "type": "radio", "tableView": true, "input": true, "label": "What color are your eyes?", "validate": {"required": true}, "values": [{"label": "Light blue, gray, green", "value": 0}, {"label": "Blue, gray, or green", "value": 1}, {"label": "Blue", "value": 2}, {"label": "Dark brown", "value": 3}, {"label": "Brownish black", "value": 4}]}, {"key": "hairColor", "tableView": true, "type": "radio", "input": true, "validate": {"required": true}, "label": "What is your natural hair color?", "values": [{"label": "<PERSON> red", "value": 0}, {"label": "Blonde", "value": 1}, {"label": "Chestnut/Dark blonde", "value": 2}, {"label": "Dark brown", "value": 3}, {"label": "Black", "value": 4}]}, {"key": "skinColor", "tableView": true, "type": "radio", "validate": {"required": true}, "input": true, "label": "What is your skin color (unexposed areas)?", "values": [{"label": "Reddish", "value": 0}, {"label": "Very pale", "value": 1}, {"label": "Pale with a beige tint", "value": 2}, {"label": "Light brown", "value": 3}, {"label": "Dark brown", "value": 4}]}, {"key": "freckles", "tableView": true, "type": "radio", "validate": {"required": true}, "input": true, "label": "Do you have freckles on unexposed areas?", "values": [{"label": "Many", "value": 0}, {"label": "Several", "value": 1}, {"label": "Few", "value": 2}, {"label": "Very Few", "value": 3}, {"label": "None", "value": 4}]}, {"key": "sunReaction", "tableView": true, "type": "radio", "validate": {"required": true}, "input": true, "label": "What happens when you stay too long in the sun?", "values": [{"label": "Painful redness, blistering, peeling", "value": 0}, {"label": "Blistering followed by peeling", "value": 1}, {"label": "Burns sometimes followed by peeling", "value": 2}, {"label": "Rare burns", "value": 3}, {"label": "Never had burns", "value": 4}]}, {"key": "tanDegree", "tableView": true, "type": "radio", "validate": {"required": true}, "input": true, "label": "To what degree do you turn brown?", "values": [{"label": "Hardly or not at all", "value": 0}, {"label": "Light color tan", "value": 1}, {"label": "Reasonable tan", "value": 2}, {"label": "Tan very easily", "value": 3}, {"label": "Turn dark brown quickly", "value": 4}]}, {"key": "brownAfterSun", "tableView": true, "type": "radio", "validate": {"required": true}, "input": true, "label": "Do you turn brown after several hours of sun exposure?", "values": [{"label": "Never", "value": 0}, {"label": "Seldom", "value": 1}, {"label": "Sometimes", "value": 2}, {"label": "Often", "value": 3}, {"label": "Always", "value": 4}]}, {"key": "faceReaction", "tableView": true, "type": "radio", "validate": {"required": true}, "input": true, "label": "How does your face react to the sun?", "values": [{"label": "Very sensitive", "value": 0}, {"label": "Sensitive", "value": 1}, {"label": "Normal", "value": 2}, {"label": "Very resistant", "value": 3}, {"label": "Never had a problem", "value": 4}]}, {"key": "lastSunExposure", "tableView": true, "type": "radio", "validate": {"required": true}, "input": true, "label": "When did you last expose your body to the sun (or artificial sunlamp/tanning cream)?", "values": [{"label": "More than 3 months ago", "value": 0}, {"label": "2-3 months ago", "value": 1}, {"label": "1-2 months ago", "value": 2}, {"label": "Less than a month ago", "value": 3}, {"label": "Less than 2 weeks ago", "value": 4}]}, {"key": "faceExposure", "tableView": true, "type": "radio", "validate": {"required": true}, "input": true, "label": "Do you expose your face to the sun?", "values": [{"label": "Never", "value": 0}, {"label": "Hardly ever", "value": 1}, {"label": "Sometimes", "value": 2}, {"label": "Often", "value": 3}, {"label": "Always", "value": 4}]}, {"key": "totalScore", "tableView": true, "type": "textfield", "input": true, "label": "Total Fitzpatrick Score", "disabled": true, "calculateValue": {"+": [{"var": "data.eyeColor"}, {"var": "data.hairColor"}, {"var": "data.skinColor"}, {"var": "data.freckles"}, {"var": "data.sunReaction"}, {"var": "data.tanDegree"}, {"var": "data.brownAfterSun"}, {"var": "data.faceReaction"}, {"var": "data.lastSunExposure"}, {"var": "data.faceExposure"}]}}, {"key": "skinType", "tableView": true, "type": "textfield", "input": true, "label": "<PERSON><PERSON>patrick Skin Type", "disabled": true, "calculateValue": {"if": [{"<=": [{"var": "data.totalScore"}, 7]}, "Type I", {"if": [{"<=": [{"var": "data.totalScore"}, 16]}, "Type II", {"if": [{"<=": [{"var": "data.totalScore"}, 25]}, "Type III", {"if": [{"<=": [{"var": "data.totalScore"}, 30]}, "Type IV", "Type V-VI"]}]}]}]}}]}]}