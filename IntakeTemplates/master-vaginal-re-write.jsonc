{"components": [{"key": "heading_vaginal_symptoms", "html": "<h2 class='text-center'>Vaginal Symptoms</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show =  data.sex == 'female'"}, {"key": "changes_vaginal", "type": "radio", "input": true, "label": "Have you noticed any of the following vaginal symptoms?<ul><li>Itchiness, dryness, discomfort, or unusual discharge</li><li>Odour, skin changes (e.g. rash, swelling), or a lump near the vagina</li><li>Bleeding after sex, spotting between cycles, or any other unusual symptoms</li></ul>", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.sex == 'female'", "optionsLabelPosition": "right"}, {"key": "vaginal_symptom_overview", "type": "selectboxes", "input": true, "label": "Are you currently experiencing any of the following symptoms?", "values": [{"label": "Vaginal Itchiness", "value": "vaginal_itchiness"}, {"label": "Vaginal Discharge", "value": "vaginal_discharge"}, {"label": "Vaginal Odour", "value": "vaginal_odour"}, {"label": "Vaginal Dryness", "value": "vaginal_dryness"}, {"label": "Bleeding after sex", "value": "bleeding_after_sex"}, {"label": "Spotting between my cycles unrelated to sex", "value": "mid_cycle_spotting"}, {"label": "Lump around my vagina", "value": "vaginal_lump"}, {"label": "Skin changes around the vagina (e.g. rash, irritation, swelling)", "value": "vaginal_skin_changes"}, {"label": "Other vaginal symptoms", "value": "other"}], "adminFlag": true, "inputType": "checkbox", "tableView": true, "customConditional": "show = data.sex === 'female' && data.changes_vaginal", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_vaginal_symptom_overview", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "Please select at least one symptom or check 'None of the above.'"}, "validate": {"custom": "valid = !!data.none_of_the_above_vaginal_symptom_overview || _.some(_.values(data.vaginal_symptom_overview));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.sex === 'female' && data.changes_vaginal"}, {"key": "vaginal_symptoms_present", "type": "textfield", "input": true, "label": "Patient indicated they have the following vaginal symptoms:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "You have the following vaginal symptoms:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.vaginal_symptom_overview)), _.capitalize), ', '), /_/g, ' ');"}, {"key": "vaginal_symptoms_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following vaginal symptoms:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "You DO NOT have the following vaginal symptoms:", "calculateValue": "value = _.replace(_.join(_.map(_.difference(_.keys(_.pickBy(data.vaginal_symptom_overview, _.negate(_.identity))),['other']), _.capitalize), ', '), /_/g, ' ');"}, {"key": "stated_other_symptoms_vaginal", "type": "textarea", "input": true, "label": "Please use this area to describe your other vaginal symptoms not described above.<br>Please leave this section blank if you are asymptomatic or have no symptoms.", "adminFlag": true, "tableView": true, "autoExpand": false, "placeholder": "No symptoms", "customConditional": "show = data.vaginal_symptom_overview?.other===true"}, {"key": "vaginal_symptom_contradiction_warning", "html": "<div style='border-left: 4px solid #dc3545; background-color: #f8d7da; padding: 10px; margin-bottom: 10px;'><strong>Notice:</strong> You indicated that you have vaginal symptoms, but then selected \"None of the above.\" Please review your answers above and make sure they reflect your current symptoms accurately.</div>", "type": "content", "input": false, "customConditional": "show = data.changes_vaginal === 'yes' && data.none_of_the_above_vaginal_symptom_overview === true"}, {"key": "vaginal_symptom_triggers", "type": "selectboxes", "input": true, "label": "Did these symptoms start after any of the following triggers? Please select all that apply:", "values": [{"label": "Shaving or waxing", "value": "shaving_waxing", "shortcut": ""}, {"label": "New lubricant during intercourse", "value": "new_lubricant", "shortcut": ""}, {"label": "Use of a sex toy", "value": "sex_toy", "shortcut": ""}, {"label": "New hygiene product (e.g., vaginal soap)", "value": "new_hygiene_product", "shortcut": ""}, {"label": "Douching", "value": "douching", "shortcut": ""}, {"label": "Using a new condom brand", "value": "new_condom_brand", "shortcut": ""}, {"label": "New brand of tampon or liner", "value": "new_tampon_liner", "shortcut": ""}, {"label": "New sexual partner", "value": "new_sexual_partner", "shortcut": ""}, {"label": "Particular fabric or underwear", "value": "fabric", "shortcut": ""}], "adminFlag": true, "inputType": "checkbox", "tableView": true, "customConditional": "show = data.sex === 'female' && data.vaginal_symptom_overview && _.some(_.values(data.vaginal_symptom_overview)) && !data.none_of_the_above_vaginal_symptom_overview", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_vaginal_symptom_triggers", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a trigger."}, "validate": {"custom": "valid = !!data.none_of_the_above_vaginal_symptom_triggers || _.some(_.values(data.vaginal_symptom_triggers));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.sex === 'female' && data.vaginal_symptom_overview && _.some(_.values(data.vaginal_symptom_overview)) && !data.none_of_the_above_vaginal_symptom_overview"}, {"key": "vaginal_symptom_triggers_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following vaginal symptom triggers:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "I DO NOT have the following vaginal symptom triggers:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.vaginal_symptom_triggers, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "vaginal_symptom_onset_pattern", "type": "radio", "input": true, "label": "Did your vaginal symptoms start around the same time or over separate days?", "values": [{"label": "All symptoms started around the same time", "value": "same_time"}, {"label": "Symptoms started on separate days", "value": "separate_days"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.vaginal_symptom_overview && _.sum(_.values(data.vaginal_symptom_overview).map(Number)) >= 2", "optionsLabelPosition": "right"}, {"key": "durations", "type": "textfield", "input": true, "label": "Durations:", "hidden": true, "disabled": true, "multiple": true, "tableView": false, "clearOnHide": false, "calculateValue": [{"label": "Doesn't apply", "value": "doesn't_apply"}, {"label": "Last night", "value": "last_night"}, {"label": "Today", "value": "today"}, {"label": "1 day ago", "value": "1_day_ago"}, {"label": "2 days ago", "value": "2_days_ago"}, {"label": "3 days ago", "value": "3_days_ago"}, {"label": "4 days ago", "value": "4_days_ago"}, {"label": "5 days ago", "value": "5_days_ago"}, {"label": "6 days ago", "value": "6_days_ago"}, {"label": "7 days ago", "value": "7_days_ago"}, {"label": "8 days ago", "value": "8_days_ago"}, {"label": "9 days ago", "value": "9_days_ago"}, {"label": "10 days ago", "value": "10_days_ago"}, {"label": "11 days ago", "value": "11_days_ago"}, {"label": "12 days ago", "value": "12_days_ago"}, {"label": "13 days ago", "value": "13_days_ago"}, {"label": "14 days ago", "value": "14_days_ago"}, {"label": "14-21 days ago", "value": "14-21_days"}, {"label": "21-28 days ago", "value": "21-28_days"}, {"label": "28-60 days ago", "value": "28-60_days"}, {"label": "60-90 days ago", "value": "60-90_days"}, {"label": "90 days - 1 year ago", "value": "90_days-1_year"}, {"label": "1+ year ago", "value": "1+_year"}]}, {"key": "all_vaginal_symptoms_start", "data": {"custom": "values = data.durations"}, "type": "select", "input": true, "label": "When did your vaginal symptoms start?", "widget": "html5", "dataSrc": "custom", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.vaginal_symptom_overview && data.vaginal_symptom_onset_pattern == 'same_time' && !data.no_vaginal_symptoms", "optionsLabelPosition": "right"}, {"key": "heading_vaginal_itchiness", "html": "<h4 class='mb-n2'>Vaginal Itchiness</h4>", "type": "content", "input": false, "label": "<PERSON><PERSON><PERSON><PERSON>", "tableView": false, "refreshOnChange": true, "customConditional": "show = data.sex == 'female' && data.vaginal_symptom_overview && data.vaginal_symptom_overview.vaginal_itchiness == true"}, {"key": "symptom_start_vaginal_itchiness", "data": {"custom": "values = [{label: `I've always had vaginal itchiness and this isn't new for me`,value:`always`}].concat(data.durations)"}, "type": "select", "input": true, "label": "When did your vaginal itchiness start?", "widget": "html5", "dataSrc": "custom", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.sex == 'female' && !!data.vaginal_symptom_overview && data.vaginal_symptom_overview.vaginal_itchiness && (_.sum(_.values(data.vaginal_symptom_overview).map(Number)) < 2 || data.vaginal_symptom_onset_pattern == 'separate_days')", "optionsLabelPosition": "right"}, {"key": "vaginal_itchiness_location", "type": "selectboxes", "input": true, "label": "Where do you experience vaginal itchiness? Please select all that apply:", "values": [{"label": "Around the vaginal area (not sure exactly where)", "value": "general_area", "shortcut": ""}, {"label": "Vaginal opening", "value": "vaginal_opening", "shortcut": ""}, {"label": "Inside the vagina", "value": "inside_vagina", "shortcut": ""}, {"label": "Outer skin folds (outer labia)", "value": "outer_labia", "shortcut": ""}, {"label": "Inner skin folds (inner labia)", "value": "inner_labia", "shortcut": ""}, {"label": "Around the anal area", "value": "anal_area", "shortcut": ""}, {"label": "Groin or inner thigh", "value": "groin_or_thigh", "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}], "adminFlag": true, "inputType": "checkbox", "tableView": true, "customConditional": "show = data.sex === 'female' && !!data.vaginal_symptom_overview && data.vaginal_symptom_overview.vaginal_itchiness", "optionsLabelPosition": "right"}, {"key": "vaginal_itchiness_location_other", "type": "textarea", "input": true, "label": "Please describe the location where you experience vaginal itchiness:", "tableView": true, "autoExpand": false, "placeholder": "E.g. on the thighs, near the anus, in skin folds, etc.", "customConditional": "show = data.vaginal_itchiness_location?.other === true"}, {"key": "none_of_the_above_vaginal_itchiness_location", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a location."}, "validate": {"custom": "valid = !!data.none_of_the_above_vaginal_itchiness_location || _.some(_.values(data.vaginal_itchiness_location));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.sex === 'female' && !!data.vaginal_symptom_overview && data.vaginal_symptom_overview.vaginal_itchiness"}, {"key": "vaginal_itchiness_location_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have itchiness in the following locations:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "No itchiness in these locations:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.vaginal_itchiness_location, _.negate(_.identity))), _.startCase), ', '), /_/g, ' ');"}, {"key": "vaginal_skin_changes_reported_later", "type": "radio", "input": true, "label": "Have you also noticed any skin changes along with the itchiness (e.g. rash, irritation, swelling, sores)?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.sex === 'female' && data.vaginal_symptom_overview?.vaginal_itchiness === true && !data.vaginal_symptom_overview?.vaginal_skin_changes"}, {"key": "heading_discharge_quality", "html": "<h4 class='mb-n2'>Vaginal Discharge</h4>", "type": "content", "input": false, "label": "Vaginal Discharge Details", "tableView": false, "refreshOnChange": true, "customConditional": "show = data.vaginal_symptom_overview && data.vaginal_symptom_overview.vaginal_discharge === true"}, {"key": "symptom_start_vaginal_discharge", "data": {"custom": "values = [{label: `I've always had vaginal discharge and this isn't new for me`,value:`always`}].concat(data.durations)"}, "type": "select", "input": true, "label": "When did you start experiencing discharge?", "widget": "html5", "dataSrc": "custom", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.sex == 'female' && !!data.vaginal_symptom_overview && data.vaginal_symptom_overview.vaginal_discharge && (_.sum(_.values(data.vaginal_symptom_overview).map(Number)) < 2 || data.vaginal_symptom_onset_pattern == 'separate_days')", "optionsLabelPosition": "right"}, {"key": "vaginal_discharge_color", "type": "selectboxes", "input": true, "label": "What is the color of your discharge? Please select all that apply:", "values": [{"label": "Curdish-white", "value": "curdish_white"}, {"label": "Yellow", "value": "yellow"}, {"label": "Pink", "value": "pink"}, {"label": "Bloody", "value": "bloody"}, {"label": "Green", "value": "green"}, {"label": "Grey", "value": "grey"}, {"label": "None of the above", "value": "other"}], "inputType": "checkbox", "tableView": true, "customConditional": "show = data.sex == 'female' && data.vaginal_symptom_overview && data.vaginal_symptom_overview.vaginal_discharge", "optionsLabelPosition": "right"}, {"key": "vaginal_discharge_consistency", "type": "selectboxes", "input": true, "label": "What is the consistency of your discharge? Please select all that apply:", "values": [{"label": "<PERSON><PERSON><PERSON>", "value": "thick", "shortcut": ""}, {"label": "Thin", "value": "thin", "shortcut": ""}, {"label": "Watery", "value": "watery", "shortcut": ""}, {"label": "Streaks of blood", "value": "streaks_of_blood", "shortcut": ""}], "adminFlag": true, "tableView": true, "customConditional": "show = data.sex == 'female' && data.vaginal_symptom_overview && data.vaginal_symptom_overview.vaginal_discharge", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_vaginal_discharge_consistency", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a consistency."}, "validate": {"custom": "valid = !!data.none_of_the_above_vaginal_discharge_consistency || _.some(_.values(data.vaginal_discharge_consistency));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.sex == 'female' && data.vaginal_symptom_overview && data.vaginal_symptom_overview.vaginal_discharge"}, {"key": "vaginal_discharge_consistency_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following discharge consistencies:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "My discharge does not have the following consistencies:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.vaginal_discharge_consistency, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "heading_vaginal_odour", "html": "<h4 class='mb-n2'>Vaginal Odour</h4>", "type": "content", "input": false, "label": "Vaginal Odor Details", "tableView": false, "refreshOnChange": true, "customConditional": "show = data.sex == 'female' && data.vaginal_symptom_overview && data.vaginal_symptom_overview.vaginal_odour === true"}, {"key": "symptom_start_vaginal_odour", "data": {"custom": "values = [{label: `I've always noticed an odour and this isn't new for me`,value:`always`},{label: `Always with my menstrual cycles, but never in between`,value:`with_menses`}].concat(data.durations)"}, "type": "select", "input": true, "label": "When did you first notice your vaginal odour?", "widget": "html5", "dataSrc": "custom", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.sex == 'female' && !!data.vaginal_symptom_overview && data.vaginal_symptom_overview.vaginal_odour && (_.sum(_.values(data.vaginal_symptom_overview).map(Number)) < 2 || data.vaginal_symptom_onset_pattern == 'separate_days')", "optionsLabelPosition": "right"}, {"key": "vaginal_odour_description", "type": "selectboxes", "input": true, "label": "What describes the odour of your vaginal discharge? Please select all that apply:", "values": [{"label": "Fishy (often stronger after intercourse)", "value": "fishy", "shortcut": ""}, {"label": "Off-odour (unusual or unpleasant, but hard to describe)", "value": "off_odour", "shortcut": ""}, {"label": "Yeast-like (bread or beer smell)", "value": "yeast_like", "shortcut": ""}, {"label": "Metallic (like iron or blood)", "value": "metallic", "shortcut": ""}, {"label": "No odour", "value": "no_odour", "shortcut": ""}, {"label": "I'm not sure, but something seems off", "value": "not_sure_something_off", "shortcut": ""}], "adminFlag": true, "tableView": true, "customConditional": "show = data.sex == 'female' && data.vaginal_symptom_overview && data.vaginal_symptom_overview.vaginal_odour", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_vaginal_odour_description", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a description."}, "validate": {"custom": "valid = !!data.none_of_the_above_vaginal_odour_description || _.some(_.values(data.vaginal_odour_description));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.sex == 'female' && data.vaginal_symptom_overview && data.vaginal_symptom_overview.vaginal_odour"}, {"key": "vaginal_odour_description_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following odour descriptions:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "I DO NOT have the following odour descriptions:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.vaginal_odour_description, _.negate(_.identity))), _.startCase), ', '), /_/g, ' ');"}, {"key": "heading_vaginal_dryness", "html": "</br><h4 class='mb-n2'>Vaginal Dryness</h4>", "type": "content", "input": false, "label": "Vaginal Dryness Details", "tableView": false, "refreshOnChange": true, "customConditional": "show = data.vaginal_symptom_overview && data.vaginal_symptom_overview.vaginal_dryness === true"}, {"key": "symptom_start_vaginal_dryness", "data": {"custom": "values = [{label: `I've always had dryness and this isn't new for me`,value:`always`},{label: `I only experience dryness with intercourse`,value:`with_intercourse`}].concat(data.durations)"}, "type": "select", "input": true, "label": "When did you first notice your vaginal dryness?", "widget": "html5", "dataSrc": "custom", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.sex == 'female' && !!data.vaginal_symptom_overview && data.vaginal_symptom_overview.vaginal_dryness && (_.sum(_.values(data.vaginal_symptom_overview).map(Number)) < 2 || data.vaginal_symptom_onset_pattern == 'separate_days')", "optionsLabelPosition": "right"}, {"key": "menopause_perimenopausal_status", "type": "radio", "input": true, "label": "Have you gone through menopause or been told by your doctor that you are perimenopausal?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "I don't think so", "value": "doesn't_think_so"}, {"label": "I don't know", "value": "doesn't_know"}], "tableView": true, "customConditional": "show = data.vaginal_symptom_overview && data.vaginal_symptom_overview.vaginal_dryness"}, {"key": "dryness_relationship_intercourse", "type": "radio", "input": true, "label": "Is your dryness related only to intercourse, or is it present all the time?", "values": [{"label": "Only during intercourse", "value": "intercourse_only"}, {"label": "Present all the time", "value": "all_time"}], "tableView": true, "customConditional": "show = data.vaginal_symptom_overview && data.vaginal_symptom_overview.vaginal_dryness && !!data.menopause_perimenopausal_status"}, {"key": "vaginal_moisturizers_use", "type": "radio", "input": true, "label": "Have you used over-the-counter (i.e. non-prescription) vaginal moisturizers from the pharmacy, like Replens?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "tableView": true, "customConditional": "show = data.vaginal_symptom_overview && data.vaginal_symptom_overview.vaginal_dryness && !!data.dryness_relationship_intercourse"}, {"key": "vaginal_estrogen_prescription", "type": "radio", "input": true, "label": "Have you ever been prescribed vaginal estrogen?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "tableView": true, "customConditional": "show = data.vaginal_symptom_overview && data.vaginal_symptom_overview.vaginal_dryness && !!data.vaginal_moisturizers_use"}, {"key": "birth_control_relation", "type": "radio", "input": true, "label": "Did you find your symptoms started after taking birth control?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "tooltip": "Higher estrogen dose birth control can resolve vaginal dryness.", "tableView": true, "customConditional": "show = data.vaginal_symptom_overview && data.vaginal_symptom_overview.vaginal_dryness && !!data.vaginal_estrogen_prescription"}, {"key": "heading_bleeding_after_sex", "html": "</br><h4 class='mb-n2'>Bleeding After Sex</h4>", "type": "content", "input": false, "label": "Bleeding After Sex", "tableView": false, "refreshOnChange": true, "customConditional": "show = data.vaginal_symptom_overview?.bleeding_after_sex === true"}, {"key": "bleeding_correlation", "type": "selectboxes", "input": true, "label": "When does the bleeding tend to happen? Please select all that apply:", "values": [{"label": "During intercourse", "value": "during_intercourse", "shortcut": ""}, {"label": "The day or two after intercourse", "value": "after_intercourse", "shortcut": ""}, {"label": "Unrelated to sex", "value": "unrelated_to_sex", "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}], "adminFlag": true, "tableView": true, "customConditional": "show = data.sex === 'female' && data.vaginal_symptom_overview?.bleeding_after_sex === true", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_bleeding_correlation", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a bleeding pattern."}, "validate": {"custom": "valid = !!data.none_of_the_above_bleeding_correlation || _.some(_.values(data.bleeding_correlation));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.sex === 'female' && data.changes_vaginal === 'yes' && data.vaginal_symptom_overview?.bleeding_after_sex === true"}, {"key": "bleeding_correlation_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following bleeding patterns:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "I DO NOT have the following bleeding patterns:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.bleeding_correlation, _.negate(_.identity))), _.startCase), ', '), /_/g, ' ');"}, {"key": "number_bleeding_after_sex_episodes", "data": {"values": [{"label": "I haven't had any bleeding episodes", "value": "none"}, {"label": "1 episode", "value": "1"}, {"label": "2 episodes", "value": "2"}, {"label": "3 episodes", "value": "3"}, {"label": "4 episodes", "value": "4"}, {"label": "5 episodes", "value": "5"}, {"label": "6 episodes", "value": "6"}, {"label": "7 episodes", "value": "7"}, {"label": "8 episodes", "value": "8"}, {"label": "9 episodes", "value": "9"}, {"label": "10 episodes", "value": "10"}, {"label": "11-15 episodes", "value": "11-15"}, {"label": "15+ episodes", "value": "15_plus"}]}, "type": "select", "input": true, "label": "How many episodes of bleeding related to sex have you experienced?", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.sex == 'female' && !!data.vaginal_symptom_overview && data.vaginal_symptom_overview.bleeding_after_sex && !!data.bleeding_correlation", "optionsLabelPosition": "right"}, {"key": "postcoital_bleeding_recommendation", "html": "<div style='border-left: 4px solid #dc3545; background-color: #f8d7da; padding: 10px; margin-bottom: 10px;'><strong>Recommendation:</strong> If your bleeding after sex is persistent—even with negative STI screening—or if you develop new abdominal or pelvic discomfort while waiting for results, it's important to see your doctor in-person for a speculum exam of your cervix. These symptoms can sometimes signal conditions that require further investigation.</div>", "type": "content", "input": false, "customConditional": "show = data.sex === 'female' && data.vaginal_symptom_overview?.bleeding_after_sex && !!data.number_bleeding_after_sex_episodes && data.number_bleeding_after_sex_episodes !== 'none'"}, {"key": "postcoital_bleeding_recommendation_understanding", "type": "radio", "input": true, "label": "Do you understand this recommendation?", "values": [{"label": "I understand", "value": "understand"}, {"label": "I do not understand", "value": "do_not_understand"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Understands In-Person Speculum Exam Recommendation:", "customConditional": "show = data.sex === 'female' && data.vaginal_symptom_overview?.bleeding_after_sex && !!data.number_bleeding_after_sex_episodes && data.number_bleeding_after_sex_episodes !== 'none'"}, {"key": "iud_usage", "type": "radio", "input": true, "label": "Do you have an IUD?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "tableView": true, "customConditional": "show = data.sex == 'female' && data.vaginal_symptom_overview && data.vaginal_symptom_overview.bleeding_after_sex && !!data.number_bleeding_after_sex_episodes"}, {"key": "iud_type", "type": "radio", "input": true, "label": "Which type of IUD do you have?", "values": [{"label": "<PERSON><PERSON> (Hormonal) - 8 years", "value": "<PERSON><PERSON>"}, {"label": "<PERSON><PERSON> (Hormonal) - 5 years", "value": "kyleena"}, {"label": "<PERSON> (Hormonal) - 3 years", "value": "skyla"}, {"label": "<PERSON> (Non-hormonal) - 5 years", "value": "mona_lisa"}, {"label": "<PERSON><PERSON><PERSON> (Non-hormonal) - 5 years", "value": "liverte_copper_iud"}, {"label": "Nova-T (Non-hormonal) - 5 years", "value": "nova_copper_iud"}, {"label": "Flexi-T (Non-hormonal) - 5 years", "value": "flexi_copper_iud"}, {"label": "Other", "value": "other"}, {"label": "Not sure", "value": "not_sure"}], "tableView": true, "customConditional": "show = data.sex == 'female' && data.vaginal_symptom_overview && data.vaginal_symptom_overview.bleeding_after_sex && data.iud_usage === 'yes' && !!data.number_bleeding_after_sex_episodes"}, {"key": "iud_symptom_relation", "type": "radio", "input": true, "label": "Did you find the symptoms started after the IUD was inserted?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "tableView": true, "customConditional": "show = data.vaginal_symptom_overview && data.vaginal_symptom_overview.bleeding_after_sex && !!data.iud_usage && data.iud_usage === 'yes'"}, {"key": "iud_string_check", "type": "radio", "input": true, "label": "Have you had the IUD string length checked with a speculum exam since this started?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "tableView": true, "customConditional": "show = data.vaginal_symptom_overview && data.vaginal_symptom_overview.bleeding_after_sex && !!data.iud_symptom_relation && data.iud_usage === 'yes'"}, {"key": "iud_string_check_recommendation", "html": "<div style='border-left: 4px solid #0d6efd; background-color: #e7f1ff; padding: 10px; margin-bottom: 10px;'><strong>Recommendation:</strong> If you're experiencing bleeding after sex and have an IUD, it's important to have the string length checked with a speculum exam. A misplaced or low-positioned IUD can irritate the cervix and may contribute to bleeding. This can only be assessed during an in-person pelvic exam.</div>", "type": "content", "input": false, "customConditional": "show = data.iud_string_check === 'no'"}, {"key": "iud_string_check_understanding", "type": "radio", "input": true, "tableView": true, "label": "Do you understand why checking the IUD string length is recommended?", "values": [{"label": "I understand", "value": "understand"}, {"label": "I do not understand", "value": "do_not_understand"}], "validate": {"required": true}, "customConditional": "show = data.iud_string_check === 'no'"}, {"key": "other_birth_control_or_vasectomy", "type": "radio", "tableView": true, "input": true, "label": "Are you currently using any other form of birth control (e.g. patch, implant like Nexplanon), or does your current partner have a vasectomy?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "customConditional": "show = data.iud_usage === 'no'"}, {"key": "heading_midcycle_spotting", "html": "<h4 class='mb-n2'>Mid-Cycle Spotting</h4>", "type": "content", "input": false, "label": "Mid-Cycle Spotting", "tableView": false, "refreshOnChange": true, "customConditional": "show = data.vaginal_symptom_overview && data.vaginal_symptom_overview.mid_cycle_spotting === true"}, {"key": "menstrual_cycle_regularity", "type": "radio", "input": true, "label": "Are your menstrual cycles regular?<br><br><div style='border-left: 4px solid #0d6efd; background-color: #e7f1ff; padding: 10px;'><strong>Regular cycles:</strong> Timing and flow are fairly consistent each month (e.g., 25-30 days between periods, lasting 3-5 days).<br><br><strong>Irregular cycles:</strong> Timing and flow vary from month to month (e.g., 14 days one month and 50 the next; spotting one time, heavy bleeding another).</div>", "values": [{"label": "Yes, my cycles are regular", "value": "regular"}, {"label": "No, my cycles are irregular", "value": "irregular"}, {"label": "I'm not sure", "value": "not_sure"}], "tableView": true, "customConditional": "show = data.sex == 'female' && !!data.vaginal_symptom_overview && data.vaginal_symptom_overview.mid_cycle_spotting", "optionsLabelPosition": "right"}, {"key": "symptom_start_spotting_between_cycles", "data": {"values": [{"label": "Less than 1 month ago", "value": "less_than_1_month"}, {"label": "1-2 months ago", "value": "1_2_months"}, {"label": "2-3 months ago", "value": "2_3_months"}, {"label": "3-6 months ago", "value": "3_6_months"}, {"label": "6-12 months ago", "value": "6_12_months"}, {"label": "More than 1 year ago", "value": "1_plus_year"}, {"label": "I've always spotted between cycles", "value": "longstanding"}]}, "type": "select", "input": true, "label": "When did you first notice that you were spotting between menstrual cycles?", "widget": "html5", "dataSrc": "values", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.sex === 'female' && data.vaginal_symptom_overview?.mid_cycle_spotting === true"}, {"key": "spotting_prior_exam", "type": "radio", "input": true, "tableView": true, "label": "Have you ever had this spotting investigated in the past?<br><br>This could include any of the following:<ul><li>A vaginal exam or speculum exam to examine your cervix</li><li>A referral to a gynecologist</li><li>A pelvic ultrasound</li><li>A Pap test (cervical cancer screening)</li><li>Other workup related to bleeding between periods</li></ul>", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "customConditional": "show = data.sex === 'female' && data.vaginal_symptom_overview?.mid_cycle_spotting === true"}, {"key": "spotting_exam_recommendation", "html": "<div style='border-left: 4px solid #0d6efd; background-color: #e7f1ff; padding: 10px; margin-bottom: 10px;'><strong>Recommendation:</strong> If you're experiencing spotting between cycles and haven't had an exam before, it's important to get a speculum exam of your cervix. This helps rule out causes like cervical inflammation or polyps. If your exam is normal but spotting continues, your doctor may recommend a pelvic ultrasound to check for uterine or ovarian conditions.</div>", "type": "content", "input": false, "customConditional": "show = data.spotting_prior_exam === 'no'"}, {"key": "spotting_exam_recommendation_understanding", "type": "radio", "tableView": true, "input": true, "label": "Do you understand this recommendation to have a speculum exam and, if needed, a pelvic ultrasound?", "values": [{"label": "I understand", "value": "understand"}, {"label": "I do not understand", "value": "do_not_understand"}], "validate": {"required": true}, "customConditional": "show = data.spotting_prior_exam === 'no'"}, {"key": "spotting_prior_exam_details", "type": "selectboxes", "input": true, "tableView": true, "label": "What type of evaluation have you had for this spotting?", "values": [{"label": "Pelvic exam or speculum exam to examine the cervix", "value": "speculum_exam"}, {"label": "Referral to a gynecologist", "value": "gyne_referral"}, {"label": "Pelvic ultrasound", "value": "pelvic_ultrasound"}, {"label": "Pap test (cervical cancer screening)", "value": "pap_test"}, {"label": "Other type of evaluation", "value": "other"}], "customConditional": "show = data.spotting_prior_exam === 'yes'", "optionsLabelPosition": "right"}, {"key": "spotting_missing_cervical_exam_recommendation", "html": "<div style='border-left: 4px solid #0d6efd; background-color: #e7f1ff; padding: 10px; margin-bottom: 10px;'><strong>Recommendation:</strong> Even if you've had other tests like a pelvic ultrasound, it's important to ensure that your cervix has been properly evaluated with a speculum exam and a Pap test (if due). The cervix can be a source of spotting, especially with inflammation, infection, or changes to cervical tissue. These issues may not show up on an ultrasound but can be seen during a physical exam.</div>", "type": "content", "input": false, "customConditional": "show = data.spotting_prior_exam === 'yes' && !data.spotting_prior_exam_details?.pap_test && !data.spotting_prior_exam_details?.speculum_exam"}, {"key": "spotting_missing_cervical_exam_understanding", "type": "radio", "input": true, "tableView": true, "label": "Do you understand why it's important to have your cervix examined with a speculum and Pap test, even if other test results were normal?", "values": [{"label": "I understand", "value": "understand"}, {"label": "I do not understand", "value": "do_not_understand"}], "validate": {"required": true}, "customConditional": "show = data.spotting_prior_exam === 'yes' && !data.spotting_prior_exam_details?.pap_test && !data.spotting_prior_exam_details?.speculum_exam"}, {"key": "heading_skin_changes", "html": "</br><h4 class='mb-n2'>Skin Changes Around the Vagina</h4>", "type": "content", "input": false, "label": "Skin Changes", "tableView": false, "refreshOnChange": true, "customConditional": "show = data.vaginal_symptom_overview?.vaginal_skin_changes === true || data.vaginal_skin_changes_reported_later === 'yes'"}, {"key": "symptom_start_vaginal_skin_changes", "data": {"custom": "values = [{label: `I've always had these skin changes and this isn't new for me`, value: `always`}].concat(data.durations)"}, "type": "select", "input": true, "label": "When did you first notice these skin changes?", "widget": "html5", "dataSrc": "custom", "validate": {"required": true}, "tableView": true, "customConditional": "show = (data.vaginal_symptom_overview?.vaginal_skin_changes === true || data.vaginal_skin_changes_reported_later === 'yes') && (_.sum(_.values(data.vaginal_symptom_overview).map(Number)) < 2 || data.vaginal_symptom_onset_pattern === 'separate_days')", "optionsLabelPosition": "right"}, {"key": "vaginal_skin_rash_characteristics", "type": "selectboxes", "input": true, "label": "How would you describe the skin changes or rash? Please select all that apply:", "values": [{"label": "Redness", "value": "redness"}, {"label": "Dry or flaky skin", "value": "dry_flaky"}, {"label": "Cracked or broken skin", "value": "broken_skin"}, {"label": "Swelling or puffiness", "value": "swelling"}, {"label": "Raised bumps or blisters", "value": "raised_bumps"}, {"label": "Fluid-filled blisters (like cold sores)", "value": "fluid_filled_blisters"}, {"label": "Sores or ulcers that scab over", "value": "sores_or_ulcers"}, {"label": "Discoloured patches", "value": "discoloured_patches"}, {"label": "Other", "value": "other"}], "adminFlag": true, "inputType": "checkbox", "tableView": true, "customConditional": "show = data.vaginal_symptom_overview?.vaginal_skin_changes === true || data.vaginal_skin_changes_reported_later === 'yes'", "optionsLabelPosition": "right"}, {"key": "vaginal_skin_rash_characteristics_other", "type": "textfield", "tableView": true, "input": true, "label": "Please describe the skin changes:", "placeholder": "e.g. bruising, peeling, or colour changes", "customConditional": "show = (data.vaginal_symptom_overview?.vaginal_skin_changes === true || data.vaginal_skin_changes_reported_later === 'yes') && data.vaginal_skin_rash_characteristics?.other === true"}, {"key": "none_of_the_above_vaginal_skin_rash_characteristics", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a rash characteristic."}, "validate": {"custom": "valid = !!data.none_of_the_above_vaginal_skin_rash_characteristics || _.some(_.values(data.vaginal_skin_rash_characteristics));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.vaginal_symptom_overview?.vaginal_skin_changes === true || data.vaginal_skin_changes_reported_later === 'yes'"}, {"key": "vaginal_skin_rash_characteristics_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following rash characteristics:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "My rash doesn't have these characteristics:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.vaginal_skin_rash_characteristics, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "vaginal_skin_rash_recommendation", "html": "<div style='border-left: 4px solid #dc3545; background-color: #f8d7da; padding: 10px; margin-bottom: 10px;'><strong>Recommendation:</strong> Certain types of skin changes—such as raised bumps, fluid-filled blisters, or sores that scab over—may be caused by contact irritation or allergic reactions (e.g. from a new lubricant, pad/liner, soap, or shaving). They may also be due to <strong>folliculitis</strong>, which is irritation or infection of the hair follicles. Folliculitis can look like red or pus-filled pimples and may appear after shaving, waxing, or friction from clothing. It is usually harmless but can be itchy or painful.<br><br>However, similar symptoms can also be due to a <strong>new herpes infection</strong>, especially if they appear after exposure to a new sexual partner.<br><br><strong>We recommend an in-person medical exam as soon as possible—ideally within 72 hours—so the area can be swabbed for testing.</strong> Herpes cannot be confirmed by visual diagnosis alone. It requires a special swab that is only available through walk-in or in-person clinics.<br><br>Please note: Vaginal self-swab testing at a lab only looks for yeast or bacterial vaginosis (BV), not herpes.</div>", "type": "content", "input": false, "customConditional": "show = (data.vaginal_symptom_overview?.vaginal_skin_changes === true || data.vaginal_skin_changes_reported_later === 'yes') && ['raised_bumps', 'fluid_filled_blisters', 'sores_or_ulcers'].some(k => data.vaginal_skin_rash_characteristics?.[k])"}, {"key": "vaginal_skin_rash_recommendation_understanding", "type": "radio", "input": true, "label": "Do you understand this recommendation?", "values": [{"label": "I understand", "value": "understand"}, {"label": "I do not understand", "value": "do_not_understand"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Understands HSV and contact rash recommendation:", "customConditional": "show = (data.vaginal_symptom_overview?.vaginal_skin_changes === true || data.vaginal_skin_changes_reported_later === 'yes') && ['raised_bumps', 'fluid_filled_blisters', 'sores_or_ulcers'].some(k => data.vaginal_skin_rash_characteristics?.[k])"}, {"key": "heading_vaginal_lump", "html": "</br><h4 class='mb-n2'>Lump Around the Vagina</h4>", "type": "content", "input": false, "label": "Lump Details", "tableView": false, "refreshOnChange": true, "customConditional": "show = data.vaginal_symptom_overview?.vaginal_lump === true"}, {"key": "vaginal_lump_characteristics", "type": "selectboxes", "tableView": true, "input": true, "label": "What best describes the lump you are noticing? Please select all that apply:", "values": [{"label": "Feels like a small bump just under the skin", "value": "under_skin_bump"}, {"label": "Feels like a swollen gland or pea-sized lump near the vaginal opening", "value": "swollen_gland"}, {"label": "Feels like a painful or irritated lump that developed quickly", "value": "painful_or_irritated"}, {"label": "The lump seems to be growing or getting bigger over time", "value": "growing_larger"}, {"label": "The lump comes and goes (sometimes it disappears, then comes back)", "value": "recurrent"}, {"label": "The lump has leaked fluid or burst open in the past", "value": "burst_or_leaked"}], "customConditional": "show = data.vaginal_symptom_overview?.vaginal_lump === true", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_vaginal_lump_characteristics", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "Please select at least one option or check 'None of the above.'"}, "validate": {"custom": "valid = !!data.none_of_the_above_vaginal_lump_characteristics || _.some(_.values(data.vaginal_lump_characteristics));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.vaginal_symptom_overview?.vaginal_lump === true"}, {"key": "vaginal_lump_characteristics_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following lump characteristics:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "Does not have the following lump characteristics:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.vaginal_lump_characteristics, _.negate(_.identity))), _.startCase), ', '), /_/g, ' ');", "customConditional": "show = false"}, {"key": "vaginal_lump_prior_diagnosis", "type": "selectboxes", "input": true, "tableView": true, "label": "Have you ever had a lump around the vagina that a doctor told you was one of the following?", "values": [{"label": "Bartholin cyst or abscess (a lump near the vaginal opening, may become painful or swollen)", "value": "barth<PERSON>"}, {"label": "A swollen lymph node", "value": "lymph_node"}, {"label": "A hernia in the groin or vulva area", "value": "hernia"}, {"label": "A skin cyst (e.g. sebaceous cyst, ingrown hair, or pimple-like lump)", "value": "skin_cyst"}, {"label": "I don't know / I was never given a diagnosis", "value": "unknown"}], "customConditional": "show = data.vaginal_symptom_overview?.vaginal_lump === true", "optionsLabelPosition": "right"}, {"key": "vaginal_lump_recommendation", "html": "<div style='border-left: 4px solid #dc3545; background-color: #f8d7da; padding: 10px; margin-bottom: 10px;'><strong>Important:</strong> New vaginal lumps or swelling should be assessed <strong>in person on the same day</strong> by a local healthcare provider or walk-in clinic. Some lumps—especially infected cysts or abscesses—may need to be drained to heal properly and reduce pain or risk of complications.<br><br>Even if the lump is not painful, a physical exam is the only way to confirm the cause. <strong>Please do not delay care.</strong><br><br><strong>TeleTest can still provide the paperwork for lab testing</strong> if it's appropriate—even if you do or do not follow through with an in-person visit. However, based on your symptoms, <strong>same-day in-person care is our clinic's recommendation.</strong></div>", "type": "content", "input": false, "customConditional": "show = data.vaginal_lump_prior_diagnosis && _.some(_.values(data.vaginal_lump_prior_diagnosis))"}, {"key": "vaginal_lump_recommendation_understanding", "type": "radio", "input": true, "tableView": true, "label": "Do you understand this recommendation to have the lump assessed in person, as some cysts or abscesses may require drainage?", "values": [{"label": "I understand", "value": "understand"}, {"label": "I do not understand", "value": "do_not_understand"}], "validate": {"required": true}, "customConditional": "show = data.vaginal_lump_prior_diagnosis && _.some(_.values(data.vaginal_lump_prior_diagnosis))"}, {"key": "heading_previous_diagnosis", "html": "</br><h4 class='mb-n2'>Previous Diagnosis</h4>", "type": "content", "input": false, "customConditional": "show = data.vaginal_symptom_overview && _.some(_.values(data.vaginal_symptom_overview)) && !data.none_of_the_above_vaginal_symptom_overview"}, {"key": "previous_similar_symptoms", "type": "radio", "input": true, "tableView": true, "label": "Have you ever had similar vaginal symptoms in the past?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "customConditional": "show = data.vaginal_symptom_overview && _.some(_.values(data.vaginal_symptom_overview)) && !data.none_of_the_above_vaginal_symptom_overview"}, {"key": "previous_diagnosis_list", "type": "selectboxes", "input": true, "tableView": true, "label": "What were you previously diagnosed with for similar symptoms?", "values": [{"label": "Bacterial vaginosis (BV)", "value": "bv"}, {"label": "Yeast infection", "value": "yeast"}, {"label": "Her<PERSON> (HSV)", "value": "herpes"}, {"label": "Allergy or sensitivity to soap", "value": "soap_allergy"}, {"label": "Allergy or sensitivity to condoms or lubricants", "value": "condom_allergy"}, {"label": "I used over-the-counter treatment but didn't get tested", "value": "otc_no_test"}, {"label": "I never saw a doctor for a diagnosis — the symptoms resolved on their own", "value": "self_resolved"}, {"label": "I was told the symptoms were normal and no testing was needed", "value": "told_normal"}, {"label": "I was tested but never got the results", "value": "no_results"}, {"label": "I don't remember the diagnosis", "value": "dont_remember"}, {"label": "The symptoms happen often and I no longer get them checked", "value": "recurrent_untreated"}, {"label": "Other", "value": "other"}], "customConditional": "show = data.previous_similar_symptoms === 'yes'", "optionsLabelPosition": "right"}, {"key": "current_symptoms_feel_similar", "type": "selectboxes", "tableView": true, "input": true, "label": "Do your current symptoms feel similar to any of your past diagnoses?", "values": [{"label": "Bacterial vaginosis (BV)", "value": "bv"}, {"label": "Yeast infection", "value": "yeast"}, {"label": "Her<PERSON> (HSV)", "value": "herpes"}, {"label": "Allergic reaction or irritation", "value": "allergic"}, {"label": "Other", "value": "other"}, {"label": "None of the above", "value": "none"}], "customConditional": "show = data.previous_similar_symptoms === 'yes'", "optionsLabelPosition": "right"}, {"key": "bv_treatment_interest", "type": "radio", "tableView": true, "input": true, "label": "Would you like to be treated for bacterial vaginosis (BV) now, before doing any testing? If your symptoms feel just like a previous BV infection, treatment can be started right away. Testing is still recommended to confirm the diagnosis and rule out other causes.", "values": [{"label": "Yes, I would like to be treated now", "value": "yes"}, {"label": "No, I prefer to wait for test results", "value": "no"}], "customConditional": "show = data.current_symptoms_feel_similar?.bv === true"}, {"key": "yeast_treatment_understanding", "type": "radio", "tableView": true, "input": true, "label": "You can treat a yeast infection with an over-the-counter cream (like Canesten) or with a single-dose oral pill called fluconazole 150mg, which is also available without a prescription at most pharmacies. Fluconazole is not recommended during pregnancy. Do you understand this?", "values": [{"label": "I understand yeast treatment", "value": "understand"}, {"label": "I do not understand yeast treatment", "value": "do_not_understand"}], "validate": {"required": true}, "customConditional": "show = data.yeast_treatment_interest === 'yes'"}, {"key": "vaginal_treatment_timing_acknowledgement", "html": "<div style='border-left: 4px solid #0d6efd; background-color: #e7f1ff; padding: 10px; margin-bottom: 10px;'><strong>Important:</strong> If you decide to go ahead with treatment and also want to do a vaginal swab test, please make sure to complete the swab <strong>before starting the medication</strong>. Using the medication first can affect the accuracy of the swab and may result in a false-negative for bacterial vaginosis or yeast.<br><br>If you are also doing STI testing, you can still go ahead with treatment—<strong>this will not affect your STI test results.</strong></div>", "type": "content", "input": false, "customConditional": "show = data.bv_treatment_interest === 'yes' || data.yeast_treatment_interest === 'yes'"}, {"key": "vaginal_treatment_timing_confirm", "type": "radio", "tableView": true, "input": true, "label": "Do you understand that you should complete your vaginal swab test before starting treatment?", "values": [{"label": "Yes, I understand", "value": "understand"}, {"label": "No, I do not understand", "value": "do_not_understand"}], "validate": {"required": true}, "customConditional": "show = data.bv_treatment_interest === 'yes' || data.yeast_treatment_interest === 'yes'"}, {"key": "heading_bv_treatment", "html": "<h4 class='mb-n2'>BV Treatment</h4>", "type": "content", "input": false, "customConditional": "show = data.bv_treatment_interest === 'yes'"}, {"key": "bv_allergy_medications", "type": "selectboxes", "input": true, "label": "Do you have an allergy or reaction to any of the following medications?", "values": [{"label": "Metronidazole (Flagyl)", "value": "metronidazole"}, {"label": "Clindamycin", "value": "clindamycin"}], "customConditional": "show = data.bv_treatment_interest === 'yes'", "tableView": true, "optionsLabelPosition": "right"}, {"key": "none_of_the_above_bv_allergy_medications", "type": "checkbox", "input": true, "label": "No allergies to the above medications", "errors": {"custom": "Please select at least one option or check 'No allergies to the above medications'"}, "validate": {"custom": "valid = !!data.none_of_the_above_bv_allergy_medications || _.some(_.values(data.bv_allergy_medications));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.bv_treatment_interest === 'yes'"}, {"key": "bv_allergy_medications_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have an allergy to the following BV medications:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "No allergy to the following BV medications:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.bv_allergy_medications, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');", "customConditional": "show = false"}, {"key": "bv_preference_avoid_metro", "type": "radio", "tableView": true, "input": true, "label": "Metronidazole is the most effective treatment option for BV. However:<ul><li>Some people experience side effects like nausea, a metallic taste, or upset stomach.</li><li>You must avoid alcohol while taking it and for 48 hours afterward.</li><li>Combining alcohol with metronidazole can cause severe nausea and vomiting.</li></ul><br><strong>Please choose the option that best applies to you:</strong>", "values": [{"label": "Tolerated before, will avoid alcohol", "value": "tolerated_and_avoid_alcohol"}, {"label": "Never tried, want to use it", "value": "never_tried_prefer"}, {"label": "Had bad side effects, want to avoid", "value": "intolerant_prefer_avoid"}, {"label": "Tolerated before, but plan to drink", "value": "tolerated_but_drinking"}, {"label": "Never tried, but plan to drink", "value": "never_tried_but_drinking"}], "validate": {"required": true}, "customConditional": "show = data.bv_treatment_interest === 'yes' && !data.bv_allergy_medications?.metronidazole && data.none_of_the_above_bv_allergy_medications === true"}, {"key": "bv_treatment_options_metro_only", "type": "radio", "tableView": true, "input": true, "label": "Select one of the recommended metronidazole treatment options below.", "values": [{"label": "Metronidazole 500mg tablet twice daily for 7 days (preferred, highest cure rate)", "value": "metro_7_day"}, {"label": "Metronidazole gel 0.75% inserted vaginally once daily for 5 days", "value": "metro_vaginal"}, {"label": "Metronidazole 2g single dose (lowest cure rate)", "value": "metro_single_dose"}, {"label": "None of the above", "value": "none"}], "validate": {"required": true}, "customConditional": "show = ['tolerated_and_avoid_alcohol', 'never_tried_prefer'].includes(data.bv_preference_avoid_metro)"}, {"key": "bv_treatment_options_clinda_only", "type": "radio", "input": true, "tableView": true, "label": "Based on your preferences, please select one of the clindamycin treatment options below, or choose 'None of the above' if you'd prefer not to start treatment now:", "values": [{"label": "Clindamycin 300mg tablet twice daily for 7 days", "value": "clinda_oral"}, {"label": "Clindamycin vaginal cream 2% inserted nightly for 7 days", "value": "clinda_vaginal"}, {"label": "None of the above", "value": "none"}], "validate": {"required": true}, "customConditional": "show = ['intolerant_prefer_avoid', 'tolerated_but_drinking', 'never_tried_but_drinking'].includes(data.bv_preference_avoid_metro) || data.bv_allergy_medications?.metronidazole === true"}, {"key": "bv_clindamycin_warning", "html": "<div style='border-left: 4px solid #dc3545; background-color: #f8d7da; padding: 10px; margin-bottom: 10px;'><strong>Warning:</strong> Bacterial vaginosis (BV) often resolves on its own when the pH returns to normal without antibiotics. Clindamycin is usually only recommended if you cannot tolerate metronidazole. Oral clindamycin has been associated with a rare but serious intestinal infection called <strong>Clostridium difficile (C. diff)</strong>. This can cause severe diarrhea, abdominal pain, fever, and, in rare cases, hospitalization. If you have not had problems with metronidazole before, it is generally safer to use that first.</div>", "type": "content", "input": false, "customConditional": "show = data.bv_treatment_options_clinda_only === 'clinda_oral' || data.bv_treatment_options_clinda_only === 'clinda_vaginal'"}, {"key": "bv_clindamycin_understanding", "type": "radio", "input": true, "tableView": true, "label": "Do you understand the risks of using clindamycin, including the rare risk of C. diff infection? If you wish to change your preference and consider metronidazole instead, you can do so by revising your answers above.", "values": [{"label": "I understand and wish to proceed with treatment", "value": "understand_proceed_with_treatment"}, {"label": "I do not understand", "value": "do_not_understand"}], "validate": {"required": true}, "customConditional": "show = data.bv_treatment_options_clinda_only === 'clinda_oral' || data.bv_treatment_options_clinda_only === 'clinda_vaginal'"}, {"key": "bv_treatment_risks_metro", "html": "<div style='border-left: 4px solid #ffc107; background-color: #fff3cd; padding: 10px; margin-bottom: 10px;'><strong>Please Note:</strong> Bacterial vaginosis (BV) often improves on its own when the vaginal pH returns to normal, especially if symptoms are mild.<br><br>Metronidazole is the most effective treatment, but may cause side effects such as nausea, metallic taste, dizziness, or stomach upset. <strong>Alcohol must be avoided while taking it and for 48 hours afterward</strong> due to the risk of severe nausea and vomiting.</div>", "type": "content", "input": false, "customConditional": "show = data.bv_treatment_options_metro_only"}, {"key": "bv_treatment_risks_understanding_metro", "type": "radio", "input": true, "tableView": true, "label": "Do you understand that BV can sometimes resolve without treatment, and that metronidazole can cause side effects including a strong reaction with alcohol?", "values": [{"label": "I understand", "value": "understand"}, {"label": "I do not understand", "value": "do_not_understand"}], "validate": {"required": true}, "customConditional": "show = data.bv_treatment_options_metro_only"}, {"key": "bv_confirm_proceed_with_treatment", "type": "radio", "input": true, "tableView": true, "label": "Would you still like to proceed with treatment for BV?", "values": [{"label": "Yes, I would like to proceed with treatment", "value": "yes"}, {"label": "No, I'd prefer to wait or let it resolve on its own", "value": "no"}], "validate": {"required": true}, "customConditional": "show = data.bv_treatment_risks_understanding === 'understand'"}, {"key": "recommend_vswb", "type": "textfield", "input": true, "label": "recommend_vswb:", "hidden": true, "disabled": true, "adminFlag": true, "tableView": true, "clearOnHide": false, "calculateValue": "value = data.sex == 'female' && data.vaginal_symptom_overview && ['vaginal_itchiness','vaginal_discharge','vaginal_odour','vaginal_dryness','bleeding_after_sex','mid_cycle_spotting','other'].some(v => data.vaginal_symptom_overview?.[v])", "refreshOnChange": true}, {"key": "vaginal_idrs", "type": "textfield", "input": true, "label": "Vaginal Intake Denial Reasons:", "hidden": true, "disabled": true, "multiple": true, "adminFlag": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "value = []", "refreshOnChange": true}]}