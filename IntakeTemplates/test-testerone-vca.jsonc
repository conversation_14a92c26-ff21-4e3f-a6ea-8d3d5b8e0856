{"components": [{"key": "testosterone_type", "type": "radio", "input": true, "label": "Please select which form of testosterone you are currently on:", "inline": false, "values": [{"label": "Testosterone Enanthate (intramuscular or subcutaneous)", "value": "testosterone_etnanthate", "shortcut": ""}, {"label": "Testosterone Cypionate (intramuscular or subcutaneous)", "value": "testosterone_cypionate", "shortcut": ""}, {"label": "Testosterone Gel (1%)", "value": "testosterone_gel_1", "shortcut": ""}, {"label": "Testosterone Gel (1.62%)", "value": "testosterone_gel_162", "shortcut": ""}, {"label": "I'm Not On Testosterone", "value": "not_on_testosterone", "shortcut": ""}], "tableView": true, "optionsLabelPosition": "right"}, {"key": "restart_testosterone", "type": "radio", "input": true, "label": "Have you recently stopped or run out of your testosterone, and are looking to restart your prescription?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "tableView": true, "customConditional": "show = data.anticoagulant_type == 'warfarin';", "optionsLabelPosition": "right"}, {"key": "inr_contraindication", "label": "Are you currently experiencing any of the following symptoms:", "values": [{"label": "Chest pain or heaviness", "value": "chest_pain", "shortcut": ""}, {"label": "Shortness of breath", "value": "dyspnea", "shortcut": ""}, {"label": "Feel lightheaded or faint", "value": "presy<PERSON><PERSON>", "shortcut": ""}, {"label": "Arm or Leg Swelling", "value": "limb_swelling", "shortcut": ""}, {"label": "Heart palpitations", "value": "palpitations", "shortcut": ""}, {"label": "Nausea or vomiting", "value": "nausea_vomiting", "shortcut": ""}, {"label": "Coughing up blood", "value": "hemoptysis", "shortcut": ""}, {"label": "New bruising", "value": "bruising", "shortcut": ""}, {"label": "New bleeding (gums, eyes, colon)", "value": "bleeding_new", "shortcut": ""}], "input": true, "inputType": "checkbox", "customConditional": "show = data.anticoagulant_type == 'warfarin' && data.restart_warfarin_administration == false;", "optionsLabelPosition": "right", "tableView": true, "type": "selectboxes"}, {"key": "no_inr_contraindications", "customClass": "mt-n3", "defaultValue": false, "input": true, "label": "None of the above", "tableView": true, "type": "checkbox", "customConditional": "show = data.anticoagulant_type != 'xarelto' && data.anticoagulant_type != 'apixaban' && data.anticoagulant_type != 'edoxaban' && data.anticoagulant_type != 'dabigatran'  && data.anticoagulant_type != 'heparin' && data.anticoagulant_type != 'no_anticogulant' && data.restart_warfarin_administration == false;", "validate": {"custom": "valid = !!data.no_inr_contraindications || !!_.some(_.values(data.inr_contraindication));"}, "errors": {"custom": "required, or select a symptom."}}, {"key": "blood_pressure_reading", "label": "TeleTest requires a recent blood pressure measurement within the last 2 weeks. You can measure your blood pressure for free at a local pharmacy. <br/><br/><strong>Please confirm your blood pressure below:</strong>", "values": [{"label": "Less than 140/90", "value": "normotensive", "shortcut": ""}, {"label": "Greater than 140/90", "value": "hypertensive", "shortcut": ""}, {"label": "I haven't checked my blood pressure", "value": "bp_unknown", "shortcut": ""}], "input": true, "optionsLabelPosition": "right", "customConditional": "show = data.anticoagulant_type == 'warfarin' && data.restart_warfarin_administration == false && data.no_inr_contraindications == true;", "tableView": false, "type": "radio"}], "display": "form", "name": "redFlags", "title": "Red Flags", "type": "form"}