{"components": [{"key": "current_age", "type": "radio", "input": true, "label": "Please select your age", "inline": false, "values": [{"label": "50+", "value": "50+", "shortcut": ""}, {"label": "49 or Younger", "value": "<50", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "family_history_colon_cancer", "type": "radio", "input": true, "label": "Do you have a 1st degree relative (i.e. mother, brother, father, sister) with a history of colon cancer?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.current_age == '<50';", "optionsLabelPosition": "right"}, {"key": "colonoscopy_rf", "type": "selectboxes", "input": true, "label": "Have you been diagnosed with any of the following medical conditions:", "values": [{"label": "<PERSON><PERSON><PERSON>'s Disease", "value": "crohns", "shortcut": ""}, {"label": "Ulcerative Colitis", "value": "ulcerative_colitis", "shortcut": ""}, {"label": "Familial Adenomatous Polyposis (FAP)", "value": "melena_stools", "shortcut": ""}, {"label": "Lynch Syndrome", "value": "lynch_syndrome", "shortcut": ""}, {"label": "Colon Cancer", "value": "colon_cancer", "shortcut": ""}, {"label": "Oligopoly<PERSON><PERSON>", "value": "oligopolyposis", "shortcut": ""}, {"label": "Polyps requiring surveillance", "value": "surveillance_polyps", "shortcut": ""}, {"label": "Juvenile Polyposis Syndrome", "value": "juvenile_polyposis_syndrome", "shortcut": ""}, {"label": "<PERSON><PERSON><PERSON> syndrome", "value": "cowden_syndrome", "shortcut": ""}, {"label": "<PERSON><PERSON><PERSON><PERSON> syndrome", "value": "peutz_jeg<PERSON>_syndrome", "shortcut": ""}], "inputType": "checkbox", "tableView": true, "customConditional": "show = data.family_history_colon_cancer == false;", "optionsLabelPosition": "right"}, {"key": "no_colonoscopy_rf", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = _.some(_.values(data.colonoscopy_rf)) || data.no_colonoscopy_rf;"}, "tableView": false, "customConditional": "show = data.family_history_colon_cancer == false;", "customClass": "mt-n3", "defaultValue": false}, {"key": "colonoscopy_contraindications", "type": "selectboxes", "input": true, "label": "Are you experiencing any of the following symptoms:", "values": [{"label": "Abdominal or Pelvic Pain", "value": "abdominal_pain", "shortcut": ""}, {"label": "Bloating", "value": "bloating", "shortcut": ""}, {"label": "Black or tarry stools", "value": "melena_stools", "shortcut": ""}, {"label": "Red blood in your stool", "value": "brbpr", "shortcut": ""}, {"label": "Chest pain or heaviness", "value": "chest_pain", "shortcut": ""}, {"label": "Shortness of breath", "value": "dyspnea", "shortcut": ""}, {"label": "Feel lightheaded or faint", "value": "presy<PERSON><PERSON>", "shortcut": ""}, {"label": "Heart palpitations (slow or fast heart beat)", "value": "palpitations", "shortcut": ""}], "inputType": "checkbox", "tableView": true, "optionsLabelPosition": "right"}, {"key": "no_colonoscopy_contraindications", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = _.some(_.values(data.colonoscopy_contraindications)) || data.no_colonoscopy_contraindications;"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}]}