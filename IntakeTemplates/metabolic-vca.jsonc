{"components": [{"key": "symptoms_contra", "type": "selectboxes", "input": true, "label": "Are you experiencing any of the following symptoms?", "values": [{"label": "Chest pain", "value": "chest_pain"}, {"label": "Fevers or chills", "value": "fevers"}, {"label": "Shortness of breath", "value": "sob"}, {"label": "Feel unwell", "value": "unwell"}, {"label": "New swelling in my legs, ankles, or feet", "value": "edema"}, {"label": "Heart palpitations", "value": "palpitations"}, {"label": "Headaches or vision changes", "value": "headache"}, {"label": "Numbness or weakness (face, arm, or leg)", "value": "numbness_weakness"}, {"label": "Difficulty speaking or confusion", "value": "speech_confusion"}, {"label": "Fainting or loss of consciousness", "value": "syncope"}, {"label": "Nausea or vomiting", "value": "nausea_vomiting"}], "inputType": "checkbox", "tableView": true, "confirmLabel": "Symptoms present:", "customConditional": "show = data.bp_greater_160_100 === 'no';", "optionsLabelPosition": "right"}, {"key": "no_symptoms_contra", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "Please select at least one symptom or confirm none apply."}, "validate": {"custom": "valid = !!data.no_symptoms_contra || !!_.some(_.values(data.symptoms_contra));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.bp_greater_160_100 === 'no';"}, {"key": "routine_screening", "type": "checkbox", "input": true, "label": "This visit is for routine screening only and I am not currently experiencing any symptoms", "defaultValue": false, "customClass": "mb-2", "tableView": false, "customConditional": "show = data.bp_greater_160_100 === 'no';"}, {"key": "no_indications", "type": "textfield", "input": true, "label": "No Indication:", "hidden": true, "disabled": true, "multiple": false, "tableView": true, "clearOnHide": false, "defaultValue": false, "calculateValue": "value = false;", "refreshOnChange": true}, {"key": "contraindication_keys", "type": "textfield", "input": true, "label": "Contraindication keys:", "hidden": true, "disabled": true, "multiple": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "value = [];\nif (_.some(_.values(data.symptoms_contra))) {\n  value.push('cardiac_resp_symptoms');\n}", "refreshOnChange": true}, {"key": "rejected_html", "type": "content", "input": true, "tableView": false, "clearOnHide": false, "defaultValue": "<h3 class='text-red'>You're ineligible for monitoring at this time. Please seek in-person care with a physician to discuss your concerns.</h3>", "refreshOnChange": true}]}