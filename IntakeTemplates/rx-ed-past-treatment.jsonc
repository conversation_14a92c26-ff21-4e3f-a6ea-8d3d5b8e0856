{"components": [{"key": "header_medication_use", "html": "<h2>Medication Use</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "medication_use_intro", "html": "<p>Please answer the following questions about any previous use of erectile-dysfunction medications. This helps us understand what you've already tried, what worked, and what may be safest moving forward.</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "past_ed_medication_use", "type": "radio", "input": true, "label": "Have you ever used medication for erectile dysfunction (e.g., Viagra®, Cialis®, Levitra® or their generics)?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure / don’t remember", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Previous ED-medication use:", "optionsLabelPosition": "right"}, {"key": "ed_medication_types", "type": "selectboxes", "input": true, "label": "Which erectile-dysfunction medications have you used? (Select all that apply)", "values": [{"label": "Sildenafil (Viagra® or generic)", "value": "sildenafil"}, {"label": "Tadalafil (Cialis® or generic)", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"label": "Vardenafil (Levitra® or generic)", "value": "vardenafil"}, {"label": "Avanafil (Stendra®)", "value": "avana<PERSON><PERSON>"}, {"label": "Alprostadil injection (Caverject®)", "value": "caverject"}, {"label": "Alprostadil urethral suppository (MUSE®)", "value": "muse"}, {"label": "Other", "value": "other"}, {"label": "Not sure / don’t remember", "value": "not_sure"}], "validate": {"required": true}, "adminFlag": true, "tableView": true, "confirm_label": "Types of ED medication used:", "customConditional": "show = data.past_ed_medication_use === 'yes'", "optionsLabelPosition": "right"}, {"key": "ed_medication_types_other", "type": "textfield", "input": true, "label": "Please specify the other medication:", "tableView": true, "customConditional": "show = data.ed_medication_types && data.ed_medication_types.other"}, {"key": "sildenafil_heading", "html": "</br><h3>Viagra® <small>(sildenafil)</small></h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.ed_medication_types && data.ed_medication_types.sildenafil"}, {"key": "dose_sildenafil", "data": {"values": [{"label": "25 mg", "value": "25mg"}, {"label": "50 mg", "value": "50mg"}, {"label": "100 mg", "value": "100mg"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "What dose of Sildenafil (Viagra) did you use most often?", "widget": "html5", "validate": {"required": true}, "adminFlag": true, "tableView": true, "confirm_label": "Sildenafil dose:", "customConditional": "show = data.ed_medication_types && data.ed_medication_types.sildenafil", "optionsLabelPosition": "right"}, {"key": "satisfaction_sildenafil", "type": "radio", "input": true, "label": "Overall, how satisfied were you with <PERSON><PERSON><PERSON><PERSON><PERSON>’s results?", "values": [{"label": "Very satisfied", "value": "very_satisfied"}, {"label": "Somewhat satisfied", "value": "somewhat_satisfied"}, {"label": "Not satisfied", "value": "not_satisfied"}], "validate": {"required": true}, "adminFlag": true, "tableView": true, "confirm_label": "Satisfaction with Sildenafil:", "customConditional": "show = data.ed_medication_types && data.ed_medication_types.sildenafil"}, {"key": "side_effects_sildenafil", "type": "selectboxes", "input": true, "label": "Which side-effects did you notice while using Sildenafil? (Select all that apply)", "values": [{"label": "Headache", "value": "headache"}, {"label": "Facial flushing", "value": "flushing"}, {"label": "Nasal congestion", "value": "congestion"}, {"label": "Upset stomach / reflux", "value": "dyspepsia"}, {"label": "Visual colour tinge", "value": "visual_change"}, {"label": "Back or muscle pain", "value": "back_pain"}], "adminFlag": true, "tableView": true, "confirm_label": "Sildenafil side-effects:", "customConditional": "show = data.ed_medication_types && data.ed_medication_types.sildenafil", "optionsLabelPosition": "right"}, {"key": "none_side_effects_sildenafil", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "Select at least one side-effect or tick 'None of the above'."}, "validate": {"custom": "valid = !!data.none_side_effects_sildenafil || _.some(_.values(data.side_effects_sildenafil));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.ed_medication_types && data.ed_medication_types.sildenafil"}, {"key": "sildenafil_se_management", "type": "radio", "input": true, "label": "How did you manage these Sildenafil side-effects at the dose you selected?", "values": [{"label": "Lowered the dose and side-effects resolved", "value": "dose_lowered_resolved"}, {"label": "Lowered the dose – side-effects improved but still present", "value": "dose_lowered_improved"}, {"label": "Lowered the dose – side-effects did not improve", "value": "dose_lowered_no_change"}, {"label": "Stayed on the same dose – side-effects were mild/manageable", "value": "kept_dose_mild"}, {"label": "Stopped Sildenafil because side-effects were intolerable", "value": "stopped_due_to_se"}], "validate": {"required": true}, "adminFlag": true, "tableView": true, "confirm_label": "Sildenafil SE management:", "customConditional": "show = data.ed_medication_types && data.ed_medication_types.sildenafil && !data.none_side_effects_sildenafil && _.some(_.values(data.side_effects_sildenafil));", "optionsLabelPosition": "right"}, {"key": "tadalafil_heading", "html": "</br><h3>Cialis® <small>(tadalafil)</small></h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.ed_medication_types && data.ed_medication_types.tadalafil"}, {"key": "dose_tadalafil", "data": {"values": [{"label": "5 mg", "value": "5mg"}, {"label": "10 mg", "value": "10mg"}, {"label": "20 mg", "value": "20mg"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "What dose of Tadalafil (Cialis) did you use most often?", "widget": "html5", "validate": {"required": true}, "adminFlag": true, "tableView": true, "confirm_label": "Tadalafil dose:", "customConditional": "show = data.ed_medication_types && data.ed_medication_types.tadalafil"}, {"key": "satisfaction_tadalafil", "type": "radio", "input": true, "label": "Overall, how satisfied were you with <PERSON><PERSON><PERSON><PERSON>’s results?", "values": [{"label": "Very satisfied", "value": "very_satisfied"}, {"label": "Somewhat satisfied", "value": "somewhat_satisfied"}, {"label": "Not satisfied", "value": "not_satisfied"}], "validate": {"required": true}, "adminFlag": true, "tableView": true, "confirm_label": "Satisfaction with Tadalafil:", "customConditional": "show = data.ed_medication_types && data.ed_medication_types.tadalafil"}, {"key": "side_effects_tadalafil", "type": "selectboxes", "input": true, "label": "Which side-effects did you notice while using Tadalafil? (Select all that apply)", "values": [{"label": "Headache", "value": "headache"}, {"label": "Facial flushing", "value": "flushing"}, {"label": "Nasal congestion", "value": "congestion"}, {"label": "Upset stomach / reflux", "value": "dyspepsia"}, {"label": "Back or muscle pain", "value": "back_pain"}, {"label": "Leg or arm pain", "value": "limb_pain"}], "adminFlag": true, "tableView": true, "confirm_label": "Tadalafil side-effects:", "customConditional": "show = data.ed_medication_types && data.ed_medication_types.tadalafil", "optionsLabelPosition": "right"}, {"key": "none_side_effects_tadalafil", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "Select at least one side-effect or tick 'None of the above'."}, "validate": {"custom": "valid = !!data.none_side_effects_tadalafil || _.some(_.values(data.side_effects_tadalafil));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.ed_medication_types && data.ed_medication_types.tadalafil"}, {"key": "tadalafil_se_management", "type": "radio", "input": true, "label": "How did you manage these Tadalafil side-effects at the dose you selected?", "values": [{"label": "Lowered the dose and side-effects resolved", "value": "dose_lowered_resolved"}, {"label": "Lowered the dose – side-effects improved but still present", "value": "dose_lowered_improved"}, {"label": "Lowered the dose – side-effects did not improve", "value": "dose_lowered_no_change"}, {"label": "Stayed on the same dose – side-effects were mild/manageable", "value": "kept_dose_mild"}, {"label": "Stopped Tadalafil because side-effects were intolerable", "value": "stopped_due_to_se"}], "validate": {"required": true}, "adminFlag": true, "tableView": true, "confirm_label": "Tadalafil SE management:", "customConditional": "show = data.ed_medication_types && data.ed_medication_types.tadalafil && !data.none_side_effects_tadalafil && _.some(_.values(data.side_effects_tadalafil));", "optionsLabelPosition": "right"}, {"key": "vardenafil_heading", "html": "</br><h3>Levitra® <small>(vardenafil)</small></h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.ed_medication_types && data.ed_medication_types.vardenafil"}, {"key": "dose_vardenafil", "data": {"values": [{"label": "5 mg", "value": "5mg"}, {"label": "10 mg", "value": "10mg"}, {"label": "20 mg", "value": "20mg"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "What dose of Vardenafil (Levi<PERSON>) did you use most often?", "widget": "html5", "validate": {"required": true}, "adminFlag": true, "tableView": true, "confirm_label": "Vardenafil dose:", "customConditional": "show = data.ed_medication_types && data.ed_medication_types.vardenafil"}, {"key": "satisfaction_vardenafil", "type": "radio", "input": true, "label": "Overall, how satisfied were you with Vardenafil’s results?", "values": [{"label": "Very satisfied", "value": "very_satisfied"}, {"label": "Somewhat satisfied", "value": "somewhat_satisfied"}, {"label": "Not satisfied", "value": "not_satisfied"}], "validate": {"required": true}, "adminFlag": true, "tableView": true, "confirm_label": "Satisfaction with Vardenafil:", "customConditional": "show = data.ed_medication_types && data.ed_medication_types.vardenafil"}, {"key": "side_effects_vardenafil", "type": "selectboxes", "input": true, "label": "Which side-effects did you notice while using Vardenafil? (Select all that apply)", "values": [{"label": "Headache", "value": "headache"}, {"label": "Facial flushing", "value": "flushing"}, {"label": "Nasal congestion", "value": "congestion"}, {"label": "Upset stomach / reflux", "value": "dyspepsia"}, {"label": "Back or muscle pain", "value": "back_pain"}, {"label": "Visual colour tinge", "value": "visual_change"}], "adminFlag": true, "tableView": true, "confirm_label": "Vardenafil side-effects:", "customConditional": "show = data.ed_medication_types && data.ed_medication_types.vardenafil", "optionsLabelPosition": "right"}, {"key": "none_side_effects_vardenafil", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "Select at least one side-effect or tick 'None of the above'."}, "validate": {"custom": "valid = !!data.none_side_effects_vardenafil || _.some(_.values(data.side_effects_vardenafil));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.ed_medication_types && data.ed_medication_types.vardenafil"}, {"key": "vardenafil_se_management", "type": "radio", "input": true, "label": "How did you manage these Vardenafil side-effects at the dose you selected?", "values": [{"label": "Lowered the dose and side-effects resolved", "value": "dose_lowered_resolved"}, {"label": "Lowered the dose – side-effects improved but still present", "value": "dose_lowered_improved"}, {"label": "Lowered the dose – side-effects did not improve", "value": "dose_lowered_no_change"}, {"label": "Stayed on the same dose – side-effects were mild/manageable", "value": "kept_dose_mild"}, {"label": "Stopped Vardenafil because side-effects were intolerable", "value": "stopped_due_to_se"}], "validate": {"required": true}, "adminFlag": true, "tableView": true, "confirm_label": "Vardenafil SE management:", "customConditional": "show = data.ed_medication_types && data.ed_medication_types.vardenafil && !data.none_side_effects_vardenafil && _.some(_.values(data.side_effects_vardenafil));", "optionsLabelPosition": "right"}]}