{"components": [{"key": "ed_med_header", "html": "<h1 style=\"text-align:center;\">Erectile-Dysfunction Medication Options</h1>", "type": "content", "input": false, "label": "Content"}, {"key": "ed_table_intro", "html": "<p><strong>Select your erectile-dysfunction treatment</strong></p><p>You’ll be able to choose the option that suits you best. TeleTest can route prescriptions to:</p><ul><li><strong>Partner pharmacy</strong> – stocks low-cost generic and lactose-free compounded <em>tadalafil</em> &amp; <em>sildenafil</em> with Ontario-wide shipping.</li><li><strong>Your own local pharmacy</strong> – if you prefer in-person pickup.</li><li>Branded Viagra® or Cialis® are available on request but cost more than generics.</li><li>The table below shows doses, onset, duration and partner-pharmacy pricing updated: <strong>{{ moment().format('MMM') }}&nbsp;{{ moment().format('YYYY') }}</strong>.</li></ul>", "type": "content", "input": false, "label": "Content"}, {"key": "ed_table_detailed", "html": "<div style=\"overflow-x:auto\"><table class=\"table table-sm table-bordered table-striped align-middle\" style=\"min-width:920px\"><thead class=\"table-light\"><tr><th style=\"width:26%\"></th><th style=\"width:19%\">Tadalafil</th><th style=\"width:19%\">Tadalafil</th><th style=\"width:18%\">Sildenafil</th><th style=\"width:18%\">Sildenafil</th></tr></thead><tbody><tr><th>Lactose-free</th><td class=\"text-center\">✓</td><td class=\"text-center\">x</td><td class=\"text-center\">✓</td><td class=\"text-center\">x</td></tr><tr><th>Format</th><td>Capsule</td><td>Tablet</td><td>Capsule</td><td>Tablet</td></tr><tr><th>Source</th><td>Compounded</td><td>Generic</td><td>Compounded</td><td>Generic</td></tr><tr><th>Available doses (mg)</th><td>3 / 5 / 6 / 10 / 20</td><td>5 / 10 / 20</td><td>25 / 50 / 100</td><td>25 / 50 / 100</td></tr><tr><th>Typical onset</th><td>30–45 min</td><td>30–45 min</td><td>30–60 min</td><td>30–60 min</td></tr><tr><th>Duration</th><td>Up to 36 h</td><td>Up to 36 h</td><td>≈ 4–6 h</td><td>≈ 4–6 h</td></tr><tr><th>Price per pill</th><td><ul class=\"list-unstyled mb-0\"><li>3 mg – $2.25</li><li>5 mg – $3.00</li><li>6 mg – $3.25</li><li>10 mg – $4.50</li><li>20 mg – $6.50</li></ul></td><td><ul class=\"list-unstyled mb-0\"><li>5 mg – $4.01</li><li>10 mg – $13.12</li><li>20 mg – $13.12</li></ul></td><td><ul class=\"list-unstyled mb-0\"><li>25 mg – $3.25</li><li>50 mg – $3.75</li><li>100 mg – $4.50</li></ul></td><td><ul class=\"list-unstyled mb-0\"><li>25 mg – $9.12</li><li>50 mg – $9.73</li><li>100 mg – $10.12</li></ul></td></tr><tr><th>Common side-effects</th><td colspan=\"4\">Headache, flushing, nasal congestion; tadalafil products may also cause back-pain / muscle-aches</td></tr></tbody></table><p class=\"text-muted mb-0\"><small>TeleTest works with a compounding pharmacy that offers lactose-free options for individuals who avoid lactose or are lactose intolerant.</small></p></div>", "type": "content", "input": false, "label": "Content"}, {"key": "rx_brand_preference_ed", "type": "radio", "input": true, "label": "<p><strong>Which medication do you prefer?</strong></p><ul><li><strong>Our suggestion:</strong> <em>Cialis®</em> lasts up to 36 hours, offering the widest window for spontaneity.</li></ul>", "values": [{"label": "Cialis® (tadalafil)", "value": "cialis_brand"}, {"label": "Viagra® (sildenafil)", "value": "viagra_brand"}, {"label": "Levitra® (vardenafil)", "value": "levitra_brand"}], "tableView": true, "defaultValue": "cialis_brand", "confirm_label": "Brand preference:"}, {"key": "past_ed_medication_use", "type": "radio", "input": true, "label": "Have you used any ED medication before?", "values": [{"label": "No – this will be my first trial", "value": "no"}, {"label": "Yes – I’ve taken one before", "value": "yes"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Past ED medication use:"}, {"key": "avoid_lactose_ed", "type": "radio", "input": true, "label": "Do you need a lactose-free formulation?", "values": [{"label": "Yes – lactose-free capsule", "value": "yes_lactose_free"}, {"label": "No – standard tablets are fine", "value": "no_lactose_ok"}], "tableView": true, "confirm_label": "Lactose-free Preference:", "customConditional": "show = data.rx_brand_preference_ed === 'cialis_brand' || data.rx_brand_preference_ed === 'viagra_brand';"}, {"key": "vardenafil_prev_tablet", "type": "radio", "input": true, "label": "Highest Levitra®/vardenafil tablet you tolerated *without side-effects*: ", "values": [{"label": "10 mg", "value": 10}, {"label": "20 mg", "value": 20}, {"label": "Unsure / can’t remember", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Past Levitra®/vardenafil tablet use:", "customConditional": "show = data.past_ed_medication_use==='yes' && data.rx_brand_preference_ed==='levitra_brand';"}, {"key": "cialis_prev_tablet", "type": "radio", "input": true, "label": "Highest Cialis®/tadalafil tablet you tolerated <em>without side-effects</em>:", "values": [{"label": "3 mg (capsule)", "value": 3}, {"label": "5 mg", "value": 5}, {"label": "6 mg (capsule)", "value": 6}, {"label": "10 mg", "value": 10}, {"label": "20 mg", "value": 20}, {"label": "Unsure / can’t remember", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Past Cialis®/tadalafil tablet use:", "customConditional": "show = data.past_ed_medication_use==='yes' && data.rx_brand_preference_ed==='cialis_brand' && data.avoid_lactose_ed==='no_lactose_ok';"}, {"key": "sildenafil_prev_tablet", "type": "radio", "input": true, "label": "Highest Viagra®/sildenafil tablet you tolerated <em>without side-effects</em>:", "values": [{"label": "25 mg", "value": 25}, {"label": "50 mg", "value": 50}, {"label": "100 mg", "value": 100}, {"label": "Unsure / can’t remember", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Past Viagra®/sildenafil tablet use:", "customConditional": "show = data.past_ed_medication_use==='yes' && data.rx_brand_preference_ed==='viagra_brand' && data.avoid_lactose_ed==='no_lactose_ok';"}, {"key": "cialis_prev_capsule", "type": "radio", "input": true, "label": "Highest Cialis®/tadalafil capsule you tolerated <em>without side-effects</em>:", "values": [{"label": "3 mg", "value": 3}, {"label": "5 mg", "value": 5}, {"label": "6 mg", "value": 6}, {"label": "10 mg", "value": 10}, {"label": "20 mg", "value": 20}, {"label": "Unsure / can’t remember", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Confirm your past Cialis®/tadalafil capsule use:", "customConditional": "show = data.past_ed_medication_use === 'yes' && data.rx_brand_preference_ed === 'cialis_brand' && data.avoid_lactose_ed === 'yes_lactose_free';"}, {"key": "sildenafil_prev_capsule", "type": "radio", "input": true, "label": "Highest Viagra®/sildenafil capsule you tolerated <em>without side-effects</em>:", "values": [{"label": "25 mg", "value": 25}, {"label": "50 mg", "value": 50}, {"label": "100 mg", "value": 100}, {"label": "Unsure / can’t remember", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Past Viagra®/sildenafil capsule use:", "customConditional": "show = data.past_ed_medication_use === 'yes' && data.rx_brand_preference_ed === 'viagra_brand' && data.avoid_lactose_ed === 'yes_lactose_free';"}, {"key": "brand_vs_generic_ed", "type": "radio", "input": true, "label": "Do you prefer branded or generic tablets?", "values": [{"label": "Generic (lower cost)", "value": "generic"}, {"label": "Branded (original brand-name)", "value": "branded"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Brand preference:", "customConditional": "show = data.avoid_lactose_ed && data.avoid_lactose_ed.startsWith('no_lactose') && (data.rx_brand_preference_ed === 'cialis_brand' || data.rx_brand_preference_ed === 'viagra_brand');"}, {"key": "cialis_lf_dose_first", "type": "radio", "input": true, "label": "Please select your preferred Cialis® lactose-free capsule strength", "values": [{"label": "3 mg", "value": 3}, {"label": "5 mg", "value": 5}], "validate": {"required": true}, "tableView": true, "confirm_label": "Preferred Cialis® lactose-free dose:", "customConditional": "show = data.rx_brand_preference_ed === 'cialis_brand' && data.avoid_lactose_ed === 'yes_lactose_free' && data.past_ed_medication_use === 'no';"}, {"key": "sildenafil_lf_dose_first", "type": "radio", "input": true, "label": "Please select your preferred Viagra® lactose-free capsule strength", "values": [{"label": "25 mg", "value": 25}], "validate": {"required": true}, "tableView": true, "confirm_label": "Preferred Viagra® lactose-free dose:", "customConditional": "show = data.rx_brand_preference_ed === 'viagra_brand' && data.avoid_lactose_ed === 'yes_lactose_free' && data.past_ed_medication_use === 'no';"}, {"key": "cialis_repeat_3or5", "type": "radio", "input": true, "label": "Please select your preferred Cialis® lactose-free capsule strength", "values": [{"label": "3 mg", "value": 3}, {"label": "5 mg", "value": 5}, {"label": "6 mg (one-step increase)", "value": 6}], "validate": {"required": true}, "tableView": true, "confirm_label": "Preferred Cialis® lactose-free dose:", "customConditional": "show = data.rx_brand_preference_ed==='cialis_brand' && data.avoid_lactose_ed==='yes_lactose_free' && data.past_ed_medication_use==='yes' && ['3','5'].includes(String(data.cialis_prev_capsule));"}, {"key": "cialis_repeat_6", "type": "radio", "input": true, "label": "Please select your preferred Cialis® lactose-free capsule strength", "values": [{"label": "3 mg", "value": 3}, {"label": "5 mg", "value": 5}, {"label": "6 mg", "value": 6}, {"label": "10 mg (one-step increase)", "value": 10}], "validate": {"required": true}, "tableView": true, "confirm_label": "Preferred Cialis® lactose-free dose:", "customConditional": "show = data.rx_brand_preference_ed==='cialis_brand' && data.avoid_lactose_ed==='yes_lactose_free' && data.past_ed_medication_use==='yes' && String(data.cialis_prev_capsule)==='6';"}, {"key": "cialis_repeat_10plus", "type": "radio", "input": true, "label": "Please select your preferred Cialis® lactose-free capsule strength", "values": [{"label": "3 mg", "value": 3}, {"label": "5 mg", "value": 5}, {"label": "6 mg", "value": 6}, {"label": "10 mg", "value": 10}, {"label": "20 mg ", "value": 20}], "validate": {"required": true}, "tableView": true, "confirm_label": "Preferred Cialis® lactose-free dose:", "customConditional": "show = data.rx_brand_preference_ed==='cialis_brand' && data.avoid_lactose_ed==='yes_lactose_free' && data.past_ed_medication_use==='yes' && ['10','20'].includes(String(data.cialis_prev_capsule));"}, {"key": "sildenafil_repeat_25", "type": "radio", "input": true, "label": "Please select your preferred Viagra® lactose-free capsule strength", "values": [{"label": "25 mg", "value": 25}, {"label": "50 mg (one-step increase)", "value": 50}], "validate": {"required": true}, "tableView": true, "confirm_label": "Preferred Viagra® lactose-free dose:", "customConditional": "show = data.rx_brand_preference_ed==='viagra_brand' && data.avoid_lactose_ed==='yes_lactose_free' && data.past_ed_medication_use==='yes' && String(data.sildenafil_prev_capsule)==='25';"}, {"key": "sildenafil_repeat_50", "type": "radio", "input": true, "label": "Please select your preferred Viagra® lactose-free capsule strength", "values": [{"label": "25 mg", "value": 25}, {"label": "50 mg", "value": 50}, {"label": "100 mg (one-step increase)", "value": 100}], "validate": {"required": true}, "tableView": true, "confirm_label": "Preferred Viagra® lactose-free dose:", "customConditional": "show = data.rx_brand_preference_ed==='viagra_brand' && data.avoid_lactose_ed==='yes_lactose_free' && data.past_ed_medication_use==='yes' && String(data.sildenafil_prev_capsule)==='50';"}, {"key": "sildenafil_repeat_100", "type": "radio", "input": true, "label": "Please select your preferred Viagra® lactose-free capsule strength", "values": [{"label": "25 mg", "value": 25}, {"label": "50 mg", "value": 50}, {"label": "100 mg (highest strength)", "value": 100}], "validate": {"required": true}, "tableView": true, "confirm_label": "Preferred Viagra® lactose-free dose:", "customConditional": "show = data.rx_brand_preference_ed==='viagra_brand' && data.avoid_lactose_ed==='yes_lactose_free' && data.past_ed_medication_use==='yes' && String(data.sildenafil_prev_capsule)==='100';"}, {"key": "can_request_stronger_notice", "html": "<p><em>You can request a higher strength in future if you tolerate the starter dose without side-effects.</em></p>", "type": "content", "input": false, "customConditional": "show = data.past_ed_medication_use === 'no';"}, {"key": "vardenafil_generic_dose", "type": "radio", "input": true, "label": "Please select your preferred Vardenafil tablet strength (generic only)", "values": [{"label": "10 mg", "value": "vardenafil_generic_10"}, {"label": "20 mg", "value": "vardenafil_generic_20"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Preferred Vardenafil dose (generic only):", "customConditional": "show = data.rx_brand_preference_ed === 'levitra_brand' && data.avoid_lactose_ed === 'no_lactose_ok';"}, {"key": "cialis_gen_first", "type": "radio", "input": true, "label": "Please confirm your preferred Generic tadalafil tablet strength", "values": [{"label": "5 mg", "value": 5}], "validate": {"required": true}, "tableView": true, "confirm_label": "Preferred Generic tadalafil dose:", "customConditional": "show = data.rx_brand_preference_ed==='cialis_brand' && data.avoid_lactose_ed==='no_lactose_ok' && data.brand_vs_generic_ed==='generic' && data.past_ed_medication_use==='no';"}, {"key": "cialis_gen_5", "type": "radio", "input": true, "label": "Please select your preferred Generic tadalafil tablet strength", "values": [{"label": "5 mg", "value": 5}, {"label": "10 mg (one-step up)", "value": 10}], "validate": {"required": true}, "tableView": true, "confirm_label": "Preferred Generic tadalafil dose:", "customConditional": "show = data.rx_brand_preference_ed==='cialis_brand' && data.avoid_lactose_ed==='no_lactose_ok' && data.brand_vs_generic_ed==='generic' && ['3','5'].includes(String(data.cialis_prev_tablet));"}, {"key": "cialis_gen_10", "type": "radio", "input": true, "label": "Please select your preferred generic tadalafil tablet strength", "values": [{"label": "5 mg", "value": 5}, {"label": "10 mg", "value": 10}, {"label": "20 mg (one-step up)", "value": 20}], "validate": {"required": true}, "tableView": true, "confirm_label": "Preferred Generic tadalafil dose:", "customConditional": "show = data.rx_brand_preference_ed==='cialis_brand' && data.avoid_lactose_ed==='no_lactose_ok' && data.brand_vs_generic_ed==='generic' && ['6','10'].includes(String(data.cialis_prev_tablet));"}, {"key": "cialis_gen_20", "type": "radio", "input": true, "label": "Please select your preferred generic tadalafil tablet strength", "values": [{"label": "5 mg", "value": 5}, {"label": "10 mg", "value": 10}, {"label": "20 mg (highest strength)", "value": 20}], "validate": {"required": true}, "tableView": true, "confirm_label": "Preferred Generic tadalafil dose:", "customConditional": "show = data.rx_brand_preference_ed==='cialis_brand' && data.avoid_lactose_ed==='no_lactose_ok' && data.brand_vs_generic_ed==='generic' && String(data.cialis_prev_tablet)==='20';"}, {"key": "sild_gen_first", "type": "radio", "input": true, "label": "Please select your preferred generic sildenafil tablet strength", "values": [{"label": "25 mg", "value": 25}], "validate": {"required": true}, "tableView": true, "confirm_label": "Preferred generic sildenafil dose:", "customConditional": "show = data.rx_brand_preference_ed==='viagra_brand' && data.avoid_lactose_ed==='no_lactose_ok' && data.brand_vs_generic_ed==='generic' && data.past_ed_medication_use==='no';"}, {"key": "sild_gen_25", "type": "radio", "input": true, "label": "Please select your preferred generic sildenafil tablet strength", "values": [{"label": "25 mg", "value": 25}, {"label": "50 mg (one-step up)", "value": 50}], "validate": {"required": true}, "tableView": true, "confirm_label": "Preferred generic sildenafil dose:", "customConditional": "show = data.rx_brand_preference_ed==='viagra_brand' && data.avoid_lactose_ed==='no_lactose_ok' && data.brand_vs_generic_ed==='generic' && String(data.sildenafil_prev_tablet)==='25';"}, {"key": "sild_gen_50", "type": "radio", "input": true, "label": "Please select your preferred generic sildenafil tablet strength", "values": [{"label": "25 mg", "value": 25}, {"label": "50 mg", "value": 50}, {"label": "100 mg (one-step up)", "value": 100}], "validate": {"required": true}, "tableView": true, "confirm_label": "Preferred generic sildenafil dose:", "customConditional": "show = data.rx_brand_preference_ed==='viagra_brand' && data.avoid_lactose_ed==='no_lactose_ok' && data.brand_vs_generic_ed==='generic' && String(data.sildenafil_prev_tablet)==='50';"}, {"key": "sild_gen_100", "type": "radio", "input": true, "label": "Please select your preferred generic sildenafil tablet strength", "values": [{"label": "25 mg", "value": 25}, {"label": "50 mg", "value": 50}, {"label": "100 mg (highest strength)", "value": 100}], "validate": {"required": true}, "tableView": true, "confirm_label": "Preferred generic sildenafil dose:", "customConditional": "show = data.rx_brand_preference_ed==='viagra_brand' && data.avoid_lactose_ed==='no_lactose_ok' && data.brand_vs_generic_ed==='generic' && String(data.sildenafil_prev_tablet)==='100';"}, {"key": "vardenafil_first", "type": "radio", "input": true, "label": "Please select your preferred Vardenafil tablet strength", "values": [{"label": "10 mg", "value": 10}], "validate": {"required": true}, "tableView": true, "confirm_label": "Preferred Vardenafil dose:", "customConditional": "show = data.rx_brand_preference_ed==='levitra_brand' && data.past_ed_medication_use==='no';"}, {"key": "vardenafil_repeat", "type": "radio", "input": true, "label": "Please select your preferred Vardenafil tablet strength", "values": [{"label": "10 mg", "value": 10}, {"label": "20 mg (one-step increase)", "value": 20}], "validate": {"required": true}, "tableView": true, "confirm_label": "Preferred Vardenafil dose:", "customConditional": "show = (() => {\n  if (!(data.rx_brand_preference_ed==='levitra_brand' && data.past_ed_medication_use==='yes')) return false;\n  const prev = String(data.vardenafil_prev_tablet||'');\n  if (prev==='20'){ this.values = this.values.filter(v=>v.value===20); return true; }\n  return true; // prev 10 mg or unsure → show both 10 & 20\n})();"}, {"key": "cialis_brand_first", "type": "radio", "input": true, "label": "Please select your preferred branded Cialis® tablet strength", "values": [{"label": "5 mg", "value": 5}], "validate": {"required": true}, "tableView": true, "confirm_label": "Preferred Cialis® dose:", "customConditional": "show = data.rx_brand_preference_ed==='cialis_brand' && data.avoid_lactose_ed==='no_lactose_ok' && data.brand_vs_generic_ed==='branded' && data.past_ed_medication_use==='no';"}, {"key": "cialis_brand_5", "type": "radio", "input": true, "label": "Please select your preferred branded Cialis® tablet strength", "values": [{"label": "5 mg", "value": 5}, {"label": "10 mg (one-step up)", "value": 10}], "validate": {"required": true}, "tableView": true, "confirm_label": "Preferred Cialis® dose:", "customConditional": "show = data.rx_brand_preference_ed==='cialis_brand' && data.avoid_lactose_ed==='no_lactose_ok' && data.brand_vs_generic_ed==='branded' && ['3','5'].includes(String(data.cialis_prev_tablet));"}, {"key": "cialis_brand_10", "type": "radio", "input": true, "label": "Please select your preferred branded Cialis® tablet strength", "values": [{"label": "5 mg", "value": 5}, {"label": "10 mg", "value": 10}, {"label": "20 mg (one-step up)", "value": 20}], "validate": {"required": true}, "tableView": true, "confirm_label": "Preferred Cialis® dose:", "customConditional": "show = data.rx_brand_preference_ed==='cialis_brand' && data.avoid_lactose_ed==='no_lactose_ok' && data.brand_vs_generic_ed==='branded' && ['6','10'].includes(String(data.cialis_prev_tablet));"}, {"key": "cialis_brand_20", "type": "radio", "input": true, "label": "Please select your preferred branded Cialis® tablet strength", "values": [{"label": "5 mg", "value": 5}, {"label": "10 mg", "value": 10}, {"label": "20 mg (highest strength)", "value": 20}], "validate": {"required": true}, "tableView": true, "confirm_label": "Preferred Cialis® dose:", "customConditional": "show = data.rx_brand_preference_ed==='cialis_brand' && data.avoid_lactose_ed==='no_lactose_ok' && data.brand_vs_generic_ed==='branded' && String(data.cialis_prev_tablet)==='20';"}, {"key": "sild_brand_first", "type": "radio", "input": true, "label": "Please select your preferred branded Viagra® tablet strength", "values": [{"label": "25 mg", "value": 25}], "validate": {"required": true}, "tableView": true, "confirm_label": "Preferred Viagra® dose:", "customConditional": "show = data.rx_brand_preference_ed==='viagra_brand' && data.avoid_lactose_ed==='no_lactose_ok' && data.brand_vs_generic_ed==='branded' && data.past_ed_medication_use==='no';"}, {"key": "sild_brand_25", "type": "radio", "input": true, "label": "Please select your preferred branded Viagra® tablet strength", "values": [{"label": "25 mg", "value": 25}, {"label": "50 mg (one-step increase)", "value": 50}], "validate": {"required": true}, "tableView": true, "confirm_label": "Preferred Viagra® dose:", "customConditional": "show = data.rx_brand_preference_ed==='viagra_brand' && data.avoid_lactose_ed==='no_lactose_ok' && data.brand_vs_generic_ed==='branded' && String(data.sildenafil_prev_tablet)==='25';"}, {"key": "sild_brand_50", "type": "radio", "input": true, "label": "Please select your preferred branded Viagra® tablet strength", "values": [{"label": "25 mg", "value": 25}, {"label": "50 mg", "value": 50}, {"label": "100 mg (one-step increase)", "value": 100}], "validate": {"required": true}, "tableView": true, "confirm_label": "Preferred Viagra® dose:", "customConditional": "show = data.rx_brand_preference_ed==='viagra_brand' && data.avoid_lactose_ed==='no_lactose_ok' && data.brand_vs_generic_ed==='branded' && String(data.sildenafil_prev_tablet)==='50';"}, {"key": "sild_brand_100", "type": "radio", "input": true, "label": "Please select your preferred branded Viagra® tablet strength", "values": [{"label": "25 mg", "value": 25}, {"label": "50 mg", "value": 50}, {"label": "100 mg (highest strength)", "value": 100}], "validate": {"required": true}, "tableView": true, "confirm_label": "Preferred Viagra® dose:", "customConditional": "show = data.rx_brand_preference_ed==='viagra_brand' && data.avoid_lactose_ed==='no_lactose_ok' && data.brand_vs_generic_ed==='branded' && String(data.sildenafil_prev_tablet)==='100';"}, {"key": "dose_change_flag", "type": "hidden", "input": true, "persistent": false, "calculateValue": "value = (() => {\n  const n = v => (v===undefined || v===null || v==='unsure') ? null : Number(v);\n\n  /* current choice */\n  const chosen = n([\n    data.cialis_lf_dose_first,\n    data.cialis_lf_repeat_3or5,\n    data.cialis_lf_repeat_6,\n    data.cialis_lf_repeat_10plus,\n    data.cialis_brand_first,\n    data.cialis_brand_5,\n    data.cialis_brand_10,\n    data.cialis_brand_20,\n    data.cialis_gen_first,\n    data.cialis_gen_5,\n    data.cialis_gen_10,\n    data.cialis_gen_20,\n    data.sildenafil_lf_dose_first,\n    data.sildenafil_lf_repeat_25,\n    data.sildenafil_lf_repeat_50,\n    data.sildenafil_lf_repeat_100,\n    data.sild_brand_first,\n    data.sild_brand_25,\n    data.sild_brand_50,\n    data.sild_brand_100,\n    data.sild_gen_first,\n    data.sild_gen_25,\n    data.sild_gen_50,\n    data.sild_gen_100,\n    data.vardenafil_first,\n    data.vardenafil_repeat\n  ].find(v => v !== undefined));\n\n  /* highest previous tolerated */\n  const prev = n(\n    data.cialis_prev_capsule  || data.cialis_prev_tablet ||\n    data.sildenafil_prev_capsule || data.sildenafil_prev_tablet ||\n    data.vardenafil_prev_tablet\n  );\n\n  if (!chosen || !prev) return '';\n  if (chosen > prev) return 'higher';\n  if (chosen < prev) return 'lower';\n  return 'same';\n})();"}, {"key": "dose_increase_reason", "type": "selectboxes", "label": "Why would you like to try a higher dose?", "values": [{"label": "Erection wasn’t hard enough", "value": "insufficient_firmness"}, {"label": "Effect wore off too quickly", "value": "short_duration"}, {"label": "Had trouble achieving an erection at all", "value": "no_response"}, {"label": "Works, but reliability is inconsistent", "value": "inconsistent_effect"}, {"label": "Tolerance developed over time", "value": "tolerance"}, {"label": "Cheaper to split a higher-strength tablet", "value": "cost_split"}, {"label": "Other reason", "value": "other_reason"}], "tableView": true, "confirm_label": "Reason for higher dose:", "customConditional": "show = data.dose_change_flag === 'higher';"}, {"key": "dose_increase_other_text", "type": "textfield", "label": "Other reason (please specify)", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.dose_increase_reason && data.dose_increase_reason.other_reason;"}, {"key": "ack_higher_checkbox", "type": "checkbox", "label": "I’ve chosen a <strong class=\"text-success\">higher</strong> dose — I know this may raise the chance of side-effects.", "validate": {"required": true}, "tableView": true, "customClass": "mt-3", "confirm_label": "Confirms dose is higher than previous:", "customConditional": "show = data.dose_change_flag === 'higher';"}, {"key": "ack_lower_checkbox", "type": "checkbox", "label": "I’ve chosen a <strong class=\"text-success\">lower</strong> dose — I understand it might be less effective.", "validate": {"required": true}, "tableView": true, "customClass": "mt-3", "confirm_label": "Confirms dose is lower than previous:", "customConditional": "show = data.dose_change_flag === 'lower';"}, {"key": "ack_same_checkbox", "type": "checkbox", "label": "I’m happy to stay on the <strong class=\"text-success\">same</strong> dose.", "validate": {"required": true}, "tableView": true, "customClass": "mt-3", "confirm_label": "Confirms dose is the same as previous:", "customConditional": "show = data.dose_change_flag === 'same';"}, {"key": "tablet_split_notice", "html": "<p><strong>Starting dose reminder:</strong> Because this is your first ED-medication trial, we recommend beginning with the <em>lowest</em> tablet dose. Higher-strength tablets can be split with a pill-cutter (e.g.&nbsp;20 mg Cialis® → quarter tablet = 5 mg).</p>", "type": "content", "input": false, "customConditional": "show = data.past_ed_medication_use === 'no' && (['tadalafil_generic_10','tadalafil_generic_20','cialis_brand_10','cialis_brand_20','sildenafil_generic_50','sildenafil_generic_100','viagra_brand_50','viagra_brand_100'].includes(data.cialis_generic_dose) || ['sildenafil_generic_50','sildenafil_generic_100'].includes(data.sildenafil_generic_dose) || ['viagra_brand_50','viagra_brand_100'].includes(data.sildenafil_brand_dose));"}, {"key": "tablet_split_ack", "type": "radio", "input": true, "label": "I will split the tablet to take the lowest starting dose (e.g. ¼ of a 20 mg tablet = 5 mg)", "values": [{"label": "Yes – I will split the tablet", "value": "ack_split"}, {"label": "No – I need a different dose", "value": "decline_split"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Confirm tablet-splitting:", "customConditional": "show = !!data.tablet_split_notice;"}, {"key": "lf_disclaimer_ack", "type": "radio", "input": true, "label": "<p><strong>Please read before continuing:</strong></p><ul><li>Lactose-free capsules are <em>compounded</em> at a licensed Ontario pharmacy.</li><li>The strengths offered differ from standard brand tablets and are therefore <em>off-label</em>.</li><li>You may experience greater or lower side-effect potential than with a standard tablet dose.</li><li><strong>Capsules cannot be split</strong>; they must be swallowed whole.</li></ul>", "values": [{"label": "I understand and wish to proceed", "value": "understand"}, {"label": "I do not understand; need more info", "value": "do_not_understand"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Advice on lactose-free capsules:", "customConditional": "show = data.avoid_lactose_ed === 'yes_lactose_free';"}, {"key": "heading_side_effects_ed", "html": "<br><h4>Typical Side-Effects (ED Tablets & Capsules)</h4><p>Take a quick look at the common side-effects for the option you’ve chosen, then tick the box below if you’re happy to continue.</p>", "type": "content", "input": false, "customConditional": "show = ['cialis_brand','viagra_brand','levitra_brand'].includes(data.rx_brand_preference_ed);"}, {"key": "side_effects_cialis", "html": "<strong>Cialis® / tadalafil</strong><ul><li>Headache – <em>common</em> (≈ 11 %)</li><li>Flushing – <em>common</em> (≈ 4 %)</li><li>Indigestion / heartburn – <em>common</em> (≈ 7 %)</li><li>Nasal congestion – <em>common</em> (≈ 4 %)</li><li>Back pain / muscle aches – <em>uncommon</em> (≈ 4 %)</li><li>Dizziness or blurred vision – <em>rare</em></li><li>Sudden hearing or vision loss – <em>very rare</em>; seek care immediately</li></ul>", "type": "content", "input": false, "customConditional": "show = data.rx_brand_preference_ed === 'cialis_brand';"}, {"key": "side_effects_sildenafil", "html": "<strong>Viagra® / sildenafil</strong><ul><li>Headache – <em>common</em> (≈ 16 %)</li><li>Flushing – <em>common</em> (≈ 11 %)</li><li>Nasal congestion – <em>common</em> (≈ 4 %)</li><li>Indigestion – <em>common</em> (≈ 4 %)</li><li>Visual tint / light sensitivity – <em>uncommon</em> (≈ 3 %)</li><li>Dizziness – <em>uncommon</em> (≈ 2 %)</li><li>Sudden hearing or vision loss – <em>very rare</em>; seek care immediately</li></ul>", "type": "content", "input": false, "customConditional": "show = data.rx_brand_preference_ed === 'viagra_brand';"}, {"key": "side_effects_vardenafil", "html": "<strong>Levitra® / vardenafil</strong><ul><li>Headache – <em>common</em> (≈ 11 %)</li><li>Flushing – <em>common</em> (≈ 11 %)</li><li>Nasal congestion – <em>common</em> (≈ 4 %)</li><li>Indigestion – <em>common</em> (≈ 3 %)</li><li>Dizziness – <em>uncommon</em></li><li>Visual changes – <em>rare</em></li><li>Sudden hearing or vision loss – <em>very rare</em>; seek care immediately</li></ul>", "type": "content", "input": false, "customConditional": "show = data.rx_brand_preference_ed === 'levitra_brand';"}, {"key": "side_effects_ed_ack", "type": "radio", "input": true, "label": "I have reviewed the side-effects above:", "values": [{"label": "✔ I understand these risks and wish to proceed", "value": "understand_proceed"}, {"label": "✖ I’m unsure / need more counselling before proceeding", "value": "need_counselling"}], "validate": {"required": true}, "tableView": true, "customClass": "mt-3", "confirm_label": "Reviews and has understood side effects:", "customConditional": "show = ['cialis_brand','viagra_brand','levitra_brand'].includes(data.rx_brand_preference_ed);", "optionsLabelPosition": "right"}, {"key": "rxts", "type": "textfield", "input": true, "label": "Rx Templates", "hidden": true, "disabled": true, "multiple": true, "adminFlag": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "value=(()=>{const n=v=>(v===undefined||v===null||v===''||v==='unsure')?null:Number(v);const out=[];/* tadalafil */const t=n([data.cialis_lf_dose_first,data.cialis_repeat_3or5,data.cialis_repeat_6,data.cialis_repeat_10plus,data.cialis_gen_first,data.cialis_gen_5,data.cialis_gen_10,data.cialis_gen_20,data.cialis_brand_first,data.cialis_brand_5,data.cialis_brand_10,data.cialis_brand_20].find(v=>v!==undefined));if(t){if(data.avoid_lactose_ed==='yes_lactose_free'){out.push(`tadalafil-${t}mg-lactose-free`);}else{const flav=(data.brand_vs_generic_ed==='branded')?'branded':'generic';out.push(`tadalafil-${t}mg-${flav}`);}}/* sildenafil */const s=n([data.sildenafil_lf_dose_first,data.sildenafil_repeat_25,data.sildenafil_repeat_50,data.sildenafil_repeat_100,data.sild_gen_first,data.sild_gen_25,data.sild_gen_50,data.sild_gen_100,data.sild_brand_first,data.sild_brand_25,data.sild_brand_50,data.sild_brand_100].find(v=>v!==undefined));if(s){if(data.avoid_lactose_ed==='yes_lactose_free'){out.push(`sildenafil-${s}mg-lactose-free`);}else{const flav=(data.brand_vs_generic_ed==='branded')?'branded':'generic';out.push(`sildenafil-${s}mg-${flav}`);}}/* vardenafil (generic only) */const v=n(data.vardenafil_first??data.vardenafil_repeat);if(v)out.push(`vardenafil-${v}mg`);return out;})();", "refreshOnChange": true}]}