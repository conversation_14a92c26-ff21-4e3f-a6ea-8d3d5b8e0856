{"components": [{"key": "content4", "html": "<h2><strong>Instructions</strong></h2><p>To expedite your care, please answer the following questions to the best of your abilities. &nbsp;We ask these questions to provide the best possible care. If you have any questions, please list them in the space provided.", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "purpose_of_consultation", "html": "<h2 style=\"text-align:center;\"><strong>Purpose of Consultation</strong></h2>", "type": "content", "input": false, "label": "Purpose of Consultation", "tableView": false, "refreshOnChange": false}, {"key": "inr_testing_indication", "type": "selectboxes", "input": true, "label": "Please select your reason(s) for INR testing:", "values": [{"label": "Routine Monitoring", "value": "routine_inr_monitoring", "shortcut": ""}, {"label": "Recent Warfarin Dose Change", "value": "recent_change_warfarin_dose", "shortcut": ""}, {"label": "Recent Dietary Change", "value": "recent_dietary_changes", "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "confirm_label": "Reason(s) for testing:", "optionsLabelPosition": "right"}, {"key": "stated_reason_for_interested_inr_other", "type": "textarea", "input": true, "label": "As you selected other, please specify your reason below:", "validate": {"required": true}, "tableView": true, "autoExpand": false, "customConditional": "show = data.inr_testing_indication.other;"}, {"key": "warfarin_indication", "type": "selectboxes", "input": true, "label": "What is your reason for being on warfarin:", "values": [{"label": "Mechanical heart valve", "value": "mechanical_valve", "shortcut": ""}, {"label": "Atrial Fibrillation", "value": "atrial_fibrillation", "shortcut": ""}, {"label": "Previous Pulmonary Embolus (PE)", "value": "pe", "shortcut": ""}, {"label": "Previous Deep Vein Thrombosis (DVT)", "value": "dvt", "shortcut": ""}, {"label": "Severe liver disease", "value": "liver disease", "shortcut": ""}, {"label": "Severe kidney disease", "value": "kidney disease", "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "optionsLabelPosition": "right"}, {"key": "stated_reason_for_warfarin_other", "type": "textarea", "input": true, "label": "As you selected other, please specify your reason below:", "validate": {"required": true}, "tableView": true, "autoExpand": false, "customConditional": "show = data.warfarin_indication.other;"}, {"key": "heading_administration", "html": "<h3>General Medication Questions&nbsp;</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "medication_consistency", "type": "radio", "input": true, "label": "Do you take your medication every day?", "inline": false, "values": [{"label": "I take my pills regularly", "value": "takes_consistently", "shortcut": ""}, {"label": "I sometimes miss pills", "value": "takes_inconsistently", "shortcut": ""}, {"label": "I don't understand this question", "value": "doesn't_understand", "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "medication_consistency_other", "type": "textarea", "input": true, "label": "As you selected other, please specify your reason below:", "validate": {"required": true}, "tableView": true, "autoExpand": false, "customConditional": "show = data.medication_consistency == 'other';"}, {"key": "single_multi_pill_regimen", "type": "radio", "input": true, "label": "Do you take the same dose of warfarin every day, or take different doses on different days?", "inline": false, "values": [{"label": "Same daily dose", "value": "same", "shortcut": ""}, {"label": "Different doses on different days", "value": "multi-pill", "shortcut": ""}], "tableView": true, "optionsLabelPosition": "right"}, {"key": "current_inr_dosing", "data": {"values": [{"label": "2 mg daily", "value": "2mg"}, {"label": "2.5 mg daily", "value": "2.5mg"}, {"label": "3 mg daily", "value": "3.5mg"}, {"label": "3.5 mg daily", "value": "3.5mg"}, {"label": "4 mg daily", "value": "4mg"}, {"label": "4.5 mg daily", "value": "4.5mg"}, {"label": "5 mg daily", "value": "5mg"}, {"label": "5.5 mg daily", "value": "5.5mg"}, {"label": "6 mg daily", "value": "6mg"}, {"label": "6.5 mg daily", "value": "6.5mg"}, {"label": "7 mg daily", "value": "7mg"}, {"label": "7.5 mg daily", "value": "7.5mg"}, {"label": "Other", "value": "other"}]}, "type": "select", "input": true, "label": "What is your current warfarin dose?", "widget": "html5", "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "current_dose_other", "type": "textarea", "input": true, "label": "As you selected other, please specify your daily dose below:", "validate": {"required": true}, "tableView": true, "autoExpand": false, "customConditional": "show = data.current_inr_dosing == 'other';"}, {"key": "multi_pill_regimen_description", "type": "textarea", "input": true, "label": "Please state your current pill regimen (i.e. I take 3 mg 4 days per week, and 4 mg 3 days per week)", "tableView": true, "autoExpand": false, "customConditional": "show = data.single_multi_pill_regimen == 'multi-pill';"}, {"key": "duration_current_dose", "type": "radio", "input": true, "label": "How long have you been taking your current daily dose?", "inline": false, "values": [{"label": "<7 days", "value": "<7_days"}, {"label": "7-14 days", "value": "7-14_days"}, {"label": "14-30 days", "value": "14-30_days"}, {"label": "30+ days", "value": "30+_days"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "heading_compliance", "html": "<h3>Medication Compliance&nbsp;</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "missed_days_in_last_week", "data": {"values": [{"label": "Never", "value": "never"}, {"label": "1 day / week", "value": "1_day"}, {"label": "2 days / week", "value": "2_days"}, {"label": "3 days / week", "value": "3_days"}, {"label": "4 days / week", "value": "4_days"}, {"label": "5 days / week", "value": "5_days"}, {"label": "6 days / week", "value": "6_days"}, {"label": "7 days / week", "value": "7_days"}]}, "type": "select", "input": true, "label": "How many days on average do you forget to take your pills?", "widget": "html5", "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "heading_prior_lab_testing", "html": "<h3>Previous Lab Testing&nbsp;</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "last_inr_level", "data": {"values": [{"label": "1-4 days ago", "value": "1-4_days ago"}, {"label": "4-7 days ago", "value": "4-7_days"}, {"label": "8-14 days ago", "value": "8-14_days_ago"}, {"label": "14-30 days ago", "value": "14_30_days_ago"}, {"label": "1-3 months ago", "value": "1-3_months_ago"}, {"label": "3-6 months ago", "value": "3-6_months_ago"}, {"label": "6-12 months ago", "value": "6_12_months_ago"}, {"label": "12+ months", "value": "12+_months"}]}, "type": "select", "input": true, "label": "When was your last INR level completed?", "widget": "html5", "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "testing_frequency", "type": "radio", "input": true, "label": "How frequently have you been testing your INR?", "inline": false, "values": [{"label": "Every couple of weeks", "value": "biweekly", "shortcut": ""}, {"label": "Once a month", "value": "monthly", "shortcut": ""}, {"label": "Every 1-3 months", "value": "1-3_months", "shortcut": ""}, {"label": "Every 3-6 months", "value": "3-6_months", "shortcut": ""}, {"label": "Once a year", "value": "annual", "shortcut": ""}, {"label": "I don't understand this question", "value": "doesn't_understand", "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "testing_frequency_other", "type": "textarea", "input": true, "label": "As you selected other, please specify your reason below:", "validate": {"required": true}, "tableView": true, "autoExpand": false, "customConditional": "show = data.testing_frequency == 'other';"}, {"key": "side_effect_heading", "html": "<h3>Medication Side Effects&nbsp;</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "bleeding_above_baseline", "type": "radio", "input": true, "label": "It is normal to have some mild bruising while on warfarin.  Are you experiencing any bruising above your baseline (normal)?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}, {"label": "I don't understand this question", "value": "doesn't_understand", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "cardiac_screening", "type": "radio", "input": true, "label": "Are you experiencing any chest pain/pressure, shortness of breath or lightheadedness/dizziness when sitting or with light walking?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}, {"label": "I don't understand this question", "value": "did_not_understand", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "VTE_screening_limb_swelling", "type": "radio", "input": true, "label": "Have you noticed any recent swelling in one of your arms or legs?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}, {"label": "I don't understand this question", "value": "did_not_understand", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "PE_screening", "type": "radio", "input": true, "label": "Have you noticed if you are coughing up blood at any time?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}, {"label": "I don't understand this question", "value": "did_not_understand", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "gi_bleeding", "type": "radio", "input": true, "label": "Have you noticed any bright red blood in your stool OR a black/tar like consistency?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}, {"label": "I don't understand this question", "value": "did_not_understand", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "heading_interactions", "html": "<h3>Medication Interactions&nbsp;</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "diet_changes", "type": "radio", "input": true, "label": "Have you made any recent changes to your diet to increase or decrease foods rich in Vitamin K (green leafy vegetables, soybean and canola oil, etc)?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}, {"label": "I don't understand this question", "value": "did_not_understand", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "medication_interactions", "type": "radio", "input": true, "label": "Have you been on any of the following medications within the last 4 weeks?", "inline": false, "values": [{"label": "Antibiotics [Trimethoprim/sulfamethoxazole (TMP/SMX), Ciprofloxacin, Levofloxacin, Metronidazole, Azithromycin, and Clarithromycin]", "value": "antibiotics", "shortcut": ""}, {"label": "Antifungals [i.e. Fluconazole]", "value": "antifungals", "shortcut": ""}, {"label": "Anti-epileptics [i.e. <PERSON>, <PERSON>enytoin, Primidone]", "value": "seizure_medication", "shortcut": ""}, {"label": "St. John's Wort", "value": "st_johns_wort", "shortcut": ""}, {"label": "None of the above", "value": "no_antibiotics_antifungals_seizure_st_johns", "shortcut": ""}, {"label": "I don't understand this question", "value": "did_not_understand", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "stated_other_symptoms", "type": "textarea", "input": true, "label": "Please use this area to describe any symptoms that we haven't discussed above that you feel is relevant to your story.  Please leave this section blank if you have no other concerns.", "tableView": true, "autoExpand": false, "placeholder": "No other relevant concerns"}]}