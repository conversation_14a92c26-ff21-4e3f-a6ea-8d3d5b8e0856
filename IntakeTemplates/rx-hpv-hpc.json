{"components": [{"key": "content", "html": "<h2>Instructions</h2><p>Please complete the following questionnaire about your interest in Gardasil 9 (HPV) Vaccine.</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "vaccine_indication", "type": "radio", "input": true, "label": "What reason do you want Gardasil 9 vaccination?", "inline": false, "values": [{"label": "Protect against future strains of HPV from new sexual partners", "value": "protect_against_future__partnesr", "shortcut": ""}, {"label": "Protect against future strains of HPV from occupational exposure (i.e. healthcare)", "value": "protect_hpv_occupational_exposure", "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "vaccine_indication_other", "type": "textarea", "input": true, "label": "You selected 'other' - please specify your reason for HPV vaccination:", "tableView": true, "autoExpand": false, "customConditional": "show = data.vaccine_indication == 'other';"}, {"key": "gardasil_4_vaccinated", "type": "radio", "input": true, "label": "Have you had <PERSON><PERSON><PERSON><PERSON> 4 before?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No/I don't know", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "complete_gardasil_4_vaccine", "type": "radio", "input": true, "label": "Did you complete a full series of your Gardasil 4 vaccine (2 doses)?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No/Don't Know", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.gardasil_4_vaccinated == true;", "optionsLabelPosition": "right"}, {"key": "prior_gard_9_doses", "type": "radio", "input": true, "label": "Have you had any doses of Gardasil 9 before?", "inline": false, "values": [{"label": "Yes/Don't know", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "gardasil_records", "type": "radio", "input": true, "label": "Do you have access or can you obtain access to your vaccine records?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.prior_gard_9_doses == true;", "optionsLabelPosition": "right"}, {"key": "gardasil_9_record_confirmation", "type": "radio", "input": true, "label": "Please review your vaccine records and confirm your total number of Gardasil 9 doses recieved.", "inline": false, "values": [{"label": "0 doses", "value": "0_dose", "shortcut": ""}, {"label": "1 injection", "value": "1_dose", "shortcut": ""}, {"label": "2 injections", "value": "2_doses", "shortcut": ""}, {"label": "3 injections", "value": "3_doses", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.gardasil_records == true;", "optionsLabelPosition": "right"}, {"key": "age_at_gardasil_9", "type": "radio", "input": true, "label": "Was your first dose of Gardasil 9 given before the age of 15?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = (data.gardasil_9_record_confirmation == '2_doses');", "optionsLabelPosition": "right"}, {"key": "gardasil_9_before_15_seperated_5_months", "type": "radio", "input": true, "label": "Were your two doses seperated by 5 months?", "inline": false, "values": [{"label": "yes", "value": "doesnt_need_3rd_dose", "shortcut": ""}, {"label": "No", "value": "needs_3rd_dose", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.age_at_gardasil_9 == true;", "optionsLabelPosition": "right"}, {"key": "vaccine_contraindication", "type": "radio", "input": true, "label": "Do you have any of the following?", "values": [{"label": "I have a fever or am recovering from a recent illness", "value": "recent_illness", "shortcut": ""}, {"label": "I'm pregnant", "value": "pregnancy", "shortcut": "", "customConditional": "show = data.sex == 'female';"}, {"label": "None of the above", "value": "no_contraindications", "shortcut": ""}, {"label": "I don't understand this question", "value": "needs_clarification_vaccine_contraindication", "shortcut": ""}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "optionsLabelPosition": "right"}]}