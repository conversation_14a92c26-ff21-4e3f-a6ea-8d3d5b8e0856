{# -----------------  ED INTAKE SUMMARY (mail)  ----------------- #}
{% load template_extras %}

{% with data=questionnaire.hpc.data %}
{% with summary=questionnaire.raw_formio_summary %}
{% with insured=questionnaire.insured_assays.all %}
{% with uninsured=questionnaire.uninsured_assays.all %}

<p>Hi&nbsp;{{ patient.name }},</p>

<p>This is {{ doctor.name }} (CPSO&nbsp;#{{ doctor.cpso_number }}) – clinic phone {{ doctor.phone }}.</p>

<p>
  I’ve reviewed the erectile-dysfunction intake you submitted.
  If anything below looks inaccurate, reply so we can open a secure follow-up chat;
  otherwise we’ll proceed as outlined.
</p>


  {# ——--- RX BLOCK ---—— #}
  <h4 style="font-weight:bold;margin-top:32px;">Prescriptions Issued</h4>
  {% with rxts=questionnaire.rxts.all %}
    {% if rxts %}
      <ul>
        {% for rx in rxts %}
          <li>{{ rx.display_name }}</li>
        {% endfor %}
      </ul>
    {% endif %}
  {% endwith %}

{# ----------  TESTS / INVESTIGATIONS ---------- #}
<h4 style="font-weight:bold;margin-top:32px;">Lab Testing Requested</h4>
{% if insured or uninsured %}
  {% if insured %}
    <p><strong>Insured tests (OHIP-covered)</strong></p>
    <ul>{% for a in insured %}<li>{{ a.name }} – {{ a.test_type }}</li>{% endfor %}</ul>
  {% endif %}
  {% if uninsured %}
    <p><strong>Uninsured tests (private-pay)</strong></p>
    <ul>{% for a in uninsured %}<li>{{ a.name }} – {{ a.test_type }}</li>{% endfor %}</ul>
    <p>Private-pay items can be declined at the lab.</p>
  {% endif %}
{% else %}
  <p>No diagnostic tests required at this stage.</p>
{% endif %}


{# ----------  PLAN ---------- #}
<h3 style="font-weight:bold;margin-top:32px;">Plan</h3>
<ol>
  <li>Review the intake summary below for accuracy.</li>

  <li>
    If a prescription is issued, you can choose:
    <ul>
      <li><strong>Mail delivery</strong> – ships to the address on file (arrives in&nbsp;≈ 1-3 business days).</li>
      <li><strong>Local pharmacy pick-up</strong> – provide your preferred pharmacy and we’ll <em>fax the prescription directly</em> for same-day pick-up (subject to stock).</li>
    </ul>
  </li>

  <li>If a lab requisition is attached, print or download it and take it to the laboratory of your choice.</li>
  <li>Results will upload automatically; we’ll contact you if follow-up is needed.</li>
</ol>


<!-- Health Condition Summary -->
<h4 style="font-weight:bold;margin-top:32px;">Health Summary</h4>
<ul>

  <li><strong>Current Medications:</strong> 
    {% if summary.medications_list and summary.medications_list.c_val %}
      {{ summary.medications_list.c_val }}
    {% else %}
      None
    {% endif %}
  </li>

  <li><strong>Medication Allergies:</strong> 
    {% if summary.medication_allergies_list and summary.medication_allergies_list.c_val %}
      {{ summary.medication_allergies_list.c_val }}
    {% else %}
      None
    {% endif %}
  </li>

  <li><strong>Past Surgeries:</strong> 
    {% if summary.past_surgeries_list and summary.past_surgeries_list.c_val %}
      {{ summary.past_surgeries_list.c_val }}
    {% else %}
      None
    {% endif %}
  </li>

  <li><strong>Health Conditions:</strong> 
    {% if summary.health_conditions_list and summary.health_conditions_list.c_val %}
      {{ summary.health_conditions_list.c_val }}
    {% else %}
      None
    {% endif %}
  </li>
</ul>
<!-- ─────────── CLINICAL SUMMARY ─────────── -->
<h3 style="font-weight:bold;margin-top:32px;">ED History</h3>
<ul class="ms-1">


  <!-- ▶︎ RELEVANT ED HISTORY -->
  <li><strong>ED History</strong>
    <ul>
      {% for item in summary|confirm:"previous_ed_diagnosis,ed_diagnosis_provider,ed_diagnosis_provider_other,ed_diagnosis_timing" %}
        <li>{{ item|safe }}</li>
      {% empty %}<li>None reported.</li>{% endfor %}
    </ul>
  </li>

  <!-- ▶︎ CURRENT ED SYMPTOMS -->
  <li><strong>Current ED Symptoms</strong>
    <ul>
      {% for item in summary|confirm:"ed_symptoms,ed_symptoms_none_explanation,duration_of_ed_symptoms,morning_erections" %}
        <li>{{ item|safe }}</li>
      {% empty %}<li>No ED symptoms selected.</li>{% endfor %}
    </ul>
  </li>

<!-- ▶︎ INTERCOURSE PATTERN & ANATOMIC CHANGES -->
<li><strong>ED Symptom Pattern</strong>
  <ul>
    {% with positives=summary|confirm:"ed_intercourse_pattern,peyronies_symptoms,testicular_symptoms,peyronies_exam_understanding,testicular_exam_understanding" %}

      {# items the patient DID report #}
      {% if positives %}{% for p in positives %}<li>{{ p|safe }}</li>{% endfor %}{% endif %}

      {# friendly negatives driven by “None of the above” check-boxes #}
      {% if data.none_of_the_above_intercourse_pattern %}<li>No intercourse-related symptom pattern reported.</li>{% endif %}
      {% if data.none_of_the_above_peyronies_symptoms %}<li>No penile-shape / Peyronie’s symptoms reported.</li>{% endif %}
      {% if data.none_of_the_above_testicular_symptoms %}<li>No testicular symptoms reported.</li>{% endif %}

      {# fallback when NOTHING applies #}
      {% if not positives and not data.none_of_the_above_intercourse_pattern and not data.none_of_the_above_peyronies_symptoms and not data.none_of_the_above_testicular_symptoms %}
        <li>None reported.</li>
      {% endif %}

    {% endwith %}
  </ul>
</li>

  <!-- ▶︎ PREVIOUS ED-MEDICATION USE -->
  <li><strong>Previous ED-Medication Use</strong>
    <ul>
      {% for item in summary|confirm:"ed_medication_types,dose_sildenafil,satisfaction_sildenafil,sildenafil_se_management,dose_tadalafil,satisfaction_tadalafil,tadalafil_se_management,dose_vardenafil,satisfaction_vardenafil,vardenafil_se_management,other_ed_medications" %}
        <li>{{ item|safe }}</li>
      {% empty %}<li>No prior use reported.</li>{% endfor %}
    </ul>
  </li>

  <!-- ▶︎ TREATMENT PREFERENCES -->
  <li><strong>Treatment Preferences</strong>
    <ul>
      {% for item in summary|confirm:"past_ed_medication_use,rx_brand_preference_ed,avoid_lactose_ed,preferred_cialis_strength,cialis_prev_capsule,cialis_repeat,lf_disclaimer_ack,side_effects_ed_ack" %}
        <li>{{ item|safe }}</li>
      {% empty %}<li>No specific preferences noted.</li>{% endfor %}
    </ul>
  </li>

  <!-- ▶︎ CURRENT MEDICATION CLASSES -->
  <li><strong>Current Medication Classes &amp; Observed Impact</strong>
    <ul>
      {% for item in summary|confirm:"current_medications,current_medications_not_present,correlation_bp_meds,correlation_antidepressants" %}
        <li>{{ item|safe }}</li>
      {% empty %}<li>No relevant medications.</li>{% endfor %}
    </ul>
  </li>

  <!-- ▶︎ DIABETES DETAILS -->
  <li><strong>Diabetes History &amp; Complications</strong>
    <ul>
      {% for item in summary|confirm:"type_of_diabetes,diabetes_complications,diabetes_complications_not_present" %}
        <li>{{ item|safe }}</li>
      {% empty %}<li>No diabetes history.</li>{% endfor %}
    </ul>
  </li>

  <!-- ▶︎ CARDIOVASCULAR SYMPTOMS -->
  <li><strong>Cardiovascular Symptoms</strong>
    <ul>
      {% for item in summary|confirm:"cardiovascular_symptoms,cardiac_symptoms_not_present,chest_pain_onset,chest_pain_triggers,chest_pain_relievers,chest_pain_character,chest_pain_location,chest_pain_radiation,chest_pain_pattern,palpitations_onset,palpitations_rhythm,palpitations_triggers,palpitations_duration,palpitations_associated_symptoms,swelling_location,swelling_sidedness,swelling_onset,swelling_timing,swelling_pitting,dizziness_onset,dizziness_frequency,dizziness_character,dizziness_timing,fainting_loss_consciousness" %}
        <li>{{ item|safe }}</li>
      {% empty %}<li>No cardiovascular symptoms.</li>{% endfor %}
    </ul>
  </li>

  <!-- ▶︎ RESPIRATORY SYMPTOMS -->
  <li><strong>Respiratory Symptoms</strong>
    <ul>
      {% for item in summary|confirm:"respiratory_symptoms,respiratory_symptoms_not_present,cough_type,cough_duration,cough_coughing_blood,cough_feeling_unwell,cough_with_cold,cough_progression,cough_urgent_warning_understanding,sob_triggers,wheezing_timing,wheezing_relief,wheezing_asthma_history" %}
        <li>{{ item|safe }}</li>
      {% empty %}<li>No respiratory symptoms.</li>{% endfor %}
    </ul>
  </li>

  <!-- ▶︎ BLOOD-PRESSURE DATA -->
  <li><strong>Blood-Pressure Monitoring &amp; Readings</strong>
    <ul>
      {% for item in summary|confirm:"systolic_bp,diastolic_bp,bp_reading_date,bp_reading_confirmed,bp_symptoms_check,bp_urgency_advice_understanding,bp_emergency_advice_understanding,bp_low_emergency_advice" %}
        <li>{{ item|safe }}</li>
      {% empty %}<li>No BP data supplied.</li>{% endfor %}
    </ul>
  </li>

  <!-- ▶︎ PREVIOUS ED INVESTIGATIONS -->
  <li><strong>Previous ED Investigations</strong>
    <ul>
      {% for item in summary|confirm:"previous_ed_tests,previous_ed_tests_not_present,results_blood_hormone_levels,results_ultrasound,results_nocturnal_penile_tumescence" %}
        <li>{{ item|safe }}</li>
      {% empty %}<li>No prior investigations.</li>{% endfor %}
    </ul>
  </li>

<!-- ▶︎ LABORATORY HISTORY & OUTSTANDING TESTS -->
<li><strong>Laboratory History &amp; Outstanding Tests</strong>

  {# ── Detect if the patient supplied ANY prior-lab timing ── #}
  {% with has_labs=summary|confirm:"last_a1c_test,last_fasting_glucose_test,last_lipid_profile_test,last_kidney_function_test,last_cbc_test,last_testosterone_test,last_tsh_test" %}
  {% if has_labs %}
    <div class="table-responsive">
      <table class="table table-sm align-middle mb-2">
        <thead class="table-light">
          <tr>
            <th>Assay</th>
            <th>Last&nbsp;Test&nbsp;(timing)</th>
            <th>Most&nbsp;Recent&nbsp;Result&nbsp;/&nbsp;Finding</th>
          </tr>
        </thead>
        <tbody>

          {% with a=summary.last_a1c_test b=summary.recent_a1c_value %}
            {% if a %}<tr><td>HbA1c</td><td>{{ a.answer }}</td><td>{{ b.answer|default:"—" }}</td></tr>{% endif %}
          {% endwith %}

          {% with a=summary.last_fasting_glucose_test b=summary.recent_fbg_value %}
            {% if a %}<tr><td>Fasting&nbsp;Glucose</td><td>{{ a.answer }}</td><td>{{ b.answer|default:"—" }}</td></tr>{% endif %}
          {% endwith %}

          {% with a=summary.last_lipid_profile_test b=summary.lipid_profile_abnormalities %}
            {% if a %}<tr><td>Lipid&nbsp;Profile</td><td>{{ a.answer }}</td><td>{{ b.answer|default:"—" }}</td></tr>{% endif %}
          {% endwith %}

          {% with a=summary.last_kidney_function_test b=summary.prior_kidney_function_value %}
            {% if a %}<tr><td>Kidney&nbsp;Function&nbsp;(eGFR)</td><td>{{ a.answer }}</td><td>{{ b.answer|default:"—" }}</td></tr>{% endif %}
          {% endwith %}

          {% with a=summary.last_cbc_test b=summary.prior_cbc_value %}
            {% if a %}<tr><td>CBC&nbsp;–&nbsp;Hemoglobin</td><td>{{ a.answer }}</td><td>{{ b.answer|default:"—" }}</td></tr>{% endif %}
          {% endwith %}

          {% with a=summary.last_testosterone_test b=summary.recent_testosterone_value %}
            {% if a %}<tr><td>Total&nbsp;Testosterone</td><td>{{ a.answer }}</td><td>{{ b.answer|default:"—" }}</td></tr>{% endif %}
          {% endwith %}

          {% with a=summary.last_tsh_test b=summary.prior_tsh_value %}
            {% if a %}<tr><td>TSH</td><td>{{ a.answer }}</td><td>{{ b.answer|default:"—" }}</td></tr>{% endif %}
          {% endwith %}

        </tbody>
      </table>
    </div>
  {% else %}
    <p class="mb-1">No prior lab results provided.</p>
  {% endif %}
  {% endwith %}

  {# ── Recommended / Overdue tests ── #}
{% if summary.overdue_labs and summary.overdue_labs.answer %}
  <h6 class="mt-2 mb-1">Recommended / Overdue Tests</h6>
  <ul class="ms-1">

    {# list of tests #}
    <li><strong>Recommended lab testing:</strong>
        {{ summary.overdue_labs.answer }}</li>

    {# patient’s requisition choice, phrased for the patient #}
    {% if summary.offer_lab_requisition %}
      {% if summary.offer_lab_requisition.answer == "yes" %}
        <li>You asked that we send a laboratory requisition for these tests.</li>
      {% elif summary.offer_lab_requisition.answer == "no" %}
        <li>You chose to arrange these tests on your own / declined a requisition.</li>
      {% endif %}
    {% endif %}

  </ul>
{% endif %}

</li>

  <!-- ▶︎ IN-OFFICE MONITORING -->
  <li><strong>In-Office Monitoring & Regular Care</strong>
    <ul>
      {% for item in summary|confirm:"last_office_bp_check,clinic_vs_home_reading,last_physical_exam,has_regular_provider" %}
        <li>{{ item|safe }}</li>
      {% empty %}<li>No follow-up information.</li>{% endfor %}
    </ul>
  </li>

  <!-- ▶︎ SYMPTOMS NOT PRESENT -->
  <li><strong>Symptoms NOT Present</strong>
    <ul>
      {% for item in summary|confirm:"gastrointestinal_symptoms_not_present,mental_health_symptoms_not_present" %}
        {% if item %}<li>{{ item|safe }}</li>{% endif %}
      {% empty %}<li>No negative symptoms specifically noted.</li>{% endfor %}
    </ul>
  </li>

</ul>

{# ----------  RECOMMENDATIONS / WARNINGS ---------- #}
{% if data.recommendation_penile_doppler_due_to_curvature or data.cough_urgent_warning or data.mental_health_suicidal_check %}
  <h4>Recommendations</h4>
  <ul>
    {% if data.recommendation_penile_doppler_due_to_curvature %}
      <li>
        Penile Doppler ultrasound advised for curvature –
        {% if data.penile_doppler_curvature_understanding == "understand" %}<strong>understands</strong>{% else %}<strong>follow-up recommended</strong>{% endif %}
      </li>
    {% endif %}
    {% if data.cough_urgent_warning %}
      <li>Cough red-flags: seek same-day care if symptoms worsen.</li>
    {% endif %}
    {% if data.mental_health_suicidal_check == "yes" %}
      <li>Mental-health safety: attend the nearest ED today for suicidal thoughts.</li>
    {% endif %}
  </ul>
{% endif %}


<!-- ─────────── IMPORTANT SAFETY REMINDERS ─────────── -->
<div class="border-start ps-3 ms-1">
  <h5 class="fw-bold">Important Safety Reminders</h5>

  <ul class="list-unstyled">
    <li>⚠️ <strong>Chest Pain:</strong> Stop activity and call 911 if you develop chest pain, shortness of breath, or feel faint.</li>

    <li>🚫 <strong>Drug Interactions:</strong> Never combine ED pills with nitrates or “poppers” — this mix can cause a dangerous drop in blood pressure.</li>

    <li>👁️ <strong>Vision / Hearing Changes:</strong> Sudden loss or blurring of vision or hearing after a dose needs same-day medical attention.</li>

    <li>🍻 <strong>Alcohol & Other Medicines:</strong> Keep alcohol to ≤ 2 drinks and take ED pills at least 4 hours apart from prostate- or blood-pressure medicines to avoid dizziness or fainting.</li>
  </ul>

</div>

<!-- ▶︎ HOW TO TAKE YOUR ED MEDICATION -->
<h6 class="mt-4 mb-1">How to Take Your Medication</h6>

<div class="border-start ps-3">
  <ul class="mb-2">
    <li><strong>Timing:</strong> Swallow 1 tablet about <u>30-60 minutes before sexual activity</u>.</li>

    <li><strong>Maximum frequency:</strong> Do <u>not</u> take more than <u>one dose in a 24-hour period</u> unless specifically instructed.</li>

    <li><strong>Food &amp; drink:</strong> A light snack is fine, but a large or high-fat meal can delay the pill’s effect.  Stay well-hydrated.</li>

    <li><strong>Alcohol:</strong> Limit to ≤ 2 standard drinks; excess alcohol can blunt the effect and raise the risk of dizziness or low blood pressure.</li>

    <li><strong>Other medicines:</strong> Space at least <u>4 hours</u> from blood-pressure or prostate medications that can also lower blood pressure.</li>

    <li><strong>Onset &amp; duration:</strong> Most people feel an effect within 30–60 minutes.  The window of action lasts up to 4 hours for sildenafil / vardenafil and up to 36 hours for tadalafil.</li>

    <li><strong>Missed or extra doses:</strong> If you accidentally take more than one dose, skip any further pills for 24 hours and monitor for dizziness or flushing; seek medical advice if you feel unwell.</li>

    <li><strong>Storage:</strong> Keep tablets in their original container at room temperature, away from heat and moisture.</li>

    <li class="mt-2 fw-bold">Stop and seek same-day medical care if you experience:</li>
    <ul>
      <li>Persistent dizziness, fainting, or a racing heartbeat &gt; 120 bpm</li>
      <li>Sudden vision or hearing changes</li>
      <li>Erection lasting &gt; 4 hours despite rest and no stimulation</li>
      <li>Severe headache, chest pain, or shortness of breath</li>
    </ul>
  </ul>
</div>

<p class="mt-3">
    <strong>Confirmation:</strong>
    By completing and submitting this intake, you confirm that the information provided is accurate to the best of your knowledge, <u>that you have read and understand all advice contained in the intake form</u>, and that you agree to seek emergency or in-person medical care if severe side-effects or new symptoms develop.
  </p>
<p>Best regards,<br>{{ doctor.name }}</p>

{% endwith %}
{% endwith %}
{% endwith %}
{% endwith %}