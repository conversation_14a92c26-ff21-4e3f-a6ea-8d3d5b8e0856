{% load template_extras %}
{% with data=questionnaire.hpc.data %}
{% with summary=questionnaire.raw_formio_summary %}
{% with insured=questionnaire.insured_assays.all %}
{% with uninsured=questionnaire.uninsured_assays.all %}
{% with rxts=questionnaire.rxts.all %}

<!-- Introduction -->
<p>Hi {{ patient.name }},</p>
<p>
    This is {{ doctor.name }} (CPSO #{{ doctor.cpso_number }}) from our clinic. 
    If you have questions about your responses or feel your answers missed any symptoms or conditions, 
    we can arrange for secure real-time messaging. Otherwise, we can proceed with the plan below but I need your confirmation of the following information before I can send the prescription to the pharmacy.
</p>

<!-- Patient Information Section -->
<ul>
    <li><strong>You are not:</strong>
        <ul>
            <li>Currently pregnant</li>
            <li>Experiencing abdominal pain, fevers, chills or feel unwell</li>
            <li>Never had an allergic reaction to Ella</li>
        </ul>
    </li>
</ul>

<!-- =================  MEDICAL SUMMARY  ================= -->
<h2 class="text-center">Medical-Summary</h2>

<!-- Indication & preferences -->
<h4>Reason for request & key preferences</h4>
<ul>
  {% for qa in summary|confirm:"screening_indication,other_screening_indication,confirm_no_symptoms" %}
    <li>{{ qa|replace:": |: <strong>"|safe }}</strong></li>
  {% endfor %}
</ul>

<!-- Current symptoms -->
<h4>Current symptoms</h4>
<ul>
  {# Constitutional #}
  {% with cs_list=summary|confirm:"constitutional_symptoms" %}
  {% if cs_list and not data.none_of_the_above_constitutional_symptoms %}
    <li><strong>Constitutional symptoms</strong>
      <ul>
        {% for qa in cs_list %}
          {% if not "None of the above" in qa %}
            <li>{{ qa|replace:": |: <strong>"|safe }}</strong></li>
          {% endif %}
        {% endfor %}
      </ul>
    </li>
  {% endif %}
  {% endwith %}

  {# Cardiovascular #}
  {% with cv_list=summary|confirm:"cardiovascular_symptoms" %}
  {% if cv_list and not data.none_of_the_above_cardiovascular_symptoms %}
    <li><strong>Heart-related symptoms</strong>
      <ul>
        {% for qa in cv_list %}
          {% if not "None of the above" in qa %}
            <li>{{ qa|replace:": |: <strong>"|safe }}</strong></li>
          {% endif %}
        {% endfor %}
      </ul>
    </li>
  {% endif %}
  {% endwith %}

  {# Respiratory #}
  {% with resp_list=summary|confirm:"respiratory_symptoms" %}
  {% if resp_list and not data.none_of_the_above_respiratory_symptoms %}
    <li><strong>Breathing symptoms</strong>
      <ul>
        {% for qa in resp_list %}
          {% if not "None of the above" in qa %}
            <li>{{ qa|replace:": |: <strong>"|safe }}</strong></li>
          {% endif %}
        {% endfor %}
      </ul>
    </li>
  {% endif %}
  {% endwith %}

  {# Abdominal/GI #}
  {% with gi_list=summary|confirm:"abdominal_gastrointestinal_symptoms" %}
  {% if gi_list and not data.none_of_the_above_abdominal_gastrointestinal_symptoms %}
    <li><strong>Abdominal / GI symptoms</strong>
      <ul>
        {% for qa in gi_list %}
          {% if not "None of the above" in qa %}
            <li>{{ qa|replace:": |: <strong>"|safe }}</strong></li>
          {% endif %}
        {% endfor %}
      </ul>
    </li>
  {% endif %}
  {% endwith %}
</ul>
<!-- Vitamin D testing history -->
<h4>Previous vitamin D testing</h4>
<ul>
  {% for qa in summary|confirm:"previous_vit_d_test,last_vit_d_test,last_vit_d_results,stable_vit_d_bloodwork" %}
    <li>{{ qa|replace:": |: <strong>"|safe }}</strong></li>
  {% endfor %}
</ul>

<!-- Supplement history -->
<h4>Vitamin D supplementation history</h4>
<ul>
  {% for qa in summary|confirm:"currently_on_meds,current_vit_d_medication,current_vit_d_dosing,current_dose_other,duration_current_dose,previously_on_meds,past_vit_d_medication,last_time_took_medication,reason_stopping,stopped_med_other,current_medication_consistency" %}
    <li>{{ qa|replace:": |: <strong>"|safe }}</strong></li>
  {% endfor %}
</ul>

<!-- Risk factors & related history -->
<h4>Risk factors & related history</h4>
<ul>
  {% for qa in summary|confirm:"hx_low_vit_d,greater_2000_iu_vitamin_d_daily,vit_d_risk_medical_conditions,vit_d_risk_medical_procedures,vit_d_risk_medications,fitzpatrick_type,clothing_religious_garb" %}
    <li>{{ qa|replace:": |: <strong>"|safe }}</strong></li>
  {% endfor %}
</ul>


<!-- Plan (prescriptions if any) -->
<h4>Plan</h4>
{% if questionnaire.rxts.all %}
  <p><strong>Prescriptions:</strong></p>
  <ul>
    {% for rx in questionnaire.rxts.all %}
      <li>{{ rx.display_name }}</li>
    {% endfor %}
  </ul>
{% else %}
  <p><em>No prescriptions issued yet.</em></p>
{% endif %}

<!-- Advice Section -->
<h5>Advice</h5>
<p>
    You confirm that you have reviewed and understood the advice provided in the intake form about 
    Vitamin D supplementation and testing.  
    This includes:  
</p>
<ul>
  <li>Typical daily supplementation (800 - 2000 IU for most adults)</li>
  <li>Alternative dosing if forgetful (e.g., 10,000 IU weekly or 50,000 IU monthly)</li>
  <li>That routine vitamin D testing is not usually required unless risk factors are present</li>
  <li>That vitamin D testing is not covered by OHIP under most circumstances</li>
</ul>

<!-- Plan Section -->
<h5>Plan</h5>
{% if rxts %}
<p><strong>Prescriptions:</strong></p>
<ul>
    {% for rx in rxts %}
    <li>{{ rx.display_name }}</li>
    {% endfor %}
</ul>
{% else %}
<p>No prescriptions have been issued at this time.</p>
{% endif %}

<!-- Instructions Section -->
<h5>Instructions</h5>
<ol>
    <li>
        <strong>Before Testing:</strong>
        <ul>
            <li>Vitamin D levels should only be checked after you have been taking a consistent dose for at least <strong>3 months</strong> (about 100 days). Testing earlier will not reflect your current supplementation.</li>
            <li>You do <strong>not</strong> need to fast before this test — it can be done at any time of day.</li>
        </ul>
    </li>
    <li>
        <strong>During the Test:</strong>
        <ul>
            <li>The correct test is <strong>25-hydroxyvitamin D (25-OH D)</strong>. This is the standard way to measure vitamin D stores in your body.</li>
            <li>Other vitamin D - related tests (like calcitriol/1,25-OH D) are not recommended for routine monitoring.</li>
        </ul>
    </li>
    <li>
        <strong>After Testing:</strong>
        <ul>
            <li>Results are typically available in a few days. Your levels will be interpreted alongside your current supplement dose.</li>
            <li>If your levels are <strong>above 75 nmol/L</strong>, no change may be needed. If low, we may recommend adjusting your dose or recommending a short course of high-dose vitamin D.</li>
        </ul>
    </li>
</ol>


<!-- Footer -->
<p>Best regards,<br>{{ doctor.name }}</p>

{% endwith %}
{% endwith %}
{% endwith %}
{% endwith %}
{% endwith %}