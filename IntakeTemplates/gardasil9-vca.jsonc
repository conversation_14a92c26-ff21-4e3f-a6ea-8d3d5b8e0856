{"components": [{"key": "prior_gardasil_type", "type": "radio", "input": true, "label": "Have you previously received any Gardasil vaccine doses?", "confirm_label": "Prior Gardasil vaccine received:", "values": [{"label": "None", "value": "none"}, {"label": "Gardasil 4", "value": "gardasil_4"}, {"label": "Gardasil 9", "value": "gardasil_9"}], "optionsLabelPosition": "right", "validate": {"required": true}, "tableView": true}, {"key": "gardasil_9_doses", "type": "radio", "input": true, "label": "How many Gardasil 9 doses have you received so far?", "confirm_label": "Gardasil 9 doses received:", "values": [{"label": "1 dose", "value": 1}, {"label": "2 doses", "value": 2}, {"label": "3 doses", "value": 3}, {"label": "Unsure", "value": "unsure"}], "optionsLabelPosition": "right", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.prior_gardasil_type === 'gardasil_9';"}, {"key": "started_before_15", "type": "radio", "input": true, "label": "Did you start (receive both doses) before your 15th birthday?", "confirm_label": "Both doses before age 15:", "values": [{"label": "Yes", "value": true}, {"label": "No", "value": false}, {"label": "Unsure", "value": "unsure"}], "optionsLabelPosition": "right", "validate": {"required": true}, "tableView": true, "customConditional": "show = (data.prior_gardasil_type === 'gardasil_9' && data.gardasil_9_doses == 2);"}, {"key": "can_obtain_records", "type": "radio", "input": true, "label": "Are you able to obtain your previous vaccination records?", "confirm_label": "Can obtain records:", "values": [{"label": "Yes, I can get them", "value": true}, {"label": "No, I can't get them", "value": false}], "optionsLabelPosition": "right", "validate": {"required": true}, "tableView": true, "customConditional": "show = (data.prior_gardasil_type === 'gardasil_9' && data.gardasil_9_doses === 'unsure');"}, {"key": "egg_allergy", "type": "radio", "input": true, "label": "Do you have a severe egg allergy (anaphylaxis) or allergy to any vaccine component?", "confirm_label": "Severe egg allergy:", "values": [{"label": "Yes", "value": true}, {"label": "No", "value": false}], "optionsLabelPosition": "right", "validate": {"required": true}, "tableView": true, "customConditional": "show = (data.prior_gardasil_type === 'none' || data.prior_gardasil_type === 'gardasil_4' || (data.prior_gardasil_type === 'gardasil_9' && !(data.gardasil_9_doses == 3 || (data.gardasil_9_doses == 2 && data.started_before_15 === true))));"}, {"key": "no_indications", "type": "textfield", "input": true, "label": "No indication:", "hidden": true, "disabled": true, "multiple": false, "tableView": true, "clearOnHide": false, "defaultValue": true, "calculateValue": "value = ((data.prior_gardasil_type === 'gardasil_9') && (data.gardasil_9_doses == 3 || (data.gardasil_9_doses == 2 && data.started_before_15 === true))) || (data.egg_allergy === true);", "refreshOnChange": true}, {"key": "contraindication_keys", "type": "textfield", "input": true, "label": "Contraindication keys:", "hidden": true, "disabled": true, "multiple": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "value = []; if (data.prior_gardasil_type === 'gardasil_9' && (data.gardasil_9_doses == 3 || (data.gardasil_9_doses == 2 && data.started_before_15 === true))) { value.push('series_completed'); } if (data.egg_allergy === true) { value.push('egg_allergy'); }", "refreshOnChange": true}, {"key": "rejected_html", "type": "content", "input": true, "tableView": false, "clearOnHide": false, "defaultValue": "<h3 class='text-red'>We cannot offer you a prescription for Gardasil&nbsp;9 at this time.</h3><p>Please see an in-person healthcare provider for further assessment.</p><p><strong>Reasons why Gardasil&nbsp;9 vaccination are not provided include:</strong></p><ul><li>You have already completed the full HPV vaccine series (three total doses, or two doses finished before your 15<sup>th</sup> birthday).</li><li>You reported a severe egg allergy or allergy to a vaccine component.</li></ul>"}, {"key": "approved_html", "type": "content", "input": true, "tableView": false, "clearOnHide": false, "refreshOnChange": true, "defaultValue": "<h3 class='text-green'>You are eligible to receive Gardasil&nbsp;9.</h3><p><strong>Dosing schedule:</strong> You’ll need three shots: one now, a second shot ~2&nbsp;months later, and a final shot 6&nbsp;months after the first.</p><p><strong>Cost:</strong> About&nbsp;$200 per shot. Many private drug plans cover some or all of this - check with your insurer.</p><p><strong>How to get your shot:</strong> If we receive your request by <strong>1&nbsp;PM</strong> (local time), we guarantee a same-day physician consult who can fax the prescription to your preferred pharmacy. Some pharmacies accept walk-ins for the injection. Most patients get their Gardasil shot on the same day they request it.</p>"}]}