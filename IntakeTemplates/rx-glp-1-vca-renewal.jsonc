{"components": [{"intake_template_key": "bmi"}, {"key": "current_medication", "type": "selectboxes", "input": true, "label": "Please select the medication you are currently taking:", "values": [{"label": "Wegovy (semaglutide)", "value": "wegovy", "shortcut": ""}, {"label": "Ozempic (semaglutide)", "value": "ozempic", "shortcut": ""}, {"label": "Rybel<PERSON> (semaglutide)", "value": "ry<PERSON><PERSON>", "shortcut": ""}, {"label": "Saxenda (liraglutide)", "value": "sa<PERSON><PERSON>", "shortcut": ""}, {"label": "Trulicity (dulaglutide)", "value": "trulicity", "shortcut": ""}, {"label": "Mounjaro (Tirzepatide)", "value": "mou<PERSON><PERSON>", "shortcut": ""}, {"label": "None of the above", "value": "none", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "new_start_date", "html": "<p class='text-red'>The panel you have selected is for individuals with a <strong> current</strong> weight loss prescription provided by a physician, nurse practioner or TeleTest. </p><ul><li class='text-black'>If you wish to start weight loss medication, please select <a href='https://teletest.ca/app/care/weight-loss/' target='_blank'>Weight Loss Medication Start</a>.</li></ul>", "type": "content", "input": true, "tableView": false, "clearOnHide": false, "customConditional": "show = data.current_medication.none;"}, {"key": "currently_pregnant", "type": "radio", "input": true, "label": "Are you currently pregnant?", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "I don't know", "value": "i_dont_know", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.current_medication && !data.current_medication.none && data.sex == 'female';", "optionsLabelPosition": "right"}, {"key": "contraindicationsMEN2", "type": "selectboxes", "input": true, "label": "Do you have any of the following conditions? (Select all that apply)", "values": [{"label": "Medullary Thyroid Cancer (MTC)", "value": "MTC"}, {"label": "Pheochromocytoma", "value": "pheochromocytoma"}, {"label": "Primary Parathyroid Hyperplasia", "value": "primary_parathyroid_disease"}, {"label": "Irregular heart rhythm", "value": "arrhythmia"}], "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = !data.current_medication.none && (data.sex == 'male' || (data.sex == 'female' && data.currently_pregnant == 'no'));", "inputType": "checkbox"}, {"key": "no_contraindicationsMEN2", "type": "checkbox", "input": true, "label": "None of the above", "validate": {"custom": "valid = _.some(_.values(data.contraindicationsMEN2)) || data.no_contraindicationsMEN2;"}, "tableView": false, "defaultValue": false, "customConditional": "show = !data.current_medication.none && (data.sex == 'male' || (data.sex == 'female' && data.currently_pregnant == 'no'));", "customClass": "mt-n3"}, {"key": "current_DPP4_inhibitors_insulin", "type": "selectboxes", "input": true, "label": "Are you currently taking any of the following medications? (Select all that apply)", "values": [{"label": "<PERSON><PERSON><PERSON><PERSON> (Januvia)", "value": "sitagliptin_januvia"}, {"label": "Saxagliptin (Onglyza)", "value": "saxagliptin_onglyza"}, {"label": "<PERSON><PERSON><PERSON><PERSON> (Tradjenta)", "value": "linagliptin_tradjenta"}, {"label": "<PERSON><PERSON><PERSON>", "value": "insulin"}], "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = !data.current_medication.none && (data.sex == 'male' || (data.sex == 'female' && data.currently_pregnant == 'no')) && data.no_contraindicationsMEN2;", "inputType": "checkbox"}, {"key": "no_DPP4_inhibitors_insulin", "type": "checkbox", "input": true, "label": "None of the above", "validate": {"custom": "valid = _.some(_.values(data.current_DPP4_inhibitors_insulin)) || data.no_DPP4_inhibitors_insulin;"}, "tableView": false, "defaultValue": false, "customConditional": "show = !data.current_medication.none && (data.sex == 'male' || (data.sex == 'female' && data.currently_pregnant == 'no')) && data.no_contraindicationsMEN2;", "customClass": "mt-n3", "errors": {"custom": "Please select at least one medication you are currently taking, or select 'None of the above'."}}, {"key": "eating_disorder_detail", "customConditional": "show = !data.current_medication.none && (data.sex == 'male' || (data.sex == 'female' && data.currently_pregnant == 'no')) && data.no_DPP4_inhibitors_insulin;", "type": "selectboxes", "input": true, "label": "Have you ever been diagnosed with or suspected of having any of the following eating disorders? (Select all that apply)", "values": [{"label": "Anorexia Nervosa", "value": "anorexia_nervosa", "shortcut": ""}, {"label": "Bulimia Nervosa", "value": "bulimia_nervosa", "shortcut": ""}, {"label": "Binge Eating Disorder", "value": "binge_eating_disorder", "shortcut": ""}, {"label": "Avoidant/Restrictive Food Intake Disorder (ARFID)", "value": "arfid", "shortcut": ""}, {"label": "Other Eating Disorder", "value": "other_eating_disorder", "shortcut": ""}], "placeholder": "Select an option", "tableView": true, "optionsLabelPosition": "right"}, {"key": "none_of_the_above_eating_disorders", "type": "checkbox", "input": true, "label": "None of the above", "tableView": false, "defaultValue": false, "customConditional": "show = !data.current_medication.none && (data.sex == 'male' || (data.sex == 'female' && data.currently_pregnant == 'no')) && data.no_DPP4_inhibitors_insulin;", "customClass": "mt-n3", "validate": {"custom": "valid = _.some(_.values(data.eating_disorder_detail)) || data.none_of_the_above_eating_disorders;", "customMessage": "Please select at least one type of eating disorder, or confirm that none apply."}}, {"key": "no_indications", "type": "textfield", "input": true, "label": "No Indication:", "hidden": true, "disabled": true, "multiple": false, "tableView": true, "clearOnHide": false, "defaultValue": true, "calculateValue": "value = data.current_medication.none;", "refreshOnChange": true}, {"key": "contraindication_keys", "type": "textfield", "input": true, "label": "Contraindication keys:", "hidden": true, "disabled": true, "multiple": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "value = _.concat((data.currently_pregnant === 'yes' || data.currently_pregnant === 'i_dont_know') ? ['pregnancy_contraindication'] : [], _.some(_.values(data.contraindicationsMEN2)) ? ['MEN2_contraindication'] : [], _.some(_.values(data.current_DPP4_inhibitors_insulin)) ? ['DPP4_inhibitor_contraindication'] : [], _.some(_.values(data.eating_disorder_detail)) ? ['eating_disorder_contraindication'] : []);", "refreshOnChange": true}]}