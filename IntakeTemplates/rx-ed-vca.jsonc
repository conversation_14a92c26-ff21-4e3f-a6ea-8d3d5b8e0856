{"components": [{"key": "previous_rx_ed", "type": "selectboxes", "input": true, "label": "Have you previously been prescribed any of the following medications for erectile dysfunction?", "values": [{"label": "Sildenafil (Viagra)", "value": "sildenafil"}, {"label": "Tadalafil (Cialis)", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"label": "Vardenafil (Levitra)", "value": "vardenafil"}, {"label": "Other", "value": "other"}, {"label": "I have not used ED medication", "value": "none"}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "optionsLabelPosition": "right"}, {"key": "previous_rx_ed_other", "type": "textarea", "input": true, "label": "Please specify the “other” medication you were prescribed:", "tableView": true, "autoExpand": false, "customConditional": "show = data.previous_rx_ed && data.previous_rx_ed.other"}, {"key": "hx_nitrates", "type": "selectboxes", "input": true, "label": "Are you currently taking any of the following nitrate medications?", "values": [{"label": "Nitroglycerin (Nitro-Dur, Nitrostat, Trinitrin)", "value": "nitroglycerin"}, {"label": "Isosorbide Mononitrate (Imdur, Monoket)", "value": "isosorbide_mononitrate"}, {"label": "Isosorbide Dinitrate (Isordil, Sorbitrate)", "value": "isosorbide_dinitrate"}, {"label": "Nitroprusside (Nitropress, Nipride)", "value": "nitroprusside"}, {"label": "<PERSON><PERSON> (Poppers)", "value": "amyl_nitrite"}, {"label": "None of the above", "value": "none"}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "customConditional": "show = _.some(data.previous_rx_ed)", "optionsLabelPosition": "right"}, {"key": "cardiac_conditions", "type": "selectboxes", "input": true, "label": "Do you have any of the following cardiac conditions:", "values": [{"label": "Had a heart attack", "value": "recent_heart_attack"}, {"label": "Diagnosed with Heart Failure (i.e. congestive heart failure)", "value": "heart_failure"}, {"label": "<PERSON><PERSON>", "value": "unstable_angina"}, {"label": "Have an arrhythmia (i.e. irregular heart beat)", "value": "arrhythmia"}, {"label": "None of the above", "value": "none"}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "customConditional": "show = data.hx_nitrates && data.hx_nitrates.none && !_.some(_.omit(data.hx_nitrates, 'none'))", "optionsLabelPosition": "right"}, {"key": "hx_alpha_blockers", "type": "selectboxes", "input": true, "label": "Are you taking any of the following medications for high blood pressure or prostate problems?", "values": [{"label": "Doxazosin (Cardura, Carduran, Cascor, Doxadura)", "value": "doxazosin"}, {"label": "Prazosin (Minipress, Lysivane)", "value": "prazosin"}, {"label": "Terazosin (<PERSON><PERSON><PERSON>, Terapress)", "value": "terazosin"}, {"label": "Tamsulosin (Flomax, Omnic, Pradif, Contiflo)", "value": "tamsulosin"}, {"label": "Alfuzosin (Uroxatral, Xatger, Xatral)", "value": "alfuzosin"}, {"label": "Silodosin (Rapaflo, Silodyx, Urorec)", "value": "silodosin"}, {"label": "None of the above", "value": "none"}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "customConditional": "show = data.cardiac_conditions && data.cardiac_conditions.none && !_.some(_.omit(data.cardiac_conditions, 'none'))", "optionsLabelPosition": "right"}, {"key": "pde5_contra_symptoms", "type": "selectboxes", "input": true, "label": "Have you experienced any of the following?", "values": [{"label": "A history of low blood pressure or severe hypotension", "value": "severe_hypotension"}, {"label": "Severe or frequent headaches", "value": "headaches"}, {"label": "Palpitations or chest pain", "value": "palpitations"}, {"label": "Shortness of breath", "value": "dyspnea"}, {"label": "Dizziness or fainting spells", "value": "dizziness"}, {"label": "Changes in vision (e.g., blurry vision or loss of vision)", "value": "vision_changes"}, {"label": "Painful or prolonged erections", "value": "priapism"}, {"label": "Chest tightness, especially if occurring during or after sexual activity", "value": "chest_pain_with_sexual_activity"}, {"label": "None of the above", "value": "none"}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "customConditional": "show = data.hx_alpha_blockers && data.hx_alpha_blockers.none && !_.some(_.omit(data.hx_alpha_blockers, 'none'))", "optionsLabelPosition": "right"}, {"key": "pde5_contraindications_symptoms_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following PDE5 contraindication symptoms:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.pde5_contra_symptoms, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "contraindication_keys", "type": "textfield", "input": true, "label": "Contraindication keys:", "hidden": true, "disabled": true, "multiple": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "value = _.flatMap(['hx_nitrates', 'cardiac_conditions', 'hx_alpha_blockers', 'pde5_contra_symptoms'], k => _.some(_.omit(data[k], 'none')) ? [k] : [])"}, {"key": "approved_html", "type": "content", "input": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = `<h3 class='text-success'>You’re eligible to complete a physician consultation and request treatment</h3><br><p>After a secure messaging consultation, a TeleTest.ca physician will confirm that medication is clinically appropriate for you. If approved, they’ll issue an electronic prescription you can fill at <em>any</em> pharmacy; our Ontario partner offers next-day delivery in discreet, unbranded packaging.</p><h4>Generic tablets</h4><table style='width:100%; border-collapse:collapse; margin-bottom:12px;'><thead><tr><th style='text-align:left; border-bottom:1px solid #ccc;'>Medication</th><th style='text-align:left; border-bottom:1px solid #ccc;'>Strength&nbsp;(mg)</th><th style='text-align:left; border-bottom:1px solid #ccc;'>Price&nbsp;per&nbsp;pill&nbsp;(as&nbsp;low&nbsp;as*)</th></tr></thead><tbody><tr><td><strong><PERSON><PERSON><PERSON><PERSON>l (generic)</strong></td><td>25,&nbsp;50,&nbsp;100</td><td>$9.00</td></tr><tr><td><strong>Tadalafil (generic)</strong></td><td>5,&nbsp;10,&nbsp;20</td><td>$9.75</td></tr></tbody></table><h4>Lactose-free compounded capsules</h4><table style='width:100%; border-collapse:collapse; margin-bottom:8px;'><thead><tr><th style='text-align:left; border-bottom:1px solid #ccc;'>Medication</th><th style='text-align:left; border-bottom:1px solid #ccc;'>Strength&nbsp;(mg)</th><th style='text-align:left; border-bottom:1px solid #ccc;'>Price&nbsp;per&nbsp;pill&nbsp;(as&nbsp;low&nbsp;as*)</th></tr></thead><tbody><tr><td><strong>Sildenafil (lactose-free)</strong></td><td>25,&nbsp;50,&nbsp;100</td><td>$3.25</td></tr><tr><td><strong>Tadalafil (lactose-free)</strong></td><td>3,&nbsp;5,&nbsp;6,&nbsp;10,&nbsp;20</td><td>$2.25</td></tr></tbody></table><p style='margin-top:8px;'><em>Brand-name tablets (Viagra®, Cialis®, Levitra®) are available on request—ask during your consultation.</em></p><p style='font-size:0.85em; color:#666;'>Prices updated ${moment().format('MMMM YYYY')}; dispensing fees &amp; HST may apply.<br>* “As low as” reflects the price of the lowest-strength pill; higher strengths cost more.</p>`;", "refreshOnChange": true}]}