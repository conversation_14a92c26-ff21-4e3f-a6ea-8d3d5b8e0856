{"components": [{"key": "heading_ed_medication", "html": "<h1><center><strong>Erectile Dysfunction Testing</strong></center></h1><p>To assist in providing the best care, please respond to the following questions regarding your health and medical history related to erectile dysfunction.</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "header_hpi", "html": "<h2>Medical History</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "previous_ed_diagnosis", "type": "radio", "input": true, "label": "Have you been previously diagnosed with erectile dysfunction?", "values": [{"label": "Yes", "value": true}, {"label": "No", "value": false}, {"label": "I don't know", "value": "dont_know"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "current_ed_symptoms", "type": "selectboxes", "input": true, "label": "Are you currently experiencing any symptoms related to erectile dysfunction? (Select all that apply)", "values": [{"label": "Difficulty in achieving an erection", "value": "difficulty_achieving"}, {"label": "Difficulty in maintaining an erection", "value": "difficulty_maintaining"}, {"label": "Reduced sexual desire", "value": "reduced_desire"}, {"label": "None of the above", "value": "none"}], "tableView": true}, {"key": "symptoms_peyronies", "type": "radio", "input": true, "label": "Have you noticed any of the following changes to the shape of your penis with erections?", "values": [{"label": "New curvature or bending during erections", "value": "curvature"}, {"label": "Narrowing or shortening of the penis", "value": "narrowing_shortening"}, {"label": "Painful erections", "value": "painful_erections"}, {"label": "Lumps or hard areas within the shaft", "value": "lumps_hard_areas"}, {"label": "None of the above", "value": "none_above"}], "tableView": true, "validate": {"required": true}, "optionsLabelPosition": "right"}, {"key": "morning_erections", "type": "radio", "input": true, "label": "Do you experience morning erections?", "values": [{"label": "Regularly", "value": "regularly"}, {"label": "Sometimes", "value": "sometimes"}, {"label": "Rarely", "value": "rarely"}, {"label": "Never", "value": "never"}, {"label": "Not sure", "value": "not_sure"}], "tableView": true}, {"key": "ed_symptoms_frequency", "type": "radio", "input": true, "label": "How often do you experience symptoms of erectile dysfunction?", "values": [{"label": "Regularly", "value": "regularly"}, {"label": "Sometimes", "value": "sometimes"}, {"label": "Rarely", "value": "rarely"}, {"label": "Never", "value": "never"}], "tableView": true}, {"key": "duration_of_ed_symptoms", "type": "select", "input": true, "label": "For how long have you been experiencing erectile dysfunction symptoms?", "tooltip": "This helps us understand the duration and progression of your symptoms.", "data": {"values": [{"label": "Less than 3 months", "value": "less_than_3_months"}, {"label": "3-6 months", "value": "3_to_6_months"}, {"label": "6-12 months", "value": "6_to_12_months"}, {"label": "More than a year", "value": "more_than_a_year"}]}, "widget": "html5", "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "stress_impact_on_ed", "type": "radio", "input": true, "label": "Do you feel that stress affects your erectile dysfunction?", "values": [{"label": "Yes, significantly", "value": "yes_significantly"}, {"label": "Yes, somewhat", "value": "yes_somewhat"}, {"label": "Not sure", "value": "not_sure"}, {"label": "No", "value": "no"}], "tableView": true}, {"key": "ed_anxiety_stress_relation", "type": "radio", "input": true, "label": "Do your erectile dysfunction symptoms improve or resolve when performance anxiety or stress is reduced?", "values": [{"label": "Yes, symptoms improve with reduced anxiety or stress", "value": "improve_with_reduction"}, {"label": "No, symptoms persist regardless of anxiety or stress levels", "value": "persist_regardless"}, {"label": "Sometimes, it varies", "value": "varies"}, {"label": "Not sure", "value": "not_sure"}], "tableView": true}, {"key": "impact_of_ed", "type": "radio", "input": true, "label": "How much does erectile dysfunction affect your quality of life?", "values": [{"label": "Significantly", "value": "significantly"}, {"label": "Moderately", "value": "moderately"}, {"label": "<PERSON><PERSON><PERSON>", "value": "slightly"}, {"label": "Not at all", "value": "not_at_all"}], "tableView": true}, {"key": "ed_concern_level", "type": "radio", "input": true, "label": "How concerned are you about your erectile dysfunction?", "values": [{"label": "Extremely concerned", "value": "extremely_concerned"}, {"label": "Moderately concerned", "value": "moderately_concerned"}, {"label": "Slightly concerned", "value": "slightly_concerned"}, {"label": "Not concerned", "value": "not_concerned"}], "tableView": true}, {"key": "header_medication_history", "html": "<h2>Current Medications</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "current_medications", "type": "selectboxes", "input": true, "label": "Are you currently taking any of the following medications? (Select all that apply)", "values": [{"label": "Blood pressure medications", "value": "blood_pressure_medications"}, {"label": "Diabetes medications", "value": "diabetes_medications"}, {"label": "Cholesterol-lowering medications", "value": "cholesterol_medications"}, {"label": "Antidepressants", "value": "antidepressants"}, {"label": "Other", "value": "other"}, {"label": "None", "value": "none"}], "tableView": true}, {"key": "header_blood_pressure_medication_history", "html": "<h2>Blood Pressure Medications</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.current_medications.blood_pressure_medications"}, {"key": "correlation_bp_meds", "type": "radio", "input": true, "label": "Did you notice the onset or worsening of erectile dysfunction symptoms after starting or changing the dosage of your blood pressure medications?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "not_sure"}], "customConditional": "show = data.current_medications.blood_pressure_medications", "tableView": true}, {"key": "header_diabetes_history", "html": "<h2>Diabetes History</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.current_medications.diabetes_medications"}, {"key": "type_of_diabetes", "type": "radio", "input": true, "label": "What type of diabetes do you have?", "values": [{"label": "Type 1 Diabetes", "value": "type_1", "shortcut": ""}, {"label": "Type 2 Diabetes", "value": "type_2", "shortcut": ""}, {"label": "I take diabetes medications for weight loss", "value": "weight_loss", "shortcut": ""}, {"label": "Not sure", "value": "not_sure", "shortcut": ""}], "tableView": true, "validate": {"required": true}, "customConditional": "show = data.current_medications && data.current_medications.diabetes_medications"}, {"key": "diabetes_control", "type": "select", "input": true, "label": "What is your most recent Hemoglobin A1c level?", "tooltip": "Hemoglobin A1c levels are used to assess blood sugar control over the past 2-3 months.", "data": {"values": [{"label": "< 5.5%", "value": "less_5_5"}, {"label": "5.5% - 6.0%", "value": "5_5_to_6_0"}, {"label": "6.0% - 6.5%", "value": "6_0_to_6_5"}, {"label": "6.5% - 7.0%", "value": "6_5_to_7_0"}, {"label": "7.0% - 7.5%", "value": "7_0_to_7_5"}, {"label": "7.5% - 8.0%", "value": "7_5_to_8_0"}, {"label": "8.0% - 8.5%", "value": "8_0_to_8_5"}, {"label": "8.5% - 9.0%", "value": "8_5_to_9_0"}, {"label": "9.0% - 9.5%", "value": "9_0_to_9_5"}, {"label": "9.5% - 10.0%", "value": "9_5_to_10_0"}, {"label": "> 10%", "value": "greater_10"}, {"label": "Not sure", "value": "not_sure"}]}, "widget": "html5", "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = data.current_medications.diabetes_medications"}, {"key": "diabetes_complications", "type": "selectboxes", "input": true, "label": "Have you experienced any of the following complications related to diabetes? (Select all that apply)", "values": [{"label": "Neuropathy (nerve damage)", "value": "neuropathy"}, {"label": "Retinopathy (eye problems)", "value": "retinopathy"}, {"label": "Nephropathy (kidney issues)", "value": "nephropathy"}, {"label": "Cardiovascular problems", "value": "cardiovascular"}, {"label": "None", "value": "none"}], "tableView": true, "customConditional": "show = data.current_medications.diabetes_medications"}, {"key": "header_anti_depressant_medication_history", "html": "<h2>Anti-Depressant Medications</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.current_medications.antidepressants"}, {"key": "correlation_antidepressants", "type": "radio", "input": true, "label": "Did you notice the onset or worsening of erectile dysfunction symptoms after starting or changing the dosage of your antidepressants?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "not_sure"}], "customConditional": "show = data.current_medications.antidepressants", "tableView": true}, {"key": "header_ed_medication_history", "html": "<h2>Erectile Dysfunction Medications</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "previous_ed_medication", "type": "selectboxes", "input": true, "label": "What medications have you used for erectile dysfunction? (Select all that apply)", "values": [{"label": "Sildenafil (Viagra)", "value": "sildenafil"}, {"label": "Tadalafil (Cialis)", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"label": "Vardenafil (Levitra)", "value": "vardenafil"}, {"label": "Other", "value": "other"}, {"label": "I have not used ED medication", "value": "none_used"}], "tableView": true, "optionsLabelPosition": "right"}, {"key": "header_sildenafil", "html": "<h2>Sildenafil (Viagra)</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.previous_ed_medication.sildenafil"}, {"key": "effectiveness_sildenafil", "type": "radio", "input": true, "label": "How effective was <PERSON><PERSON><PERSON><PERSON><PERSON> (Viagra) for you?", "values": [{"label": "Very effective", "value": "very_effective"}, {"label": "Somewhat effective", "value": "somewhat_effective"}, {"label": "Not effective", "value": "not_effective"}], "tableView": true, "customConditional": "show = data.previous_ed_medication.sildenafil", "widget": "html5"}, {"key": "dose_sildenafil", "type": "select", "widget": "html5", "input": true, "label": "What was the dose of Si<PERSON>na<PERSON>l (Viagra) you were taking?", "data": {"values": [{"label": "25 mg", "value": "25mg"}, {"label": "50 mg", "value": "50mg"}, {"label": "100 mg", "value": "100mg"}, {"label": "Not sure", "value": "not_sure"}]}, "tableView": true, "customConditional": "show = data.previous_ed_medication.sildenafil"}, {"key": "frequency_sildenafil", "type": "select", "input": true, "label": "How frequently were you taking Sildenafil (Viagra)?", "data": {"values": [{"label": "As needed", "value": "as_needed"}, {"label": "Once daily", "value": "once_daily"}, {"label": "Several times a week", "value": "several_times_week"}, {"label": "Not regularly", "value": "not_regularly"}, {"label": "Not sure", "value": "not_sure"}]}, "tableView": true, "widget": "html5", "customConditional": "show = data.previous_ed_medication.sildenafil"}, {"key": "header_tadalafil", "html": "<h2>Tadalafil (Cialis)</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.previous_ed_medication.tadalafil"}, {"key": "effectiveness_tadalafil", "type": "radio", "input": true, "label": "How effective was <PERSON><PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>) for you?", "values": [{"label": "Very effective", "value": "very_effective"}, {"label": "Somewhat effective", "value": "somewhat_effective"}, {"label": "Not effective", "value": "not_effective"}], "tableView": true, "widget": "html5", "customConditional": "show = data.previous_ed_medication.tadalafil"}, {"key": "dose_tadalafil", "type": "select", "input": true, "widget": "html5", "label": "What was the dose of <PERSON><PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>) you were taking?", "data": {"values": [{"label": "10 mg", "value": "10mg"}, {"label": "20 mg", "value": "20mg"}, {"label": "Not sure", "value": "not_sure"}]}, "tableView": true, "customConditional": "show = data.previous_ed_medication.tadalafil"}, {"key": "frequency_tadalafil", "type": "select", "input": true, "widget": "html5", "label": "How frequently were you taking <PERSON><PERSON><PERSON><PERSON> (Cialis)?", "data": {"values": [{"label": "As needed", "value": "as_needed"}, {"label": "Once daily", "value": "once_daily"}, {"label": "Not sure", "value": "not_sure"}]}, "tableView": true, "customConditional": "show = data.previous_ed_medication.tadalafil"}, {"key": "header_vardenafil", "html": "<h2>Vardenafil (Levitra)</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.previous_ed_medication.vardenafil"}, {"key": "effectiveness_vardenafil", "type": "radio", "input": true, "label": "How effective was <PERSON><PERSON><PERSON><PERSON><PERSON> (Levi<PERSON>) for you?", "values": [{"label": "Very effective", "value": "very_effective"}, {"label": "Somewhat effective", "value": "somewhat_effective"}, {"label": "Not effective", "value": "not_effective"}], "tableView": true, "customConditional": "show = data.previous_ed_medication.vardenafil"}, {"key": "dose_vardenafil", "type": "select", "widget": "html5", "input": true, "label": "What was the dose of V<PERSON><PERSON><PERSON>l (<PERSON><PERSON>) you were taking?", "data": {"values": [{"label": "5 mg", "value": "5mg"}, {"label": "10 mg", "value": "10mg"}, {"label": "20 mg", "value": "20mg"}, {"label": "Not sure", "value": "not_sure"}]}, "tableView": true, "customConditional": "show = data.previous_ed_medication.vardenafil"}, {"key": "frequency_vardenafil", "type": "radio", "input": true, "label": "How frequently were you taking <PERSON><PERSON><PERSON><PERSON><PERSON> (<PERSON><PERSON>)?", "values": [{"label": "As needed", "value": "as_needed"}, {"label": "Not regularly", "value": "not_regularly"}, {"label": "Not sure", "value": "not_sure"}], "tableView": true, "validate": {"required": true}, "customConditional": "show = data.previous_ed_medication && data.previous_ed_medication.vardenafil"}, {"key": "header_previous_investigations", "html": "<h2>Previous Investigations</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "previous_ed_tests", "type": "selectboxes", "input": true, "label": "Which of the following tests have you undergone for erectile dysfunction? (Select all that apply)", "values": [{"label": "Blood tests for hormone levels", "value": "blood_hormone_levels"}, {"label": "Ultrasound", "value": "ultrasound"}, {"label": "Nerve function tests", "value": "nerve_function_tests"}, {"label": "Nocturnal penile tumescence test", "value": "nocturnal_penile_tumescence"}, {"label": "None", "value": "none"}], "tableView": true}, {"key": "results_blood_hormone_levels", "type": "selectboxes", "input": true, "label": "Which specific blood tests for hormone levels have you undergone? (Select all that apply)", "values": [{"label": "Testosterone levels", "value": "testosterone_levels"}, {"label": "Prolactin levels", "value": "prolactin_levels"}, {"label": "Thyroid function tests (TSH)", "value": "thyroid_tests"}, {"label": "Luteinizing hormone (LH)", "value": "luteinizing_hormone"}, {"label": "Follicle-stimulating hormone (FSH)", "value": "follicle_stimulating_hormone"}, {"label": "Other hormone tests", "value": "other_hormone_tests"}, {"label": "Not sure / Don't remember", "value": "not_sure"}], "tableView": true, "customConditional": "show = data.previous_ed_tests.blood_hormone_levels"}, {"key": "results_ultrasound", "type": "selectboxes", "input": true, "widget": "html5", "label": "Results of Penile Ultrasound", "values": [{"label": "Normal blood flow", "value": "normal_blood_flow"}, {"label": "Reduced or inadequate blood flow", "value": "reduced_blood_flow"}, {"label": "Structural abnormalities", "value": "structural_abnormalities"}, {"label": "Venous leak", "value": "venous_leak"}, {"label": "Other findings", "value": "other_findings"}, {"label": "Not sure", "value": "not_sure"}], "tableView": true, "customConditional": "show = data.previous_ed_tests.ultrasound"}, {"key": "results_nocturnal_penile_tumescence", "type": "select", "widget": "html5", "input": true, "label": "Results of Nocturnal Penile Tumescence Test", "data": {"values": [{"label": "Normal erectile function during sleep", "value": "normal_erectile_function"}, {"label": "Reduced or absent erections during sleep", "value": "reduced_absent_erections"}, {"label": "Erections present but not sustained", "value": "erections_not_sustained"}, {"label": "Inconclusive results", "value": "inconclusive_results"}, {"label": "Never tested", "value": "never_tested"}, {"label": "Not sure", "value": "not_sure"}]}, "customConditional": "show = data.previous_ed_tests.nocturnal_penile_tumescence", "tableView": true}, {"key": "header_med_safety_factors", "html": "<h1>Medication Safety</h1>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "cardiac_conditions", "type": "selectboxes", "input": true, "label": "Do you have any of the following cardiac conditions:", "values": [{"label": "Had a heart attack", "value": "recent_heart_attack", "shortcut": ""}, {"label": "Diagnosed with Heart Failure (i.e. congestive heart failure)", "value": "heart_failure", "shortcut": ""}, {"label": "<PERSON><PERSON>", "value": "unstable_angina", "shortcut": ""}, {"label": "Have an arrhythmia (i.e. irregular heart beat)", "value": "arrhythmia", "shortcut": ""}, {"label": "None of the above", "value": "none", "shortcut": ""}], "inputType": "checkbox", "tableView": true, "optionsLabelPosition": "right"}, {"key": "hx_alpha_blockers", "type": "selectboxes", "input": true, "label": "Are you taking any of the following medications for high blood pressure or prostate problems?", "values": [{"label": "Doxazosin (Cardura, Carduran, Cascor, Doxadura)", "value": "doxazosin", "shortcut": ""}, {"label": "Prazosin (Minipress, Lysivane)", "value": "prazosin", "shortcut": ""}, {"label": "Terazosin (<PERSON><PERSON><PERSON>, Terapress)", "value": "terazosin", "shortcut": ""}, {"label": "Tamsulosin (Flomax, Omnic, Pradif, Contiflo)", "value": "tamsulosin", "shortcut": ""}, {"label": "Alfuzosin (Uroxatral, Xatger, Xatral)", "value": "alfuzosin", "shortcut": ""}, {"label": "Silodosin (Rapaflo, Silodyx, Urorec)", "value": "silodosin", "shortcut": ""}, {"label": "None of the above", "value": "none", "shortcut": ""}], "inputType": "checkbox", "tableView": true, "optionsLabelPosition": "right"}, {"key": "hx_severe_hypotension", "type": "radio", "input": true, "label": "Do you have a history of low blood pressure?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "pde5_contraindications_symptoms", "type": "selectboxes", "input": true, "label": "Have you currently or recently experienced any of the following symptoms:", "values": [{"label": "Severe or frequent headaches", "value": "headaches", "shortcut": ""}, {"label": "Shortness of breath", "value": "dyspnea", "shortcut": ""}, {"label": "Dizziness or fainting spells", "value": "dizziness", "shortcut": ""}, {"label": "Changes in vision (e.g., sudden loss or blurring)", "value": "vision_changes", "shortcut": ""}, {"label": "Hearing loss or ringing in the ears", "value": "hearing_issues", "shortcut": ""}, {"label": "Painful or prolonged erection (lasting more than 4 hours)", "value": "priapism", "shortcut": ""}, {"label": "Chest pain, especially if occurring during or after sexual activity", "value": "chest_pain_sexual_activity", "shortcut": ""}, {"label": "None of the above", "value": "none", "shortcut": ""}], "inputType": "checkbox", "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "header_lifestyle_factors", "html": "<h1>Lifestyle Factors</h1>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "sleep_patterns", "type": "select", "input": true, "widget": "html5", "label": "How would you describe your regular sleep patterns?", "data": {"values": [{"label": "Regular and restful", "value": "regular_restful"}, {"label": "Occasionally disrupted", "value": "occasionally_disrupted"}, {"label": "Frequently disrupted", "value": "frequently_disrupted"}, {"label": "Very poor", "value": "very_poor"}]}, "tableView": true}, {"key": "smoking_status", "type": "radio", "input": true, "label": "Do you smoke?", "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "I used to smoke", "value": "used_to", "shortcut": ""}], "tableView": true}, {"key": "alcohol_consumption", "type": "radio", "input": true, "label": "How often do you consume alcohol? (Select all that apply)", "values": [{"label": "Daily", "value": "daily"}, {"label": "Several times a week", "value": "several_times_week"}, {"label": "Once a week", "value": "once_week"}, {"label": "Less than once a week", "value": "less_once_week"}, {"label": "Never", "value": "never"}], "tableView": true}, {"key": "exercise_frequency", "type": "radio", "input": true, "label": "How often do you exercise? (Select all that apply)", "values": [{"label": "Daily", "value": "daily"}, {"label": "Several times a week", "value": "several_times_week"}, {"label": "Once a week", "value": "once_week"}, {"label": "Less than once a week", "value": "less_once_week"}, {"label": "Never", "value": "never"}], "tableView": true}, {"key": "stress_levels", "type": "radio", "input": true, "label": "How often do you feel stressed? (Select one)", "values": [{"label": "Daily", "value": "daily"}, {"label": "Several times a week", "value": "several_times_week"}, {"label": "Once a week", "value": "once_week"}, {"label": "Less than once a week", "value": "less_once_week"}, {"label": "Rarely", "value": "rarely"}, {"label": "Never", "value": "never"}], "tableView": true}, {"key": "additional_questions_ed", "type": "textarea", "input": true, "label": "Please use this space to ask any additional questions or provide further information related to medical history you feel we might have missed:", "tableView": true, "autoExpand": false}]}