{"components": [{"key": "current_symptoms", "type": "radio", "input": true, "label": "Are you currently experiencing symptoms?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "confirmed_exposure", "type": "radio", "input": true, "label": "Have you had a exposure to a person who tested positive for Mycoplasma Genitalium?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.current_symptoms == false;", "optionsLabelPosition": "right"}, {"key": "work_testing", "type": "radio", "input": true, "label": "Do you currently require testing for work purposes?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.confirmed_exposure == false && data.current_symptoms == false;", "optionsLabelPosition": "right"}, {"key": "mycoplasma_contraindication", "label": "Are you currently experiencing any of the following symptoms:", "values": [{"label": "Abdominal or pelvic pain/cramping", "value": "abdo_pelvic_pain", "shortcut": ""}, {"label": "Fevers or chills", "value": "fevers_chills", "shortcut": ""}, {"label": "Rectal pain, discharge or bleeding", "value": "rectal_pain_discharge_bleeding", "shortcut": ""}, {"label": "Feel unwell", "value": "malaise", "shortcut": ""}, {"label": "<PERSON><PERSON>", "value": "genital_rash", "shortcut": ""}, {"label": "Testicular lumps or pain", "customConditional": "show = data.sex == 'male';", "value": "testicular_mass", "shortcut": ""}], "input": true, "inputType": "checkbox", "customConditional": "show = data.current_symptoms == true;", "optionsLabelPosition": "right", "tableView": true, "type": "selectboxes"}, {"key": "no_mycoplasma_contraindications", "customClass": "mt-n3", "defaultValue": false, "input": true, "label": "None of the above", "tableView": true, "type": "checkbox", "customConditional": "show = data.current_symptoms == true;", "validate": {"custom": "valid = !!data.no_mycoplasma_contraindications || !!_.some(_.values(data.mycoplasma_contraindication));"}, "errors": {"custom": "required, or select a symptom."}}, {"key": "list_current_symptoms", "label": "Please select any symptoms that apply to you:", "values": [{"label": "Pain/discomfort with urination", "value": "dysuria", "shortcut": ""}, {"label": "Feeling of incomplete bladder emptying", "value": "incomplete_bladder_emptying", "shortcut": ""}, {"label": "Penile discharge", "value": "penile_discharge", "customConditional": "show = data.sex == 'male';", "shortcut": ""}, {"label": "Vaginal discharge", "customConditional": "show = data.sex == 'female';", "value": "vaginal_discharge", "shortcut": ""}, {"label": "Vaginal itchiness", "customConditional": "show = data.sex == 'female';", "value": "vaginal_pruritis", "shortcut": ""}, {"label": "Bleeding after sex", "customConditional": "show = data.sex == 'female';", "value": "vaginal_bleeding", "shortcut": ""}], "input": true, "inputType": "checkbox", "customConditional": "show = data.current_symptoms == true && data.no_mycoplasma_contraindications == true;", "optionsLabelPosition": "right", "tableView": true, "type": "selectboxes"}, {"key": "no_list_current_symptoms", "customClass": "mt-n3", "defaultValue": false, "input": true, "label": "None of the above", "tableView": true, "type": "checkbox", "customConditional": "show = data.current_symptoms == true  && data.no_mycoplasma_contraindications == true;", "validate": {"custom": "valid = !!data.no_list_current_symptoms || !!_.some(_.values(data.list_current_symptoms));"}, "errors": {"custom": "required, or select a symptom."}}, {"key": "completed_sti_testing", "type": "radio", "input": true, "label": "Have you completed testing for sexually transmissible infections (chlamydia, gonorrhea, trichomonas) since your symptoms started?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.current_symptoms == true && data.no_list_current_symptoms == false && data.no_mycoplasma_contraindications == true;", "optionsLabelPosition": "right"}, {"key": "completed_uti_testing", "type": "radio", "input": true, "label": "Have you completed testing for a Urinary Tract Infection (UTI) since your symptoms started?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.current_symptoms == true && data.no_list_current_symptoms == false && data.no_mycoplasma_contraindications == true && data.completed_sti_testing == true && (data.list_current_symptoms.dysuria || data.list_current_symptoms.incomplete_bladder_emptying);", "optionsLabelPosition": "right"}, {"key": "completed_swab_testing", "type": "radio", "input": true, "label": "Have you completed vaginal testing for Bacterial Vaginosis (BV) and yeast since your symptoms started?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.current_symptoms == true && data.no_list_current_symptoms == false && data.no_mycoplasma_contraindications == true && data.completed_sti_testing == true && (data.list_current_symptoms.vaginal_discharge || data.list_current_symptoms.vaginal_pruritis || data.list_current_symptoms.vaginal_bleeding) && data.sex == 'female';", "optionsLabelPosition": "right"}, {"key": "completed_sti_tests", "type": "selectboxes", "input": true, "label": "Please select which of following tests you have completed:", "values": [{"label": "Chlamydia", "value": "chlamydia", "shortcut": ""}, {"label": "Gonorrhea", "value": "gonorrhea", "shortcut": ""}, {"label": "Trichomonas", "value": "trichomonas", "shortcut": ""}, {"label": "Urinary Tract Infection (UTI)", "value": "uti", "shortcut": ""}], "inputType": "checkbox", "customConditional": "show = data.current_symptoms == true && data.no_list_current_symptoms == false && data.no_mycoplasma_contraindications == true && data.completed_uti_sti_testing == true;", "tableView": true, "optionsLabelPosition": "right"}]}