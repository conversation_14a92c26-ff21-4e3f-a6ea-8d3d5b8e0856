{"components": [{"key": "header_vaccine_testing", "html": "<h4>Immunity Testing for School or Workplace</h4><p>Please complete the following section to help us determine what immunity or infectious disease testing you may need. This ensures that your lab requisition includes all necessary tests required by your school, workplace, or other requesting agency.</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "required_tests", "type": "selectboxes", "label": "Which specific tests are required for you?", "input": true, "values": [{"label": "<PERSON><PERSON><PERSON>", "value": "measles"}, {"label": "<PERSON><PERSON>", "value": "mumps"}, {"label": "<PERSON><PERSON><PERSON>", "value": "rubella"}, {"label": "Varicell<PERSON> (Chickenpox)", "value": "varicella"}, {"label": "Hepatitis A", "value": "hepatitis_a"}, {"label": "Hepatitis B", "value": "hepatitis_b"}, {"label": "Other", "value": "other"}], "tableView": true}, {"key": "required_tests_other_text", "type": "textfield", "input": true, "label": "Please specify other testing needed:", "customConditional": "show = data.required_tests && data.required_tests.other;", "tableView": true}, {"key": "hep_a_testing_header", "type": "content", "input": false, "html": "</br><h4>Hepatitis A Testing</h4><p>Hepatitis A testing may include:</p><ul><li><strong>Immunity testing</strong> - to check if you're already protected from past vaccination or infection<br><em>Example: if you previously received a vaccine like Twinrix or Havrix</em></li><li><strong>Current infection testing (antigen)</strong> - to see if you currently have Hepatitis A<br><em>This is less commonly done and usually only needed if specifically requested.</em></li></ul><p>If you're unsure, don't worry — we'll arrange for the most commonly requested testing from work and school programs. You'll have a chance to upload your paperwork at the end of this intake.</p>", "customConditional": "show = data.required_tests && data.required_tests.hepatitis_a;"}, {"key": "hep_a_confirm_testing", "type": "radio", "label": "Are you sure you require Hepatitis A testing?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No / Not sure", "value": "no"}], "input": true, "tableView": true, "customConditional": "show = data.required_tests && data.required_tests.hepatitis_a;"}, {"key": "hep_a_testing_type", "type": "selectboxes", "label": "What type of Hepatitis A testing has been requested?", "description": "Select all that apply if known. If you're unsure, choose 'I have no idea.'", "input": true, "values": [{"label": "I have no idea", "value": "unsure"}, {"label": "Hepatitis A IgG <strong>(HepA-SAb)</strong> - to see if you're immune", "value": "s_ab", "defaultValue": true}, {"label": "Hepatitis A SAg <strong>(HepA-SAg)</strong> - to see if you have a current infection (less common)", "value": "s_ag"}], "tableView": true, "customConditional": "show = data.hep_a_confirm_testing === 'yes';"}, {"key": "hep_b_testing_header", "type": "content", "input": false, "html": "</br><h4>Hepatitis B Testing</h4><p>This may include testing to check if you're immune, currently infected, or have had past exposure.</p><ul><li><strong>HBsAb</strong> - immunity from vaccination or past infection<br><em>Example: Twin<PERSON>, Engerix-B, Recombivax HB</em></li><li><strong>HBsAg</strong> - current infection (less common)</li><li><strong>HBcAb</strong> - past exposure to Hepatitis B</li></ul>", "customConditional": "show = data.required_tests && data.required_tests.hepatitis_b;"}, {"key": "hep_b_confirm_testing", "type": "radio", "label": "Are you sure you require Hepatitis B testing?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No / Not sure", "value": "no"}], "input": true, "tableView": true, "customConditional": "show = data.required_tests && data.required_tests.hepatitis_b;"}, {"key": "hep_b_testing_type", "type": "selectboxes", "label": "What type of Hepatitis B testing has been requested?", "description": "Select all that apply if known. If you're unsure, choose 'I have no idea.'", "input": true, "values": [{"label": "I have no idea", "value": "unsure"}, {"label": "Hepatitis B surface antibody <strong>(HBsAb)</strong> - to see if you're immune", "value": "surface_antibody", "defaultValue": true}, {"label": "Hepatitis B surface antigen <strong>(HBsAg)</strong> - to see if you have a current infection", "value": "surface_antigen", "defaultValue": true}, {"label": "Hepatitis B core antibody <strong>(HBcAb)</strong> - to check for past exposure or infection", "value": "core_antibody"}], "tableView": true, "customConditional": "show = data.hep_b_confirm_testing === 'yes';"}, {"key": "hep_b_positive_history", "type": "radio", "label": "Have you ever tested positive for Hepatitis B?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "I’m not sure", "value": "unsure"}], "input": true, "tableView": true, "customConditional": "show = data.hep_b_confirm_testing === 'yes';"}, {"key": "hep_b_family_history", "type": "radio", "label": "Do you have a first-degree relative (e.g., parent or sibling) with Hepatitis B?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "I'm not sure", "value": "unsure"}], "input": true, "tableView": true, "customConditional": "show = data.hep_b_confirm_testing === 'yes';"}, {"key": "hep_b_screened_due_to_family", "type": "radio", "label": "Have you ever been screened to ensure you have never had Hepatitis B?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "I’m not sure", "value": "unsure"}], "input": true, "tableView": true, "customConditional": "show = data.hep_b_family_history === 'yes';"}, {"key": "sti_screening_heading", "type": "content", "input": false, "html": "</br><h4>HIV, Syphilis, and Hepatitis C Testing</h4><p>Please let us know if any of the following tests are required. If you have a known history of testing positive for any of these conditions, we will follow up with additional questions to ensure appropriate management.</p>"}, {"key": "sti_testing_required", "type": "selectboxes", "label": "Are any of the following required?", "input": true, "values": [{"label": "HIV", "value": "hiv"}, {"label": "Syphilis", "value": "syphilis"}, {"label": "Hepatitis C", "value": "hep_c"}, {"label": "None", "value": "none"}], "tableView": true}, {"key": "sti_known_positive", "type": "selectboxes", "label": "Have you ever tested positive for any of the following conditions?", "input": true, "values": [{"label": "HIV", "value": "hiv_positive"}, {"label": "Syphilis", "value": "syphilis_positive"}, {"label": "Hepatitis C", "value": "hep_c_positive"}, {"label": "No, I haven't tested positive for any of these", "value": "none"}], "customConditional": "show = _.some(['hiv', 'syphilis', 'hep_c'], k => data.sti_testing_required && data.sti_testing_required[k]);", "tableView": true}, {"key": "vaccination_status_heading", "type": "content", "input": false, "html": "</br><h4>Vaccination Status</h4><p>This helps us understand your likely vaccine history and whether additional documentation may be required.</p>"}, {"key": "born_in_canada", "type": "radio", "input": true, "label": "Were you born in Canada?", "values": [{"label": "Yes", "value": "canada"}, {"label": "No, I was born outside of Canada", "value": "outside_canada"}], "tableView": true}, {"key": "routine_vaccinations_status", "type": "radio", "input": true, "label": "Did you receive routine childhood vaccinations in your country of birth?", "values": [{"label": "Yes, all routine vaccines", "value": "yes_all"}, {"label": "Some vaccines, but not all", "value": "some"}, {"label": "No, I did not receive routine childhood vaccinations", "value": "none"}, {"label": "I'm not sure", "value": "unsure"}], "customConditional": "show = data.born_in_canada || data.born_in_canada === 'outside_canada';", "tableView": true}, {"key": "vaccine_records_access", "type": "radio", "input": true, "label": "Do you have access to your vaccine records?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "tableView": true}, {"key": "mmr_vaccine_record", "type": "radio", "input": true, "label": "Do you have access to your MMR vaccine records?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "tableView": true, "customConditional": "show = _.includes(data.required_tests, 'MMR');"}, {"key": "mmr_vaccine_type", "type": "radio", "input": true, "label": "Was your MMR vaccine administered as a single combination vaccine or as separate vaccines?", "values": [{"label": "Single Combination Vaccine", "value": "single"}, {"label": "Separate Vaccines", "value": "separate"}], "tableView": true, "customConditional": "show = data.mmr_vaccine_record == 'yes';"}, {"key": "mmr_vaccine_doses", "type": "radio", "input": true, "label": "How many doses of the MMR vaccine did you receive?", "values": [{"label": "1 dose", "value": "1"}, {"label": "2 doses", "value": "2"}, {"label": "3 doses", "value": "3"}, {"label": "I don't know", "value": "unknown"}], "tableView": true, "customConditional": "show = data.mmr_vaccine_type != '';"}, {"key": "mmr_vaccine_age", "type": "select", "label": "At what age did you receive the MMR vaccine?", "description": "Select the age or range that best applies. If you're not sure, you can choose 'I don't know.'", "input": true, "widget": "html5", "data": {"values": [{"label": "Before age 1", "value": "Before age 1"}, {"label": "Between ages 1 and 3", "value": "Ages 1–3"}, {"label": "Between ages 4 and 6", "value": "Ages 4–6"}, {"label": "Between ages 7 and 12", "value": "Ages 7–12"}, {"label": "Teen years (13–19)", "value": "Ages 13–19"}, {"label": "As an adult (20+)", "value": "Ages 20+"}, {"label": "I don't know", "value": "I don't know"}]}, "validate": {"required": true}, "tableView": true, "customConditional": "show = data.mmr_vaccine_doses != '';"}, {"key": "varicella_history", "type": "radio", "label": "Have you ever had chickenpox or shingles?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "I'm not sure", "value": "unsure"}], "input": true, "tableView": true, "customConditional": "show = data.mmr_vaccine_age != '';"}, {"key": "varicella_type", "type": "radio", "label": "If yes, which condition did you have?", "values": [{"label": "Chickenpox", "value": "chickenpox"}, {"label": "Shin<PERSON>", "value": "shingles"}, {"label": "I don't know", "value": "unknown"}], "input": true, "tableView": true, "customConditional": "show = data.varicella_history === 'yes';"}, {"key": "varicella_diagnosis_confirmed", "type": "radio", "label": "Was this diagnosis confirmed by a doctor?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "I'm not sure", "value": "unsure"}], "input": true, "tableView": true, "customConditional": "show = data.varicella_history === 'yes';"}, {"key": "hep_b_vaccine_record", "type": "radio", "input": true, "label": "Do you have access to your Hepatitis B vaccine records?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "tableView": true, "customConditional": "show = _.includes(data.required_tests, 'HepABC');"}, {"key": "hep_b_vaccine_series", "type": "radio", "input": true, "label": "Did you complete the Hepatitis B primary series and later receive a booster?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "tableView": true, "customConditional": "show = data.hep_b_vaccine_record == 'yes';"}, {"key": "hep_b_fluorescent_test", "type": "radio", "input": true, "label": "Do you have any follow-up immunity testing for Hepatitis B showing you are a non-responder?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "tableView": true, "customConditional": "show = data.hep_b_vaccine_series == 'yes';"}, {"key": "hiv_syphilis_screening", "type": "checkbox", "label": "Do you require HIV and Syphilis screening?", "values": [{"label": "Yes, I need screening for both", "value": "both"}, {"label": "Only HIV screening", "value": "hiv"}, {"label": "Only Syphilis screening", "value": "syphilis"}], "tableView": true, "customConditional": "show = _.includes(data.required_tests, 'HIV_Syphilis');"}, {"key": "tb_vaccination_history", "type": "radio", "input": true, "label": "Did you have a BCG vaccination as a child?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Unknown", "value": "unknown"}], "tableView": true, "customConditional": "show = _.includes(data.required_tests, 'TB');"}, {"key": "bcg_age_at_vaccination", "type": "radio", "input": true, "label": "At what age did you receive the BCG vaccination?", "values": [{"label": "< 1 year", "value": "<1"}, {"label": "> 1 year", "value": ">1"}, {"label": "Unknown", "value": "unknown"}], "tableView": true, "customConditional": "show = data.tb_vaccination_history == 'yes';"}, {"key": "tb_exposure", "type": "radio", "input": true, "label": "Have you had tuberculosis (TB) yourself or been exposed to someone with TB?", "values": [{"label": "Yes, I have had TB", "value": "had_tb"}, {"label": "Yes, I have had TB exposure", "value": "exposure"}, {"label": "No", "value": "no"}], "tableView": true, "customConditional": "show = data.tb_vaccination_history == 'yes';"}, {"key": "tb_screening_type", "type": "radio", "input": true, "label": "Do you require TB screening (Skin test or IGRA)?", "values": [{"label": "TB Skin Test", "value": "skin_test"}, {"label": "IGRA (Interferon Gamma Release Assay)", "value": "igra_test"}], "tableView": true, "customConditional": "show = data.tb_exposure == 'had_tb' || data.tb_exposure == 'exposure';"}, {"key": "tetanus_vaccine_heading", "type": "content", "input": false, "html": "</br><h4>Tetanus, <PERSON><PERSON><PERSON> and Polio Vaccine</h4><p>There is no blood test to check for immunity to Tetanus, Pertussis (whooping cough), or Polio. If you do not have a record of your vaccination, you can receive a tetanus booster at your local pharmacy.</p>"}, {"key": "tetanus_recent_vaccine", "type": "radio", "label": "Have you received a Tetanus vaccine in the past 10 years?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "I'm not sure", "value": "unsure"}], "input": true, "tableView": true}, {"key": "tetanus_recommendation", "type": "content", "input": false, "html": "<div style='border-left: 4px solid #0d6efd; background-color: #e7f1ff; padding: 10px; margin-top: 10px;'><strong>Recommendation:</strong> If you haven't had a tetanus vaccine in the past 10 years, it's recommended to receive one. You can get this from most local pharmacies.</div>", "customConditional": "show = data.tetanus_recent_vaccine === 'no';"}, {"key": "tb_testing_heading", "type": "content", "input": false, "html": "</br><h4>Tuberculosis (TB) Testing</h4>"}, {"key": "tb_testing_required", "type": "radio", "label": "Do you require TB testing (skin test or IGRA)?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "I'm not sure", "value": "unsure"}], "input": true, "tableView": true}, {"key": "tb_past_history", "type": "radio", "label": "Have you ever had tuberculosis (TB) in the past?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "I'm not sure", "value": "unsure"}], "input": true, "tableView": true, "customConditional": "show = data.tb_testing_required === 'yes';"}, {"key": "tb_bcg_vaccine", "type": "radio", "label": "Did you receive the BCG vaccine for TB as a child? (Note: This is not typically given in Canada, the United States, or the UK)", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "I'm not sure", "value": "unsure"}], "input": true, "tableView": true, "customConditional": "show = data.tb_testing_required === 'yes';"}, {"key": "tb_bcg_age", "type": "radio", "label": "At what age did you receive the BCG vaccine?", "values": [{"label": "Under 1 year old", "value": "under_1"}, {"label": "1 year or older", "value": "1_plus"}, {"label": "I don't know", "value": "unknown"}], "input": true, "tableView": true, "customConditional": "show = data.tb_testing_required === 'yes' && data.tb_bcg_vaccine === 'yes';"}, {"key": "tb_skin_test_history", "type": "radio", "label": "Have you ever had a TB skin test (<PERSON><PERSON><PERSON> test)?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "I'm not sure", "value": "unsure"}], "input": true, "tableView": true, "customConditional": "show = data.tb_testing_required === 'yes';"}, {"key": "tb_skin_test_positive", "type": "radio", "label": "Have you ever tested positive on a TB skin test?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "I'm not sure", "value": "unsure"}], "input": true, "tableView": true, "customConditional": "show = data.tb_testing_required === 'yes' && data.tb_skin_test_history === 'yes';"}, {"key": "tb_positive_recommendation", "type": "content", "input": false, "html": "<div style='border-left: 4px solid #0d6efd; background-color: #e7f1ff; padding: 10px; margin-top: 10px;'><strong>Recommendation:</strong> If you have ever tested positive on a TB skin test, a chest X-ray is recommended. TB skin testing should not be repeated once a person has tested positive.</div>", "customConditional": "show = data.tb_testing_required === 'yes' && data.tb_skin_test_positive === 'yes';"}, {"key": "heading_photo_prescription", "html": "<h1><strong>Upload Paperwork Request</strong></h1><p>If you have received paperwork outlining the tests required by your school or employer, please upload a clear photo or screenshot of that request here. Acceptable formats include mobile photos or screenshots.</p><p><strong>Providing this document helps ensure we order all the correct tests. If any required testing is missed, it may necessitate repeat visits to the lab.</strong></p><p><strong>If you do not have the paperwork at this time, that’s okay — you may skip the upload by checking the box below.</strong></p>", "type": "content", "input": false, "label": "Content"}, {"key": "skip_prescription_photo", "type": "checkbox", "label": "I do not have access to the testing paperwork at this time", "input": true, "defaultValue": false, "tableView": true}, {"key": "photo_upload_header", "html": "<h2>Photo Upload</h2>", "type": "content", "input": false, "label": "Content", "customConditional": "show = !data.skip_prescription_photo;"}, {"key": "uploadUrl", "url": "/app/q/{qpk}/formio-files/{pk}/", "type": "file", "image": true, "input": true, "label": "Upload your testing request paperwork", "webcam": true, "capture": "user", "storage": "url", "multiple": true, "fileTypes": [{"label": "", "value": ""}], "tableView": true, "validateWhenHidden": false, "customConditional": "show = !data.skip_prescription_photo;"}, {"key": "intake_kind", "type": "textfield", "input": true, "label": "Intake Kind", "hidden": true, "disabled": true, "tableView": true, "clearOnHide": false, "calculateValue": "value = !data.intake_denial_reasons?.length ? 'mail' : 'chat';"}, {"key": "intake_mt_key", "type": "textfield", "input": true, "label": "Intake Mail Template Key", "hidden": true, "disabled": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.intake_kind=='mail'?'intake-metabolic':'intake-denial'"}, {"key": "allowed_rt_keys", "type": "textfield", "input": true, "label": "Allowed Rep<PERSON>", "hidden": true, "disabled": true, "multiple": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.intake_mt_key=='intake-denial'?[]:['get-req','appointment-intake','edit-intake']"}]}