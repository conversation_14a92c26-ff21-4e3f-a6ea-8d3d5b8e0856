{"name": "hepatitis", "type": "form", "title": "Hepatitis", "display": "form", "components": [{"key": "hepBVaccinated", "type": "radio", "input": true, "label": "Have you been fully vaccinated for Hepatitis B?", "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No/Don't know", "value": false, "shortcut": ""}], "conditional": {"json": {"and": [{"!": {"var": "data.needle_stick"}}, {"in": ["HEPB", {"var": "data.assay_choices"}]}]}}, "optionsLabelPosition": "right"}, {"key": "previousHepBTest", "type": "radio", "input": true, "label": "Have you previously been tested for Hepatitis B?", "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "conditional": {"json": {"and": [{"!": {"var": "data.needle_stick"}}, {"in": ["HEPB", {"var": "data.assay_choices"}]}, {"var": "data.hepBVaccinated"}]}}, "optionsLabelPosition": "right"}, {"key": "hepBRiskFactorsVaccinated", "type": "selectboxes", "input": true, "label": "Do you have any of the following risk factors for Hepatitis B?", "values": [{"label": "Require immunosuppressive therapy", "value": "immunosuppressiveTherapy", "shortcut": ""}, {"label": "Are a donor of plasma, semen, organs, or tissue", "value": "donor", "shortcut": ""}, {"label": "Born to a mother with Hepatitis B", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shortcut": ""}, {"label": "Are currently pregnant", "value": "pregnant", "shortcut": ""}, {"label": "Are from an area where there is an intermediate (>2%) or higher risk of prevalence", "value": "", "shortcut": ""}], "inputType": "checkbox", "conditional": {"json": {"and": [{"!": {"var": "data.needle_stick"}}, {"in": ["HEPB", {"var": "data.assay_choices"}]}, {"!": {"var": "data.previousHepBTest"}}, {"var": "data.hepBVaccinated"}]}}, "optionsLabelPosition": "right"}, {"key": "noneOfTheAbovehepBRiskFactorsVaccinated", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a risk factor."}, "validate": {"custom": "valid = !!data.noneOfTheAbovehepBRiskFactorsVaccinated || _.some(_.values(data.hepBRiskFactorsVaccinated));"}, "conditional": {"json": {"and": [{"!": {"var": "data.needle_stick"}}, {"in": ["HEPB", {"var": "data.assay_choices"}]}, {"!": {"var": "data.previousHepBTest"}}, {"var": "data.hepBVaccinated"}]}}, "customClass": "mt-n3", "defaultValue": false}, {"key": "previousHepCTest", "type": "radio", "input": true, "label": "Have you previously been tested for Hepatitis C?", "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "conditional": {"json": {"and": [{"!": {"var": "data.needle_stick"}}, {"in": ["HEPC", {"var": "data.assay_choices"}]}]}}}, {"key": "HIVPositive", "type": "radio", "input": true, "label": "Have you tested positive or been treated for HIV?", "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "conditional": {"json": {"and": [{"!": {"var": "data.needle_stick"}}, {"in": ["HEPB", {"var": "data.assay_choices"}]}, {"!": {"var": "data.hepBVaccinated"}}]}}, "optionsLabelPosition": "right"}, {"key": "hepCPositive", "type": "radio", "input": true, "label": "Have you tested positive or been treated for Hepatitis C?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "tableView": false, "conditional": {"json": {"!": {"var": "data.needle_stick"}}}, "optionsLabelPosition": "right"}, {"key": "showHepRiskFactors", "type": "checkbox", "input": true, "label": "Show hepatitis risk factors", "hidden": true, "disabled": true, "clearOnHide": false, "defaultValue": true, "calculateValue": {"and": [{"!": {"var": "data.needle_stick"}}, {"or": [{"and": [{"in": ["HEPB", {"var": "data.assay_choices"}]}, {"!": {"var": "data.hepBVaccinated"}}]}, {"and": [{"in": ["HEPC", {"var": "data.assay_choices"}]}, {"!": {"var": "data.hepCPositive"}}]}]}]}}, {"key": "hepRiskFactors", "type": "selectboxes", "input": true, "label": "Do you have any of the following risk factors?", "values": [{"label": "Have used intranasal and inhaled drugs (e.g. crystal meth, cocaine)", "value": "drugsInhaled", "shortcut": ""}, {"label": "Current or past history of injection drug use", "value": "drugsIV", "shortcut": ""}, {"label": "Born, traveled, or resided in an Hepatitis B or C endemic country", "value": "endemicRegion", "shortcut": ""}, {"label": "Have been a hemodialysis patient", "value": "hemodialysis", "shortcut": ""}, {"label": "Have been homeless", "value": "homeless", "shortcut": ""}, {"label": "Have been in prison or a correctional facility", "value": "incarceration", "shortcut": ""}, {"label": "Have had needle stick injuries in the past", "value": "pastNeedleInjury", "shortcut": ""}, {"label": "Occupational exposure to blood or bodily fluids", "value": "occupation", "shortcut": ""}, {"label": "Shared medical devics, sharp instruments, or personal hygiene materials with someone who is Hepatitis B or C positive", "value": "sharedSharps", "shortcut": ""}, {"label": "Had tattoos or body piercing", "value": "tattoos", "shortcut": ""}, {"label": "Received healthcare where you were uncertain if sterilization of medical equipment was practiced", "value": "uncertainMedicalSterilization", "shortcut": ""}, {"label": "Multiple unprotected partners, engaged in sex work, or intercourse with a sex worker", "value": "unsafeSex", "shortcut": ""}], "inputType": "checkbox", "tableView": false, "conditional": {"json": {"var": "data.showHepRiskFactors"}}, "optionsLabelPosition": "right"}, {"key": "bloodPre1970", "type": "checkbox", "input": true, "label": "Received of blood transfusions, blood products, or an organ transplant before 1970", "tableView": false, "conditional": {"json": {"var": "data.showHepRiskFactors"}}, "customClass": "mt-n3", "defaultValue": false}, {"key": "bloodPre1992", "type": "checkbox", "input": true, "label": "Received of blood transfusions, blood products, or an organ transplant before 1992", "tableView": false, "conditional": {"json": {"and": [{"var": "data.showHepRiskFactors"}, {"!": {"var": "data.bloodPre1970"}}]}}, "customClass": "mt-n3", "defaultValue": false}, {"key": "noneOfTheAboveHepRiskFactors", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a risk factor."}, "validate": {"custom": "valid = !!data.noneOfTheAboveHepRiskFactors || !!data.bloodPre1970 || !!data.bloodPre1992 || _.some(_.values(data.hepRiskFactors));"}, "tableView": false, "conditional": {"json": {"var": "data.showHepRiskFactors"}}, "customClass": "mt-n3", "defaultValue": false}, {"key": "recTestFreqMonths_Hepatitis", "type": "textfield", "input": true, "label": "0: HEPB, 1: HEPC", "hidden": true, "disabled": true, "multiple": true, "tableView": true, "spellcheck": false, "clearOnHide": false, "calculateValue": [{"if": [{"in": ["HEPB", {"var": "data.assay_choices"}]}, {"if": [{"var": "data.hepBVaccinated"}, {"if": [{"and": [{"!": {"var": "data.previousHepBTest"}}, {"_some": {"_values": {"var": "data.hepBRiskFactorsVaccinated"}}}]}, 99, 101]}, {"if": [{"or": [{"_some": {"_values": {"var": "data.hepRiskFactors"}}}, {"var": "data.bloodPre1970"}, {"var": "data.hepCPositive"}, {"var": "data.HIVPositive"}]}, 12, 101]}]}, 101]}, {"if": [{"in": ["HEPC", {"var": "data.assay_choices"}]}, {"if": [{"!": {"var": "data.hepCPositive"}}, {"if": [{"or": [{"_some": {"_values": {"var": "data.hepRiskFactors"}}}, {"var": "data.bloodPre1970"}, {"var": "data.bloodPre1992"}]}, 12, 101]}, 101]}, 101]}]}, {"key": "recTestFreqMonths_HEPB", "type": "textfield", "input": true, "label": "", "hidden": true, "disabled": true, "tableView": true, "spellcheck": false, "clearOnHide": false, "calculateValue": {"var": "data.recTestFreqMonths_Hepatitis.0"}}, {"key": "recTestFreqMonths_HEPC", "type": "textfield", "input": true, "label": "", "hidden": true, "disabled": true, "tableView": true, "spellcheck": false, "clearOnHide": false, "calculateValue": {"var": "data.recTestFreqMonths_Hepatitis.1"}}]}