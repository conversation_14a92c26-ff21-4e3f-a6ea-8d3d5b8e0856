{"components": [{"key": "heading_jublia_rx", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "html": "<h1><strong>Fungal Medication and Testing</strong></h1><p>To provide the best possible care, please answer the following questions to the best of your abilities.</p>"}, {"key": "heading_reason_consult", "type": "content", "input": false, "html": "<br><h4>Why You're Reaching Out About Your Nail Fungus Today</h4>", "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "consult_reason", "type": "selectboxes", "input": true, "label": "I would like help with:", "inline": false, "values": [{"label": "Renewal of a previous antifungal prescription", "value": "renewal"}, {"label": "Treatment for my current nail infection", "value": "current_infection"}, {"label": "Managing side-effects of my current treatment", "value": "side_effects"}, {"label": "Change in treatment options (e.g., switching meds)", "value": "change_options"}, {"label": "Other", "value": "other"}, {"label": "None of the above", "value": "none", "customClass": "mt-n3"}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "confirm_label": "Reasons for consultation:", "optionsLabelPosition": "right"}, {"key": "consult_reason_other", "type": "textarea", "input": true, "label": "Please tell us more:", "tableView": true, "autoExpand": false, "confirm_label": "Other reason:", "customConditional": "show = data.consult_reason && (data.consult_reason.other === true || data.consult_reason.none === true);"}, {"key": "heading_previous_diagnosis", "html": "</br><h4>Previous Diagnosis</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "nail_diagnosis_known", "type": "radio", "input": true, "label": "Have you ever been formally diagnosed with a fungal nail infection (or another nail condition)?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Previously diagnosed:", "optionsLabelPosition": "right"}, {"key": "nail_diagnosis_type", "type": "selectboxes", "input": true, "label": "Which diagnosis were you given? (Select all that apply)", "inline": false, "inputType": "checkbox", "values": [{"label": "Fungal nail infection", "value": "onychomycosis"}, {"label": "Bacterial nail infection", "value": "bacterial_nail"}, {"label": "Nail psoriasis", "value": "psoriasis_nail"}, {"label": "Injury-related nail damage", "value": "trauma_dystrophy"}, {"label": "Other nail condition", "value": "other"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Diagnosis given:", "customConditional": "show = data.nail_diagnosis_known === 'yes';", "optionsLabelPosition": "right"}, {"key": "nail_diagnosis_type_other", "type": "textarea", "input": true, "label": "Please specify the diagnosis:", "tableView": true, "autoExpand": false, "confirm_label": "Other diagnosis:", "customConditional": "show = data.nail_diagnosis_type && data.nail_diagnosis_type.other === true;"}, {"key": "diagnosing_provider", "type": "radio", "input": true, "inline": false, "label": "Who provided this diagnosis?", "values": [{"label": "Family doctor", "value": "family_doctor"}, {"label": "Dermatologist", "value": "dermatologist"}, {"label": "Nurse practitioner", "value": "nurse_practitioner"}, {"label": "Podiatrist", "value": "podiatrist"}, {"label": "Self-diagnosis", "value": "self"}, {"label": "Other", "value": "other"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Diagnosing provider:", "customConditional": "show = data.nail_diagnosis_known === 'yes';", "optionsLabelPosition": "right"}, {"key": "diagnosing_provider_other", "type": "textarea", "input": true, "label": "Please specify who diagnosed you:", "tableView": true, "autoExpand": false, "confirm_label": "Other provider:", "customConditional": "show = data.diagnosing_provider === 'other';"}, {"key": "heading_nail_onset_course", "type": "content", "input": false, "html": "</br><h4>Current Nail-Infection Details</h4>", "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "nail_start_time", "type": "select", "input": true, "label": "When did you first notice changes to the nail(s)?", "widget": "html5", "data": {"values": [{"label": "< 1 month ago", "value": "lt1mo"}, {"label": "1-3 months ago", "value": "1_3mo"}, {"label": "3-6 months ago", "value": "3_6mo"}, {"label": "6-12 months ago", "value": "6_12mo"}, {"label": "> 1 year ago", "value": "gt1yr"}, {"label": "I'm not sure", "value": "unknown"}]}, "validate": {"required": true}, "tableView": true, "confirm_label": "Changes noticed since:"}, {"key": "nail_first_or_repeat", "type": "radio", "input": true, "label": "Is this your first nail infection, or have you had nail fungus before?", "inline": false, "values": [{"label": "First time", "value": "first"}, {"label": "I've had it before", "value": "repeat"}, {"label": "Not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "First or repeat:", "customConditional": "show = !!data.nail_start_time;", "optionsLabelPosition": "right"}, {"key": "nail_count_current", "type": "select", "input": true, "label": "How many nails are affected right now?", "widget": "html5", "data": {"values": [{"label": "1 nail", "value": "one"}, {"label": "2 nails", "value": "two"}, {"label": "3-5 nails", "value": "three_five"}, {"label": "6-10 nails", "value": "six_ten"}, {"label": "All fingernails / toenails", "value": "all"}, {"label": "Unsure", "value": "unsure"}]}, "validate": {"required": true}, "tableView": true, "confirm_label": "Nails affected:", "customConditional": "show = !!data.nail_course_since_onset;"}, {"key": "nail_symptoms_pain", "type": "radio", "input": true, "label": "Do the affected nail(s) cause pain or discomfort?", "inline": false, "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Occasionally", "value": "sometimes"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Painful:", "customConditional": "show = !!data.nail_count_current;", "optionsLabelPosition": "right"}, {"key": "infection_resolution_pattern", "type": "radio", "input": true, "label": "When you had a nail infection before, how did it clear up?", "inline": false, "values": [{"label": "Needed oral prescription pills", "value": "needs_oral_rx"}, {"label": "Cleared with prescription topical treatment", "value": "topical_rx"}, {"label": "Improved with over-the-counter products", "value": "otc_helped"}, {"label": "Eventually cleared on its own", "value": "spontaneous"}, {"label": "Never fully cleared", "value": "never_cleared"}, {"label": "I'm not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Past clearance pattern:", "customConditional": "show = data.nail_first_or_repeat === 'repeat';", "optionsLabelPosition": "right"}, {"key": "otc_effectiveness_nail", "type": "radio", "input": true, "label": "Have non-prescription antifungal lacquers, creams, or sprays helped this infection?", "inline": false, "values": [{"label": "Yes - they usually help", "value": "yes_often"}, {"label": "Sometimes - depends on product", "value": "sometimes"}, {"label": "No - they don't seem to help", "value": "no_effect"}, {"label": "I haven't really tried", "value": "not_tried"}], "validate": {"required": true}, "tableView": true, "confirm_label": "OTC product effect:", "customConditional": "show = !!data.nail_course_since_onset;", "optionsLabelPosition": "right"}, {"key": "heading_current_nails", "type": "content", "input": false, "label": "Content", "tableView": false, "html": "<h3>Current Nails Affected</h3><p>Please tell us which nails are currently affected and give a few details about when the changes began.</p>"}, {"key": "affected_nail_sites", "type": "selectboxes", "input": true, "label": "Which nails are affected?", "confirm_label": "Affected sites:", "values": [{"label": "Fingernails", "value": "fingernails"}, {"label": "Toenails", "value": "toenails"}], "validate": {"required": true}, "tableView": true, "inputType": "checkbox", "optionsLabelPosition": "right"}, {"key": "fingernail_grid", "type": "datagrid", "input": true, "label": "Select the affected fingernails (check all that apply):", "description": "R = right,  <PERSON> = left. Thumb / big toe is digit 1, index 2, middle 3, ring 4, pinky 5.", "tableView": false, "disableAddingRemovingRows": true, "hideAddAnother": true, "rowOpen": false, "defaultValue": [{"digit_name": "Thumb (1)", "R": false, "L": false}, {"digit_name": "Index (2)", "R": false, "L": false}, {"digit_name": "Middle (3)", "R": false, "L": false}, {"digit_name": "Ring (4)", "R": false, "L": false}, {"digit_name": "<PERSON><PERSON> (5)", "R": false, "L": false}], "components": [{"key": "digit_name", "type": "textfield", "label": "Finger", "input": true, "disabled": true, "tableView": true, "persistent": false, "modalEdit": false}, {"key": "R", "type": "checkbox", "label": "R", "labelPosition": "top", "tableView": true}, {"key": "L", "type": "checkbox", "label": "L", "labelPosition": "top", "tableView": true}]}, {"key": "toenail_grid", "type": "datagrid", "input": true, "label": "Select the affected toenails (check all that apply):", "description": "R = right,  L = left. Big toe is digit 1, second toe 2, middle 3, fourth 4, little toe 5.", "tableView": false, "disableAddingRemovingRows": true, "hideAddAnother": true, "rowOpen": false, "defaultValue": [{"digit_name": "Big toe (1)", "R": false, "L": false}, {"digit_name": "Second toe (2)", "R": false, "L": false}, {"digit_name": "Middle toe (3)", "R": false, "L": false}, {"digit_name": "Fourth toe (4)", "R": false, "L": false}, {"digit_name": "Little toe (5)", "R": false, "L": false}], "components": [{"key": "digit_name", "type": "textfield", "label": "<PERSON>e", "input": true, "disabled": true, "tableView": true, "persistent": false, "modalEdit": false}, {"key": "R", "type": "checkbox", "label": "R", "labelPosition": "top", "tableView": true}, {"key": "L", "type": "checkbox", "label": "L", "labelPosition": "top", "tableView": true}]}, {"key": "nails_onset_pattern", "type": "radio", "input": true, "label": "Did all of the affected nails change around the same time, or at different times?", "confirm_label": "Onset pattern:", "values": [{"label": "All started around the same time", "value": "same_time"}, {"label": "Different nails changed at different times", "value": "different_times"}, {"label": "I'm not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.affected_nail_sites && _.some(_.values(data.affected_nail_sites));", "optionsLabelPosition": "right"}, {"key": "heading_other_causes", "type": "content", "input": false, "label": "Content", "tableView": false, "html": "<h3>Other Conditions</h3><p>The next few questions help us understand if your nail changes are related to other conditions that can look like a fungal infection.</p>"}, {"key": "personal_psoriasis", "type": "radio", "input": true, "label": "Have you ever been diagnosed with psoriasis?", "confirm_label": "Personal psoriasis history:", "values": [{"label": "Yes", "value": true}, {"label": "No", "value": false}, {"label": "I'm not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "dark_streak_present", "type": "radio", "input": true, "label": "Do any of the affected nails have a dark brown or black streak or spot?", "confirm_label": "Dark streak present:", "values": [{"label": "Yes", "value": true}, {"label": "No", "value": false}, {"label": "I'm not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "dark_streak_duration", "type": "select", "input": true, "widget": "html5", "label": "How long have you noticed the dark streak or spot?", "data": {"values": [{"label": "Less than 1 month", "value": "lt_1m"}, {"label": "1-3 months", "value": "1_3m"}, {"label": "3-6 months", "value": "3_6m"}, {"label": "More than 6 months", "value": "gt_6m"}, {"label": "I'm not sure / can't remember", "value": "unsure"}]}, "validate": {"required": true}, "tableView": true, "confirm_label": "Streak present for:", "customConditional": "show = data.dark_streak_present === true;"}, {"key": "dark_streak_change", "type": "radio", "input": true, "label": "Since first noticing it, has the streak been changing in size or colour?", "confirm_label": "Streak changing:", "values": [{"label": "Getting larger or darker", "value": "larger_darker"}, {"label": "Staying about the same", "value": "stable"}, {"label": "Getting smaller or lighter", "value": "smaller_lighter"}, {"label": "I'm not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.dark_streak_present === true;", "optionsLabelPosition": "right"}, {"key": "dark_streak_skin_extension", "type": "radio", "input": true, "label": "Does the dark streak extend onto the surrounding skin (the cuticle or skin next to the nail)?", "confirm_label": "Pigment on skin:", "values": [{"label": "Yes", "value": true}, {"label": "No", "value": false}, {"label": "I'm not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.dark_streak_present === true;", "optionsLabelPosition": "right"}, {"key": "dark_streak_symptoms", "type": "selectboxes", "input": true, "label": "Do you have any of the following in the nail with the streak?", "confirm_label": "Associated symptoms:", "values": [{"label": "Pain or tenderness", "value": "pain"}, {"label": "Bleeding", "value": "bleeding"}, {"label": "Nail splitting or cracking", "value": "splitting"}, {"label": "None of the above", "value": "none"}], "tableView": true, "validate": {"required": false}, "customConditional": "show = data.dark_streak_present === true;", "optionsLabelPosition": "right"}, {"key": "heading_inperson_warning", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = _.some(data.dark_streak_symptoms, function(v){ return v === true; });", "html": "<div class=\"alert alert-warning\" style=\"border:1px solid #f0ad4e;padding:12px;border-radius:6px;\"><strong>Important:</strong> TeleTest can arrange nail testing and prescribe treatment. <br/>However,  dark streaks or spots in a nail can occasionally signal more serious conditions, we <u>strongly recommend an in-person examination</u> through a local clinic or dermatologist even if you are prescribed treatment within the next 2 weeks.</div>"}, {"key": "acknowledge_inperson_warning", "type": "radio", "input": true, "label": "Please confirm you understand this recommendation:", "confirm_label": "Understands need for in-person exam:", "values": [{"label": "I understand", "value": "understand"}, {"label": "I need clarification", "value": "clarification"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = _.some(data.dark_streak_symptoms, function(v){ return v === true; });", "optionsLabelPosition": "right"}, {"key": "clarification_info", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.acknowledge_inperson_warning === 'clarification';", "html": "<div style=\"border-left:4px solid #17a2b8;padding:10px 12px;margin-top:10px;\"><strong>Why is an in-person visit important?</strong><br/>In extremely rare cases, a new dark streak or spot under a nail can be related to <em>melanoma</em>, a type of skin cancer. A hands-on examination—sometimes with a quick biopsy—is the safest way to rule this out.</div>"}, {"key": "acknowledge_clarification", "type": "radio", "input": true, "label": "After reading the information above, do you understand why an in-person exam is advised?", "confirm_label": "Understands after clarification:", "values": [{"label": "Yes, I understand now", "value": "understand_now"}, {"label": "No, I'm still unsure", "value": "still_unsure"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.acknowledge_inperson_warning === 'clarification';", "optionsLabelPosition": "right"}, {"key": "nail_trauma_history", "type": "radio", "input": true, "label": "Did any of the affected nails have an injury (e.g., stubbed toe, slammed finger) before the changes began?", "confirm_label": "Prior trauma:", "values": [{"label": "Yes", "value": true}, {"label": "No", "value": false}, {"label": "I'm not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "heading_investigations", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "html": "<h3>Previous Investigations &amp; Test Results</h3>"}, {"key": "previous_nail_testing", "type": "radio", "input": true, "label": "Have you ever had any nail samples (clippings or scrapings) tested for fungus or yeast?", "confirm_label": "Prior nail testing:", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "I'm not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "nail_test_type", "type": "selectboxes", "input": true, "label": "Which test(s) were done on your nail sample?", "description": "Select all that apply. If you're not sure, choose “I'm not sure.”", "confirm_label": "Type of tests:", "values": [{"label": "Microscope look at nail clippings (KOH test)", "value": "koh_microscopy"}, {"label": "Lab culture to grow the fungus (fungal culture)", "value": "fungal_culture"}, {"label": "DNA test for fungus (PCR)", "value": "pcr_test"}, {"label": "Other", "value": "other"}, {"label": "I'm not sure", "value": "unsure"}], "validate": {"custom": "valid = _.some(_.values(data.nail_test_type));"}, "tableView": true, "customConditional": "show = data.previous_nail_testing === 'yes';", "optionsLabelPosition": "right"}, {"key": "nail_test_type_other", "type": "textarea", "input": true, "label": "Please specify the other test:", "tableView": true, "autoExpand": false, "confirm_label": "Other test type:", "customConditional": "show = data.nail_test_type && data.nail_test_type.other === true;"}, {"key": "nail_test_result", "type": "radio", "input": true, "label": "Did any of these tests confirm a fungal infection?", "confirm_label": "Test confirmed fungus:", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "I don't remember", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.previous_nail_testing === 'yes';", "optionsLabelPosition": "right"}, {"key": "identified_organism", "type": "selectboxes", "input": true, "label": "Which organism(s) did the lab report mention?", "description": "Select all that apply. If you're unsure, choose “I don't know / not sure.”", "confirm_label": "Identified organism(s):", "values": [{"label": "I don't know / not sure", "value": "unknown"}, {"label": "Trichophyton rubrum (common fungus)", "value": "trichophyton_rubrum"}, {"label": "Trichophyton interdigitale / mentagrophytes", "value": "trichophyton_interdigitale"}, {"label": "Candida species (yeast)", "value": "candida"}, {"label": "Fusarium species", "value": "fusarium"}, {"label": "Scopulariopsis brevicaulis", "value": "scopulariopsis"}, {"label": "Aspergillus species", "value": "aspergillus"}, {"label": "Penicillium species", "value": "penicillium_nonpathogenic"}, {"label": "Other pathogen (not listed above)", "value": "other_pathogen"}], "validate": {"custom": "valid = _.some(_.values(data.identified_organism));"}, "tableView": true, "customConditional": "show = data.nail_test_result === 'yes';", "optionsLabelPosition": "right"}, {"key": "nail_test_date", "type": "select", "input": true, "widget": "html5", "label": "When was your most recent nail test performed?", "data": {"values": [{"label": "Within the last 3 months", "value": "lt_3m"}, {"label": "3-6 months ago", "value": "3_6m"}, {"label": "6-12 months ago", "value": "6_12m"}, {"label": "More than a year ago", "value": "gt_12m"}]}, "validate": {"required": true}, "tableView": true, "confirm_label": "Most recent test:", "customConditional": "show = data.previous_nail_testing === 'yes';"}, {"key": "heading_photos_nails", "type": "content", "input": false, "label": "Content", "html": "<h2>Required Photos</h2><p>Upload <strong>two clear photos</strong> of your affected nails: 1) a close-up of the nail(s) in question and 2) a wider shot that shows the whole finger or toe for context.</p>", "tableView": false, "refreshOnChange": false}, {"key": "same_nails_as_tested", "type": "radio", "input": true, "label": "Are the nail(s) you'd like treated today the same one(s) that were tested?", "confirm_label": "Same nails as tested:", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.previous_nail_testing === 'yes';", "optionsLabelPosition": "right"}, {"key": "uploadUrl", "type": "file", "input": true, "confirm_label": "Confirm nail photos uploaded:", "label": "Upload Nail Photos", "image": true, "webcam": true, "capture": "user", "storage": "url", "url": "/app/q/{qpk}/formio-files/{pk}/", "multiple": true, "fileTypes": [{"label": "", "value": ""}], "tableView": true, "validate": {"required": true, "minFiles": 2, "maxFiles": 10}}, {"key": "photo_confirm_nails", "type": "checkbox", "input": true, "confirm_label": "Confirm photo-upload acknowledgment:", "label": "I've uploaded two clear photos of my affected nails.", "validate": {"required": true}, "tableView": false}, {"key": "intake_denial_reasons", "type": "textfield", "input": true, "label": "Intake Denial Reasons", "hidden": true, "disabled": true, "multiple": true, "adminFlag": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "value = [];", "refreshOnChange": true}, {"key": "intake_kind", "type": "textfield", "input": true, "label": "Intake Kind", "hidden": true, "disabled": true, "tableView": true, "clearOnHide": false, "calculateValue": "value = !data.intake_denial_reasons?.length ? 'mail' : 'chat';"}, {"key": "intake_mt_key", "type": "textfield", "input": true, "label": "Intake Mail Template Key", "hidden": true, "disabled": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.intake_kind === 'mail' ? 'intake-nail' : 'intake-denial';"}, {"key": "allowed_rt_keys", "type": "textfield", "input": true, "label": "Allowed Rep<PERSON>", "hidden": true, "disabled": true, "multiple": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.intake_mt_key === 'intake-denial' ? [] : ['get-rx', 'appointment-intake', 'edit-intake'];"}]}