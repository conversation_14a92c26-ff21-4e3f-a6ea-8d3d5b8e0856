{"components": [{"key": "celiac_diagnosis", "type": "radio", "input": true, "label": "Do you currently have a diagnosis of celiac disease?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "no_celiac_test_previous_celiac", "html": "<p class='text-red'>Celiac testing is not medically indicated if you have already been diagnosed with celiac disease.</p>", "type": "content", "input": true, "tableView": false, "clearOnHide": false, "customConditional": "show = data.celiac_diagnosis == true;"}, {"key": "on_gluten_free_diet", "type": "radio", "input": true, "label": "Are you currently on a gluten free diet?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.celiac_diagnosis == false;", "optionsLabelPosition": "right"}, {"key": "no_celiac_test_gluten_challenge", "html": "<p class='text-red'>Celiac testing requires a gluten challenge, or you can have a false negative result.</p><p>This consists of taking 8-10 grams (4-6 slices of bread per day) for 6-8 weeks before completing an celiac test.</p>", "type": "content", "input": true, "tableView": false, "clearOnHide": false, "customConditional": "show = !!data.on_gluten_free_diet"}, {"key": "celiac_red_flags", "type": "selectboxes", "input": true, "label": "Do you have any of the following symptoms:", "values": [{"label": "Fevers", "value": "fevers", "shortcut": ""}, {"label": "Night Sweats", "value": "night_sweats", "shortcut": ""}, {"label": "Weight Loss (Without Dietary/Activity Changes)", "value": "weight_loss", "shortcut": ""}, {"label": "Rectal Bleeding (Red or Tarry/Black Stools)", "value": "rectal_bleeding", "shortcut": ""}, {"label": "Feel lightheaded/faint", "value": "preynscope", "shortcut": ""}, {"label": "Chest Pressure/Pain", "value": "chest_pressure", "shortcut": ""}, {"label": "Shortness of Breath", "value": "dyspnea", "shortcut": ""}], "inputType": "checkbox", "tableView": true, "customConditional": "show = data.celiac_diagnosis == false && data.on_gluten_free_diet == false;", "optionsLabelPosition": "right"}, {"key": "no_celiac_rf", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = _.some(_.values(data.celiac_red_flags)) || data.no_celiac_rf;"}, "tableView": false, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.celiac_diagnosis == false && data.on_gluten_free_diet == false;"}, {"key": "family_member_celiac", "type": "radio", "input": true, "label": "Do you have a 1st degree relative (mother, brother, father, sister) with a diagnosis of celiac disease (i.e. not gluten intolerance)?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = !_.some(_.values(data.celiac_red_flags)) && data.celiac_diagnosis == false && data.on_gluten_free_diet == false;", "optionsLabelPosition": "right"}, {"key": "celiac_symptoms", "type": "selectboxes", "input": true, "label": "Do you have any of the following:", "values": [{"label": "Three or more loose bowel movements per day", "value": "chronic_diarrhea", "shortcut": ""}, {"label": "A history of iron deficency anemia", "value": "iron_deficiency_anemia", "shortcut": ""}, {"label": "Greasy stools (Appear oily or float)", "value": "steatorrhea", "shortcut": ""}, {"label": "Osteoperosis", "value": "osteoperosis", "shortcut": ""}, {"label": "Bloating after eating", "value": "bloating", "shortcut": ""}, {"label": "History of low B12 on bloodwork", "value": "low_b12", "shortcut": ""}, {"label": "Eczema or rough skin over your elbows and front of your knees", "value": "dermatitis_herpetiformis", "shortcut": ""}, {"label": "Diagnosed with hypothroidism", "value": "hypothroidism_diagnosis", "shortcut": ""}], "inputType": "checkbox", "tableView": true, "customConditional": "show = data.family_member_celiac == false && data.celiac_diagnosis == false;", "optionsLabelPosition": "right"}, {"key": "no_celiac_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = _.some(_.values(data.celiac_symptoms)) || data.no_celiac_symptoms;"}, "tableView": false, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.family_member_celiac == false && data.celiac_diagnosis == false;"}, {"key": "symptoms_greater_12_months", "type": "radio", "input": true, "label": "Has your symptoms/diagnosis been present for more than 12 months?", "inline": false, "values": [{"label": "More than 12 months", "value": true, "shortcut": ""}, {"label": "Less than 12 months", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = _.some(_.values(data.celiac_symptoms))", "optionsLabelPosition": "right"}, {"key": "no_indications", "type": "textfield", "input": true, "label": "No Indication:", "hidden": true, "disabled": true, "multiple": false, "tableView": true, "clearOnHide": false, "defaultValue": true, "calculateValue": "value = !data.family_member_celiac && (!_.some(_.values(data.celiac_symptoms)) || !data.symptoms_greater_12_months);", "refreshOnChange": true}, {"key": "contraindication_keys", "type": "textfield", "input": true, "label": "Contraindication keys:", "hidden": true, "disabled": true, "multiple": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "value = _.concat((_.some(_.values(data.celiac_red_flags)) ? ['celiac_red_flags'] : []), (!!data.celiac_diagnosis ? ['celiac_diagnosis'] : []), (!!data.on_gluten_free_diet ? ['on_gluten_free_diet'] : []));", "refreshOnChange": true}]}