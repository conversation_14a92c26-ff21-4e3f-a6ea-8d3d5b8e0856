{"components": [{"key": "heading_prep_intake", "html": "<h1><center><strong>Pre-Exposure Prophylaxis (PrEP) Intake History</strong></center></h1><p>Please provide your health and risk history to help us determine your eligibility and tailor your PrEP regimen.</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "heading_prep_history", "html": "<h2><strong>PrEP History</strong></h2><p>Please provide details about your past experience with PrEP (if applicable).</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "previous_prep_use", "type": "radio", "input": true, "label": "Have you been on PrEP before?", "inline": false, "values": [{"label": "Yes", "value": true}, {"label": "No", "value": false}, {"label": "Prefer not to disclose", "value": "prefer_not_to_disclose"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "last_prep_use", "data": {"values": [{"label": "< 1 week ago", "value": "less_1_week"}, {"label": "1-3 weeks ago", "value": "1-3_weeks"}, {"label": "3 weeks - 3 months", "value": "3_weeks_90_days"}, {"label": "3-6 months ago", "value": "3-6_months"}, {"label": "6-12 months ago", "value": "6-12_months"}, {"label": "1-2 years ago", "value": "1-2_years"}, {"label": "More than 2 years ago", "value": "2+_years"}, {"label": "I don't remember", "value": "did_not_remember"}]}, "type": "select", "input": true, "label": "When were you last on PrEP?", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.previous_prep_use == true;", "optionsLabelPosition": "right"}, {"key": "daily_prep_brand", "data": {"values": [{"label": "Truvada (brand name)", "value": "truvada"}, {"label": "Descovy (brand name)", "value": "descovy"}, {"label": "Generic Truvada", "value": "generic_truvada"}, {"label": "I don't remember", "value": "did_not_remember"}]}, "type": "select", "input": true, "label": "Which brand of Daily PrEP did you take?", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.prep_type_taken == 'daily_prep';", "optionsLabelPosition": "right"}, {"key": "prep_stopping_reason", "type": "selectboxes", "input": true, "label": "What was your reason for stopping PrEP? (Select all that apply)", "values": [{"label": "No longer felt at risk", "value": "no_risk"}, {"label": "Side effects", "value": "side_effects"}, {"label": "Doctor advised stopping", "value": "doctor_advised"}, {"label": "Insurance or cost issues", "value": "cost_issues"}, {"label": "Difficulty with adherence (e.g., missed doses)", "value": "adherence_difficulty"}, {"label": "Switched to a different HIV prevention method", "value": "switched_method"}, {"label": "Other", "value": "other"}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "customConditional": "show = data.previous_prep_use == true;", "optionsLabelPosition": "right"}, {"key": "prep_stopping_reason_other", "type": "textarea", "input": true, "label": "Please specify your reason for stopping PrEP:", "tableView": true, "autoExpand": false, "customConditional": "show = data.prep_stopping_reason && data.prep_stopping_reason.other;"}, {"key": "heading_prep_side_effects", "html": "<h2><strong>Medication Side Effects</strong></h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.previous_prep_use == true;"}, {"key": "experienced_prep_side_effects", "type": "radio", "input": true, "label": "Did you experience any side effects while taking PrEP?", "inline": false, "values": [{"label": "Yes", "value": true}, {"label": "No", "value": false}, {"label": "I don't remember", "value": "did_not_remember"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.previous_prep_use == true;", "optionsLabelPosition": "right"}, {"key": "prep_side_effect_severity", "type": "radio", "input": true, "label": "How severe were the side effects?", "inline": false, "values": [{"label": "Mild (e.g., minor discomfort, resolved on its own)", "value": "mild"}, {"label": "Moderate (e.g., noticeable but manageable symptoms)", "value": "moderate"}, {"label": "Severe (e.g., required medical attention or led to stopping PrEP)", "value": "severe"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.experienced_prep_side_effects == true;", "optionsLabelPosition": "right"}, {"key": "prep_side_effects_resolved", "type": "radio", "input": true, "label": "Did your side effects disappear after the initial adjustment period on PrEP?", "inline": false, "values": [{"label": "Yes, they went away after a short time", "value": "resolved"}, {"label": "No, they persisted throughout use", "value": "persisted"}, {"label": "I stopped PrEP before they could resolve", "value": "stopped_early"}, {"label": "I don't remember", "value": "did_not_remember"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.experienced_prep_side_effects == true;", "optionsLabelPosition": "right"}, {"key": "prep_side_effects_list", "type": "selectboxes", "input": true, "label": "Which side effects did you experience? (Select all that apply)", "values": [{"label": "Nausea or vomiting", "value": "nausea_vomiting"}, {"label": "Diarrhea", "value": "diarrhea"}, {"label": "Headache", "value": "headache"}, {"label": "Dizziness", "value": "dizziness"}, {"label": "Fatigue", "value": "fatigue"}, {"label": "Depression or mood changes", "value": "mood_changes"}, {"label": "Kidney issues (e.g., changes in kidney function)", "value": "kidney_issues"}, {"label": "Bone density loss or joint pain", "value": "bone_density_loss"}, {"label": "Skin rash", "value": "rash"}, {"label": "Other", "value": "other"}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "customConditional": "show = data.prep_side_effects_resolved == 'persisted' || data.prep_side_effects_resolved == 'stopped_early';", "optionsLabelPosition": "right"}, {"key": "prep_side_effects_other", "type": "textarea", "input": true, "label": "Please specify any other side effects you experienced:", "tableView": true, "autoExpand": false, "customConditional": "show = data.prep_side_effects_list && data.prep_side_effects_list.other;"}, {"key": "heading_prep_treatment_indication", "html": "<h2><strong>PrEP Treatment Indication</strong></h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.previous_prep_use == true || data.prep_interest_reasons;"}, {"key": "prep_risk_factors", "type": "selectboxes", "input": true, "label": "Which risk factors apply to you? (Select all that apply)", "values": [{"label": "I currently have or plan on having multiple sexual partners", "value": "multiple_partners"}, {"label": "I don't use condoms consistently with new partners", "value": "inconsistent_condom"}, {"label": "My partner is HIV positive", "value": "partner_hiv_positive"}, {"label": "I was recently diagnosed with an STI", "value": "recent_sti"}, {"label": "I engage in sex work", "value": "sex_work"}, {"label": "I inject drugs and share needles", "value": "inject_drug_use"}, {"label": "I have a partner who injects drugs", "value": "partner_injects_drugs"}, {"label": "Other", "value": "other"}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "optionsLabelPosition": "right"}, {"key": "prep_risk_factors_other", "type": "textarea", "input": true, "label": "Please specify any other risk factors:", "tableView": true, "autoExpand": false, "customConditional": "show = data.prep_risk_factors && data.prep_risk_factors.other;"}, {"key": "heading_partner_hiv", "html": "<h2><strong>Your Partner's HIV Status</strong></h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.prep_risk_factors && data.prep_risk_factors.partner_hiv_positive;"}, {"key": "partner_on_antiviral_medication", "type": "radio", "input": true, "label": "Is your partner on antiviral medication for HIV?", "inline": false, "values": [{"label": "Yes, my partner is on antiviral medication", "value": "yes"}, {"label": "No, my partner is not on antiviral medication", "value": "no"}, {"label": "I don't know", "value": "dont_know"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.prep_risk_factors && data.prep_risk_factors.partner_hiv_positive;", "optionsLabelPosition": "right"}, {"key": "partner_viral_load_status", "type": "radio", "input": true, "label": "Do you know if your partner has an undetectable viral load?", "inline": false, "values": [{"label": "Yes, their viral load is undetectable", "value": "undetectable"}, {"label": "No, their viral load is detectable", "value": "detectable"}, {"label": "I don't know", "value": "dont_know"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.partner_on_antiviral_medication;", "optionsLabelPosition": "right"}, {"key": "partner_medication_adherence", "type": "radio", "input": true, "label": "Is your partner consistent with taking their HIV medication as directed by their doctor?", "inline": false, "values": [{"label": "Yes, they take it consistently", "value": "consistent"}, {"label": "No, they miss doses frequently", "value": "inconsistent"}, {"label": "I don't know", "value": "dont_know"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.partner_on_antiviral_medication == 'yes';", "optionsLabelPosition": "right"}, {"key": "heading_prep_health_considerations", "html": "<h2><strong>Important Health Considerations</strong></h2><p>Before starting PrEP, it’s important to understand any health factors that may affect its use. Please answer the following questions to ensure PrEP is the right choice for you.</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "kidney_disease_history", "type": "radio", "input": true, "label": "Have you ever been diagnosed with kidney disease or experienced kidney problems?", "inline": false, "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "I don't know", "value": "dont_know"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "bone_health_diagnosis", "type": "radio", "input": true, "label": "Have you ever been diagnosed with osteoporosis or osteopenia (low bone density)?", "inline": false, "values": [{"label": "Yes", "value": true}, {"label": "No", "value": false}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "content6", "html": "<h2 style=\"text-align:center;\"><strong>Test History</strong></h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "last_sti_test", "type": "radio", "input": true, "label": "When was your last test for STIs outside of using TeleTest?", "inline": false, "values": [{"label": "Never", "value": "never"}, {"label": "1-14 days ago", "value": "1-14_days"}, {"label": "15-30 days ago", "value": "15-30_days"}, {"label": "31-90 days ago", "value": "31-90_days"}, {"label": "90+ days ago", "value": "90+_days"}, {"label": "Prefer not to disclose", "value": "prefer_not_to_disclose"}], "tableView": true, "confirm_label": "Last STI test:", "optionsLabelPosition": "right"}, {"key": "past_std", "type": "radio", "input": true, "label": "Have you tested positive or been treated for syphilis, HIV, or Hepatitis C in the past?", "inline": false, "values": [{"label": "Yes", "value": true}, {"label": "No", "value": false}, {"label": "Prefer not to disclose", "value": "prefer_not_to_disclose"}, {"label": "Doesn't apply to me as I haven't been tested before", "value": "N/A"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "heading_syphilis_history", "html": "</br><h4>Syphilis</h4>", "type": "content", "input": false, "label": "<PERSON><PERSON><PERSON><PERSON>", "tableView": false, "refreshOnChange": true, "customConditional": "show = data.past_std == true"}, {"key": "previous_syphilis_positive", "type": "radio", "input": true, "label": "Have you tested positive for syphilis in the past?", "inline": false, "values": [{"label": "Yes", "value": true}, {"label": "No", "value": false}, {"label": "Prefer not to disclose", "value": "prefer_not_to_disclose"}, {"label": "Doesn't apply to me as I haven't been tested before", "value": "N/A"}, {"label": "I don't understand this question", "value": "did_not_understand"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.past_std == true", "optionsLabelPosition": "right"}, {"key": "previous_syphilis_treatment", "type": "radio", "input": true, "label": "Have you been treated for syphilis in the past?", "inline": false, "values": [{"label": "Yes", "value": true}, {"label": "No", "value": false}, {"label": "I don't understand this question", "value": "did_not_understand"}, {"label": "I don't know if syphilis is what I was treated for", "value": "did_not_know_if_treated_for_syphilis"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.previous_syphilis_positive == true", "optionsLabelPosition": "right"}, {"key": "syphilis_last_treatment", "type": "radio", "input": true, "label": "What was your last treatment for syphilis?", "inline": false, "values": [{"label": "An injection of penicillin once", "value": "early_syphilis_single_dose"}, {"label": "An injection of penicillin once every week for three weeks", "value": "latent_syphilis_three_doses"}, {"label": "Two weeks of doxycycline", "value": "early_syphilis_doxycycline_2_weeks"}, {"label": "Four weeks of doxycycline", "value": "latent_syphilis_doxycycline_4_weeks"}, {"label": "Other", "value": "other"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.previous_syphilis_treatment == true", "optionsLabelPosition": "right"}, {"key": "syphilis_treatment_location", "type": "radio", "input": true, "label": "Where were you treated for syphilis?", "inline": false, "values": [{"label": "Canada - within Ontario", "value": "canada_ontario"}, {"label": "Canada - outside of Ontario", "value": "canada_outside_ontario"}, {"label": "The United States", "value": "united_states"}, {"label": "A different country", "value": "different_country"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.previous_syphilis_treatment == true", "optionsLabelPosition": "right"}, {"key": "last_rpr_titre_level", "data": {"values": [{"label": "I have no idea what this is", "value": "did_not_know"}, {"label": "I don't remember", "value": "did_not_remember"}, {"label": "1:1", "value": "1_1"}, {"label": "1:2", "value": "1_2"}, {"label": "1:4", "value": "1_4"}, {"label": "1:8", "value": "1_8"}, {"label": "1:16", "value": "1_16"}, {"label": "1:32", "value": "1_32"}, {"label": "1:64", "value": "1_64"}, {"label": "1:128", "value": "1_128"}, {"label": "1:256", "value": "1_256"}]}, "type": "select", "input": true, "label": "What was your last syphilis titre level?", "widget": "html5", "dataSrc": "values", "validate": {"required": true}, "tableView": true, "customConditional": "show = !!data.syphilis_treatment_location;", "optionsLabelPosition": "right"}, {"key": "last_rpr_test", "data": {"values": [{"label": "I have no idea", "value": "did_not_know"}, {"label": "<1 week ago", "value": "less_1_week"}, {"label": "2-4 weeks ago", "value": "2-4_weeks_ago"}, {"label": "4-6 weeks ago", "value": "4-6_weeks"}, {"label": "6 weeks - 3 months ago", "value": "6_weeks-3_months"}, {"label": "3-6 months ago", "value": "3-6_months"}, {"label": "6-12 months ago", "value": "6-12_months"}, {"label": "12-24 months ago", "value": "12-24_months"}, {"label": "24+ months ago", "value": "24+_months"}, {"label": "Never had one", "value": "never_had"}]}, "type": "select", "input": true, "label": "When was your last titre test?", "widget": "html5", "tooltip": "An RPR test measures the levels of syphilis antibodies in your blood.", "validate": {"required": true}, "tableView": true, "customConditional": "show = !!data.last_rpr_titre_level;", "optionsLabelPosition": "right"}, {"key": "heading_hiv_history", "html": "<h4>HIV</h4>", "type": "content", "input": false, "label": "HIV Details", "tableView": false, "refreshOnChange": true, "customConditional": "show = data.past_std == true"}, {"key": "previous_hiv_positive", "type": "radio", "input": true, "label": "Have you tested positive for HIV in the past?", "inline": false, "values": [{"label": "Yes", "value": true}, {"label": "No", "value": false}, {"label": "Prefer not to disclose", "value": "prefer_not_to_disclose"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.past_std == true", "optionsLabelPosition": "right"}, {"key": "unprotected_sex", "type": "radio", "input": true, "label": "Have you had vaginal or anal sex without protection (i.e. condom) within the last 3 months?", "inline": false, "values": [{"label": "Yes", "value": true}, {"label": "No", "value": false}, {"label": "Prefer not to disclose", "value": "prefer_not_to_disclose"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.show_all", "optionsLabelPosition": "right"}, {"key": "number_of_recent_sexual_partners", "type": "radio", "input": true, "label": "How many sexual partners have you had within the last 6 months?", "inline": false, "values": [{"label": "Prefer not to disclose", "value": "prefer_not_to_disclose"}, {"label": "1", "value": "1"}, {"label": "2-3", "value": "2-3"}, {"label": "4-9", "value": "4-9"}, {"label": "10+", "value": "10+"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "partner_sex", "type": "selectboxes", "input": true, "label": "Please select the sex(s) of your sexual partners:", "values": [{"label": "Male", "value": "male"}, {"label": "Female", "value": "female"}, {"label": "Trans", "value": "trans"}, {"label": "Prefer not to disclose", "value": "prefer_not_to_disclose"}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "confirm_label": "Partner sex(s):", "optionsLabelPosition": "right"}, {"key": "type_of_sex", "type": "selectboxes", "input": true, "label": "Please select your route of exposure (i.e. type of sex) including both protected and unprotected sex. This helps the physician know what medical tests are appropriate for your care.", "values": [{"label": "Vaginal Sex", "value": "vaginal"}, {"label": "Perform Anal Sex (ie your penis in partner's anus)", "value": "perform_anal", "customConditional": "show = data.sex != 'female';"}, {"label": "Receive Anal Sex (partner's penis in your anus)", "value": "receive_anal", "customConditional": "show = data.partner_sex && (data.partner_sex.male || data.partner_sex.trans || data.partner_sex.prefer_not_to_disclose);"}, {"label": "Perform Oral Sex (ie your mouth)", "value": "perform_oral"}, {"label": "Receive Oral Sex (ie partner's mouth)", "value": "receive_oral"}, {"label": "Use sex toys", "value": "sex_toys"}, {"label": "No sexual contact except for fingering/digital penetration", "value": "digital_penetration"}, {"label": "No sexual contact", "value": "no_sex"}, {"label": "Prefer not to disclose", "value": "prefer_not_to_disclose"}, {"label": "I don't understand this question", "value": "did_not_understand"}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "confirm_label": "Route of exposure:", "optionsLabelPosition": "right"}, {"key": "heading_exposure_history", "html": "<h4>Exposure History</h4>", "type": "content", "input": false, "tableView": false, "refreshOnChange": true, "customConditional": "show = data.show_all && !!data.type_of_sex;"}, {"key": "concerned_about_known_exposure", "type": "radio", "input": true, "label": "Have you recently had sexual contact with someone who was confirmed positive for an STI by a healthcare provider or lab test?", "inline": false, "values": [{"label": "Yes", "value": true}, {"label": "No", "value": false}, {"label": "Prefer not to disclose", "value": "prefer_not_to_disclose"}, {"label": "I don't understand this question", "value": "did_not_understand"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.show_all && !!data.type_of_sex;", "optionsLabelPosition": "right"}, {"key": "type_of_exposure", "type": "radio", "input": true, "label": "What type of exposure have you had?", "inline": false, "values": [{"label": "Received anonymous text message", "value": "anonymous_text"}, {"label": "Current partner tested positive", "value": "current_partner_positive"}, {"label": "Prior partner tested positive", "value": "prior_partner_positive"}, {"label": "Current partner suspects an STI but doesn't have test results", "value": "current_partner_suspects"}, {"label": "I haven't had an exposure that has been confirmed by a physician or tests", "value": "no_confirmed_exposure"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.concerned_about_known_exposure == true;", "optionsLabelPosition": "right"}, {"key": "list_of_confirmed_exposures", "type": "selectboxes", "input": true, "label": "Please report any confirmed exposures (i.e. partner notified you of a confirmed or suspected infection they had):", "values": [{"label": "Chlamydia", "value": "chlamydia"}, {"label": "Gonorrhea", "value": "gonorrhea"}, {"label": "Trichomonas", "value": "trichomonas"}, {"label": "HIV", "value": "hiv"}, {"label": "Syphilis", "value": "syphilis"}, {"label": "Herpes", "value": "herpes"}, {"label": "Other", "value": "other"}, {"label": "I wasn't notified what STI I was exposed to", "value": "wasnt_notified_of_which_sti"}, {"label": "Doesn't apply to me", "value": "N/A"}, {"label": "I don't understand this question", "value": "did_not_understand"}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "customConditional": "show = data.show_all && !!data.type_of_exposure && data.concerned_about_known_exposure != false;", "optionsLabelPosition": "right"}, {"key": "other_sti_exposure_reason", "type": "textarea", "input": true, "label": "Please list your concerns about “other” STI exposures here:", "tableView": true, "autoExpand": false, "customConditional": "show = data.list_of_confirmed_exposures && !!data.type_of_exposure && data.list_of_confirmed_exposures.other;"}, {"key": "header_hiv_risk_exposure", "html": "<h2>HIV Risk Exposure History</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "recent_hiv_exposure", "type": "radio", "input": true, "label": "Have you had any known or suspected exposure to HIV in the past 72 hours?", "values": [{"label": "Yes", "value": true}, {"label": "No", "value": false}], "tableView": true, "optionsLabelPosition": "right"}, {"key": "prep_interest_reasons", "type": "selectboxes", "input": true, "label": "What are your reasons for considering PrEP? (Select all that apply)", "values": [{"label": "Multiple sexual partners", "value": "multiple_partners", "shortcut": ""}, {"label": "Partner is HIV positive", "value": "partner_positive", "shortcut": ""}, {"label": "Inconsistent condom use", "value": "inconsistent_condom", "shortcut": ""}, {"label": "Recent high-risk exposure", "value": "recent_exposure", "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}], "tableView": true, "optionsLabelPosition": "right"}, {"key": "header_medication_history", "html": "<h2>Medication History</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "current_medications", "type": "selectboxes", "input": true, "label": "Are you currently taking any medications? (Select all that apply)", "values": [{"label": "Antiretrovirals", "value": "antiretrovirals", "shortcut": ""}, {"label": "Blood pressure medications", "value": "blood_pressure", "shortcut": ""}, {"label": "Diabetes medications", "value": "diabetes", "shortcut": ""}, {"label": "None", "value": "none", "shortcut": ""}], "tableView": true, "optionsLabelPosition": "right"}, {"key": "prep_med_allergies", "type": "selectboxes", "input": true, "label": "Do you have any known allergies to medications commonly used for PrEP (e.g., Tenofovir, Emtricitabine)?", "values": [{"label": "Tenofovir", "value": "tenofovir", "shortcut": ""}, {"label": "Emtricitabine", "value": "emtricitabine", "shortcut": ""}, {"label": "None", "value": "none", "shortcut": ""}], "inputType": "checkbox", "tableView": true, "optionsLabelPosition": "right"}, {"key": "heading_prep_lab_values", "html": "<h2>Pre-Screening Lab Values for PrEP</h2>", "type": "content", "input": false, "label": "Content", "tableView": false}, {"key": "prep_tests_completed", "type": "selectboxes", "input": true, "label": "Have you completed any of the following tests as part of your PrEP screening?", "values": [{"label": "Kidney function (eGFR)", "value": "egfr"}, {"label": "Liver function (ALT/AST)", "value": "liver"}, {"label": "Hepatitis B Panel", "value": "hepB"}, {"label": "I have not had these tests completed", "value": "no_tests"}], "inputType": "checkbox", "tableView": true, "optionsLabelPosition": "right"}, {"key": "heading_kidney_function", "html": "<h3>Kidney Function (eGFR)</h3><p>Please provide details about your kidney function test.</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.prep_tests_completed && data.prep_tests_completed.indexOf('egfr') !== -1;"}, {"key": "last_kidney_function_test", "data": {"values": [{"label": "< 3 months ago", "value": "<3months"}, {"label": "3-6 months ago", "value": "3-6_months"}, {"label": "6-12 months ago", "value": "6-12_months"}, {"label": "1-2 years ago", "value": "1-2_years"}, {"label": "2-3 years ago", "value": "2-3_years"}, {"label": "3-5 years ago", "value": "3-5_years"}, {"label": "More than 5 years ago", "value": "5+_years"}]}, "type": "select", "input": true, "label": "When was your last kidney function test completed?", "widget": "html5", "validate": {"required": true}, "tableView": true, "placeholder": "Select the time range", "confirm_label": "Last Kidney Function Test:", "customConditional": "show = data.prep_tests_completed && data.prep_tests_completed.indexOf('egfr') !== -1;"}, {"key": "prep_egfr_value", "data": {"values": [{"label": "I don't remember my result", "value": "dont_remember"}, {"label": "eGFR > 90", "value": "egfr_90+"}, {"label": "eGFR 85-89", "value": "egfr_85-89"}, {"label": "eGFR 80-84", "value": "egfr_80-84"}, {"label": "eGFR 75-79", "value": "egfr_75-79"}, {"label": "eGFR 70-74", "value": "egfr_70-74"}, {"label": "eGFR 65-69", "value": "egfr_65-69"}, {"label": "eGFR 60-64", "value": "egfr_60-64"}, {"label": "eGFR 55-59", "value": "egfr_55-59"}, {"label": "eGFR 50-54", "value": "egfr_50-54"}, {"label": "eGFR 45-49", "value": "egfr_45-49"}, {"label": "eGFR < 45", "value": "egfr_<45"}]}, "type": "select", "input": true, "label": "What was your most recent eGFR measurement?", "widget": "html5", "validate": {"required": true}, "tableView": true, "placeholder": "Select the value range", "confirm_label": "eGFR Value Range:", "customConditional": "show = data.prep_tests_completed && data.prep_tests_completed.indexOf('egfr') !== -1;"}, {"key": "heading_liver_function", "html": "<h3>Liver Function (ALT/AST)</h3><p>Please provide details about your liver function test.</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.prep_tests_completed && data.prep_tests_completed.indexOf('liver') !== -1;"}, {"key": "last_liver_function_test", "data": {"values": [{"label": "< 3 months ago", "value": "<3months"}, {"label": "3-6 months ago", "value": "3-6_months"}, {"label": "6-12 months ago", "value": "6-12_months"}, {"label": "1-2 years ago", "value": "1-2_years"}, {"label": "2-3 years ago", "value": "2-3_years"}, {"label": "3-5 years ago", "value": "3-5_years"}, {"label": "More than 5 years ago", "value": "5+_years"}]}, "type": "select", "input": true, "label": "When was your last liver function test completed?", "widget": "html5", "validate": {"required": true}, "tableView": true, "placeholder": "Select the time range", "confirm_label": "Last Liver Function Test:", "customConditional": "show = data.prep_tests_completed && data.prep_tests_completed.indexOf('liver') !== -1;"}, {"key": "prep_liver_value", "data": {"values": [{"label": "I don't remember my result", "value": "dont_remember"}, {"label": "Normal", "value": "normal"}, {"label": "Slightly elevated", "value": "slightly_elevated"}, {"label": "Moderately elevated", "value": "moderately_elevated"}, {"label": "Significantly elevated", "value": "significantly_elevated"}]}, "type": "select", "input": true, "label": "What were your most recent ALT/AST levels?", "widget": "html5", "validate": {"required": true}, "tableView": true, "placeholder": "Select the value range", "confirm_label": "ALT/AST Levels:", "customConditional": "show = data.prep_tests_completed && data.prep_tests_completed.indexOf('liver') !== -1;"}, {"key": "heading_hepB", "html": "<h3>Hepatitis B Panel</h3><p>Please provide details about your Hepatitis B screening.</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.prep_tests_completed && data.prep_tests_completed.indexOf('hepB') !== -1;"}, {"key": "last_hepB_test", "data": {"values": [{"label": "< 3 months ago", "value": "<3months"}, {"label": "3-6 months ago", "value": "3-6_months"}, {"label": "6-12 months ago", "value": "6-12_months"}, {"label": "1-2 years ago", "value": "1-2_years"}, {"label": "2-3 years ago", "value": "2-3_years"}, {"label": "3-5 years ago", "value": "3-5_years"}, {"label": "More than 5 years ago", "value": "5+_years"}]}, "type": "select", "input": true, "label": "When was your last Hepatitis B test completed?", "widget": "html5", "validate": {"required": true}, "tableView": true, "placeholder": "Select the time range", "confirm_label": "Last Hepatitis B Test:", "customConditional": "show = data.prep_tests_completed && data.prep_tests_completed.indexOf('hepB') !== -1;"}, {"key": "prep_hepB_value", "data": {"values": [{"label": "I don't remember my result", "value": "dont_remember"}, {"label": "Negative", "value": "negative"}, {"label": "Positive", "value": "positive"}, {"label": "Inconclusive", "value": "inconclusive"}]}, "type": "select", "input": true, "label": "What was your most recent Hepatitis B panel result?", "widget": "html5", "validate": {"required": true}, "tableView": true, "placeholder": "Select your result", "confirm_label": "Hepatitis B Result:", "customConditional": "show = data.prep_tests_completed && data.prep_tests_completed.indexOf('hepB') !== -1;"}, {"key": "header_additional_info", "html": "<h2>Additional Information</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "additional_questions_prep", "type": "textarea", "input": true, "label": "Please provide any additional information or questions regarding your PrEP intake history:", "tableView": true, "autoExpand": false}, {"key": "intake_kind", "type": "textfield", "input": true, "label": "Intake Kind", "hidden": true, "disabled": true, "tableView": true, "clearOnHide": false, "calculateValue": "value = !data.intake_denial_reasons?.length ? 'mail' : 'chat';"}, {"key": "intake_mt_key", "type": "textfield", "input": true, "label": "Intake Mail Template Key", "hidden": true, "disabled": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.intake_kind=='mail'?'intake-prep':'intake-denial'"}, {"key": "allowed_rt_keys", "type": "textfield", "input": true, "label": "Allowed Rep<PERSON>", "hidden": true, "disabled": true, "multiple": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.intake_mt_key=='intake-denial'?[]:['get-req','appointment-intake','edit-intake']"}]}