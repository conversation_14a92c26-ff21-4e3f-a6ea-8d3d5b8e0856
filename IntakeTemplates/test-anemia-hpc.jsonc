{"components": [{"key": "heading_anemia_testing", "html": "<h1><center><strong>Anemia Testing</strong></center></h1><p>To help us provide you with the best care, please answer the following questions about your health and medical history related to anemia.</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "header_anemia_history", "html": "<h2>Anemia History</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "previous_anemia_diagnosis", "type": "radio", "input": true, "label": "Have you been previously diagnosed with anemia?", "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}, {"label": "I don't know", "value": "dont_know", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "type_of_anemia", "type": "selectboxes", "input": true, "label": "If yes, what type of anemia were you diagnosed with? (Select all that apply)", "values": [{"label": "I do not know", "value": "doesn't_know"}, {"label": "Low iron levels", "value": "iron_deficiency"}, {"label": "Pregnancy", "value": "pregnancy_related_anemia"}, {"label": "Surgery", "value": "surgery_related_anemia"}, {"label": "Sickle cell anemia", "value": "sickle_cell"}, {"label": "Thalassemia (Alpha or Beta)", "value": "thalassemia"}, {"label": "Other", "value": "other"}], "tableView": true, "customConditional": "show = data.previous_anemia_diagnosis == true;", "optionsLabelPosition": "right"}, {"key": "other_anemia_type", "type": "textarea", "input": true, "label": "If you selected 'Other', please specify:", "tableView": true, "autoExpand": false, "customConditional": "show = data.previous_anemia_diagnosis == true && data.type_of_anemia.other;"}, {"key": "header_prior_lab_testing", "html": "<h1>Prior Lab Testing</h1>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "last_cbc_test", "type": "select", "input": true, "label": "When was your last <strong> Complete Blood Count (CBC)</strong>?", "tooltip": "A CBC includes a number of tests including your hemoglobin, hematocrit, and various white cell counts.", "data": {"values": [{"label": "<1 week ago", "value": "less_1_week"}, {"label": "2-4 weeks ago", "value": "2-4_weeks_ago"}, {"label": "4 weeks - 6 weeks ago", "value": "4-6_weeks"}, {"label": "6 weeks - 3 months ago", "value": "6_weeks-3_months"}, {"label": "3-6 months", "value": "3-6_months"}, {"label": "6-12 months", "value": "6-12_months"}, {"label": "12-24 months", "value": "12-24_months"}, {"label": "24+ months", "value": "24+_months"}, {"label": "Never had one", "value": "never_had"}]}, "widget": "html5", "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "last_hemoglobin_level", "type": "select", "widget": "html5", "input": true, "label": "What was your last <strong>Hemoglobin Count (HB)</strong>? (If known, please select the closest range in g/L)", "data": {"values": [{"label": "I don't know", "value": "doesnt_know"}, {"label": "70 - 80 g/L (7.0 - 8.0 g/dL)", "value": "70_80_g_l"}, {"label": "80 - 90 g/L (8.0 - 9.0 g/dL)", "value": "80_90_g_l"}, {"label": "90 - 100 g/L (9.0 - 10.0 g/dL)", "value": "90_100_g_l"}, {"label": "100 - 110 g/L (10.0 - 11.0 g/dL)", "value": "100_110_g_l"}, {"label": "110 - 120 g/L (11.0 - 12.0 g/dL)", "value": "110_120_g_l"}, {"label": "120 - 130 g/L (12.0 - 13.0 g/dL)", "value": "120_130_g_l"}, {"label": "130 - 140 g/L (13.0 - 14.0 g/dL)", "value": "130_140_g_l"}, {"label": "140 - 150 g/L (14.0 - 15.0 g/dL)", "value": "140_150_g_l"}, {"label": "150 - 170 g/L (15.0 - 17.0 g/dL)", "value": "150_170_g_l"}, {"label": "170 - 190 g/L (17.0 - 19.0 g/dL)", "value": "170_190_g_l"}, {"label": "190 - 210 g/L (19.0 - 21.0 g/dL)", "value": "190_210_g_l"}, {"label": "> 210 g/L (> 21.0 g/dL)", "value": "greater_210_g_l"}]}, "tableView": true}, {"key": "last_ferritin_test", "type": "select", "input": true, "label": "When was your last <strong>Fe<PERSON>tin</strong> level?", "data": {"values": [{"label": "Never had one", "value": "never_had"}, {"label": "<1 week ago", "value": "less_1_week"}, {"label": "2-4 weeks ago", "value": "2-4_weeks_ago"}, {"label": "4 weeks - 6 weeks ago", "value": "4-6_weeks"}, {"label": "6 weeks - 3 months ago", "value": "6_weeks-3_months"}, {"label": "3-6 months", "value": "3-6_months"}, {"label": "6-12 months", "value": "6-12_months"}, {"label": "12-24 months", "value": "12-24_months"}, {"label": "24+ months", "value": "24+_months"}]}, "widget": "html5", "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "recent_blood_transfusion", "type": "radio", "input": true, "label": "Have you had a blood transfusion in the past for anemia?", "tooltip": "A blood transfusion involves receiving blood through an intravenous (IV) line, typically done in a hospital or clinical setting. It's used to replace lost components of the blood, often due to surgery, injury, or illness.", "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "I don't understand the question", "value": "doesn't_understand", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "header_current_symptoms", "html": "<h1>Current Symptoms</h1>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "non_red_flag_symptoms", "type": "selectboxes", "input": true, "label": "Are you experiencing any of the following symptoms? (Select all that apply)", "values": [{"label": "Mild or occasional headaches", "value": "mild_headaches", "shortcut": ""}, {"label": "Occasional muscle or joint pain", "value": "occasional_muscle_joint_pain", "shortcut": ""}, {"label": "Mild fatigue", "value": "mild_fatigue", "shortcut": ""}, {"label": "None of the above", "value": "none_of_the_above", "shortcut": ""}], "tableView": true}, {"key": "red_flag_symptoms", "type": "selectboxes", "input": true, "label": "Are you experiencing any of the following red flag symptoms? (Select all that apply)", "values": [{"label": "I'm losing weight without trying", "value": "unexplained_weight_loss", "shortcut": ""}, {"label": "Chest pain or tightness", "value": "chest_pain", "shortcut": ""}, {"label": "Palpitations (i.e. feeling your heart beating fast or irregularly)", "value": "palpitations", "shortcut": ""}, {"label": "Abdominal or pelvic pain", "value": "abdominal_pelvic_pain", "shortcut": ""}, {"label": "Feel lightheaded or dizzy", "value": "presy<PERSON><PERSON>", "shortcut": ""}, {"label": "Feel unwell", "value": "malaise", "shortcut": ""}, {"label": "Extreme fatigue", "value": "extreme_fatigue", "shortcut": ""}, {"label": "Rectal bleeding (i.e. blood in your stool)", "value": "rectal_bleeding", "shortcut": ""}, {"label": "Shortness of breath", "value": "shortness_of_breath", "shortcut": ""}, {"label": "None of the above", "value": "none_of_the_above", "shortcut": ""}], "tableView": true}, {"key": "header_family_history", "html": "<h1>Family History</h1>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "family_history_anemia", "type": "radio", "input": true, "label": "Do you have a family history of anemia or blood disorders?", "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "family_history_celiac_disease", "type": "radio", "input": true, "label": "Do you have a family history of celiac disease?", "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "family_history_colon_cancer", "type": "radio", "input": true, "label": "Do you have a family history of colon cancer?", "tooltip": "A family history of colon cancer refers to having immediate family members (such as parents, siblings, or children) who have been diagnosed with colon cancer.", "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "age_of_relative_colon_cancer_diagnosis", "type": "select", "input": true, "label": "At what age was your <strong>youngest</strong> relative first diagnosed with colon cancer?", "tooltip": "Knowing the age at which your relative was first diagnosed with colon cancer can help assess your own risk and inform screening recommendations.", "widget": "html5", "data": {"values": [{"label": "20 - 24 years", "value": "20_24_years"}, {"label": "25 - 29 years", "value": "25_29_years"}, {"label": "30 - 34 years", "value": "30_34_years"}, {"label": "35 - 39 years", "value": "35_39_years"}, {"label": "40 - 44 years", "value": "40_44_years"}, {"label": "45 - 49 years", "value": "45_49_years"}, {"label": "50 - 54 years", "value": "50_54_years"}, {"label": "55 - 59 years", "value": "55_59_years"}, {"label": "60 - 64 years", "value": "60_64_years"}, {"label": "65 - 69 years", "value": "65_69_years"}, {"label": "70 years or older", "value": "70_plus_years"}]}, "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = data.family_history_colon_cancer === 'yes'"}, {"key": "header_menstrual_history", "html": "<h1>Menstrual History</h1>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "menstrual_cycle_regularity", "type": "selectboxes", "input": true, "label": "How would you describe the regularity of your menstrual cycles?", "tooltip": "Menstrual regularity refers to the consistency of the menstrual cycle length.", "values": [{"label": "Very Regular (varies by 1-3 days)", "value": "very_regular"}, {"label": "Somewhat Regular (varies by 4-7 days)", "value": "somewhat_regular"}, {"label": "Irregular (varies by more than 7 days)", "value": "irregular"}, {"label": "Very Irregular (cycle length is unpredictable)", "value": "very_irregular"}]}, {"key": "menstrual_flow_duration", "type": "select", "input": true, "label": "How many days does your typical menstrual flow last?", "tooltip": "This refers to the number of days you experience bleeding during your menstrual cycle.", "data": {"values": [{"label": "Very Short (1-2 days)", "value": "very_short"}, {"label": "Short (3-4 days)", "value": "short"}, {"label": "Average (5-7 days)", "value": "average"}, {"label": "Long (8-10 days)", "value": "long"}, {"label": "Very Long (more than 10 days)", "value": "very_long"}]}, "widget": "html5", "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "menstrual_pain", "type": "selectboxes", "input": true, "label": "Do you experience any of the following types of pain or cramping during your menstrual periods? (Select all that apply)", "values": [{"label": "No Pain", "value": "no_pain", "shortcut": ""}, {"label": "Mild Pain", "value": "mild_pain", "shortcut": ""}, {"label": "Moderate Pain", "value": "moderate_pain", "shortcut": ""}, {"label": "Severe <PERSON>", "value": "severe_pain", "shortcut": ""}], "tableView": true}, {"key": "pain_medication", "type": "radio", "input": true, "label": "Do you typically need to take pain medication for menstrual cramps?", "values": [{"label": "Never", "value": "never", "shortcut": ""}, {"label": "Sometimes", "value": "sometimes", "shortcut": ""}, {"label": "Often", "value": "often", "shortcut": ""}, {"label": "Always", "value": "always", "shortcut": ""}], "tableView": true}, {"key": "menstrual_symptoms", "type": "selectboxes", "widget": "html5", "input": true, "label": "Do you experience any of the following symptoms before or during your period? (Select all that apply)", "values": [{"label": "Bloating", "value": "bloating"}, {"label": "Fatigue", "value": "fatigue"}, {"label": "Breast Tenderness", "value": "breast_tenderness"}, {"label": "<PERSON><PERSON>s", "value": "mood_swings"}, {"label": "Headaches", "value": "headaches"}, {"label": "<PERSON><PERSON><PERSON>", "value": "nausea"}, {"label": "None of the Above", "value": "none"}]}, {"key": "menstrual_cycle_changes", "type": "selectboxes", "input": true, "label": "Have you noticed any recent changes in your menstrual cycles? (Select all that apply)", "values": [{"label": "No Changes", "value": "no_changes", "shortcut": ""}, {"label": "Increased Flow", "value": "increased_flow", "shortcut": ""}, {"label": "Decreased Flow", "value": "decreased_flow", "shortcut": ""}, {"label": "More Painful", "value": "more_painful", "shortcut": ""}, {"label": "Less Painful", "value": "less_painful", "shortcut": ""}, {"label": "More Irregular", "value": "more_irregular", "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}], "tableView": true}, {"key": "thyroid_test_inquiry", "type": "radio", "input": true, "label": "If you selected 'Increased Flow', have you had your thyroid tested?", "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "Not Sure", "value": "not_sure", "shortcut": ""}], "tableView": true, "customConditional": "show = data.menstrual_cycle_changes.increased_flow"}, {"key": "time_of_cycle_changes", "type": "select", "input": true, "label": "When did you first notice these changes in your menstrual cycle?", "tooltip": "Select the approximate time frame when you first noticed changes in your menstrual cycle.", "data": {"values": [{"label": "< 3 months ago", "value": "less_3_months"}, {"label": "3 - 6 months ago", "value": "3-6_months"}, {"label": "6 - 9 months ago", "value": "6-9_months"}, {"label": "9 - 12 months ago", "value": "9-12_months"}, {"label": "12 - 15 months ago", "value": "12-15_months"}, {"label": "15 - 18 months ago", "value": "15-18_months"}, {"label": "> 18 months ago", "value": "greater_18_months"}]}, "widget": "html5", "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = !data.menstrual_cycle_changes.no_changes"}, {"key": "investigation_of_menstrual_changes", "type": "selectboxes", "input": true, "label": "Have the changes in your menstrual cycles been investigated through any of the following methods? (Select all that apply)", "values": [{"label": "A pelvic ultrasound", "value": "pelvic_ultrasound", "shortcut": ""}, {"label": "An endometrial biopsy (sample of the lining of the uterus)", "value": "endometrial_biopsy", "shortcut": ""}, {"label": "A gynecology assessment", "value": "gynecology_assessment", "shortcut": ""}, {"label": "Bloodwork", "value": "bloodwork", "shortcut": ""}], "tableView": true, "customConditional": "show = !data.menstrual_cycle_changes.no_changes"}, {"key": "header_current_health_conditions", "html": "<h1>Current Health Conditions</h1>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "current_or_suspected_pregnancy", "type": "radio", "input": true, "label": "Are you currently pregnant or suspect that you might be?", "values": [{"label": "Yes, currently pregnant", "value": "currently_pregnant", "shortcut": ""}, {"label": "Possibly pregnant", "value": "possibly_pregnant", "shortcut": ""}, {"label": "No, not pregnant", "value": "not_pregnant", "shortcut": ""}], "tableView": true}, {"key": "history_of_menstrual_disorders", "type": "selectboxes", "input": true, "label": "Have you ever been diagnosed with any menstrual disorders (e.g., PCOS, endometriosis, amenorrhea)?", "values": [{"label": "PCOS", "value": "pcos"}, {"label": "Endometriosis", "value": "endometriosis"}, {"label": "Am<PERSON>rr<PERSON>", "value": "amenorrhea"}, {"label": "Other", "value": "other"}]}, {"key": "gynecological_procedures", "type": "selectboxes", "input": true, "label": "Have you undergone any gynecological surgeries or procedures? (Select all that apply)", "values": [{"label": "IUD Insertion", "value": "iud_insertion"}, {"label": "Hysterectomy", "value": "hysterectomy"}, {"label": "Endometrial Ablation", "value": "endometrial_ablation"}, {"label": "Fibroid Surgery", "value": "fibroid_surgery"}, {"label": "Endometrial Biopsy", "value": "endometrial_biopsy"}, {"label": "None of the Above", "value": "none_of_the_above"}]}, {"key": "other_gynecological_procedure", "type": "textarea", "input": true, "label": "If you selected 'Other', please specify the gynecological surgery or procedure:", "tableView": true, "autoExpand": false, "customConditional": "show = data.gynecological_procedures.other"}, {"key": "header_diet", "html": "<h1>Diet</h1>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "dietary_types_anemia_risk", "type": "selectboxes", "input": true, "label": "Please select any dietary types you follow that might be associated with an increased risk of anemia:", "values": [{"label": "Vegetarian (no meat consumption)", "value": "vegetarian", "shortcut": ""}, {"label": "Vegan (no animal products)", "value": "vegan", "shortcut": ""}, {"label": "Restricted diet (significant food group limitations, e.g., no red meat)", "value": "restricted_diet", "shortcut": ""}, {"label": "None of the above", "value": "none_of_the_above", "shortcut": ""}], "inputType": "checkbox", "tableView": true, "optionsLabelPosition": "right"}, {"key": "diet_iron_rich_foods", "type": "selectboxes", "input": true, "label": "Please select any iron-rich foods that are a regular part of your diet:", "values": [{"label": "Red meat (e.g., beef, lamb)", "value": "red_meat", "shortcut": ""}, {"label": "Poultry (e.g., chicken, turkey)", "value": "poultry", "shortcut": ""}, {"label": "Fish and Shellfish", "value": "fish_shellfish", "shortcut": ""}, {"label": "Leafy green vegetables (e.g., spinach, kale)", "value": "leafy_greens", "shortcut": ""}, {"label": "Legumes (e.g., beans, lentils, chickpeas)", "value": "legumes", "shortcut": ""}, {"label": "Iron-fortified foods (e.g., certain cereals and bread)", "value": "iron_fortified_foods", "shortcut": ""}, {"label": "Nuts and Seeds", "value": "nuts_seeds", "shortcut": ""}, {"label": "Tofu and Soy Products", "value": "tofu_soy", "shortcut": ""}], "tableView": true}, {"key": "header_medication_supplements", "html": "<h1>Medication and Supplements</h1>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "medication_use_screening", "type": "selectboxes", "input": true, "label": "Please select any over-the-counter medications or common pain relievers you are currently taking:", "values": [{"label": "Ibuprofen (e.g., Advil, Motrin)", "value": "ibuprofen", "shortcut": ""}, {"label": "Naproxen (e.g., Aleve)", "value": "naproxen", "shortcut": ""}, {"label": "<PERSON><PERSON><PERSON>", "value": "aspirin", "shortcut": ""}, {"label": "Acetaminophen (e.g., Tylenol)", "value": "acetaminophen", "shortcut": ""}, {"label": "Cold or Allergy Medication", "value": "cold_allergy_medication", "shortcut": ""}, {"label": "None of the Above", "value": "none_of_the_above", "shortcut": ""}], "inputType": "checkbox", "tableView": true, "optionsLabelPosition": "right"}, {"key": "supplement_use", "type": "selectboxes", "input": true, "label": "Do you regularly take any of the following supplements? (Select all that apply)", "values": [{"label": "Vitamin B12 Supplements", "value": "vitamin_b12_supplements", "shortcut": ""}, {"label": "Folic Acid Supplements", "value": "folic_acid_supplements", "shortcut": ""}, {"label": "Vitamin C Supplements", "value": "vitamin_c_supplements", "shortcut": ""}, {"label": "Multivitamins", "value": "multivitamins", "shortcut": ""}, {"label": "None of the Above", "value": "none_of_the_above", "shortcut": ""}], "tableView": true}, {"key": "iron_supplement_use", "type": "radio", "input": true, "label": "Do you regularly take iron supplements?", "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}], "tableView": true}, {"key": "type_of_iron_supplements", "type": "selectboxes", "input": true, "label": "Which iron supplements are you currently taking? (Select all that apply)", "values": [{"label": "Ferrous Sulfate (e.g., Feosol, Fer-In-Sol)", "value": "ferrous_sulfate", "shortcut": ""}, {"label": "Ferrous Gluconate (e.g., Fergon, Ferralet)", "value": "ferrous_gluconate", "shortcut": ""}, {"label": "Ferrous Fumarate (e.g., <PERSON><PERSON>on, Ferretts)", "value": "ferrous_fumarate", "shortcut": ""}, {"label": "Carbonyl Iron (e.g., Feosol Carbonyl)", "value": "carbonyl_iron", "shortcut": ""}, {"label": "Heme Iron Polypeptide (e.g., Proferrin)", "value": "heme_iron_polypeptide", "shortcut": ""}, {"label": "FeraMAX", "value": "feramax", "shortcut": ""}, {"label": "Other (please specify)", "value": "other", "shortcut": ""}, {"label": "I don't take iron supplements", "value": "doesnt_take", "shortcut": ""}], "tableView": true, "customConditional": "show = data.iron_supplement_use === 'yes'"}, {"key": "other_iron_supplement_details", "type": "textarea", "input": true, "label": "If you selected 'Other', please specify the iron supplement:", "tableView": true, "autoExpand": false, "customConditional": "show = data.type_of_iron_supplements.other"}, {"key": "additional_questions_anemia", "type": "textarea", "input": true, "label": "Please use this space to ask any additional questions or provide further information:", "tableView": true, "autoExpand": false}]}