{"components": [{"key": "heading_h_pylori_section", "html": "<h1><center><strong><PERSON><PERSON> Screening Questionnaire</strong></h1><center><p>To provide you with the best care, please complete the following questions. Your responses will help us determine the most appropriate care for you.</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "indication_for_consultation", "type": "selectboxes", "input": true, "label": "Please select your interest <PERSON><PERSON> testing or treatment:", "values": [{"label": "Interested in H. Pylori Testing", "value": "h_pylori_testing", "shortcut": ""}, {"label": "Interested in H. P<PERSON>ori <PERSON>", "value": "h_pylori_treatment", "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "confirm_label": "Your reason(s) for <PERSON><PERSON> testing:", "optionsLabelPosition": "right"}, {"key": "reason_for_h_pylori_testing_other_specify", "type": "textarea", "input": true, "label": "Please specify your reason here:", "tableView": true, "autoExpand": false, "customConditional": "show = data.indication_for_consultation.other;", "validate": {"required": true}}, {"key": "indication_for_testing", "type": "selectboxes", "input": true, "label": "Pleas select your reason for testing:", "values": [{"label": "Stomach discomfort or indigestion", "value": "stomach_discomfort", "shortcut": ""}, {"label": "Previous gastric/duodenal ulcer", "value": "ulcers_history", "shortcut": ""}, {"label": "Family member with gastric (stomach) cancer", "value": "gastric_cancer_history", "shortcut": ""}, {"label": "Born outside of Canada", "value": "immigrant_high_prevalence", "shortcut": ""}, {"label": "None of these apply to me", "value": "none_of_the_above", "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.indication_for_consultation.h_pylori_testing;", "confirm_label": "Your reason(s) for <PERSON><PERSON> testing:", "optionsLabelPosition": "right"}, {"key": "reason_for_h_pylori_testing_other_specify", "type": "textarea", "input": true, "label": "Please specify your reason for <PERSON><PERSON> testing:", "tableView": true, "autoExpand": false, "customConditional": "show = data.indication_for_testing && data.indication_for_testing.other;", "validate": {"required": true}}, {"key": "heading_test_history", "type": "content", "html": "<h2>Test History</h2>", "input": false, "label": "Test History", "tableView": false, "refreshOnChange": false}, {"key": "previous_h_pylori_testing", "type": "radio", "input": true, "label": "Have you been tested for <PERSON><PERSON> before?", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "confirm_label": "Previous <PERSON><PERSON> testing:", "optionsLabelPosition": "right"}, {"key": "date_of_last_h_pylori_test", "type": "select", "input": true, "label": "When was your last <PERSON><PERSON> test?", "tooltip": "Select the time frame that best describes when you were last tested for <PERSON><PERSON>.", "data": {"values": [{"label": "<1 week ago", "value": "less_1_week"}, {"label": "2-4 weeks ago", "value": "2-4_weeks_ago"}, {"label": "4 weeks - 6 weeks ago", "value": "4-6_weeks"}, {"label": "6 weeks - 3 months ago", "value": "6_weeks-3_months"}, {"label": "3-6 months", "value": "3-6_months"}, {"label": "6-12 months", "value": "6-12_months"}, {"label": "12-24 months", "value": "12-24_months"}, {"label": "24+ months", "value": "24+_months"}, {"label": "Never had one", "value": "never_had"}]}, "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.previous_h_pylori_testing == 'yes';", "optionsLabelPosition": "right"}, {"key": "previous_h_pylori_test_result", "type": "radio", "input": true, "label": "What was the result of your last <PERSON><PERSON> test?", "inline": false, "values": [{"label": "Positive", "value": "positive", "shortcut": ""}, {"label": "Negative", "value": "negative", "shortcut": ""}, {"label": "I don't know", "value": "dont_know", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.previous_h_pylori_testing == 'yes';", "optionsLabelPosition": "right"}, {"key": "previous_h_pylori_test_method", "type": "radio", "input": true, "label": "How was your last <PERSON><PERSON> test performed?", "inline": false, "values": [{"label": "Blood test", "value": "blood_test", "shortcut": ""}, {"label": "Breath test", "value": "breath_test", "shortcut": ""}, {"label": "Stool test", "value": "stool_test", "shortcut": ""}, {"label": "Endoscopy", "value": "endoscopy", "shortcut": ""}, {"label": "I don't know", "value": "dont_know", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.previous_h_pylori_testing == 'yes';", "optionsLabelPosition": "right"}, {"key": "treatment_for_h_pylori", "type": "radio", "input": true, "label": "Have you been treated for <PERSON><PERSON> in the past?", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "confirm_label": "Treatment for <PERSON><PERSON>:", "optionsLabelPosition": "right"}, {"key": "heading_treatment_history", "type": "content", "html": "<h2>Treatment History</h2>", "input": false, "label": "Treatment History", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.treatment_for_h_pylori == 'yes';"}, {"key": "number_of_treatments", "type": "radio", "input": true, "label": "How many times have you been treated for <PERSON><PERSON>?", "tooltip": "Select the number of times you have received treatment for <PERSON><PERSON>.", "values": [{"label": "Once", "value": "once", "shortcut": ""}, {"label": "Twice", "value": "twice", "shortcut": ""}, {"label": "Three times", "value": "three_times", "shortcut": ""}, {"label": "Four times", "value": "four_times", "shortcut": ""}, {"label": "Five or more times", "value": "five_or_more_times", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.treatment_for_h_pylori == 'yes';", "optionsLabelPosition": "right"}, {"key": "treatment_years_free_text", "type": "textfield", "input": true, "label": "Please specify all years you were treated for <PERSON><PERSON>, separated by commas (e.g., 2010, 2014, 2018):", "placeholder": "e.g., 2010, 2014, 2018", "tooltip": "Enter each year you received treatment for <PERSON><PERSON>. You can separate the years by commas or simply list them.", "validate": {"required": true, "customMessage": "Please provide the years of your treatment."}, "tableView": true, "customConditional": "show = data.treatment_for_h_pylori == 'yes';"}, {"key": "treatment_outcome", "type": "radio", "input": true, "label": "If treated, was the treatment successful in curing <PERSON><PERSON>?", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "I didn't have a follow-up test", "value": "no_follow_up_testing_completed", "shortcut": ""}, {"label": "I don't know", "value": "dont_know", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.treatment_for_h_pylori == 'yes';", "confirm_label": "Outcome of <PERSON><PERSON> treatment:", "optionsLabelPosition": "right"}, {"key": "h_pylori_treatment_duration", "type": "radio", "input": true, "label": "Was your previous <PERSON><PERSON> treatment a 7-day or 14-day regimen?", "values": [{"label": "7-day treatment", "value": "7_day", "shortcut": ""}, {"label": "14-day treatment", "value": "14_day", "shortcut": ""}, {"label": "I don't know", "value": "dont_know", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.treatment_for_h_pylori == 'yes';", "optionsLabelPosition": "right"}, {"key": "h_pylori_treatment_regimen", "type": "selectboxes", "input": true, "label": "Which <PERSON><PERSON> treatment were you prescribed?", "tooltip": "Select the specific treatment you were given for <PERSON><PERSON>. Check all that apply.", "values": [{"label": "a) Amoxicillin, b) Metronidazole, c) Clarithromycin", "value": "amox_met_clari"}, {"label": "a) Bismuth subsalicylate, b) Metronidazole, c) Tetracycline", "value": "bismuth_met_tetra"}, {"label": "a) Amoxicillin, b) Levofloxacin", "value": "amox_levo"}, {"label": "a) Amoxicillin, b) Rifabutin", "value": "amox_rifabutin"}, {"label": "Other", "value": "other"}, {"label": "I don't know", "value": "dont_know"}, {"label": "None of the above", "value": "none"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.treatment_for_h_pylori == 'yes';", "optionsLabelPosition": "right"}, {"key": "side_effects_experienced", "type": "selectboxes", "input": true, "label": "Select any side effects you experienced from the treatment:", "values": [{"label": "<PERSON><PERSON><PERSON>", "value": "nausea"}, {"label": "Diarrhea", "value": "diarrhea"}, {"label": "Headache", "value": "headache"}, {"label": "Dizziness", "value": "dizziness"}, {"label": "Rash or allergic reactions", "value": "rash_allergic_reactions"}, {"label": "Taste changes", "value": "taste_changes"}, {"label": "None", "value": "none"}, {"label": "Other", "value": "other"}], "tableView": true, "autoExpand": false, "customConditional": "show = data.treatment_for_h_pylori == 'yes';"}, {"key": "side_effects_experienced", "type": "textarea", "input": true, "label": "If you experienced any side effects from the treatment, please describe them:", "tableView": true, "autoExpand": false, "customConditional": "show = data.side_effects_experienced.other;"}, {"key": "heading_colonoscopy", "html": "<h5>Colonoscopy&nbsp;</h5>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "prior_colonoscopy", "type": "radio", "input": true, "label": "Have you had a colonoscopy or sigmoidoscopy before?", "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No/Don't Know", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "confirm_label": "Previous colonoscopy/sigmoidoscopy:", "optionsLabelPosition": "right"}, {"key": "date_of_last_colonoscopy", "data": {"values": [{"label": "<6 weeks", "value": "less_6_weeks"}, {"label": "6 weeks - 3 months", "value": "6weeks_3months"}, {"label": "3-6 months", "value": "3-6_months"}, {"label": "6-12 months", "value": "6_12_months"}, {"label": "1-2 years", "value": "1-2_years"}, {"label": "2-3 years", "value": "2-3_years"}, {"label": "3-5 years", "value": "3-5_years"}, {"label": "5-10 years", "value": "5-10_years"}, {"label": "10+ years", "value": "10+_years"}, {"label": "I don't know", "value": "doesn't_know"}]}, "type": "select", "input": true, "label": "When was your colonoscopy completed", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.prior_colonoscopy == true;", "optionsLabelPosition": "right"}, {"key": "colonoscopy_findings", "type": "selectboxes", "input": true, "label": "Do you recall if your doctor identified any of the following:", "values": [{"label": "Polyps", "value": "polyps", "shortcut": ""}, {"label": "Diverticulosis / Diverticulitis", "value": "diverticulosis/diverticulitis", "shortcut": ""}, {"label": "Hemorrhoids (interal/external)", "value": "hemorrhoids", "shortcut": ""}, {"label": "Anal fissure", "value": "anal_fissure", "shortcut": ""}, {"label": "Inflammation (i.e. colitis)", "value": "inflammation", "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "confirm_label": "Previous colonoscopy/sigmoidoscopy findings:", "customConditional": "show = data.prior_colonoscopy == true;", "optionsLabelPosition": "right"}, {"key": "colonoscopy_findings_other", "type": "textarea", "input": true, "label": "As you selected other, please specify any findings not included above:", "validate": {"required": true}, "tableView": true, "autoExpand": false, "customConditional": "show = data.prior_colonoscopy == true && data.colonoscopy_findings.other;"}, {"key": "heading_endoscopy", "html": "<h5>Endoscopy&nbsp;</h5>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "prior_endoscopy", "type": "radio", "input": true, "label": "Have you had an endoscopy / gastroscopy (camera examines throat to stomach)?", "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No/Don't Know", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "confirm_label": "Previous colonoscopy/sigmoidoscopy:", "optionsLabelPosition": "right"}, {"key": "date_of_last_endoscopy", "data": {"values": [{"label": "<6 weeks", "value": "less_6_weeks"}, {"label": "6 weeks - 3 months", "value": "6weeks_3months"}, {"label": "3-6 months", "value": "3-6_months"}, {"label": "6-12 months", "value": "6_12_months"}, {"label": "1-2 years", "value": "1-2_years"}, {"label": "2-3 years", "value": "2-3_years"}, {"label": "3-5 years", "value": "3-5_years"}, {"label": "5-10 years", "value": "5-10_years"}, {"label": "10+ years", "value": "10+_years"}, {"label": "I don't know", "value": "doesn't_know"}]}, "type": "select", "input": true, "label": "When was your endoscopy completed?", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.prior_endoscopy == true;", "optionsLabelPosition": "right"}, {"key": "endoscopy_findings", "type": "selectboxes", "input": true, "label": "Do you recall if your doctor identified any of the following:", "values": [{"label": "<PERSON><PERSON>", "value": "h_pylori", "shortcut": ""}, {"label": "Gastric or duodenal ulcer (i.e. an ulcer)", "value": "ulcer", "shortcut": ""}, {"label": "<PERSON>'s esophagus", "value": "barret's_esophagus", "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "confirm_label": "Previous endoscopy findings:", "customConditional": "show = data.prior_endoscopy == true;", "optionsLabelPosition": "right"}, {"key": "endoscopy_findings_other", "type": "textarea", "input": true, "label": "As you selected other, please specify any findings not included above:", "validate": {"required": true}, "tableView": true, "autoExpand": false, "customConditional": "show = data.prior_endoscopy == true && data.endoscopy_findings.other;"}, {"key": "heading_family_history_colon_cancer", "html": "<h4>Family History</h4>", "type": "content", "input": false, "label": "Family History", "tableView": false, "refreshOnChange": false}, {"key": "family_history_colon_cancer", "type": "radio", "input": true, "label": "Do you have a family history of colon cancer?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "relative_diagnosis_colon_cancer", "type": "selectboxes", "input": true, "label": "Who was diagnosed with colon cancer in your family?", "inline": false, "values": [{"label": "Mother, Father, Brother or Sister", "value": "1st degree relative"}, {"label": "Grandparent, Uncle, Aunt, <PERSON><PERSON><PERSON><PERSON> or <PERSON><PERSON><PERSON>", "value": "2nd degree relative"}, {"label": "Cousin", "value": "3rd degree relative"}, {"label": "I don't know", "value": "dont_know"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Family member with colon cancer:", "customConditional": "show = data.family_history_colon_cancer === 'yes';", "optionsLabelPosition": "right"}, {"key": "age_youngest_member_colon_cancer_dx", "type": "select", "input": true, "label": "How old was your youngest relative at the time they were diagnosed with colon cancer?", "data": {"values": [{"label": "20-30 years", "value": "20-30_years"}, {"label": "30-40 years", "value": "30-40_years"}, {"label": "40-50 years", "value": "40-50_years"}, {"label": "50-60 years", "value": "50-60_years"}, {"label": "60-70 years", "value": "60-70_years"}, {"label": "70+ years", "value": "70_years"}, {"label": "I don't know", "value": "dont_know"}]}, "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.family_history_colon_cancer === 'yes';", "optionsLabelPosition": "right"}, {"key": "family_history_h_pylori", "type": "radio", "input": true, "label": "Does anyone in your family have <PERSON><PERSON>?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "relative_h_pylori_diagnosis", "type": "selectboxes", "input": true, "label": "Who has <PERSON><PERSON> in your family?", "inline": false, "values": [{"label": "Mother, Father, Brother or Sister", "value": "1st degree relative"}, {"label": "Grandparent, Uncle, Aunt, <PERSON><PERSON><PERSON><PERSON> or <PERSON><PERSON><PERSON>", "value": "2nd degree relative"}, {"label": "Cousin", "value": "3rd degree relative"}, {"label": "I don't know", "value": "dont_know"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Family member with <PERSON><PERSON>:", "customConditional": "show = data.family_history_h_pylori === 'yes';", "optionsLabelPosition": "right"}, {"key": "heading_reflux_indigestion", "html": "<h4>Current Symptoms</h4>", "type": "content", "input": false, "label": "Reflux and Indigestion", "tableView": false, "refreshOnChange": false}, {"key": "dyspepsia_symptoms", "label": "Please select any symptoms of indigestion you currently experience:", "type": "selectboxes", "input": true, "values": [{"label": "Bloating", "value": "bloating"}, {"label": "Nausea after eating", "value": "nausea"}, {"label": "Belching or burping", "value": "belching"}, {"label": "Acid reflux or heartburn", "value": "acid_reflux"}, {"label": "Stomach pain or discomfort", "value": "stomach_pain"}, {"label": "Feeling full too quickly during a meal", "value": "early_fullness"}, {"label": "Other", "value": "other"}, {"label": "I don't have any symptoms", "value": "no_symptoms"}], "validate": {"required": true}}, {"key": "symptoms_onset_description", "type": "select", "input": true, "label": "When did you first notice these symptoms?", "customConditional": "show = !data.dyspepsia_symptoms.no_symptoms;", "data": {"values": [{"label": "<1 week ago", "value": "less_1_week"}, {"label": "2-4 weeks ago", "value": "2-4_weeks_ago"}, {"label": "4 weeks - 6 weeks ago", "value": "4-6_weeks"}, {"label": "6 weeks - 3 months ago", "value": "6_weeks-3_months"}, {"label": "3-6 months", "value": "3-6_months"}, {"label": "6-12 months", "value": "6-12_months"}, {"label": "12-24 months", "value": "12-24_months"}, {"label": "24+ months", "value": "24+_months"}, {"label": "Doesn't apply to me", "value": "not_applicable"}]}, "widget": "html5", "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "discomfort_location", "type": "selectboxes", "input": true, "label": "Where do you feel discomfort or pain the most?", "values": [{"label": "Behind the chest bone", "value": "behind_breastbone"}, {"label": "In the upper abdomen", "value": "upper_abdomen"}, {"label": "General discomfort in the stomach area", "value": "stomach_area_discomfort"}, {"label": "I don't know", "value": "dont_know"}, {"label": "Other", "value": "other"}], "validate": {"required": true}, "customConditional": "show = data.dyspepsia_symptoms.stomach_pain;", "tableView": true}, {"key": "post_prandial_symptoms", "type": "selectboxes", "input": true, "label": "Do you experience any of the following after eating?", "customConditional": "show = !data.dyspepsia_symptoms.no_symptoms;", "values": [{"label": "A burning sensation in the chest or throat", "value": "burning_sensation"}, {"label": "Nausea or vomiting", "value": "nausea_vomiting"}, {"label": "Bloating or belching", "value": "bloating_belching"}], "validate": {"required": true}, "tableView": true}, {"key": "symptom_triggers", "type": "selectboxes", "customConditional": "show = !data.dyspepsia_symptoms.no_symptoms;", "input": true, "label": "Which of the following triggers or worsens your symptoms?", "values": [{"label": "Consuming spicy, acidic, or fatty foods", "value": "trigger_foods"}, {"label": "Stress or anxiety", "value": "trigger_stress"}, {"label": "Eating large meals", "value": "trigger_size"}, {"label": "Eating specific foods", "value": "specific foods"}, {"label": "None of the above", "value": "trigger_none"}], "tableView": true}, {"key": "experience_regurgitation", "type": "selectboxes", "customConditional": "show = !data.dyspepsia_symptoms.no_symptoms;", "input": true, "label": "Do you experience regurgitation (food coming back into your mouth)?", "values": [{"label": "Frequently", "value": "regurgitation_frequently"}, {"label": "Rarely", "value": "regurgitation_rarely"}, {"label": "Occasionally, especially with overeating", "value": "regurgitation_occasionally"}, {"label": "I don't experience regurgitation", "value": "regurgitation_none"}], "tableView": true}, {"key": "symptom_frequency", "type": "selectboxes", "customConditional": "show = !data.dyspepsia_symptoms.no_symptoms;", "input": true, "label": "How often do you experience symptoms?", "values": [{"label": "Daily", "value": "symptoms_daily"}, {"label": "A few times a week", "value": "symptoms_few_times_week"}, {"label": "A few times a month", "value": "symptoms_few_times_month"}, {"label": "Rarely", "value": "symptoms_rarely"}, {"label": "I don't know", "value": "symptoms_dont_know"}], "tableView": true}, {"key": "dietary_changes_response", "type": "selectboxes", "input": true, "customConditional": "show = !data.dyspepsia_symptoms.no_symptoms;", "label": "How do your symptoms respond to dietary changes (i.e. smaller, more frequent meals or avoiding specific foods)?", "values": [{"label": "Significant improvement", "value": "diet_improvement_significant"}, {"label": "Some improvement, but not always", "value": "diet_improvement_some"}, {"label": "Little to no improvement", "value": "diet_improvement_none"}, {"label": "I haven't noticed any pattern related to dietary changes", "value": "diet_improvement_no_pattern"}, {"label": "I haven't tried any dietary changes", "value": "diet_improvement_not_tried"}], "tableView": true}, {"key": "lifestyle_modifications_impact", "type": "selectboxes", "input": true, "customConditional": "show = !data.dyspepsia_symptoms.no_symptoms;", "label": "Have lifestyle modifications (such as weight loss, quitting smoking, or elevating the head of your bed) impacted your symptoms?", "values": [{"label": "Yes, there's a noticeable improvement", "value": "lifestyle_improvement_yes"}, {"label": "Yes, but only minimally", "value": "lifestyle_improvement_minimal"}, {"label": "No, I've seen no change", "value": "lifestyle_improvement_no"}, {"label": "I haven't tried any lifestyle modifications", "value": "lifestyle_improvement_not_tried"}], "tableView": true}, {"key": "symptoms_prediction", "type": "selectboxes", "customConditional": "show = data.current_symptoms === 'yes';", "input": true, "label": "Regarding your symptoms, which statement feels most accurate?", "values": [{"label": "Mostly predictable and related to specific triggers", "value": "symptoms_predictable"}, {"label": "Related to medication use or stress rather than dietary habits", "value": "symptoms_medication_stress"}, {"label": "Unpredictable, and I can't identify specific triggers", "value": "symptoms_unpredictable"}, {"label": "Present constantly, regardless of my diet or stress levels.", "value": "symptoms_constant"}, {"label": "I don't know", "value": "symptoms_dont_know"}], "tableView": true}, {"key": "gi_red_flags", "type": "selectboxes", "customConditional": "show = !data.dyspepsia_symptoms.no_symptoms;", "input": true, "label": "Have you experienced any of the following symptoms?", "values": [{"label": "I've lost weight without trying", "value": "weight_loss", "shortcut": ""}, {"label": "Chest pain or tightness", "value": "chest_paint_tightness", "shortcut": ""}, {"label": "Shortness of breath or difficulty breathing", "value": "shortness_of_breath", "shortcut": ""}, {"label": "Sensation of food being stuck", "value": "food_stuck", "shortcut": ""}, {"label": "Sharp, stabbing pain when eating", "value": "pain", "shortcut": ""}, {"label": "Vomiting blood or coffee ground-like material", "value": "vomiting_blood", "shortcut": ""}, {"label": "Rectal bleeding (i.e. bright red blood or tarry black stools)", "value": "rectal_bleeding", "shortcut": ""}, {"label": "None of the above", "value": "none_of_the_above", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "confirm_label": "<PERSON><PERSON> alarm features:", "optionsLabelPosition": "right"}, {"key": "additional_questions_h_pylori", "type": "textarea", "input": true, "label": "Please list any additional questions or concerns you have:", "tableView": true, "autoExpand": false}]}