{"components": [{"key": "content", "html": "<h2>Instructions</h2><p>Please complete the following questionnaire about your interest in obtaining a Continuous Glucose Monitor (CGM).</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "cgm_indication", "type": "radio", "input": true, "label": "What is your primary reason for wanting a CGM?", "confirmLabel": "Primary reason for wanting a CGM:", "inline": false, "values": [{"label": "I want to use a CGM to make lifestyle changes to improve my health", "value": "lifestyle_improvements", "shortcut": ""}, {"label": "I am a diabetic on insulin", "value": "diabetic_on_insulin", "shortcut": ""}, {"label": "I have diabetes during pregnancy", "value": "diabetes_in_pregnancy", "shortcut": ""}, {"label": "I am a diabetic tracking blood sugar levels because I've had symptoms of low blood sugar", "value": "tracking_low_blood_sugar", "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "cgm_indication_other", "type": "textarea", "input": true, "label": "You selected 'other' - please specify your reason for using a CGM:", "tableView": true, "confirmLabel": "Other reason for wanting a CGM:", "autoExpand": false, "customConditional": "show = data.cgm_indication == 'other';"}, {"key": "prior_cgm_use", "type": "radio", "confirmLabel": "Prior CGM use:", "input": true, "label": "Have you ever used a CGM before?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "previous_monitor_type", "type": "radio", "input": true, "confirmLabel": "Previous CGM:", "label": "Which CGM did you use previously?", "inline": false, "values": [{"label": "Dexcom", "value": "dexcom", "shortcut": ""}, {"label": "FreeStyle Libre", "value": "freestyle_libre", "shortcut": ""}, {"label": "Other", "value": "other_monitor", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.prior_cgm_use == true;", "optionsLabelPosition": "right"}, {"key": "monitor_details", "type": "textarea", "input": true, "label": "If you selected 'Other,' please specify the CGM you used previously:", "tableView": true, "autoExpand": false, "customConditional": "show = data.previous_monitor_type == 'other_monitor';"}, {"key": "recent_symptoms_header", "html": "<h3>Symptoms</h3>", "type": "content", "input": false, "tableView": false}, {"key": "cgm_contraindications_symptoms", "type": "selectboxes", "input": true, "label": "Have you experienced any of the following symptoms:", "values": [{"label": "Severe or frequent headaches", "value": "headaches", "shortcut": ""}, {"label": "Palpitations or chest pain", "value": "palpitations", "shortcut": ""}, {"label": "Shortness of breath", "value": "dyspnea", "shortcut": ""}, {"label": "Dizziness or fainting spells", "value": "dizziness", "shortcut": ""}, {"label": "Changes in vision (e.g., blurry vision or loss of vision)", "value": "vision_changes", "shortcut": ""}, {"label": "Chest tightness, especially if occurring during or after physical activity (i.e. exercise)", "value": "chest_pain_with_activity", "shortcut": ""}], "adminflag": true, "inputType": "checkbox", "tableView": true, "optionsLabelPosition": "right"}, {"key": "no_cgm_contraindications_symptoms", "type": "checkbox", "confirmLabel": "No headaches, palpitations, shortness of breath, dizziness, vision changes or chest tightness", "input": true, "label": "None of the above", "validate": {"custom": "valid = _.some(_.values(data.cgm_contraindications_symptoms)) || data.no_cgm_contraindications_symptoms;"}, "customClass": "mt-n3", "defaultValue": false}, {"key": "cgm_contraindications_symptoms_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following CGM contraindication symptoms:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.cgm_contraindications_symptoms, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "experience_hypoglycemia", "type": "radio", "input": true, "confirmLabel": "Experience hypoglycemic episodes (shakiness, sweating, confusion, dizziness):", "label": "Do you experience symptoms of low blood sugar (Shakiness, Sweating, Confusion, Dizziness, Blurred vision, Hunger, Fatigue)?", "values": [{"label": "Yes", "value": true}, {"label": "No", "value": false}], "validate": {"required": true}, "tableView": true}, {"key": "first_noticed_symptoms", "data": {"values": [{"label": "< 1 week", "value": "<1_week"}, {"label": "1-4 weeks", "value": "1-4_weeks"}, {"label": "4 weeks - 3 months", "value": "4_weeks-3_months"}, {"label": "3-6 months", "value": "3-6_months"}, {"label": "6-12 months", "value": "6-12_months"}, {"label": "1 year to 2 years", "value": "1-2_years"}, {"label": "2-3 years", "value": "2-3_years"}, {"label": "3-5 years", "value": "3-5_years"}, {"label": "5-10 years", "value": "5-10_years"}, {"label": "10+ years", "value": "10+_years"}]}, "type": "select", "input": true, "label": "When did you first notice these symptoms?", "widget": "html5", "validate": {"required": true}, "tableView": true, "placeholder": "Select a time range", "customConditional": "show = data.experience_hypoglycemia === true;"}, {"key": "number_of_episodes", "data": {"values": [{"label": "1 episode", "value": "1"}, {"label": "2-3 episodes", "value": "2-3"}, {"label": "4-5 episodes", "value": "4-5"}, {"label": "6-7 episodes", "value": "6-7"}, {"label": "8-9 episodes", "value": "8-9"}, {"label": "10-11 episodes", "value": "10-11"}, {"label": "12-13 episodes", "value": "12-13"}, {"label": "14-15 episodes", "value": "14-15"}, {"label": "16-17 episodes", "value": "16-17"}, {"label": "18-19 episodes", "value": "18-19"}, {"label": "20+ episodes", "value": "20+"}]}, "type": "select", "input": true, "label": "How many hypoglycemia episodes have you experienced?", "placeholder": "Select the number of episodes", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.experience_hypoglycemia === true;"}, {"key": "investigated_in_past", "type": "selectboxes", "input": true, "label": "Have you had this investigated in the past with any of the following?", "values": [{"label": "Lab testing", "value": "lab_testing", "shortcut": ""}, {"label": "Physical exam by a doctor", "value": "physical_exam", "shortcut": ""}, {"label": "Blood sugar testing during an episode", "value": "blood_sugar_testing", "shortcut": ""}, {"label": "None of the above", "value": "none_of_the_above", "shortcut": ""}], "inputType": "checkbox", "validate": {"custom": "valid = _.some(_.values(data.investigated_in_past));", "customMessage": "Please select at least one option."}, "tableView": true, "customConditional": "show = data.consume_meal_snack === 'yes';", "optionsLabelPosition": "right"}, {"key": "frequency_of_hypoglycemia", "type": "radio", "input": true, "label": "How often do you experience these symptoms?", "values": [{"label": "Daily", "value": "daily"}, {"label": "Weekly", "value": "weekly"}, {"label": "Monthly", "value": "monthly"}, {"label": "Rarely", "value": "rarely"}, {"label": "Never", "value": "never"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = !!data.first_noticed_symptoms;"}, {"key": "number_of_episodes", "data": {"values": [{"label": "1 episode", "value": "1"}, {"label": "2-3 episodes", "value": "2-3"}, {"label": "4-5 episodes", "value": "4-5"}, {"label": "6-7 episodes", "value": "6-7"}, {"label": "8-9 episodes", "value": "8-9"}, {"label": "10-11 episodes", "value": "10-11"}, {"label": "12-13 episodes", "value": "12-13"}, {"label": "14-15 episodes", "value": "14-15"}, {"label": "16-17 episodes", "value": "16-17"}, {"label": "18-19 episodes", "value": "18-19"}, {"label": "20+ episodes", "value": "20+"}]}, "type": "select", "input": true, "label": "How many hypoglycemia episodes have you experienced?", "placeholder": "Select the number of episodes", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.experience_hypoglycemia === true;"}, {"key": "episode_duration", "type": "select", "input": true, "label": "How long does each hypoglycemia episode typically last?", "placeholder": "Select the duration", "widget": "html5", "data": {"values": [{"label": "< 5 minutes", "value": "<5_minutes"}, {"label": "5-10 minutes", "value": "5-10_minutes"}, {"label": "11-20 minutes", "value": "11-20_minutes"}, {"label": "21-30 minutes", "value": "21-30_minutes"}, {"label": "31-60 minutes", "value": "31-60_minutes"}, {"label": "1-2 hours", "value": "1-2_hours"}, {"label": "2-3 hours", "value": "2-3_hours"}, {"label": "> 3 hours", "value": ">3_hours"}, {"label": "Varies greatly between episodes", "value": "varies_greatly"}, {"label": "Some episodes last minutes, others hours", "value": "mixed_duration"}, {"label": "I'm not sure", "value": "not_sure"}]}, "validate": {"required": true}, "tableView": true, "customConditional": "show = data.number_of_episodes !== undefined;"}, {"key": "hypoglycemia_related_symptoms", "type": "selectboxes", "input": true, "label": "Which symptoms do you typically experience during hypoglycemia episodes? (Select all that apply)", "values": [{"label": "Shakiness", "value": "shakiness"}, {"label": "Sweating", "value": "sweating"}, {"label": "Confusion", "value": "confusion"}, {"label": "Dizziness", "value": "dizziness"}, {"label": "Blurred vision", "value": "blurred_vision"}, {"label": "Other", "value": "other_symptom"}], "inputType": "checkbox", "tableView": true, "customConditional": "show = data.experience_hypoglycemia === true;", "optionsLabelPosition": "right"}, {"key": "other_hypoglycemia_symptoms", "type": "textarea", "input": true, "label": "If you selected 'Other,' please describe your symptoms:", "tableView": true, "autoExpand": false, "customConditional": "show = data.experience_hypoglycemia === true && data.hypoglycemia_related_symptoms && data.hypoglycemia_related_symptoms.other_symptom;"}, {"key": "treatment_for_hypoglycemia", "type": "selectboxes", "input": true, "label": "How do you typically treat your hypoglycemia episodes? (Select all that apply)", "values": [{"label": "Eat a sugary snack (e.g., candy, glucose tablets)", "value": "sugary_snack"}, {"label": "Drink a sugary beverage (e.g., juice, soda)", "value": "sugary_beverage"}, {"label": "Eat a full meal", "value": "full_meal"}, {"label": "Take prescribed glucose gel or medication", "value": "glucose_gel"}, {"label": "Rest until symptoms pass", "value": "rest"}, {"label": "Seek medical attention", "value": "medical_attention"}, {"label": "Other", "value": "other_treatment"}], "inputType": "checkbox", "tableView": true, "customConditional": "show = data.experience_hypoglycemia === true;", "optionsLabelPosition": "right"}, {"key": "symptoms_during_driving", "type": "radio", "input": true, "label": "Do you experience symptoms of hypoglycemia while driving or operating heavy equipment?<br><strong>Important: If you answer 'Yes,' we are obligated to report this to the Ministry of Transportation.</strong>", "values": [{"label": "Yes", "value": true}, {"label": "No", "value": false}], "tooltip": "Reporting this is a legal requirement for your safety and others' safety on the road.", "validate": {"required": true}, "adminflag": true, "tableView": true, "customConditional": "show = data.experience_hypoglycemia === true && !!data.frequency_of_hypoglycemia;"}, {"key": "hypoglycemia_severity", "type": "radio", "input": true, "label": "How severe are your hypoglycemia episodes?", "values": [{"label": "<PERSON><PERSON> (able to self-treat)", "value": "mild"}, {"label": "Moderate (require assistance from others)", "value": "moderate"}, {"label": "Severe (require medical intervention)", "value": "severe"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.experience_hypoglycemia === true && !!data.symptoms_during_driving;"}, {"key": "primary_care_provider", "type": "radio", "input": true, "label": "Do you have a primary care provider (i.e., family doctor or nurse practitioner) or an endocrinologist that you see for your routine medical concerns?", "values": [{"label": "Yes, I have a primary care provider or endocrinologist", "value": "yes"}, {"label": "No, I do not have a primary care provider or endocrinologist", "value": "no"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.experience_hypoglycemia === true && !!data.treatment_for_hypoglycemia;"}, {"key": "heading_prior_lab_values", "type": "content", "input": false, "label": "Content", "html": "<h2>Prior Lab Values</h2>", "tableView": false}, {"key": "prior_tests_completed", "type": "selectboxes", "input": true, "label": "Have you had any of the following tests completed?", "values": [{"label": "HbA1c", "value": "hba1c"}, {"label": "Blood Sugar Level", "value": "blood_sugar"}, {"label": "I have not had these tests completed", "value": "no_prior_hba1c_blood_sugar"}], "inputType": "checkbox", "tableView": true, "optionsLabelPosition": "right"}, {"key": "heading_hba1c", "type": "content", "input": false, "label": "Content", "html": "<h3>HbA1c</h3><p>Please provide details about your HbA1c test.</p>", "customConditional": "show = data.prior_tests_completed?.hba1c;", "tableView": false}, {"key": "last_hba1c_test", "type": "select", "input": true, "label": "When was your last HbA1c test completed?", "placeholder": "Select the time range", "widget": "html5", "data": {"values": [{"label": "< 3 months ago", "value": "<3months"}, {"label": "3-6 months ago", "value": "3-6_months"}, {"label": "6-12 months ago", "value": "6-12_months"}, {"label": "1-2 years ago", "value": "1-2_years"}, {"label": "2-3 years ago", "value": "2-3_years"}, {"label": "3-5 years ago", "value": "3-5_years"}, {"label": "More than 5 years ago", "value": "5+_years"}]}, "validate": {"required": true}, "tableView": true, "confirm_label": "Last HbA1c Test:", "customConditional": "show = data.prior_tests_completed?.hba1c;"}, {"key": "prior_hba1c_value", "type": "select", "input": true, "label": "What was your most recent HbA1c level?", "placeholder": "Select your HbA1c level", "widget": "html5", "data": {"values": [{"label": "I don't remember my result", "value": "dont_remember"}, {"label": "< 5.7%", "value": "<5.7"}, {"label": "5.7-6.4%", "value": "5.7-6.4"}, {"label": "6.5-7.0%", "value": "6.5-7.0"}, {"label": "7.1-8.0%", "value": "7.1-8.0"}, {"label": "> 8.0%", "value": ">8.0"}]}, "validate": {"required": true}, "tableView": true, "confirm_label": "HbA1c Value:", "customConditional": "show = data.prior_tests_completed?.hba1c;"}, {"key": "heading_blood_sugar", "type": "content", "input": false, "label": "Content", "html": "<h3>Blood Sugar Level</h3><p>Please provide details about your blood sugar test.</p>", "customConditional": "show = data.prior_tests_completed?.blood_sugar;", "tableView": false}, {"key": "last_blood_sugar_test", "type": "select", "input": true, "label": "When was your last blood sugar test completed?", "placeholder": "Select the time range", "widget": "html5", "data": {"values": [{"label": "< 3 months ago", "value": "<3months"}, {"label": "3-6 months ago", "value": "3-6_months"}, {"label": "6-12 months ago", "value": "6-12_months"}, {"label": "1-2 years ago", "value": "1-2_years"}, {"label": "2-3 years ago", "value": "2-3_years"}, {"label": "3-5 years ago", "value": "3-5_years"}, {"label": "More than 5 years ago", "value": "5+_years"}]}, "validate": {"required": true}, "tableView": true, "confirm_label": "Last Blood Sugar Test:", "customConditional": "show = data.prior_tests_completed?.blood_sugar;"}, {"key": "prior_blood_sugar_value", "type": "select", "input": true, "label": "What was your most recent blood sugar level (mmol/L)?", "placeholder": "Select your blood sugar level", "widget": "html5", "data": {"values": [{"label": "I don't remember my result", "value": "dont_remember"}, {"label": "< 4.0 mmol/L (< 72 mg/dL)", "value": "<4.0"}, {"label": "4.0-4.4 mmol/L (72-79 mg/dL)", "value": "4.0-4.4"}, {"label": "4.5-4.9 mmol/L (81-88 mg/dL)", "value": "4.5-4.9"}, {"label": "5.0-5.4 mmol/L (90-97 mg/dL)", "value": "5.0-5.4"}, {"label": "5.5-5.9 mmol/L (99-106 mg/dL)", "value": "5.5-5.9"}, {"label": "6.0-6.4 mmol/L (108-115 mg/dL)", "value": "6.0-6.4"}, {"label": "6.5-6.9 mmol/L (117-124 mg/dL)", "value": "6.5-6.9"}, {"label": "7.0-7.4 mmol/L (126-133 mg/dL)", "value": "7.0-7.4"}, {"label": "7.5-7.9 mmol/L (135-142 mg/dL)", "value": "7.5-7.9"}, {"label": "8.0-8.4 mmol/L (144-151 mg/dL)", "value": "8.0-8.4"}, {"label": "8.5-8.9 mmol/L (153-160 mg/dL)", "value": "8.5-8.9"}, {"label": "9.0-9.4 mmol/L (162-169 mg/dL)", "value": "9.0-9.4"}, {"label": "> 9.4 mmol/L (> 169 mg/dL)", "value": ">9.4"}]}, "validate": {"required": true}, "tableView": true, "confirm_label": "Blood Sugar Level:", "customConditional": "show = data.prior_tests_completed?.blood_sugar;"}, {"key": "blood_sugar_fasting_status", "type": "radio", "input": true, "label": "Was this blood sugar test done while fasting or non-fasting?", "values": [{"label": "Fasting", "value": "fasting"}, {"label": "Non-fasting", "value": "non_fasting"}, {"label": "I’m not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.prior_tests_completed?.blood_sugar && data.prior_blood_sugar_value !== 'dont_remember';"}, {"key": "prior_investigations_heading", "type": "content", "input": false, "label": "Content", "html": "<h3>Prior Investigations</h3>", "tableView": false, "customConditional": "show = data.experience_hypoglycemia === true;"}, {"key": "prior_investigations_question", "type": "radio", "input": true, "label": "Since experiencing these episodes, have you had this investigated by a doctor in the past, either with an exam or lab testing?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.experience_hypoglycemia === true;"}, {"key": "prior_investigation_details", "type": "selectboxes", "input": true, "label": "If yes, which of the following investigations were conducted?", "values": [{"label": "Lab work (e.g., blood sugar levels, HbA1c)", "value": "lab_work", "shortcut": ""}, {"label": "Physical exam", "value": "physical_exam", "shortcut": ""}, {"label": "Stress testing (e.g., exercise tolerance)", "value": "stress_testing", "shortcut": ""}, {"label": "Continuous Glucose Monitoring (CGM)", "value": "cgm_testing", "shortcut": ""}, {"label": "ECG or heart monitoring", "value": "ecg_monitoring", "shortcut": ""}, {"label": "Other", "value": "other_investigation", "shortcut": ""}], "inputType": "checkbox", "validate": {"custom": "valid = _.some(_.values(data.prior_investigation_details));", "customMessage": "Please select at least one investigation method."}, "tableView": true, "customConditional": "show = data.experience_hypoglycemia === true && data.prior_investigations_question === 'yes';", "optionsLabelPosition": "right"}, {"key": "other_investigation_details", "type": "textarea", "input": true, "label": "If 'Other', please specify:", "tableView": true, "autoExpand": false, "customConditional": "show = data.experience_hypoglycemia === true && data.prior_investigation_details?.other_investigation;"}, {"key": "intake_kind", "type": "textfield", "input": true, "label": "Intake Kind", "hidden": true, "disabled": true, "tableView": true, "clearOnHide": false, "calculateValue": "value = 'mail';"}, {"key": "intake_mt_key", "type": "textfield", "input": true, "label": "Intake Mail Template Key", "hidden": true, "disabled": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.intake_kind=='mail'?'intake-cgm':'intake-denial'"}, {"key": "allowed_rt_keys", "type": "textfield", "input": true, "label": "Allowed Rep<PERSON>", "hidden": true, "disabled": true, "multiple": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.intake_mt_key=='intake-denial'?[]:['get-rx','appointment-intake','edit-intake']"}]}