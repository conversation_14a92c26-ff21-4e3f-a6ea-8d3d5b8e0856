{"components": [{"key": "rx_preference_glp", "type": "radio", "input": true, "label": "Do you have a preferred treatment rx or medication?", "inline": false, "values": [{"label": "Ozempic (Semaglutide) - Most Popular", "value": "ozempic"}, {"label": "Wegovy (Semaglutide)", "value": "wegovy"}, {"label": "Ry<PERSON><PERSON> (Semaglutide)", "value": "ry<PERSON><PERSON>"}, {"label": "Mounjaro (Tirzepatide)", "value": "mou<PERSON><PERSON>"}, {"label": "Saxenda (Liraglutide)", "value": "sa<PERSON><PERSON>"}, {"label": "Not sure", "value": "not_sure"}, {"label": "Other", "value": "other"}], "validate": {"required": true}, "tableView": true, "clearOnHide": true, "optionsLabelPosition": "right"}, {"key": "rx_preference_glp_other", "type": "textarea", "input": true, "label": "You selected “other”, please specify:", "validate": {"required": true}, "tableView": true, "autoExpand": false, "customConditional": "show = data.rx_preference_glp == 'other'"}, {"key": "rxts", "type": "textfield", "input": true, "label": "Rx Templates:", "hidden": true, "disabled": true, "multiple": true, "adminFlag": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "v = _.get({'weight_loss_medication_start':{'not_sure':'ozempic-starting','ozempic':'ozempic-starting','wegovy':'wegovy-starting-standard','mounjaro':'mounjaro-25mg-4-weeks','saxenda':'saxenda-starting','rybelsus':'rybelsus-7mg'},'weight_loss_medication_renewal':{'not_sure':'ozempic-1mg-12-weeks','ozempic':'ozempic-1mg-12-weeks','wegovy':'wegovy-starting-12-weeks-1mg','mounjaro':'mounjaro-75mg','saxenda':'saxenda-renewal-3mg','rybelsus':'rybelsus-7mg-3-month'}},`${data.sku}.${data.rx_preference_glp}`,0); value = v ? [v] : [];", "refreshOnChange": true}]}