{"components": [{"key": "heading_school_absence", "html": "<h1><strong>School Absence Consultation</strong></h1><p>This consultation aims to gather information about your current health concerns leading to your absence from school or work. Your responses will help our healthcare team expedite your care.</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "reason_for_absence", "type": "selectboxes", "input": true, "label": "What is the reason for your absence? (Select all that apply)", "values": [{"label": "Cold or Influenza", "value": "cold_flu"}, {"label": "<PERSON><PERSON><PERSON> illness", "value": "stomach_illness"}, {"label": "Flare of a health issue", "value": "flare_health_issue"}, {"label": "Menstrual pain", "value": "menstrual_discomfort", "customConditional": "show = data.sex === 'female';"}, {"label": "<PERSON><PERSON> / Migraine", "value": "headache"}, {"label": "Insomnia (i.e. difficulty sleeping)", "value": "insomnia"}, {"label": "Stress / Mental Health", "value": "stress"}, {"label": "Other", "value": "other"}], "inputType": "checkbox", "tableView": true, "optionsLabelPosition": "right"}, {"key": "other_reason_for_absence", "type": "textfield", "input": true, "label": "Please specify your reason for absence:", "tableView": true, "customConditional": "show = data.reason_for_absence && data.reason_for_absence.other === true;"}, {"key": "heading_symptom_onset", "html": "<h2>Symptom Onset</h2><p>Please indicate when your symptoms first began.</p>", "type": "content", "input": false, "tableView": false, "customConditional": "show = data.reason_for_absence && _.size(data.reason_for_absence) > 0;"}, {"key": "symptom_onset_date", "type": "datetime", "input": true, "label": "When did your symptoms start?", "widget": "calendar", "tableView": true, "customConditional": "show = data.reason_for_absence && _.size(data.reason_for_absence) > 0;"}, {"key": "heading_cold_flu", "type": "content", "input": false, "html": "<h2>Cold or Influenza</h2>", "tableView": false, "customConditional": "show = data.reason_for_absence && data.reason_for_absence.cold_flu === true;"}, {"key": "cold_flu_frequency", "type": "radio", "input": true, "label": "How often do you typically experience cold or flu episodes?", "values": [{"label": "Frequently (multiple episodes per season)", "value": "frequent"}, {"label": "Occasionally (once per season)", "value": "occasional"}, {"label": "Rarely (less than once per season)", "value": "rare"}, {"label": "This is my first episode", "value": "first_time"}], "tableView": true, "validate": {"required": true}, "customConditional": "show = data.reason_for_absence && data.reason_for_absence.cold_flu === true;", "optionsLabelPosition": "right"}, {"key": "cold_flu_severity", "type": "radio", "input": true, "label": "How severe are your current cold or flu symptoms?", "values": [{"label": "Mild", "value": "mild"}, {"label": "Moderate", "value": "moderate"}, {"label": "Severe", "value": "severe"}], "tableView": true, "validate": {"required": true}, "customConditional": "show = data.cold_flu_frequency != null && data.reason_for_absence && data.reason_for_absence.cold_flu === true;", "optionsLabelPosition": "right"}, {"key": "cold_flu_duration", "type": "select", "input": true, "label": "How long have your current cold or flu symptoms lasted?", "data": {"values": [{"label": "< 1 day", "value": "less_than_1_day"}, {"label": "1-2 days", "value": "1_2_days"}, {"label": "3-4 days", "value": "3_4_days"}, {"label": "5-7 days", "value": "5_7_days"}, {"label": "More than 7 days", "value": "more_than_7_days"}]}, "tableView": true, "validate": {"required": true}, "customConditional": "show = data.cold_flu_severity != null && data.reason_for_absence && data.reason_for_absence.cold_flu === true;", "widget": "html5", "optionsLabelPosition": "right"}, {"key": "cold_flu_comparison", "type": "radio", "input": true, "label": "Does this episode feel similar to your previous cold/flu episodes, or is it different?", "values": [{"label": "Similar to usual", "value": "similar"}, {"label": "Different from usual", "value": "different"}, {"label": "Not sure", "value": "unsure"}], "tableView": true, "validate": {"required": true}, "customConditional": "show = data.cold_flu_duration != null && data.reason_for_absence && data.reason_for_absence.cold_flu === true;", "optionsLabelPosition": "right"}, {"key": "cold_flu_status", "type": "radio", "input": true, "label": "What is the current status of your cold or flu symptoms?", "values": [{"label": "Improving", "value": "improving"}, {"label": "Resolved", "value": "resolved"}, {"label": "No change", "value": "no_change"}, {"label": "Worsening", "value": "worsening"}], "tableView": true, "validate": {"required": true}, "customConditional": "show = data.cold_flu_comparison != null && data.reason_for_absence && data.reason_for_absence.cold_flu === true;", "optionsLabelPosition": "right"}, {"key": "cold_flu_red_flags", "type": "selectboxes", "input": true, "label": "Are you experiencing any concerning symptoms (red flags) with your cold or flu?", "values": [{"label": "Fever", "value": "fever", "shortcut": ""}, {"label": "Severe shortness of breath", "value": "severe_shortness", "shortcut": ""}, {"label": "Chest pain or pressure", "value": "chest_pain", "shortcut": ""}, {"label": "Nausea or vomiting", "value": "nausea or vomiting", "shortcut": ""}, {"label": "<PERSON><PERSON>", "value": "rash", "shortcut": ""}], "inputType": "checkbox", "tableView": true, "validate": {"required": true}, "customConditional": "show = data.cold_flu_status != null && data.reason_for_absence && data.reason_for_absence.cold_flu === true;", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_cold_flu_red_flags", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "Please select at least one red flag symptom or 'None of the above'."}, "validate": {"custom": "valid = !!data.none_of_the_above_cold_flu_red_flags || _.some(_.values(data.cold_flu_red_flags));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "optionsLabelPosition": "right", "customConditional": "show = data.cold_flu_red_flags != null && data.reason_for_absence && data.reason_for_absence.cold_flu === true;"}, {"key": "cold_shortness_of_breath_functional", "type": "radio", "input": true, "label": "Do you feel short of breath when speaking in full sentences or walking from your living room to your bathroom?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.cold_flu_red_flags != null && data.reason_for_absence && data.reason_for_absence.cold_flu === true;", "optionsLabelPosition": "right"}, {"key": "heading_stomach_illness", "type": "content", "input": false, "html": "</br><h2><PERSON><PERSON><PERSON></h2>", "tableView": false, "customConditional": "show = data.reason_for_absence && data.reason_for_absence.stomach_illness === true;"}, {"key": "stomach_illness_frequency", "type": "radio", "input": true, "label": "How often do you experience episodes of stomach illness?", "values": [{"label": "Frequently (multiple episodes per season)", "value": "frequent"}, {"label": "Occasionally (once per season)", "value": "occasional"}, {"label": "Rarely (less than once per season)", "value": "rare"}, {"label": "This is my first episode", "value": "first_time"}], "tableView": true, "validate": {"required": true}, "customConditional": "show = data.reason_for_absence && data.reason_for_absence.stomach_illness === true;", "optionsLabelPosition": "right"}, {"key": "stomach_illness_severity", "type": "radio", "input": true, "label": "How severe are your current stomach illness symptoms?", "values": [{"label": "Mild", "value": "mild"}, {"label": "Moderate", "value": "moderate"}, {"label": "Severe", "value": "severe"}], "tableView": true, "validate": {"required": true}, "customConditional": "show = data.stomach_illness_frequency != null && data.reason_for_absence && data.reason_for_absence.stomach_illness === true;", "optionsLabelPosition": "right"}, {"key": "stomach_illness_duration", "type": "select", "input": true, "label": "How long have your current stomach illness symptoms lasted?", "data": {"values": [{"label": "< 6 hours", "value": "less_than_6_hours"}, {"label": "6-12 hours", "value": "6_12_hours"}, {"label": "12-18 hours", "value": "12_18_hours"}, {"label": "18-24 hours", "value": "18_24_hours"}, {"label": "1 day", "value": "1_day"}, {"label": "2 days", "value": "2_days"}, {"label": "3 days", "value": "3_days"}, {"label": "4 days", "value": "4_days"}, {"label": "5+ days", "value": "5_plus_days"}]}, "tableView": true, "validate": {"required": true}, "customConditional": "show = data.stomach_illness_severity != null && data.reason_for_absence && data.reason_for_absence.stomach_illness === true;", "widget": "html5", "optionsLabelPosition": "right"}, {"key": "stomach_illness_comparison", "type": "radio", "input": true, "label": "Does this episode feel similar to your previous stomach illness episodes, or is it different?", "values": [{"label": "Similar to usual", "value": "similar"}, {"label": "Different from usual", "value": "different"}, {"label": "Not sure", "value": "unsure"}], "tableView": true, "validate": {"required": true}, "customConditional": "show = data.stomach_illness_duration != null && data.reason_for_absence && data.reason_for_absence.stomach_illness === true;", "optionsLabelPosition": "right"}, {"key": "stomach_illness_triggers", "type": "selectboxes", "input": true, "label": "Have you noticed any obvious triggers for your stomach illness? (Select all that apply)", "values": [{"label": "<PERSON><PERSON>", "value": "sushi"}, {"label": "Spicy foods", "value": "spicy"}, {"label": "Dairy products", "value": "dairy"}, {"label": "Gluten", "value": "gluten"}, {"label": "Fried foods", "value": "fried"}, {"label": "Alcohol", "value": "alcohol"}, {"label": "Stress", "value": "stress"}, {"label": "Medications", "value": "medications"}, {"label": "Other", "value": "other_trigger"}], "inputType": "checkbox", "tableView": true, "validate": {"required": false}, "customConditional": "show = data.stomach_illness_comparison != null && data.reason_for_absence && data.reason_for_absence.stomach_illness === true;", "optionsLabelPosition": "right"}, {"key": "stomach_illness_red_flags", "type": "selectboxes", "input": true, "label": "Are you experiencing any concerning symptoms with your stomach illness?", "values": [{"label": "Severe abdominal pain", "value": "severe_abdominal_pain", "shortcut": ""}, {"label": "Bloody vomit", "value": "bloody_vomit", "shortcut": ""}, {"label": "Bloody stool", "value": "bloody_stool", "shortcut": ""}, {"label": "Severe dehydration", "value": "severe_dehydration", "shortcut": ""}, {"label": "High fever", "value": "high_fever", "shortcut": ""}, {"label": "Persistent vomiting", "value": "persistent_vomiting", "shortcut": ""}], "inputType": "checkbox", "tableView": true, "validate": {"required": true}, "customConditional": "show = data.stomach_illness_comparison != null && data.reason_for_absence && data.reason_for_absence.stomach_illness === true;", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_stomach_red_flags", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "Please select at least one red flag symptom or choose 'None of the above'."}, "validate": {"custom": "valid = !!data.none_of_the_above_stomach_red_flags || _.some(_.values(data.stomach_illness_red_flags));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "optionsLabelPosition": "right", "customConditional": "show = data.stomach_illness_red_flags != null && data.reason_for_absence && data.reason_for_absence.stomach_illness === true;"}, {"key": "stomach_investigations", "type": "radio", "input": true, "label": "Have you had any investigations (e.g., abdominal ultrasound, endoscopy) in the past?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "unsure"}], "tableView": true, "validate": {"required": true}, "customConditional": "show = data.stomach_illness_functional != null && data.reason_for_absence && data.reason_for_absence.stomach_illness === true;", "optionsLabelPosition": "right"}, {"key": "stomach_investigation_details", "type": "selectboxes", "input": true, "label": "What investigations have you had done? (Select all that apply)", "values": [{"label": "Abdominal ultrasound", "value": "abdominal_ultrasound"}, {"label": "Endoscopy", "value": "endoscopy"}, {"label": "CT scan", "value": "ct_scan"}, {"label": "Blood tests", "value": "blood_tests"}, {"label": "Other", "value": "other_investigation"}], "inputType": "checkbox", "tableView": true, "validate": {"required": true}, "customConditional": "show = data.stomach_investigations === 'yes' && data.reason_for_absence && data.reason_for_absence.stomach_illness === true;", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_stomach_investigation_details", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "Please select at least one investigation or choose 'None of the above'."}, "validate": {"custom": "valid = !!data.none_of_the_above_stomach_investigation_details || _.some(_.values(data.stomach_investigation_details));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "optionsLabelPosition": "right", "customConditional": "show = data.stomach_investigation_details != null && data.reason_for_absence && data.reason_for_absence.stomach_illness === true;"}, {"key": "stomach_symptom_trend", "type": "radio", "input": true, "label": "Do you feel your stomach illness symptoms are getting better, worse, or remaining the same?", "values": [{"label": "Better", "value": "better"}, {"label": "Worse", "value": "worse"}, {"label": "The same", "value": "same"}, {"label": "Not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.stomach_investigations != null && data.reason_for_absence && data.reason_for_absence.stomach_illness === true;", "optionsLabelPosition": "right"}, {"key": "heading_flare_health_issue", "html": "<h2>Flare of a Health Issue</h2>", "type": "content", "input": false, "tableView": false, "customConditional": "show = data.reason_for_absence && data.reason_for_absence.flare_health_issue === true;"}, {"key": "heading_menstrual_discomfort", "html": "<h2>Menstrual Pain</h2>", "type": "content", "input": false, "tableView": false, "customConditional": "show = data.reason_for_absence && data.reason_for_absence.menstrual_discomfort === true && data.sex === 'female';"}, {"key": "menstrual_pain_timing", "type": "selectboxes", "input": true, "label": "When do you typically experience menstrual pain? (Select all that apply)", "values": [{"label": "In the middle of my cycle", "value": "mid_cycle"}, {"label": "A few days before my period", "value": "few_days_before"}, {"label": "Throughout my period", "value": "throughout_cycle"}, {"label": "A few days after my period", "value": "a_few_days_after"}, {"label": "Randomly", "value": "random"}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "customConditional": "show = data.reason_for_absence && data.reason_for_absence.menstrual_discomfort === true && data.sex === 'female';", "optionsLabelPosition": "right"}, {"key": "menstrual_pain_history", "type": "radio", "input": true, "label": "Have your cycles always been painful, or is this a new change?", "values": [{"label": "Always been painful", "value": "always"}, {"label": "This is a new change", "value": "new"}, {"label": "I'm not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.reason_for_absence && data.reason_for_absence.menstrual_discomfort === true && data.sex === 'female' && _.size(data.menstrual_pain_timing) > 0;", "optionsLabelPosition": "right"}, {"key": "menstrual_pain_change_duration", "data": {"values": [{"label": "< 1 month ago", "value": "less_than_1_month"}, {"label": "1-3 months ago", "value": "1_3_months"}, {"label": "3-6 months ago", "value": "3_6_months"}, {"label": "6-12 months ago", "value": "6_12_months"}, {"label": "1-2 years ago", "value": "1_2_years"}, {"label": "2-3 years ago", "value": "2_3_years"}, {"label": "3-5 years ago", "value": "3_5_years"}, {"label": "5+ years ago", "value": "5_plus_years"}]}, "type": "select", "input": true, "label": "When did you first notice this change in your menstrual pain?", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.reason_for_absence && data.reason_for_absence.menstrual_discomfort === true && data.sex === 'female' && (data.menstrual_pain_history === 'new' || data.menstrual_pain_history === 'unsure');", "optionsLabelPosition": "right"}, {"key": "menstrual_investigations", "type": "radio", "input": true, "label": "Have you had any investigations (e.g., pelvic exam, ultrasound) for your menstrual pain?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.menstrual_pain_history != null && data.reason_for_absence && data.reason_for_absence.menstrual_discomfort === true && data.sex === 'female';", "optionsLabelPosition": "right"}, {"key": "menstrual_investigation_details", "type": "selectboxes", "input": true, "label": "What investigations have you had done? (Select all that apply)", "values": [{"label": "Ultrasound", "value": "ultrasound"}, {"label": "Pelvic exam", "value": "pelvic_exam"}, {"label": "Gynecologist referral", "value": "gynecologist_referral"}, {"label": "Colposcopy", "value": "colposcopy"}, {"label": "Pap test", "value": "pap_test"}, {"label": "STI/STD screening", "value": "sti_screening"}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "customConditional": "show = data.menstrual_investigations === 'yes' && data.reason_for_absence && data.reason_for_absence.menstrual_discomfort === true && data.sex === 'female';", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_menstrual_investigation_details", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "Please select at least one investigation or choose 'None of the above'."}, "validate": {"custom": "valid = !!data.none_of_the_above_menstrual_investigation_details || !!_.some(_.values(data.menstrual_investigation_details));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.menstrual_investigations === 'yes' && data.reason_for_absence && data.reason_for_absence.menstrual_discomfort === true && data.sex === 'female';", "optionsLabelPosition": "right"}, {"key": "menstrual_currently_pregnant", "type": "radio", "input": true, "label": "Are you currently pregnant?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.menstrual_investigations != null && data.reason_for_absence && data.reason_for_absence.menstrual_discomfort === true && data.sex === 'female';", "optionsLabelPosition": "right"}, {"key": "menstrual_cycle_delay", "type": "radio", "input": true, "label": "Are you currently late for your menstrual cycle?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.menstrual_currently_pregnant === 'no' && data.reason_for_absence && data.reason_for_absence.menstrual_discomfort === true && data.sex === 'female';", "optionsLabelPosition": "right"}, {"key": "pregnancy_test_taken", "type": "radio", "input": true, "label": "Have you taken a pregnancy test?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.menstrual_cycle_delay === 'yes' && data.reason_for_absence && data.reason_for_absence.menstrual_discomfort === true && data.sex === 'female';", "optionsLabelPosition": "right"}, {"key": "pregnancy_test_warning", "html": "<div style='border: 1px solid red; padding: 10px; background-color: #ffe6e6;'><strong style='color: red;'>Important:</strong><ul style='color: red; margin: 0; padding-left: 20px;'><li><strong>Immediate Action:</strong> Please take a pregnancy test at home immediately.</li><li><strong>Warning:</strong> A <strong>positive result</strong> combined with <strong>abdominal pain</strong> may indicate an <strong>ectopic pregnancy</strong>, which can be <strong>life-threatening</strong>.</li></ul></div>", "type": "content", "input": false, "tableView": false, "customConditional": "show = data.pregnancy_test_taken === 'no' && data.reason_for_absence && data.reason_for_absence.menstrual_discomfort === true && data.sex === 'female';"}, {"key": "pregnancy_test_instructions", "type": "radio", "input": true, "label": "Do you understand these instructions?", "values": [{"label": "I understand", "value": "understand"}, {"label": "I do not understand", "value": "do_not_understand"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.pregnancy_test_taken === 'no' && data.reason_for_absence && data.reason_for_absence.menstrual_discomfort === true && data.sex === 'female';", "optionsLabelPosition": "right"}, {"key": "pregnancy_test_positive", "type": "radio", "input": true, "label": "Was the test positive?", "values": [{"label": "Yes", "value": "positive"}, {"label": "No", "value": "negative"}, {"label": "Not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.pregnancy_test_taken === 'yes' && data.reason_for_absence && data.reason_for_absence.menstrual_discomfort === true && data.sex === 'female';", "optionsLabelPosition": "right"}, {"key": "menstrual_pain_character", "type": "radio", "input": true, "label": "Does the pain seem similar to your typical menstrual pain or different?", "values": [{"label": "Similar to usual pain", "value": "similar"}, {"label": "Different from usual", "value": "different"}, {"label": "Not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.menstrual_cycle_delay != null && data.reason_for_absence && data.reason_for_absence.menstrual_discomfort === true && data.sex === 'female';", "optionsLabelPosition": "right"}, {"key": "menstrual_pain_character_confirmation", "type": "textfield", "input": true, "label": "Please confirm by typing 'same as usual' if your menstrual pain is definitely typcial of prior pain:", "validate": {"custom": "valid = (input && input.toLowerCase() === 'same as usual');", "required": true}, "tableView": true, "errorLabel": "Your response must be 'same as usual'.", "customConditional": "show = data.menstrual_pain_character === 'similar';"}, {"key": "menstrual_systemic_symptoms", "type": "radio", "input": true, "label": "Have you experienced any fevers along with this menstrual pain?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.menstrual_pain_character != null && data.reason_for_absence && data.reason_for_absence.menstrual_discomfort === true && data.sex === 'female';", "optionsLabelPosition": "right"}, {"key": "menstrual_diagnosis", "type": "selectboxes", "input": true, "label": "Have you ever been diagnosed with any of the following conditions?", "values": [{"label": "Endometriosis", "value": "endometriosis"}, {"label": "Uterine Fibroids", "value": "fibroids"}, {"label": "Polycystic Ovary Syndrome (PCOS)", "value": "pcos"}, {"label": "Adenomyosis", "value": "adenomyosis"}], "inputType": "checkbox", "tableView": true, "customConditional": "show = data.menstrual_systemic_symptoms != null && data.reason_for_absence && data.reason_for_absence.menstrual_discomfort === true && data.sex === 'female';", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_menstrual_diagnosis", "type": "checkbox", "input": true, "label": "None of the above", "validate": {"custom": "valid = !!data.none_of_the_above_menstrual_diagnosis || !!_.some(_.values(data.menstrual_diagnosis));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.menstrual_systemic_symptoms != null && data.reason_for_absence && data.reason_for_absence.menstrual_discomfort === true && data.sex === 'female';", "optionsLabelPosition": "right"}, {"key": "heading_headache", "type": "content", "input": false, "html": "</br><h2>Headache/Migraines</h2>", "tableView": false, "customConditional": "show = data.reason_for_absence && data.reason_for_absence.headache === true;"}, {"key": "headache_frequency", "type": "radio", "input": true, "label": "How often do you experience headaches or migraines?", "values": [{"label": "Daily", "value": "daily"}, {"label": "Several times a week", "value": "several_times_week"}, {"label": "Occasionally", "value": "occasionally"}, {"label": "Rarely", "value": "rarely"}], "tableView": true, "validate": {"required": true}, "customConditional": "show = data.reason_for_absence && data.reason_for_absence.headache === true;", "optionsLabelPosition": "right"}, {"key": "headache_intensity", "type": "radio", "input": true, "label": "How would you describe the intensity of your headaches?", "values": [{"label": "Mild", "value": "mild"}, {"label": "Moderate", "value": "moderate"}, {"label": "Severe", "value": "severe"}], "tableView": true, "validate": {"required": true}, "customConditional": "show = data.headache_frequency != null && data.reason_for_absence && data.reason_for_absence.headache === true;", "optionsLabelPosition": "right"}, {"key": "headache_duration", "type": "select", "input": true, "label": "How long do your headaches usually last?", "data": {"values": [{"label": "< 1 hour", "value": "less_than_1_hour"}, {"label": "1-6 hours", "value": "1_6_hours"}, {"label": "6-12 hours", "value": "6_12_hours"}, {"label": "12-24 hours", "value": "12_24_hours"}, {"label": "1 day - 2 days", "value": "1_2_days"}, {"label": "3+ days", "value": "3_plus_days"}]}, "tableView": true, "validate": {"required": true}, "customConditional": "show = data.headache_intensity != null && data.reason_for_absence && data.reason_for_absence.headache === true;", "widget": "html5", "optionsLabelPosition": "right"}, {"key": "headache_triggers", "type": "selectboxes", "input": true, "label": "What triggers your headaches? (Select all that apply)", "values": [{"label": "Stress", "value": "stress_trigger"}, {"label": "Lack of sleep", "value": "lack_of_sleep"}, {"label": "Bright lights", "value": "bright_lights"}, {"label": "Food triggers", "value": "food_triggers"}, {"label": "Hormonal changes", "value": "hormonal_changes"}, {"label": "Other", "value": "other_trigger"}], "inputType": "checkbox", "tableView": true, "validate": {"required": true}, "customConditional": "show = data.headache_duration != null && data.reason_for_absence && data.reason_for_absence.headache === true;", "optionsLabelPosition": "right"}, {"key": "other_headache_trigger", "type": "textfield", "input": true, "label": "Please specify other triggers:", "tableView": true, "customConditional": "show = data.headache_triggers && data.headache_triggers.other_trigger === true;", "validate": {"required": false}}, {"key": "headache_diagnosis", "type": "selectboxes", "input": true, "label": "Have you ever been diagnosed with any of the following headache types?", "values": [{"label": "Migraines", "value": "migraine_without_aura"}, {"label": "Migraine with aura", "value": "migraine_with_aura"}, {"label": "Tension-type headache", "value": "tension_type"}, {"label": "Cluster headache", "value": "cluster"}, {"label": "Just headaches", "value": "headaches_nyd"}, {"label": "Other", "value": "other_headache"}], "inputType": "checkbox", "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = data.headache_triggers != null && data.reason_for_absence && data.reason_for_absence.headache === true;"}, {"key": "other_headache_diagnosis", "type": "textfield", "input": true, "label": "Please specify other headache diagnosis:", "tableView": true, "customConditional": "show = data.headache_diagnosis && data.headache_diagnosis.other_headache === true;", "validate": {"required": false}}, {"key": "headache_improvement", "type": "radio", "input": true, "label": "What is the current status of your headache?", "values": [{"label": "Improving", "value": "improving"}, {"label": "Resolved", "value": "resolved"}, {"label": "No change", "value": "no_change"}, {"label": "Worsening", "value": "worsening"}], "tableView": true, "validate": {"required": true}, "customConditional": "show = data.headache_diagnosis != null && data.reason_for_absence && data.reason_for_absence.headache === true;", "optionsLabelPosition": "right"}, {"key": "head_injury_trauma", "type": "radio", "input": true, "label": "Have you experienced any trauma or head injury prior to your headache?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "unsure"}], "tableView": true, "validate": {"required": true}, "customConditional": "show = data.headache_improvement != null && data.reason_for_absence && data.reason_for_absence.headache === true;", "optionsLabelPosition": "right"}, {"key": "neurological_symptoms", "type": "selectboxes", "input": true, "label": "Do you have any of the following symptoms?", "values": [{"label": "Sudden weakness or numbness", "value": "sudden_weakness", "shortcut": ""}, {"label": "Trouble speaking or slurred speech", "value": "speech_difficulty", "shortcut": ""}, {"label": "Feeling very confused or not thinking clearly", "value": "confusion", "shortcut": ""}, {"label": "Seizures", "value": "seizures", "shortcut": ""}, {"label": "Problems with your vision (e.g., seeing double or losing sight)", "value": "visual_disturbances", "shortcut": ""}, {"label": "Fainting or passing out", "value": "loss_of_consciousness", "shortcut": ""}], "inputType": "checkbox", "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = data.head_injury_trauma != null && data.reason_for_absence && data.reason_for_absence.headache === true;"}, {"key": "none_of_the_above_neurological_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "Please select at least one symptom or choose 'None of the above'."}, "validate": {"custom": "valid = !!data.none_of_the_above_neurological_symptoms || _.some(_.values(data.neurological_symptoms));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "optionsLabelPosition": "right", "customConditional": "show = data.neurological_symptoms != null && data.reason_for_absence && data.reason_for_absence.headache === true;"}, {"key": "blood_pressure_checked", "type": "radio", "input": true, "label": "Have you checked your blood pressure recently?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.neurological_symptoms != null && data.reason_for_absence && data.reason_for_absence.headache === true;", "optionsLabelPosition": "right"}, {"key": "blood_pressure_values", "type": "columns", "input": true, "columns": [{"components": [{"key": "systolic_bp", "type": "textfield", "input": true, "label": "Systolic (mmHg)", "validate": {"required": true}, "tableView": true}]}, {"components": [{"key": "diastolic_bp", "type": "textfield", "input": true, "label": "Diastolic (mmHg)", "validate": {"required": true}, "tableView": true}]}], "customConditional": "show = data.blood_pressure_checked === 'yes' && data.reason_for_absence && data.reason_for_absence.headache === true;", "tableView": false}, {"key": "headache_awoke_from_sleep", "type": "radio", "input": true, "label": "Did your headache force you to wake up from sleep? (For example, while you were sleeping, the headache became so severe that it woke you up, rather than you waking up naturally with a headache.)", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.reason_for_absence && data.reason_for_absence.headache === true;", "optionsLabelPosition": "right"}, {"key": "headache_typical_or_different", "type": "radio", "input": true, "label": "Does this headache feel like your typical headache, or does it feel different?", "values": [{"label": "Typical", "value": "typical"}, {"label": "Different", "value": "different"}, {"label": "Not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.reason_for_absence && data.reason_for_absence.headache === true;", "optionsLabelPosition": "right"}, {"key": "heading_insomnia", "html": "<h2>Insomnia</h2>", "type": "content", "input": false, "tableView": false, "customConditional": "show = data.reason_for_absence && data.reason_for_absence.insomnia === true;"}, {"key": "heading_stress", "type": "content", "input": false, "html": "<h2>Stress / Mental Health</h2>", "tableView": false, "customConditional": "show = data.reason_for_absence && data.reason_for_absence.stress === true;"}, {"key": "mh_prior_diagnosis", "type": "radio", "input": true, "label": "Have you ever been diagnosed with a mental health condition (for example, depression, anxiety, bipolar disorder)?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "unsure"}], "tableView": true, "validate": {"required": true}, "customConditional": "show = data.reason_for_absence && data.reason_for_absence.stress === true;", "optionsLabelPosition": "right"}, {"key": "mh_diagnostic_conditions", "type": "selectboxes", "input": true, "label": "Which of the following mental health conditions have you been diagnosed with?", "values": [{"label": "Depression", "value": "depression"}, {"label": "Anxiety Disorder", "value": "anxiety"}, {"label": "Bipolar Disorder", "value": "bipolar"}, {"label": "Post-Traumatic Stress Disorder (PTSD)", "value": "ptsd"}, {"label": "Obsessive-Compulsive Disorder (OCD)", "value": "ocd"}, {"label": "ADHD", "value": "adhd"}, {"label": "Other", "value": "other_mh_diagnosis"}], "inputType": "checkbox", "tableView": true, "customConditional": "show = data.mh_er_warning != null && data.reason_for_absence && data.reason_for_absence.stress === true;", "optionsLabelPosition": "right"}, {"key": "mh_stress_triggers", "type": "selectboxes", "input": true, "label": "What are the primary triggers for your stress?", "values": [{"label": "Personal issues", "value": "personal"}, {"label": "Family issues", "value": "family"}, {"label": "Overworked", "value": "overworked"}, {"label": "Financial concerns", "value": "financial"}, {"label": "Health concerns", "value": "health"}, {"label": "Other", "value": "other_stress_trigger"}], "inputType": "checkbox", "tableView": true, "customConditional": "show = data.mh_diagnostic_conditions != null && data.reason_for_absence && data.reason_for_absence.stress === true;", "optionsLabelPosition": "right"}, {"key": "mh_red_flags", "type": "selectboxes", "input": true, "label": "Are you currently experiencing any concerning mental health symptoms? (Select all that apply)", "values": [{"label": "Thoughts of self-harm, suicide, or hurting others", "value": "self_harm"}, {"label": "Hearing or seeing things that others do not", "value": "hallucinations"}, {"label": "Severe anxiety or panic attacks", "value": "severe_anxiety"}, {"label": "Extreme mood swings", "value": "extreme_mood_swings"}, {"label": "Other", "value": "other_mh"}], "inputType": "checkbox", "tableView": true, "validate": {"required": true}, "customConditional": "show = data.mh_prior_diagnosis != null && data.reason_for_absence && data.reason_for_absence.stress === true;", "optionsLabelPosition": "right"}, {"key": "mh_follow_up", "type": "radio", "input": true, "label": "Are you currently receiving follow-up care or treatment from a mental health professional?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "unsure"}], "tableView": true, "validate": {"required": true}, "customConditional": "show = data.mh_red_flags != null && data.reason_for_absence && data.reason_for_absence.stress === true;", "optionsLabelPosition": "right"}, {"key": "mh_regular_provider", "type": "radio", "input": true, "label": "Do you have a regular healthcare provider (doctor, nurse practitioner, or specialist) that you see for your mental health?", "values": [{"label": "Yes, I have a regular provider", "value": "yes_provider"}, {"label": "I only use walk-in clinics", "value": "walk_in"}, {"label": "I do not have any provider", "value": "none"}, {"label": "I see a psychiatrist/psychologist regularly", "value": "psychiatrist_psychologist"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.mh_er_instructions != null && data.reason_for_absence && data.reason_for_absence.stress === true;", "optionsLabelPosition": "right"}, {"key": "mh_followup_recommendation", "type": "radio", "input": true, "label": "After obtaining your note, we suggest that you consider scheduling a visit with a local walk-in clinic to discuss additional options for managing your mental health. These include:<br><ul><li>Referral for psychotherapy</li><li>Referral to a psychiatrist for therapy (e.g., cognitive behavioural therapy)</li><li>Initiation of medication, if appropriate</li><li>Stress management stretches and support through student health services</li></ul><br>Do you understand these recommendations?", "values": [{"label": "I understand", "value": "understand"}, {"label": "I do not understand", "value": "do_not_understand"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.mh_follow_up != null && data.reason_for_absence && data.reason_for_absence.stress === true;", "optionsLabelPosition": "right"}, {"key": "mh_er_instructions", "type": "radio", "input": true, "label": "If you experience any severe mental health symptoms (such as intense self-harm thoughts or severe panic), you should seek immediate emergency care. Do you understand these instructions?", "values": [{"label": "I understand", "value": "understand"}, {"label": "I do not understand", "value": "do_not_understand"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.mh_follow_up != null && data.reason_for_absence && data.reason_for_absence.stress === true;", "optionsLabelPosition": "right"}, {"key": "heading_absence_dates", "type": "content", "input": false, "html": "<h2>Absence Dates</h2><p>Please select the start date of your absence and indicate how many days you missed.</p>", "tableView": false}, {"key": "absence_start_date", "type": "datetime", "input": true, "label": "Start Date of Absence", "widget": "calendar", "tableView": true, "validate": {"required": true}}, {"key": "absence_days_missed", "type": "select", "input": true, "label": "How many days did you miss?", "data": {"values": [{"label": "1 day", "value": "1"}, {"label": "2 days", "value": "2"}, {"label": "3 days", "value": "3"}, {"label": "4 days", "value": "4"}, {"label": "5 days", "value": "5"}, {"label": "6 days", "value": "6"}, {"label": "7 days", "value": "7"}]}, "widget": "html5", "tableView": true, "validate": {"required": true}, "optionsLabelPosition": "right"}, {"intake_template_key": "hx-any-questions"}, {"key": "intake_denial_reasons", "type": "textfield", "input": true, "label": "Intake Denial Reasons:", "hidden": true, "disabled": true, "multiple": true, "adminFlag": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "value=_.concat((data.any_other_questions === true && !!data.stated_other_questions && !/^\\s*$/.test(data.stated_other_questions)?['stated_other_questions.not_blank']:[]));", "refreshOnChange": true}, {"key": "intake_kind", "type": "textfield", "input": true, "label": "Intake Kind", "hidden": true, "disabled": true, "tableView": true, "clearOnHide": false, "calculateValue": "value = !data.intake_denial_reasons?.length ? 'mail' : 'chat';"}, {"key": "intake_mt_key", "type": "textfield", "input": true, "label": "Intake Mail Template Key", "hidden": true, "disabled": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.intake_kind=='mail'?'intake-glp':'intake-denial'"}, {"key": "allowed_rt_keys", "type": "textfield", "input": true, "label": "Allowed Rep<PERSON>", "hidden": true, "disabled": true, "multiple": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = ['get-req', 'appointment-intake', 'edit-intake'];"}]}