{"components": [{"key": "heading_smoking_cessation_medication", "html": "</br><h1><center><strong>Smoking Cessation Medication</strong></center></h1><p>To help us provide the best care, please answer the following questions regarding your health and medical history related to smoking cessation.</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "header_smoking_habits", "html": "</br><h3>Smoking Habits</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "types_of_smoking", "type": "selectboxes", "input": true, "label": "What types of smoking do you engage in? (Select all that apply)", "values": [{"label": "Cigarettes", "value": "cigarettes"}, {"label": "Cigars", "value": "cigars"}, {"label": "Pipe tobacco", "value": "pipe_tobacco"}, {"label": "Vaping/e-cigarettes", "value": "vaping"}, {"label": "Hookah/waterpipe", "value": "hookah"}, {"label": "Nicotine Replacement", "value": "nicotine_replacement"}, {"label": "Other", "value": "other"}], "validate": {"required": true}, "confirm_label": "Types of smoking:", "tableView": true}, {"key": "other_smoking_type", "type": "textfield", "input": true, "label": "If you selected 'Other', please specify:", "validate": {"required": true, "customMessage": "Please specify the type of smoking."}, "tableView": true, "confirm_label": "Other type of smoking:", "customConditional": "show = data.types_of_smoking.other"}, {"key": "cigarettes_quantity", "type": "radio", "input": true, "label": "How many cigarettes do you smoke per day?", "values": [{"label": "1-5 cigarettes", "value": "1_5"}, {"label": "6-10 cigarettes", "value": "6_10"}, {"label": "11-15 cigarettes", "value": "11_15"}, {"label": "16-20 cigarettes", "value": "16_20"}, {"label": "More than 20 cigarettes", "value": "more_than_20"}], "validate": {"required": true, "customMessage": "Please select the average number of cigarettes smoked per day."}, "tableView": true, "customConditional": "show = data.types_of_smoking.cigarettes"}, {"key": "cigars_quantity", "type": "radio", "input": true, "confirm_label": "Cigars quantity:", "label": "How many cigars do you smoke per day?", "values": [{"label": "1-2 cigars", "value": "1_2"}, {"label": "3-4 cigars", "value": "3_4"}, {"label": "5-6 cigars", "value": "5_6"}, {"label": "More than 6 cigars", "value": "more_than_6"}], "validate": {"required": true, "customMessage": "Please select the average number of cigars smoked per day."}, "tableView": true, "customConditional": "show = data.types_of_smoking.cigars"}, {"key": "years_smoking", "type": "radio", "input": true, "confirm_label": "Years smoking:", "label": "How many years have you been smoking?", "values": [{"label": "Less than 1 year", "value": "less_than_1", "shortcut": ""}, {"label": "1-5 years", "value": "1_5", "shortcut": ""}, {"label": "6-10 years", "value": "6_10", "shortcut": ""}, {"label": "11-20 years", "value": "11_20", "shortcut": ""}, {"label": "More than 20 years", "value": "more_than_20", "shortcut": ""}], "tooltip": "Calculate the total years you've been smoking by adding up all the years and subtracting any periods you were not smoking.", "validate": {"required": true, "customMessage": "Please select the range that best represents the number of years you have been smoking."}, "tableView": true}, {"key": "header_quit_attempts", "html": "</br><h3>Quit Attempts</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "previous_quit_attempts", "type": "radio", "input": true, "confirm_label": "Previous quit attempts:", "label": "Have you tried quitting smoking before?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "quit_attempts", "type": "radio", "confirm_label": "Quit attempt count:", "input": true, "label": "How many times have you attempted to quit smoking?", "values": [{"label": "1 attempt", "value": "1_attempt"}, {"label": "2 attempts", "value": "2_attempts"}, {"label": "3 attempts", "value": "3_attempts"}, {"label": "4 attempts", "value": "4_attempts"}, {"label": "5 attempts", "value": "5_attempts"}, {"label": "6 or more attempts", "value": "6_more_attempts"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.previous_quit_attempts === 'yes'"}, {"key": "successful_quit", "type": "radio", "confirm_label": "Successful quit:", "input": true, "label": "Have you ever successfully quit smoking for a period of time greater than 3 months?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.previous_quit_attempts === 'yes'"}, {"key": "previous_quit_methods", "type": "selectboxes", "input": true, "confirm_label": "Previous quit methods:", "label": "What methods have you tried before to quit smoking? (Select all that apply)", "values": [{"label": "Nicotine replacement therapy (gum, patches, etc.)", "value": "nicotine_replacement"}, {"label": "Prescription medications", "value": "prescription_medications"}, {"label": "Counseling or support groups", "value": "counseling_support"}, {"label": "E-cigarettes/vaping", "value": "vaping"}, {"label": "Cold turkey", "value": "cold_turkey"}, {"label": "Other", "value": "other"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.previous_quit_attempts === 'yes'"}, {"key": "smoking_at_home", "type": "radio", "confirm_label": "Smoking at home:", "input": true, "label": "Does anyone else smoke with you at home?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "header_zyban_history", "html": "</br><h2><PERSON><PERSON><PERSON> (Bupropion)</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "used_zyban", "type": "radio", "confirm_label": "Used Zyban before:", "input": true, "label": "Have you previously used Zyban (Bupropion) for smoking cessation?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true}, {"key": "current_zyban_use", "type": "radio", "input": true, "confirm_label": "Current Zyban use:", "label": "Are you currently taking <PERSON><PERSON><PERSON> (Bupropion)?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.used_zyban === 'yes';"}, {"key": "smoking_while_zyban", "type": "radio", "input": true, "confirm_label": "Smoking while on Zyban:", "label": "If currently on Zyban, are you still smoking or vaping?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.current_zyban_use === 'yes';"}, {"key": "zyban_usage_timing", "type": "radio", "input": true, "confirm_label": "Zyban usage timing:", "label": "When did you last use Zyban (Bupropion) for quitting smoking?", "values": [{"label": "Within the last month", "value": "last_month"}, {"label": "1-3 months ago", "value": "1_3_months_ago"}, {"label": "3-6 months ago", "value": "3_6_months_ago"}, {"label": "6-12 months ago", "value": "6_12_months_ago"}, {"label": "Over a year ago", "value": "over_a_year_ago"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.used_zyban === 'yes'", "optionsLabelPosition": "right"}, {"key": "zyban_effectiveness", "type": "radio", "input": true, "confirm_label": "Zyban effectiveness:", "label": "If yes, how effective was <PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON><PERSON>) for you?", "values": [{"label": "Very effective", "value": "very_effective"}, {"label": "Somewhat effective", "value": "somewhat_effective"}, {"label": "Not effective", "value": "not_effective"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.used_zyban === 'yes'"}, {"key": "zyban_side_effects", "type": "selectboxes", "input": true, "confirm_label": "Zyban side effects:", "label": "Did you experience any side effects while using Zyban (Bupropion)? (Select all that apply)", "values": [{"label": "Dry mouth", "value": "dry_mouth"}, {"label": "Insomnia", "value": "insomnia"}, {"label": "Headaches", "value": "headaches"}, {"label": "<PERSON><PERSON><PERSON>", "value": "nausea"}, {"label": "Constipation", "value": "constipation"}, {"label": "Increased sweating", "value": "increased_sweating"}, {"label": "Tremors", "value": "tremors"}, {"label": "Ringing in the ears", "value": "ringing_ears"}, {"label": "None of the above", "value": "none"}], "validate": {"required": true}, "tableView": true, "autoExpand": false, "customConditional": "show = data.used_zyban === 'yes'"}, {"key": "zyban_side_effects_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following Zyban side effects:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "confirm_label": "Zyban side effects not present:", "tableView": true, "spellcheck": false, "clearOnHide": false, "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.omitBy(data.zyban_side_effects, _.identity)), _.capitalize), ', '), /_/g, ' ');"}, {"key": "header_champix_history", "html": "</br><h2>Champix (Varenicline)</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "used_champix", "type": "radio", "input": true, "confirm_label": "Used Champix before:", "label": "Have you previously used Champix (Varenicline) for smoking cessation?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true}, {"key": "current_champix_use", "type": "radio", "input": true, "confirm_label": "Current Champix use:", "label": "Are you currently taking Champix (Varenicline)?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.used_champix === 'yes';"}, {"key": "smoking_while_champix", "type": "radio", "input": true, "confirm_label": "Smoking while on Champix:", "label": "If currently on Champix, are you still smoking or vaping?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.current_champix_use === 'yes';"}, {"key": "champix_usage_timing", "type": "radio", "input": true, "confirm_label": "Champix usage timing:", "label": "When did you last use Champix (Varenicline) for quitting smoking?", "values": [{"label": "Within the last month", "value": "last_month"}, {"label": "1-3 months ago", "value": "1_3_months_ago"}, {"label": "3-6 months ago", "value": "3_6_months_ago"}, {"label": "6-12 months ago", "value": "6_12_months_ago"}, {"label": "Over a year ago", "value": "over_a_year_ago"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.used_champix === 'yes'", "optionsLabelPosition": "right"}, {"key": "champix_effectiveness", "type": "radio", "confirm_label": "Champix effectiveness:", "input": true, "label": "If yes, how effective was Champix (Varenicline) for you?", "values": [{"label": "Very effective", "value": "very_effective"}, {"label": "Somewhat effective", "value": "somewhat_effective"}, {"label": "Not effective", "value": "not_effective"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.used_champix === 'yes'"}, {"key": "champix_side_effects", "type": "selectboxes", "input": true, "confirm_label": "Champix side effects:", "label": "Did you experience any side effects while using Champix (Varenicline)? (Select all that apply)", "values": [{"label": "<PERSON><PERSON><PERSON>", "value": "nausea"}, {"label": "Sleep disturbances (insomnia or abnormal dreams)", "value": "sleep_disturbances"}, {"label": "Headaches", "value": "headaches"}, {"label": "Increased appetite", "value": "increased_appetite"}, {"label": "Taste changes", "value": "taste_changes"}, {"label": "Dry mouth", "value": "dry_mouth"}, {"label": "Drowsiness", "value": "drowsiness"}, {"label": "Fatigue", "value": "fatigue"}, {"label": "Constipation", "value": "constipation"}, {"label": "Mood changes (e.g., depression, irritability)", "value": "mood_changes"}, {"label": "None of the above", "value": "none"}], "validate": {"required": true}, "tableView": true, "autoExpand": false, "customConditional": "show = data.used_champix === 'yes'"}, {"key": "champix_side_effects_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following Champix side effects:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "confirm_label": "Champix side effects not present:", "clearOnHide": false, "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.omitBy(data.champix_side_effects, _.identity)), _.capitalize), ', '), /_/g, ' ');"}, {"key": "header_nrt_section", "type": "content", "input": false, "html": "</br><h3>Nicotine-Replacement Therapy (NRT)</h3>", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.types_of_smoking.nicotine_replacement;"}, {"key": "nrt_types", "type": "selectboxes", "input": true, "confirm_label": "NRT products used:", "label": "Which NRT products do you CURRENTLY use? (Select all that apply)", "values": [{"label": "Nicotine gum", "value": "gum"}, {"label": "Nicotine lozenge", "value": "lozenge"}, {"label": "Nicotine patch", "value": "patch"}, {"label": "<PERSON><PERSON> inhaler", "value": "inhaler"}, {"label": "Nicotine nasal spray", "value": "nasal_spray"}, {"label": "Nicotine pouch", "value": "pouch"}, {"label": "Other", "value": "other"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.types_of_smoking.nicotine_replacement;"}, {"key": "nrt_other_type", "type": "textfield", "confirm_label": "Other NRT product:", "input": true, "label": "Please specify the OTHER NRT product:", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.nrt_types.other === true;"}, {"key": "smoking_while_on_nrt", "type": "radio", "confirm_label": "Smoking while on NRT:", "input": true, "label": "Are you still smoking (cigarettes, cigars, vaping, etc.) while using NRT?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = (data.types_of_smoking && (data.types_of_smoking.cigarettes || data.types_of_smoking.cigars || data.types_of_smoking.vaping || data.types_of_smoking.hookah || data.types_of_smoking.pipe_tobacco)) && (data.nrt_types && _.some(data.nrt_types, Boolean));"}, {"key": "nrt_gum_brand", "type": "select", "input": true, "confirm_label": "Nicotine gum brand:", "widget": "html5", "label": "Which nicotine gum brand are you using?", "data": {"values": [{"label": "Zonnic", "value": "zonnic"}, {"label": "Nicorette", "value": "nicorette"}, {"label": "Generic / other", "value": "other_gum"}]}, "validate": {"required": true}, "tableView": true, "customConditional": "show = data.nrt_types.gum === true;"}, {"key": "nrt_pouch_brand", "type": "select", "input": true, "confirm_label": "Nicotine pouch brand:", "widget": "html5", "label": "Which nicotine pouch brand are you using?", "data": {"values": [{"label": "Zonnic", "value": "zonnic"}, {"label": "Generic / other", "value": "other_pouch"}]}, "validate": {"required": true}, "tableView": true, "customConditional": "show = data.nrt_types.pouch === true;"}, {"key": "header_zonnic_pouch_details", "type": "content", "input": false, "html": "</br><h4>Zonnic Pouch Details</h4>", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.nrt_pouch_brand === 'zonnic';"}, {"key": "pouch_strength", "type": "radio", "input": true, "confirm_label": "Zonnic pouch strength:", "label": "Zonnic pouch strength:", "values": [{"label": "2 mg", "value": "2mg"}, {"label": "4 mg", "value": "4mg"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.nrt_pouch_brand === 'zonnic';"}, {"key": "pouch_daily_count", "type": "select", "input": true, "confirm_label": "Zonnic pouches per day:", "label": "Approximate Zonnic pouches you use per day (max 15):", "widget": "html5", "data": {"values": [{"label": "1", "value": "1"}, {"label": "2", "value": "2"}, {"label": "3", "value": "3"}, {"label": "4", "value": "4"}, {"label": "5", "value": "5"}, {"label": "6", "value": "6"}, {"label": "7", "value": "7"}, {"label": "8", "value": "8"}, {"label": "9", "value": "9"}, {"label": "10", "value": "10"}, {"label": "11", "value": "11"}, {"label": "12", "value": "12"}, {"label": "13", "value": "13"}, {"label": "14", "value": "14"}, {"label": "15", "value": "15"}, {"label": "15+", "value": "15+"}]}, "validate": {"required": true}, "tableView": true, "customConditional": "show = data.nrt_pouch_brand === 'zonnic';"}, {"key": "pouch_use_duration", "type": "radio", "input": true, "confirm_label": "Zonnic pouch use duration:", "label": "How long have you been using Zonnic pouches?", "values": [{"label": "< 1 week", "value": "under_1w"}, {"label": "1 - 4 weeks", "value": "1_4w"}, {"label": "1 - 3 months", "value": "1_3m"}, {"label": "> 3 months", "value": "over_3m"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.nrt_pouch_brand === 'zonnic';"}, {"key": "pouch_side_effects", "type": "selectboxes", "input": true, "confirm_label": "Zonnic pouch side effects:", "label": "Side-effects while using Zonnic pouches (select all that apply):", "values": [{"label": "Mouth irritation", "value": "mouth_irritation"}, {"label": "Hiccups", "value": "hiccups"}, {"label": "Upset stomach", "value": "upset_stomach"}, {"label": "Sore throat", "value": "sore_throat"}, {"label": "None of the above", "value": "none"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.nrt_pouch_brand === 'zonnic';"}, {"key": "header_zonnic_details", "type": "content", "input": false, "html": "</br><h4>Zonnic Details</h4>", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.nrt_gum_brand === 'zonnic';"}, {"key": "zonnic_strength", "type": "radio", "input": true, "confirm_label": "Zonnic gum strength:", "label": "Zonnic gum strength:", "values": [{"label": "2 mg", "value": "2mg"}, {"label": "4 mg", "value": "4mg"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.nrt_gum_brand === 'zonnic';"}, {"key": "zonnic_daily_dose", "type": "radio", "confirm_label": "Zonnic gum daily dose:", "input": true, "label": "Approximate number of Zonnic pieces you use per day:", "values": [{"label": "1 - 4", "value": "1_4"}, {"label": "5 - 9", "value": "5_9"}, {"label": "10 - 15", "value": "10_15"}, {"label": "More than 15", "value": "over_15"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.nrt_gum_brand === 'zonnic';"}, {"key": "zonnic_use_duration", "type": "radio", "confirm_label": "Zonnic gum use duration:", "input": true, "label": "How long have you been using Zonnic?", "values": [{"label": "< 1 week", "value": "under_1w"}, {"label": "1 - 4 weeks", "value": "1_4w"}, {"label": "1 - 3 months", "value": "1_3m"}, {"label": "> 3 months", "value": "over_3m"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.nrt_gum_brand === 'zonnic';"}, {"key": "zonnic_side_effects", "type": "selectboxes", "input": true, "label": "Side-effects while using Zonnic gum (select all that apply):", "confirm_label": "Zonnic gum side-effects:", "values": [{"label": "Jaw soreness", "value": "jaw_soreness"}, {"label": "Hiccups", "value": "hiccups"}, {"label": "Mouth irritation", "value": "mouth_irritation"}, {"label": "Upset stomach", "value": "upset_stomach"}, {"label": "Headache", "value": "headache"}], "tableView": true, "customConditional": "show = data.nrt_gum_brand === 'zonnic';"}, {"key": "none_of_the_above_zonnic_side_effects", "type": "checkbox", "input": true, "label": "None of the above", "defaultValue": false, "customClass": "mt-n3", "errors": {"custom": "required, or select a side-effect."}, "validate": {"custom": "valid = !!data.none_of_the_above_zonnic_side_effects || _.some(_.values(data.zonnic_side_effects));"}, "tableView": true, "customConditional": "show = data.nrt_gum_brand === 'zonnic';"}, {"key": "zonnic_side_effects_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following Zonnic side-effects:", "hidden": true, "disabled": true, "tableView": true, "clearOnHide": false, "confirm_label": "Zonnic: no side-effects for", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.zonnic_side_effects, _.negate(_.identity))), _.startCase), ', '), /_/g, ' ');"}, {"key": "header_lozenge_details", "type": "content", "input": false, "html": "</br><h4>Nicotine Lozenge Details</h4>", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.nrt_types.lozenge === true;"}, {"key": "lozenge_strength", "type": "radio", "input": true, "confirm_label": "Lozenge strength:", "label": "Lozenge strength:", "values": [{"label": "2&nbsp;mg", "value": "2mg"}, {"label": "4&nbsp;mg", "value": "4mg"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.nrt_types.lozenge === true;"}, {"key": "lozenge_daily_count", "type": "radio", "confirm_label": "Lozenge daily count:", "input": true, "label": "Average lozenges per day:", "values": [{"label": "1&nbsp;-&nbsp;4", "value": "1_4"}, {"label": "5&nbsp;-&nbsp;9", "value": "5_9"}, {"label": "10&nbsp;-&nbsp;15", "value": "10_15"}, {"label": "More than&nbsp;15", "value": "over_15"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.nrt_types.lozenge === true;"}, {"key": "lozenge_use_duration", "type": "radio", "confirm_label": "Lozenge use duration:", "input": true, "label": "How long have you used lozenges?", "values": [{"label": "< 1&nbsp;week", "value": "under_1w"}, {"label": "1&nbsp;-&nbsp;4&nbsp;weeks", "value": "1_4w"}, {"label": "1&nbsp;-&nbsp;3&nbsp;months", "value": "1_3m"}, {"label": "> 3&nbsp;months", "value": "over_3m"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.nrt_types.lozenge === true;"}, {"key": "lozenge_side_effects", "type": "selectboxes", "input": true, "label": "Side-effects while using nicotine lozenges (select all that apply):", "confirm_label": "Lozenge side-effects:", "values": [{"label": "Mouth / throat irritation", "value": "irritation"}, {"label": "Hiccups", "value": "hiccups"}, {"label": "<PERSON><PERSON>", "value": "heartburn"}, {"label": "<PERSON><PERSON><PERSON>", "value": "nausea"}], "tableView": true, "customConditional": "show = data.nrt_types.lozenge === true;"}, {"key": "none_of_the_above_lozenge_side_effects", "type": "checkbox", "input": true, "label": "None of the above", "defaultValue": false, "customClass": "mt-n3", "errors": {"custom": "required, or select a side-effect."}, "validate": {"custom": "valid = !!data.none_of_the_above_lozenge_side_effects || _.some(_.values(data.lozenge_side_effects));"}, "tableView": true, "customConditional": "show = data.nrt_types.lozenge === true;"}, {"key": "lozenge_side_effects_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following lozenge side-effects:", "hidden": true, "disabled": true, "tableView": true, "clearOnHide": false, "confirm_label": "Lozenge: no side-effects for", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.lozenge_side_effects, _.negate(_.identity))), _.startCase), ', '), /_/g, ' ');"}, {"key": "header_patch_details", "type": "content", "input": false, "html": "</br><h4><PERSON><PERSON> Patch Details</h4>", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.nrt_types.patch === true;"}, {"key": "patch_strength", "type": "radio", "input": true, "confirm_label": "Patch strength:", "label": "Patch strength worn in 24 hours:", "values": [{"label": "7&nbsp;mg", "value": "7mg"}, {"label": "14&nbsp;mg", "value": "14mg"}, {"label": "21&nbsp;mg", "value": "21mg"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.nrt_types.patch === true;"}, {"key": "patch_use_duration", "type": "radio", "input": true, "confirm_label": "Patch use duration:", "label": "How long have you been using patches?", "values": [{"label": "< 1&nbsp;week", "value": "under_1w"}, {"label": "1&nbsp;-&nbsp;4&nbsp;weeks", "value": "1_4w"}, {"label": "1&nbsp;-&nbsp;3&nbsp;months", "value": "1_3m"}, {"label": "> 3&nbsp;months", "value": "over_3m"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.nrt_types.patch === true;"}, {"key": "patch_side_effects", "type": "selectboxes", "input": true, "confirm_label": "Patch side effects:", "label": "Side-effects while using patches:", "values": [{"label": "Skin irritation", "value": "skin_irritation"}, {"label": "Vivid dreams/insomnia", "value": "insomnia"}, {"label": "<PERSON><PERSON><PERSON>", "value": "nausea"}, {"label": "Headache", "value": "headache"}, {"label": "None of the above", "value": "none"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.nrt_types.patch === true;"}, {"key": "header_inhaler_details", "type": "content", "input": false, "html": "</br><h4><PERSON><PERSON> Inhaler Details</h4>", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.nrt_types.inhaler === true;"}, {"key": "inhaler_cartridges_per_day", "type": "radio", "confirm_label": "Inhaler cartridges per day:", "input": true, "label": "Approximate cartridges used per day:", "values": [{"label": "1&nbsp;-&nbsp;4", "value": "1_4"}, {"label": "5&nbsp;-&nbsp;9", "value": "5_9"}, {"label": "10&nbsp;-&nbsp;12", "value": "10_12"}, {"label": "More than&nbsp;12", "value": "over_12"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.nrt_types.inhaler === true;"}, {"key": "inhaler_use_duration", "type": "radio", "confirm_label": "Inhaler use duration:", "input": true, "label": "How long have you been using the inhaler?", "values": [{"label": "< 1&nbsp;week", "value": "under_1w"}, {"label": "1&nbsp;-&nbsp;4&nbsp;weeks", "value": "1_4w"}, {"label": "1&nbsp;-&nbsp;3&nbsp;months", "value": "1_3m"}, {"label": "> 3&nbsp;months", "value": "over_3m"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.nrt_types.inhaler === true;"}, {"key": "inhaler_side_effects", "type": "selectboxes", "input": true, "label": "Side-effects while using the nicotine inhaler (select all that apply):", "confirm_label": "Inhaler side-effects:", "values": [{"label": "Mouth / throat irritation", "value": "irritation"}, {"label": "<PERSON><PERSON>", "value": "cough"}, {"label": "Upset stomach", "value": "nausea"}], "tableView": true, "customConditional": "show = data.nrt_types.inhaler === true;"}, {"key": "none_of_the_above_inhaler_side_effects", "type": "checkbox", "input": true, "label": "None of the above", "defaultValue": false, "customClass": "mt-n3", "errors": {"custom": "required, or select a side-effect."}, "validate": {"custom": "valid = !!data.none_of_the_above_inhaler_side_effects || _.some(_.values(data.inhaler_side_effects));"}, "tableView": true, "customConditional": "show = data.nrt_types.inhaler === true;"}, {"key": "inhaler_side_effects_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following inhaler side-effects:", "hidden": true, "disabled": true, "tableView": true, "clearOnHide": false, "confirm_label": "Inhaler: no side-effects for", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.inhaler_side_effects, _.negate(_.identity))), _.startCase), ', '), /_/g, ' ');"}, {"key": "header_nasal_spray_details", "type": "content", "input": false, "html": "</br><h4><PERSON><PERSON> Spray Details</h4>", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.nrt_types.nasal_spray === true;"}, {"key": "spray_doses_per_day", "type": "radio", "input": true, "confirm_label": "Spray doses per day:", "label": "Approximate sprays per day (both nostrils combined):", "values": [{"label": "1&nbsp;-&nbsp;16", "value": "1_16"}, {"label": "17&nbsp;-&nbsp;32", "value": "17_32"}, {"label": "33&nbsp;-&nbsp;40", "value": "33_40"}, {"label": "More than&nbsp;40", "value": "over_40"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.nrt_types.nasal_spray === true;"}, {"key": "spray_use_duration", "type": "radio", "input": true, "confirm_label": "Spray use duration:", "label": "How long have you been using the nasal spray?", "values": [{"label": "< 1&nbsp;week", "value": "under_1w"}, {"label": "1&nbsp;-&nbsp;4&nbsp;weeks", "value": "1_4w"}, {"label": "1&nbsp;-&nbsp;3&nbsp;months", "value": "1_3m"}, {"label": "> 3&nbsp;months", "value": "over_3m"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.nrt_types.nasal_spray === true;"}, {"key": "spray_side_effects", "type": "selectboxes", "input": true, "label": "Side-effects while using the nicotine nasal spray (select all that apply):", "confirm_label": "Spray side-effects:", "values": [{"label": "Nasal irritation", "value": "irritation"}, {"label": "Watery eyes", "value": "watery_eyes"}, {"label": "Sneezing", "value": "sneezing"}, {"label": "<PERSON><PERSON>", "value": "cough"}], "tableView": true, "customConditional": "show = data.nrt_types.nasal_spray === true;"}, {"key": "none_of_the_above_spray_side_effects", "type": "checkbox", "input": true, "label": "None of the above", "defaultValue": false, "customClass": "mt-n3", "errors": {"custom": "required, or select a side-effect."}, "validate": {"custom": "valid = !!data.none_of_the_above_spray_side_effects || _.some(_.values(data.spray_side_effects));"}, "tableView": true, "customConditional": "show = data.nrt_types.nasal_spray === true;"}, {"key": "spray_side_effects_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following spray side-effects:", "hidden": true, "disabled": true, "tableView": true, "clearOnHide": false, "confirm_label": "Spray: no side-effects for", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.spray_side_effects, _.negate(_.identity))), _.startCase), ', '), /_/g, ' ');"}, {"key": "header_medical_safety", "html": "</br><h2>Other Conditions</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "med_contraindications", "type": "selectboxes", "confirm_label": "Medical contraindications:", "input": true, "label": "Have you been diagnosed with any of the following conditions at present time, or in the past? (Select all that apply)", "values": [{"label": "Seizure disorders", "value": "seizure_disordersn"}, {"label": "Eating disorder (e.g., bulimia, anorexia)", "value": "eating_disorders"}, {"label": "Bipolar disorder", "value": "bipolar_disorder"}, {"label": "Psycho<PERSON>", "value": "psychosis"}, {"label": "Depression", "value": "depression"}, {"label": "Anxiety", "value": "anxiety"}, {"label": "None of the above", "value": "none"}], "validate": {"required": true}, "tableView": true}, {"key": "zyban_contraindications_symptoms_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following Zyban contraindication conditions:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "confirm_label": "Zyban contraindications not present:", "spellcheck": false, "clearOnHide": false, "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.omitBy(data.zyban_contraindications, _.identity)), _.capitalize), ', '), /_/g, ' ');"}, {"key": "header_lifestyle_factors", "html": "</br><h2>Lifestyle Factors</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "drink_alcohol", "type": "radio", "input": true, "label": "Do you drink alcohol?", "confirm_label": "Alcohol consumption:", "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}], "validate": {"required": true}, "tableView": true}, {"key": "type_of_alcohol", "type": "selectboxes", "confirm_label": "Type of alcohol:", "input": true, "label": "What type of alcohol do you usually drink? (Select all that apply)", "values": [{"label": "Beer", "value": "beer"}, {"label": "Wine", "value": "wine"}, {"label": "Spirits", "value": "spirits"}, {"label": "Other", "value": "other"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.drink_alcohol === 'yes';", "optionsLabelPosition": "right"}, {"key": "header_beer_details", "html": "</br><h4>Beer Details</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.drink_alcohol === 'yes' && data.type_of_alcohol.beer;"}, {"key": "beer_format", "type": "radio", "confirm_label": "Beer format:", "input": true, "label": "What is the typical size of the bottle/can you drink?", "values": [{"label": "473ml", "value": "473ml"}, {"label": "355ml", "value": "355ml"}, {"label": "500ml", "value": "500ml"}, {"label": "650ml", "value": "650ml"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.drink_alcohol === 'yes' && data.type_of_alcohol.beer;"}, {"key": "beer_abv_content", "type": "radio", "confirm_label": "Beer ABV content:", "input": true, "label": "What is the typical Alcohol By Volume (ABV) content of the beer you drink?", "values": [{"label": "Non-alcoholic (<0.5%)", "value": "non_alcoholic"}, {"label": "Low (0.5% - 3.5%)", "value": "low"}, {"label": "Standard (3.5% - 5.5%)", "value": "standard"}, {"label": "High (>5.5%)", "value": "high"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.drink_alcohol === 'yes' && data.type_of_alcohol.beer;"}, {"key": "number_of_beers_per_day", "type": "radio", "confirm_label": "Number of beers per day:", "input": true, "label": "How many beers do you typically drink in a day?", "values": [{"label": "None", "value": "none"}, {"label": "Less than 1 beer", "value": "less_than_1_beer"}, {"label": "1 beer", "value": "1_beer"}, {"label": "2 beers", "value": "2_beers"}, {"label": "3-4 beers", "value": "3_4_beers"}, {"label": "5 or more beers", "value": "5_more_beers"}], "tooltip": "Please calculate the average number of beers you drink per day. For example, if you drink 24 beers a week, this would average out to approximately 3 beers per day.", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.drink_alcohol === 'yes' && data.type_of_alcohol.beer;"}, {"key": "number_of_beers_per_week", "type": "radio", "confirm_label": "Number of beers per week:", "input": true, "label": "How many beers do you typically drink in a week?", "values": [{"label": "None", "value": "none"}, {"label": "1 - 6 beers", "value": "1_6_beers"}, {"label": "7 - 13 beers", "value": "7_13_beers"}, {"label": "14 - 20 beers", "value": "14_20_beers"}, {"label": "21 - 27 beers", "value": "21_27_beers"}, {"label": "28 or more beers", "value": "28_more_beers"}], "tooltip": "Add up all the beers you drink across a typical week (e.g., 24 beers/week ≈ 3-4 per day).", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.drink_alcohol === 'yes' && data.type_of_alcohol.beer;"}, {"key": "header_wine_details", "html": "</br><h4>Wine Details</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.drink_alcohol === 'yes' && data.type_of_alcohol.wine;"}, {"key": "wine_bottle_size", "type": "radio", "confirm_label": "Wine bottle size:", "input": true, "label": "What is the typical size of the wine bottle you drink?", "values": [{"label": "375ml (Half bottle)", "value": "375ml"}, {"label": "750ml (Standard bottle)", "value": "750ml"}, {"label": "1 Liter", "value": "1liter"}, {"label": "1.5 Liters (Magnum)", "value": "1.5liters"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.drink_alcohol === 'yes' && data.type_of_alcohol.wine;"}, {"key": "wine_abv_content", "type": "radio", "confirm_label": "Wine ABV content:", "input": true, "label": "What is the typical Alcohol By Volume (ABV) content of the wine you drink?", "values": [{"label": "Low (<8%)", "value": "low"}, {"label": "Medium (8% - 14%)", "value": "medium"}, {"label": "High (>14%)", "value": "high"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.drink_alcohol === 'yes' && data.type_of_alcohol.wine;"}, {"key": "wine_bottle_duration", "type": "radio", "input": true, "confirm_label": "Wine bottle duration:", "label": "If you're drinking wine by yourself, how long does a bottle typically last?", "values": [{"label": "Less than a day", "value": "less_than_a_day"}, {"label": "1 day", "value": "1_day"}, {"label": "2 days", "value": "2_days"}, {"label": "3 days", "value": "3_days"}, {"label": "4 days", "value": "4_days"}, {"label": "5 days", "value": "5_days"}, {"label": "More than 5 days", "value": "more_than_5_days"}, {"label": "I rarely finish a bottle", "value": "rarely_finish"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.drink_alcohol === 'yes' && data.type_of_alcohol.wine;"}, {"key": "header_spirits_details", "html": "</br><h4>Spirits Details</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.drink_alcohol === 'yes' && data.type_of_alcohol.spirits;"}, {"key": "spirits_bottle_size", "type": "radio", "input": true, "label": "What is the typical size of the spirits bottle you drink?", "confirm_label": "Spirits bottle size:", "values": [{"label": "0.375 Liters (12.7 oz)", "value": "375ml_12.7oz"}, {"label": "0.75 Liters (25.4 oz)", "value": "750ml_25.4oz"}, {"label": "1 Liter (33.8 oz)", "value": "1liter_33.8oz"}, {"label": "1.14 Liters (38.5 oz)", "value": "1.14liter_38.5oz"}, {"label": "1.75 Liters (59.2 oz)", "value": "1.75liters_59.2oz"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.drink_alcohol === 'yes' && data.type_of_alcohol.spirits;"}, {"key": "spirits_abv_content", "type": "radio", "input": true, "label": "What is the typical Alcohol By Volume (ABV) content of the spirits you drink?", "confirm_label": "Spirits ABV content:", "values": [{"label": "Low (<20%)", "value": "low"}, {"label": "Medium (20% - 40%)", "value": "medium"}, {"label": "High (>40%)", "value": "high"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.drink_alcohol === 'yes' && data.type_of_alcohol.spirits;"}, {"key": "spirits_bottle_duration", "type": "radio", "input": true, "confirm_label": "Spirits bottle duration:", "label": "If you're drinking spirits by yourself, how long does a bottle typically last?", "values": [{"label": "Less than a day", "value": "less_than_a_day"}, {"label": "1-2 days", "value": "1_2_days"}, {"label": "3-4 days", "value": "3_4_days"}, {"label": "5-9 days", "value": "5_9_days"}, {"label": "10-14 days", "value": "10_14_days"}, {"label": "15-19 days", "value": "15_19_days"}, {"label": "20-24 days", "value": "20_24_days"}, {"label": "25-29 days", "value": "25_29_days"}, {"label": "More than 30 days", "value": "more_than_30_days"}, {"label": "I rarely finish a bottle", "value": "rarely_finish"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.drink_alcohol === 'yes' && data.type_of_alcohol.spirits;"}]}