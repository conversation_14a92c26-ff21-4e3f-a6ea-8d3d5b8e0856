{"components": [{"key": "heading_cosmetic_dermatology", "html": "<h1><strong>Cosmetic Dermatology Consult</strong></h1><p>Please complete the following questionnaire to help us understand your skin concerns, treatment history, and goals.</p>", "type": "content", "input": false, "label": "Content"}, {"key": "skin_care_goals_header", "html": "<h2>Skin Care Goals</h2>", "type": "content", "input": false, "label": "Content"}, {"key": "treatment_goals", "type": "selectboxes", "input": true, "label": "What are your primary treatment goals for today's consultation? (Select all that apply)", "values": [{"label": "Reduce acne", "value": "reduce_acne"}, {"label": "Minimize fine lines and wrinkles", "value": "minimize_wrinkles"}, {"label": "Improve skin texture and smoothness", "value": "improve_texture"}, {"label": "Diminish hyperpigmentation or dark spots", "value": "diminish_hyperpigmentation"}, {"label": "Address melasma", "value": "address_melasma"}, {"label": "Improve visible blood vessels, bumps, or flushing", "value": "improve_rosacea_changes"}, {"label": "Reduce Ke<PERSON>", "value": "keratosis_pilaris"}, {"label": "Minimize enlarged pores", "value": "minimize_pores"}, {"label": "Enhance skin tone and radiance", "value": "enhance_tone"}, {"label": "Other", "value": "other"}, {"label": "None of the above", "value": "none_of_the_above"}], "tableView": true, "optionsLabelPosition": "right", "validate": {"custom": "valid = !data.none_of_the_above || _.some(_.values(data.treatment_goals));"}}, {"key": "none_of_the_above_treatment_goals", "type": "checkbox", "input": true, "label": "None of the above", "tableView": true, "customClass": "mt-n3", "defaultValue": false, "validate": {"custom": "valid = data.none_of_the_above_treatment_goals || _.some(_.values(data.treatment_goals));"}}, {"key": "treatment_goals_other", "type": "textfield", "input": true, "label": "Please specify your other treatment goal", "placeholder": "E.g., address specific scars, improve skin hydration, reduce dark circles, etc.", "tableView": true, "customConditional": "show = _.get(data, 'treatment_goals.other') && !data.none_of_the_above_treatment_goals;"}, {"key": "prior_diagnosis_header", "html": "<h2>Previous Skin Conditions</h2>", "type": "content", "input": false, "label": "Content"}, {"key": "diagnosed_conditions", "type": "selectboxes", "input": true, "label": "Have you been diagnosed with any of the following skin conditions? (Select all that apply)", "values": [{"label": "Acne", "value": "acne"}, {"label": "<PERSON><PERSON><PERSON>", "value": "melasma"}, {"label": "<PERSON><PERSON>", "value": "rosacea"}, {"label": "Eczema or Perioral Dermatitis", "value": "eczema"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "psoriasis"}, {"label": "Other", "value": "other"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "acne_history_heading", "html": "<h2>Acne History</h2><p>Please provide additional details about your acne to help us better understand your condition and recommend treatment options.</p>", "type": "content", "input": false, "label": "Content", "customConditional": "show = _.get(data, 'reason_for_interest_in_topical_retinoids.treat_acne_prevent_breakouts');"}, {"key": "acne_age_of_diagnosis", "type": "textfield", "input": true, "label": "What was your age at the time of your acne diagnosis?", "validate": {"required": true, "custom": "valid = input && !isNaN(input) && input > 0;", "customMessage": "Please enter a valid age."}, "tableView": true, "customConditional": "show = _.get(data, 'reason_for_interest_in_topical_retinoids.treat_acne_prevent_breakouts');"}, {"key": "acne_severity", "type": "radio", "input": true, "label": "Which of the following best describes your acne?", "values": [{"label": "Occasional breakouts with small, red pimples or blackheads/whiteheads", "value": "occasional_breakouts"}, {"label": "Frequent breakouts with red pimples, inflamed areas, and some scarring", "value": "frequent_breakouts_with_inflammation"}, {"label": "Severe and persistent breakouts with cysts, large painful bumps, and significant scarring", "value": "severe_persistent_breakouts"}, {"label": "I'm not sure how to describe it", "value": "unsure_severity"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = _.get(data, 'reason_for_interest_in_topical_retinoids.treat_acne_prevent_breakouts');"}, {"key": "acne_triggers", "type": "selectboxes", "input": true, "label": "What factors seem to trigger your acne? (Select all that apply)", "values": [{"label": "Hormonal changes", "value": "hormonal_changes"}, {"label": "Stress", "value": "stress"}, {"label": "Dietary choices", "value": "dietary_choices"}, {"label": "Skin care products", "value": "skin_care_products"}, {"label": "Other", "value": "other"}], "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = _.get(data, 'reason_for_interest_in_topical_retinoids.treat_acne_prevent_breakouts');"}, {"key": "acne_triggers_other", "type": "textfield", "input": true, "label": "Please specify other triggers", "placeholder": "E.g., specific food items, environmental factors, etc.", "tableView": true, "customConditional": "show = _.get(data, 'acne_triggers.other');"}, {"key": "acne_treatments", "type": "selectboxes", "input": true, "label": "What treatments have you tried for your acne? (Select all that apply)", "values": [{"label": "Over-the-counter creams", "value": "otc_creams"}, {"label": "Prescription creams", "value": "prescription_creams"}, {"label": "Oral antibiotics", "value": "oral_antibiotics"}, {"label": "Hormonal therapy (e.g., birth control pills)", "value": "hormonal_therapy"}, {"label": "Blood pressure medication (e.g., spironolactone)", "value": "blood_pressure_medication"}, {"label": "Other", "value": "other"}], "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = _.get(data, 'reason_for_interest_in_topical_retinoids.treat_acne_prevent_breakouts');"}, {"key": "acne_treatments_other", "type": "textfield", "input": true, "label": "Please specify other treatments", "placeholder": "E.g., isotretinoin, laser treatments, etc.", "tableView": true, "customConditional": "show = _.get(data, 'acne_treatments.other');"}, {"key": "melasma_history_heading", "html": "<h2>Melasma History</h2><p>Please provide additional details about your melasma to help us understand your condition and recommend suitable treatments.</p>", "type": "content", "input": false, "label": "Content", "customConditional": "show = _.get(data, 'diagnosed_conditions.melasma');"}, {"key": "melasma_age_of_diagnosis", "type": "textfield", "input": true, "label": "What was your age at the time of your melasma diagnosis?", "validate": {"required": true, "custom": "valid = input && !isNaN(input) && input > 0;", "customMessage": "Please enter a valid age."}, "tableView": true, "customConditional": "show = _.get(data, 'diagnosed_conditions.melasma');", "description": "This helps us understand the progression and possible triggers for your melasma."}, {"key": "melasma_triggers", "type": "selectboxes", "input": true, "label": "What factors seem to trigger or worsen your melasma? (Select all that apply)", "values": [{"label": "Sun exposure", "value": "sun_exposure"}, {"label": "Hormonal changes (e.g., pregnancy, birth control)", "value": "hormonal_changes"}, {"label": "Stress", "value": "stress"}, {"label": "Certain medications", "value": "medications"}, {"label": "Other", "value": "other"}], "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = _.get(data, 'diagnosed_conditions.melasma');"}, {"key": "melasma_triggers_other", "type": "textfield", "input": true, "label": "Please specify other triggers", "placeholder": "E.g., specific medications, lifestyle factors, etc.", "tableView": true, "customConditional": "show = _.get(data, 'melasma_triggers.other');"}, {"key": "melasma_treatments", "type": "selectboxes", "input": true, "label": "What treatments have you tried for your melasma? (Select all that apply)", "values": [{"label": "Topical creams (e.g., hydroquinone, tretinoin)", "value": "topical_creams"}, {"label": "Chemical peels", "value": "chemical_peels"}, {"label": "Laser therapy", "value": "laser_therapy"}, {"label": "Sun protection (e.g., sunscreen, hats)", "value": "sun_protection"}, {"label": "Other", "value": "other"}], "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = _.get(data, 'diagnosed_conditions.melasma');"}, {"key": "melasma_treatments_other", "type": "textfield", "input": true, "label": "Please specify other treatments", "placeholder": "E.g., oral medications, home remedies, etc.", "tableView": true, "customConditional": "show = _.get(data, 'melasma_treatments.other');"}, {"key": "rosacea_history_heading", "html": "<h2>Rosacea History</h2><p>Please provide additional details about your rosacea to help us understand your condition and recommend effective treatments.</p>", "type": "content", "input": false, "label": "Content", "customConditional": "show = _.get(data, 'diagnosed_conditions.rosacea');"}, {"key": "rosacea_age_of_diagnosis", "type": "textfield", "input": true, "label": "What was your age at the time of your rosacea diagnosis?", "validate": {"required": true, "custom": "valid = input && !isNaN(input) && input > 0;", "customMessage": "Please enter a valid age."}, "tableView": true, "customConditional": "show = _.get(data, 'diagnosed_conditions.rosacea');"}, {"key": "rosacea_triggers", "type": "selectboxes", "input": true, "label": "What factors seem to trigger or worsen your rosacea? (Select all that apply)", "values": [{"label": "Sun exposure", "value": "sun_exposure"}, {"label": "Stress", "value": "stress"}, {"label": "Hot or spicy foods", "value": "hot_spicy_foods"}, {"label": "Alcohol consumption", "value": "alcohol"}, {"label": "Certain skin care products", "value": "skin_care_products"}, {"label": "Other", "value": "other"}], "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = _.get(data, 'diagnosed_conditions.rosacea');"}, {"key": "rosacea_triggers_other", "type": "textfield", "input": true, "label": "Please specify other triggers", "placeholder": "E.g., weather changes, specific activities, etc.", "tableView": true, "customConditional": "show = _.get(data, 'rosacea_triggers.other');"}, {"key": "rosacea_symptoms", "type": "selectboxes", "input": true, "label": "What symptoms do you experience with rosacea? (Select all that apply)", "values": [{"label": "Facial redness or flushing", "value": "redness_flushing"}, {"label": "Visible blood vessels", "value": "visible_blood_vessels"}, {"label": "Bumps or pimples", "value": "bumps_pimples"}, {"label": "Eye irritation or dryness", "value": "eye_irritation"}, {"label": "Thickened skin", "value": "thickened_skin"}, {"label": "Other", "value": "other"}], "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = _.get(data, 'diagnosed_conditions.rosacea');"}, {"key": "rosacea_symptoms_other", "type": "textfield", "input": true, "label": "Please specify other symptoms", "placeholder": "E.g., burning sensations, swelling, etc.", "tableView": true, "customConditional": "show = _.get(data, 'rosacea_symptoms.other');"}, {"key": "rosacea_treatments", "type": "selectboxes", "input": true, "label": "What treatments have you tried for your rosacea? (Select all that apply)", "values": [{"label": "Topical creams (e.g., metronidazole, azelaic acid)", "value": "topical_creams"}, {"label": "Oral antibiotics", "value": "oral_antibiotics"}, {"label": "Laser or light therapy", "value": "laser_light_therapy"}, {"label": "Skin care routines (e.g., gentle cleansers)", "value": "skin_care_routines"}, {"label": "Other", "value": "other"}], "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = _.get(data, 'diagnosed_conditions.rosacea');"}, {"key": "rosacea_treatments_other", "type": "textfield", "input": true, "label": "Please specify other treatments", "placeholder": "E.g., home remedies, specific prescriptions, etc.", "tableView": true, "customConditional": "show = _.get(data, 'rosacea_treatments.other');"}, {"key": "eczema_history_heading", "html": "<h2>Eczema or Perioral Dermatitis History</h2><p>Please provide additional details about your eczema or perioral dermatitis to help us understand your condition and recommend effective treatments.</p>", "type": "content", "input": false, "label": "Content", "customConditional": "show = _.get(data, 'diagnosed_conditions.eczema');"}, {"key": "eczema_age_of_diagnosis", "type": "textfield", "input": true, "label": "What was your age at the time of your eczema or perioral dermatitis diagnosis?", "validate": {"required": true, "custom": "valid = input && !isNaN(input) && input > 0;", "customMessage": "Please enter a valid age."}, "tableView": true, "customConditional": "show = _.get(data, 'diagnosed_conditions.eczema');", "description": "Providing this information helps us understand the long-term effects of your condition."}, {"key": "eczema_triggers", "type": "selectboxes", "input": true, "label": "What factors seem to trigger or worsen your eczema or perioral dermatitis? (Select all that apply)", "values": [{"label": "Allergens (e.g., pollen, dust)", "value": "allergens"}, {"label": "Irritants (e.g., soaps, detergents)", "value": "irritants"}, {"label": "Stress", "value": "stress"}, {"label": "Weather changes (e.g., dry or cold air)", "value": "weather_changes"}, {"label": "Certain foods", "value": "foods"}, {"label": "Other", "value": "other"}], "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = _.get(data, 'diagnosed_conditions.eczema');"}, {"key": "eczema_triggers_other", "type": "textfield", "input": true, "label": "Please specify other triggers", "placeholder": "E.g., specific allergens, medications, etc.", "tableView": true, "customConditional": "show = _.get(data, 'eczema_triggers.other');"}, {"key": "eczema_symptoms", "type": "selectboxes", "input": true, "label": "What symptoms do you experience with eczema or perioral dermatitis? (Select all that apply)", "values": [{"label": "Dry, scaly skin", "value": "dry_scaly_skin"}, {"label": "Itching or irritation", "value": "itching_irritation"}, {"label": "Red or inflamed patches", "value": "red_inflamed_patches"}, {"label": "Cracking or weeping skin", "value": "cracking_weeping_skin"}, {"label": "Thickened skin", "value": "thickened_skin"}, {"label": "Other", "value": "other"}], "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = _.get(data, 'diagnosed_conditions.eczema');"}, {"key": "eczema_symptoms_other", "type": "textfield", "input": true, "label": "Please specify other symptoms", "placeholder": "E.g., burning sensations, specific skin changes, etc.", "tableView": true, "customConditional": "show = _.get(data, 'eczema_symptoms.other');"}, {"key": "eczema_treatments", "type": "selectboxes", "input": true, "label": "What treatments have you tried for your eczema or perioral dermatitis? (Select all that apply)", "values": [{"label": "Topical steroids (e.g., hydrocortisone)", "value": "topical_steroids"}, {"label": "Moisturizers or emollients", "value": "moisturizers"}, {"label": "Prescription creams (e.g., calcineurin inhibitors)", "value": "prescription_creams"}, {"label": "Oral medications (e.g., antihistamines)", "value": "oral_medications"}, {"label": "Other", "value": "other"}], "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = _.get(data, 'diagnosed_conditions.eczema');"}, {"key": "eczema_treatments_other", "type": "textfield", "input": true, "label": "Please specify other treatments", "placeholder": "E.g., light therapy, home remedies, etc.", "tableView": true, "customConditional": "show = _.get(data, 'eczema_treatments.other');"}, {"key": "psoriasis_history_heading", "html": "<h2>Psoriasis History</h2><p>Please provide additional details about your psoriasis to help us understand your condition and recommend effective treatments.</p>", "type": "content", "input": false, "label": "Content", "customConditional": "show = _.get(data, 'diagnosed_conditions.psoriasis');"}, {"key": "psoriasis_age_of_diagnosis", "type": "textfield", "input": true, "label": "What was your age at the time of your psoriasis diagnosis?", "validate": {"required": true, "custom": "valid = input && !isNaN(input) && input > 0;", "customMessage": "Please enter a valid age."}, "tableView": true, "customConditional": "show = _.get(data, 'diagnosed_conditions.psoriasis');", "description": "This helps us evaluate the progression and management of your psoriasis."}, {"key": "psoriasis_types", "type": "selectboxes", "input": true, "label": "What type(s) of psoriasis have you been diagnosed with? (Select all that apply)", "values": [{"label": "<PERSON><PERSON><PERSON> psoriasis", "value": "plaque_psoriasis"}, {"label": "Guttate psoriasis", "value": "guttate_psoriasis"}, {"label": "Inverse psoriasis", "value": "inverse_psoriasis"}, {"label": "Pustular psoriasis", "value": "pustular_psoriasis"}, {"label": "Erythrodermic psoriasis", "value": "erythrodermic_psoriasis"}, {"label": "Not sure", "value": "not_sure"}], "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = _.get(data, 'diagnosed_conditions.psoriasis');"}, {"key": "psoriasis_triggers", "type": "selectboxes", "input": true, "label": "What factors seem to trigger or worsen your psoriasis? (Select all that apply)", "values": [{"label": "Stress", "value": "stress"}, {"label": "Infections (e.g., strep throat)", "value": "infections"}, {"label": "Cold or dry weather", "value": "cold_dry_weather"}, {"label": "Skin injuries (e.g., cuts, scrapes)", "value": "skin_injuries"}, {"label": "Certain medications", "value": "medications"}, {"label": "Other", "value": "other"}], "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = _.get(data, 'diagnosed_conditions.psoriasis');"}, {"key": "psoriasis_triggers_other", "type": "textfield", "input": true, "label": "Please specify other triggers", "placeholder": "E.g., specific medications, environmental factors, etc.", "tableView": true, "customConditional": "show = _.get(data, 'psoriasis_triggers.other');"}, {"key": "psoriasis_symptoms", "type": "selectboxes", "input": true, "label": "What symptoms do you experience with psoriasis? (Select all that apply)", "values": [{"label": "Red patches of skin covered with thick scales", "value": "red_patches_thick_scales"}, {"label": "Itching, burning, or soreness", "value": "itching_burning_soreness"}, {"label": "Dry, cracked skin that may bleed", "value": "dry_cracked_skin"}, {"label": "Thickened or ridged nails", "value": "thickened_nails"}, {"label": "Joint pain or stiffness", "value": "joint_pain_stiffness"}, {"label": "Other", "value": "other"}], "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = _.get(data, 'diagnosed_conditions.psoriasis');"}, {"key": "psoriasis_symptoms_other", "type": "textfield", "input": true, "label": "Please specify other symptoms", "placeholder": "E.g., additional joint symptoms, specific skin changes, etc.", "tableView": true, "customConditional": "show = _.get(data, 'psoriasis_symptoms.other');"}, {"key": "psoriasis_treatments", "type": "selectboxes", "input": true, "label": "What treatments have you tried for your psoriasis? (Select all that apply)", "values": [{"label": "Topical treatments (e.g., corticosteroids)", "value": "topical_treatments"}, {"label": "Phototherapy (light therapy)", "value": "phototherapy"}, {"label": "Oral medications (e.g., methotrexate)", "value": "oral_medications"}, {"label": "Biologic injections", "value": "biologic_injections"}, {"label": "Other", "value": "other"}], "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = _.get(data, 'diagnosed_conditions.psoriasis');"}, {"key": "psoriasis_treatments_other", "type": "textfield", "input": true, "label": "Please specify other treatments", "placeholder": "E.g., alternative therapies, home remedies, etc.", "tableView": true, "customConditional": "show = _.get(data, 'psoriasis_treatments.other');"}, {"key": "keloids_and_healing_header", "html": "<h2>Keloids and Healing</h2><p><strong>What are keloids?</strong> Keloids are thick, raised scars that grow larger than the original injury. They can form after surgeries, tattoos, piercings, or even minor cuts and acne. Keloids may feel itchy or uncomfortable and can sometimes affect self-confidence. Answering these questions will help us understand your skin's healing process.</p>", "type": "content", "input": false, "label": "Content"}, {"key": "abnormal_scarring", "type": "radio", "input": true, "label": "Have you ever had surgery, tattoos, or piercings that caused unusual or raised scarring?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No, I haven't noticed", "value": "no_noticed"}, {"label": "No, I haven't had surgery, tattoos, or piercings to notice", "value": "no_none"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "keloids_after_injuries", "type": "radio", "input": true, "label": "Have you experienced thickened or raised scars (keloids) after minor injuries such as cuts or burns?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "scars_itch_hurt_grow", "type": "radio", "input": true, "label": "Do your scars feel itchy, painful, or grow larger over time?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "family_history_keloids", "type": "radio", "input": true, "label": "Has anyone in your family had thick, raised scars (like keloids) or unusual scarring after injuries?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = _.get(data, 'abnormal_scarring');"}, {"key": "treatment_for_keloids", "type": "selectboxes", "input": true, "label": "Have you sought medical treatment for keloids or abnormal scars in the past?", "values": [{"label": "Silicone gels", "value": "silicone_gels"}, {"label": "Steroid injections", "value": "steroid_injections"}, {"label": "Laser therapy", "value": "laser_therapy"}, {"label": "None of the above", "value": "none"}], "tableView": true, "optionsLabelPosition": "right"}, {"key": "scar_colour_change", "type": "radio", "input": true, "label": "Do you have scars that are darker or lighter than your normal skin tone?", "values": [{"label": "Darker", "value": "darker"}, {"label": "Lighter", "value": "lighter"}, {"label": "No noticeable change", "value": "no_change"}], "tableView": true, "customConditional": "show = _.some(_.values(data.treatment_for_keloids));", "optionsLabelPosition": "right"}, {"key": "skin_type_header", "html": "<h2>Skin Type</h2>", "type": "content", "input": false, "label": "Content"}, {"key": "skin_tone", "type": "radio", "input": true, "label": "What best describes your natural skin tone?", "values": [{"label": "Very fair or pale", "value": "very_fair"}, {"label": "Fair", "value": "fair"}, {"label": "Medium or olive", "value": "medium"}, {"label": "<PERSON>", "value": "brown"}, {"label": "Dark brown to black", "value": "dark_brown"}], "tableView": true, "validate": {"required": true}, "optionsLabelPosition": "right"}, {"key": "sun_reaction", "type": "radio", "input": true, "label": "How does your skin react to sun exposure?", "values": [{"label": "Always burns, never tans", "value": "always_burns_never_tans"}, {"label": "Usually burns, tans minimally", "value": "usually_burns_tans_minimally"}, {"label": "Sometimes burns, tans gradually", "value": "sometimes_burns_tans_gradually"}, {"label": "Rarely burns, tans easily", "value": "rarely_burns_tans_easily"}, {"label": "Very rarely burns, tans very easily", "value": "very_rarely_burns_tans_very_easily"}, {"label": "Never burns, tans very easily", "value": "never_burns_tans_very_easily"}], "tableView": true, "validate": {"required": true}, "optionsLabelPosition": "right"}, {"key": "fitz<PERSON>_skin_type", "type": "textfield", "input": true, "label": "<PERSON><PERSON>patrick Skin Type", "hidden": true, "disabled": true, "defaultValue": "", "calculateValue": "value = (data.skin_tone === 'very_fair' && data.sun_reaction === 'always_burns_never_tans') ? 'Type I' : (data.skin_tone === 'fair' && data.sun_reaction === 'usually_burns_tans_minimally') ? 'Type II' : (data.skin_tone === 'medium' && data.sun_reaction === 'sometimes_burns_tans_gradually') ? 'Type III' : (data.skin_tone === 'medium' && data.sun_reaction === 'rarely_burns_tans_easily') ? 'Type IV' : (data.skin_tone === 'brown' && data.sun_reaction === 'very_rarely_burns_tans_very_easily') ? 'Type V' : (data.skin_tone === 'dark_brown' && data.sun_reaction === 'never_burns_tans_very_easily') ? 'Type VI' : '';"}, {"key": "ethnic_background_header", "html": "<h2>Ethnic Background</h2><p>Your ethnic background may influence your skin's response to treatments. This information helps us tailor your care.</p>", "type": "content", "input": false, "label": "Content"}, {"key": "parent1_ethnic_background", "type": "selectboxes", "input": true, "label": "What is the ethnic background of your first parent? (Select all that apply)", "values": [{"label": "Caucasian", "value": "caucasian"}, {"label": "African or Afro-Caribbean", "value": "african"}, {"label": "East Asian", "value": "east_asian"}, {"label": "South Asian", "value": "south_asian"}, {"label": "Middle Eastern", "value": "middle_eastern"}, {"label": "Hispanic or Latino", "value": "hispanic"}, {"label": "Indigenous", "value": "indigenous"}, {"label": "Other", "value": "other"}], "tableView": true, "optionsLabelPosition": "right"}, {"key": "parent1_ethnic_background_other", "type": "textfield", "input": true, "label": "Please specify the ethnic background of your first parent", "tableView": true, "customConditional": "show = _.get(data, 'parent1_ethnic_background.other');"}, {"key": "parent2_ethnic_background", "type": "selectboxes", "input": true, "label": "What is the ethnic background of your second parent? (Select all that apply)", "values": [{"label": "Caucasian", "value": "caucasian"}, {"label": "African or Afro-Caribbean", "value": "african"}, {"label": "East Asian", "value": "east_asian"}, {"label": "South Asian", "value": "south_asian"}, {"label": "Middle Eastern", "value": "middle_eastern"}, {"label": "Hispanic or Latino", "value": "hispanic"}, {"label": "Indigenous", "value": "indigenous"}, {"label": "Other", "value": "other"}], "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = _.some(_.values(data.parent1_ethnic_background));"}, {"key": "parent2_ethnic_background_other", "type": "textfield", "input": true, "label": "Please specify the ethnic background of your second parent", "tableView": true, "customConditional": "show = _.get(data, 'parent2_ethnic_background.other');"}, {"key": "sun_exposure_header", "html": "<h2>Prior Sun Exposure History</h2><p>Your history of sun exposure helps us recommend the most effective skin care routine tailored to your needs.</p>", "type": "content", "input": false, "label": "Content"}, {"key": "frequent_sun_exposure", "type": "radio", "input": true, "label": "Have you had frequent or prolonged sun exposure (e.g., working outdoors or living in sunny climates)?", "values": [{"label": "Yes, frequently", "value": "frequent"}, {"label": "Yes, occasionally", "value": "occasional"}, {"label": "No, rarely", "value": "rare"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "sunburn_history", "type": "radio", "input": true, "label": "How often have you experienced sunburns?", "values": [{"label": "Frequently", "value": "frequently"}, {"label": "Occasionally", "value": "occasionally"}, {"label": "Rarely or never", "value": "rarely"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "tanning_bed_use", "type": "radio", "input": true, "label": "Have you ever used tanning beds?", "values": [{"label": "Yes, regularly", "value": "regularly"}, {"label": "Yes, occasionally", "value": "occasionally"}, {"label": "No, never", "value": "never"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "sun_protection_use", "type": "radio", "input": true, "label": "Do you use sun protection (e.g., sunscreen, hats, or protective clothing)?", "values": [{"label": "Yes, daily", "value": "daily"}, {"label": "Yes, occasionally", "value": "occasionally"}, {"label": "No, rarely or never", "value": "rarely"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "sun_exposure_follow_up", "type": "textarea", "input": true, "label": "Please share any additional details about your sun exposure or protection habits that you think are relevant.", "tableView": true, "placeholder": "E.g., specific times of life with high exposure, preferred sunscreen type, etc."}, {"key": "skin_conditions_header", "html": "<h2>Chronic Skin Conditions</h2><p>Certain chronic skin conditions can affect your skin health and treatment options. Understanding these helps us provide the best care for you.</p>", "type": "content", "input": false, "label": "Content"}, {"key": "chronic_skin_conditions", "type": "selectboxes", "input": true, "label": "Have you been diagnosed with any of the following chronic skin conditions? (Select all that apply)", "values": [{"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "psoriasis"}, {"label": "Vitiligo", "value": "vitiligo"}, {"label": "Lupus (discoid or systemic)", "value": "lupus"}, {"label": "Scleroderma", "value": "scleroderma"}, {"label": "Bullous pemphigoid", "value": "bullous_pemphigoid"}, {"label": "Pemphigus vulgaris", "value": "pemphigus_vulgaris"}, {"label": "Dermatomyositis", "value": "dermatomyositis"}, {"label": "Other", "value": "other"}], "tableView": true, "optionsLabelPosition": "right"}, {"key": "chronic_skin_conditions_other", "type": "textfield", "input": true, "label": "Please specify the chronic skin condition", "tableView": true, "customConditional": "show = _.get(data, 'chronic_skin_conditions.other');"}, {"key": "chronic_skin_conditions_treatment", "type": "textarea", "input": true, "label": "If you selected any, please describe the treatments you’ve received for your condition.", "placeholder": "E.g., topical steroids, immunosuppressants, light therapy, etc.", "tableView": true, "customConditional": "show = _.some(_.values(data.chronic_skin_conditions));"}, {"key": "prior_consultation_header", "html": "<h2>Prior Consultation</h2>", "type": "content", "input": false, "label": "Content"}, {"key": "prior_consultation", "type": "radio", "input": true, "label": "Have you previously consulted a skin specialist for this concern?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "tableView": true, "customConditional": "show = (_.some(_.values(data.treatment_goals)) || data.none_of_the_above_diagnosed_conditions);", "optionsLabelPosition": "right"}, {"key": "consultation_type", "type": "selectboxes", "input": true, "label": "What type of specialist did you consult? (Select all that apply)", "values": [{"label": "Medical dermatologist", "value": "medical_dermatologist"}, {"label": "Medi-spa or aesthetic clinic", "value": "medi_spa"}, {"label": "General practitioner", "value": "general_practitioner"}, {"label": "Plastic surgeon", "value": "plastic_surgeon"}, {"label": "Cosmetic nurse", "value": "cosmetic_nurse"}, {"label": "Other", "value": "other"}], "tableView": true, "customConditional": "show = data.prior_consultation === 'yes';", "optionsLabelPosition": "right"}]}