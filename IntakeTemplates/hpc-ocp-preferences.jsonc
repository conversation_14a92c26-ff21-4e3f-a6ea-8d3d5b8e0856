{"components": [{"key": "days_duration_delay_menses", "data": {"values": [{"label": "1 day", "value": "1_day"}, {"label": "2 days", "value": 2}, {"label": "3 days", "value": 3}, {"label": "4 days", "value": 4}, {"label": "5 days", "value": 5}, {"label": "6 days", "value": 6}, {"label": "7 days", "value": 7}, {"label": "8 days", "value": 8}, {"label": "9 days", "value": 9}, {"label": "10 days", "value": 10}, {"label": "11 days", "value": 11}, {"label": "12 days", "value": 12}, {"label": "13 days", "value": 13}, {"label": "14 days", "value": 14}, {"label": "15 days", "value": 15}, {"label": "16 days", "value": 16}, {"label": "17 days", "value": 17}, {"label": "18 days", "value": 18}, {"label": "19 days", "value": 19}, {"label": "20 days", "value": 20}, {"label": "21 days", "value": 21}, {"label": "Over 21 days", "value": "over_21_days"}]}, "type": "select", "input": true, "label": "How many days do you wish to delay your cycle?", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.sku == 'fem_pn_suppress_menses';", "optionsLabelPosition": "right"}, {"key": "rx_preference_suppress_menses", "type": "radio", "input": true, "label": "MPA and NETA are progesterone medications, and both are reasonable options to delay your menstrual cycle. At TeleTest, we preferentially prescribe MPA pills over NETA. When taken orally, some NETA is converted into estrogen by your body, and carries the same risks associated with using birth control pills (i.e. a higher risk of blood clots, stroke, etc). If you have been on NETA in the past, and tolerated this medication well, it remains a safe and reasonable option.</br></br>Norethindrone Acetate (NETA)</br><li>Officially Approved in the United Kindom for Delay of Menstruation</li><li>Dosing: Remember to take it three times per day (i.e. every 8 hours)</li><li>Approximate Cost: $6/day + Pharmacy Dispensing Fee</li></br></br>Medroxyprogesterone Acetate (MPA)</br><li>Used 'off-label' for Delay of Menstruation</li><li>Dosing: Once or twice daily </li><li>Approximate Cost: $0.25-$0.50/day + Pharmacy Dispensing Fee</li></br></br>Which medication would you prefer to use?", "inline": false, "values": [{"label": "MPA (recommended)", "value": "MPA"}, {"label": "NETA", "value": "NETA"}, {"label": "Not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.sku == 'fem_pn_suppress_menses';", "optionsLabelPosition": "right"}, {"key": "rx_preference_ocp", "type": "radio", "input": true, "label": "Do you have any preference for medication?", "inline": false, "values": [{"label": "<PERSON><PERSON><PERSON> 21", "value": "alysena-21"}, {"label": "<PERSON><PERSON><PERSON> 28 (Most Popular)", "value": "alysena-28"}, {"label": "<PERSON><PERSON><PERSON>", "value": "evra-200mcg35mcg-1year"}, {"label": "<PERSON><PERSON>", "value": "lolo-1mg10mcg10mcg"}, {"label": "Slynd", "value": "slynd"}, {"label": "Yaz", "value": "yaz-3mg002mg"}, {"label": "<PERSON><PERSON><PERSON>", "value": "yasmin-28-year"}, {"label": "Other", "value": "other"}, {"label": "Not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.sku == 'fem_pn_ocp'", "optionsLabelPosition": "right"}, {"key": "rx_preference_ocp_other", "type": "textarea", "input": true, "label": "You selected “other”, please specify:", "validate": {"required": true}, "tableView": true, "autoExpand": false, "customConditional": "show = data.sku == 'fem_pn_ocp' && data.rx_preference_ocp == 'other'"}, {"key": "rxts", "type": "textfield", "input": true, "label": "Rx Templates:", "hidden": true, "disabled": true, "multiple": true, "adminFlag": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "if (data.sku == 'fem_pn_ocp') { value = ['other','not_sure'].includes(data.rx_preference_ocp) ? [] : [data.rx_preference_ocp]; } else if (data.sku == 'fem_pn_suppress_menses') {let days = data.days_duration_delay_menses; days = Number.isInteger(days) ? days : 18; days += 3; days = days < 6 ? 6 : days; value = data.rx_preference_suppress_menses =='NETA'?[`neta-5mg-tid-${days}-days`]:[`medroxyprogesterone-acetate-10mg-${days}-days`];} else { value = []; }", "refreshOnChange": true}]}