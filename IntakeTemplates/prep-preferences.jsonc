{"components": [{"key": "header_prep_method_selection", "type": "content", "input": false, "label": "Content", "html": "</br><h3>PrEP Method Selection</h3><p>To help determine which PrEP approach you qualify for, please answer the following:</p>"}, {"key": "sex_assigned_birth", "type": "radio", "input": true, "label": "What sex were you assigned at birth? (the classification—male or female—given at birth based on anatomy)", "values": [{"label": "Male", "value": "male"}, {"label": "Female", "value": "female"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "confirm_label": "Sex assigned at birth:"}, {"key": "gender_identity", "type": "radio", "input": true, "label": "How do you identify now?", "values": [{"label": "Male", "value": "male"}, {"label": "Female", "value": "female"}, {"label": "Non-binary / Other", "value": "nonbinary"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "confirm_label": "Current identity:"}, {"key": "gender_classification", "type": "textfield", "input": true, "label": "Your gender classification:", "disabled": true, "calculateValue": "var s=data.sex_assigned_birth;var g=data.gender_identity;var c='';if(s==='male'&&g==='male'){c='Male (assigned male at birth, identifies as a man)';}else if(s==='female'&&g==='female'){c='Female (assigned female at birth, identifies as a woman)';}else if(s==='male'&&g==='female'){c='Transgender female';}else if(s==='female'&&g==='male'){c='Transgender male';}else{c='Non-binary / Other';}value=c;", "refreshOnChange": true, "tableView": true, "confirm_label": "Classification:"}, {"key": "confirm_gender_classification", "type": "radio", "input": true, "label": "Is this correct?", "values": [{"label": "Yes, that's correct", "value": "yes"}, {"label": "No, let me revise", "value": "no"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = !!data.gender_classification;", "confirm_label": "Confirmed correct:"}, {"key": "gender_revision_warning", "type": "content", "input": false, "html": "<p style='color:red; font-weight: bold;'>Please revise your selections above before continuing.</p>", "customConditional": "show = data.confirm_gender_classification === 'no';"}, {"key": "bottom_surgery_history", "type": "radio", "input": true, "label": "Have you had gender-affirming bottom surgery?", "values": [{"label": "Yes", "value": true}, {"label": "No", "value": false}, {"label": "I don't know what that is", "value": "unknown"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = data.gender_identity === 'trans_male' || data.gender_identity === 'trans_female';", "confirm_label": "Bottom surgery:"}, {"key": "heading_on_demand_eligibility", "type": "content", "input": false, "label": "Content", "html": "</br><h4>On-Demand Eligibility</h4><p>This section helps determine your eligibility for on-demand PrEP dosing.</p>", "tableView": false, "customConditional": "show = data.sex_assigned_birth === 'male' && (data.gender_identity === 'male' || data.gender_identity === 'nonbinary');"}, {"key": "on_demand_sex_frequency", "type": "radio", "input": true, "label": "How often do you have sex?", "values": [{"label": "Definitely less than once per week", "value": "less_than_week"}, {"label": "Sometimes (about once per week)", "value": "about_week"}, {"label": "More often than once per week", "value": "more_than_week"}, {"label": "Prefer not to say", "value": "prefer_not"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "confirm_label": "Sex frequency:", "customConditional": "show = data.sex_assigned_birth === 'male' && (data.gender_identity === 'male' || data.gender_identity === 'nonbinary');"}, {"key": "on_demand_anticipate_sex", "type": "radio", "input": true, "label": "Can you anticipate or delay sex to take doses at least 2 hours before intercourse?", "values": [{"label": "Yes", "value": true}, {"label": "No", "value": false}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "confirm_label": "Can anticipate/delay sex:", "customConditional": "show = data.sex_assigned_birth === 'male' && (data.gender_identity === 'male' || data.gender_identity === 'nonbinary');"}, {"key": "ivdu_usage", "type": "radio", "input": true, "label": "Do you inject non-prescribed drugs (IVDU)?", "values": [{"label": "Yes", "value": true}, {"label": "No", "value": false}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "confirm_label": "IVDU usage:"}, {"key": "regimen_selection", "type": "selectboxes", "input": true, "label": "Based on your selections, you qualify for:", "values": [{"label": "Truvada (daily dosing)", "value": "truvada-daily"}, {"label": "Truvada (on-demand 2-1-1)", "value": "truvada-on-demand", "customConditional": "show = data.sex_assigned_birth === 'male' && (data.gender_identity === 'male' || data.gender_identity === 'nonbinary') && data.on_demand_sex_frequency === 'less_than_week' && data.on_demand_anticipate_sex === true && !data.ivdu_usage;"}, {"label": "Descovy (daily dosing)", "value": "descovy", "customConditional": "show = data.sex_assigned_birth === 'male' && !data.ivdu_usage;"}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "confirm_label": "Route of exposure:", "optionsLabelPosition": "right"}]}