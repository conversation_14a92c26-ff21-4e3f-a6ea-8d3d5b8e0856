{"components": [{"key": "heading_photoaging", "html": "<h1><strong>Photoaging Treatment Consultation</strong></h1>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "heading_diagnosis", "html": "<h2><strong>Diagnosis and History</strong></h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "prior_photoaging_diagnosis", "type": "radio", "input": true, "label": "Have you ever been told by a healthcare professional that you have sun damage or skin damage caused by sun exposure?</br></br><ul><li>Sun damage refers to skin changes caused by prolonged exposure to UV rays.</li><li>Common signs include wrinkles, dark spots, and uneven skin texture.</li><li>If you're unsure, consider whether your skin has these characteristics.</li></ul>", "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "I'm not sure", "value": "dont_know", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "confirm_label": "Prior Sun Damage Diagnosis", "optionsLabelPosition": "right"}, {"key": "prior_photoaging_diagnosis_professional", "type": "radio", "input": true, "label": "Who diagnosed you with sun damange?", "inline": false, "values": [{"label": "Dermatologist", "value": "dermatologist", "shortcut": ""}, {"label": "Aesthetician", "value": "aesthetician", "shortcut": ""}, {"label": "Family Doctor", "value": "family_doctor", "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "confirm_label": "Prior Photoaging Diagnosis Professional", "customConditional": "show = data.prior_photoaging_diagnosis == true;", "optionsLabelPosition": "right"}, {"key": "heading_reason_for_interest", "html": "<h2><strong>Why Are You Interested in Retinoids?</strong></h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "reason_for_interest_in_topical_retinoids", "type": "selectboxes", "input": true, "label": "Please select your reason/interest in topical retinoids:", "values": [{"label": "Reducing the appearance of fine lines and wrinkles", "value": "reduce_fine_lines_wrinkles"}, {"label": "Improving skin texture and tone", "value": "improve_texture_tone"}, {"label": "Treating acne and preventing breakouts", "value": "treat_acne_prevent_breakouts"}, {"label": "Fading dark spots and hyperpigmentation", "value": "fade_dark_spots_hyperpigmentation"}, {"label": "Seeking overall skin rejuvenation", "value": "overall_skin_rejuvenation"}, {"label": "Recommended by a dermatologist or healthcare provider", "value": "recommended_by_professional"}, {"label": "Other", "value": "other_reasons"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Reason for Interest in Retinoids", "optionsLabelPosition": "right"}, {"key": "other_photoaging_diagnosis_professional", "type": "textarea", "input": true, "label": "Please specify who diagnosed you with sun damage:", "tableView": true, "autoExpand": false, "confirm_label": "Other Photoaging Diagnosis Professional:", "placeholder": "Enter the name of the professional", "customConditional": "show = data.prior_photoaging_diagnosis_professional == 'other';"}, {"key": "photoaging_treatment_heading", "html": "<h3>Past Treatments for Photoaging</h3>", "type": "content", "input": false}, {"key": "photoaging_treatment_history", "type": "selectboxes", "input": true, "label": "Have you used any of the following treatments for photoaging in the past?", "values": [{"label": "Over-the-counter creams (i.e. retinol)", "value": "otc_creams", "shortcut": ""}, {"label": "Prescription retinoids", "value": "prescription_retinoids", "shortcut": ""}, {"label": "Chemical peels", "value": "chemical_peels", "shortcut": ""}, {"label": "Laser treatments", "value": "laser_treatments", "shortcut": ""}], "tableView": true, "confirm_label": "Prior Photoaging Treatments:", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_photoaging_treatments", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a treatment."}, "validate": {"custom": "valid = !!data.none_of_the_above_photoaging_treatments || _.some(_.values(data.photoaging_treatment_history));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "otc_creams_last_used", "data": {"values": [{"label": "Within the past month", "value": "past_month"}, {"label": "1-3 months ago", "value": "1_3_months"}, {"label": "3-6 months ago", "value": "3_6_months"}, {"label": "6+ months ago", "value": "6_plus_months"}]}, "type": "select", "input": true, "label": "When did you last use over-the-counter creams (e.g., retinol)?", "widget": "html5", "validate": {"required": true}, "confirm_label": "Last OTC Cream Use:", "customConditional": "show = data.photoaging_treatment_history.otc_creams === true;"}, {"key": "otc_creams_used", "type": "selectboxes", "input": true, "label": "Which over-the-counter products have you used?", "values": [{"label": "Retinol serum", "value": "retinol_serum"}, {"label": "Bakuchiol", "value": "bakuchiol"}, {"label": "Niacinamide", "value": "niacinamide"}, {"label": "Vitamin C", "value": "vitamin_c"}, {"label": "Other", "value": "other"}], "confirm_label": "OTC Cream Types Used:", "tableView": true, "customConditional": "show = data.photoaging_treatment_history.otc_creams === true;"}, {"key": "retinol_overlap_alert", "html": "<div style='border-left: 4px solid #f5c518; background-color: #fff3cd; padding: 10px; margin-bottom: 10px;'><strong>Important:</strong> Retinol is much less potent than prescription retinoids. Using both together can increase irritation and is not recommended. You should stop using over-the-counter retinol when starting a prescription retinoid.</div>", "type": "content", "input": false, "customConditional": "show = data.otc_creams_used?.retinol_serum === true;"}, {"key": "retinol_overlap_warning", "type": "radio", "input": true, "label": "Do you understand that you should stop using over-the-counter retinol when starting a prescription retinoid?", "values": [{"label": "I understand", "value": "understand"}, {"label": "I do not understand", "value": "do_not_understand"}], "validate": {"required": true}, "confirm_label": "Retinol & Rx Retinoid Advice Understood:", "tableView": true, "customConditional": "show = data.otc_creams_used?.retinol_serum === true;"}, {"key": "otc_creams_duration", "data": {"values": [{"label": "<1 month", "value": "less_1_month"}, {"label": "1-3 months", "value": "1_3_months"}, {"label": "3-6 months", "value": "3_6_months"}, {"label": "More than 6 months", "value": "more_6_months"}]}, "type": "select", "input": true, "label": "How long have you used them?", "widget": "html5", "validate": {"required": true}, "confirm_label": "Duration of OTC Cream Use:", "tableView": true, "customConditional": "show = data.photoaging_treatment_history.otc_creams === true;"}, {"key": "otc_creams_side_effects", "type": "selectboxes", "input": true, "label": "Did you experience any side effects?", "values": [{"label": "Redness", "value": "redness"}, {"label": "Irritation", "value": "irritation"}, {"label": "<PERSON>ing", "value": "peeling"}, {"label": "Dryness", "value": "dryness"}, {"label": "None", "value": "none"}], "confirm_label": "OTC Cream Side Effects:", "tableView": true, "customConditional": "show = data.photoaging_treatment_history.otc_creams === true;"}, {"key": "otc_creams_tolerance", "type": "radio", "input": true, "label": "Did you tolerate the over-the-counter treatment well?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "not_sure"}, {"label": "Stopped due to side effects", "value": "stopped_due_to_side_effects"}], "confirm_label": "Tolerated OTC Creams:", "tableView": true, "customConditional": "show = data.photoaging_treatment_history.otc_creams === true;"}, {"key": "otc_creams_side_effects_management", "type": "radio", "input": true, "label": "How did you manage the side effects?", "values": [{"label": "Mild and manageable - continued treatment", "value": "manageable"}, {"label": "Moderate - adjusted frequency or dose", "value": "adjusted_dose"}, {"label": "Severe - had to stop treatment", "value": "discontinued"}, {"label": "Other", "value": "other"}], "validate": {"required": true}, "confirm_label": "OTC Cream Side Effect Management:", "tableView": true, "customConditional": "show = data.photoaging_treatment_history.otc_creams === true && _.some(_.values(data.otc_creams_side_effects), Boolean) && !data.otc_creams_side_effects.none;"}, {"key": "prescription_retinoids_last_used", "data": {"values": [{"label": "Currently using them", "value": "currently_using"}, {"label": "1 month ago", "value": "1_month"}, {"label": "2 months ago", "value": "2_months"}, {"label": "3 months ago", "value": "3_months"}, {"label": "4 months ago", "value": "4_months"}, {"label": "5 months ago", "value": "5_months"}, {"label": "6 months ago", "value": "6_months"}, {"label": "7 months ago", "value": "7_months"}, {"label": "8 months ago", "value": "8_months"}, {"label": "9 months ago", "value": "9_months"}, {"label": "10 months ago", "value": "10_months"}, {"label": "11 months ago", "value": "11_months"}, {"label": "12 or more months ago", "value": "12_plus_months"}]}, "type": "select", "tableView": true, "input": true, "label": "When did you last use prescription retinoids?", "widget": "html5", "validate": {"required": true}, "confirm_label": "Last Prescription Retinoid Use:", "customConditional": "show = data.photoaging_treatment_history.prescription_retinoids === true;"}, {"key": "prescription_retinoids_used", "type": "selectboxes", "input": true, "label": "Which prescription retinoids have you used?", "values": [{"label": "<PERSON><PERSON><PERSON><PERSON> (Retin-A)", "value": "tretinoin"}, {"label": "Tazarotene (Tazorac or Arazlo)", "value": "tazarotene"}, {"label": "Adapal<PERSON> (Differin Rx strength)", "value": "adapalene"}, {"label": "Trifarotene (Aklief)", "value": "trifarotene"}, {"label": "Other", "value": "other"}], "confirm_label": "Prescription Retinoids Used:", "tableView": true, "customConditional": "show = data.photoaging_treatment_history.prescription_retinoids === true;"}, {"key": "heading_tretinoin_details", "html": "<h3><strong>Tretinoin (Retin-A) Use Details</strong></h3>", "type": "content", "input": false, "customConditional": "show = data.prescription_retinoids_used?.tretinoin === true;"}, {"key": "tretinoin_last_used", "data": {"values": [{"label": "Currently using", "value": "currently_using"}, {"label": "1 month ago", "value": "1_month"}, {"label": "2 months ago", "value": "2_months"}, {"label": "3 months ago", "value": "3_months"}, {"label": "4 months ago", "value": "4_months"}, {"label": "5 months ago", "value": "5_months"}, {"label": "6 months ago", "value": "6_months"}, {"label": "7 months ago", "value": "7_months"}, {"label": "8 months ago", "value": "8_months"}, {"label": "9 months ago", "value": "9_months"}, {"label": "10 months ago", "value": "10_months"}, {"label": "11 months ago", "value": "11_months"}, {"label": "12 or more months ago", "value": "12_plus_months"}]}, "type": "select", "input": true, "label": "When did you last use Tretinoin?", "widget": "html5", "validate": {"required": true}, "confirm_label": "Last Tretinoin Use:", "tableView": true, "customConditional": "show = data.prescription_retinoids_used?.tretinoin === true;"}, {"key": "tretinoin_duration", "data": {"values": [{"label": "<1 month", "value": "less_1_month"}, {"label": "1-3 months", "value": "1_3_months"}, {"label": "3-6 months", "value": "3_6_months"}, {"label": "6-12 months", "value": "6_12_months"}, {"label": "Over 1 year", "value": "over_1_year"}]}, "type": "select", "input": true, "label": "How long did you use Tretinoin in total?", "widget": "html5", "validate": {"required": true}, "confirm_label": "Tretinoin Duration of Use:", "tableView": true, "customConditional": "show = data.prescription_retinoids_used?.tretinoin === true;"}, {"key": "tretinoin_strength_form", "type": "selectboxes", "input": true, "label": "Which strengths and formulations of Tretinoin have you used?", "values": [{"label": "0.01% Cream/gel", "value": "0_01_cream_gel"}, {"label": "0.025% Cream", "value": "0_025_cream"}, {"label": "0.05% Cream", "value": "0_05_cream"}, {"label": "0.1% Cream", "value": "0_1_cream"}, {"label": "0.025% Gel", "value": "0_025_gel"}, {"label": "0.05% Gel", "value": "0_05_gel"}, {"label": "0.1% Gel", "value": "0_1_gel"}, {"label": "Other", "value": "other"}], "validate": {"required": true}, "confirm_label": "Tretinoin Strength & Form:", "tableView": true, "customConditional": "show = data.prescription_retinoids_used?.tretinoin === true;"}, {"key": "tretinoin_0_1_warning", "html": "<div style='border-left: 4px solid #0d6efd; background-color: #e7f1ff; padding: 10px; margin-bottom: 10px;'><strong>Note:</strong> 0.1% Tretinoin is very different from 0.01% Tretinoin. It is a much stronger strength that is rarely prescribed due to a higher risk of side effects and often limited added benefit compared to 0.05%.<br><br>Please also note that <strong>retinol 0.1%</strong> (an over-the-counter product) is not the same as <strong>tretinoin 0.1%</strong> (a prescription product). Double-check the strength and product name before continuing.</div>", "type": "content", "input": false, "customConditional": "show = data.tretinoin_strength_form?.['0_1_cream'] || data.tretinoin_strength_form?.['0_1_gel'];"}, {"key": "tretinoin_0_1_confirmation", "type": "radio", "input": true, "label": "To clarify, which of the following best describes your use?", "values": [{"label": "I was using prescription Tretinoin 0.1% (Cream or Gel)", "value": "confirmed_0_1"}, {"label": "I meant 0.01% Tretinoin, not 0.1%", "value": "meant_0_01"}, {"label": "I was using 0.1% Retinol (over-the-counter), not Tretinoin", "value": "retinol_0_1"}, {"label": "I'm not sure", "value": "not_sure"}], "validate": {"required": true}, "confirm_label": "Tretinoin 0.1% Strength Confirmation:", "tableView": true, "customConditional": "show = data.tretinoin_strength_form?.['0_1_cream'] || data.tretinoin_strength_form?.['0_1_gel'];"}, {"key": "tretinoin_side_effects", "type": "selectboxes", "input": true, "label": "Did you experience any side effects from Tretinoin?", "values": [{"label": "Redness", "value": "redness"}, {"label": "Irritation", "value": "irritation"}, {"label": "<PERSON>ing", "value": "peeling"}, {"label": "Burning", "value": "burning"}, {"label": "Dryness", "value": "dryness"}, {"label": "None", "value": "none"}], "confirm_label": "Tretinoin Side Effects:", "tableView": true, "customConditional": "show = data.prescription_retinoids_used?.tretinoin === true;"}, {"key": "tretinoin_tolerance", "type": "radio", "input": true, "label": "Did you tolerate <PERSON><PERSON><PERSON><PERSON> well?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "not_sure"}, {"label": "Stopped due to side effects", "value": "stopped_due_to_side_effects"}], "confirm_label": "Tretinoin Tolerance:", "tableView": true, "customConditional": "show = data.prescription_retinoids_used?.tretinoin === true;"}, {"key": "tretinoin_side_effects_management", "type": "radio", "input": true, "label": "How did you manage the side effects from Tretinoin?", "values": [{"label": "Mild and manageable - continued treatment", "value": "manageable"}, {"label": "Moderate - adjusted frequency or dose", "value": "adjusted_dose"}, {"label": "Severe - had to stop treatment", "value": "discontinued"}, {"label": "Other", "value": "other"}], "validate": {"required": true}, "confirm_label": "Tretinoin Side Effect Management:", "tableView": true, "customConditional": "show = data.prescription_retinoids_used?.tretinoin === true && _.some(_.values(data.tretinoin_side_effects), Boolean) && !data.tretinoin_side_effects.none;"}, {"key": "tretinoin_use_pattern", "data": {"values": [{"label": "Used daily at night for anti-aging or acne", "value": "daily_use"}, {"label": "Used a few times per week to reduce irritation", "value": "intermittent_use"}, {"label": "Used periodically during acne flares", "value": "flare_based_use"}, {"label": "Used seasonally (e.g., avoided in summer months)", "value": "seasonal_use"}, {"label": "Started and stopped multiple times", "value": "on_off_cycles"}, {"label": "Other", "value": "other"}]}, "type": "select", "input": true, "label": "What was your typical pattern of Tretinoin use?", "tableView": true, "widget": "html5", "validate": {"required": true}, "confirm_label": "Tretinoin Use Pattern:", "customConditional": "show = data.prescription_retinoids_used?.tretinoin === true;"}, {"key": "tretinoin_use_pattern_other", "type": "textarea", "input": true, "label": "Please describe your Tretinoin use pattern:", "tableView": true, "confirm_label": "Tretinoin Use Pattern Other:", "placeholder": "e.g., Used only during winter, applied with moisturizer first, etc.", "customConditional": "show = data.tretinoin_use_pattern === 'other';"}, {"key": "heading_tazarotene_details", "html": "<h3><strong>Tazarotene Use Details</strong></h3>", "type": "content", "input": false, "customConditional": "show = data.prescription_retinoids_used?.tazarotene === true;"}, {"key": "tazarotene_last_used", "data": {"values": [{"label": "Currently using", "value": "currently_using"}, {"label": "1 month ago", "value": "1_month"}, {"label": "2 months ago", "value": "2_months"}, {"label": "3 months ago", "value": "3_months"}, {"label": "4 months ago", "value": "4_months"}, {"label": "5 months ago", "value": "5_months"}, {"label": "6 months ago", "value": "6_months"}, {"label": "7 months ago", "value": "7_months"}, {"label": "8 months ago", "value": "8_months"}, {"label": "9 months ago", "value": "9_months"}, {"label": "10 months ago", "value": "10_months"}, {"label": "11 months ago", "value": "11_months"}, {"label": "12 or more months ago", "value": "12_plus_months"}]}, "type": "select", "tableView": true, "input": true, "label": "When did you last use Tazarotene?", "widget": "html5", "validate": {"required": true}, "confirm_label": "Last Tazarotene Use:", "customConditional": "show = data.prescription_retinoids_used?.tazarotene === true;"}, {"key": "tazarotene_duration", "data": {"values": [{"label": "<1 month", "value": "less_1_month"}, {"label": "1-3 months", "value": "1_3_months"}, {"label": "3-6 months", "value": "3_6_months"}, {"label": "6-12 months", "value": "6_12_months"}, {"label": "Over 1 year", "value": "over_1_year"}]}, "type": "select", "tableView": true, "input": true, "label": "How long did you use Tazarotene in total?", "widget": "html5", "validate": {"required": true}, "confirm_label": "Tazarotene Duration of Use:", "customConditional": "show = data.prescription_retinoids_used?.tazarotene === true;"}, {"key": "tazarotene_strength_form", "type": "selectboxes", "input": true, "label": "Which strengths and formulations of Tazarotene have you used?", "values": [{"label": "0.045% Lotion (Arazlo)", "value": "0_045_lotion"}, {"label": "0.05% Cream", "value": "0_05_cream"}, {"label": "0.05% Gel", "value": "0_05_gel"}, {"label": "0.1% Cream", "value": "0_1_cream"}, {"label": "0.1% Gel", "value": "0_1_gel"}, {"label": "Other", "value": "other"}], "validate": {"required": true}, "confirm_label": "Tazarotene Strength & Form:", "tableView": true, "customConditional": "show = data.prescription_retinoids_used?.tazarotene === true;"}, {"key": "tazarotene_use_pattern", "data": {"values": [{"label": "Used daily at night for anti-aging or acne", "value": "daily_use"}, {"label": "Used a few times per week to reduce irritation", "value": "intermittent_use"}, {"label": "Used periodically during acne flares", "value": "flare_based_use"}, {"label": "Used seasonally (e.g., avoided in summer months)", "value": "seasonal_use"}, {"label": "Started and stopped multiple times", "value": "on_off_cycles"}, {"label": "Other", "value": "other"}]}, "type": "select", "tableView": true, "input": true, "label": "What was your typical pattern of Tazarotene use?", "widget": "html5", "validate": {"required": true}, "confirm_label": "Tazarotene Use Pattern:", "customConditional": "show = data.prescription_retinoids_used?.tazarotene === true;"}, {"key": "tazarotene_use_pattern_other", "type": "textarea", "input": true, "label": "Please describe your Tazarotene use pattern:", "placeholder": "e.g., Used in winter, avoided with dryness, etc.", "tableView": true, "customConditional": "show = data.tazarotene_use_pattern === 'other';"}, {"key": "tazarotene_side_effects", "type": "selectboxes", "input": true, "label": "Did you experience any side effects from Tazarotene?", "values": [{"label": "Redness", "value": "redness"}, {"label": "Irritation", "value": "irritation"}, {"label": "<PERSON>ing", "value": "peeling"}, {"label": "Burning", "value": "burning"}, {"label": "Dryness", "value": "dryness"}, {"label": "None", "value": "none"}], "confirm_label": "Tazarotene Side Effects:", "tableView": true, "customConditional": "show = data.prescription_retinoids_used?.tazarotene === true;"}, {"key": "tazarotene_tolerance", "type": "radio", "input": true, "label": "Did you tolerate <PERSON><PERSON><PERSON><PERSON> well?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "not_sure"}, {"label": "Stopped due to side effects", "value": "stopped_due_to_side_effects"}], "confirm_label": "Tazarotene Tolerance:", "tableView": true, "customConditional": "show = data.prescription_retinoids_used?.tazarotene === true;"}, {"key": "tazarotene_side_effects_management", "type": "radio", "input": true, "label": "How did you manage the side effects from Tazarotene?", "values": [{"label": "Mild and manageable - continued treatment", "value": "manageable"}, {"label": "Moderate - adjusted frequency or dose", "value": "adjusted_dose"}, {"label": "Severe - had to stop treatment", "value": "discontinued"}, {"label": "Other", "value": "other"}], "validate": {"required": true}, "confirm_label": "Tazarotene Side Effect Management:", "tableView": true, "customConditional": "show = data.prescription_retinoids_used?.tazarotene === true && _.some(_.values(data.tazarotene_side_effects), Boolean) && !data.tazarotene_side_effects.none;"}, {"key": "heading_adapalene_details", "html": "<h3><strong><PERSON><PERSON><PERSON> (<PERSON><PERSON>in) Use Details</strong></h3>", "type": "content", "input": false, "customConditional": "show = data.prescription_retinoids_used?.adapalene === true;"}, {"key": "adapalene_last_used", "data": {"values": [{"label": "Currently using", "value": "currently_using"}, {"label": "1 month ago", "value": "1_month"}, {"label": "2 months ago", "value": "2_months"}, {"label": "3 months ago", "value": "3_months"}, {"label": "4 months ago", "value": "4_months"}, {"label": "5 months ago", "value": "5_months"}, {"label": "6 months ago", "value": "6_months"}, {"label": "7 months ago", "value": "7_months"}, {"label": "8 months ago", "value": "8_months"}, {"label": "9 months ago", "value": "9_months"}, {"label": "10 months ago", "value": "10_months"}, {"label": "11 months ago", "value": "11_months"}, {"label": "12 or more months ago", "value": "12_plus_months"}]}, "type": "select", "input": true, "tableView": true, "label": "When did you last use Adapalene?", "widget": "html5", "validate": {"required": true}, "confirm_label": "Last Adapalene Use:", "customConditional": "show = data.prescription_retinoids_used?.adapalene === true;"}, {"key": "adapalene_duration", "data": {"values": [{"label": "<1 month", "value": "less_1_month"}, {"label": "1-3 months", "value": "1_3_months"}, {"label": "3-6 months", "value": "3_6_months"}, {"label": "6-12 months", "value": "6_12_months"}, {"label": "Over 1 year", "value": "over_1_year"}]}, "type": "select", "input": true, "tableView": true, "label": "How long did you use Adapalene?", "widget": "html5", "validate": {"required": true}, "confirm_label": "Adapalene Duration of Use:", "customConditional": "show = data.prescription_retinoids_used?.adapalene === true;"}, {"key": "adapalene_strength_form", "type": "selectboxes", "input": true, "label": "Which strengths and formulations of Adapalene have you used?", "values": [{"label": "0.1% Cream", "value": "0_1_cream"}, {"label": "0.1% Gel", "value": "0_1_gel"}, {"label": "0.3% Gel", "value": "0_3_gel"}, {"label": "Other", "value": "other"}], "validate": {"required": true}, "confirm_label": "Adapalene Strength & Form:", "tableView": true, "customConditional": "show = data.prescription_retinoids_used?.adapalene === true;"}, {"key": "adapalene_use_pattern", "data": {"values": [{"label": "Used daily at night for anti-aging or acne", "value": "daily_use"}, {"label": "Used a few times per week to reduce irritation", "value": "intermittent_use"}, {"label": "Used periodically during acne flares", "value": "flare_based_use"}, {"label": "Used seasonally", "value": "seasonal_use"}, {"label": "Started and stopped multiple times", "value": "on_off_cycles"}, {"label": "Other", "value": "other"}]}, "type": "select", "input": true, "label": "What was your typical pattern of Adapalene use?", "widget": "html5", "tableView": true, "validate": {"required": true}, "confirm_label": "Adapalene Use Pattern:", "customConditional": "show = data.prescription_retinoids_used?.adapalene === true;"}, {"key": "adapalene_use_pattern_other", "type": "textarea", "input": true, "label": "Please describe your Adapalene use pattern:", "placeholder": "e.g., Applied after moisturizer, used only for chin breakouts, etc.", "customConditional": "show = data.adapalene_use_pattern === 'other';"}, {"key": "adapalene_side_effects", "type": "selectboxes", "input": true, "tableView": true, "label": "Did you experience any side effects from Adapalene?", "values": [{"label": "Redness", "value": "redness"}, {"label": "Irritation", "value": "irritation"}, {"label": "<PERSON>ing", "value": "peeling"}, {"label": "Burning", "value": "burning"}, {"label": "Dryness", "value": "dryness"}, {"label": "None", "value": "none"}], "confirm_label": "Adapalene Side Effects:", "customConditional": "show = data.prescription_retinoids_used?.adapalene === true;"}, {"key": "adapalene_tolerance", "type": "radio", "tableView": true, "input": true, "label": "Did you tolerate <PERSON><PERSON><PERSON> well?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "not_sure"}, {"label": "Stopped due to side effects", "value": "stopped_due_to_side_effects"}], "confirm_label": "Adapalene Tolerance:", "customConditional": "show = data.prescription_retinoids_used?.adapalene === true;"}, {"key": "adapalene_side_effects_management", "type": "radio", "tableView": true, "input": true, "label": "How did you manage the side effects from Adapalene?", "values": [{"label": "Mild and manageable - continued treatment", "value": "manageable"}, {"label": "Moderate - adjusted frequency or dose", "value": "adjusted_dose"}, {"label": "Severe - had to stop treatment", "value": "discontinued"}, {"label": "Other", "value": "other"}], "validate": {"required": true}, "confirm_label": "Adapalene Side Effect Management:", "customConditional": "show = data.prescription_retinoids_used?.adapalene === true && _.some(_.values(data.adapalene_side_effects), Boolean) && !data.adapalene_side_effects.none;"}, {"key": "heading_trifarotene_details", "html": "<h3><strong>Trifarotene (Aklief) Use Details</strong></h3>", "type": "content", "input": false, "customConditional": "show = data.prescription_retinoids_used?.trifarotene === true;"}, {"key": "trifarotene_last_used", "tableView": true, "data": {"values": [{"label": "Currently using", "value": "currently_using"}, {"label": "1 month ago", "value": "1_month"}, {"label": "2 months ago", "value": "2_months"}, {"label": "3 months ago", "value": "3_months"}, {"label": "4 months ago", "value": "4_months"}, {"label": "5 months ago", "value": "5_months"}, {"label": "6 months ago", "value": "6_months"}, {"label": "7 months ago", "value": "7_months"}, {"label": "8 months ago", "value": "8_months"}, {"label": "9 months ago", "value": "9_months"}, {"label": "10 months ago", "value": "10_months"}, {"label": "11 months ago", "value": "11_months"}, {"label": "12 or more months ago", "value": "12_plus_months"}]}, "type": "select", "input": true, "label": "When did you last use Trifarotene?", "widget": "html5", "validate": {"required": true}, "confirm_label": "Last Trifarotene Use:", "customConditional": "show = data.prescription_retinoids_used?.trifarotene === true;"}, {"key": "trifarotene_duration", "data": {"values": [{"label": "<1 month", "value": "less_1_month"}, {"label": "1-3 months", "value": "1_3_months"}, {"label": "3-6 months", "value": "3_6_months"}, {"label": "6-12 months", "value": "6_12_months"}, {"label": "Over 1 year", "value": "over_1_year"}]}, "type": "select", "input": true, "label": "How long did you use Trifarotene?", "tableView": true, "widget": "html5", "validate": {"required": true}, "confirm_label": "Trifarotene Duration of Use:", "customConditional": "show = data.prescription_retinoids_used?.trifarotene === true;"}, {"key": "trifarotene_strength_form", "tableView": true, "type": "selectboxes", "input": true, "label": "Which formulation of Trifarotene have you used?", "values": [{"label": "0.005% Cream (Aklief)", "value": "0_005_cream"}, {"label": "Other", "value": "other"}], "validate": {"required": true}, "confirm_label": "Trifarotene Formulation:", "customConditional": "show = data.prescription_retinoids_used?.trifarotene === true;"}, {"key": "trifarotene_use_pattern", "tableView": true, "data": {"values": [{"label": "Used daily at night for anti-aging or acne", "value": "daily_use"}, {"label": "Used a few times per week to reduce irritation", "value": "intermittent_use"}, {"label": "Used periodically during acne flares", "value": "flare_based_use"}, {"label": "Used seasonally", "value": "seasonal_use"}, {"label": "Started and stopped multiple times", "value": "on_off_cycles"}, {"label": "Other", "value": "other"}]}, "type": "select", "input": true, "label": "What was your typical pattern of Trifarotene use?", "widget": "html5", "validate": {"required": true}, "confirm_label": "Trifarotene Use Pattern:", "customConditional": "show = data.prescription_retinoids_used?.trifarotene === true;"}, {"key": "trifarotene_use_pattern_other", "tableView": true, "type": "textarea", "input": true, "label": "Please describe your Trifarotene use pattern:", "placeholder": "e.g., Short cycles due to irritation, used after moisturizer, etc.", "customConditional": "show = data.trifarotene_use_pattern === 'other';"}, {"key": "trifarotene_side_effects", "tableView": true, "type": "selectboxes", "input": true, "label": "Did you experience any side effects from Trifarotene?", "values": [{"label": "Redness", "value": "redness"}, {"label": "Irritation", "value": "irritation"}, {"label": "<PERSON>ing", "value": "peeling"}, {"label": "Burning", "value": "burning"}, {"label": "Dryness", "value": "dryness"}, {"label": "None", "value": "none"}], "confirm_label": "Trifarotene Side Effects:", "customConditional": "show = data.prescription_retinoids_used?.trifarotene === true;"}, {"key": "trifarotene_tolerance", "tableView": true, "type": "radio", "input": true, "label": "Did you tolerate Trifar<PERSON><PERSON> well?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "not_sure"}, {"label": "Stopped due to side effects", "value": "stopped_due_to_side_effects"}], "confirm_label": "Trifarotene Tolerance:", "customConditional": "show = data.prescription_retinoids_used?.trifarotene === true;"}, {"key": "trifarotene_side_effects_management", "tableView": true, "type": "radio", "input": true, "label": "How did you manage the side effects from Trifarotene?", "values": [{"label": "Mild and manageable - continued treatment", "value": "manageable"}, {"label": "Moderate - adjusted frequency or dose", "value": "adjusted_dose"}, {"label": "Severe - had to stop treatment", "value": "discontinued"}, {"label": "Other", "value": "other"}], "validate": {"required": true}, "confirm_label": "Trifarotene Side Effect Management:", "customConditional": "show = data.prescription_retinoids_used?.trifarotene === true && _.some(_.values(data.trifarotene_side_effects), Boolean) && !data.trifarotene_side_effects.none;"}, {"key": "heading_skin_care_routine", "html": "<h2><strong>Daily Skin Care Routine</strong></h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "sun_exposure", "type": "radio", "input": true, "label": "Have you had a lot of sun exposure without the use of sunscreen in the past?", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "confirm_label": "Sun Exposure History:", "optionsLabelPosition": "right"}, {"key": "spf_use", "type": "radio", "input": true, "label": "Please select your SPF (sun protection factor) usage habits:", "values": [{"label": "I use SPF daily, regardless of weather or indoor/outdoor activities", "value": "daily_use"}, {"label": "I use SPF only on sunny days", "value": "sunny_days_only"}, {"label": "I use SPF primarily during outdoor activities", "value": "outdoor_activities"}, {"label": "I seldom use SPF", "value": "seldom_use"}, {"label": "I never use SPF", "value": "never_use"}], "validate": {"required": true}, "tableView": true, "confirm_label": "SPF Usage Habits:", "optionsLabelPosition": "right"}, {"key": "spf_use_advice_alert", "html": "<div style='border-left: 4px solid #28a745; background-color: #d4edda; padding: 10px; margin-bottom: 10px;'><strong>Recommendation:</strong> Regular sunscreen (SPF) use is one of the most effective ways to prevent photoaging, including wrinkles, pigmentation, and skin roughness. It also reduces your risk of skin cancer.</div>", "type": "content", "input": false, "customConditional": "show = data.spf_use === 'seldom_use' || data.spf_use === 'never_use';"}, {"key": "spf_use_advice_understanding", "type": "radio", "input": true, "label": "Do you understand the importance of using sunscreen regularly to protect your skin and reduce your risk of sun damage?", "values": [{"label": "I understand", "value": "understand"}, {"label": "I do not understand and need more counselling", "value": "do_not_understand"}], "validate": {"required": true}, "tableView": true, "confirm_label": "SPF Use Understanding:", "customConditional": "show = data.spf_use === 'seldom_use' || data.spf_use === 'never_use';", "optionsLabelPosition": "right"}, {"key": "spf_strength", "type": "radio", "input": true, "label": "What SPF (sun protection factor) strength do you typically use?</br></br><ul><li><strong>SPF 30 or higher</strong> is recommended for daily protection against premature aging and skin cancer.</li><li>If you're not sure, check the label on your sunscreen product—it's usually printed on the front of the bottle.</li></ul>", "values": [{"label": "SPF 15 or below", "value": "spf_15_below"}, {"label": "SPF 16–29", "value": "spf_16_29"}, {"label": "SPF 30–49", "value": "spf_30_49"}, {"label": "SPF 50 or higher", "value": "spf_50_above"}, {"label": "I'm not sure", "value": "spf_not_sure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "SPF Strength Used:", "customConditional": "show = data.spf_use === 'daily_use' || data.spf_use === 'sunny_days_only' || data.spf_use === 'outdoor_activities';", "optionsLabelPosition": "right"}, {"key": "tanning_bed_use", "type": "radio", "input": true, "label": "Have you ever used tanning beds?", "values": [{"label": "Yes, I currently use them", "value": "current_use", "shortcut": ""}, {"label": "Yes, I used them in the past", "value": "past_use", "shortcut": ""}, {"label": "No, I have never used them", "value": "never_used", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "confirm_label": "Tanning Bed Use History:", "optionsLabelPosition": "right"}, {"key": "tanning_bed_warning_box", "html": "<div style='border-left: 4px solid #dc3545; background-color: #f8d7da; padding: 10px; margin-bottom: 10px;'><strong>Warning:</strong> Tanning bed use accelerates photoaging, increases wrinkle formation, and significantly raises the risk of skin cancer. It is strongly recommended to avoid tanning beds to protect your skin and overall health.</div>", "type": "content", "input": false, "customConditional": "show = data.tanning_bed_use === 'current_use' || data.tanning_bed_use === 'past_use';"}, {"key": "tanning_bed_advice_understanding", "type": "radio", "input": true, "label": "Do you understand why it's best to avoid tanning beds to reduce skin damage and prevent photoaging?", "values": [{"label": "I understand", "value": "understand"}, {"label": "I do not understand", "value": "do_not_understand"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Tanning Bed Use Understanding:", "customConditional": "show = data.tanning_bed_use === 'current_use' || data.tanning_bed_use === 'past_use';", "optionsLabelPosition": "right"}, {"key": "heading_family_history", "html": "<h2><strong>Family History</strong></h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "family_history_skin_cancer", "type": "selectboxes", "input": true, "label": "Do you have a family history of any skin cancer (i.e., melanoma, basal cell skin cancer, squamous cell skin cancer), or have had skin cancer yourself? (Select all that apply)", "inline": false, "values": [{"label": "Melanoma", "value": "melanoma"}, {"label": "Basal cell skin cancer", "value": "basal_cell"}, {"label": "Squamous cell skin cancer", "value": "squamous_cell"}, {"label": "Family member with an unknown type of skin cancer", "value": "skin_cancer_nyd"}, {"label": "I have not had skin cancer or know of family members with skin cancer", "value": "none"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Family History of Skin Cancer:", "optionsLabelPosition": "right"}, {"key": "annual_skin_exam_warning", "html": "<div style='border-left: 4px solid #f5c518; background-color: #fff3cd; padding: 10px; margin-bottom: 10px;'><strong>Recommendation:</strong> Based on your family history, we recommend an annual full-body skin examination with your doctor. You should also book an earlier appointment if you notice any new moles or changes in shape, colour, size, or texture.</div>", "type": "content", "input": false, "customConditional": "show = data.family_history_skin_cancer?.melanoma || data.family_history_skin_cancer?.basal_cell || data.family_history_skin_cancer?.squamous_cell || data.family_history_skin_cancer?.skin_cancer_nyd;"}, {"key": "annual_skin_exam_understanding", "type": "radio", "input": true, "label": "Do you understand the importance of an annual skin exam and monitoring any new or changing moles?", "values": [{"label": "I understand", "value": "understand"}, {"label": "I do not understand", "value": "do_not_understand"}], "validate": {"required": true}, "confirm_label": "Understands Skin Cancer Screening Advice:", "tableView": true, "customConditional": "show = data.family_history_skin_cancer?.melanoma || data.family_history_skin_cancer?.basal_cell || data.family_history_skin_cancer?.squamous_cell || data.family_history_skin_cancer?.skin_cancer_nyd;"}, {"key": "skin_cancer_concern", "type": "radio", "input": true, "label": "Are you currently concerned that you may have skin cancer?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "I'm not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Concerned About Skin Cancer:", "optionsLabelPosition": "right"}, {"key": "mole_changes", "type": "radio", "input": true, "label": "Have you noticed any moles or skin spots that have changed or appear unusual in any of the following ways?</br></br><ul><li><strong>Asymmetry</strong>: One half doesn't match the other</li><li><strong>Border irregularity</strong>: Edges are uneven or blurred</li><li><strong>Colour changes</strong>: Multiple colours or darkening</li><li><strong>Diameter</strong>: Larger than 6mm (about the size of a pencil eraser)</li><li><strong>Evolution</strong>: The spot is growing, itching, bleeding, or changing in any way</li></ul>", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "I'm not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Noticed Changes in Moles or Skin Spots:", "optionsLabelPosition": "right"}, {"key": "combined_skin_cancer_warning", "html": "<div style='border-left: 4px solid #dc3545; background-color: #f8d7da; padding: 10px; margin-bottom: 10px;'><strong>Important:</strong> If you're concerned about skin cancer or have noticed any new or changing moles, we strongly recommend seeing a doctor in person within the next 7 days for a full skin exam. Early detection is essential for timely diagnosis and treatment.</div>", "type": "content", "input": false, "customConditional": "show = (data.skin_cancer_concern === 'yes' || data.skin_cancer_concern === 'not_sure' || data.mole_changes === 'yes' || data.mole_changes === 'not_sure');"}, {"key": "skin_exam_urgency_understanding", "type": "radio", "input": true, "label": "Do you understand why it's important to book an in-person skin exam as soon as possible?", "values": [{"label": "I understand", "value": "understand"}, {"label": "I do not understand", "value": "do_not_understand"}], "validate": {"required": true}, "confirm_label": "Understands Need for Skin Exam:", "tableView": true, "customConditional": "show = (data.skin_cancer_concern === 'yes' || data.skin_cancer_concern === 'not_sure' || data.mole_changes === 'yes' || data.mole_changes === 'not_sure');"}, {"key": "photo_upload_header", "html": "<h2>Photo Upload</h2><p>Please ensure you upload a clear photo of all areas where you wish to apply the cream. Providing photos will expedite care.</p>", "type": "content", "input": false, "label": "Content"}, {"key": "uploadUrl", "url": "/app/q/{qpk}/formio-files/{pk}/", "type": "file", "image": true, "input": true, "label": "Upload: URL", "webcam": true, "capture": "user", "storage": "url", "multiple": true, "fileTypes": [{"label": "", "value": ""}], "tableView": true, "validateWhenHidden": false}, {"key": "confirm_photo_upload", "type": "radio", "input": true, "label": "I've uploaded photos of all areas I wish to treat", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Photo Upload Confirmation:", "optionsLabelPosition": "right"}, {"key": "heading_pregnancy_retinoids", "html": "<h3><strong>Pregnancy and Retinoids</strong></h3>", "type": "content", "input": false, "customConditional": "show = data.sex == 'female';"}, {"key": "currently_pregnant", "type": "radio", "input": true, "label": "Are you currently pregnant?", "values": [{"label": "Yes/Not Sure", "value": true}, {"label": "No", "value": false}], "validate": {"required": true}, "confirm_label": "Currently Pregnant:", "adminFlag": true, "tableView": true, "customConditional": "show = data.sex == 'female';"}, {"key": "planning_pregnancy", "type": "radio", "input": true, "label": "Are you planning to become pregnant within the next 6 months?", "values": [{"label": "Yes", "value": true}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "not_sure"}], "validate": {"required": true}, "confirm_label": "Planning Pregnancy:", "adminFlag": true, "tableView": true, "customConditional": "show = data.sex == 'female';"}, {"key": "currently_breastfeeding", "type": "radio", "input": true, "label": "Are you currently breastfeeding?", "values": [{"label": "Yes", "value": true}, {"label": "No", "value": false}], "validate": {"required": true}, "confirm_label": "Currently Breastfeeding:", "adminFlag": true, "tableView": true, "customConditional": "show = data.sex == 'female';"}, {"key": "retinoid_breastfeeding_warning", "html": "<div style='border-left: 4px solid #f5c518; background-color: #fff3cd; padding: 10px; margin-bottom: 10px;'><strong>Recommendation:</strong> During breastfeeding, the use of topical retinoids is considered to carry a low risk to the nursing infant due to limited absorption into the bloodstream. However, safety data is limited. To minimize potential risk, avoid applying the cream to areas where your baby may have direct contact, such as the chest.</div>", "type": "content", "input": false, "customConditional": "show = data.sex === 'female' && data.currently_breastfeeding === true;"}, {"key": "retinoid_risk_understanding", "type": "radio", "input": true, "label": "Do you understand this recommendation?", "values": [{"label": "I understand", "value": "understand"}, {"label": "I do not understand", "value": "do_not_understand"}], "validate": {"required": true}, "confirm_label": "Understands Retinoid Breastfeeding Guidance:", "tableView": true, "customConditional": "show = data.sex === 'female' && data.currently_breastfeeding === true;"}, {"key": "intake_kind", "type": "textfield", "input": true, "label": "Intake Kind", "hidden": true, "disabled": true, "tableView": true, "clearOnHide": false, "calculateValue": "value = !data.intake_denial_reasons?.length ? 'mail' : 'chat';"}, {"key": "intake_mt_key", "type": "textfield", "input": true, "label": "Intake Mail Template Key", "hidden": true, "disabled": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.intake_kind=='mail'?'intake-wrinkle-treatment':'intake-denial'"}, {"key": "allowed_rt_keys", "type": "textfield", "input": true, "label": "Allowed Rep<PERSON>", "hidden": true, "disabled": true, "multiple": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.intake_mt_key=='intake-denial'?[]:['get-rx','appointment-intake','edit-intake']"}]}