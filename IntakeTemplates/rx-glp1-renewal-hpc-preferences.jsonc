{"components": [{"key": "glp1_medication__preferencesheader", "html": "<h2 style=\"text-align:center;\">Medication&nbsp;Preferences</h2>\n<p>Please tell us which dose you’d like moving forward. Your choice guides the prescription we prepare and helps us tailor your treatment plan.</p>\n<ul style=\"margin-left:1.25rem;line-height:1.55;\">\n  <li><strong>Stay at the same dose</strong> if weight-loss is steady (≈ 1–2 lb / week) and side-effects are mild.</li>\n  <li><strong>Step up one level</strong> only when weight-loss has slowed (&lt; 1 lb / week) for ≥ 4 weeks <em>and</em> you’re tolerating the current dose.</li>\n  <li><strong>Consider a lower dose</strong> if you’re losing &gt; 2 lb / week, your BMI is &lt; 20, or side-effects feel hard to manage.</li>\n  <li>You can open a <em>secure chat</em> with your TeleTest prescriber after submitting this form to discuss any concerns or to adjust your plan later.</li>\n</ul>", "type": "content", "input": false, "label": "Content"}, {"key": "summary_callout", "html": "<div style=\"background:#e8f4ff;border-left:4px solid #3399ff;padding:16px;border-radius:6px;\">\n  <h4 style=\"margin:0 0 0.75rem 0;text-align:center;\">Here's what you told us</h4>\n  <ul style=\"margin:0 0 0 1rem;line-height:1.6;\">\n    {{ (() => {\n         // map dose-value → display-label\n         const map = {\n           'wegovy-starting-12-weeks-025mg':'0.25 mg weekly',\n           'wegovy-starting-12-weeks-05mg' :'0.5 mg weekly',\n           'wegovy-starting-12-weeks-1mg'  :'1.0 mg weekly',\n           'wegovy-starting-12-weeks-17mg' :'1.7 mg weekly',\n           'wegovy-starting-12-weeks-24mg' :'2.4 mg weekly',\n\n           'ozempic-025mg-12-weeks':'0.25 mg weekly',\n           'ozempic-05mg-12-weeks' :'0.5 mg weekly',\n           'ozempic-1mg-12-weeks'  :'1.0 mg weekly',\n           'ozempic-2mg-12-weeks'  :'2.0 mg weekly',\n\n           'rybelsus-3mg-12-weeks' :'3 mg daily',\n           'rybelsus-7mg-12-weeks' :'7 mg daily',\n           'rybelsus-14mg-12-weeks':'14 mg daily',\n\n           'mounjaro-25mg-12-weeks' :'2.5 mg weekly',\n           'mounjaro-5mg-12-weeks'  :'5 mg weekly',\n           'mounjaro-75mg-12-weeks' :'7.5 mg weekly',\n           'mounjaro-10mg-12-weeks' :'10 mg weekly',\n           'mounjaro-125mg-12-weeks':'12.5 mg weekly',\n           'mounjaro-15mg-12-weeks' :'15 mg weekly',\n\n           'saxenda-6mg-12-weeks' :'0.6 mg daily',\n           'saxenda-12mg-12-weeks':'1.2 mg daily',\n           'saxenda-18mg-12-weeks':'1.8 mg daily',\n           'saxenda-24mg-12-weeks':'2.4 mg daily',\n           'saxenda-3mg-12-weeks' :'3.0 mg daily'\n         };\n         const med   = (data.current_glp1_medication||'').toLowerCase();\n         const using = data[med + '_use'] === 'currently_using';\n         if (!med || med==='none' || !using) return '';\n         const doseCode = data[med + '_current_dose'];\n         const doseLbl  = map[doseCode] || doseCode || '';\n         return `<li>Medication: <strong>${_.startCase(med)} – ${doseLbl}</strong></li>`;\n       })() }}\n\n    {{ (data.weightValue && data.weightUnits) ? '<li>Current weight: <strong>' + data.weightValue + ' ' + data.weightUnits + '</strong></li>' : '' }}\n\n    {{ (data.heightValueCM && data.heightValueCM !== '') ? '<li>Height: <strong>' + data.heightValueCM + ' cm</strong></li>' : ((data.heightFeet || data.heightInches) ? '<li>Height: <strong>' + (data.heightFeet || '') + '\\'' + (data.heightInches || '') + '\"</strong></li>' : '') }}\n\n    {{ data.bmi ? '<li>BMI: <strong>' + data.bmi + '</strong></li>' : '' }}\n\n    <li>Side-effects: <strong>{{ _.startCase(data.side_effect_tolerance) }}</strong></li>\n    <li>Weekly weight-loss over the last 4 weeks: <strong>{{ data.weekly_loss_lbs_per_week }} lb / week</strong></li>\n    <li>At desired weight goal: <strong>{{ data.desired_weight_achieved }}</strong></li>\n    {{ data.desired_weight_achieved === 'yes' ? '<li>Plan: <strong>' + _.startCase(data.desired_weight_plan || 'still deciding') + '</strong></li>' : '' }}\n  </ul>\n</div>", "type": "content", "input": false, "tableView": true, "label": "Summary Call-out", "refreshOnChange": true, "customConditional": "show = !!data.weekly_loss_lbs_per_week && !!data.side_effect_tolerance && !!data.desired_weight_achieved;"}, {"key": "summary_confirm", "type": "radio", "input": true, "label": "Does all of the information above look correct?", "values": [{"label": "Yes, it looks right", "value": "yes"}, {"label": "No, I need to revise it", "value": "no"}], "validate": {"required": true}, "tableView": true, "refreshOnChange": true}, {"key": "summary_revision_prompt", "html": "<div style=\"background:#fff3cd;border-left:4px solid #ffb300;padding:12px 16px;border-radius:6px;\">Please click the <strong>Back</strong> button (or use the section list) to review and correct your answers, then return here.</div>", "type": "content", "input": false, "label": "Revision Prompt", "customConditional": "show = data.summary_confirm === 'no';"}, {"key": "dose_recommendation_code", "type": "textfield", "input": true, "hidden": true, "tableView": false, "clearOnHide": false, "calculateValue": "(() => {\n  const bmi      = parseFloat(data.bmi) || 0;\n  const loss     = parseFloat(data.weekly_loss_lbs_per_week) || 0;\n  const sx       = data.side_effect_tolerance;          // tolerable | borderline | intolerable\n  const achieved = data.desired_weight_achieved === 'yes';\n  const plan     = data.desired_weight_plan;            // taper | maintain | not_sure (when achieved=yes)\n\n  let code = 'maintain_default';  // safe default\n\n  if (sx === 'intolerable')                 code = 'reduce_sx';\n  else if (bmi && bmi < 20)                 code = 'reduce_low_bmi';\n  else if (loss > 2)                        code = 'reduce_fast';\n  else if (sx === 'borderline' || plan === 'taper')\n                                           code = 'hold_or_reduce';\n  // ▲ NEW safeguard: only suggest increase if NOT at goal\n  else if (!achieved && loss < 1 && sx === 'tolerable')\n                                           code = 'increase_slow';\n  // otherwise keep default maintain\n\n  value = code;\n})()", "refreshOnChange": true}, {"key": "rec_severe_sx_reduce", "html": "<div style=\"background:#e6f7ec;border-left:4px solid #28a745;padding:12px 16px;border-radius:6px;\"><strong>Our advice:</strong> You're experiencing <strong>severe side-effects</strong>; lower your dose until symptoms ease.</div>", "type": "content", "input": false, "label": "Recommendation – Severe Side-Effects", "tableView": false, "customConditional": "show = (data.summary_confirm === 'yes') && (data.dose_recommendation_code === 'reduce_sx');"}, {"key": "rec_low_bmi_reduce", "html": "<div style=\"background:#e6f7ec;border-left:4px solid #28a745;padding:12px 16px;border-radius:6px;\"><strong>Our advice:</strong> Your BMI is below 20. A smaller or tapered dose is safest.</div>", "type": "content", "input": false, "label": "Recommendation – Low BMI", "tableView": false, "customConditional": "show = (data.summary_confirm === 'yes') && (data.dose_recommendation_code === 'reduce_low_bmi');"}, {"key": "rec_fast_loss_reduce", "html": "<div style=\"background:#e6f7ec;border-left:4px solid #28a745;padding:12px 16px;border-radius:6px;\"><strong>Our advice:</strong> You're losing weight faster than 2&nbsp;lb / week. Dropping to a lower dose can reduce gall-stone risk and protect lean muscle.</div>", "type": "content", "input": false, "label": "Recommendation – Rapid Weight-Loss", "tableView": false, "customConditional": "show = (data.summary_confirm === 'yes') && (data.dose_recommendation_code === 'reduce_fast');"}, {"key": "rec_moderate_no_increase", "html": "<div style=\"background:#e6f7ec;border-left:4px solid #28a745;padding:12px 16px;border-radius:6px;\"><strong>Our advice:</strong> Because you've reported <strong>moderate side-effects</strong> or plan to taper, we do <em>not</em> recommend increasing your dose. Holding steady or reducing is preferable.</div>", "type": "content", "input": false, "label": "Recommendation – <PERSON>", "tableView": false, "customConditional": "show = (data.summary_confirm === 'yes') && (data.dose_recommendation_code === 'hold_or_reduce');"}, {"key": "rec_slow_loss_increase", "html": "<div style=\"background:#e6f7ec;border-left:4px solid #28a745;padding:12px 16px;border-radius:6px;\"><strong>Our advice:</strong> Weight-loss has slowed to under 1&nbsp;lb / week and side-effects are mild—consider stepping up one dose level.</div>", "type": "content", "input": false, "label": "Recommendation – Slow Progress", "tableView": false, "customConditional": "show = (data.summary_confirm === 'yes') && (data.dose_recommendation_code === 'increase_slow');"}, {"key": "rec_default_maintain", "html": "<div style=\"background:#e6f7ec;border-left:4px solid #28a745;padding:12px 16px;border-radius:6px;\"><strong>Our advice:</strong> Your current dose looks appropriate. Keep it steady and check in again in 4 weeks.</div>", "type": "content", "input": false, "label": "Recommendation – Maintain", "tableView": false, "customConditional": "show = (data.summary_confirm === 'yes') && (data.dose_recommendation_code === 'maintain_default');"}, {"key": "dose_education_box", "html": "<div style=\"background:#e6f4ff;border-left:4px solid #3399ff;padding:12px 16px;border-radius:6px;\"><h4>Need help deciding?</h4><p><strong>Stay</strong> if weight-loss is steady and side-effects are mild.<br/><strong>Increase</strong> if loss has stalled for&nbsp;&gt;4&nbsp;weeks and you're tolerating the dose.<br/><strong>Decrease / taper</strong> for troublesome side-effects or after reaching goal.</p><p class=\"mb-0\">If unsure, staying put is safest — you can adjust later.</p></div>", "type": "content", "input": false, "label": "Dose Education Box", "tableView": false, "customConditional": "show = (data.summary_confirm === 'yes') && (data.preferred_dose_renewal === 'not_sure');"}, {"key": "wegovy_preferences_section", "html": "<h3>Wegovy (Semaglutide)</h3>", "type": "content", "input": false, "customConditional": "show = (data.summary_confirm === 'yes') && (data.wegovy_use === 'currently_using');"}, {"key": "wegovy_confirm_preferred_dose", "type": "radio", "input": true, "label": "Please confirm your preferred dose:", "values": [{"label": "0.25 mg weekly", "value": "wegovy-starting-12-weeks-025mg"}, {"label": "0.5 mg weekly", "value": "wegovy-starting-12-weeks-05mg"}, {"label": "1.0 mg weekly", "value": "wegovy-starting-12-weeks-1mg"}, {"label": "1.7 mg weekly", "value": "wegovy-starting-12-weeks-17mg"}, {"label": "2.4 mg weekly (maximum)", "value": "wegovy-starting-12-weeks-24mg"}, {"label": "I am not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "<PERSON><PERSON><PERSON> Preferred Dose:", "customConditional": "show = (data.summary_confirm === 'yes') && (data.wegovy_use === 'currently_using');"}, {"key": "ozempic_preferences_section", "html": "<h3>Ozempic (Semaglutide)</h3>", "type": "content", "input": false, "customConditional": "show = (data.summary_confirm === 'yes') && (data.ozempic_use === 'currently_using');"}, {"key": "ozempic_confirm_preferred_dose", "type": "radio", "input": true, "label": "Please confirm your preferred dose:", "values": [{"label": "0.25 mg weekly", "value": "ozempic-025mg-12-weeks"}, {"label": "0.5 mg weekly", "value": "ozempic-05mg-12-weeks"}, {"label": "1.0 mg weekly", "value": "ozempic-1mg-12-weeks"}, {"label": "2.0 mg weekly (maximum)", "value": "ozempic-2mg-12-weeks"}, {"label": "I am not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "<PERSON><PERSON><PERSON> Preferred Dose:", "customConditional": "show = (data.summary_confirm === 'yes') && (data.ozempic_use === 'currently_using');"}, {"key": "rybelsus_preferences_section", "html": "<h3><PERSON><PERSON><PERSON><PERSON> (Semaglutide)</h3>", "type": "content", "input": false, "customConditional": "show = (data.summary_confirm === 'yes') && (data.rybelsus_use === 'currently_using');"}, {"key": "rybelsus_confirm_preferred_dose", "type": "radio", "input": true, "label": "Please confirm your preferred dose:", "values": [{"label": "3 mg daily", "value": "rybelsus-3mg-12-weeks"}, {"label": "7 mg daily", "value": "rybelsus-7mg-12-weeks"}, {"label": "14 mg daily", "value": "rybelsus-14mg-12-weeks"}, {"label": "I am not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "<PERSON><PERSON><PERSON><PERSON> Preferred Dose:", "customConditional": "show = (data.summary_confirm === 'yes') && (data.rybelsus_use === 'currently_using');"}, {"key": "mounjaro_preference_section", "html": "<h3><PERSON><PERSON><PERSON><PERSON> (Tirzepatide)</h3>", "type": "content", "input": false, "customConditional": "show = (data.summary_confirm === 'yes') && (data.mounjaro_use === 'currently_using');"}, {"key": "mounjaro_confirm_preferred_dose", "type": "radio", "input": true, "label": "Please confirm your preferred dose:", "values": [{"label": "2.5 mg weekly", "value": "mounjaro-25mg-12-weeks"}, {"label": "5 mg weekly", "value": "mounjaro-5mg-12-weeks"}, {"label": "7.5 mg weekly", "value": "mounjaro-75mg-12-weeks"}, {"label": "10 mg weekly", "value": "mounjaro-10mg-12-weeks"}, {"label": "12.5 mg weekly", "value": "mounjaro-125mg-12-weeks"}, {"label": "15 mg weekly (maximum)", "value": "mounjaro-15mg-12-weeks"}, {"label": "I am not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "<PERSON><PERSON><PERSON><PERSON> Preferred Dose:", "customConditional": "show = (data.summary_confirm === 'yes') && (data.mounjaro_use === 'currently_using');"}, {"key": "saxenda_preference_section", "html": "<h3><PERSON><PERSON><PERSON> (Liraglutide)</h3>", "type": "content", "input": false, "customConditional": "show = (data.summary_confirm === 'yes') && (data.saxenda_use === 'currently_using');"}, {"key": "saxenda_confirm_preferred_dose", "type": "radio", "input": true, "label": "Please confirm your preferred dose:", "values": [{"label": "0.6 mg daily", "value": "saxenda-6mg-12-weeks"}, {"label": "1.2 mg daily", "value": "saxenda-12mg-12-weeks"}, {"label": "1.8 mg daily", "value": "saxenda-18mg-12-weeks"}, {"label": "2.4 mg daily", "value": "saxenda-24mg-12-weeks"}, {"label": "3.0 mg daily (maximum)", "value": "saxenda-3mg-12-weeks"}, {"label": "I am not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "<PERSON><PERSON><PERSON> Preferred Dose:", "customConditional": "show = (data.summary_confirm === 'yes') && (data.saxenda_use === 'currently_using');"}, {"key": "dose_change_final", "type": "textfield", "input": true, "hidden": true, "tableView": false, "persistent": false, "calculateValue": "(() => {\n  const map = {\n    'wegovy-starting-12-weeks-025mg':0.25,'wegovy-starting-12-weeks-05mg':0.5,'wegovy-starting-12-weeks-1mg':1,'wegovy-starting-12-weeks-17mg':1.7,'wegovy-starting-12-weeks-24mg':2.4,\n    'ozempic-025mg-12-weeks':0.25,'ozempic-05mg-12-weeks':0.5,'ozempic-1mg-12-weeks':1,'ozempic-2mg-12-weeks':2,\n    'rybelsus-3mg-12-weeks':3,'rybelsus-7mg-12-weeks':7,'rybelsus-14mg-12-weeks':14,\n    'mounjaro-25mg-12-weeks':2.5,'mounjaro-5mg-12-weeks':5,'mounjaro-75mg-12-weeks':7.5,'mounjaro-10mg-12-weeks':10,'mounjaro-125mg-12-weeks':12.5,'mounjaro-15mg-12-weeks':15,\n    'saxenda-6mg-12-weeks':0.6,'saxenda-12mg-12-weeks':1.2,'saxenda-18mg-12-weeks':1.8,'saxenda-24mg-12-weeks':2.4,'saxenda-3mg-12-weeks':3\n  };\n  const med = (data.current_glp1_medication||'').toLowerCase();\n  if (!med || data[med + '_use']!=='currently_using') return '';\n  const cur = map[data[med + '_current_dose']];\n  const nxt = map[data[med + '_confirm_preferred_dose']];\n  if (!(cur&&nxt)) return '';\n  if (nxt>cur) return 'higher';\n  if (nxt<cur) return 'lower';\n  return 'same';\n})()", "refreshOnChange": true}, {"key": "dose_override_reason", "type": "selectboxes", "input": true, "label": "Why do you want to stay at (or increase to) this dose even though we advised a lower dose?<br><small>(Select all that apply)</small>", "values": [{"label": "My hunger has returned", "value": "hunger_returned"}, {"label": "Weight-loss has slowed recently", "value": "slowed_loss"}, {"label": "Current side-effects feel manageable", "value": "side_effects_ok"}, {"label": "Previous clinician suggested this dose", "value": "clinician_advised"}, {"label": "Other reason", "value": "other"}], "validate": {"required": true}, "confirm_label": "Reason for overriding advice:", "refreshOnChange": true, "customConditional": "show = (data.summary_confirm==='yes') && ['reduce_fast','reduce_low_bmi','reduce_sx'].includes(data.dose_recommendation_code) && (data.dose_change_final==='same' || data.dose_change_final==='higher');"}, {"key": "dose_override_other_text", "type": "textfield", "input": true, "label": "Please specify your other reason:", "validate": {"required": true}, "refreshOnChange": true, "customConditional": "show = !!(data.dose_override_reason && data.dose_override_reason.other);"}, {"key": "ack_same_checkbox", "type": "checkbox", "input": true, "label": "I’m happy to stay on the <strong class=\"text-success\">same</strong> dose.", "validate": {"required": true}, "customClass": "mt-3", "confirm_label": "Confirms dose is the same as previous:", "refreshOnChange": true, "customConditional": "show = (() => {\n  if (data.summary_confirm !== 'yes') return false;\n  const med = (data.current_glp1_medication || '').toLowerCase();\n  if (!med || data[med + '_use'] !== 'currently_using') return false;\n  const map = {\n    'wegovy-starting-12-weeks-025mg':0.25,'wegovy-starting-12-weeks-05mg':0.5,'wegovy-starting-12-weeks-1mg':1,'wegovy-starting-12-weeks-17mg':1.7,'wegovy-starting-12-weeks-24mg':2.4,\n    'ozempic-025mg-12-weeks':0.25,'ozempic-05mg-12-weeks':0.5,'ozempic-1mg-12-weeks':1,'ozempic-2mg-12-weeks':2,\n    'rybelsus-3mg-12-weeks':3,'rybelsus-7mg-12-weeks':7,'rybelsus-14mg-12-weeks':14,\n    'mounjaro-25mg-12-weeks':2.5,'mounjaro-5mg-12-weeks':5,'mounjaro-75mg-12-weeks':7.5,'mounjaro-10mg-12-weeks':10,'mounjaro-125mg-12-weeks':12.5,'mounjaro-15mg-12-weeks':15,\n    'saxenda-6mg-12-weeks':0.6,'saxenda-12mg-12-weeks':1.2,'saxenda-18mg-12-weeks':1.8,'saxenda-24mg-12-weeks':2.4,'saxenda-3mg-12-weeks':3\n  };\n  const cur = map[data[med + '_current_dose']];\n  const pref = map[data[med + '_confirm_preferred_dose']];\n  return !!cur && !!pref && pref === cur;\n})();"}, {"key": "ack_higher_checkbox", "type": "checkbox", "input": true, "label": "I’ve chosen a <strong class=\"text-success\">higher</strong> dose — I know this may raise the chance of side-effects.", "validate": {"required": true}, "customClass": "mt-3", "confirm_label": "Confirms dose is higher than previous:", "refreshOnChange": true, "customConditional": "show = (() => {\n  if (data.summary_confirm !== 'yes') return false;\n  const med = (data.current_glp1_medication || '').toLowerCase();\n  if (!med || data[med + '_use'] !== 'currently_using') return false;\n  const map = {\n    'wegovy-starting-12-weeks-025mg':0.25,'wegovy-starting-12-weeks-05mg':0.5,'wegovy-starting-12-weeks-1mg':1,'wegovy-starting-12-weeks-17mg':1.7,'wegovy-starting-12-weeks-24mg':2.4,\n    'ozempic-025mg-12-weeks':0.25,'ozempic-05mg-12-weeks':0.5,'ozempic-1mg-12-weeks':1,'ozempic-2mg-12-weeks':2,\n    'rybelsus-3mg-12-weeks':3,'rybelsus-7mg-12-weeks':7,'rybelsus-14mg-12-weeks':14,\n    'mounjaro-25mg-12-weeks':2.5,'mounjaro-5mg-12-weeks':5,'mounjaro-75mg-12-weeks':7.5,'mounjaro-10mg-12-weeks':10,'mounjaro-125mg-12-weeks':12.5,'mounjaro-15mg-12-weeks':15,\n    'saxenda-6mg-12-weeks':0.6,'saxenda-12mg-12-weeks':1.2,'saxenda-18mg-12-weeks':1.8,'saxenda-24mg-12-weeks':2.4,'saxenda-3mg-12-weeks':3\n  };\n  const cur = map[data[med + '_current_dose']];\n  const pref = map[data[med + '_confirm_preferred_dose']];\n  return !!cur && !!pref && pref > cur;\n})();"}, {"key": "ack_lower_checkbox", "type": "checkbox", "input": true, "label": "I’ve chosen a <strong class=\"text-success\">lower</strong> dose — I understand it might be less effective.", "validate": {"required": true}, "customClass": "mt-3", "confirm_label": "Confirms dose is lower than previous:", "refreshOnChange": true, "customConditional": "show = (() => {\n  if (data.summary_confirm !== 'yes') return false;\n  const med = (data.current_glp1_medication || '').toLowerCase();\n  if (!med || data[med + '_use'] !== 'currently_using') return false;\n  const map = {\n    'wegovy-starting-12-weeks-025mg':0.25,'wegovy-starting-12-weeks-05mg':0.5,'wegovy-starting-12-weeks-1mg':1,'wegovy-starting-12-weeks-17mg':1.7,'wegovy-starting-12-weeks-24mg':2.4,\n    'ozempic-025mg-12-weeks':0.25,'ozempic-05mg-12-weeks':0.5,'ozempic-1mg-12-weeks':1,'ozempic-2mg-12-weeks':2,\n    'rybelsus-3mg-12-weeks':3,'rybelsus-7mg-12-weeks':7,'rybelsus-14mg-12-weeks':14,\n    'mounjaro-25mg-12-weeks':2.5,'mounjaro-5mg-12-weeks':5,'mounjaro-75mg-12-weeks':7.5,'mounjaro-10mg-12-weeks':10,'mounjaro-125mg-12-weeks':12.5,'mounjaro-15mg-12-weeks':15,\n    'saxenda-6mg-12-weeks':0.6,'saxenda-12mg-12-weeks':1.2,'saxenda-18mg-12-weeks':1.8,'saxenda-24mg-12-weeks':2.4,'saxenda-3mg-12-weeks':3\n  };\n  const cur = map[data[med + '_current_dose']];\n  const pref = map[data[med + '_confirm_preferred_dose']];\n  return !!cur && !!pref && pref < cur;\n})();"}, {"key": "additional_notes", "type": "textarea", "tableView": true, "input": true, "label": "Do you have any additional comments or concerns about your medication?", "customConditional": "show = data.summary_confirm === 'yes';"}, {"key": "rxts", "type": "textfield", "input": true, "label": "Rx Templates:", "hidden": true, "disabled": true, "multiple": true, "adminFlag": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "value = [data.wegovy_confirm_preferred_dose, data.ozempic_confirm_preferred_dose, data.rybelsus_confirm_preferred_dose, data.saxenda_confirm_preferred_dose, data.mounjaro_confirm_preferred_dose].filter(Boolean);", "refreshOnChange": true}]}