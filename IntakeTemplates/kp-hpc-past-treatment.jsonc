{"components": [{"key": "heading_past_treatments_kp", "type": "content", "html": "</br><h4>Past KP Treatments</h4>"}, {"key": "used_prescription_past_kp", "type": "radio", "label": "Have you ever used prescription medications for keratosis pilaris?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "unsure"}], "validate": {"required": true}}, {"key": "past_treatment_effectiveness_kp", "type": "radio", "label": "Overall, how well did those treatments work?", "values": [{"label": "Very effective - bumps cleared", "value": "very_effective"}, {"label": "Helped but didn't fully clear", "value": "partial"}, {"label": "Not effective", "value": "ineffective"}, {"label": "Not sure / varied", "value": "unsure"}], "validate": {"required": true}, "customConditional": "show = data.used_prescription_past_kp === 'yes';"}, {"key": "past_rx_types_kp", "type": "selectboxes", "label": "Which prescription types have you used? (Select all that apply)", "values": [{"label": "Topical creams / gels / lotions", "value": "topical"}, {"label": "Oral isotretinoin", "value": "oral"}, {"label": "Other prescription", "value": "other"}], "validate": {"required": true}, "inputType": "checkbox", "customConditional": "show = data.used_prescription_past_kp === 'yes' && !!data.past_treatment_effectiveness_kp;"}, {"key": "heading_topical_selected_kp", "type": "content", "label": "Content", "html": "<br><h4>Topical products you've used</h4>", "customConditional": "show = data.past_rx_types_kp && data.past_rx_types_kp.topical === true;"}, {"key": "kp_rx_topical_grid", "type": "<PERSON><PERSON><PERSON>", "label": "Add each topical product you've used:", "addAnother": "+ Add Another", "components": [{"key": "rx_name", "type": "select", "label": "Topical product", "widget": "html5", "data": {"values": [{"label": "Azelaic acid 15 % gel (Finacea®)", "value": "azelaic_15"}, {"label": "Azelaic acid 20 % cream (Skinoren®)", "value": "azelaic_20"}, {"label": "Urea 30 % cream", "value": "urea_30"}, {"label": "Urea 40 % cream", "value": "urea_40"}, {"label": "Lactic-acid 12 % lotion (AmLactin®)", "value": "lactic_12"}, {"label": "Salicylic-acid 2 % lotion/gel", "value": "salicylic_2"}, {"label": "Adapalene 0.1 % gel", "value": "adapalene_01"}, {"label": "Tretinoin 0.05 % cream", "value": "tretinoin_005"}, {"label": "Tretinoin 0.1 % cream", "value": "tretinoin_01"}, {"label": "Tazarotene 0.045 % lotion (Arazlo®)", "value": "tazarotene_0045"}, {"label": "Other topical Rx", "value": "other"}]}, "validate": {"required": true}}, {"key": "rx_other_name", "type": "textfield", "label": "Other product", "validate": {"required": true}, "customConditional": "show = row && row.rx_name === 'other';"}, {"key": "rx_where", "type": "selectboxes", "label": "Main area applied", "values": [{"label": "Upper arms", "value": "upper_arms"}, {"label": "Forearms", "value": "forearms"}, {"label": "Thighs", "value": "thighs"}, {"label": "<PERSON><PERSON>", "value": "calves"}, {"label": "Buttocks", "value": "buttocks"}, {"label": "Cheeks", "value": "cheeks"}, {"label": "Other", "value": "other"}, {"label": "None of the above", "value": "none", "customClass": "mt-n3"}], "validate": {"required": true}, "inputType": "checkbox"}, {"key": "rx_where_other", "type": "textfield", "label": "Other area", "validate": {"required": true}, "customConditional": "show = row && row.rx_where && row.rx_where.other;"}, {"key": "rx_current", "type": "radio", "label": "Using", "inline": true, "values": [{"label": "Currently", "value": "current"}, {"label": "Past", "value": "past"}], "validate": {"required": true}}, {"key": "stop_reason", "type": "select", "label": "Why did you stop this medicine?", "widget": "html5", "data": {"values": [{"label": "Skin cleared / no longer needed", "value": "cleared"}, {"label": "Side-effects", "value": "sidefx"}, {"label": "Didn't help enough", "value": "ineff"}, {"label": "Cost / insurance coverage", "value": "cost"}, {"label": "Pregnancy / breastfeeding", "value": "preg"}, {"label": "Other reason", "value": "other"}]}, "validate": {"required": true}, "customConditional": "show = row && row.rx_current === 'past';"}, {"key": "stop_sidefx_list", "type": "selectboxes", "label": "Which side-effects did you notice?", "values": [{"label": "Dryness / peeling", "value": "dry"}, {"label": "Redness / irritation", "value": "red"}, {"label": "Burning / stinging", "value": "burn"}, {"label": "Other", "value": "other"}], "validate": {"required": true}, "inputType": "checkbox", "customConditional": "show = row && row.stop_reason === 'sidefx';"}, {"key": "stop_sidefx_other_detail", "type": "textfield", "label": "Other side-effect:", "validate": {"required": true}, "customConditional": "show = row && row.stop_sidefx_list && row.stop_sidefx_list.other;"}, {"key": "stop_other_detail", "type": "textfield", "label": "Please specify other reason:", "validate": {"required": true}, "customConditional": "show = row && row.stop_reason === 'other';"}, {"key": "rx_last_used", "type": "select", "label": "When did you last use it?", "widget": "html5", "data": {"values": [{"label": "≤ 1 week ago", "value": "le1w"}, {"label": "1-4 weeks ago", "value": "1_4w"}, {"label": "1-3 months ago", "value": "1_3m"}, {"label": "3-6 months ago", "value": "3_6m"}, {"label": "> 6 months ago", "value": "gt6m"}, {"label": "Not sure", "value": "unsure"}]}, "validate": {"required": true}, "customConditional": "show = row && row.rx_current === 'past';"}, {"key": "rx_effect", "type": "select", "label": "Effect on KP", "widget": "html5", "data": {"values": [{"label": "Cleared bumps", "value": "cleared"}, {"label": "Helped somewhat", "value": "partial"}, {"label": "No change", "value": "none"}, {"label": "Made it worse", "value": "worse"}, {"label": "Not sure", "value": "unsure"}]}, "validate": {"required": true}}, {"key": "rx_freq", "type": "select", "label": "Frequency used", "widget": "html5", "data": {"values": [{"label": "Once daily", "value": "qd"}, {"label": "Twice daily", "value": "bid"}, {"label": "Every other day", "value": "qod"}, {"label": "PRN / occasionally", "value": "prn"}, {"label": "Not sure", "value": "unsure"}]}, "validate": {"required": true}}, {"key": "rx_longest", "type": "select", "label": "Longest stretch you used it (no breaks):", "widget": "html5", "data": {"values": [{"label": "≤ 4 weeks", "value": "le4w"}, {"label": "1 month", "value": "1m"}, {"label": "2 months", "value": "2m"}, {"label": "3 months", "value": "3m"}, {"label": "4 months", "value": "4m"}, {"label": "5 months", "value": "5m"}, {"label": "6 months", "value": "6m"}, {"label": "> 6 months", "value": "gt6m"}, {"label": "Not sure", "value": "unsure"}]}, "validate": {"required": true}}, {"key": "rx_form", "type": "select", "label": "Form", "widget": "html5", "data": {"values": [{"label": "<PERSON>el", "value": "gel"}, {"label": "Cream", "value": "cream"}, {"label": "Lotion", "value": "lotion"}, {"label": "Foam", "value": "foam"}, {"label": "Other / unsure", "value": "unsure"}]}, "validate": {"required": true}}], "rowTemplate": "<div class='card shadow-sm w-100'><div class='card-body py-2 px-3'><div class='row small'><div class='col-sm-3'><strong>{{ data.rx_name === 'other' ? data.rx_other_name : _.startCase(data.rx_name.replace(/_/g,' ')) }}</strong></div><div class='col-sm-3'>{{ _.join(_.map(_.keys(_.pickBy(data.rx_where,Boolean)), _.startCase), ', ') }}</div><div class='col-sm-1'>{{ data.rx_current==='current' ? 'Yes' : 'Past' }}</div><div class='col-sm-2'>{{ (data.rx_effect || '').replace('_',' ') | title }}</div><div class='col-sm-1'>{{ ({qd:'1×',bid:'2×',qod:'QOD',prn:'PRN',unsure:'N/S'}[data.rx_freq] || '') }}</div><div class='col-sm-2'>{{ ({le4w:'≤4 w',1m:'1 m',2m:'2 m',3m:'3 m',4m:'4 m',5m:'5 m',6m:'6 m',gt6m:'>6 m'}[data.rx_longest] || '') }}</div></div></div></div>", "customConditional": "show = data.past_rx_types_kp && data.past_rx_types_kp.topical;"}]}