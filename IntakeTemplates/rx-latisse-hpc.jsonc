{"components": [{"key": "heading_main_section", "type": "content", "input": false, "label": "Content", "html": "<h1 style=\"text-align:center;\"><strong>Prescription for Eyelash Growth (LATISSE®)</strong></h1><p>To expedite your care, please answer the following questions as accurately and completely as possible. Your responses will be reviewed by an Ontario-licensed physician before any prescription is issued.</p><ul><li><strong>Safety first:</strong> honest answers help us prescribe safely and avoid complications such as eye irritation or colour changes.</li><li><strong>Generic option:</strong> a lower-cost bimatoprost alternative is available (same active ingredient); applicators are purchased separately.</li><li><strong>Photos required:</strong> upload two clear, mascara-free photos of your eyes (open and closed) so we can confirm suitability.</li></ul>", "tableView": false, "refreshOnChange": false}, {"key": "heading_eye_medical_history", "type": "content", "input": false, "label": "Content", "html": "<h2>Eye & Medical History</h2>", "tableView": false, "refreshOnChange": false}, {"key": "history_eye_conditions", "type": "selectboxes", "input": true, "label": "Do you have a history of eye conditions (e.g. uveitis, macular edema, iritis)?", "values": [{"label": "U<PERSON>itis", "value": "uveitis"}, {"label": "Macular edema", "value": "macular_edema"}, {"label": "<PERSON><PERSON><PERSON>", "value": "iritis"}], "adminFlag": true, "confirm_label": "Eye Conditions:", "tableView": true, "optionsLabelPosition": "right"}, {"key": "no_history_eye_conditions", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a condition."}, "validate": {"custom": "valid = !!data.no_history_eye_conditions || _.some(_.values(data.history_eye_conditions));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "eye_conditions_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following eye conditions:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "Eye related conditions", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.history_eye_conditions, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "eye_surgery_history", "type": "radio", "input": true, "confirm_label": "Confirm eye-surgery history:", "label": "Have you had eye surgery (or is one planned soon)?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "eye_surgery_types", "type": "selectboxes", "input": true, "label": "What type of eye surgery have you had or planned?", "values": [{"label": "LASIK / other refractive surgery", "value": "refractive"}, {"label": "Cataract extraction or lens implant", "value": "cataract"}, {"label": "Glaucoma surgery (trabeculectomy, shunt)", "value": "glaucoma"}, {"label": "Retinal surgery", "value": "retinal"}, {"label": "Corneal transplant", "value": "corneal_transplant"}, {"label": "Other (specify below)", "value": "other"}], "adminFlag": true, "confirm_label": "Eye surgery type(s):", "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = data.eye_surgery_history === 'yes';", "validate": {"custom": "valid = data.eye_surgery_history === 'no' || _.some(_.values(data.eye_surgery_types));"}}, {"key": "eye_surgery_other_detail", "type": "textfield", "input": true, "label": "Please specify other eye surgery:", "confirm_label": "Other eye surgery specified:", "customConditional": "show = data.eye_surgery_history === 'yes' && data.eye_surgery_types && data.eye_surgery_types.other;", "tableView": true}, {"key": "dx_glaucoma_or_high_iop", "type": "radio", "input": true, "confirm_label": "Diagnosed with glaucoma / high IOP:", "label": "Have you been diagnosed with <strong>High eye pressure (IOP)</strong> or <strong>Glaucoma</strong>?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Unsure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "iop_glaucoma_meds", "type": "selectboxes", "input": true, "confirm_label": "IOP / glaucoma medications:", "label": "Are you currently taking any of these eye-pressure medications?", "values": [{"label": "Latanoprost (Xalatan)", "value": "latanoprost"}, {"label": "Travoprost (Travatan Z)", "value": "travoprost"}, {"label": "<PERSON><PERSON><PERSON> (Timoptic)", "value": "timolol"}, {"label": "Brimonidine (Alphagan)", "value": "brimonidine"}, {"label": "Dorzolamide (Trusopt)", "value": "dorzolamide"}, {"label": "Dorzolamide/Timolol (Cosopt)", "value": "dorzolamide_timolol"}, {"label": "Brinzolamide (Azopt)", "value": "brinzolamide"}], "adminFlag": true, "inputType": "checkbox", "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = !!data.dx_glaucoma_or_high_iop;"}, {"key": "no_iop_glaucoma_meds", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "Select a medication or choose 'None of the above'."}, "validate": {"custom": "valid = !!data.no_iop_glaucoma_meds || _.some(_.values(data.iop_glaucoma_meds));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = !!data.dx_glaucoma_or_high_iop;"}, {"key": "heading_previous_treatments", "type": "content", "input": false, "label": "Content", "html": "</br><h2>Previous Eyelash Treatments</h2>", "tableView": false, "refreshOnChange": false}, {"key": "used_latisse_bimatoprost", "type": "radio", "input": true, "confirm_label": "Previous LATISSE®/bimatoprost use:", "label": "Have you ever used LATISSE® or generic bimatoprost for eyelash growth?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "latisse_product_type", "type": "radio", "input": true, "confirm_label": "Product used:", "label": "Which product did you (or do you) use?", "values": [{"label": "Branded LATISSE® (Allergan)", "value": "branded"}, {"label": "Generic bimatoprost 0.03 % solution", "value": "generic"}, {"label": "Both / switched between brands", "value": "both"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = data.used_latisse_bimatoprost === 'yes';"}, {"key": "latisse_use_status", "type": "radio", "input": true, "confirm_label": "Current usage status:", "label": "Are you currently using LATISSE®/bimatoprost or have you stopped?", "values": [{"label": "Currently using", "value": "current"}, {"label": "Stopped", "value": "stopped"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = data.used_latisse_bimatoprost === 'yes';"}, {"key": "latisse_started_when", "type": "select", "input": true, "confirm_label": "Start date (interval):", "label": "When did you first start using LATISSE®/bimatoprost?", "widget": "html5", "data": {"values": [{"label": "Within the last week", "value": "0_1w"}, {"label": "1 - 2 weeks ago", "value": "1_2w"}, {"label": "2 - 3 weeks ago", "value": "2_3w"}, {"label": "3 - 4 weeks ago", "value": "3_4w"}, {"label": "4 - 5 weeks ago", "value": "4_5w"}, {"label": "5 - 6 weeks ago", "value": "5_6w"}, {"label": "6 - 7 weeks ago", "value": "6_7w"}, {"label": "7 - 8 weeks ago", "value": "7_8w"}, {"label": "8 - 9 weeks ago", "value": "8_9w"}, {"label": "9 - 10 weeks ago", "value": "9_10w"}, {"label": "10 - 11 weeks ago", "value": "10_11w"}, {"label": "11 - 12 weeks ago", "value": "11_12w"}, {"label": "3 - 6 months ago", "value": "3_6m"}, {"label": "6 - 9 months ago", "value": "6_9m"}, {"label": "9 - 12 months ago", "value": "9_12m"}, {"label": "12 - 15 months ago", "value": "12_15m"}, {"label": "15 - 18 months ago", "value": "15_18m"}, {"label": "18 - 21 months ago", "value": "18_21m"}, {"label": "21 - 24 months ago", "value": "21_24m"}, {"label": "2 - 5 years ago", "value": "2_5y"}, {"label": "More than 5 years ago", "value": "gt5y"}, {"label": "Not sure", "value": "unsure"}]}, "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = data.used_latisse_bimatoprost === 'yes';"}, {"key": "latisse_duration_current", "type": "select", "input": true, "confirm_label": "Duration of use (current):", "label": "How long have you been using LATISSE®/bimatoprost?", "widget": "html5", "data": {"values": [{"label": "< 1 month", "value": "<1m"}, {"label": "1 - 3 months", "value": "1_3m"}, {"label": "3 - 6 months", "value": "3_6m"}, {"label": "6 - 9 months", "value": "6_9m"}, {"label": "9 - 12 months", "value": "9_12m"}, {"label": "1 - 1.5 years", "value": "12_18m"}, {"label": "1.5 - 2 years", "value": "18_24m"}, {"label": "2 - 3 years", "value": "2_3y"}, {"label": "3 - 5 years", "value": "3_5y"}, {"label": "> 5 years", "value": ">5y"}]}, "validate": {"required": true}, "customConditional": "show = data.latisse_use_status === 'current';", "tableView": true}, {"key": "latisse_duration_past", "type": "select", "input": true, "confirm_label": "Duration of use (past):", "label": "How long did you use LATISSE®/bimatoprost before stopping?", "widget": "html5", "data": {"values": [{"label": "< 1 month", "value": "<1m"}, {"label": "1 - 3 months", "value": "1_3m"}, {"label": "3 - 6 months", "value": "3_6m"}, {"label": "6 - 9 months", "value": "6_9m"}, {"label": "9 - 12 months", "value": "9_12m"}, {"label": "1 - 1.5 years", "value": "12_18m"}, {"label": "1.5 - 2 years", "value": "18_24m"}, {"label": "2 - 3 years", "value": "2_3y"}, {"label": "3 - 5 years", "value": "3_5y"}, {"label": "> 5 years", "value": ">5y"}]}, "validate": {"required": true}, "customConditional": "show = data.latisse_use_status === 'stopped';", "tableView": true}, {"key": "latisse_last_used", "type": "select", "input": true, "confirm_label": "Last used:", "label": "When did you last apply LATISSE®/bimatoprost?", "widget": "html5", "data": {"values": [{"label": "≤ 1 week ago", "value": "0_1w"}, {"label": "1 - 2 weeks ago", "value": "1_2w"}, {"label": "2 - 3 weeks ago", "value": "2_3w"}, {"label": "3 - 4 weeks ago", "value": "3_4w"}, {"label": "1 - 2 months ago", "value": "1_2m"}, {"label": "2 - 3 months ago", "value": "2_3m"}, {"label": "3 - 6 months ago", "value": "3_6m"}, {"label": "6 - 9 months ago", "value": "6_9m"}, {"label": "9 - 12 months ago", "value": "9_12m"}, {"label": "1 - 2 years ago", "value": "1_2y"}, {"label": "2 - 5 years ago", "value": "2_5y"}, {"label": "> 5 years ago", "value": ">5y"}, {"label": "Not sure", "value": "unsure"}]}, "validate": {"required": true}, "customConditional": "show = data.latisse_use_status === 'stopped';", "tableView": true}, {"key": "latisse_frequency_current", "type": "select", "input": true, "confirm_label": "Frequency used (current):", "label": "How often do you apply LATISSE®/bimatoprost?", "widget": "html5", "data": {"values": [{"label": "Once daily", "value": "qd"}, {"label": "Every other day (≈ 3-4 x/week)", "value": "qod"}, {"label": "Twice daily", "value": "bid"}, {"label": "2-3 times per week", "value": "2_3x_week"}, {"label": "Once weekly", "value": "1x_week"}, {"label": "Less than once weekly / occasionally", "value": "occasional"}, {"label": "Not sure", "value": "unsure"}]}, "validate": {"required": true}, "customConditional": "show = data.latisse_use_status === 'current';", "tableView": true}, {"key": "latisse_frequency_past", "type": "select", "input": true, "confirm_label": "Frequency used (past):", "label": "When you were using LATISSE®/bimatoprost, how often did you apply it?", "widget": "html5", "data": {"values": [{"label": "Once daily", "value": "qd"}, {"label": "Every other day (≈ 3-4 x/week)", "value": "qod"}, {"label": "Twice daily", "value": "bid"}, {"label": "2-3 times per week", "value": "2_3x_week"}, {"label": "Once weekly", "value": "1x_week"}, {"label": "Less than once weekly / occasionally", "value": "occasional"}, {"label": "Stopped after only a few doses", "value": "few_doses"}, {"label": "Not sure", "value": "unsure"}]}, "validate": {"required": true}, "customConditional": "show = data.latisse_use_status === 'stopped';", "tableView": true}, {"key": "latisse_effect_current", "type": "select", "input": true, "confirm_label": "Effect on lashes (current):", "label": "So far, what effect is LATISSE®/bimatoprost having on your lashes?", "widget": "html5", "data": {"values": [{"label": "Excellent (clearly longer/thicker)", "value": "excellent"}, {"label": "Good (noticeable improvement)", "value": "good"}, {"label": "Minimal change", "value": "minimal"}, {"label": "No change", "value": "none"}, {"label": "Not sure yet", "value": "unsure"}]}, "validate": {"required": true}, "customConditional": "show = data.latisse_use_status === 'current';", "tableView": true}, {"key": "latisse_effect_past", "type": "select", "input": true, "confirm_label": "Effect on lashes (past):", "label": "When you used LATISSE®/bimatoprost, what effect did it have on your lashes?", "widget": "html5", "data": {"values": [{"label": "Excellent (clearly longer/thicker)", "value": "excellent"}, {"label": "Good (noticeable improvement)", "value": "good"}, {"label": "Minimal change", "value": "minimal"}, {"label": "No change", "value": "none"}, {"label": "Not sure", "value": "unsure"}]}, "validate": {"required": true}, "customConditional": "show = data.latisse_use_status === 'stopped';", "tableView": true}, {"key": "latisse_reason_stopped", "type": "selectboxes", "input": true, "confirm_label": "Reason(s) for stopping:", "label": "Why did you stop using LATISSE®/bimatoprost?", "values": [{"label": "Cost", "value": "cost"}, {"label": "Lack of results", "value": "no_results"}, {"label": "Side effects", "value": "side_effects"}, {"label": "Completed course", "value": "completed_course"}, {"label": "Other", "value": "other"}], "adminFlag": true, "inputType": "checkbox", "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = data.latisse_use_status === 'stopped';", "validate": {"custom": "valid = data.latisse_use_status !== 'stopped' || _.some(_.values(data.latisse_reason_stopped));"}}, {"key": "latisse_reason_other_detail", "type": "textfield", "input": true, "confirm_label": "Other stop reason detail:", "label": "Please specify other reason:", "customConditional": "show = data.latisse_use_status === 'stopped' && data.latisse_reason_stopped && data.latisse_reason_stopped.other;", "validate": {"required": true}, "tableView": true}, {"key": "heading_stop_side_effects", "type": "content", "input": false, "label": "Content", "html": "<h3>Side effects that made you stop LATISSE®/bimatoprost</h3>", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.latisse_use_status === 'stopped' && data.latisse_reason_stopped?.side_effects;"}, {"key": "latisse_stop_side_effects", "type": "selectboxes", "input": true, "confirm_label": "Side effects (stop reason):", "label": "Which side effects led you to stop? (select all that apply)", "values": [{"label": "Eye redness / irritation", "value": "redness"}, {"label": "Darkening of eyelid skin", "value": "eyelid_darkening"}, {"label": "Change in eye colour", "value": "eye_colour_change"}, {"label": "Itchy eyes", "value": "itchy_eyes"}, {"label": "Dry eyes", "value": "dry_eyes"}, {"label": "Other", "value": "other"}], "adminFlag": true, "inputType": "checkbox", "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = data.latisse_use_status === 'stopped' && data.latisse_reason_stopped?.side_effects;", "validate": {"custom": "valid = _.some(_.values(data.latisse_stop_side_effects)) || !!data.no_latisse_stop_side_effects;"}}, {"key": "no_latisse_stop_side_effects", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "Select a side-effect or choose 'None of the above'."}, "validate": {"custom": "valid = !!data.no_latisse_stop_side_effects || _.some(_.values(data.latisse_stop_side_effects));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.latisse_use_status === 'stopped' && data.latisse_reason_stopped?.side_effects;"}, {"key": "latisse_stop_se_other_detail", "type": "textfield", "input": true, "confirm_label": "Other side-effect detail (stop reason):", "label": "Please describe the other side-effect:", "customConditional": "show = data.latisse_use_status === 'stopped' && data.latisse_stop_side_effects?.other;", "validate": {"required": true}, "tableView": true}, {"key": "heading_se_redness", "type": "content", "input": false, "label": "Content", "html": "<h4>Eye redness / irritation</h4>", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.latisse_use_status === 'stopped' && data.latisse_stop_side_effects?.redness;"}, {"key": "se_redness_resolved", "type": "radio", "input": true, "label": "Did the redness resolve after you stopped LATISSE®/bimatoprost?", "confirm_label": "Redness resolved after stopping:", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Unsure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = data.latisse_stop_side_effects?.redness;"}, {"key": "se_redness_current", "type": "radio", "input": true, "label": "Are you currently experiencing redness or irritation?", "confirm_label": "Redness currently present:", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = data.se_redness_resolved !== undefined;"}, {"key": "heading_se_eyelid_darkening", "type": "content", "input": false, "label": "Content", "html": "<h4>Eyelid skin darkening</h4>", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.latisse_use_status === 'stopped' && data.latisse_stop_side_effects?.eyelid_darkening;"}, {"key": "se_darkening_resolved", "type": "radio", "input": true, "label": "Has the darkening faded since stopping?", "confirm_label": "Darkening improved:", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Unsure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = data.latisse_stop_side_effects?.eyelid_darkening;"}, {"key": "se_darkening_bothersome", "type": "radio", "input": true, "label": "Does the eyelid darkening bother you cosmetically?", "confirm_label": "Darkening bothersome:", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = data.se_darkening_resolved !== undefined;"}, {"key": "heading_se_eye_colour_change", "type": "content", "input": false, "label": "Content", "html": "<h4>Eye-colour change</h4>", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.latisse_use_status === 'stopped' && data.latisse_stop_side_effects?.eye_colour_change;"}, {"key": "se_colour_confirmed", "type": "radio", "input": true, "label": "Was the colour change confirmed by an eye-care professional?", "confirm_label": "Colour change confirmed by professional:", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = data.latisse_stop_side_effects?.eye_colour_change;"}, {"key": "se_colour_stable", "type": "radio", "input": true, "label": "Has the eye-colour change progressed since stopping?", "confirm_label": "Colour change progressed:", "values": [{"label": "No, it stayed the same", "value": "stable"}, {"label": "Yes, it’s getting darker", "value": "worse"}, {"label": "Unsure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = data.se_colour_confirmed !== undefined;"}, {"key": "heading_se_itchy_eyes", "type": "content", "input": false, "label": "Content", "html": "<h4>Itchy eyes</h4>", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.latisse_use_status === 'stopped' && data.latisse_stop_side_effects?.itchy_eyes;"}, {"key": "se_itchy_current", "type": "radio", "input": true, "label": "Are your eyes still itchy now?", "confirm_label": "Itchy eyes currently:", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = data.latisse_stop_side_effects?.itchy_eyes;"}, {"key": "heading_se_dry_eyes", "type": "content", "input": false, "label": "Content", "html": "<h4>Dry eyes</h4>", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.latisse_use_status === 'stopped' && data.latisse_stop_side_effects?.dry_eyes;"}, {"key": "se_dry_eyes_treated", "type": "radio", "input": true, "label": "Are you using lubricating drops for the dryness?", "confirm_label": "Using drops for dryness:", "values": [{"label": "Yes, regularly", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = data.latisse_stop_side_effects?.dry_eyes;"}, {"key": "heading_se_other", "type": "content", "input": false, "label": "Content", "html": "<h4>Other side-effect</h4>", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.latisse_use_status === 'stopped' && data.latisse_stop_side_effects?.other;"}, {"key": "se_other_detail", "type": "textfield", "input": true, "label": "Describe the other side-effect that led you to stop:", "confirm_label": "Other side-effect description:", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.latisse_stop_side_effects?.other;"}, {"key": "latisse_current_side_effects", "type": "selectboxes", "input": true, "confirm_label": "Side effects currently:", "label": "Are you currently experiencing any side effects?", "values": [{"label": "Eye redness / irritation", "value": "redness"}, {"label": "Darkening of eyelid skin", "value": "eyelid_darkening"}, {"label": "Change in eye colour", "value": "eye_colour_change"}, {"label": "Itchy eyes", "value": "itchy_eyes"}, {"label": "Dry eyes", "value": "dry_eyes"}, {"label": "Other", "value": "other"}], "adminFlag": true, "inputType": "checkbox", "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = data.latisse_use_status === 'current';"}, {"key": "none_latisse_current_se", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a side-effect."}, "validate": {"custom": "valid = !!data.none_latisse_current_se || _.some(_.values(data.latisse_current_side_effects));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.latisse_use_status === 'current';"}, {"key": "latisse_current_side_effects_other_detail", "type": "textfield", "input": true, "confirm_label": "Other current side-effect detail:", "label": "Please specify other current side-effect:", "customConditional": "show = data.latisse_current_side_effects?.other && data.latisse_use_status === 'current';", "validate": {"required": true}, "tableView": true}, {"key": "heading_current_se_master", "type": "content", "input": false, "label": "Content", "html": "<h3>Details about your current side-effects</h3>", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.latisse_use_status === 'current' && _.some(_.values(data.latisse_current_side_effects));"}, {"key": "heading_current_se_redness", "type": "content", "input": false, "label": "Content", "html": "<h4>Eye redness / irritation</h4>", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.latisse_use_status === 'current' && data.latisse_current_side_effects?.redness;"}, {"key": "current_se_redness_description", "type": "radio", "input": true, "label": "Which best describes the redness you see right now?", "confirm_label": "Redness description (current):", "values": [{"label": "Only a slight pink tint, no discomfort", "value": "slight_pink"}, {"label": "Noticeable redness; feels a little irritated", "value": "noticeable"}, {"label": "Red and bothersome; often use soothing drops", "value": "bothersome"}, {"label": "Very red / painful; affects daily activities", "value": "very_red"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = data.latisse_use_status === 'current' && data.latisse_current_side_effects?.redness;"}, {"key": "current_se_redness_duration", "type": "select", "input": true, "label": "How long has the redness been present?", "confirm_label": "Redness duration (current):", "widget": "html5", "data": {"values": [{"label": "< 1 week", "value": "<1w"}, {"label": "1 – 4 weeks", "value": "1_4w"}, {"label": "1 – 3 months", "value": "1_3m"}, {"label": "> 3 months", "value": ">3m"}]}, "validate": {"required": true}, "tableView": true, "customConditional": "show = data.latisse_use_status === 'current' && data.latisse_current_side_effects?.redness;"}, {"key": "current_se_redness_treatment", "type": "radio", "input": true, "label": "Are you using any drops or treatment for the redness?", "confirm_label": "Treating redness currently:", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = data.latisse_use_status === 'current' && data.latisse_current_side_effects?.redness;"}, {"key": "heading_current_se_darkening", "type": "content", "input": false, "label": "Content", "html": "<h4>Eyelid skin darkening</h4>", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.latisse_use_status === 'current' && data.latisse_current_side_effects?.eyelid_darkening;"}, {"key": "current_se_darkening_bothersome", "type": "radio", "input": true, "label": "Does the eyelid darkening bother you cosmetically?", "confirm_label": "Darkening bothersome (current):", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = data.latisse_use_status === 'current' && data.latisse_current_side_effects?.eyelid_darkening;"}, {"key": "heading_current_se_colour_change", "type": "content", "input": false, "label": "Content", "html": "<h4>Eye-colour change</h4>", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.latisse_use_status === 'current' && data.latisse_current_side_effects?.eye_colour_change;"}, {"key": "current_se_colour_confirmed", "type": "radio", "input": true, "label": "Has an eye-care professional confirmed the colour change?", "confirm_label": "Colour change confirmed (current):", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = data.latisse_use_status === 'current' && data.latisse_current_side_effects?.eye_colour_change;"}, {"key": "heading_current_se_itchy", "type": "content", "input": false, "label": "Content", "html": "<h4>Itchy eyes</h4>", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.latisse_use_status === 'current' && data.latisse_current_side_effects?.itchy_eyes;"}, {"key": "current_se_itchy_frequency", "type": "radio", "input": true, "label": "How often are your eyes itchy today?", "confirm_label": "Itchy-eye frequency (current):", "values": [{"label": "Rarely (once or twice a day)", "value": "rare"}, {"label": "Often (several times a day)", "value": "often"}, {"label": "Constantly (most of the day)", "value": "constant"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = data.latisse_use_status === 'current' && data.latisse_current_side_effects?.itchy_eyes;"}, {"key": "current_se_itchy_treated", "type": "radio", "input": true, "label": "Are you using any treatment for itchy eyes?", "confirm_label": "Treating itch (current):", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = data.latisse_use_status === 'current' && data.latisse_current_side_effects?.itchy_eyes;"}, {"key": "heading_current_se_dry", "type": "content", "input": false, "label": "Content", "html": "<h4>Dry eyes</h4>", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.latisse_use_status === 'current' && data.latisse_current_side_effects?.dry_eyes;"}, {"key": "current_se_dry_frequency", "type": "radio", "input": true, "label": "How often do your eyes feel dry or gritty?", "confirm_label": "Dry-eye frequency (current):", "values": [{"label": "Occasionally (a few times a week)", "value": "occasional"}, {"label": "Daily but manageable", "value": "daily"}, {"label": "Daily and bothersome despite drops", "value": "persistent"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = data.latisse_use_status === 'current' && data.latisse_current_side_effects?.dry_eyes;"}, {"key": "current_se_dry_treated", "type": "radio", "input": true, "label": "Are you using lubricating drops for the dryness?", "confirm_label": "Treating dryness (current):", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = data.latisse_use_status === 'current' && data.latisse_current_side_effects?.dry_eyes;"}, {"key": "heading_current_se_other", "type": "content", "input": false, "label": "Content", "html": "<h4>Other side-effect</h4>", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.latisse_use_status === 'current' && data.latisse_current_side_effects?.other;"}, {"key": "current_se_other_detail", "type": "textfield", "input": true, "label": "Describe the other side-effect you are experiencing:", "confirm_label": "Other side-effect (current):", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.latisse_use_status === 'current' && data.latisse_current_side_effects?.other;"}, {"key": "heading_other_treatments", "type": "content", "input": false, "label": "Content", "html": "</br><h2>Other Eyelash Treatments</h2>", "tableView": false, "refreshOnChange": false}, {"key": "previous_eyelash_treatments", "type": "radio", "input": true, "confirm_label": "Confirm prior eyelash growth treatments:", "label": "Have you tried <strong>any</strong> other eyelash-growth treatments?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "other_eyelash_treatments", "type": "selectboxes", "input": true, "confirm_label": "Types of treatments tried:", "label": "Which of the following have you tried? (select all that apply)", "values": [{"label": "Over-the-counter eyelash serums", "value": "otc_serums"}, {"label": "Prescription serums (not LATISSE®)", "value": "rx_serums"}, {"label": "Eyelash extensions", "value": "extensions"}, {"label": "Growth-enhancing mascaras", "value": "growth_mascara"}], "adminFlag": true, "inputType": "checkbox", "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = !!data.previous_eyelash_treatments;", "validate": {"custom": "valid = !!data.no_other_eyelash_treatments || _.some(_.values(data.other_eyelash_treatments));"}}, {"key": "no_other_eyelash_treatments", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "Select a treatment or choose 'None of the above'."}, "validate": {"custom": "valid = !!data.no_other_eyelash_treatments || _.some(_.values(data.other_eyelash_treatments));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = !!data.previous_eyelash_treatments;"}, {"key": "heading_mental_health", "type": "content", "input": false, "label": "Content", "html": "</br><h2>Mental Health & Body Image</h2>", "tableView": false, "refreshOnChange": false}, {"key": "bdd_diagnosis", "type": "radio", "input": true, "confirm_label": "Confirm BDD diagnosis:", "label": "Have you ever been diagnosed with Body Dysmorphic Disorder (BDD)?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "eyelashes_thought_frequency", "type": "radio", "input": true, "confirm_label": "Confirm how often you think about eyelashes:", "label": "How often do you think about your eyelashes?", "values": [{"label": "Not at all", "value": "not_at_all"}, {"label": "Occasionally", "value": "occasionally"}, {"label": "Frequently", "value": "frequently"}, {"label": "Constantly", "value": "constantly"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "social_situations_avoidance", "type": "radio", "input": true, "confirm_label": "Confirm social avoidance due to eyelashes:", "label": "Have you ever avoided social situations because of your eyelashes?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "mental_health_therapy", "type": "radio", "input": true, "confirm_label": "Confirm current therapy for appearance:", "label": "Are you currently in therapy for body image or appearance concerns?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "heading_pregnancy_lifestyle", "type": "content", "input": false, "label": "Content", "html": "<h2>Pregnancy & Lifestyle</h2>", "tableView": false, "refreshOnChange": false}, {"key": "pregnant_or_breastfeeding", "type": "radio", "input": true, "confirm_label": "Confirm pregnancy/breastfeeding status:", "label": "Are you pregnant, trying to conceive, or breastfeeding?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "contact_lenses", "type": "radio", "input": true, "confirm_label": "Confirm contact-lens use:", "label": "Do you wear contact lenses?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "heading_photos", "type": "content", "input": false, "label": "Content", "html": "<h2>Required Photos</h2><p>Upload <strong>two clear photos</strong> of your eyes: 1) eyes open (upper lash line) and 2) eyes closed/looking down.</p>", "tableView": false, "refreshOnChange": false}, {"key": "uploadUrl", "type": "file", "input": true, "confirm_label": "Confirm eye photos uploaded:", "label": "Upload Eye Photos", "image": true, "webcam": true, "capture": "user", "storage": "url", "url": "/app/q/{qpk}/formio-files/{pk}/", "multiple": true, "fileTypes": [{"label": "", "value": ""}], "tableView": true, "validate": {"required": true, "minFiles": 2, "maxFiles": 5}}, {"key": "photo_confirm_eyes", "type": "checkbox", "input": true, "confirm_label": "Confirm photo-upload acknowledgment:", "label": "I've uploaded two clear photos of my eyes.", "validate": {"required": true}, "tableView": false}, {"key": "intake_denial_reasons", "type": "textfield", "input": true, "label": "Intake Denial Reasons", "hidden": true, "disabled": true, "multiple": true, "adminFlag": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "value = [];", "refreshOnChange": true}, {"key": "intake_kind", "type": "textfield", "input": true, "label": "Intake Kind", "hidden": true, "disabled": true, "tableView": true, "clearOnHide": false, "calculateValue": "value = !data.intake_denial_reasons?.length ? 'mail' : 'chat';"}, {"key": "intake_mt_key", "type": "textfield", "input": true, "label": "Intake Mail Template Key", "hidden": true, "disabled": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.intake_kind === 'mail' ? 'intake-latisse' : 'intake-denial';"}, {"key": "allowed_rt_keys", "type": "textfield", "input": true, "label": "Allowed Rep<PERSON>", "hidden": true, "disabled": true, "multiple": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.intake_mt_key === 'intake-denial' ? [] : ['get-rx', 'appointment-intake', 'edit-intake'];"}]}