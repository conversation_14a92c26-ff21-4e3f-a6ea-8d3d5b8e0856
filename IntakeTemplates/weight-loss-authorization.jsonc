{"components": [{"key": "last_tele_test_weight_loss", "type": "radio", "input": true, "label": "Have you completed a weight loss prescription start or renewal with <a href='https://teletest.ca/app/care/mens-health/' target='_blank'>TeleTest</a>?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "current_ped_note", "html": "<p class='text-red'>TeleTest currently completes insurance authroization requests for individuals issued prescriptions through our service. Please select a weight loss <a href='https://teletest.ca/app/care/weight-loss/' target='_blank'>Panel Here</a></p>", "type": "content", "input": true, "tableView": false, "clearOnHide": false, "customConditional": "show = data.last_tele_test_weight_loss === false;"}, {"key": "no_indications", "type": "textfield", "input": true, "label": "No Indication:", "hidden": true, "disabled": true, "multiple": false, "tableView": true, "clearOnHide": true, "defaultValue": "No indications", "calculateValue": "value = !data.last_tele_test_weight_loss;", "refreshOnChange": true}, {"key": "contraindication_keys", "type": "textfield", "input": true, "label": "Contraindication keys:", "hidden": true, "disabled": true, "multiple": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "value = !data.last_tele_test_weight_loss ? ['weight_loss_not_completed'] : [];", "refreshOnChange": true}, {"key": "rejected_html", "type": "content", "input": true, "tableView": false, "refreshOnChange": true, "clearOnHide": false, "defaultValue": "<h3 class='text-red'>You're ineligible to complete insurance paperwork at this time. Please speak to your original prescribing physician.</h3>", "customConditional": "show = data.last_tele_test_weight_loss === false"}, {"key": "approved_html", "type": "content", "input": true, "tableView": false, "refreshOnChange": true, "clearOnHide": false, "defaultValue": "<h3 class='text-green'> You're eligible to review and complete your insurance paperwork.</h3>", "customConditional": "show = data.last_tele_test_weight_loss === true"}]}