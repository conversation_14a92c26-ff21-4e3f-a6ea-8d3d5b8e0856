{"components": [{"key": "content", "html": "<h2>Instructions</h2><p>Please complete the following questionnaire about your interest in preventative health screening.  Some tests require additional fees at the time you visit the lab, as they are not covered by OHIP.</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "heading_preventative_health_indication", "html": "<h4>Indication for Preventative Health Screening</h4>", "type": "content", "input": false, "label": "Indication for Testing", "tableView": false, "refreshOnChange": false}, {"key": "screening_indication", "type": "selectboxes", "input": true, "label": "Please clarify why you are interested in preventative health screening:", "values": [{"label": "I want to monitor my general health and wellness", "value": "general_health_monitoring", "shortcut": ""}, {"label": "I have a family history of high cholesterol or diabetes", "value": "family_history", "shortcut": ""}, {"label": "I am starting a new fitness or weight management program", "value": "fitness_program", "shortcut": ""}, {"label": "I am taking medications that require monitoring", "value": "medication_monitoring", "shortcut": ""}, {"label": "I have a history of high cholesterol or diabetes", "value": "personal_history", "shortcut": ""}, {"label": "I want to check my risk for metabolic diseases like diabetes or heart disease", "value": "risk_assessment", "shortcut": ""}, {"label": "I have symptoms", "value": "current_symptoms", "shortcut": ""}, {"label": "Other", "value": "other_reason", "shortcut": ""}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "confirm_label": "Reason(s) for testing:", "optionsLabelPosition": "right"}, {"key": "other_screening_indication", "type": "textarea", "input": true, "label": "Please state your “other” reason(s) for seeking preventative health screening:", "tableView": true, "autoExpand": false, "confirm_label": "Other reason(s) for testing:", "customConditional": "show = data.screening_indication.other_reason;"}, {"key": "confirm_no_symptoms", "type": "radio", "input": true, "label": "Please confirm that you do not have any symptoms at the present time:", "values": [{"label": "No symptoms", "value": "no_symptoms"}, {"label": "I have symptoms and wish to update my selection", "value": "have_symptoms"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Current symptoms:", "customConditional": "show = !data.screening_indication?.current_symptoms;"}, {"key": "heading_constitutional_symptoms", "html": "<h2>General Symptoms</h2><p>These are general symptoms that may affect your overall health. Please let us know if you are experiencing any of the following.</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.screening_indication?.current_symptoms || data.confirm_no_symptoms === 'have_symptoms';"}, {"key": "constitutional_symptoms", "type": "selectboxes", "input": true, "label": "Do you have any of the following general symptoms?", "values": [{"label": "Have fevers without having cold symptoms (ie. runny nose, sore throat)", "value": "fever"}, {"label": "Losing weight without dieting or exercising", "value": "weight_loss"}, {"label": "Feeling unusually tired", "value": "fatigue"}, {"label": "Sweating at night", "value": "night_sweats"}, {"label": "Feeling unusually cold", "value": "chills"}, {"label": "Not feeling hungry", "value": "loss_of_appetite"}], "tableView": true, "customConditional": "show = data.screening_indication?.current_symptoms || data.confirm_no_symptoms === 'have_symptoms';", "optionsLabelPosition": "right"}, {"key": "heading_heart_symptoms", "html": "<h2>Heart Symptoms</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.screening_indication?.current_symptoms || data.confirm_no_symptoms === 'have_symptoms';"}, {"key": "cardiovascular_symptoms", "type": "selectboxes", "input": true, "label": "Do you have any of the following heart-related symptoms?", "values": [{"label": "Chest pain, tightness or discomfort", "value": "chest_pain"}, {"label": "Palpitations", "value": "palpitations"}, {"label": "Swelling in the legs, ankles, or feet", "value": "swelling"}, {"label": "Dizziness or fainting", "value": "dizziness"}], "tableView": true, "customConditional": "show = data.screening_indication?.current_symptoms || data.confirm_no_symptoms === 'have_symptoms';", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_cardiovascular_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "validate": {"custom": "valid = !!data.none_of_the_above_cardiovascular_symptoms || _.some(_.values(data.cardiovascular_symptoms));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.screening_indication?.current_symptoms || data.confirm_no_symptoms === 'have_symptoms';"}, {"key": "heading_chest_pain", "html": "<h4>Details About Your Chest Pain</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.cardiovascular_symptoms?.chest_pain;"}, {"key": "chest_pain_onset", "data": {"values": [{"label": "Less than 1 hour ago", "value": "<1_hour"}, {"label": "1-3 hours ago", "value": "1-3_hours"}, {"label": "4-6 hours ago", "value": "4-6_hours"}, {"label": "6-12 hours ago", "value": "6-12_hours"}, {"label": "12-24 hours ago", "value": "12-24_hours"}, {"label": "1-2 days ago", "value": "1-2_days"}, {"label": "3-7 days ago", "value": "3-7_days"}, {"label": "1-2 weeks ago", "value": "1-2_weeks"}, {"label": "2-4 weeks ago", "value": "2-4_weeks"}, {"label": "1-3 months ago", "value": "1-3_months"}, {"label": "4-6 months ago", "value": "4-6_months"}, {"label": "7-12 months ago", "value": "7-12_months"}, {"label": "More than 1 year ago", "value": ">1_year"}, {"label": "Ongoing since as long as I can remember", "value": "ongoing"}, {"label": "I'm not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "When did your chest pain start?", "widget": "html5", "validate": {"required": true}, "tableView": true, "placeholder": "Select the time range", "customConditional": "show = data.cardiovascular_symptoms?.chest_pain;"}, {"key": "chest_pain_palliation", "type": "selectboxes", "input": true, "label": "What seems to make your chest pain better or worse?", "values": [{"label": "It feels better when I rest", "value": "better_with_rest"}, {"label": "It gets worse when I'm active or exercising", "value": "worse_with_exertion"}, {"label": "It improves after I take specific medication, like pain relievers or heart medication", "value": "better_with_medication"}, {"label": "It feels better when I change position or lie down", "value": "better_with_position_change"}, {"label": "It gets worse after eating or drinking", "value": "worse_after_eating"}, {"label": "It gets worse when I'm stressed or anxious", "value": "worse_with_stress"}, {"label": "It doesn't change no matter what I do", "value": "unchanged"}], "tableView": true, "customConditional": "show = data.cardiovascular_symptoms?.chest_pain;"}, {"key": "chest_pain_prior_history", "type": "radio", "input": true, "label": "Have you ever had chest pain like this before?", "values": [{"label": "Yes, I've had similar pain before", "value": "yes"}, {"label": "No, this is the first time I've experienced this", "value": "no"}, {"label": "I'm not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.cardiovascular_symptoms?.chest_pain;"}, {"key": "chest_pain_prior_diagnosis", "type": "selectboxes", "input": true, "label": "If you've had similar chest pain before, were you given any of these diagnoses?", "values": [{"label": "Heart attack", "value": "heart_attack"}, {"label": "<PERSON><PERSON> (chest pain related to heart disease)", "value": "angina"}, {"label": "Heartburn or acid reflux", "value": "heartburn"}, {"label": "Muscle strain or injury", "value": "muscle_strain"}, {"label": "Panic attack or anxiety-related chest pain", "value": "panic_attack"}, {"label": "Pleurisy (inflammation of the lining of the lungs)", "value": "pleurisy"}, {"label": "Costochondritis (inflammation of the rib cartilage)", "value": "costochondritis"}, {"label": "No diagnosis was provided", "value": "no_diagnosis"}, {"label": "I don't know", "value": "unknown"}], "tableView": true, "customConditional": "show = data.cardiovascular_symptoms?.chest_pain && data.chest_pain_prior_history === 'yes';"}, {"key": "chest_pain_description", "type": "selectboxes", "input": true, "label": "How would you describe your chest pain? (Select all that apply)", "values": [{"label": "Tightness or squeezing sensation", "value": "tightness"}, {"label": "Sharp or stabbing pain", "value": "sharp"}, {"label": "Burning sensation", "value": "burning"}, {"label": "Dull ache or heaviness", "value": "dull_ache"}, {"label": "Pressure-like discomfort", "value": "pressure"}, {"label": "Radiates to the arms, jaw, or back", "value": "radiating"}], "tableView": true, "customConditional": "show = data.cardiovascular_symptoms?.chest_pain;"}, {"key": "heading_palpitations", "html": "<h4>Details About Your Palpitations</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.cardiovascular_symptoms?.palpitations;"}, {"key": "palpitations_onset", "data": {"values": [{"label": "Less than 1 hour ago", "value": "<1_hour"}, {"label": "1-3 hours ago", "value": "1-3_hours"}, {"label": "4-6 hours ago", "value": "4-6_hours"}, {"label": "6-12 hours ago", "value": "6-12_hours"}, {"label": "12-24 hours ago", "value": "12-24_hours"}, {"label": "1-2 days ago", "value": "1-2_days"}, {"label": "3-7 days ago", "value": "3-7_days"}, {"label": "1-2 weeks ago", "value": "1-2_weeks"}, {"label": "2-4 weeks ago", "value": "2-4_weeks"}, {"label": "1-3 months ago", "value": "1-3_months"}, {"label": "4-6 months ago", "value": "4-6_months"}, {"label": "7-12 months ago", "value": "7-12_months"}, {"label": "More than 1 year ago", "value": ">1_year"}, {"label": "Ongoing since as long as I can remember", "value": "ongoing"}, {"label": "I'm not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "When did you first notice the palpitations?", "widget": "html5", "validate": {"required": true}, "tableView": true, "placeholder": "Select the time range", "customConditional": "show = data.cardiovascular_symptoms?.palpitations;"}, {"key": "palpitations_description", "type": "selectboxes", "input": true, "label": "How would you describe your palpitations? (Select all that apply)", "values": [{"label": "Feels like my heart is racing", "value": "heart_racing"}, {"label": "Feels like my heart is skipping beats", "value": "skipping_beats"}, {"label": "Feels like fluttering in my chest", "value": "fluttering"}, {"label": "Feels like pounding or thumping", "value": "pounding"}, {"label": "Uncomfortable but not painful", "value": "uncomfortable"}, {"label": "Accompanied by chest pain or dizziness", "value": "associated_symptoms"}], "tableView": true, "customConditional": "show = data.cardiovascular_symptoms?.palpitations;"}, {"key": "palpitations_triggers", "type": "selectboxes", "input": true, "label": "Have you noticed any triggers for your palpitations? (Select all that apply)", "values": [{"label": "Physical activity or exercise", "value": "exercise"}, {"label": "Stress or anxiety", "value": "stress"}, {"label": "Caffeine or energy drinks", "value": "caffeine"}, {"label": "Alcohol consumption", "value": "alcohol"}, {"label": "Medications or supplements", "value": "medications"}, {"label": "None of these seem to trigger it", "value": "none"}], "tableView": true, "customConditional": "show = data.cardiovascular_symptoms?.palpitations;"}, {"key": "palpitations_resolution", "type": "selectboxes", "input": true, "label": "What seems to make your palpitations go away? (Select all that apply)", "values": [{"label": "Resting or sitting down", "value": "rest"}, {"label": "Practicing relaxation techniques (e.g., deep breathing)", "value": "relaxation"}, {"label": "Stopping physical activity", "value": "stopping_exercise"}, {"label": "Taking prescribed medications", "value": "taking_medications"}, {"label": "Avoiding caffeine", "value": "avoiding_caffeine"}, {"label": "Avoiding alcohol", "value": "avoiding_alcohol"}, {"label": "They disappear on their own without any intervention", "value": "spontaneous_resolution"}, {"label": "Nothing seems to make them go away", "value": "nothing"}], "tableView": true, "customConditional": "show = data.cardiovascular_symptoms?.palpitations;"}, {"key": "heading_swelling", "html": "<h4>Details About Your Swelling</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.cardiovascular_symptoms?.swelling;"}, {"key": "swelling_location", "type": "selectboxes", "input": true, "label": "Where have you noticed swelling? (Select all that apply)", "values": [{"label": "Legs", "value": "legs"}, {"label": "<PERSON><PERSON>", "value": "ankles"}, {"label": "Feet", "value": "feet"}, {"label": "Abdomen", "value": "abdomen"}, {"label": "Face", "value": "face"}], "tableView": true, "customConditional": "show = data.cardiovascular_symptoms?.swelling;"}, {"key": "swelling_time", "data": {"values": [{"label": "Less than 24 hours ago", "value": "<24_hours"}, {"label": "1-2 days ago", "value": "1-2_days"}, {"label": "3-7 days ago", "value": "3-7_days"}, {"label": "1-2 weeks ago", "value": "1-2_weeks"}, {"label": "2-4 weeks ago", "value": "2-4_weeks"}, {"label": "More than 1 month ago", "value": "1_month+"}]}, "type": "select", "input": true, "label": "When did you first notice the swelling?", "widget": "html5", "validate": {"required": true}, "tableView": true, "placeholder": "Select the time range", "customConditional": "show = data.cardiovascular_symptoms?.swelling;"}, {"key": "swelling_triggers", "type": "selectboxes", "input": true, "label": "Have you noticed any triggers for your swelling?", "values": [{"label": "Prolonged standing or sitting", "value": "prolonged_standing"}, {"label": "High salt intake", "value": "high_salt"}, {"label": "Heat or hot weather", "value": "hot_weather"}, {"label": "Alcohol", "value": "alcohol"}, {"label": "Injury or trauma", "value": "injury"}, {"label": "None of these", "value": "none"}], "tableView": true, "customConditional": "show = data.cardiovascular_symptoms?.swelling;"}, {"key": "heading_dizziness", "html": "<h4>Details About Your Dizziness</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.cardiovascular_symptoms?.dizziness;"}, {"key": "dizziness_onset", "data": {"values": [{"label": "Less than 1 hour ago", "value": "<1_hour"}, {"label": "1-3 hours ago", "value": "1-3_hours"}, {"label": "4-6 hours ago", "value": "4-6_hours"}, {"label": "6-12 hours ago", "value": "6-12_hours"}, {"label": "12-24 hours ago", "value": "12-24_hours"}, {"label": "1-2 days ago", "value": "1-2_days"}, {"label": "3-7 days ago", "value": "3-7_days"}, {"label": "1-2 weeks ago", "value": "1-2_weeks"}, {"label": "2-4 weeks ago", "value": "2-4_weeks"}, {"label": "More than 1 month ago", "value": "1_month+"}]}, "type": "select", "input": true, "label": "When did you first experience dizziness?", "widget": "html5", "validate": {"required": true}, "tableView": true, "placeholder": "Select the time range", "customConditional": "show = data.cardiovascular_symptoms?.dizziness;"}, {"key": "dizziness_description", "type": "selectboxes", "input": true, "label": "How would you describe your dizziness?", "values": [{"label": "Feeling lightheaded or faint", "value": "lightheaded"}, {"label": "Spinning sensation (vertigo)", "value": "spinning"}, {"label": "Unsteady or off-balance", "value": "unsteady"}, {"label": "Nausea associated with dizziness", "value": "nausea"}], "tableView": true, "customConditional": "show = data.cardiovascular_symptoms?.dizziness;"}, {"key": "cardio_specialist_consultation", "type": "radio", "input": true, "label": "Have you seen a healthcare specialist for an examination or consultation about these symptoms?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "I don't remember", "value": "dont_remember"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = Object.values(data.cardiovascular_symptoms || {}).some(value => value);"}, {"key": "heading_breathing_symptoms", "html": "<h2>Breathing Symptoms</h2><p>Please share if you are experiencing any symptoms related to breathing.</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.screening_indication?.current_symptoms || data.confirm_no_symptoms === 'have_symptoms';"}, {"key": "respiratory_symptoms", "type": "selectboxes", "input": true, "label": "Do you have any of the following symptoms?", "values": [{"label": "Coughing", "value": "cough"}, {"label": "Shortness of breath", "value": "shortness_of_breath"}, {"label": "Wheezing", "value": "wheezing"}, {"label": "Chest tightness", "value": "chest_tightness"}], "tableView": true, "customConditional": "show = data.screening_indication?.current_symptoms || data.confirm_no_symptoms === 'have_symptoms';", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_respiratory_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "validate": {"custom": "valid = !!data.none_of_the_above_respiratory_symptoms || _.some(_.values(data.respiratory_symptoms));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.screening_indication?.current_symptoms || data.confirm_no_symptoms === 'have_symptoms';"}, {"key": "heading_cough_details", "html": "<h4>Details About Your Cough</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.respiratory_symptoms?.cough;"}, {"key": "cough_onset", "data": {"values": [{"label": "Less than 24 hours ago", "value": "<24_hours"}, {"label": "1-3 days ago", "value": "1-3_days"}, {"label": "4-7 days ago", "value": "4-7_days"}, {"label": "1-2 weeks ago", "value": "1-2_weeks"}, {"label": "2-4 weeks ago", "value": "2-4_weeks"}, {"label": "More than 1 month ago", "value": ">1_month"}]}, "type": "select", "input": true, "label": "When did your cough start?", "widget": "html5", "validate": {"required": true}, "tableView": true, "placeholder": "Select the time range", "customConditional": "show = data.respiratory_symptoms?.cough;"}, {"key": "cough_description", "type": "selectboxes", "input": true, "label": "How would you describe your cough?", "values": [{"label": "Dry cough", "value": "dry_cough"}, {"label": "Wet or productive cough", "value": "productive_cough"}, {"label": "Coughing up blood", "value": "blood_cough"}, {"label": "Persistent or chronic cough", "value": "persistent_cough"}], "tableView": true, "customConditional": "show = data.respiratory_symptoms?.cough;"}, {"key": "cough_triggers", "type": "selectboxes", "input": true, "label": "Have you noticed any triggers for your cough?", "values": [{"label": "Cold air", "value": "cold_air"}, {"label": "Physical activity", "value": "physical_activity"}, {"label": "Dust or allergens", "value": "allergens"}, {"label": "None of these seem to trigger it", "value": "no_triggers"}], "tableView": true, "customConditional": "show = data.respiratory_symptoms?.cough;"}, {"key": "cough_consultation", "type": "radio", "input": true, "label": "Have you consulted a healthcare provider about your cough?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "I don't remember", "value": "dont_remember"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.respiratory_symptoms?.cough;"}, {"key": "heading_wheezing_details", "html": "<h4>Details About Your Wheezing</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.respiratory_symptoms?.wheezing;"}, {"key": "wheezing_onset", "data": {"values": [{"label": "Less than 24 hours ago", "value": "<24_hours"}, {"label": "1-3 days ago", "value": "1-3_days"}, {"label": "4-7 days ago", "value": "4-7_days"}, {"label": "1-2 weeks ago", "value": "1-2_weeks"}, {"label": "2-4 weeks ago", "value": "2-4_weeks"}, {"label": "More than 1 month ago", "value": ">1_month"}]}, "type": "select", "input": true, "label": "When did your wheezing start?", "widget": "html5", "validate": {"required": true}, "tableView": true, "placeholder": "Select the time range", "customConditional": "show = data.respiratory_symptoms?.wheezing;"}, {"key": "wheezing_description", "type": "selectboxes", "input": true, "label": "How would you describe your wheezing?", "values": [{"label": "Occurs with physical activity", "value": "with_activity"}, {"label": "Occurs at rest", "value": "at_rest"}, {"label": "Accompanied by shortness of breath", "value": "with_shortness_of_breath"}, {"label": "Happens mostly at night", "value": "night_wheezing"}, {"label": "Triggered by allergens (e.g., pollen, dust)", "value": "allergen_triggered"}], "tableView": true, "customConditional": "show = data.respiratory_symptoms?.wheezing;"}, {"key": "wheezing_triggers", "type": "selectboxes", "input": true, "label": "Have you noticed any triggers for your wheezing?", "values": [{"label": "Physical activity or exertion", "value": "physical_activity"}, {"label": "Cold air", "value": "cold_air"}, {"label": "Allergens (e.g., pollen, pet dander)", "value": "allergens"}, {"label": "Stress or anxiety", "value": "stress"}, {"label": "Dust or smoke exposure", "value": "dust_smoke"}, {"label": "Respiratory infections (e.g., colds, flu)", "value": "respiratory_infections"}, {"label": "Strong odors or fumes (e.g., perfumes, cleaning products)", "value": "strong_odors"}, {"label": "Weather changes (e.g., humidity, temperature shifts)", "value": "weather_changes"}, {"label": "None of these seem to trigger it", "value": "no_triggers"}], "tableView": true, "customConditional": "show = data.respiratory_symptoms?.wheezing;"}, {"key": "wheezing_alleviators", "type": "selectboxes", "input": true, "label": "What helps reduce or stop your wheezing?", "values": [{"label": "Resting", "value": "resting"}, {"label": "Using an inhaler or medication", "value": "inhaler"}, {"label": "Avoiding allergens or irritants", "value": "avoiding_triggers"}, {"label": "None of these help", "value": "none"}], "tableView": true, "customConditional": "show = data.respiratory_symptoms?.wheezing;"}, {"key": "wheezing_consultation", "type": "radio", "input": true, "label": "Have you consulted a healthcare provider about your wheezing?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "I don't remember", "value": "dont_remember"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.respiratory_symptoms?.wheezing;"}, {"key": "asthma_copd_diagnosis", "type": "radio", "input": true, "label": "Have you been diagnosed with asthma or chronic obstructive pulmonary disease (COPD)?", "values": [{"label": "Yes, asthma", "value": "asthma"}, {"label": "Yes, COPD", "value": "copd"}, {"label": "No", "value": "no"}, {"label": "I don't know", "value": "dont_know"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.wheezing_consultation === 'yes';"}, {"key": "cardio_investigations", "type": "selectboxes", "input": true, "label": "Have you had any of the following tests or procedures for your heart or breathing-related symptoms?", "values": [{"label": "Electrocardiogram (ECG)", "value": "ecg"}, {"label": "Stress test", "value": "stress_test"}, {"label": "Chest X-ray", "value": "chest_xray"}, {"label": "Blood tests (e.g., troponin)", "value": "blood_tests"}, {"label": "Echocardiogram", "value": "echocardiogram"}, {"label": "I haven't had any tests", "value": "no_tests"}], "tableView": true, "customConditional": "show = Object.values(data.cardiovascular_symptoms || {}).some(value => value) || Object.values(data.respiratory_symptoms || {}).some(value => value);"}, {"key": "heading_abdominal_symptoms", "html": "<h2>Abdominal or Stomach Symptoms</h2><p>Provide information about any symptoms you are experiencing in your abdomen or stomach area.</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.screening_indication?.current_symptoms || data.confirm_no_symptoms === 'have_symptoms';"}, {"key": "abdominal_gastrointestinal_symptoms", "type": "selectboxes", "input": true, "label": "Do you have any of the following abdominal or stomach related symptoms?", "values": [{"label": "Abdominal pain", "value": "abdominal_pain"}, {"label": "<PERSON><PERSON><PERSON>", "value": "nausea"}, {"label": "Vomiting", "value": "vomiting"}, {"label": "Diarrhea", "value": "diarrhea"}, {"label": "Constipation", "value": "constipation"}, {"label": "Bloating or gas", "value": "bloating_gas"}, {"label": "Rectal bleeding", "value": "rectal_bleeding"}], "tableView": true, "customConditional": "show = data.screening_indication?.current_symptoms || data.confirm_no_symptoms === 'have_symptoms';", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_abdominal_gastrointestinal_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "validate": {"custom": "valid = !!data.none_of_the_above_abdominal_gastrointestinal_symptoms || _.some(_.values(data.abdominal_gastrointestinal_symptoms));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.screening_indication?.current_symptoms || data.confirm_no_symptoms === 'have_symptoms';"}, {"key": "heading_abdominal_pain", "html": "<h4>Details About Your Abdominal Pain</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.abdominal_gastrointestinal_symptoms?.abdominal_pain;"}, {"key": "abdominal_pain_onset", "data": {"values": [{"label": "Less than 1 hour ago", "value": "<1_hour"}, {"label": "1-6 hours ago", "value": "1-6_hours"}, {"label": "6-12 hours ago", "value": "6-12_hours"}, {"label": "12-24 hours ago", "value": "12-24_hours"}, {"label": "1-2 days ago", "value": "1-2_days"}, {"label": "2-7 days ago", "value": "2-7_days"}, {"label": "1-2 weeks ago", "value": "1-2_weeks"}, {"label": "More than 2 weeks ago", "value": ">2_weeks"}]}, "type": "select", "input": true, "label": "When did your abdominal pain start?", "widget": "html5", "validate": {"required": true}, "tableView": true, "placeholder": "Select the time range", "customConditional": "show = data.abdominal_gastrointestinal_symptoms?.abdominal_pain;"}, {"key": "abdominal_pain_description", "type": "selectboxes", "input": true, "label": "How would you describe your abdominal pain?", "values": [{"label": "Sharp or stabbing pain", "value": "sharp"}, {"label": "Dull or aching pain", "value": "dull"}, {"label": "Cramping pain", "value": "cramping"}, {"label": "Burning sensation", "value": "burning"}, {"label": "Pain that comes and goes", "value": "intermittent"}, {"label": "Pain that is constant", "value": "constant"}], "tableView": true, "customConditional": "show = data.abdominal_gastrointestinal_symptoms?.abdominal_pain;"}, {"key": "abdominal_pain_triggers", "type": "selectboxes", "input": true, "label": "Have you noticed any triggers for your abdominal pain?", "values": [{"label": "After eating", "value": "after_eating"}, {"label": "Before eating (hunger pain)", "value": "before_eating"}, {"label": "After physical activity", "value": "after_activity"}, {"label": "Stress or anxiety", "value": "stress"}, {"label": "None of these seem to trigger it", "value": "no_triggers"}], "tableView": true, "customConditional": "show = data.abdominal_gastrointestinal_symptoms?.abdominal_pain;"}, {"key": "abdominal_pain_relief", "type": "selectboxes", "input": true, "label": "What helps relieve your abdominal pain?", "values": [{"label": "Resting", "value": "rest"}, {"label": "Using over-the-counter medications (e.g., antacids, pain relievers)", "value": "otc_medications"}, {"label": "Avoiding certain foods", "value": "avoiding_foods"}, {"label": "None of these help", "value": "no_relief"}], "tableView": true, "customConditional": "show = data.abdominal_gastrointestinal_symptoms?.abdominal_pain;"}, {"key": "heading_nausea_vomiting", "html": "<h4>Details About Your Nausea or Vomiting</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.abdominal_gastrointestinal_symptoms?.nausea || data.abdominal_gastrointestinal_symptoms?.vomiting;"}, {"key": "nausea_onset", "data": {"values": [{"label": "Less than 6 hours ago", "value": "<6_hours"}, {"label": "6-12 hours ago", "value": "6-12_hours"}, {"label": "12-24 hours ago", "value": "12-24_hours"}, {"label": "1-2 days ago", "value": "1-2_days"}, {"label": "2-7 days ago", "value": "2-7_days"}, {"label": "More than 1 week ago", "value": ">1_week"}]}, "type": "select", "input": true, "label": "When did your nausea or vomiting start?", "widget": "html5", "validate": {"required": true}, "tableView": true, "placeholder": "Select the time range", "customConditional": "show = data.abdominal_gastrointestinal_symptoms?.nausea || data.abdominal_gastrointestinal_symptoms?.vomiting;"}, {"key": "nausea_description", "type": "selectboxes", "input": true, "label": "How would you describe your nausea or vomiting?", "values": [{"label": "Mild nausea", "value": "mild_nausea"}, {"label": "Severe nausea", "value": "severe_nausea"}, {"label": "Occasional vomiting", "value": "occasional_vomiting"}, {"label": "Frequent vomiting", "value": "frequent_vomiting"}, {"label": "Dry heaving without vomiting", "value": "dry_heaving"}], "tableView": true, "customConditional": "show = data.abdominal_gastrointestinal_symptoms?.nausea || data.abdominal_gastrointestinal_symptoms?.vomiting;"}, {"key": "header_lipid_questions", "html": "</br><h2>Cholesterol History&nbsp;</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "previous_cholesterol_testing", "type": "radio", "input": true, "label": "Have you had cholesterol testing in the past?", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "I Don't Know", "value": "i_dont_know", "shortcut": ""}], "tableView": true, "confirm_label": "Previous Cholesterol Testing:", "optionsLabelPosition": "right"}, {"key": "last_cholesterol_test", "data": {"values": [{"label": "< 1 month ago", "value": "<1_month"}, {"label": "1-3 months ago", "value": "1-3_months"}, {"label": "3-6 months ago", "value": "3-6_months"}, {"label": "6-12 months ago", "value": "6-12_months"}, {"label": "1 - 2 years ago", "value": "1-2_years"}, {"label": "2 - 3 years ago", "value": "2-3_years"}, {"label": "3-5 years ago", "value": "3-5_years"}, {"label": "5 - 10 years ago", "value": "5-10_years"}, {"label": "More than 10 years ago", "value": "10+_years"}, {"label": "I don't remember", "value": "dont_remember"}]}, "type": "select", "input": true, "label": "When did you have your last cholesterol testing completed outside of using TeleTest?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Last Cholesterol Test:", "customConditional": "show = data.previous_cholesterol_testing == 'yes';", "optionsLabelPosition": "right"}, {"key": "previous_abnormal_lipids", "type": "radio", "input": true, "label": "Do you recall if you your previous cholesterol testing was abnormal (i.e. high or borderline high cholesterol)?", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "I Don't Know", "value": "doesn't_know", "shortcut": ""}], "tableView": true, "confirm_label": "Previous Abnormal Cholesterol Profile:", "customConditional": "show = data.previous_cholesterol_testing == 'yes';", "optionsLabelPosition": "right"}, {"key": "select_abnormal_cholesterol_values", "type": "selectboxes", "input": true, "label": "Please check any of the following apply to your prior tests (HDL = good cholesterol, LDL = bad cholesterol, triglycerides = fat in blood):", "values": [{"label": "High LDL", "value": "high_ldl", "shortcut": ""}, {"label": "Low LDL", "value": "low_ldl", "shortcut": ""}, {"label": "High HDL", "value": "high_hdl", "shortcut": ""}, {"label": "Low HDL", "value": "low_hdl", "shortcut": ""}, {"label": "High Triglycerides", "value": "high_tg", "shortcut": ""}, {"label": "Low Triglycerides", "value": "low_tg", "shortcut": ""}, {"label": "I Don't Know", "value": "doesn't know", "shortcut": ""}], "inputType": "checkbox", "tableView": true, "confirm_label": "Abnormal Cholesterol Values:", "customConditional": "show = data.previous_abnormal_lipids == 'yes';", "optionsLabelPosition": "right"}, {"key": "hdl_value_ranges", "data": {"values": [{"label": "<1.0 mmol/L (40 mg/dL) - Low (Increased Risk)", "value": "hdl_low"}, {"label": "1.0-1.5 mmol/L (40-60 mg/dL) - Borderline", "value": "hdl_borderline"}, {"label": ">1.55, mmol/L (60 mg/dL) - Optimal (Protective)", "value": "hdl_high"}, {"label": "I don't know my HDL level", "value": "unknown"}]}, "type": "select", "input": true, "label": "Please select the range that applies to your HDL (good cholesterol):", "widget": "html5", "validate": {"required": true}, "tableView": true, "placeholder": "Select the range", "confirm_label": "HDL Value Range:", "customConditional": "show = data.select_abnormal_cholesterol_values?.high_hdl || data.select_abnormal_cholesterol_values?.low_hdl;"}, {"key": "ldl_value_ranges", "data": {"values": [{"label": "<2.6 mmol/L (100 mg/dL) - Optimal", "value": "<2.6 mmol/L (100 mg/dL)"}, {"label": "2.6-3.3 mmol/L (100-130 mg/dL) - Near Optimal", "value": "2.6-3.3 mmol/L (100-130 mg/dL)"}, {"label": "3.4-4.1 mmol/L (130-160 mg/dL) - Borderline High", "value": "3.4-4.1 mmol/L (130-160 mg/dL)"}, {"label": "4.2-4.9 mmol/L (160-190 mg/dL) - High", "value": "4.2-4.9 mmol/L (160-190 mg/dL)"}, {"label": ">4.9 mmol/L (190 mg/dL) - Very High", "value": ">4.9 mmol/L (190 mg/dL)"}, {"label": "I Don't Know", "value": "doesn't_know"}]}, "type": "select", "input": true, "label": "Please select the range that applies to your LDL (bad cholesterol):", "widget": "html5", "validate": {"required": true}, "tableView": true, "placeholder": "Select the range", "confirm_label": "LDL Value Range:", "customConditional": "show = data.select_abnormal_cholesterol_values?.high_ldl || data.select_abnormal_cholesterol_values?.low_ldl;"}, {"key": "triglyceride_value_ranges", "data": {"values": [{"label": "<1.7 mmol/L (150 mg/dL) - Normal", "value": "<1.7 mmol/L (150 mg/dL)"}, {"label": "1.7-2.2 mmol/L (150-200 mg/dL) - Borderline High", "value": "1.7-2.2 mmol/L (150-200 mg/dL)"}, {"label": "2.3-5.6 mmol/L (200-500 mg/dL) - High", "value": "2.3-5.6 mmol/L (200-500 mg/dL)"}, {"label": ">5.6 mmol/L (500 mg/dL) - Very High", "value": ">5.6 mmol/L (500 mg/dL)"}, {"label": "I Don't Know", "value": "doesn't_know"}]}, "type": "select", "input": true, "label": "Please select the range that applies to your triglycerides:", "widget": "html5", "validate": {"required": true}, "tableView": true, "placeholder": "Select the range", "confirm_label": "Triglyceride Value Range:", "customConditional": "show = data.select_abnormal_cholesterol_values?.high_tg || data.select_abnormal_cholesterol_values?.low_tg;"}, {"key": "previous_lipoprotein_a", "type": "radio", "input": true, "label": "Have you had a blood test for Lipoprotein(a) in the past?", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "I dont know what that is", "value": "i_dont_know", "shortcut": ""}], "tableView": true, "confirm_label": "Previous Lipoprotein(a) Test:", "optionsLabelPosition": "right"}, {"key": "lpa_level_select", "data": {"values": [{"label": "< 75 nmol/L (< 30 mg/dL)", "value": "<75_nmol_l"}, {"label": "75-125 nmol/L (30-50 mg/dL)", "value": "75-125_nmol_l"}, {"label": "125-250 nmol/L (50-100 mg/dL)", "value": "125-250_nmol_l"}, {"label": "> 250 nmol/L (> 100 mg/dL)", "value": ">250_nmol_l"}, {"label": "I don't know my Lp(a) level", "value": "unknown"}]}, "type": "select", "input": true, "label": "Please select your Lipoprotein(a) level (if known):", "widget": "html5", "validate": {"required": true}, "tableView": true, "placeholder": "Select your Lp(a) level", "confirm_label": "Lipoprotein(a) Level:", "customConditional": "show = data.previous_lipoprotein_a === 'yes';"}, {"key": "header_diabetic_questions", "html": "<h2>Diabetic Screening&nbsp;</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "previous_diabetic_testing", "type": "radio", "input": true, "label": "Have you had diabetic screening in the past?", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "I Don't Know", "value": "i_dont_know", "shortcut": ""}], "tableView": true, "confirm_label": "Previous Diabetic Screening Completed:", "optionsLabelPosition": "right"}, {"key": "last_diabetic_test", "data": {"values": [{"label": "< 1 month ago", "value": "<1month"}, {"label": "1-3 months ago", "value": "1-3months"}, {"label": "3-6 months ago", "value": "3-6months"}, {"label": "6-9 months ago", "value": "6-9months"}, {"label": "9-12 months ago", "value": "9-12months"}, {"label": "1 - 2 years ago", "value": "1-2years"}, {"label": "2 - 3 years ago", "value": "2-3years"}, {"label": "3-5 years ago", "value": "3-5years"}, {"label": "5 - 10 years ago", "value": "5-10years"}, {"label": "More than 10 years ago", "value": "10+years"}, {"label": "I don't remember", "value": "dont_remember"}]}, "type": "select", "input": true, "label": "When did you have your last diabetic screening completed outside of using TeleTest?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Last Diabetic Test:", "customConditional": "show = data.previous_diabetic_testing == 'yes';", "optionsLabelPosition": "right"}, {"key": "previous_abnormal_sugar_levels", "type": "radio", "input": true, "label": "Do you recall if your previous diabetes testing was abnormal (i.e. high or borderline high sugars)?", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "I Don't Know", "value": "doesn't_know", "shortcut": ""}], "tableView": true, "confirm_label": "Previous Abnormal Blood Sugar Test:", "customConditional": "show = data.previous_diabetic_testing == 'yes';", "optionsLabelPosition": "right"}, {"key": "blood_sugar_level", "data": {"values": [{"label": "Less than 6.0 mmol/L (<108 mg/dL)", "value": "<6mmol"}, {"label": "6.0-6.9 mmol/L (108-124 mg/dL)", "value": "6-6.9mmol"}, {"label": "7.0-7.9 mmol/L (125-143 mg/dL)", "value": "7-7.9mmol"}, {"label": "8.0-9.9 mmol/L (144-179 mg/dL)", "value": "8-9.9mmol"}, {"label": "10.0 mmol/L or higher (≥180 mg/dL)", "value": "10+mmol"}, {"label": "I don't know", "value": "unknown"}]}, "type": "select", "input": true, "label": "What was your blood sugar level during your last abnormal test? (if known)", "widget": "html5", "validate": {"required": false}, "tableView": true, "placeholder": "Select the range", "confirm_label": "Blood Sugar Level:", "customConditional": "show = data.previous_abnormal_sugar_levels === 'yes';"}, {"key": "a1c_level", "data": {"values": [{"label": "Less than 5.5%", "value": "<5.5"}, {"label": "5.6-5.9% (At risk)", "value": "5.6-5.9"}, {"label": "6.0-6.4% (Prediabetes)", "value": "6.0-6.4"}, {"label": "6.5-7.0%", "value": "6.5-7.0"}, {"label": "7.1-8.0%", "value": "7.1-8.0"}, {"label": "8.1-9.0%", "value": "8.1-9.0"}, {"label": "Greater than 9.0%", "value": ">9.0"}, {"label": "I don't know", "value": "unknown"}]}, "type": "select", "input": true, "label": "What was your A1C level during your last abnormal test? (if known)", "widget": "html5", "validate": {"required": false}, "tableView": true, "placeholder": "Select the range", "confirm_label": "A1C Level:", "customConditional": "show = data.previous_abnormal_sugar_levels === 'yes';"}, {"key": "previous_diabetic_diagnosis", "type": "radio", "input": true, "label": "Have you been diagnosed with any of the following?", "inline": false, "values": [{"label": "Diabetes", "value": "diabetes", "shortcut": ""}, {"label": "Pre-Diabetes", "value": "pre-diabetes", "shortcut": ""}, {"label": "Impaired Fasting Glucose", "value": "impaired_fasting_glucose", "shortcut": ""}, {"label": "None of the Above", "value": "none_of_the_above", "shortcut": ""}], "tableView": true, "customConditional": "show = data.previous_diabetic_testing == 'yes';", "optionsLabelPosition": "right"}, {"key": "header_colon_cancer", "html": "<h2>Colon Cancer Screening&nbsp;</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "colon_cancer_history", "type": "radio", "input": true, "label": "Do you have a 1st degree relatives <strong>(i.e. mother, brother, father, sister)</strong> with a history of colon cancer?", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}], "tableView": true, "optionsLabelPosition": "right"}, {"key": "colon_cancer_family_members", "type": "selectboxes", "input": true, "label": "Who in your family has been diagnosed with colon cancer:", "values": [{"label": "Mother", "value": "mother", "shortcut": ""}, {"label": "Father", "value": "father", "shortcut": ""}, {"label": "Brother", "value": "brother", "shortcut": ""}, {"label": "Sister", "value": "sister", "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}], "inputType": "checkbox", "tableView": true, "customConditional": "show = data.colon_cancer_history == 'yes';", "optionsLabelPosition": "right"}, {"key": "colon_cancer_history_positive", "data": {"values": [{"label": "18-20 years", "value": "18-20_years_old"}, {"label": "21-23 years", "value": "21-23_years_old"}, {"label": "24-26 years", "value": "24-26_years_old"}, {"label": "27-29 years", "value": "27-29_years_old"}, {"label": "30-32 years", "value": "30-32_years_old"}, {"label": "33-35 years", "value": "33-35_years_old"}, {"label": "36-38 years", "value": "36-38_years_old"}, {"label": "39-41 years", "value": "39-41_years_old"}, {"label": "42-44 years", "value": "42-44_years_old"}, {"label": "45-47 years", "value": "45-47_years_old"}, {"label": "48-50 years", "value": "48-50_years_old"}, {"label": "51-55 years", "value": "51-55_years_old"}, {"label": "56-60 years", "value": "56-60_years_old"}, {"label": "61-65 years", "value": "61-65_years_old"}, {"label": "66-70 years", "value": "66-70_years_old"}, {"label": "71-75 years", "value": "71-75_years_old"}, {"label": "76+ years", "value": "76+_years_old"}]}, "type": "select", "input": true, "label": "How old was your <strong>youngest</strong> family member at the time they were diagnosed with colon cancer?", "widget": "html5", "validate": {"required": true}, "tableView": true, "placeholder": "Select the age range", "customConditional": "show = data.colon_cancer_history == 'yes';"}, {"key": "early_colonoscopy_advice", "type": "radio", "input": true, "label": "Based on your family history, you require a colonoscopy 10 years earlier than their age at diagnosis? (For example, if they were diagnosed at 45, you should obtain screening at 35.) You can arrange this through a family doctor or walk-in clinic.", "values": [{"label": "Yes, I understand", "value": "yes"}, {"label": "No, I would like more information", "value": "no"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = ['18-20_years_old', '21-23_years_old', '24-26_years_old', '27-29_years_old', '30-32_years_old', '33-35_years_old', '36-38_years_old', '39-41_years_old', '42-44_years_old', '45-47_years_old', '48-50_years_old', '51-55_years_old', '56-60_years_old'].includes(data.colon_cancer_history_positive);"}, {"key": "header_breast_cancer", "html": "<h2>Breast Cancer Sreening&nbsp;</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.sex == 'male';"}, {"key": "breast_cancer_history", "type": "radio", "input": true, "label": "Do you have a family history of breast cancer in 1st degree relatives <strong>(mother, sister, brother, father)</strong>?", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}], "tableView": true, "customConditional": "show = data.sex == 'male';", "optionsLabelPosition": "right"}, {"key": "breast_cancer_family_members", "type": "selectboxes", "input": true, "label": "Who in your family has been diagnosed with breast cancer:", "values": [{"label": "Mother", "value": "mother", "shortcut": ""}, {"label": "Father", "value": "father", "shortcut": ""}, {"label": "Brother", "value": "brother", "shortcut": ""}, {"label": "Sister", "value": "sister", "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}], "inputType": "checkbox", "tableView": true, "customConditional": "show = data.breast_cancer_history == 'yes';", "optionsLabelPosition": "right"}, {"key": "breast_cancer_history_positive", "type": "radio", "input": true, "label": "How old was your youngest family member at the time they were diagnosed with breast cancer?", "inline": false, "values": [{"label": "18-20 years", "value": "18-20_years_old"}, {"label": "21-23 years", "value": "21-23_years_old"}, {"label": "24-26 years", "value": "24-26_years_old"}, {"label": "27-29 years", "value": "27-29_years_old"}, {"label": "30-32 years", "value": "30-32_years_old"}, {"label": "33-35 years", "value": "33-35_years_old"}, {"label": "36-38 years", "value": "36-38_years_old"}, {"label": "39-41 years", "value": "39-41_years_old"}, {"label": "42-44 years", "value": "42-44_years_old"}, {"label": "45-47 years", "value": "45-47_years_old"}, {"label": "48-50 years", "value": "48-50_years_old"}, {"label": "51-55 years", "value": "51-55_years_old"}, {"label": "56-60 years", "value": "56-60_years_old"}, {"label": "61-65 years", "value": "61-65_years_old"}, {"label": "66-70 years", "value": "66-70_years_old"}, {"label": "71-75 years", "value": "71-75_years_old"}, {"label": "76+ years", "value": "76+_years_old"}], "tableView": true, "customConditional": "show = data.breast_cancer_history == 'yes';", "optionsLabelPosition": "right"}, {"key": "header_risk_factors", "html": "<h2>Lifestyle Changes&nbsp;</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "repeat_testing_rf", "type": "selectboxes", "input": true, "label": "Please check if any of the following apply to you since your last screening testing (if applicable):", "values": [{"label": "I've gained more than 5% of my body weight", "value": "weight_gain"}, {"label": "I've started smoking cigarettes or vaping", "value": "started_smoking"}, {"label": "I started on a new medication that requires monitoring", "value": "medication_monitoring_indication"}, {"label": "I've made significant lifestyle changes (e.g., changed my diet, or physical activity level)", "value": "lifestyle_changes"}], "inputType": "checkbox", "tableView": true, "confirm_label": "Repeat Testing Risk Factors:", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_repeat_testing_rf", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select an option."}, "validate": {"custom": "valid = !!data.none_of_the_above_repeat_testing_rf || _.some(_.values(data.repeat_testing_rf));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "header_hemoglobinopathy", "html": "<h2>Blood Conditions&nbsp;</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "hemoglobinopathy_rf", "type": "selectboxes", "input": true, "label": "Have you been diagnosed with any of the following:", "values": [{"label": "Alpha Thalassemia", "value": "alpha_thalassemia"}, {"label": "Beta Thalassemia", "value": "beta_thalassemia"}, {"label": "Sickle Cell Trait", "value": "sickle_cell_trait"}, {"label": "Sickle Cell Disease", "value": "sickle_cell_disease"}, {"label": "Iron Deficiency Anemia", "value": "iron_deficiency_anemia"}], "inputType": "checkbox", "tableView": true, "optionsLabelPosition": "right"}, {"key": "none_of_the_above_hemoglobinopathy_rf", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a diagnosis."}, "validate": {"custom": "valid = !!data.none_of_the_above_hemoglobinopathy_rf || _.some(_.values(data.hemoglobinopathy_rf));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "heading_additional_lab_testing", "html": "<h2>Additional Lab Testing Needs</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "additional_lab_testing_factors", "type": "selectboxes", "input": true, "label": "Do any of the following apply to you?", "values": [{"label": "I drink alcohol on a daily basis", "value": "daily_alcohol_consumption"}, {"label": "I follow a vegetarian or vegan diet", "value": "vegetarian_vegan_diet"}, {"label": "I have a history of low iron levels", "value": "iron_deficiency_history"}, {"label": "None of the above", "value": "none_of_the_above"}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "confirm_label": "Additional Lab Testing Factors:", "optionsLabelPosition": "right"}, {"key": "heading_prior_lab_values", "html": "<h2>Prior Lab Values</h2>", "type": "content", "input": false, "label": "Content", "tableView": false}, {"key": "prior_tests_completed", "type": "selectboxes", "input": true, "label": "Have you had any of the following tests completed?", "values": [{"label": "Kidney function (eGFR)", "value": "egfr"}, {"label": "CBC (Complete Blood Count)", "value": "cbc"}, {"label": "Ferritin (iron levels)", "value": "ferritin"}, {"label": "I have not had these tests completed", "value": "no_prior_cbc_egfr_ferritin"}], "inputType": "checkbox", "tableView": true, "optionsLabelPosition": "right"}, {"key": "heading_kidney_function", "html": "<h3>Kidney Function (eGFR)</h3><p>Please provide details about your kidney function test.</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.prior_tests_completed?.egfr;"}, {"key": "last_kidney_function_test", "data": {"values": [{"label": "< 3 months ago", "value": "<3months"}, {"label": "3-6 months ago", "value": "3-6_months"}, {"label": "6-12 months ago", "value": "6-12_months"}, {"label": "1-2 years ago", "value": "1-2_years"}, {"label": "2-3 years ago", "value": "2-3_years"}, {"label": "3-5 years ago", "value": "3-5_years"}, {"label": "More than 5 years ago", "value": "5+_years"}]}, "type": "select", "input": true, "label": "When was your last kidney function test completed?", "widget": "html5", "validate": {"required": true}, "tableView": true, "placeholder": "Select the time range", "confirm_label": "Last Kidney Function Test:", "customConditional": "show = data.prior_tests_completed?.egfr;"}, {"key": "prior_kidney_function_value", "data": {"values": [{"label": "I don't remember my result", "value": "dont_remember"}, {"label": "eGFR > 90", "value": "egfr_90+"}, {"label": "eGFR 85-89", "value": "egfr_85-89"}, {"label": "eGFR 80-84", "value": "egfr_80-84"}, {"label": "eGFR 75-79", "value": "egfr_75-79"}, {"label": "eGFR 70-74", "value": "egfr_70-74"}, {"label": "eGFR 65-69", "value": "egfr_65-69"}, {"label": "eGFR 60-64", "value": "egfr_60-64"}, {"label": "eGFR 55-59", "value": "egfr_55-59"}, {"label": "eGFR 50-54", "value": "egfr_50-54"}, {"label": "eGFR 45-49", "value": "egfr_45-49"}, {"label": "eGFR < 45", "value": "egfr_<45"}]}, "type": "select", "input": true, "label": "What was your most recent eGFR measurement?", "widget": "html5", "validate": {"required": true}, "tableView": true, "placeholder": "Select the value range", "confirm_label": "eGFR Value Range:", "customConditional": "show = data.prior_tests_completed?.egfr;"}, {"key": "heading_cbc", "html": "<h3>CBC (Complete Blood Count)</h3><p>Please provide details about your CBC test.</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.prior_tests_completed?.cbc;"}, {"key": "last_cbc_test", "data": {"values": [{"label": "< 3 months ago", "value": "<3months"}, {"label": "3-6 months ago", "value": "3-6_months"}, {"label": "6-12 months ago", "value": "6-12_months"}, {"label": "1-2 years ago", "value": "1-2_years"}, {"label": "2-3 years ago", "value": "2-3_years"}, {"label": "3-5 years ago", "value": "3-5_years"}, {"label": "More than 5 years ago", "value": "5+_years"}]}, "type": "select", "input": true, "label": "When was your last CBC test completed?", "widget": "html5", "validate": {"required": true}, "tableView": true, "placeholder": "Select the time range", "confirm_label": "Last CBC Test:", "customConditional": "show = data.prior_tests_completed?.cbc;"}, {"key": "prior_cbc_value", "data": {"values": [{"label": "I don't remember my result", "value": "dont_remember"}, {"label": "< 70 g/L", "value": "<70"}, {"label": "70-100 g/L", "value": "70-100"}, {"label": "101-120 g/L", "value": "101-120"}, {"label": "121-150 g/L", "value": "121-150"}, {"label": "> 150 g/L", "value": ">150"}]}, "type": "select", "input": true, "label": "What was your most recent hemoglobin level (CBC result)?", "widget": "html5", "validate": {"required": true}, "tableView": true, "placeholder": "Select your hemoglobin level", "confirm_label": "Hemoglobin Level:", "customConditional": "show = data.prior_tests_completed?.cbc;"}, {"key": "heading_ferritin", "html": "<h3><PERSON><PERSON><PERSON> (Iron Levels)</h3><p>Please provide details about your ferritin test.</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.prior_tests_completed?.ferritin;"}, {"key": "last_ferritin_test", "data": {"values": [{"label": "< 3 months ago", "value": "<3months"}, {"label": "3-6 months ago", "value": "3-6_months"}, {"label": "6-12 months ago", "value": "6-12_months"}, {"label": "1-2 years ago", "value": "1-2_years"}, {"label": "2-3 years ago", "value": "2-3_years"}, {"label": "3-5 years ago", "value": "3-5_years"}, {"label": "More than 5 years ago", "value": "5+_years"}]}, "type": "select", "input": true, "label": "When was your last ferritin test completed?", "widget": "html5", "validate": {"required": true}, "tableView": true, "placeholder": "Select the time range", "confirm_label": "Last Ferritin Test:", "customConditional": "show = data.prior_tests_completed?.ferritin;"}, {"key": "prior_ferritin_value", "data": {"values": [{"label": "I don't remember my result", "value": "dont_remember"}, {"label": "< 15 μg/L", "value": "<15"}, {"label": "15-30 μg/L", "value": "15-30"}, {"label": "31-100 μg/L", "value": "31-100"}, {"label": "101-300 μg/L", "value": "101-300"}, {"label": "> 300 μg/L", "value": ">300"}]}, "type": "select", "input": true, "label": "What was your most recent ferritin level?", "widget": "html5", "validate": {"required": true}, "tableView": true, "placeholder": "Select your ferritin level", "confirm_label": "Ferritin Level:", "customConditional": "show = data.prior_tests_completed?.ferritin;"}, {"key": "intake_denial_reasons", "type": "textfield", "input": true, "label": "Intake Denial Reasons:", "hidden": true, "disabled": true, "multiple": true, "adminFlag": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "value = [];", "refreshOnChange": true}, {"key": "intake_kind", "type": "textfield", "input": true, "label": "Intake Kind", "hidden": true, "disabled": true, "tableView": true, "clearOnHide": false, "calculateValue": "value = !data.intake_denial_reasons?.length ? 'mail' : 'chat';"}, {"key": "intake_mt_key", "type": "textfield", "input": true, "label": "Intake Mail Template Key", "hidden": true, "disabled": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.intake_kind=='mail'?'intake-metabolic':'intake-denial'"}, {"key": "allowed_rt_keys", "type": "textfield", "input": true, "label": "Allowed Rep<PERSON>", "hidden": true, "disabled": true, "multiple": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.intake_mt_key=='intake-denial'?[]:['get-req','appointment-intake','edit-intake']"}]}