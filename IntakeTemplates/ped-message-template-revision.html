{% load template_extras %}
{% with data=questionnaire.hpc.data %}
{% with summary=questionnaire.raw_formio_summary %}
{% with insured=questionnaire.insured_assays.all %}
{% with uninsured=questionnaire.uninsured_assays.all %}

<p>Hi {{ patient.name }},</p>

<p>This is {{ doctor.name }} (CPSO #{{ doctor.cpso_number }}) &amp; clinic contact phone {{ doctor.phone }}.</p>

<p>I've reviewed your intake regarding current or past use of performance‑enhancing drugs (PEDs). If you had difficulty with any questions or feel your answers didn't reflect your current use or goals, we can arrange a secure follow‑up chat. Otherwise, we'll proceed with the plan below.</p>

<h5>Lab Testing Requested</h5>

{% if insured %}
<p><strong>Insured Tests:</strong></p>
<ul>
  {% for a in insured %}<li>{{ a.name }} ({{ a.test_type }} test)</li>{% endfor %}
</ul>
{% endif %}

{% if uninsured %}
<p><strong>Uninsured Tests:</strong></p>
<ul>
  {% for a in uninsured %}<li>{{ a.name }} ({{ a.test_type }} test)</li>{% endfor %}
</ul>
<p>Uninsured testing may include non‑essential monitoring or private‑pay options not covered by OHIP. These can be declined at the lab.</p>
{% endif %}

<h5>Plan</h5>

<p>Please review your intake summary to ensure accuracy. If everything looks correct, you may proceed to download your lab requisition.</p>
<ol>
  <li>Select your preferred laboratory and complete the required blood work.</li>
  <li>Your results will be uploaded to the portal once available.</li>
  <li>If any abnormalities are identified, we'll contact you to arrange a secure follow‑up chat.</li>
</ol>

<hr>

<h5>Medical Summary</h5>
{% with hqas=summary|confirm:"Past Medical History:medication_allergies,past_surgeries,health_conditions;Reason for PED Testing:ped_use_status,primary_reason_for_use,current_ped_cycle_phase;Current PED Regimen:ped_drug,specific_oral_aas,specific_inj_aas,specific_serms,specific_ais,specific_sarms,specific_epo,specific_hcg,specific_fat_burners" %}
{% for heading,qas in hqas.items %}
<h6>{{heading}}</h6>
<ul>
  {% for qa in qas %}<li>{{qa|safe}}</li>{% endfor %}
</ul>
{% endfor %}{% endwith %}

<!-- symptoms present -->
{% with hqas=summary|confirm:"Chest Pain:chest_pain_onset,chest_pain_character,chest_pain_triggers,chest_pain_relievers,chest_pain_location,chest_pain_radiation,chest_pain_pattern;Palpitations:palpitations_onset,palpitations_rhythm,palpitations_triggers,palpitations_duration,palpitations_associated_symptoms;Swelling:swelling_location,swelling_sidedness,swelling_onset,swelling_timing,swelling_pitting;Dizziness:dizziness_onset,dizziness_frequency,dizziness_character,dizziness_timing,fainting_loss_consciousness;Cough:cough_duration,cough_coughing_blood,cough_feeling_unwell,cough_with_cold,cough_progression;Shortness of breath:sob_triggers;Wheezing:wheezing_timing,wheezing_relief,wheezing_asthma_history;Abdominal Pain:abdominal_pain_location,onset_abdominal_pain,abdominal_pain_typical_vs_new;Bloating or gas:bloating_pattern,onset_bloating,bloating_typical_vs_new;Constipation:constipation_frequency,onset_constipation,constipation_typical_vs_new;Diarrhea:diarrhea_features,onset_diarrhea,diarrhea_typical_vs_new;Nausea:nausea_timing,onset_nausea,nausea_typical_vs_new;Rectal bleeding:onset_rectal_bleeding,rectal_bleeding_appearance,rectal_bleeding_typical_vs_new;Vomiting:onset_vomiting,vomit_appearance,vomiting_typical_vs_new;Liver:liver_dysfunction_symptoms;Anxiety:mental_health_onset,mental_health_ped_relationship,mental_health_prior_diagnosis,mental_health_diagnosis_source,mental_health_prior_treatment,mental_health_therapy_discussion" %}
{% if hqas %}
<h5>Symptoms Present</h5>
<ul>
  {% for heading,qas in hqas.items %}
  <li>{{heading}}</li>
  <ul>
    {% for qa in qas %}<li>{{qa|safe}}</li>{% endfor %}
  </ul>
  {% endfor %}
</ul>
{% endif %}{% endwith %}
<!-- symptoms NOT present -->
<h5>Symptoms NOT Present</h5>
<ul>
  {% for qa in summary|confirm:"cardiac_symptoms_not_present,respiratory_symptoms_not_present,gastrointestinal_symptoms_not_present,liver_symptoms_not_present,mental_health_symptoms_not_present" %}
  <li>{{qa|safe}}</li>
  {% endfor %}
</ul>
<!-- blood pressure & imaging -->
{% with hqas=summary|confirm:"Blood Pressure Monitoring:systolic_bp,diastolic_bp,bp_reading_date,bp_monitoring_frequency,bp_readings_per_sitting;Prior Imaging Tests:prior_imaging_tests" %}
{% if hqas %} {% for heading,qas in hqas.items %}
<h6>{{heading}}</h6>
<ul>
  {% for qa in qas %}<li>{{qa|safe}}</li>{% endfor %}
</ul>
{% endfor %}{% endif %}{% endwith %}

<hr>

{% if data.mental_health_suicidal_check or data.cough_urgent_warning_understanding %}
<h2 style="text-align:left;margin-top:30px;">Recommendations</h2>
<ul>
  {% if data.mental_health_suicidal_check == "yes" %}
    <li>Mental Health: Seek emergency care for active thoughts of self‑harm.</li>
  {% elif data.mental_health_suicidal_check == "no" %}
    <li>Mental Health: Safety plan discussed — understands when to seek care if thoughts arise.</li>
  {% endif %}
  {% if data.cough_urgent_warning_understanding == "understand" %}
    <li>Respiratory Warning: Acknowledged understanding of when to seek urgent care.</li>
  {% endif %}
</ul>
{% endif %}

<h2 style="text-align:left;margin-top:30px;">Important Safety Reminders</h2>
<ul>
  <li><strong>Blood Pressure:</strong> Monitor monthly. Seek care if BP is over 140/90.</li>
  <li><strong>Cycle Guidance:</strong> We do not advise on PED regimens or dosing adjustments.</li>
  <li><strong>Physical Exams:</strong> In‑person exams every 3–6 months are recommended.</li>
  <li><strong>Product Quality:</strong> Many PEDs are contaminated or mislabeled.</li>
  <li><strong>Normal Bloodwork ≠ No Risk:</strong> PED harms are not always detectable in labs alone.</li>
</ul>

<p style="margin-top:30px;">
    <strong>Confirmation:</strong> By completing and submitting this intake form, you confirm that the information provided is accurate to the best of your knowledge. You acknowledge that this intake does not replace the need for in-person care when symptoms arise, and that follow-up may be required based on lab results or clinical judgement. You agree to seek emergency or in-person medical care if symptoms worsen or new symptoms develop.
  </p>

<p>Best regards,<br>{{ doctor.name }}</p>

{% endwith %}
{% endwith %}
{% endwith %}
{% endwith %}