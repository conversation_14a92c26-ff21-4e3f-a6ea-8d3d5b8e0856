{"components": [{"intake_template_key": "bmi"}, {"key": "glp1_medication", "type": "selectboxes", "input": true, "label": "Please select the medication you are interested in taking:", "values": [{"label": "Wegovy (semaglutide)", "value": "wegovy"}, {"label": "Ozempic (semaglutide)", "value": "ozempic"}, {"label": "Rybel<PERSON> (semaglutide)", "value": "ry<PERSON><PERSON>"}, {"label": "Saxenda (liraglutide)", "value": "sa<PERSON><PERSON>"}, {"label": "Mounjaro (Tirzepatide)", "value": "mou<PERSON><PERSON>"}], "tableView": true, "customConditional": "show = data.bmi >27;", "optionsLabelPosition": "right"}, {"key": "no_glp1_medication", "type": "checkbox", "input": true, "label": "None of the above", "tableView": false, "customConditional": "show = data.bmi >27;", "defaultValue": false, "validate": {"custom": "valid = _.some(_.values(data.glp1_medication)) || data.no_glp1_medication;"}, "customClass": "mt-n3"}, {"key": "weight_comorbidities", "type": "selectboxes", "refreshOnChange": true, "input": true, "label": "Have you been diagnosed with any of the following conditions:", "values": [{"label": "Diabetes", "value": "diabetes"}, {"label": "High blood pressure", "value": "hypertension"}, {"label": "Fatty Liver Disease", "value": "fatty_liver_disease"}, {"label": "High cholesterol", "value": "dyslipidemia"}, {"label": "Sleep Apnea", "value": "osa"}, {"label": "Polycystic Ovary Syndrome (PCOS)", "customConditional": "show = data.sex == 'female';", "value": "pcos"}], "tableView": true, "customConditional": "show = data.bmi < 30 && data.bmi > 27 && !data. no_glp1_medication;", "optionsLabelPosition": "right"}, {"key": "no_weight_comorbidities", "type": "checkbox", "input": true, "refreshOnChange": true, "label": "None of the above", "tableView": false, "defaultValue": false, "customConditional": "show = data.bmi < 30 && data.bmi > 27 && !data. no_glp1_medication;", "validate": {"custom": "valid = _.some(_.values(data.weight_comorbidities)) || data.no_weight_comorbidities;"}, "customClass": "mt-n3"}, {"key": "currently_pregnant", "type": "radio", "input": true, "label": "Are you currently pregnant?", "inline": false, "values": [{"label": "No", "value": false, "shortcut": ""}, {"label": "Yes/I don't know", "value": true, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = (data.bmi > 27 && !data.no_glp1_medication && data.sex == 'female')||(data.bmi > 27 && !data.no_glp1_medication && data.sex == 'female');", "optionsLabelPosition": "right"}, {"key": "contraindicationsMEN2", "type": "selectboxes", "input": true, "label": "Do you have any of the following conditions? (Select all that apply)", "values": [{"label": "Medullary Thyroid Cancer (MTC)", "value": "MTC"}, {"label": "Pheochromocytoma", "value": "pheochromocytoma"}, {"label": "Primary Parathyroid Hyperplasia", "value": "primary_parathyroid_disease"}, {"label": "Irregular heart rhythm", "value": "arrhythmia"}], "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = data.bmi > 27 && !data. no_glp1_medication && (data.sex == 'male' || (data.sex == 'female' && data.currently_pregnant == false));", "inputType": "checkbox"}, {"key": "no_contraindicationsMEN2", "type": "checkbox", "input": true, "label": "None of the above", "validate": {"custom": "valid = _.some(_.values(data.contraindicationsMEN2)) || data.no_contraindicationsMEN2;"}, "tableView": false, "defaultValue": false, "customConditional": "show = data.bmi > 27 && !data. no_glp1_medication && (data.sex == 'male' || (data.sex == 'female' && data.currently_pregnant == false));", "customClass": "mt-n3"}, {"key": "current_DPP4_inhibitors_insulin", "type": "selectboxes", "input": true, "label": "Are you currently taking any of the following medications? (Select all that apply)", "values": [{"label": "<PERSON><PERSON><PERSON><PERSON> (Januvia)", "value": "sitagliptin_januvia"}, {"label": "Saxagliptin (Onglyza)", "value": "saxagliptin_onglyza"}, {"label": "<PERSON><PERSON><PERSON><PERSON> (Tradjenta)", "value": "linagliptin_tradjenta"}, {"label": "<PERSON><PERSON><PERSON>", "value": "insulin"}], "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = data.bmi > 27 && !data. no_glp1_medication && (data.sex == 'male' || (data.sex == 'female' && data.currently_pregnant == false)) && data.no_contraindicationsMEN2;", "inputType": "checkbox"}, {"key": "no_DPP4_inhibitors_insulin", "type": "checkbox", "input": true, "label": "None of the above", "validate": {"custom": "valid = _.some(_.values(data.current_DPP4_inhibitors_insulin)) || data.no_DPP4_inhibitors_insulin;"}, "tableView": false, "defaultValue": false, "customConditional": "show = data.bmi > 27 && !data. no_glp1_medication && (data.sex == 'male' || (data.sex == 'female' && data.currently_pregnant == false)) && data.no_contraindicationsMEN2;", "customClass": "mt-n3", "errors": {"custom": "Please select at least one medication you are currently taking, or select 'None of the above'."}}, {"key": "eating_disorder_detail", "customConditional": "show = data.bmi > 27 && !data. no_glp1_medication && (data.sex == 'male' || (data.sex == 'female' && data.currently_pregnant == false)) && data.no_DPP4_inhibitors_insulin;", "type": "selectboxes", "input": true, "label": "Have you ever been diagnosed with or suspected of having any of the following eating disorders? (Select all that apply)", "values": [{"label": "Anorexia Nervosa", "value": "anorexia_nervosa", "shortcut": ""}, {"label": "Bulimia Nervosa", "value": "bulimia_nervosa", "shortcut": ""}, {"label": "Binge Eating Disorder", "value": "binge_eating_disorder", "shortcut": ""}, {"label": "Other Eating Disorder", "value": "other_eating_disorder", "shortcut": ""}], "placeholder": "Select an option", "tableView": true, "optionsLabelPosition": "right"}, {"key": "none_of_the_above_eating_disorders", "type": "checkbox", "input": true, "label": "None of the above", "tableView": false, "defaultValue": false, "customConditional": "show = data.bmi > 27 && !data. no_glp1_medication && (data.sex == 'male' || (data.sex == 'female' && data.currently_pregnant == false)) && data.no_DPP4_inhibitors_insulin;", "customClass": "mt-n3", "validate": {"custom": "valid = _.some(_.values(data.eating_disorder_detail)) || data.none_of_the_above_eating_disorders;", "customMessage": "Please select at least one type of eating disorder, or confirm that none apply."}}, {"key": "no_indications", "type": "textfield", "input": true, "label": "No Indication:", "hidden": true, "disabled": true, "multiple": false, "tableView": true, "clearOnHide": false, "defaultValue": true, "calculateValue": "value = (data.bmi <= 27) || (data.bmi > 27 && data.bmi <= 30 && !_.some(_.values(data.weight_comorbidities)));", "refreshOnChange": true}, {"key": "contraindication_keys", "type": "textfield", "input": true, "label": "Contraindication keys:", "hidden": true, "disabled": true, "multiple": true, "tableView": true, "clearOnHide": false, "calculateValue": "value = _.concat((data.bmi < 27) ? ['bmi_below_27_contraindication'] : [], (data.bmi > 27 && data.bmi <= 30 && !_.some(_.values(data.weight_comorbidities))) ? ['bmi_no_comorbidities_contraindication'] : [], _.some(_.values(data.contraindicationsMEN2)) ? ['MEN2_contraindication'] : [], _.some(_.values(data.current_DPP4_inhibitors_insulin)) ? ['DPP4_inhibitor_contraindication'] : [], _.some(_.values(data.eating_disorder_detail)) ? ['eating_disorder_contraindication'] : [], (data.currently_pregnant === true) ? ['pregnancy_contraindication'] : []);", "refreshOnChange": true}]}