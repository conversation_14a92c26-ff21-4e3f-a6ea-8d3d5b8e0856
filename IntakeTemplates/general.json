{"name": "general", "type": "form", "title": "General", "display": "form", "components": [{"key": "sex", "type": "radio", "input": true, "label": "Sex", "inline": true, "values": [{"label": "Female", "value": "female", "shortcut": ""}, {"label": "Male", "value": "male", "shortcut": ""}], "validate": {"required": true}, "tableView": false, "optionsLabelPosition": "right"}, {"key": "use_insurance", "type": "checkbox", "input": true, "label": "I have a health card & wish to use it", "hidden": false, "tableView": false, "clearOnHide": false, "defaultValue": true}, {"key": "privacy_policy_terms_service", "type": "checkbox", "input": true, "label": "I agree to TeleTest's Privacy Policy and Terms of Service", "hidden": false, "validate": {"required": true}, "tableView": false, "clearOnHide": false, "defaultValue": true}, {"key": "located_within_ontario", "type": "checkbox", "input": true, "label": "I am located within the province of Ontario.", "hidden": false, "validate": {"required": true}, "tableView": false, "clearOnHide": false, "defaultValue": true}, {"key": "content", "html": "<div class='small text-muted mt-n2 mb-3'>If you’re concerned about <a href=\"https://docs.teletest.ca/anonymous-testing-and-privacy\" target='_blank'>anonymity please read this page.</a></div>", "type": "content", "input": false, "label": "Content", "hidden": true, "tableView": false, "refreshOnChange": false}, {"key": "validHealthCard", "type": "textfield", "input": true, "label": "validHealthCard", "hidden": true, "disabled": true, "hideLabel": true, "tableView": false, "spellcheck": false, "clearOnHide": false, "calculateValue": "value = data.use_insurance"}, {"key": "validInsurance", "type": "textfield", "input": true, "label": "validInsurance", "hidden": true, "disabled": true, "hideLabel": true, "tableView": false, "spellcheck": false, "clearOnHide": false, "calculateValue": "value = data.use_insurance"}, {"key": "vca", "type": "textfield", "input": true, "label": "vca", "hidden": true, "disabled": true, "hideLabel": true, "tableView": false, "spellcheck": false, "clearOnHide": false, "calculateValue": "value = true"}]}