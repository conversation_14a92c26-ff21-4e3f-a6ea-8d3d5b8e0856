{"components": [{"key": "current_ed_med", "type": "radio", "input": true, "label": "Are you currently or have recently been on a medication for erectile dysfunction?", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "ped_drug", "type": "selectboxes", "input": true, "label": "Please select which of the following category of ED medications are you are currently on:", "values": [{"label": "Viagara (Sildenafil)", "value": "viagara", "shortcut": ""}, {"label": "Cialis (Tadalafil)", "value": "cialis", "shortcut": ""}, {"label": "Levitra (Vardenafil)", "value": "sarm", "shortcut": ""}], "inputType": "checkbox", "tableView": true, "customConditional": "show = data.current_ped == 'yes';", "optionsLabelPosition": "right"}, {"key": "no_ped_drug", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = !!data.no_ped_drug || !!_.some(_.values(data.ped_drug));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.current_ped == 'yes';"}, {"key": "ped5_symptom_contraindication", "type": "selectboxes", "input": true, "label": "Do you experience any of the following symptoms:", "values": [{"label": "Chest pain or heaviness", "value": "chest_pain", "shortcut": ""}, {"label": "Abdominal pain or cramping", "value": "chest_pain", "shortcut": ""}, {"label": "Shortness of breath", "value": "dyspnea", "shortcut": ""}, {"label": "Feel lightheaded or faint", "value": "presy<PERSON><PERSON>", "shortcut": ""}, {"label": "Arm or leg swelling", "value": "limb_swelling", "shortcut": ""}, {"label": "Heart palpitations (slow or fast heart beat)", "value": "palpitations", "shortcut": ""}, {"label": "Coughing up blood", "value": "hemoptysis", "shortcut": ""}, {"label": "Erections lasting more than 2 hours", "value": "priapism", "shortcut": ""}, {"label": "Testicular lumps or pain", "value": "testicular_mass", "shortcut": ""}], "inputType": "checkbox", "tableView": true, "customConditional": "show = data.current_ped == 'yes';", "optionsLabelPosition": "right"}, {"key": "no_pde5_contraindication", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = !!data.no_ped_contraindication || !!_.some(_.values(data.pde5_symptom_contraindication));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.current_ped == 'yes';"}, {"key": "ped5_condition_contraindication", "type": "selectboxes", "input": true, "label": "Do you experience any of the following symptoms:", "values": [{"label": "Chest pain or heaviness", "value": "chest_pain", "shortcut": ""}, {"label": "Abdominal pain or cramping", "value": "chest_pain", "shortcut": ""}, {"label": "Shortness of breath", "value": "dyspnea", "shortcut": ""}, {"label": "Feel lightheaded or faint", "value": "presy<PERSON><PERSON>", "shortcut": ""}, {"label": "Arm or leg swelling", "value": "limb_swelling", "shortcut": ""}, {"label": "Heart palpitations (slow or fast heart beat)", "value": "palpitations", "shortcut": ""}, {"label": "Coughing up blood", "value": "hemoptysis", "shortcut": ""}, {"label": "Erections lasting more than 2 hours", "value": "priapism", "shortcut": ""}, {"label": "Testicular lumps or pain", "value": "testicular_mass", "shortcut": ""}], "inputType": "checkbox", "tableView": true, "customConditional": "show = data.current_ped == 'yes';", "optionsLabelPosition": "right"}, {"key": "no_pde5_contraindication", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = !!data.no_ped_contraindication || !!_.some(_.values(data.pde5_symptom_contraindication));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.current_ped == 'yes';"}, {"key": "ped5_med_contraindication", "type": "selectboxes", "input": true, "label": "Are you currently on any of the following medications:", "values": [{"label": "Chest pain or heaviness", "value": "chest_pain", "shortcut": ""}, {"label": "Abdominal pain or cramping", "value": "chest_pain", "shortcut": ""}, {"label": "Shortness of breath", "value": "dyspnea", "shortcut": ""}, {"label": "Feel lightheaded or faint", "value": "presy<PERSON><PERSON>", "shortcut": ""}, {"label": "Arm or leg swelling", "value": "limb_swelling", "shortcut": ""}, {"label": "Heart palpitations (slow or fast heart beat)", "value": "palpitations", "shortcut": ""}, {"label": "Coughing up blood", "value": "hemoptysis", "shortcut": ""}, {"label": "Erections lasting more than 2 hours", "value": "priapism", "shortcut": ""}, {"label": "Testicular lumps or pain", "value": "testicular_mass", "shortcut": ""}], "inputType": "checkbox", "tableView": true, "customConditional": "show = data.current_ped == 'yes';", "optionsLabelPosition": "right"}, {"key": "no_pde5_contraindication", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = !!data.no_ped_contraindication || !!_.some(_.values(data.pde5_symptom_contraindication));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.current_ped == 'yes';"}, {"key": "si_hi_screen", "type": "radio", "input": true, "label": "Are you currently experiencing thoughts of self-harm, suicide or of hurting others?", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.current_ped == 'yes';", "optionsLabelPosition": "right"}]}