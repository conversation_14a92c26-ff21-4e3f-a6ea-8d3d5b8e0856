{"name": "testing-frequency", "type": "form", "title": "Testing Frequency", "display": "form", "components": [{"key": "testFreqMonths_{assay.key}", "type": "radio", "input": true, "label": "How long ago was your last {assay.name} test?", "inline": false, "values": [{"label": "Never been tested", "value": 100, "shortcut": ""}, {"label": "Less than 3 months", "value": 2, "shortcut": ""}, {"label": "Between 3 to 6 months", "value": 5, "shortcut": ""}, {"label": "Between 6 to 12 months", "value": 10, "shortcut": ""}, {"label": "Greater than 12 months", "value": 13, "shortcut": ""}], "validate": {"required": true}, "tableView": false, "conditional": {"json": {"and": [{"!!": {"var": "data.showSubmit"}}]}}, "optionsLabelPosition": "right"}]}