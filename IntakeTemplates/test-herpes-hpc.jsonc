{"components": [{"key": "heading_herpes_testing", "html": "<h1><center><strong>Herpes Blood Testing</strong></h1><center><p>To expedite your care, please answer the following questions to the best of your abilities. &nbsp;We ask these questions to provide the best possible care.&nbsp;</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "heading_medical_indication", "html": "<h4>Indication for Testing&nbsp;</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "testing_indication", "type": "selectboxes", "input": true, "label": "Please clarify why you are interested in testing for herpes:", "values": [{"label": "I've had cold sores", "value": "cold_sores", "shortcut": ""}, {"label": "I have a blistering genital rash but have never had a swab to confirm the diagnosis", "value": "genital_blistering_rash", "shortcut": ""}, {"label": "I have a partner with a physician-confirmed case of genital herpes", "value": "partner_confirmed_genital_herpes", "shortcut": ""}, {"label": "I have a partner with a physician-confirmed case of cold sores", "value": "partner_confirmed_oral_herpes", "shortcut": ""}, {"label": "I'd like to know my baseline status before a new relationship", "value": "baseline_status_before_relationship", "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}], "validate": {"required": true}, "inputType": "checkbox", "confirm_label": "Reason(s) for testing:", "tableView": true, "optionsLabelPosition": "right"}, {"key": "other_testing_indication", "type": "textarea", "input": true, "label": "Please state your “other” reason(s) for HSV testing:", "tableView": true, "autoExpand": false, "customConditional": "show = data.testing_indication.other;"}, {"key": "cold_sores_differentiation_heading", "html": "<h5><strong>About Cold Sores</strong></h5>", "type": "content", "input": false, "label": "Content", "customConditional": "show = data.testing_indication && Object.values(data.testing_indication).some(value => value);", "tableView": false}, {"key": "sores_present", "type": "radio", "input": true, "label": "Do you get sores in or around the mouth, lips, or nose?", "tooltip": "Sores can include open, painful spots or wounds that may look round or have a yellow or white center with a red edge. These might show up on your skin or inside your mouth and can make it uncomfortable to eat or talk.", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "I'm not sure", "value": "not_sure"}], "validate": {"required": true}, "confirm_label": "Previous oral sores:", "customConditional": "show = data.testing_indication && Object.values(data.testing_indication).some(value => value);", "tableView": true}, {"key": "skin_change_locations", "type": "selectboxes", "input": true, "label": "Where do you notice sores or skin changes (select all that apply):", "values": [{"label": "Outer lips (e.g., border of the lips)", "value": "outer_lips"}, {"label": "Inner lips (inside the lip area)", "value": "inner_lips"}, {"label": "Corners of the mouth", "value": "mouth_corners"}, {"label": "Inside the cheeks", "value": "inside_cheeks"}, {"label": "Roof of the mouth", "value": "roof_of_mouth"}, {"label": "Gums (around or near teeth)", "value": "gums"}, {"label": "Tongue (top or underside)", "value": "tongue"}, {"label": "Inside the nose", "value": "nose_inside"}, {"label": "Tip of the nose", "value": "nose_outside"}, {"label": "Chin (below the lips)", "value": "chin"}, {"label": "I haven't noticed any sores or skin changes", "value": "no_changes"}], "customConditional": "show = data.sores_present === 'yes';", "validate": {"required": true}, "tableView": true}, {"key": "sensations_before", "type": "radio", "input": true, "label": "Before the sore appears, do you notice any sensations like itching, tingling, or burning?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "I'm not sure", "value": "not_sure"}], "customConditional": "show = !!data.skin_change_locations;", "confirm_label": "Sensations before sores:", "validate": {"required": true}, "tableView": true}, {"key": "sores_appearance", "type": "selectboxes", "input": true, "label": "What do your sores usually look like? (You can select more than one option):", "values": [{"label": "Small fluid-filled blisters that may burst and scab over", "value": "blisters_scab"}, {"label": "Painful spots with a white or yellow center and red edges", "value": "white_yellow_center"}, {"label": "Flat, red patches that may or may not hurt", "value": "flat_red_patches"}, {"label": "Itchy bumps without fluid", "value": "itchy_bumps"}, {"label": "Cracks or splits in the skin that sting or hurt", "value": "cracks_splits"}, {"label": "None of these descriptions fit", "value": "none"}, {"label": "I'm not sure", "value": "not_sure"}, {"label": "Other", "value": "other"}], "customConditional": "show = !!data.sensations_before;", "confirm_label": "<PERSON>res appearance:", "validate": {"required": true}, "tableView": true}, {"key": "sores_appearance_other", "type": "textfield", "input": true, "label": "Please describe what your sores look like:", "placeholder": "Describe your sores", "customConditional": "show = data.sores_appearance && data.sores_appearance.other;", "tableView": true, "validate": {"required": false}}, {"key": "cold_sores_physician_confirmation", "type": "radio", "input": true, "label": "Has a physician ever confirmed your diagnosis as a cold sore?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "I'm not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Physician confirmed cold sore diagnosis:", "customConditional": "show = !!data.sores_appearance;"}, {"key": "cold_sores_age_diagnosis", "type": "textfield", "input": true, "label": "How old were you when you were first noticed these sores? (If you're unsure, feel free to estimate.)", "placeholder": "Enter your age or best estimate", "validate": {"required": true, "custom": "valid = input >= 0;"}, "tableView": true, "customConditional": "show = !!data.cold_sores_physician_confirmation;"}, {"key": "cold_sores_past_medication", "type": "radio", "input": true, "label": "Have you taken medication in the past to treat cold sores?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = !!data.cold_sores_age_diagnosis;"}, {"key": "cold_sores_outbreak_frequency", "type": "select", "input": true, "label": "How many cold sore outbreaks do you typically experience in a year?", "widget": "html5", "data": {"values": [{"label": "I haven't had one in many years", "value": "remote_past"}, {"label": "I get them once every few years", "value": "remote_past"}, {"label": "None in the past year", "value": "none"}, {"label": "Less than 1 outbreak every 5 years", "value": "less_than_1_every_5_years"}, {"label": "1 outbreak every 2-5 years", "value": "1_every_2_to_5_years"}, {"label": "1 outbreak per year", "value": "1_per_year"}, {"label": "2 outbreaks per year", "value": "2_per_year"}, {"label": "3-5 outbreaks per year", "value": "3-5_per_year"}, {"label": "6-10 outbreaks per year", "value": "6-10_per_year"}, {"label": "More than 10 outbreaks per year", "value": "more_than_10_per_year"}]}, "validate": {"required": true}, "tableView": true, "placeholder": "Select the frequency", "customConditional": "show = !!data.cold_sores_past_medication;"}, {"key": "cold_sores_swab_confirmation", "type": "radio", "input": true, "label": "Have you ever had a swab taken of the cold sore to confirm the diagnosis?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Previous Oral HSV Swab Completed:", "customConditional": "show = !!data.cold_sores_outbreak_frequency;"}, {"key": "cold_sores_serotype_confirmation", "type": "radio", "input": true, "label": "Was the herpes virus type (serotype) identified through the test?", "tooltip": "Serotype testing can determine if the herpes simplex virus is HSV-1 or HSV-2.", "values": [{"label": "Confirmed HSV-1", "value": "hsv1"}, {"label": "Confirmed HSV-2", "value": "hsv2"}, {"label": "The test did not confirm the serotype", "value": "negative"}, {"label": "I don't know", "value": "unknown"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Oral Swab Result:", "customConditional": "show = data.cold_sores_swab_confirmation === 'yes';"}, {"key": "genital_herpes_differentiation_heading", "html": "<h5><strong>About Genital Herpes</strong></h5>", "type": "content", "input": false, "label": "Content", "customConditional": "show = (data.sores_present === 'no' || !!data.cold_sores_swab_confirmation);", "tableView": false}, {"key": "genital_sores_present", "type": "radio", "input": true, "label": "Do you get sores, bumps, or rashes in your genital or anal area?", "tooltip": "These can include painful spots, red bumps, or rashes near your genital or anal area. Select 'Yes' if you've noticed any of these symptoms.", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "I'm not sure", "value": "not_sure"}], "validate": {"required": true}, "confirm_label": "Previous genital sores:", "customConditional": "show = (data.sores_present === 'no' || !!data.cold_sores_swab_confirmation);", "tableView": true}, {"key": "genital_sores_sensations", "type": "radio", "input": true, "label": "Before the sores or bumps appear, do you feel sensations like itching, tingling, or burning?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "I'm not sure", "value": "not_sure"}], "customConditional": "show = data.genital_sores_present === 'yes';", "confirm_label": "Sensations before genital sores:", "validate": {"required": true}, "tableView": true}, {"key": "genital_sores_locations", "type": "selectboxes", "input": true, "label": "Where do you notice sores, blisters, or skin changes (select all that apply):", "values": [{"label": "Vagina (including outer and inner areas)", "value": "vagina", "customConditional": "show = data.sex === 'female';"}, {"label": "Around the vaginal opening", "value": "vaginal_opening", "customConditional": "show = data.sex === 'female';"}, {"label": "On or around the anus", "value": "anus", "customConditional": "show = true;"}, {"label": "Inner thighs (near the genital area)", "value": "inner_thighs", "customConditional": "show = true;"}, {"label": "On the penis (any part)", "value": "penis", "customConditional": "show = data.sex === 'male';"}, {"label": "On or around the scrotum", "value": "scrotum", "customConditional": "show = data.sex === 'male';"}, {"label": "Between the genitals and anus", "value": "perineum", "customConditional": "show = true;"}, {"label": "Nowhere, I haven't noticed any sores or skin changes", "value": "no_changes", "customConditional": "show = true;"}, {"label": "Other", "value": "other", "customConditional": "show = true;"}], "customConditional": "show = !!data.genital_sores_sensations;", "confirm_label": "Genital sore location:", "validate": {"required": true}, "tableView": true}, {"key": "genital_sores_appearance", "type": "selectboxes", "input": true, "label": "What do your sores, bumps, or rashes usually look like? (You can select more than one):", "values": [{"label": "Fluid-filled blisters that burst and scab over", "value": "blisters_scab"}, {"label": "Small red or white bumps, possibly with hair in the center", "value": "folliculitis"}, {"label": "Flat, itchy patches that are red or irritated", "value": "contact_dermatitis"}, {"label": "Painful open sores that take time to heal", "value": "painful_sores"}, {"label": "None of these descriptions fit", "value": "none"}, {"label": "I'm not sure", "value": "not_sure"}, {"label": "Other", "value": "other"}], "customConditional": "show = !!data.genital_sores_locations;", "confirm_label": "Genital sores appearance:", "validate": {"required": true}, "tableView": true}, {"key": "genital_sores_appearance_other", "type": "textfield", "input": true, "label": "Please describe your sores, bumps, or rashes if none of the above fits:", "placeholder": "Describe your symptoms", "customConditional": "show = data.genital_sores_appearance && data.genital_sores_appearance.other;", "tableView": true, "validate": {"required": false}}, {"key": "genital_sores_duration", "type": "radio", "input": true, "label": "How long do these sores or bumps typically last?", "values": [{"label": "A few days to a week", "value": "short_duration"}, {"label": "1-2 weeks", "value": "medium_duration"}, {"label": "Longer than 2 weeks", "value": "long_duration"}, {"label": "I'm not sure", "value": "not_sure"}], "customConditional": "show = !!data.genital_sores_locations && !!data.genital_sores_appearance;", "validate": {"required": true}, "tableView": true}, {"key": "genital_sores_triggers", "type": "radio", "input": true, "label": "Have you noticed any triggers for your sores or bumps?", "values": [{"label": "Yes, they occur after shaving, waxing or hair removal", "value": "shaving_triggers"}, {"label": "Yes, they occur after using new products (e.g., soap, creams, or detergents)", "value": "product_triggers"}, {"label": "Yes, they occur during or after my period (e.g., menses, use of pads, liners, or tampons)", "value": "menses_triggers", "customConditional": "show = data.sex == 'female';"}, {"label": "Yes, but I'm not sure what the trigger is", "value": "unknown_trigger"}, {"label": "No, I haven't noticed any triggers", "value": "no_triggers"}], "validate": {"required": true}, "customConditional": "show = !!data.genital_sores_duration;", "confirm_label": "Genital sore location:", "tableView": true}, {"key": "genital_herpes_physician_confirmation", "type": "radio", "input": true, "label": "Has a physician ever confirmed your diagnosis of genital herpes?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "confirm_label": "Physician confirmed genital herpes diagnosis:", "customConditional": "show = !!data.genital_sores_duration && !!data.genital_sores_triggers;", "tableView": true}, {"key": "genital_herpes_age_symptom_onset", "type": "textfield", "input": true, "label": "How old were you when you were first developed these symptoms? (If you're unsure, feel free to estimate.)", "placeholder": "Enter your age or best estimate", "validate": {"required": true, "custom": "valid = input >= 0;"}, "customConditional": "show = data.genital_herpes_physician_confirmation === 'yes' || data.genital_herpes_physician_confirmation === 'no';", "tableView": true}, {"key": "genital_herpes_outbreak_frequency", "type": "select", "input": true, "label": "How many episodes do you typically experience in a year?", "widget": "html5", "data": {"values": [{"label": "I haven't had one in many years", "value": "remote_past"}, {"label": "I get them once every few years", "value": "remote_past"}, {"label": "None in the past year", "value": "none"}, {"label": "Less than 1 outbreak every 5 years", "value": "less_than_1_every_5_years"}, {"label": "1 outbreak every 2-5 years", "value": "1_every_2_to_5_years"}, {"label": "1 outbreak per year", "value": "1_per_year"}, {"label": "2 outbreaks per year", "value": "2_per_year"}, {"label": "3-5 outbreaks per year", "value": "3-5_per_year"}, {"label": "6-10 outbreaks per year", "value": "6-10_per_year"}, {"label": "More than 10 outbreaks per year", "value": "more_than_10_per_year"}]}, "customConditional": "show = data.genital_herpes_physician_confirmation === 'yes';", "validate": {"required": true}, "tableView": true, "placeholder": "Select the frequency"}, {"key": "genital_sores_swab_confirmation", "type": "radio", "input": true, "label": "Have you ever had a swab taken of the genital sores to confirm the diagnosis?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Previous HSV Swab at Genital Site Completed:", "customConditional": "show = !!data.genital_herpes_outbreak_frequency;"}, {"key": "geital_sores_serotype_confirmation", "type": "radio", "input": true, "label": "Was the herpes virus type (serotype) identified through the test?", "tooltip": "Serotype testing can determine if the herpes simplex virus is HSV-1 or HSV-2.", "values": [{"label": "Yes, the test confirmed HSV-1", "value": "hsv1"}, {"label": "Yes, the test confirmed HSV-2", "value": "hsv2"}, {"label": "No, the test did not confirm the serotype", "value": "negative"}, {"label": "I don't know", "value": "unknown"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Genital Swab Results:", "customConditional": "show = data.genital_sores_swab_confirmation === 'yes';"}, {"key": "exposure_heading", "html": "<h4><strong>Exposure</strong></h4>", "type": "content", "input": false, "label": "Content", "customConditional": "show = data.testing_indication && (data.testing_indication.partner_confirmed_genital_herpes || data.testing_indication.partner_confirmed_oral_herpes);", "tableView": false}, {"key": "exposure_status", "type": "radio", "input": true, "label": "Do you know the nature of your partner's herpes status?", "values": [{"label": "They have symptoms but no confirmed diagnosis", "value": "symptoms_no_diagnosis"}, {"label": "They had a lab test confirming the diagnosis", "value": "lab_confirmed"}, {"label": "A physician diagnosed them based on symptoms", "value": "physician_diagnosed"}, {"label": "They disclosed their status but didn't provide details", "value": "disclosed_no_details"}, {"label": "I suspect they might have herpes, but they haven't been diagnosed", "value": "suspected_no_diagnosis"}, {"label": "They had a previous partner with herpes but aren't sure of their own status", "value": "previous_partner_herpes"}, {"label": "Other (please specify below)", "value": "other"}], "validate": {"required": true}, "customConditional": "show = data.testing_indication && (data.testing_indication.partner_confirmed_genital_herpes || data.testing_indication.partner_confirmed_oral_herpes);", "confirm_label": "Partner's HSV Status:", "tableView": true}, {"key": "exposure_status_other", "type": "textfield", "input": true, "label": "Please specify the details:", "placeholder": "Provide details here", "customConditional": "show = data.exposure_status === 'other';", "validate": {"required": true}, "tableView": true}, {"key": "last_partner_exposure_range", "type": "select", "input": true, "label": "When was your last intimate contact with this partner?", "placeholder": "Select the time range", "confirm_label": "Time from last exposure to partner:", "tooltip": "Please select the range that best describes the most recent time you were in intimate contact with this partner.", "widget": "html5", "data": {"values": [{"label": "Less than 7 days ago", "value": "<7days"}, {"label": "7-28 days ago", "value": "7-28_days"}, {"label": "28-60 days ago", "value": "28-60_days"}, {"label": "60-90 days ago", "value": "60-90_days"}, {"label": "90-180 days ago", "value": "90-180_days"}, {"label": "More than 180 days ago", "value": "180+_days"}, {"label": "Prefer not to disclose", "value": "prefer_not_to_disclose"}]}, "validate": {"required": true, "customMessage": "Please select the most accurate time range for your last contact."}, "tableView": true, "customConditional": "show = !!data.exposure_status;"}, {"key": "test_timing_understanding", "type": "radio", "confirm_label": "Understands time to positive HSV blood result can be 180 days:", "input": true, "label": "A herpes blood test can take up to 180 days to turn positive after exposure, though most people develop a positive antibody test by day 28. If you choose to proceed with testing now, please plan to repeat the test 180 days after your last exposure for definitive results. Do you understand this information?", "values": [{"label": "I understand", "value": "understand"}, {"label": "I do not understand", "value": "do_not_understand"}], "validate": {"required": true}, "customConditional": "show = ['<7_days', '7-28_days', '28-60_days', '60-90_days', '90-180_days'].includes(data.last_partner_exposure_range);", "tableView": true}, {"key": "previous_blood_testing_heading", "html": "<h5><strong>Previous Herpes Blood Testing</strong></h5>", "type": "content", "input": false, "label": "Content", "customConditional": "show = true;", "tableView": false}, {"key": "previous_blood_testing", "type": "radio", "input": true, "label": "Have you ever had a herpes blood test completed?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "I don't know", "value": "unknown"}], "validate": {"required": true}, "confirm_label": "Previous Blood Testing:", "tableView": true}, {"key": "last_blood_test_date", "type": "select", "input": true, "label": "When was your last herpes blood test completed? (If known)", "placeholder": "Select the time range", "tooltip": "Please choose the range that best describes when you last completed a herpes blood test.", "widget": "html5", "data": {"values": [{"label": "Less than 14 days ago", "value": "<14_days"}, {"label": "15-30 days ago", "value": "15-30_days"}, {"label": "31-60 days ago", "value": "31-60_days"}, {"label": "61-90 days ago", "value": "61-90_days"}, {"label": "90-180 days ago", "value": "90-180_days"}, {"label": "180 days to 1 year ago", "value": "180-1_year"}, {"label": "More than 1 year ago", "value": "more_than_1_year_ago"}]}, "validate": {"required": false, "customMessage": "Please select the time range that best describes when your last test was completed."}, "tableView": true, "confirm_label": "Last Blood Test Date:", "customConditional": "show = data.previous_blood_testing === 'yes';"}, {"key": "previous_blood_test_results", "type": "selectboxes", "input": true, "label": "What were the results of your blood test? (Select all that apply):", "values": [{"label": "Positive for HSV-1", "value": "hsv1_positive"}, {"label": "Positive for HSV-2", "value": "hsv2_positive"}, {"label": "Positive for both HSV-1 and HSV-2", "value": "hsv1_hsv2_positive"}, {"label": "Negative for both HSV-1 and HSV-2", "value": "negative"}, {"label": "I don't know the results", "value": "unknown"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Previous Blood Test Results:", "customConditional": "show = !!data.last_blood_test_date;"}, {"key": "heading_additional_questions", "html": "<h4>Additional Questions&nbsp;</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"intake_template_key": "hx-any-questions"}, {"key": "intake_denial_reasons", "type": "textfield", "input": true, "label": "Intake Denial Reasons:", "hidden": true, "disabled": true, "multiple": true, "adminFlag": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "value=_.concat((data.any_other_questions === true && !!data.stated_other_questions && !/^\\s*$/.test(data.stated_other_questions)?['stated_other_questions.not_blank']:[]));", "refreshOnChange": true}, {"key": "intake_kind", "type": "textfield", "input": true, "label": "Intake Kind", "hidden": true, "disabled": true, "tableView": true, "clearOnHide": false, "calculateValue": "value = !data.intake_denial_reasons?.length ? 'mail' : 'chat';"}, {"key": "intake_mt_key", "type": "textfield", "input": true, "label": "Intake Mail Template Key", "hidden": true, "disabled": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.intake_kind=='mail'?'intake-hsv':'intake-denial'"}, {"key": "allowed_rt_keys", "type": "textfield", "input": true, "label": "Allowed Rep<PERSON>", "hidden": true, "disabled": true, "multiple": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.intake_mt_key=='intake-denial'?[]:['get-req','appointment-intake','edit-intake']"}]}