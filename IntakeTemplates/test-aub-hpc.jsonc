{"components": [{"key": "heading_celiac_section", "html": "<h1><center><strong>**Requires BMI** Given malignancy risk factor for obesity AUBk</strong></h1><center><p>To expedite your care, please answer the following questions to the best of your abilities. &nbsp;We ask these questions to provide the best possible care.&nbsp;</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "heading_fmhx", "html": "<h2>Medical History&nbsp;</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "fm_hx", "type": "selectboxes", "input": true, "label": "Please select which of the following apply to your menstrual cycles", "inline": false, "values": [{"label": "Painful menstrual cycles", "value": "dysmenorrhea", "shortcut": ""}, {"label": "Heavy flow", "value": "menorrhagia-flow", "shortcut": ""}, {"label": "Long menstrual cycles (7+ days)", "value": "menorrhagia-duration", "shortcut": ""}, {"label": "Painful intercourse", "value": "dyspareunia", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "confirm_label": "Family history of celiac disease:", "optionsLabelPosition": "right"}, {"key": "structural_causes", "type": "selectboxes", "input": true, "label": "Have you been diagnosed wiht any of the following in the past:", "inline": false, "values": [{"label": "Fibroids", "value": "leiomyomas", "shortcut": ""}, {"label": "Adenomyosis", "value": "adenomyosis", "shortcut": ""}, {"label": "Polyps (cervical or endometrioal)", "value": "polyps", "shortcut": ""}, {"label": "None of the above", "value": "none_of_the_above", "shortcut": ""}, {"label": "I don't understand this question", "value": "doesn't_understand", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "confirm_label": "Family history of celiac disease:", "optionsLabelPosition": "right"}, {"key": "personal_hx", "type": "selectboxes", "input": true, "label": "Do you have a prior diagnosis of any of the following medical conditions", "inline": false, "values": [{"label": "Endometriosis", "value": "endometriosis", "shortcut": ""}, {"label": "Endometrial Cancer", "value": "endometrial_cancer", "shortcut": ""}, {"label": "Polycystoc Ovarian Syndrome (PCOS)", "value": "pcose", "shortcut": ""}, {"label": "Hypothroidism", "value": "hypothroidism", "shortcut": ""}, {"label": "?Congenital Adrenal Hyperplasia (CAH)", "value": "cahs", "shortcut": ""}, {"label": "Type 1 diabetes", "value": "type_1_diabetes", "shortcut": ""}, {"label": "Rheumatoid arthritis", "value": "rheumatoid_arthritis", "shortcut": ""}, {"label": "<PERSON><PERSON>", "value": "lupus", "shortcut": ""}, {"label": "None of the above", "value": "none_of_the_above", "shortcut": ""}, {"label": "I don't know my family history", "value": "doesn't_know", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "confirm_label": "Family history of celiac disease:", "optionsLabelPosition": "right"}, {"key": "heading_heavy_menses", "html": "<h2>Heavy Flow&nbsp;</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "consistent_or_inconsistent_flow", "type": "radio", "input": true, "label": "Do you experience on average, the same number of days of flow with each cycle?", "inline": false, "values": [{"label": "Yes, I bleed the same number of days each cycle", "value": "consistent_duration", "shortcut": ""}, {"label": "Yes, but some cycles are longer than others by a day or two", "value": "negative", "shortcut": ""}, {"label": "No, the number of days of flow changes by more than 2 days per cycle", "value": "negative", "shortcut": ""}, {"label": "I don't know", "value": "doesn't_know", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.previous_celiac_testing == 'yes';", "confirm_label": "Prevous celiac test results:", "optionsLabelPosition": "right"}, {"key": "duration_menses", "data": {"values": [{"label": "20-30 years", "value": "20-30_years"}, {"label": "30-40 years", "value": "30-40_years"}, {"label": "40-50 years", "value": "40-50_years"}, {"label": "50-60 years", "value": "50-60_years"}, {"label": "60-70 years", "value": "60-70_years"}, {"label": "70+ years", "value": "70_years"}]}, "type": "select", "input": true, "label": "How old was the youngest member at the age they were diagnosed with colon cancer?", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.fm_hx.colon_cancer;", "optionsLabelPosition": "right"}, {"key": "aub_medication_list", "type": "selectboxes", "input": true, "label": "How :", "inline": false, "values": [{"label": "Birth Control Pill (OCP)", "value": "ocp", "shortcut": ""}, {"label": "Brith Control Patch or Implant (EVRA or Nexplanon)", "value": "patch_or_implant", "shortcut": ""}, {"label": "<PERSON><PERSON> (Progesterone Injection)", "value": "depo_im", "shortcut": ""}, {"label": "Hormonal IUD (i.e. <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, etc)", "value": "progesterone_iud", "shortcut": ""}, {"label": "Non-hormonal IUD (i.e. Copper-T, etc)", "value": "copper_iud", "shortcut": ""}, {"label": "None of the above", "value": "none_of_the_above", "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "confirm_label": "Current contraceptive aid:", "optionsLabelPosition": "right"}, {"key": "aub_medication_list_other", "type": "textarea", "input": true, "label": "As you selected other, please specify any other contraceptive aid you are currently on:", "validate": {"required": true}, "tableView": true, "autoExpand": false, "customConditional": "show = data.aub_medication_list && data.aub_medication_list.other;"}, {"key": "fm_hx", "type": "selectboxes", "input": true, "label": "Does anyone in your family have a history of any of the following", "inline": false, "values": [{"label": "Endometriosis", "value": "endometriosis", "shortcut": ""}, {"label": "Endometrial Cancer", "value": "endometrial_cancer", "shortcut": ""}, {"label": "Polycystoc Ovarian Syndrome (PCOS)", "value": "pcose", "shortcut": ""}, {"label": "?Congenital Adrenal Hyperplasia (CAH)", "value": "cahs", "shortcut": ""}, {"label": "Type 1 diabetes", "value": "type_1_diabetes", "shortcut": ""}, {"label": "Rheumatoid arthritis", "value": "rheumatoid_arthritis", "shortcut": ""}, {"label": "<PERSON><PERSON>", "value": "lupus", "shortcut": ""}, {"label": "None of the above", "value": "none_of_the_above", "shortcut": ""}, {"label": "I don't know my family history", "value": "doesn't_know", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "confirm_label": "Family history of celiac disease:", "optionsLabelPosition": "right"}, {"key": "heading_fmhx", "html": "<h2>Medical History&nbsp;</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "relative_diagnosis_colon_cancer", "type": "selectboxes", "input": true, "label": "Who was diagnosed with colon cancer in your family?", "inline": false, "values": [{"label": "Mother, Father, Brother or Sister", "value": "1st degree relative", "shortcut": ""}, {"label": "Grandparent, Uncle, Aunt, <PERSON><PERSON><PERSON><PERSON> or <PERSON><PERSON><PERSON>", "value": "2nd degree relative", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "confirm_label": "Family member with colon cancer:", "customConditional": "show = data.fm_hx.colon_cancer;", "optionsLabelPosition": "right"}, {"key": "date_of_last_celiac_test", "data": {"values": [{"label": "20-30 years", "value": "20-30_years"}, {"label": "30-40 years", "value": "30-40_years"}, {"label": "40-50 years", "value": "40-50_years"}, {"label": "50-60 years", "value": "50-60_years"}, {"label": "60-70 years", "value": "60-70_years"}, {"label": "70+ years", "value": "70_years"}]}, "type": "select", "input": true, "label": "How old was the youngest member at the age they were diagnosed with colon cancer?", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.fm_hx.colon_cancer;", "optionsLabelPosition": "right"}, {"key": "consultation_indication", "type": "selectboxes", "input": true, "label": "Have you been diagnosed with any of the following medical conditions:", "values": [{"label": "Type 1 Diabetes", "value": "type_1_diabetes", "shortcut": ""}, {"label": "Hypothroidism", "value": "hypothroidism", "shortcut": ""}, {"label": "<PERSON>'s syndrome", "value": "diabetes", "shortcut": ""}, {"label": "<PERSON>'s syndrome", "value": "diabetes", "shortcut": ""}, {"label": "IgA Deficiency", "value": "diabetes", "shortcut": ""}, {"label": "None of the above", "value": "no_diagnosed_diabetes_htn_dyslipidemia", "shortcut": ""}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "confirm_label": "Current metabolic conditions:", "optionsLabelPosition": "right"}, {"key": "previous_celiac_testing", "type": "radio", "input": true, "label": "Have you ever had celiac screening before?", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No/Don't Know", "value": "no/don't_know", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "confirm_label": "Prevous celiac testing:", "optionsLabelPosition": "right"}, {"key": "date_of_last_celiac_test", "data": {"values": [{"label": "<6 weeks", "value": "less_6_weeks"}, {"label": "6 weeks - 3 months", "value": "6weeks_3months"}, {"label": "3-6 months", "value": "3-6_months"}, {"label": "6-12 months", "value": "6_12_months"}, {"label": "12-24 months", "value": "12_24_months"}, {"label": "24+ months", "value": "24+_months"}, {"label": "24+ months", "value": "24+_months"}, {"label": "I don't know", "value": "cannot_recall"}]}, "type": "select", "input": true, "label": "When was your Celiac test outside of TeleTest completed?", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.previous_celiac_testing == 'yes';", "optionsLabelPosition": "right"}, {"key": "completed_celiac_gluten_free", "type": "radio", "input": true, "label": "Were you on a gluten free diet at the time you completed your previous celiac test?", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No/Don't Know", "value": "no/don't_know", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.previous_celiac_testing == 'yes';", "confirm_label": "Prevous celiac testing completed while on a gluten free diet:", "optionsLabelPosition": "right"}, {"key": "prior_celiac_pos_neg", "type": "radio", "input": true, "label": "Was your previous celiac test positive or negative for celiac disease?", "inline": false, "values": [{"label": "Positive", "value": "positive", "shortcut": ""}, {"label": "Negative", "value": "negative", "shortcut": ""}, {"label": "I don't know", "value": "doesn't_know", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.previous_celiac_testing == 'yes';", "confirm_label": "Prevous celiac test results:", "optionsLabelPosition": "right"}, {"key": "heading_diet", "html": "<h2>Diet</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "lactose_intolerance", "type": "radio", "input": true, "label": "Do you have symptoms of lactose intolerance (bloating, diarrhea, flatulence) after consuming dairy products?", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "Don't Know", "value": "don't_know", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "confirm_label": "Prior lactose intolerance:", "optionsLabelPosition": "right"}, {"key": "grains_containing_gluten", "type": "selectboxes", "input": true, "label": "Which of the following grains do you regularly consume? (Select all that apply)", "values": [{"label": "Wheat (bread, cereals, pasta, cakes)", "value": "wheat", "shortcut": ""}, {"label": "Barley (soups, cereals, beer, protein bars)", "value": "barley", "shortcut": ""}, {"label": "Rye (rye bread)", "value": "rye", "shortcut": ""}, {"label": "Spelt (spelt bread, cereal, tortillas)", "value": "spelt", "shortcut": ""}, {"label": "I Don't Know", "value": "not sure", "shortcut": ""}, {"label": "None of the above", "value": "none_of_the_above", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "confirm_label": "I eat these gluten containing grains:", "optionsLabelPosition": "right"}, {"key": "gluten_free_grains", "type": "selectboxes", "input": true, "label": "Which of the following grains do you regularly consume? (Select all that apply)", "values": [{"label": "<PERSON><PERSON><PERSON>", "value": "amaranth", "shortcut": ""}, {"label": "Buckwheat", "value": "buckwheat", "shortcut": ""}, {"label": "Corn", "value": "corn", "shortcut": ""}, {"label": "Millet", "value": "millet", "shortcut": ""}, {"label": "Oats", "value": "oats", "shortcut": ""}, {"label": "<PERSON><PERSON><PERSON>", "value": "quinoa", "shortcut": ""}, {"label": "Rice", "value": "rice", "shortcut": ""}, {"label": "Sorghum", "value": "sorghum", "shortcut": ""}, {"label": "<PERSON><PERSON>", "value": "teff", "shortcut": ""}, {"label": "None of the above", "value": "none_of_the_above", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "confirm_label": "I consume these gluten free grains:", "optionsLabelPosition": "right"}, {"key": "gluten_free_diet", "type": "radio", "input": true, "label": "Are you currently on a gluten free diet?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "confirm_label": "Gluten free currently", "optionsLabelPosition": "right"}, {"key": "heading_symptoms", "html": "<h4>Symptoms</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "symptoms", "type": "selectboxes", "input": true, "label": "Which of the following symptoms do you frequently experience? (Select all that apply)", "values": [{"label": "Abdominal pain or discomfort", "value": "abdominal_pain", "shortcut": ""}, {"label": "Diarrhea", "value": "diarrhea", "shortcut": ""}, {"label": "Bloating or gas", "value": "bloating", "shortcut": ""}, {"label": "Unexplained weight loss in the past 6 months", "value": "weight_loss", "shortcut": ""}, {"label": "Fatigue or low energy levels", "value": "fatigue", "shortcut": ""}, {"label": "None of the above", "value": "none_of_the_above", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "confirm_label": "Frequently experienced symptoms:", "optionsLabelPosition": "right"}, {"key": "celiac_red_flags", "type": "selectboxes", "input": true, "label": "Do you have any of the following symptoms:", "values": [{"label": "Fevers", "value": "fevers", "shortcut": ""}, {"label": "Night Sweats", "value": "night_sweats", "shortcut": ""}, {"label": "Weight Loss (Without Dietary/Activity Changes)", "value": "weight_loss", "shortcut": ""}, {"label": "Rectal Bleeding (Red or Tarry/Black Stools)", "value": "rectal_bleeding", "shortcut": ""}, {"label": "None of the above", "value": "none_of_the_above", "shortcut": ""}], "inputType": "checkbox", "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "eczema", "type": "radio", "input": true, "label": "Have you been diagnosed with eczema or psoriasis, or noticed changes in your skin associated with intense itchiness and blistering?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "confirm_label": "Prior eczema or psoriasis diagnosis", "optionsLabelPosition": "right"}, {"key": "location_dermatitis", "type": "selectboxes", "input": true, "label": "Where have you noticed changes in your skin assoiated with itchiness and blistering:", "values": [{"label": "Back of Elbow", "value": "back_elbow", "shortcut": ""}, {"label": "Front of Elbow", "value": "front_elbow", "shortcut": ""}, {"label": "Back of Knee", "value": "back_of_knee", "shortcut": ""}, {"label": "Front of Knee", "value": "front_of_knee", "shortcut": ""}, {"label": "<PERSON><PERSON><PERSON>", "value": "scalp", "shortcut": ""}, {"label": "Buttock", "value": "buttock", "shortcut": ""}, {"label": "Back", "value": "back", "shortcut": ""}, {"label": "None of the above", "value": "none_of_the_above", "shortcut": ""}], "inputType": "checkbox", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.eczema == true;", "optionsLabelPosition": "right"}, {"key": "heading_other_investigations", "html": "<h4>Previous Investigations&nbsp;</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "heading_ultrasound", "html": "<h5>Ultrasound&nbsp;</h5>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "prior_abdominal_us", "type": "radio", "input": true, "label": "Have you had an abdominal ultrasound in the past to investigate your symptoms?", "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No/Don't Know", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "confirm_label": "Current metabolic conditions:", "optionsLabelPosition": "right"}, {"key": "date_of_last_ultrasound", "data": {"values": [{"label": "<6 weeks", "value": "less_6_weeks"}, {"label": "6 weeks - 3 months", "value": "6weeks_3months"}, {"label": "3-6 months", "value": "3-6_months"}, {"label": "6-12 months", "value": "6_12_months"}, {"label": "12-24 months", "value": "12_24_months"}, {"label": "24+ months", "value": "24+_months"}, {"label": "24+ months", "value": "24+_months"}, {"label": "I don't know", "value": "cannot_recall"}]}, "type": "select", "input": true, "label": "When was your abdominal ultrasound completed?", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.prior_abdominal_us == true;", "optionsLabelPosition": "right"}, {"key": "ultrasound_findings", "type": "selectboxes", "input": true, "label": "Do you recall if your doctor identified any of the following:", "values": [{"label": "Fatty liver", "value": "fatty_liver", "shortcut": ""}, {"label": "Liver cysts", "value": "hepatic_cysts", "shortcut": ""}, {"label": "Kidney cysts", "value": "kidney_cysts", "shortcut": ""}, {"label": "Ovarian Cysts", "value": "overian_cysts", "shortcut": ""}, {"label": "None of the above", "value": "none_of_the_above", "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "confirm_label": "Previous ultrasound findings:", "customConditional": "show = data.prior_abdominal_us == true;", "optionsLabelPosition": "right"}, {"key": "ultrasound_findings_other", "type": "textarea", "input": true, "label": "As you selected other, please specify any findings not included above:", "validate": {"required": true}, "tableView": true, "autoExpand": false, "customConditional": "show = data.prior_abdominal_us == true && data.ultrasound_findings.other;"}, {"key": "heading_ct_scan", "html": "<h5>CT Scan&nbsp;</h5>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "prior_ct_scan", "type": "radio", "input": true, "label": "Have you had an abdominal or pelvic CT scan in the past to investigate your symptoms?", "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No/Don't Know", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "confirm_label": "Current metabolic conditions:", "optionsLabelPosition": "right"}, {"key": "date_of_last_ct", "data": {"values": [{"label": "<6 weeks", "value": "less_6_weeks"}, {"label": "6 weeks - 3 months", "value": "6weeks_3months"}, {"label": "3-6 months", "value": "3-6_months"}, {"label": "6-12 months", "value": "6_12_months"}, {"label": "12-24 months", "value": "12_24_months"}, {"label": "24+ months", "value": "24+_months"}, {"label": "24+ months", "value": "24+_months"}, {"label": "I don't know", "value": "cannot_recall"}]}, "type": "select", "input": true, "label": "When was your abdominal ultrasound completed?", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.prior_ct_scan == true;", "optionsLabelPosition": "right"}, {"key": "ct_findings", "type": "selectboxes", "input": true, "label": "Do you recall if your doctor identified any of the following:", "values": [{"label": "Fatty liver", "value": "fatty_liver", "shortcut": ""}, {"label": "Liver cysts", "value": "hepatic_cysts", "shortcut": ""}, {"label": "Kidney cysts", "value": "kidney_cysts", "shortcut": ""}, {"label": "Ovarian Cysts", "value": "overian_cysts", "shortcut": ""}, {"label": "Pancreatic Cysts", "value": "pancreatic_cysts", "shortcut": ""}, {"label": "None of the above", "value": "none_of_the_above", "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "confirm_label": "Previous CT findings:", "customConditional": "show = data.prior_ct_scan == true;", "optionsLabelPosition": "right"}, {"key": "ct_findings_other", "type": "textarea", "input": true, "label": "As you selected other, please specify any findings not included above:", "validate": {"required": true}, "tableView": true, "autoExpand": false, "customConditional": "show = data.prior_ct_scan == true && data.ct_findings.other;"}, {"key": "heading_colonoscopy", "html": "<h5>Colonoscopy&nbsp;</h5>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "prior_colonoscopy", "type": "radio", "input": true, "label": "Have you had a colonoscopy or sigmoidoscopy before?", "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No/Don't Know", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "confirm_label": "Previous colonoscopy/sigmoidoscopy:", "optionsLabelPosition": "right"}, {"key": "date_of_last_colonoscopy", "data": {"values": [{"label": "<6 weeks", "value": "less_6_weeks"}, {"label": "6 weeks - 3 months", "value": "6weeks_3months"}, {"label": "3-6 months", "value": "3-6_months"}, {"label": "6-12 months", "value": "6_12_months"}, {"label": "1-2 years", "value": "1-2_years"}, {"label": "2-3 years", "value": "2-3_years"}, {"label": "3-5 years", "value": "3-5_years"}, {"label": "5-10 years", "value": "5-10_years"}, {"label": "10+ years", "value": "10+_years"}, {"label": "I don't know", "value": "doesn't_know"}]}, "type": "select", "input": true, "label": "When was your colonoscopy completed", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.prior_colonoscopy == true;", "optionsLabelPosition": "right"}, {"key": "colonoscopy_findings", "type": "selectboxes", "input": true, "label": "Do you recall if your doctor identified any of the following:", "values": [{"label": "Polyps", "value": "polyps", "shortcut": ""}, {"label": "Diverticulosis / Diverticulitis", "value": "diverticulosis/diverticulitis", "shortcut": ""}, {"label": "Hemorrhoids (interal/external)", "value": "hemorrhoids", "shortcut": ""}, {"label": "Anal fissure", "value": "anal_fissure", "shortcut": ""}, {"label": "Inflammation (i.e. colitis)", "value": "inflammation", "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "confirm_label": "Previous colonoscopy/sigmoidoscopy findings:", "customConditional": "show = data.prior_colonoscopy == true;", "optionsLabelPosition": "right"}, {"key": "colonoscopy_findings_other", "type": "textarea", "input": true, "label": "As you selected other, please specify any findings not included above:", "validate": {"required": true}, "tableView": true, "autoExpand": false, "customConditional": "show = data.prior_colonoscopy == true && data.colonoscopy_findings.other;"}, {"key": "heading_endoscopy", "html": "<h5>Endoscopy&nbsp;</h5>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "prior_endoscopy", "type": "radio", "input": true, "label": "Have you had an endoscopy / gastroscopy (camera examines throat to stomach)?", "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No/Don't Know", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "confirm_label": "Previous colonoscopy/sigmoidoscopy:", "optionsLabelPosition": "right"}, {"key": "date_of_last_endoscopy", "data": {"values": [{"label": "<6 weeks", "value": "less_6_weeks"}, {"label": "6 weeks - 3 months", "value": "6weeks_3months"}, {"label": "3-6 months", "value": "3-6_months"}, {"label": "6-12 months", "value": "6_12_months"}, {"label": "1-2 years", "value": "1-2_years"}, {"label": "2-3 years", "value": "2-3_years"}, {"label": "3-5 years", "value": "3-5_years"}, {"label": "5-10 years", "value": "5-10_years"}, {"label": "10+ years", "value": "10+_years"}, {"label": "I don't know", "value": "doesn't_know"}]}, "type": "select", "input": true, "label": "When was your endoscopy completed", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.prior_endoscopy == true;", "optionsLabelPosition": "right"}, {"key": "endoscopy_findings", "type": "selectboxes", "input": true, "label": "Do you recall if your doctor identified any of the following:", "values": [{"label": "<PERSON><PERSON>", "value": "h_pylori", "shortcut": ""}, {"label": "Gastric or duodenal ulcer (i.e. an ulcer)", "value": "ulcer", "shortcut": ""}, {"label": "<PERSON>'s esophagus", "value": "barret's_esophagus", "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "confirm_label": "Previous endoscopy findings:", "customConditional": "show = data.prior_endoscopy == true;", "optionsLabelPosition": "right"}, {"key": "endoscopy_findings_other", "type": "textarea", "input": true, "label": "As you selected other, please specify any findings not included above:", "validate": {"required": true}, "tableView": true, "autoExpand": false, "customConditional": "show = data.prior_endoscopy == true && data.endoscopy_findings.other;"}, {"key": "heading_nutrient", "html": "<h5>Vitamins&nbsp;</h5>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "vitamin_deficiency_testing", "type": "selectboxes", "input": true, "label": "Please select if you have any of the following vitamin deficiencies (i.e. low levels) confirmed by a doctor or previous test:", "values": [{"label": "Vitamin B12", "value": "vitamin_b12", "shortcut": ""}, {"label": "<PERSON><PERSON>", "value": "vitamin_d", "shortcut": ""}, {"label": "Iron Deficiency (i.e. low ferritin)", "value": "iron_deficiency", "shortcut": ""}, {"label": "None of the above", "value": "none_of_the_above", "shortcut": ""}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "confirm_label": "Current vitamin deficiencies:", "optionsLabelPosition": "right"}, {"key": "content2", "html": "<h2>Questions for the Doctor</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "additional_questions", "type": "textarea", "input": true, "label": "Please use this area to ask any questions that you might have for your health care provider:", "tableView": true, "autoExpand": false}]}