{"components": [{"key": "heading_acne", "html": "<h1><strong>Acne Prescription</strong></h1><p>To expedite your care, please answer the following questions to the best of your abilities. &nbsp;We ask these questions to provide the best possible care.&nbsp;</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "heading_symptoms", "html": "<h2><strong>Diagnosis</strong></h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "prior_acne_diagnosis", "type": "radio", "input": true, "label": "Have you been diagnosed by a health care provider with acne in the past (i.e. doctor, dermatologist, nurse practitioner, etc)?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "tableView": true, "validate": {"required": true}, "optionsLabelPosition": "right"}, {"key": "prior_acne_diagnosis_provider", "type": "radio", "input": true, "label": "Who diagnosed you with acne in the past?", "inline": false, "values": [{"label": "Family Doctor", "value": "family_doctor", "shortcut": ""}, {"label": "Dermatologist", "value": "dermatologist", "shortcut": ""}, {"label": "Nurse Practitioner", "value": "nurse_practitoner", "shortcut": ""}, {"label": "Naturopathic Doctor", "value": "naturopath", "shortcut": ""}, {"label": "Self-Diagnosis", "value": "self_diagnosis", "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}], "tableView": true, "validate": {"required": true}, "customConditional": "show = data.prior_acne_diagnosis == true;", "optionsLabelPosition": "right"}, {"key": "other_diagnosing_professional", "type": "textarea", "input": true, "label": "Please state who diagnosed you with acne in the past:", "tableView": true, "autoExpand": false, "customConditional": "show = data.prior_acne_diagnosis_provider == 'other';"}, {"key": "prior_acne_type", "type": "radio", "input": true, "label": "Has a medical professional ever described your acne as any of the following:", "inline": false, "values": [{"label": "Mild", "value": "mild_acne", "shortcut": ""}, {"label": "Mild-moderate", "value": "mild_acne", "shortcut": ""}, {"label": "Moderate-severe", "value": "moderate-severe", "shortcut": ""}, {"label": "Nodulocystic", "value": "nodulocystic", "shortcut": ""}, {"label": "I don't know", "value": "doesn't_know_grading", "shortcut": ""}], "tableView": true, "customConditional": "show = data.prior_acne_diagnosis == true;", "validate": {"required": true}, "optionsLabelPosition": "right"}, {"key": "current_whiteheads", "type": "radio", "input": true, "label": "Do you currently have <strong>whiteheads</strong>? (These are tiny bumps that are white in colour, not painful and not filled with pus)", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "I don't know", "value": "doesn't_know_white_heads", "shortcut": ""}], "tableView": true, "validate": {"required": true}, "optionsLabelPosition": "right"}, {"key": "current_blackheads", "type": "radio", "input": true, "label": "Do you currently have <strong>blackheads</strong>? (These are tiny bumps that are black in colour, not painful and not filled with pus)", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "I don't know", "value": "doesn't_know_black_heads", "shortcut": ""}], "tableView": true, "validate": {"required": true}, "optionsLabelPosition": "right"}, {"key": "current_acne_symptoms", "type": "selectboxes", "input": true, "inputType": "checkbox", "label": "Are you currently experiencing any of the following symptoms:", "inline": false, "values": [{"label": "<strong>Papule</strong> (< 5mm painful bumps that are pink and are not full of pus)", "value": "papule"}, {"label": "<strong>Pustule</strong> (< 5mm painful bumps that have a red base, yellow centre and are full of pus)", "value": "pustule"}, {"label": "<strong>Nodule</strong> (5-10mm painful bumps that are pink/red and warm)", "value": "nodule"}, {"label": "<strong>Cysts</strong> (> 5mm red painful bumps that are warm and full of liquid without pus)", "value": "cysts"}, {"label": "None of the above", "value": "none_of_the_above"}, {"label": "I don't know", "value": "doesn't_know"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "current_acne_flair", "type": "radio", "input": true, "label": "Are you currently experiencing a flare of your acne?", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "I don't know", "value": "doesn't_know", "shortcut": ""}], "tableView": true, "validate": {"required": true}, "optionsLabelPosition": "right"}, {"key": "current_distribution", "type": "selectboxes", "input": true, "inputType": "checkbox", "label": "Please select where you currently have acne outbreaks:", "inline": false, "values": [{"label": "Face", "value": "face"}, {"label": "Neck", "value": "neck"}, {"label": "Shoulder", "value": "shoulder"}, {"label": "Back", "value": "back"}, {"label": "Other", "value": "other"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "other_body_site", "type": "textarea", "input": true, "label": "Please state other affected areas:", "tableView": true, "autoExpand": false, "customConditional": "show = data.current_distribution.other;"}, {"key": "heading_symptoms", "html": "<h2><strong>Over-the-Counter Treatments</strong></h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "info_over_the_counter", "html": "<p>Please share information about your current non-prescription treatments that you have tried below.&nbsp;</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "current_daily_skin_care_routine", "type": "radio", "input": true, "label": "Do you currently have a daily or nightly skin care routine?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "tableView": true, "validate": {"required": true}, "optionsLabelPosition": "right"}, {"key": "daily_skin_cleanser", "type": "radio", "input": true, "label": "Do you use a gentle skin cleanser (i.e. CeraVe, Cetaphil, Clinique) on a daily basis?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "tableView": true, "validate": {"required": true}, "optionsLabelPosition": "right"}, {"key": "daily_non-comeddomic_moisturizer", "type": "radio", "input": true, "label": "Do you use a 'non-comedogenic' (won't clog your pores) moisturizer (i.e. Proactiv, Neutrogena, CeraVe)?", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "I don't know", "value": "doesn't_know", "shortcut": ""}], "tableView": true, "validate": {"required": true}, "optionsLabelPosition": "right"}, {"key": "daily_BPO_face_wash", "type": "radio", "input": true, "label": "Do you use a facial cleanser containing Benzoyl Peroxide (BPO)?", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "I don't know", "value": "doesn't_know", "shortcut": ""}], "tableView": true, "validate": {"required": true}, "optionsLabelPosition": "right"}, {"key": "bpo_strength", "type": "selectboxes", "input": true, "inputType": "checkbox", "label": "Please select the strength of your BPO cleanser:", "inline": false, "values": [{"label": "1-2.5%", "value": "1-2.5%"}, {"label": "2.5%-5%", "value": "2.5%-5%"}, {"label": "5%-7.5%", "value": "5%-7.5%"}, {"label": "7.5-10%", "value": "7.5%-10%"}, {"label": "Don't Know/Not Applicable", "value": "n/a"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.daily_BPO_face_wash == 'yes';", "optionsLabelPosition": "right"}, {"key": "daily_BPO_body_wash", "type": "radio", "input": true, "label": "Do you use a Benzoyl Peroxide (BPO) containing body wash?", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}], "tableView": true, "validate": {"required": true}, "optionsLabelPosition": "right"}, {"key": "current_daily_skin_care_routine", "type": "radio", "input": true, "label": "Do you currently use make-up?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "tableView": true, "validate": {"required": true}, "optionsLabelPosition": "right"}, {"key": "heading_treatment", "html": "<h2><strong>Prescription Treatments</strong></h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "prior_treatments", "type": "radio", "input": true, "label": "Have you been on prescription acne medication in the past?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "tableView": true, "validate": {"required": true}, "optionsLabelPosition": "right"}, {"key": "previous_treatment_type", "type": "selectboxes", "input": true, "inputType": "checkbox", "label": "Please select your previous treatment medication(s):", "inline": false, "values": [{"label": "Cream - Gel", "value": "topical"}, {"label": "Pill - Antibiotic (i.e. Doxycycline, Minocycline, Tetracycline, Erythromycin)", "value": "oral_abx"}, {"label": "Pill - Isotretinoin (i.e. Accutane)", "value": "oral_isotretinoin"}, {"label": "Birth Control Pills", "value": "oral_contraceptives", "customConditional": "show = data.sex == 'female';"}, {"label": "Spirinolactone", "value": "spirinolactone", "customConditional": "show = data.sex == 'female';"}, {"label": "None of the above", "value": "none_of_the_above"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.prior_treatments == true;", "optionsLabelPosition": "right"}, {"key": "heading_antibiotic_creams", "html": "<h3><strong>Cream & Gel Based Treatments</strong></h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.prior_treatments == true && data.previous_treatment_type.topical;", "refreshOnChange": false}, {"key": "heading_antibiotic_creams", "html": "<h4><strong>Antibiotic Gels</strong></h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.prior_treatments == true && data.previous_treatment_type.topical;", "refreshOnChange": false}, {"key": "previous_antibiotic_creams", "type": "selectboxes", "input": true, "inputType": "checkbox", "customConditional": "show = data.prior_treatments == true && data.previous_treatment_type.topical;", "label": "Please select if you have tried any of the following antibiotic containing products:", "inline": false, "values": [{"label": "Cleocin (Clindamycin 1%)", "value": "clindamycin_1%"}, {"label": "Erysol (Erythyromycin 2%)", "value": "erythromycin_2%"}, {"label": "Benzaclin (Clindamycin-BPO 0.3%-2.5%)", "value": "benzaclin-0.3%-2.5%"}, {"label": "Benzaclin (Clindamycin-BPO 1%-5%)", "value": "benzaclin-1%-5%"}, {"label": "Other", "value": "other"}, {"label": "None of the above", "value": "none_of_the_above"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "other_antibiotic_cream", "type": "textarea", "input": true, "label": "Please state other antibiotic gels that you have used in the past:", "tableView": true, "autoExpand": false, "customConditional": "show = data.previous_antibiotic_creams.other;"}, {"key": "heading_retinoid_creams", "html": "<h4><strong>Retinoid Based Gels</strong></h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.prior_treatments == true && data.previous_treatment_type.topical;", "refreshOnChange": false}, {"key": "previous_retinoids", "type": "selectboxes", "input": true, "inputType": "checkbox", "customConditional": "show = data.prior_treatments == true && data.previous_treatment_type.topical;", "label": "Please select if you have tried any of the following products:", "inline": false, "values": [{"label": "<PERSON><PERSON><PERSON> (Adapalene)", "value": "adapalene"}, {"label": "Retin/Stevia-A (Tretinoin)", "value": "tretinoin"}, {"label": "Tazorac (Tazarotene)", "value": "tazarotene"}, {"label": "TactuPump/Epiduo (Adapelene-BPO 0.1%/2.5%)", "value": "Adapelene-0.1-BPO-2.5-TactuPump"}, {"label": "TactuPump/Epiduo Forte (Adapelene-BPO 0.3%/2.5%)", "value": "Adapelene-0.3-BPO-2.5-TactuPumpForte"}, {"label": "Other", "value": "other"}, {"label": "None of the above", "value": "none_of_the_above"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "other_retinoids", "type": "textarea", "input": true, "label": "Please state other retinoids that you have used in the past:", "tableView": true, "autoExpand": false, "customConditional": "show = data.previous_retinoids == 'other';"}, {"key": "heading_tretinoin", "html": "<h3><strong>Tretinoin</strong></h3>", "customConditional": "show = data.prior_treatments == true && data.previous_retinoids.tretinoin;", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "previous_retinoin", "type": "selectboxes", "input": true, "inputType": "checkbox", "customConditional": "show = data.prior_treatments == true && data.previous_retinoids.tretinoin;", "label": "Please select what type of Stevia/Retin-A (Generic: Tretinoin) you have used in the past:", "inline": false, "values": [{"label": "Tretinoin 0.025% Cream", "value": "tretinoin_0.025%_cream"}, {"label": "Tretinoin 0.05% Cream", "value": "tretinoin_0.05%_cream"}, {"label": "Tretinoin 0.1% Cream", "value": "tretinoin_0.1%_cream"}, {"label": "Tretinoin 0.01% Gel", "value": "tretinoin_0.01%_gel"}, {"label": "Tretinoin 0.025% Gel", "value": "tretinoin_0.025%_gel"}, {"label": "Tretinoin 0.05%% Gel", "value": "tretinoin_0.05%_gel"}, {"label": "I don't know", "value": "doesn't_know"}, {"label": "None of the above", "value": "none_of_the_above"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "heading_adapelene", "html": "<h3><strong><PERSON><PERSON><PERSON> (Adapelene)</strong></h3>", "customConditional": "show = data.prior_treatments == true && data.previous_retinoids.adapalene;", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "previous_differin", "type": "selectboxes", "input": true, "inputType": "checkbox", "customConditional": "show = data.prior_treatments == true && data.previous_retinoids.adapalene;", "label": "Please select what type of Differin (Generic: Adapelene) you used:", "inline": false, "values": [{"label": "Adapalene 0.1% Cream", "value": "adapalene_0.1%_cream"}, {"label": "Adapalene 0.1% Lotion", "value": "adapalene_0.1%_lotion"}, {"label": "Adapalene 0.1% Gel", "value": "adapalene_0.1%_gel"}, {"label": "Adapalene 0.3% Gel", "value": "adapalene_0.3%_gel"}, {"label": "TactuPump (Generic: Adapelene(0.1%)/BPO(2.5%)", "value": "Adapelene-0.1-BPO-2.5-TactuPump"}, {"label": "TactuPump Forte (Generic: Adapelene(0.3%)/BPO(2.5%)", "value": "Adapelene-0.3-BPO-2.5-TactuPumpForte"}, {"label": "I don't know", "value": "doesn't_know"}, {"label": "None of the above", "value": "none_of_the_above"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "heading_tazarotene", "html": "<h3><strong><PERSON><PERSON><PERSON> (Tazarotene)</strong></h3>", "customConditional": "show = data.prior_treatments == true && data.previous_retinoids.tazarotene;", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "previous_tazarotene", "type": "selectboxes", "input": true, "inputType": "checkbox", "customConditional": "show = data.prior_treatments == true && data.previous_retinoids.tazarotene;", "label": "Please select what type of Tazorac (Tazarotene) you used:", "inline": false, "values": [{"label": "Tazarotene 0.05% Cream", "value": "tazarotene_0.05%_cream"}, {"label": "Tazarotene 0.05% Gel", "value": "tazarotene_0.05%_gel"}, {"label": "Tazarotene 0.1% Cream", "value": "tazarotene_0.1%_cream"}, {"label": "Tazarotene 0.1% Gel", "value": "tazarotene_0.1%_gel"}, {"label": "I don't know", "value": "doesn't_know"}, {"label": "None of the above", "value": "none_of_the_above"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "heading_oral_antibiotics", "html": "<h3><strong>Oral Antibiotics</strong></h3>", "customConditional": "show = data.prior_treatments == true && data.previous_treatment_type.oral_abx;", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "previous_oral_antibiotics", "type": "selectboxes", "input": true, "inputType": "checkbox", "customConditional": "show = data.prior_treatments == true && data.previous_treatment_type.oral_abx;", "label": "Please select if you have tried any of the following products:", "inline": false, "values": [{"label": "Doxycycline 100mg once per day", "value": "doxycycline_100mg_od"}, {"label": "Doxycycline 100mg twice per day", "value": "doxycycline_100mg_bid"}, {"label": "Minocycyline 100mg once per day", "value": "minocycline_100mg_od"}, {"label": "Minocycline 100mg twice per day", "value": "minocycline_100mg_bid"}, {"label": "Tetracycline 500mg twice per day", "value": "tetracycline_500mg_bid"}, {"label": "Tetracycline 250mg four times per day", "value": "tetracycline_250mg_qid"}, {"label": "Other", "value": "other"}, {"label": "None of the above", "value": "none_of_the_above"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "other_antibiotics", "type": "textarea", "input": true, "label": "Please state other antibiotics that you have used in the past:", "tableView": true, "autoExpand": false, "customConditional": "show = data.previous_oral_antibiotics.other;"}, {"key": "last_oral_antibiotic_treatment", "data": {"values": [{"label": "<1 week ago", "value": "less_1_weeks"}, {"label": "2-4 weeks ago", "value": "2-4_weeks_ago"}, {"label": "4 weeks - 6 weeks ago", "value": "4_weeks-6_weeks"}, {"label": "6 weeks - 3 months ago", "value": "6_weeks-3_months"}, {"label": "3-6 months", "value": "3-6_months"}, {"label": "6-12 months", "value": "6_12_months"}, {"label": "12-24 months", "value": "12_24_months"}, {"label": "24+ months", "value": "24+_months"}]}, "type": "select", "input": true, "label": "When were you last on oral antibiotics?", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.prior_treatments == true && data.previous_treatment_type.oral_abx;", "optionsLabelPosition": "right"}, {"key": "heading_isotretinoin", "html": "<h3><strong>Accutane (i.e. Isotretinoin)</strong></h3>", "customConditional": "show =  data.prior_treatments == true && data.previous_treatment_type.oral_isotretinoin;", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "last_oral_isotretinoin", "data": {"values": [{"label": "<1 week ago", "value": "less_1_weeks"}, {"label": "2-4 weeks ago", "value": "2-4_weeks_ago"}, {"label": "4 weeks - 6 weeks ago", "value": "4_weeks-6_weeks"}, {"label": "6 weeks - 3 months ago", "value": "6_weeks-3_months"}, {"label": "3-6 months", "value": "3-6_months"}, {"label": "6-12 months", "value": "6_12_months"}, {"label": "12-24 months", "value": "12_24_months"}, {"label": "24+ months", "value": "24+_months"}]}, "type": "select", "input": true, "label": "When were you last on Accutane (i.e. oral isotretinoin)?", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show =  data.prior_treatments == true && data.previous_treatment_type.oral_isotretinoin;", "optionsLabelPosition": "right"}, {"key": "content5", "html": "<h2>Pregnancy</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": true, "customConditional": "show = data.sex == 'female';"}, {"key": "currently_pregnant", "type": "radio", "input": true, "label": "Are you currently pregnant?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}, {"label": "I don't know", "value": "i_dont_know", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.sex == 'female';", "optionsLabelPosition": "right"}]}