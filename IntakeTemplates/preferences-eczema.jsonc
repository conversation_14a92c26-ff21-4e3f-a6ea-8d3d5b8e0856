{"components": [{"key": "mobile_table_css", "tag": "style", "type": "htmlelement", "content": "@media (max-width:600px){\n  /* hide any header row inside either grid */\n  .flaretbl table thead,\n  .flaretbl .datagrid-table thead{display:none!important;}\n  /* turn rows into cards */\n  .flaretbl table tbody tr{display:block;margin:0 0 1rem;border:1px solid #e0e0e0;border-radius:4px;}\n  /* make each cell full-width and wrap text */\n  .flaretbl table tbody td{display:block;width:100%;border:none;padding:6px 12px;white-space:normal;}\n}"}, {"key": "eczema_header", "html": "<h1 style=\"text-align:center;\">Eczema Treatment Options</h1>", "type": "content", "input": false, "label": "Content"}, {"key": "eczema_overview", "html": "<div class=\"alert alert-success\" style=\"padding:1rem;\"><h4>Step&nbsp;1: Treat the Flare First</h4><p>An eczema flare is like an <em>immune fire</em>. Put it out quickly with an <strong>anti-inflammatory cream</strong>—usually a short burst of topical steroid, or a steroid-free ointment such as Protopic, Eucrisa, or Zoryve.</p><br><h4>Step&nbsp;2: Maintain Control</h4><p>Once clear, switch to <em>“weekend therapy”</em> (the same cream <strong>twice a week</strong> on past trouble-spots) and keep moisturising every day. This keeps new flares from sparking.</p><br><h4>Did&nbsp;You&nbsp;Know? Steroid-Free Creams</h4><ul><li><strong>Protopic® 0.1&nbsp;% ointment</strong> (since 2001) calms inflammation <em>without</em> thinning skin—perfect for the face, folds, and eyelids.</li><li><strong>Eucrisa® 2&nbsp;% ointment</strong> &amp; <strong>Zoryve® 0.15&nbsp;% cream</strong> (launched 2023 &amp; 2025) soothe itch and redness in mild-to-moderate eczema and can be used <em>daily</em> for prevention.</li></ul><p>Because they aren't steroids, you can use these options longer on delicate areas—though they remain pricey (≈ $250 - $350 per 60-g tube).</p><br><h4>TeleTest Recommendation</h4><p>If you haven't tried a steroid-free cream before, we generally advise a trial of Protopic on your problem areas as a maintenance option to see whether your skin stays calmer between flares.</p></div>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "treatment_intro", "html": "<br><h4>Tell us a few details to see your best treatment options:</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "drug_coverage", "type": "radio", "input": true, "label": "Do you have a prescription-drug plan?", "inline": false, "values": [{"label": "OHIP+ (age ≤ 24)", "value": "ohip_plus"}, {"label": "Private drug plan", "value": "private_plan"}, {"label": "ODB - Ontario Drug Benefit (age ≥ 65)", "value": "odb"}, {"label": "No drug plan", "value": "none"}], "widget": "html5", "validate": {"required": true}, "tableView": true}, {"key": "steroid_free_interest", "type": "radio", "label": "Are you interested in trying a steroid-free eczema cream?", "values": [{"label": "Yes, show me the options", "value": "yes"}, {"label": "No, I prefer steroid creams only", "value": "no"}], "widget": "html5", "tableView": true, "validate": {"required": true}}, {"key": "steroid_free_choices", "type": "selectboxes", "label": "Which steroid-free treatment(s) would you like to consider?", "values": [{"label": "Protopic 0.1 % ointment", "value": "protopic"}, {"label": "Eucrisa 2 % ointment", "value": "e<PERSON><PERSON>a"}, {"label": "Zoryve 0.15 % cream", "value": "zoryve"}], "widget": "html5", "tableView": true, "customConditional": "show = data.steroid_free_interest === 'yes';"}, {"key": "heading_body_areas", "html": "<br><h4>Which area of your body is affected?</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "body_areas", "type": "selectboxes", "label": "Which body area(s) are you treating today? (Select all that apply)", "values": [{"label": "Face / Groin / Genitals / Under-breast", "value": "face_groin"}, {"label": "Eyelids / Around eyes", "value": "eyelids"}, {"label": "Neck / Skin-folds", "value": "neck_folds"}, {"label": "Chest / Back / Arms / Legs / Hands (soft part)", "value": "trunk_limbs"}, {"label": "Palms & Soles (thick skin)", "value": "palms_soles"}, {"label": "Scalp (hair-bearing)", "value": "scalp"}, {"label": "Ear canal", "value": "ear_canal"}], "widget": "html5", "tableView": true, "validate": {"required": true}}, {"key": "rec_hint", "html": "<p><em>Tip:&nbsp;Options shown in <span style=\"color:#28a745;font-weight:bold;\">bold&nbsp;green</span> are our recommended choice, but you're welcome to pick <strong>any</strong> treatment you prefer.</em></p>", "type": "content", "input": false, "label": "Content"}, {"key": "heading_face_groin", "html": "<br><h4><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Under the Breast</h4>", "type": "content", "input": false, "label": "Content", "customConditional": "show = data.body_areas && data.body_areas.face_groin === true;"}, {"key": "face_groin_plan_yes", "type": "radio", "label": "When this area is inflamed, which medication will you use?", "values": [{"label": "<span style=\"color:#28a745;font-weight:bold;\">Hydrocortisone + Protopic 0.1 %</span>", "value": "hc_protopic"}, {"label": "Hydrocortisone 1 % cream", "value": "hc1"}, {"label": "Zoryve 0.15 % cream (daily)", "value": "zoryve_daily"}, {"label": "Eucrisa 2 % ointment", "value": "e<PERSON><PERSON>a"}, {"label": "Protopic 0.1 % ointment (Recommended)", "value": "protopic"}, {"label": "Other (type below)", "value": "other"}], "widget": "html5", "tableView": true, "validate": {"required": true}, "customConditional": "show = data.body_areas && data.body_areas.face_groin === true && data.steroid_free_interest === 'yes';"}, {"key": "face_groin_plan_no", "type": "radio", "label": "When this area is inflamed, which medication will you use?", "values": [{"label": "Hydrocortisone 1 % cream", "value": "hc1"}, {"label": "Other (type below)", "value": "other"}], "widget": "html5", "tableView": true, "validate": {"required": true}, "customConditional": "show = data.body_areas && data.body_areas.face_groin === true && data.steroid_free_interest === 'no';"}, {"key": "face_groin_plan_other", "rows": 3, "type": "textarea", "tableView": true, "label": "Describe your flare-time treatment:", "customConditional": "show = (data.face_groin_plan_yes === 'other' || data.face_groin_plan_no === 'other');"}, {"key": "face_groin_maint_yes", "tableView": true, "type": "radio", "label": "Once the skin is calm, which product will you use to keep it clear?", "values": [{"label": "<span style=\"color:#28a745;font-weight:bold;\">Protopic 0.1 % ointment (weekends only)</span>", "value": "protopic_weekend"}, {"label": "Eucrisa 2 % ointment (daily)", "value": "eucrisa_daily"}, {"label": "Zoryve 0.15 % cream (daily)", "value": "zoryve_daily"}, {"label": "None - I don't need ongoing treatment", "value": "none"}, {"label": "Other (type below)", "value": "other"}], "widget": "html5", "validate": {"required": true}, "customConditional": "show = data.body_areas && data.body_areas.face_groin === true && data.steroid_free_interest === 'yes';"}, {"key": "face_groin_maint_no", "tableView": true, "type": "radio", "label": "Once the skin is calm, which product will you use to keep it clear?", "values": [{"label": "None - I don't need ongoing treatment", "value": "none"}, {"label": "Other (type below)", "value": "other"}], "widget": "html5", "validate": {"required": true}, "customConditional": "show = data.body_areas && data.body_areas.face_groin === true && data.steroid_free_interest === 'no';"}, {"key": "face_groin_maint_other", "tableView": true, "rows": 3, "type": "textarea", "label": "Describe your maintenance option:", "customConditional": "show = (data.face_groin_maint_yes === 'other' || data.face_groin_maint_no === 'other');"}, {"key": "heading_eyelids", "html": "<br><h4>Eyelids &amp; Around the Eyes</h4>", "type": "content", "input": false, "label": "Content", "customConditional": "show = data.body_areas && data.body_areas.eyelids === true;"}, {"key": "eyelids_plan_yes", "tableView": true, "type": "radio", "label": "When your eyelids are inflamed, which medication will you use?", "values": [{"label": "<span style=\"color:#28a745;font-weight:bold;\">Protopic 0.1 % ointment (Recommended)</span>", "value": "protopic"}, {"label": "Zoryve 0.15 % cream (daily)", "value": "zoryve_daily"}, {"label": "Eucrisa 2 % ointment", "value": "e<PERSON><PERSON>a"}, {"label": "Hydrocortisone 1 % cream", "value": "hc1"}, {"label": "Other (type below)", "value": "other"}], "widget": "html5", "validate": {"required": true}, "customConditional": "show = data.body_areas && data.body_areas.eyelids === true && data.steroid_free_interest === 'yes';"}, {"key": "eyelids_plan_no", "tableView": true, "type": "radio", "label": "When your eyelids are inflamed, which medication will you use?", "values": [{"label": "Hydrocortisone 1 % cream", "value": "hc1"}, {"label": "Other (type below)", "value": "other"}], "widget": "html5", "validate": {"required": true}, "customConditional": "show = data.body_areas && data.body_areas.eyelids === true && data.steroid_free_interest === 'no';"}, {"key": "eyelids_plan_other", "tableView": true, "rows": 3, "type": "textarea", "label": "Describe your flare-time treatment:", "customConditional": "show = (data.eyelids_plan_yes === 'other' || data.eyelids_plan_no === 'other');"}, {"key": "eyelids_maint_yes", "tableView": true, "type": "radio", "label": "Once the skin is calm, which product will you use to keep it clear?", "values": [{"label": "<span style=\"color:#28a745;font-weight:bold;\">Protopic 0.1 % ointment (weekends only)</span>", "value": "protopic_weekend"}, {"label": "Eucrisa 2 % ointment (daily)", "value": "eucrisa_daily"}, {"label": "Zoryve 0.15 % cream (daily)", "value": "zoryve_daily"}, {"label": "None - I don't need ongoing treatment", "value": "none"}, {"label": "Other (type below)", "value": "other"}], "widget": "html5", "validate": {"required": true}, "customConditional": "show = data.body_areas && data.body_areas.eyelids === true && data.steroid_free_interest === 'yes';"}, {"key": "eyelids_maint_no", "tableView": true, "type": "radio", "label": "Once the skin is calm, which product will you use to keep it clear?", "values": [{"label": "None - I don't need ongoing treatment", "value": "none"}, {"label": "Other (type below)", "value": "other"}], "widget": "html5", "validate": {"required": true}, "customConditional": "show = data.body_areas && data.body_areas.eyelids === true && data.steroid_free_interest === 'no';"}, {"key": "eyelids_maint_other", "tableView": true, "rows": 3, "type": "textarea", "label": "Describe your maintenance option:", "customConditional": "show = (data.eyelids_maint_yes === 'other' || data.eyelids_maint_no === 'other');"}, {"key": "heading_neck_folds", "html": "<br><h4>Skin Folds - Neck &amp; Body Folds</h4>", "type": "content", "input": false, "label": "Content", "customConditional": "show = data.body_areas && data.body_areas.neck_folds === true;"}, {"key": "neck_folds_plan_yes", "type": "radio", "tableView": true, "label": "When this area flares, which medication will you use?", "values": [{"label": "<span style=\"color:#28a745;font-weight:bold;\">Protopic 0.1 % ointment (Recommended)</span>", "value": "protopic"}, {"label": "Hydrocortisone 1 % cream", "value": "hc1"}, {"label": "Eucrisa 2 % ointment", "value": "e<PERSON><PERSON>a"}, {"label": "Zoryve 0.15 % cream (daily)", "value": "zoryve_daily"}, {"label": "Other (type below)", "value": "other"}], "widget": "html5", "validate": {"required": true}, "customConditional": "show = data.body_areas && data.body_areas.neck_folds === true && data.steroid_free_interest === 'yes';"}, {"key": "neck_folds_plan_no", "type": "radio", "tableView": true, "label": "When this area flares, which medication will you use?", "values": [{"label": "Hydrocortisone 1 % cream", "value": "hc1"}, {"label": "Other (type below)", "value": "other"}], "widget": "html5", "validate": {"required": true}, "customConditional": "show = data.body_areas && data.body_areas.neck_folds === true && data.steroid_free_interest === 'no';"}, {"key": "neck_folds_plan_other", "tableView": true, "rows": 3, "type": "textarea", "label": "Describe your flare-time treatment:", "customConditional": "show = (data.neck_folds_plan_yes === 'other' || data.neck_folds_plan_no === 'other');"}, {"key": "neck_folds_maint_yes", "tableView": true, "type": "radio", "label": "After it's calm, which product will you use to keep this area clear?", "values": [{"label": "<span style=\"color:#28a745;font-weight:bold;\">Protopic 0.1 % ointment (weekends only)</span>", "value": "protopic_weekend"}, {"label": "Eucrisa 2 % ointment (daily)", "value": "eucrisa_daily"}, {"label": "Zoryve 0.15 % cream (daily)", "value": "zoryve_daily"}, {"label": "None - I don't need maintenance", "value": "none"}, {"label": "Other (type below)", "value": "other"}], "widget": "html5", "validate": {"required": true}, "customConditional": "show = data.body_areas && data.body_areas.neck_folds === true && data.steroid_free_interest === 'yes';"}, {"key": "neck_folds_maint_no", "tableView": true, "type": "radio", "label": "After it's calm, which product will you use to keep this area clear?", "values": [{"label": "None - I don't need maintenance", "value": "none"}, {"label": "Other (type below)", "value": "other"}], "widget": "html5", "validate": {"required": true}, "customConditional": "show = data.body_areas && data.body_areas.neck_folds === true && data.steroid_free_interest === 'no';"}, {"key": "neck_folds_maint_other", "tableView": true, "rows": 3, "type": "textarea", "label": "Describe your maintenance option:", "customConditional": "show = (data.neck_folds_maint_yes === 'other' || data.neck_folds_maint_no === 'other');"}, {"key": "heading_trunk_limbs", "html": "<br><h4>Trunk, Arms &amp; Legs</h4>", "type": "content", "input": false, "label": "Content", "customConditional": "show = data.body_areas && data.body_areas.trunk_limbs === true;"}, {"key": "trunk_limbs_plan_yes", "tableView": true, "type": "radio", "label": "When this area flares, which medication will you use?", "values": [{"label": "<span style=\"color:#28a745;font-weight:bold;\">Betamethasone valerate 0.05&nbsp;% cream (Recommended)</span>", "value": "betameth"}, {"label": "Hydrocortisone 1 % cream", "value": "hc1"}, {"label": "Protopic 0.1 % ointment", "value": "protopic"}, {"label": "Eucrisa 2 % ointment", "value": "e<PERSON><PERSON>a"}, {"label": "Zoryve 0.15 % cream (daily)", "value": "zoryve_daily"}, {"label": "Other (type below)", "value": "other"}], "widget": "html5", "validate": {"required": true}, "customConditional": "show = data.body_areas && data.body_areas.trunk_limbs === true && data.steroid_free_interest === 'yes';"}, {"key": "trunk_limbs_plan_no", "tableView": true, "type": "radio", "label": "When this area flares, which medication will you use?", "values": [{"label": "<span style=\"color:#28a745;font-weight:bold;\">Betamethasone valerate 0.05&nbsp;% cream (Recommended)</span>", "value": "betameth"}, {"label": "Hydrocortisone 1 % cream", "value": "hc1"}, {"label": "Other (type below)", "value": "other"}], "widget": "html5", "validate": {"required": true}, "customConditional": "show = data.body_areas && data.body_areas.trunk_limbs === true && data.steroid_free_interest === 'no';"}, {"key": "trunk_limbs_plan_other", "tableView": true, "rows": 3, "type": "textarea", "label": "Describe your flare-time treatment:", "customConditional": "show = (data.trunk_limbs_plan_yes === 'other' || data.trunk_limbs_plan_no === 'other');"}, {"key": "trunk_limbs_maint_yes", "tableView": true, "type": "radio", "label": "After the skin is calm, which product will you use to keep this area clear?", "values": [{"label": "<span style=\"color:#28a745;font-weight:bold;\">Protopic 0.1 % ointment (weekends only)</span>", "value": "protopic_weekend"}, {"label": "Betamethasone valerate 0.05 % cream (weekends only)", "value": "betameth_weekend"}, {"label": "Eucrisa 2 % ointment (daily)", "value": "eucrisa_daily"}, {"label": "Zoryve 0.15 % cream (daily)", "value": "zoryve_daily"}, {"label": "None - I don't need maintenance", "value": "none"}, {"label": "Other (type below)", "value": "other"}], "widget": "html5", "validate": {"required": true}, "customConditional": "show = data.body_areas && data.body_areas.trunk_limbs === true && data.steroid_free_interest === 'yes';"}, {"key": "trunk_limbs_maint_no", "tableView": true, "type": "radio", "label": "After the skin is calm, which product will you use to keep this area clear?", "values": [{"label": "Betamethasone valerate 0.05 % cream (weekends only)", "value": "betameth_weekend"}, {"label": "None - I don't need maintenance", "value": "none"}, {"label": "Other (type below)", "value": "other"}], "widget": "html5", "validate": {"required": true}, "customConditional": "show = data.body_areas && data.body_areas.trunk_limbs === true && data.steroid_free_interest === 'no';"}, {"key": "trunk_limbs_maint_other", "tableView": true, "rows": 3, "type": "textarea", "label": "Describe your maintenance option:", "customConditional": "show = (data.trunk_limbs_maint_yes === 'other' || data.trunk_limbs_maint_no === 'other');"}, {"key": "heading_palms_soles", "html": "<br><h4>Palms &amp; Soles - Thick Skin</h4>", "type": "content", "input": false, "label": "Content", "customConditional": "show = data.body_areas && data.body_areas.palms_soles === true;"}, {"key": "palms_soles_plan_yes", "type": "radio", "label": "When this area flares, which medication will you use?", "tableView": true, "values": [{"label": "<span style=\"color:#28a745;font-weight:bold;\">Clobetasol 0.05&nbsp;% ointment</span>", "value": "clobetasol"}, {"label": "Betamethasone valerate 0.05 % cream", "value": "betameth"}, {"label": "Eucrisa 2 % ointment", "value": "e<PERSON><PERSON>a"}, {"label": "Zoryve 0.15 % cream (daily)", "value": "zoryve_daily"}, {"label": "Zoryve 0.15 % cream (daily)", "value": "zoryve_daily"}, {"label": "Other (type below)", "value": "other"}], "widget": "html5", "validate": {"required": true}, "customConditional": "show = data.body_areas && data.body_areas.palms_soles === true && data.steroid_free_interest === 'yes';"}, {"key": "palms_soles_plan_no", "tableView": true, "type": "radio", "label": "When this area flares, which medication will you use?", "values": [{"label": "<span style=\"color:#28a745;font-weight:bold;\">Protopic 0.1 % ointment (weekends only)</span>", "value": "protopic_weekend"}, {"label": "Clobetasol 0.05 % ointment", "value": "clobetasol"}, {"label": "Betamethasone valerate 0.05 % cream", "value": "betameth"}, {"label": "Other (type below)", "value": "other"}], "widget": "html5", "validate": {"required": true}, "customConditional": "show = data.body_areas && data.body_areas.palms_soles === true && data.steroid_free_interest === 'no';"}, {"key": "palms_soles_plan_other", "tableView": true, "rows": 3, "type": "textarea", "label": "Describe your flare-time treatment:", "customConditional": "show = (data.palms_soles_plan_yes === 'other' || data.palms_soles_plan_no === 'other');"}, {"key": "palms_soles_maint_yes", "tableView": true, "type": "radio", "label": "After the skin is calm, which product will you use to keep this area clear?", "values": [{"label": "<span style=\"color:#28a745;font-weight:bold;\">Protopic 0.1 % ointment (weekends only)</span>", "value": "protopic_weekend"}, {"label": "Betamethasone valerate 0.05 % cream (weekends only)", "value": "betameth_weekend"}, {"label": "Eucrisa 2 % ointment (daily)", "value": "eucrisa_daily"}, {"label": "Zoryve 0.15 % cream (daily)", "value": "zoryve_daily"}, {"label": "Barrier cream only (daily, over-the-counter)", "value": "barrier"}, {"label": "None - I don't need maintenance", "value": "none"}, {"label": "Other (type below)", "value": "other"}], "widget": "html5", "validate": {"required": true}, "customConditional": "show = data.body_areas && data.body_areas.palms_soles === true && data.steroid_free_interest === 'yes';"}, {"key": "palms_soles_maint_no", "tableView": true, "type": "radio", "label": "After the skin is calm, which product will you use to keep this area clear?", "values": [{"label": "<span style=\"color:#28a745;font-weight:bold;\">Protopic 0.1 % ointment (weekends only)</span>", "value": "protopic_weekend"}, {"label": "Betamethasone valerate 0.05 % cream (weekends only)", "value": "betameth_weekend"}, {"label": "Barrier cream only (daily, over-the-counter)", "value": "barrier"}, {"label": "None - I don't need maintenance", "value": "none"}, {"label": "Other (type below)", "value": "other"}], "widget": "html5", "validate": {"required": true}, "customConditional": "show = data.body_areas && data.body_areas.palms_soles === true && data.steroid_free_interest === 'no';"}, {"key": "palms_soles_maint_other", "tableView": true, "rows": 3, "type": "textarea", "label": "Describe your maintenance option:", "customConditional": "show = (data.palms_soles_maint_yes === 'other' || data.palms_soles_maint_no === 'other');"}, {"key": "heading_scalp", "html": "<br><h4><PERSON><PERSON><PERSON> (Hair-Bearing Skin)</h4><p>Choose a product <strong>during a flare</strong>, then pick what you'll use <strong>to stay clear</strong>.</p>", "type": "content", "input": false, "label": "Content", "customConditional": "show = data.body_areas && data.body_areas.scalp === true;"}, {"key": "scalp_plan_yes", "tableView": true, "type": "radio", "label": "When your scalp is inflamed, which medication will you use?", "values": [{"label": "<span style=\"color:#28a745;font-weight:bold;\">Mometasone furoate 0.1&nbsp;% lotion</span>", "value": "mometasone"}, {"label": "Betamethasone valerate 0.1 % lotion", "value": "betameth_lotion"}, {"label": "Eucrisa 2 % ointment", "value": "e<PERSON><PERSON>a"}, {"label": "Protopic 0.1 % ointment", "value": "protopic"}, {"label": "Zoryve 0.15 % cream (daily)", "value": "zoryve_daily"}, {"label": "Other (type below)", "value": "other"}], "widget": "html5", "validate": {"required": true}, "customConditional": "show = data.body_areas && data.body_areas.scalp === true && data.steroid_free_interest === 'yes';"}, {"key": "scalp_plan_no", "tableView": true, "type": "radio", "label": "When your scalp is inflamed, which medication will you use?", "values": [{"label": "<span style=\"color:#28a745;font-weight:bold;\">Mometasone furoate 0.1&nbsp;% lotion</span>", "value": "mometasone"}, {"label": "Betamethasone valerate 0.1 % lotion", "value": "betameth_lotion"}, {"label": "Other (type below)", "value": "other"}], "widget": "html5", "validate": {"required": true}, "customConditional": "show = data.body_areas && data.body_areas.scalp === true && data.steroid_free_interest === 'no';"}, {"key": "scalp_plan_other", "tableView": true, "rows": 3, "type": "textarea", "label": "Describe your flare-time treatment:", "customConditional": "show = (data.scalp_plan_yes === 'other' || data.scalp_plan_no === 'other');"}, {"key": "scalp_maint_yes", "tableView": true, "type": "radio", "label": "After the flare settles, which product will you use to keep your scalp clear?", "values": [{"label": "<span style=\"color:#28a745;font-weight:bold;\">Mometasone furoate 0.1&nbsp;% lotion (weekends only)</span>", "value": "mometasone_weekend"}, {"label": "Betamethasone valerate 0.1 % lotion (weekends only)", "value": "betameth_weekend"}, {"label": "Eucrisa 2 % ointment (daily)", "value": "eucrisa_daily"}, {"label": "Zoryve 0.15 % cream (daily)", "value": "zoryve_daily"}, {"label": "None - I don't need maintenance", "value": "none"}, {"label": "Other (type below)", "value": "other"}], "widget": "html5", "validate": {"required": true}, "customConditional": "show = data.body_areas && data.body_areas.scalp === true && data.steroid_free_interest === 'yes';"}, {"key": "scalp_maint_no", "tableView": true, "type": "radio", "label": "After the flare settles, which product will you use to keep your scalp clear?", "values": [{"label": "<span style=\"color:#28a745;font-weight:bold;\">Betamethasone valerate 0.1&nbsp;% lotion (weekends only)</span>", "value": "betameth_weekend"}, {"label": "Mometasone furoate 0.1 % lotion (weekends only)", "value": "mometasone_weekend"}, {"label": "None - I don't need maintenance", "value": "none"}, {"label": "Other (type below)", "value": "other"}], "widget": "html5", "validate": {"required": true}, "customConditional": "show = data.body_areas && data.body_areas.scalp === true && data.steroid_free_interest === 'no';"}, {"key": "scalp_maint_other", "tableView": true, "rows": 3, "type": "textarea", "label": "Describe your maintenance option:", "customConditional": "show = (data.scalp_maint_yes === 'other' || data.scalp_maint_no === 'other');"}, {"key": "heading_ear_canal", "html": "<br><h4>Ear Canal (Otitis externa / Eczema)</h4>", "type": "content", "input": false, "label": "Content", "customConditional": "show = data.body_areas && data.body_areas.ear_canal === true;"}, {"key": "ear_canal_plan", "tableView": true, "type": "radio", "label": "During a flare, which medication will you use inside the ear canal?", "values": [{"label": "<span style=\"color:#28a745;font-weight:bold;\">DermOtic® oil drops (fluocinolone 0.01&nbsp;%)</span>", "value": "dermotic"}, {"label": "Hydrocortisone 1 % lotion", "value": "hc1_lotion"}, {"label": "Other (type below)", "value": "other"}], "widget": "html5", "validate": {"required": true}, "customConditional": "show = data.body_areas && data.body_areas.ear_canal === true;"}, {"key": "ear_canal_plan_other", "tableView": true, "rows": 3, "type": "textarea", "label": "Describe your flare-time treatment:", "customConditional": "show = data.ear_canal_plan === 'other';"}, {"key": "ear_canal_maint_yes", "tableView": true, "type": "radio", "label": "Once calm, which product will you use for maintenance in the ear canal?", "values": [{"label": "Protopic 0.1 % ointment (weekends only)", "value": "protopic_weekend"}, {"label": "Eucrisa 2 % ointment (daily)", "value": "eucrisa_daily"}, {"label": "Zoryve 0.15 % cream (daily)", "value": "zoryve_daily"}, {"label": "None - I don't need maintenance", "value": "none"}, {"label": "Other (type below)", "value": "other"}], "widget": "html5", "validate": {"required": true}, "customConditional": "show = data.body_areas && data.body_areas.ear_canal === true && data.steroid_free_interest === 'yes';"}, {"key": "ear_canal_maint_no", "tableView": true, "type": "radio", "label": "Once calm, which product will you use for maintenance in the ear canal?", "values": [{"label": "None - I don't need maintenance", "value": "none"}, {"label": "Other (type below)", "value": "other"}], "widget": "html5", "validate": {"required": true}, "customConditional": "show = data.body_areas && data.body_areas.ear_canal === true && data.steroid_free_interest === 'no';"}, {"key": "ear_canal_maint_other", "tableView": true, "rows": 3, "type": "textarea", "label": "Describe your maintenance option:", "customConditional": "show = (data.ear_canal_maint_yes === 'other' || data.ear_canal_maint_no === 'other');"}, {"key": "regimen_summary_notice", "html": "<br><h4>Here's a summary of your treatment regimen</h4><p>Don't worry—full instructions and pharmacy directions will be waiting for you in your TeleTest portal.</p>", "type": "content", "input": false, "label": "Content", "tableView": false}, {"key": "eczema_flare_table", "tableView": true, "type": "datagrid", "input": true, "label": "🔥 Flare-time treatments", "displayAsTable": true, "reorder": false, "disabled": true, "addAnother": false, "components": [{"key": "site", "rows": 2, "type": "textarea", "label": "Application site(s)", "disabled": true, "tableView": true}, {"key": "med", "rows": 2, "type": "textarea", "label": "Medication", "disabled": true, "tableView": true}, {"key": "frequency", "rows": 2, "type": "textarea", "label": "Frequency / duration", "disabled": true, "tableView": true}], "customClass": "flaretbl", "calculateValue": "value = (() => {\n    /* ───── display names ───── */\n    const NAME = {\n      hc1             : 'Hydrocortisone 1 % cream',\n      hc_protopic     : 'Hydrocortisone → Protopic 0.1 %',\n      protopic        : 'Protopic 0.1 % ointment',\n      eucrisa         : 'Eucrisa 2 % ointment',\n      zoryve_daily    : 'Zoryve 0.15 % cream',\n      betameth        : 'Betamethasone valerate 0.05 % cream',\n      clobetasol      : 'Clobetasol 0.05 % ointment',\n      betameth_lotion : 'Betamethasone valerate 0.1 % lotion',\n      mometasone      : 'Mometasone furoate 0.1 % lotion',\n      dermotic        : 'DermOtic oil drops',\n      hc1_lotion      : 'Hydrocortisone 1 % lotion'\n    };\n\n    /* ───── default flare courses ───── */\n    const FREQ = {\n      hc1             : 'Apply twice daily for 7 - 10 days',\n      hc_protopic     : 'HC twice daily ×10 d, then Protopic twice daily (≤12 w)',\n      protopic        : 'Apply twice daily (maximum 12 w)',\n      eucrisa         : 'Thin layer twice daily',\n      zoryve_daily    : 'Thin layer twice daily',\n      betameth        : 'Apply twice daily for 7 - 14 days',\n      clobetasol      : 'Apply twice daily for 5 - 10 days',\n      betameth_lotion : 'Apply once or twice daily for ≤2 w',\n      mometasone      : 'Apply once daily for ≤2 w',\n      dermotic        : '2-5 drops twice daily × 7 d',\n      hc1_lotion      : 'Apply 2 drops twice daily × 7 d'\n    };\n\n    const pick = (yes,no) => yes || no || '';\n    const add  = (map,site,code) => {\n      if(!code) return;\n      if(!map.has(code)) map.set(code,{sites:new Set(),freq:FREQ[code]||'Use as directed'});\n      map.get(code).sites.add(site);\n    };\n\n    const a = data.body_areas || {}; const m = new Map();\n    if(a.face_groin)  add(m,'Face / Groin',  pick(data.face_groin_plan_yes,  data.face_groin_plan_no));\n    if(a.eyelids)     add(m,'Eyelids',       pick(data.eyelids_plan_yes,     data.eyelids_plan_no));\n    if(a.neck_folds)  add(m,'Neck / Folds',  pick(data.neck_folds_plan_yes,  data.neck_folds_plan_no));\n    if(a.trunk_limbs) add(m,'Trunk / Limbs', pick(data.trunk_limbs_plan_yes, data.trunk_limbs_plan_no));\n    if(a.palms_soles) add(m,'Palms / Soles', pick(data.palms_soles_plan_yes, data.palms_soles_plan_no));\n    if(a.scalp)       add(m,'Scalp',         pick(data.scalp_plan_yes,       data.scalp_plan_no));\n    if(a.ear_canal)   add(m,'Ear canal',     data.ear_canal_plan);\n\n    return Array.from(m.entries()).map(([c,o]) => ({\n      med : NAME[c] || c,\n      site: Array.from(o.sites).join(', '),\n      frequency: o.freq\n    }));\n  })()"}, {"key": "eczema_maintenance_table", "type": "datagrid", "input": true, "label": "🛡️ Maintenance treatments", "reorder": false, "disabled": true, "addAnother": false, "components": [{"key": "site", "rows": 2, "type": "textarea", "label": "Application site(s)", "disabled": true, "tableView": true}, {"key": "med", "rows": 2, "type": "textarea", "label": "Medication", "disabled": true, "tableView": true}, {"key": "frequency", "rows": 2, "type": "textarea", "label": "Frequency / duration", "disabled": true, "tableView": true}], "customClass": "flaretbl", "calculateValue": "value = (() => {\n    /* ───── display names ───── */\n    const NAME = {\n      protopic_weekend   : 'Protopic 0.1 % ointment (weekends)',\n      eucrisa_daily      : 'Eucrisa 2 % ointment (daily)',\n      zoryve_daily       : 'Zoryve 0.15 % cream (daily)',\n      betameth_weekend   : 'Betamethasone valerate 0.05 % cream (weekends)',\n      betameth_lotion    : 'Betamethasone valerate 0.1 % lotion (weekends)',\n      mometasone_weekend : 'Mometasone furoate 0.1 % lotion (weekends)',\n      shampoo            : 'Medicated shampoo',\n      barrier            : 'Over-the-counter barrier cream'\n    };\n\n    /* ───── default maintenance schedules ───── */\n    const FREQ = {\n      protopic_weekend   : 'Thin layer twice on Sat & Sun',\n      eucrisa_daily      : 'Thin layer once daily',\n      zoryve_daily       : 'Thin layer once daily',\n      betameth_weekend   : 'Apply twice on Sat & Sun',\n      betameth_lotion    : 'Apply twice on Sat & Sun',\n      mometasone_weekend : 'Apply twice on Sat & Sun',\n      shampoo            : 'Use 2 - 3× per week',\n      barrier            : 'Apply once daily'\n    };\n\n    const pick = (yes,no) => yes || no || '';\n    const SKIP = ['none'];\n    const add  = (map,site,code) => {\n      if(!code || SKIP.includes(code)) return;\n      if(!map.has(code)) map.set(code,{sites:new Set(),freq:FREQ[code]||'Use as directed'});\n      map.get(code).sites.add(site);\n    };\n\n    const a = data.body_areas || {}; const m = new Map();\n    if(a.face_groin)  add(m,'Face / Groin',  pick(data.face_groin_maint_yes,  data.face_groin_maint_no));\n    if(a.eyelids)     add(m,'Eyelids',       pick(data.eyelids_maint_yes,     data.eyelids_maint_no));\n    if(a.neck_folds)  add(m,'Neck / Folds',  pick(data.neck_folds_maint_yes,  data.neck_folds_maint_no));\n    if(a.trunk_limbs) add(m,'Trunk / Limbs', pick(data.trunk_limbs_maint_yes, data.trunk_limbs_maint_no));\n    if(a.palms_soles) add(m,'Palms / Soles', pick(data.palms_soles_maint_yes, data.palms_soles_maint_no));\n    if(a.scalp)       add(m,'Scalp',         pick(data.scalp_maint_yes,       data.scalp_maint_no));\n    if(a.ear_canal)   add(m,'Ear canal',     pick(data.ear_canal_maint_yes,   data.ear_canal_maint_no));\n\n    /* fallback row if user picked “None” everywhere */\n    if(m.size === 0) return [{ med:'None', site:'—', frequency:'—' }];\n\n    return Array.from(m.entries()).map(([c,o]) => ({\n      med : NAME[c] || c,\n      site: Array.from(o.sites).join(', '),\n      frequency: o.freq\n    }));\n  })()"}, {"key": "intake_kind", "type": "textfield", "input": true, "label": "Intake Kind", "hidden": true, "disabled": true, "tableView": true, "clearOnHide": false, "calculateValue": "value = !data.intake_denial_reasons?.length ? 'mail' : 'chat';"}, {"key": "intake_mt_key", "type": "textfield", "input": true, "label": "Intake Mail Template Key", "hidden": true, "disabled": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.intake_kind=='mail'?'intake-eczema':'intake-denial'"}, {"key": "allowed_rt_keys", "type": "textfield", "input": true, "label": "Allowed Rep<PERSON>", "hidden": true, "disabled": true, "multiple": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.intake_mt_key=='intake-denial'?[]:['get-rx','appointment-intake','edit-intake']"}]}