{"components": [{"key": "heading_sti_screening", "html": "<h5><strong>STI Screening</strong></h5>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.sku && _.includes(['fem_pn_vswb', 'fem_pn_uti', 'std_pn_uti'], data.sku);"}, {"key": "onset_partner_sti", "type": "radio", "input": true, "label": "Did your symptoms start after a new sexual partner, or are you concerned about partner infidelity (i.e. your partner might have other partners)?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}, {"label": "I don't understand this question", "value": "doesn't_understand", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.sku && _.includes(['fem_pn_vswb', 'fem_pn_uti', 'std_pn_uti'], data.sku);", "optionsLabelPosition": "right"}, {"key": "request_sti_testing", "type": "radio", "input": true, "label": "Would you like to add STI testing to your screening today?", "inline": false, "values": [{"label": "Yes, I'd like to add urine testing (i.e. gonorrhea, chlamydia, etc) <strong> and bloodwork (HIV and Syphilis)</strong>.", "value": "add_urine_bloodwork_sti", "shortcut": ""}, {"label": "Yes, I'd like to add urine testing  (i.e. gonorrhea, chlamydia, etc) <strong> but no bloodwork (HIV and Syphilis)</strong>.", "value": "add_urine_sti", "shortcut": ""}, {"label": "No, I do not want to add STI testing.", "value": "no", "shortcut": ""}, {"label": "I don't understand this question", "value": "needs_clarification", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.sku && _.includes(['fem_pn_vswb', 'fem_pn_uti', 'std_pn_uti'], data.sku) && data.onset_partner_sti != false;", "optionsLabelPosition": "right"}, {"key": "show_all", "type": "textfield", "input": true, "hidden": true, "disabled": true, "multiple": false, "tableView": false, "clearOnHide": false, "defaultValue": true, "calculateValue": "value = data.sku && (!_.includes(['fem_pn_vswb', 'fem_pn_uti', 'std_pn_uti'], data.sku) || _.includes(['add_urine_bloodwork_sti', 'add_urine_sti'], data.request_sti_testing));", "refreshOnChange": true}, {"key": "content4", "html": "<h2><strong>Instructions</strong></h2><p>To expedite your care, please answer the following questions to the best of your abilities. &nbsp;We ask these questions to provide the best possible care.&nbsp;</p><ol><li>If you prefer to <strong>keep your answers anonymous</strong>, select <strong>'prefer not to disclose'</strong>.</li><li>If you have any questions, please list them in the space provided.</li></ol>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.show_all;"}, {"key": "purpose_of_consultation", "html": "<h2 style=\"text-align:center;\"><strong>Purpose of Consultation</strong></h2>", "type": "content", "input": false, "label": "Purpose of Consultation", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.show_all;"}, {"key": "testing_indication", "type": "selectboxes", "input": true, "label": "Please select your reason(s) for testing:", "values": [{"label": "New Partner Exposure", "value": "new_partner_exposure", "shortcut": ""}, {"label": "Concern About Partner Infidelity ", "value": "concern_about_partner_infidelity", "shortcut": ""}, {"label": "No Testing Since My Last Partner", "value": "no_testing_since_last_partner", "shortcut": ""}, {"label": "Partner Notifed Me of A Positive Result", "value": "notified_of_a_positive_result", "shortcut": ""}, {"label": "Open Relationship/Lifestyle", "value": "open_relationship", "shortcut": ""}, {"label": "My Last Test Was Done Before The End of My Window Period", "value": "early_test_window_period", "shortcut": ""}, {"label": "Never had STI Testing", "value": "never_had_testing", "shortcut": ""}, {"label": "Adult Film / Sex Work & No Testing Since Last Exposure", "value": "adult_film_sex_work_no_testing", "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "confirm_label": "Reason(s) for testing:", "customConditional": "show = data.show_all;", "optionsLabelPosition": "right"}, {"key": "stated_reason_for_testing", "type": "textarea", "input": true, "label": "Please state your “other” reason(s) for testing:", "adminFlag": true, "tableView": true, "autoExpand": false, "customConditional": "show = data.show_all && data.testing_indication && data.testing_indication.other;"}, {"key": "chief_complaint", "type": "radio", "input": true, "label": "I am interested in the following:", "inline": false, "values": [{"label": "STI Testing", "value": "sti_testing", "shortcut": ""}, {"label": "Testing and Treatment", "value": "testing_and_treatment", "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "defaultValue": "sti_testing", "confirm_label": "Interested in:", "customConditional": "show = data.show_all;", "optionsLabelPosition": "right"}, {"key": "stated_reason_for_interested_in_other", "type": "textarea", "input": true, "label": "As you selected other, please specify your reason below:", "validate": {"required": true}, "tableView": true, "autoExpand": false, "customConditional": "show = data.show_all && data.chief_complaint == 'other';"}, {"key": "content6", "html": "<h2 style=\"text-align:center;\"><strong>Intake History</strong></h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.show_all;"}, {"key": "last_sti_test", "type": "radio", "input": true, "label": "When was your last test for STIs outside of using TeleTest?", "inline": false, "values": [{"label": "Never", "value": "never", "shortcut": ""}, {"label": "1-14 days ago", "value": "1-14_days", "shortcut": ""}, {"label": "15-30 days ago", "value": "15-30_days", "shortcut": ""}, {"label": "31-90 days ago", "value": "31-90_days", "shortcut": ""}, {"label": "90+ days ago", "value": "90+_days", "shortcut": ""}, {"label": "Prefer not to disclose", "value": "prefer_not_to_disclose", "shortcut": ""}], "tableView": true, "confirm_label": "Last STI test:", "customConditional": "show = data.show_all;", "optionsLabelPosition": "right"}, {"key": "past_std", "type": "radio", "input": true, "label": "Have you tested positive or been treated for syphilis, HIV, or Hepatitis C in the past?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}, {"label": "Prefer not to disclose", "value": "prefer_not_to_disclose", "shortcut": ""}, {"label": "Doesn't apply to me as I haven't been tested before", "value": "N/A", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.show_all;", "optionsLabelPosition": "right"}, {"key": "previous_syphilis_positive", "type": "radio", "input": true, "label": "Have you tested positive for syphilis in the past?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}, {"label": "Prefer not to disclose", "value": "prefer_not_to_disclose", "shortcut": ""}, {"label": "Doesn't apply to me as I haven't been tested before", "value": "N/A", "shortcut": ""}, {"label": "I don't understand this question", "value": "did_not_understand", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.past_std == true;", "optionsLabelPosition": "right"}, {"key": "content9", "html": "<p>After testing positive for syphilis, your blood test will <strong>always</strong> show a positive result.<br/>Syphilis antibody levels will remain positive throughout your life even if you are treated and cured. &nbsp;Physicians will monitor your <strong>“RPR Titre”</strong> to determine treatment response. After successful treatment, we expect a reduction in your titre level.<br/>For example, you might start out as 1:32 with a new infection, then drop to 1:16, then 1:4. A rise in the titre indicates you have a new infection.</p><p>If you have had syphilis and treatment within the last year, appropriate testing intervals are <strong>3, 6</strong>, & <strong>12 months</strong> from the date of treatment.</p><p>If you have had syphilis for <strong>&gt;1 year</strong>, appropriate testing intervals are <strong>12</strong> & <strong>24 months</strong> from the date of treatment.</p><p>If you have had <strong>syphilis involving your brain</strong>, appropriate monitoring intervals are at <strong>6, 12 and 24 months</strong> from the date of treatment.</p><em>Earlier testing is important if you are concerned about new exposure to syphilis.</em>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": true, "customConditional": "show = data.previous_syphilis_positive == true;"}, {"key": "previous_syphilis_treatment", "type": "radio", "input": true, "label": "Have you been treated for syphilis in the past?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}, {"label": "I don't understand this question", "value": "did_not_understand", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.previous_syphilis_positive == true;", "optionsLabelPosition": "right"}, {"key": "prior_syphilis_medication", "type": "radio", "input": true, "label": "What were you last treated with?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}, {"label": "I don't understand this question", "value": "did_not_understand", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.previous_syphilis_positive == true;", "optionsLabelPosition": "right"}, {"key": "monitoring_syphilis_levels", "type": "radio", "input": true, "label": "Are you testing today to monitor your syphilis titre level?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}, {"label": "I don't understand this question ", "value": "did_not_understand", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.previous_syphilis_positive == true;", "optionsLabelPosition": "right"}, {"key": "understands_syphilis_follow_up", "type": "radio", "input": true, "label": "Regarding follow-up of syphilis testing, I have read and understood appropriate timeline for testing:", "inline": false, "values": [{"label": "I have read and understood timeline for testing", "value": true, "shortcut": ""}, {"label": "I require further clarification about when to do repeat testing", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.monitoring_syphilis_levels == true;", "optionsLabelPosition": "right"}, {"key": "previous_hiv_positive", "type": "radio", "input": true, "label": "Have you tested positive for HIV in the past?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}, {"label": "Prefer not to disclose", "value": "prefer_not_to_disclose", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.past_std == true;", "optionsLabelPosition": "right"}, {"key": "understands_repeat_hiv_testing_is_unnecessary", "type": "radio", "input": true, "label": "If you have tested positive for HIV in the past, repeat testing for HIV is not medically appropriate.", "inline": false, "values": [{"label": "I understand", "value": true, "shortcut": ""}, {"label": "I do not understand", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.show_all && data.past_std != false && data.previous_hiv_positive != false;", "optionsLabelPosition": "right"}, {"key": "unprotected_sex", "type": "radio", "input": true, "label": "Have you had vaginal or anal sex without protection (i.e. condom) within the last 3 months?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}, {"label": "Prefer not to disclose", "value": "prefer_not_to_disclose", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.show_all;", "optionsLabelPosition": "right"}, {"key": "number_of_recent_sexual_partners", "type": "radio", "input": true, "label": "How many sexual partners have you had within the last 6 months?", "inline": false, "values": [{"label": "Prefer not to disclose", "value": "prefer_not_to_disclose", "shortcut": ""}, {"label": "1", "value": "1", "shortcut": ""}, {"label": "2-3", "value": "2-3", "shortcut": ""}, {"label": "4-9", "value": "4-9", "shortcut": ""}, {"label": "10+", "value": "10+", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.show_all;", "optionsLabelPosition": "right"}, {"key": "partner_sex", "type": "selectboxes", "input": true, "label": "Please select the sex(s) of your sexual partners:", "values": [{"label": "Male", "value": "male", "shortcut": ""}, {"label": "Female", "value": "female", "shortcut": ""}, {"label": "Trans", "value": "trans", "shortcut": ""}, {"label": "Prefer not to disclose", "value": "prefer_not_to_disclose", "shortcut": ""}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "confirm_label": "Partner sex(s):", "customConditional": "show = data.show_all;", "optionsLabelPosition": "right"}, {"key": "type_of_sex", "type": "selectboxes", "input": true, "label": "Please select your route of exposure (i.e. where your body was exposed to another person's genitals). This helps the physician know what medical tests are appropriate for your care.", "values": [{"label": "Prefer not to disclose", "value": "prefer_not_to_disclose", "shortcut": ""}, {"label": "Perform Vaginal Sex (my genitals on partner's vagina) ", "value": "perform_vaginal", "shortcut": "", "customConditional": "show = data.partner_sex && (data.partner_sex.female || data.partner_sex.trans || data.partner_sex.prefer_not_to_disclose);"}, {"label": "Perform Oral Sex (my mouth on partner's genitals)", "value": "perform_oral", "shortcut": ""}, {"label": "Perform Anal Sex (my penis in partner's anus)", "value": "perform_anal", "shortcut": "", "customConditional": "show = data.sex != 'female';"}, {"label": "Receive Vaginal Sex (partner's genitals on my vagina)", "value": "receive_vaginal", "shortcut": "", "customConditional": "show = data.sex != 'male';"}, {"label": "Receive Anal Sex (partner's penis in my anus)", "value": "receive_anal", "shortcut": "", "customConditional": "show = data.partner_sex && (data.partner_sex.male || data.partner_sex.trans || data.partner_sex.prefer_not_to_disclose);"}, {"label": "Receive Oral Sex (partner's mouth on my genitals)", "value": "receive_oral", "shortcut": ""}, {"label": "Use sex toys", "value": "sex_toys", "shortcut": ""}, {"label": "I don't understand this question", "value": "did_not_understand", "shortcut": ""}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "confirm_label": "Route of exposure:", "customConditional": "show = data.show_all;", "optionsLabelPosition": "right"}, {"key": "only_performs_oral_recieves_anal", "type": "textfield", "input": true, "label": "Only Performs Oral Recieves Anal", "hidden": true, "disabled": true, "multiple": false, "redrawOn": "type_of_sex", "tableView": false, "clearOnHide": false, "calculateValue": "value = _.reduce(['perform_oral', 'receive_anal'], (r1,x)=>{return r1 || _.reduce(data.type_of_sex, (r,v,k)=>{return r && ((k == x) ? v : !v)}, true)}, false);", "refreshOnChange": true}, {"key": "remove_urine_testing_type_of_sex", "type": "radio", "input": true, "label": "You indicated the only exposure you had since your last test was <strong> performing oral sex or recieve anal sex</strong>. Urine STI testing is not required as it is only performed if you you have exposure to your genitals. It will be removed from your test requistion as it is not medically required.", "inline": false, "values": [{"label": "I understand", "value": true, "shortcut": ""}, {"label": "I do not understand", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.show_all && !!data.only_performs_oral_recieves_anal", "optionsLabelPosition": "right"}, {"key": "concerned_about_known_exposure", "type": "radio", "input": true, "label": "Are you concerned about exposure to someone you think or know has a sexually transmitted infection? (A confirmed exposure means the partner was tested and confirmed positive by a doctor or blood/urine test)", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}, {"label": "Prefer not to disclose", "value": "prefer_not_to_disclose", "shortcut": ""}, {"label": "I don't understand this question", "value": "did_not_understand", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.show_all;", "optionsLabelPosition": "right"}, {"key": "list_of_confirmed_exposures", "type": "selectboxes", "input": true, "label": "Please report any confirmed exposures (i.e. partner notified you of a confirmed or suspected infection they had):", "values": [{"label": "Chlamydia", "value": "chlamydia", "shortcut": ""}, {"label": "Gonorrhea", "value": "gonorrhea", "shortcut": ""}, {"label": "Trichomonas", "value": "trichomonas", "shortcut": ""}, {"label": "HIV", "value": "hiv", "shortcut": ""}, {"label": "Syphilis", "value": "syphilis", "shortcut": ""}, {"label": "Herpes", "value": "herpes", "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}, {"label": "Doesn't apply to me", "value": "N/A", "shortcut": ""}, {"label": "I don't understand this question", "value": "did_not_understand", "shortcut": ""}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "customConditional": "show = data.show_all && data.concerned_about_known_exposure != false;", "optionsLabelPosition": "right"}, {"key": "other_sti_exposure_reason", "type": "textarea", "input": true, "label": "Please list your concerns about “other” STI exposures here:", "tableView": true, "autoExpand": false, "customConditional": "show = data.list_of_confirmed_exposures && data.list_of_confirmed_exposures.other;"}, {"key": "content5", "html": "<h2>Pregnancy</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": true, "customConditional": "show = data.sex == 'female';"}, {"key": "currently_pregnant", "type": "radio", "input": true, "label": "Are you currently pregnant?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}, {"label": "I don't know", "value": "i_dont_know", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.show_all && data.sex == 'female';", "optionsLabelPosition": "right"}, {"key": "vaginal_sex_without_contraception", "type": "radio", "input": true, "label": "Have you had unprotected vaginal sex with a male partner in the last 5 days, and are not on any form of contraception (i.e. Birth  Control Pills, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, IUD, tubes tied, partner has vasectomy)?", "inline": false, "values": [{"label": "Prefer not to disclose", "value": "prefer_not_to_disclose", "shortcut": ""}, {"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}, {"label": "I don't understand this question", "value": "did_not_understand", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.sex == 'female' && data.unprotected_sex == true && (['1-7_days', '8-14_days', '15-30_days'].indexOf(data.last_sex) > -1);", "optionsLabelPosition": "right"}, {"key": "pregnancy_advice", "html": "<h2>Pregnancy Advice</h2><p>Having unprotected sex with a male partner without a condom can put you at risk of pregnancy. &nbsp;</p><p><strong>Plan B,</strong> a medication to prevent unwanted pregnancy after recent sexual contact, is effective <strong>up to 5 days</strong> after unprotected vaginal sex. &nbsp;</p><p>If you have questions about Plan B, please bring discuss it with your physician at the time of your appointment.</p>", "type": "content", "input": false, "label": "Content", "tableView": true, "refreshOnChange": true, "customConditional": "show = data.vaginal_sex_without_contraception == true;"}, {"key": "hepatitis_heading", "html": "<h2 style=\"text-align:center;\"><strong>Hepatitis Intake History</strong></h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.show_all;"}, {"key": "tested_for_hep_c", "type": "radio", "input": true, "label": "Have you been tested for Hepatitis C in the past?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No/Don't know", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.show_all;", "optionsLabelPosition": "right"}, {"key": "no_hep_b_c_birth_country_risk", "type": "radio", "input": true, "label": "Were you born in Canada, United States, United Kingdom, France, Portugal, Germany, The Netherlands, Jamaica, Mexico, Colombia, Guyana, Trinidad and Tobago, Algeria, Morocco, Lebanon, Israel, Iran, Hong Kong, India?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "defaultValue": true, "customConditional": "show = data.show_all;", "optionsLabelPosition": "right"}, {"key": "country_of_birth", "data": {"json": ["NOT ON THE LIST", "Albania", "Algeria", "Angola", "Azerbaijan", "Bangladesh", "Belarus", "Belize", "Benin", "Bhutan", "Brunei Darussalam", "Bulgaria", "Burkina Faso", "Burundi", "Cambodia", "Cameroon ", "Cape Verde", "Central African Republic", "Chad", "China", "Colombia", "Congo", "Côte D’Ivoire", "Cyprus", "De. Rep. Congo", "Djibouti", "Dominican Republic", "Ecuador", "Egypt", "El Salvador", "Equatorial Guinea", "Eritrea", "Estonia", "Ethiopia", "Fed. Sts. Micronesia", "Fiji", "Gabon", "Gambia", "Georgia", "Ghana", "Grenada", "Guinea", "Guinea-Bissau", "Haiti", "Hungary", "Indonesia", "Iraq", "Italy", "Jamaica", "Japan", "Jordan", "Kazakhstan", "Kenya", "Kiribati", "Kiribati", "Kosovo", "Kyrgyzstan", "Laos", "Latvia", "Liberia", "Libya", "Lithuania", "Macedonia FYR", "Madagascar", "Malawi", "Mali", "Marshall Islands", "Mauritania", "Micronesia", "Moldova", "Mongolia", "Mozambique", "Myanmar", "Namibia", "<PERSON><PERSON>", "New Zealand", "Niger", "Nigeria", "Niue", "Oman", "Pakistan", "<PERSON><PERSON>", "Papua New Guinea", "Peru", "Philippines", "Poland", "Romania", "Russia", "Rwanda", "Samoa", "São Tomé and Principe", "Saudi Arabia", "Senegal", "Sierra Leone", "Singapore", "Solomon Islands", "Somalia", "South Africa", "South Korea", "South Sudan", "Sri Lanka", "St. Kitts and Nevis", "Sudan", "Suriname", "Swaziland", "Syria ", "Tahiti", "Taiwan", "Tajikistan", "Tanzania", "Thailand", "Togo", "Tonga", "Tunisia", "Turkey", "Turkmenistan", "Tuvalu", "Uganda", "Ukraine", "Uzbekistan", "Vanuatu", "Vietnam", "Yemen", "Zambia", "Zimbabwe"]}, "type": "select", "input": true, "label": "Please select the country of your birth (or “NOT ON LIST”):", "widget": "html5", "dataSrc": "json", "tableView": true, "customConditional": "show = data.show_all && !data.no_hep_b_c_birth_country_risk;"}, {"key": "hep_b_vaccinated", "type": "radio", "input": true, "label": "Have you been fully vaccinated for Hepatitis B? (Hepatitis B vaccination is given in many countries at birth and in school in Ontario)", "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No/Don't know", "value": false, "shortcut": ""}], "tableView": true, "confirm_label": "Fully vaccinated for Hepatitis B:", "customConditional": "show = data.show_all;", "optionsLabelPosition": "right"}, {"key": "hep_b_vaccine_advice", "type": "radio", "input": true, "label": "<p>Please review your immunization records to confirm your vaccination status. &nbsp;</p><p>If you cannot confirm you have recieved the vaccination, or have never had Hepatitis B vaccination, it is strongly recommended.  Vaccination is free for the following individuals though health units and walk-in clinics: anyone with a history of an STI, you are a household or sexual contact of a confirmed case of Hepatitis B, you have a history of IV drug use, you were born to a mother with hepatitis B, you have liver disease, you are a man who has sex with men, you have multiple sex partners, you have had a needlestick injury in a non-healthcare setting, you requiring frequent blood products.</p>", "values": [{"label": "I understand", "value": true, "shortcut": ""}, {"label": "I do not understand", "value": false, "shortcut": ""}], "tableView": true, "refreshOnChange": true, "customConditional": "show = data.show_all && data.hep_b_vaccinated === false;"}, {"key": "previous_hep_b_test", "type": "radio", "input": true, "label": "Have you previously been tested for Hepatitis B?", "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "tableView": true, "customConditional": "show = !!data.hep_b_vaccinated;", "optionsLabelPosition": "right"}, {"key": "hep_b_risk_factors", "type": "selectboxes", "input": true, "label": "Do you have any of the following risk factors for Hepatitis B?", "values": [{"label": "I require immunosuppressive therapy", "value": "immunosuppressive_therapy", "shortcut": ""}, {"label": "I am a donor of plasma, semen, organs, or tissue", "value": "tissue_donor", "shortcut": ""}, {"label": "I was born to a mother with Hepatitis B", "value": "hep_b_mother", "shortcut": ""}, {"label": "I am from an area where there is an intermediate (>2%) or higher risk of prevalence", "value": "high_risk_area", "shortcut": ""}], "inputType": "checkbox", "tableView": true, "customConditional": "show = !data.previous_hep_b_test && !!data.hep_b_vaccinated;", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_hep_b_risk_factors", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a risk factor."}, "validate": {"custom": "valid = !!data.none_of_the_above_hep_b_risk_factors || _.some(_.values(data.hep_b_risk_factors));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = !data.previous_hep_b_test && !!data.hep_b_vaccinated;"}, {"key": "hep_c_positive", "type": "radio", "input": true, "label": "Have you tested positive or been treated for Hepatitis C?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "tableView": true, "customConditional": "show = data.past_std == true;", "optionsLabelPosition": "right"}, {"key": "hepatitis_risk_factors", "type": "selectboxes", "input": true, "label": "Please select if any of the following apply to you:", "values": [{"label": "Have used intranasal and inhaled drugs (e.g. crystal meth, cocaine)", "value": "inhaled_drugs", "shortcut": ""}, {"label": "Current or past history of injection drug use", "value": "IVDU", "shortcut": ""}, {"label": "Have been a hemodialysis patient", "value": "hemodialysis", "shortcut": ""}, {"label": "Have been homeless", "value": "homeless", "shortcut": ""}, {"label": "Have been in prison or a correctional facility", "value": "incarceration", "shortcut": ""}, {"label": "Have had needle stick injuries in the past", "value": "past_needle_injury", "shortcut": ""}, {"label": "Occupational exposure to blood or bodily fluids", "value": "occupation", "shortcut": ""}, {"label": "Family History of Hepatitis B", "value": "family_history_hep_b", "shortcut": ""}, {"label": "Shared medical devices, sharp instruments, or personal hygiene materials with someone who is Hepatitis B or C positive", "value": "shared_sharps", "shortcut": ""}, {"label": "Had tattoos or body piercing where you felt safety precautions were not used (i.e. prison) or non-professional environments", "value": "tattoos", "shortcut": ""}, {"label": "Received healthcare where you were uncertain if sterilization of medical equipment was practiced", "value": "uncertain_medical_sterilization", "shortcut": ""}, {"label": "Engage in sex work, adult film work or intercourse with a sex worker", "value": "unsafe_sex", "shortcut": ""}], "inputType": "checkbox", "tableView": true, "customConditional": "show = data.show_all && !data.tested_for_hep_c && !data.hep_c_positive;", "optionsLabelPosition": "right"}, {"key": "pre_1970_blood_products", "type": "checkbox", "input": true, "label": "Received of blood transfusions, blood products, or an organ transplant before 1970", "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.show_all && !data.tested_for_hep_c && !data.hep_c_positive && (!data.birthyear || data.birthyear <= 1970);"}, {"key": "pre_1992_blood_products", "type": "checkbox", "input": true, "label": "Received of blood transfusions, blood products, or an organ transplant before 1992", "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.show_all && !data.tested_for_hep_c && !data.hep_c_positive && !data.pre_1970_blood_products && (!data.birthyear || data.birthyear <= 1992);"}, {"key": "no_hepatitis_risk_factors", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a risk factor."}, "validate": {"custom": "valid = !!data.no_hepatitis_risk_factors || !!data.pre_1970_blood_products || !!data.pre_1992_blood_products || _.some(_.values(data.hepatitis_risk_factors));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.show_all && !data.tested_for_hep_c && !data.hep_c_positive;"}, {"key": "hep_c_risk_factors_since_last_test", "type": "selectboxes", "input": true, "label": "Since your last test for Hepatitis C, have you had any of the following risk factors for Hepatitis C exposure:", "values": [{"label": "Shared personal care items (i.e. Shared razors, toothbrushes, nail clippers and other household items that might have infected blood on them)", "value": "shared_personal_care_items", "shortcut": ""}, {"label": "Received medical care where non-sterile equipment may have been used ", "value": "medical_equipment", "shortcut": ""}, {"label": "Have been exposed to another partner's blood during sexual activity", "value": "have_been_exposed_to_blood_during_sex", "shortcut": ""}, {"label": "Received personal services (e.g., tattooing or piercing), with nonsterile or suspected non sterile equipment", "value": "non_sterile_equipment", "shortcut": ""}, {"label": "Shared drug use equipment (i.e. needles, cookers, filters, swabs)", "value": "shared_drug_equipment", "shortcut": ""}, {"label": "Engage in sex work / adult film work and it has been more than 6 months since my last screen", "value": "repeat_screen_sex_work", "shortcut": ""}, {"label": "None of the above", "value": "No_New_HepC_Risk_Factors", "shortcut": ""}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "customConditional": "show = data.tested_for_hep_c == true;", "optionsLabelPosition": "right"}, {"key": "please_explain_how_you_were_exposed_through_equipment", "type": "textfield", "input": true, "label": "Please explain how you were exposed through equipment:", "tableView": true, "autoExpand": false, "customConditional": "show = data.hep_c_risk_factors_since_last_test && data.hep_c_risk_factors_since_last_test.medical_equipment;"}, {"key": "medical_recommendation_heading", "html": "<h2 style=\"text-align:center;\"><strong><p>Medical Recommendations</strong></h2></p>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": true, "customConditional": "show = (_.some(_.values(data.hep_c_risk_factors_since_last_test)) && !data.hep_c_risk_factors_since_last_test.No_New_HepC_Risk_Factors)||(data.type_of_sex && (data.type_of_sex.receive_anal));"}, {"key": "content7", "html": "<h2>Hepatitis C Testing</h2><p>Based on your response, we would recommend Hepatitis C testing a minimum of 60 days after your last exposure, and a repeat test at 180 days.</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": true, "customConditional": "show = _.some(_.values(data.hep_c_risk_factors_since_last_test && !data.hep_c_risk_factors_since_last_test.No_New_HepC_Risk_Factors));"}, {"key": "content", "html": "<p>Based on your response, it is recommend that you complete a site specific testing (oral or anal swab) for gonorrhoea/chlamydia. &nbsp; You can proceed to a local health clinic for swabbing by a health care provider. &nbsp;&nbsp;</p>", "type": "content", "input": false, "label": "content", "tableView": true, "refreshOnChange": true, "customConditional": "show = data.type_of_sex && (data.type_of_sex.receive_anal);"}, {"key": "vdrl_only", "html": "<h2 style=\"text-align:center;\"><strong>Syphilis Only Panel</strong></h2>", "type": "content", "input": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = _.isEqual(data.assay_choices, ['VDRL']);", "customConditional": "show = !!data.vdrl_only;"}, {"key": "vdrl_only_reason", "type": "radio", "input": true, "label": "By opting out of the standard STI screening panel and only selecting Syphilis, you are not being screened for HIV, Hepatitis C, and urine transmissible infections.  Not testing for these infections carries a risk of having an undiagnosed sexually transmitted infection.  Please specify your reason for opting out:", "inline": false, "values": [{"label": "I am monitoring my syphilis levels following treatment", "value": "monitoring_syphilis_levels", "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = !!data.vdrl_only;", "optionsLabelPosition": "right"}, {"key": "stated_standard_std_panel_opt_out_reason", "type": "textarea", "input": true, "label": "Please specify your reason here:", "tableView": true, "autoExpand": false, "customConditional": "show = data.vdrl_only_reason == 'other';"}, {"key": "standard_sti_opt_out", "html": "<h2 style=\"text-align:center;\"><strong>Standard STI Panel Opt-Out</strong></h2>", "type": "content", "input": true, "label": "Opt-Out", "tableView": false, "clearOnHide": false, "calculateValue": "value = data.originalAssays && data.assay_choices && _.includes(['std_pn_cgtvh', 'std_pn_rpswb'], data.sku) && _.intersection(data.assay_choices, ['CT','GC','VDRL','HIV']).length < 4;", "customConditional": "show = data.show_all && !!data.standard_sti_opt_out;"}, {"key": "standard_sti_opt_out_reason", "type": "radio", "input": true, "label": "By opting out of the standard STI screening panel, you are not being screened for some sexually transmitted infections, if applicable. Not testing for these infections carries a risk of having an undiagnosed sexually transmitted infection. Please specify you reason for opting out:", "inline": false, "values": [{"label": "I like to test with bloodwork less frequently than urine testing.", "value": "prefer_less_frequent_bloodwork", "shortcut": ""}, {"label": "I will get bloodwork done with my next set of routine tests.", "value": "will_complete_with_future_routine_bloodwork", "shortcut": ""}, {"label": "I do not want HIV testing at this time.", "value": "declines_hiv_testing", "shortcut": ""}, {"label": "I do not want syphilis testing at this time.", "value": "declines_vdrl_testing", "shortcut": ""}, {"label": "I already completed HIV and Syphilis testing recently. I'm aware I need new bloodwork following new partner exposure.", "value": "completed_hiv_syphilis_recently", "shortcut": ""}, {"label": "I already completed urine STI testing recently.", "value": "completed_urine_sti_recently", "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.show_all && !!data.standard_sti_opt_out;", "optionsLabelPosition": "right"}, {"key": "standard_sti_opt_out_reason_stated", "type": "textarea", "input": true, "label": "Please specify your reason for opting out of Standard STI screening:", "tableView": true, "autoExpand": false, "customConditional": "show = data.standard_sti_opt_out_reason == 'other'"}, {"key": "hepatitis_b_c_screening_recommendation", "type": "radio", "input": true, "label": "Based on your birth country and prior screening, we recommend a once in a lifetime screen for Hepatiits B and Hepatitis C. Not testing carries the risk of having an infection undiagnosed.", "inline": false, "values": [{"label": "I would like to test for Hepatitis B and C.", "value": true, "shortcut": ""}, {"label": "I do not want to test for Hepatitis B and C.", "value": false, "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "refreshOnChange": true, "customConditional": "show = (data.previous_hep_b_test == false && data.tested_for_hep_c == false && ['Burkina Faso','Cambodia','Cameroon','Cape Verde','Central African Republic', 'China', 'Côte D’Ivoire', 'De. Rep. Congo', 'Fiji', 'Gabon', 'Gambia','Georgia', 'Guniea', 'Haiti', 'Kazakhstan', 'Kyrgystan', 'Liberia', 'Malawi', 'Mali', 'Mauritania', 'Moldova', 'Mongolia', 'Mozambique', 'Niger', 'Nigeria', 'Palau', 'Papau New Guinea', 'Philippines', 'Romania', 'Russia', 'Rawanda', 'Senegal', 'Soloman Islands', 'Tajikstan', 'Tonga', 'Turkey', 'Uzbekistan', 'Vanuatu'].indexOf(data.country_of_birth) > -1);", "optionsLabelPosition": "right"}, {"key": "hepatitis_b_screening_recommendation", "type": "radio", "input": true, "label": "Based on your birth country and prior screening, we recommend a once in a lifetime screen for Hepatitis B.  Not testing carries the risk of having an infection undiagnosed.", "inline": false, "values": [{"label": "I would like to test for Hepatitis B.", "value": true, "shortcut": ""}, {"label": "I do not want to test for Hepatitis B.", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "refreshOnChange": true, "customConditional": "show = (data.previous_hep_b_test == false && data.tested_for_hep_c == true && ['Burkina Faso','Cambodia','Cameroon','Cape Verde','Central African Republic', 'China', 'Côte D’Ivoire', 'De. Rep. Congo', 'Fiji', 'Gabon', 'Gambia','Georgia', 'Guniea', 'Haiti', 'Kazakhstan', 'Kyrgystan', 'Liberia', 'Malawi', 'Mali', 'Mauritania', 'Moldova', 'Mongolia', 'Mozambique', 'Niger', 'Nigeria', 'Palau', 'Papau New Guinea', 'Philippines', 'Romania', 'Russia', 'Rawanda', 'Senegal', 'Soloman Islands', 'Tajikstan', 'Tonga', 'Turkey', 'Uzbekistan', 'Vanuatu'].indexOf(data.country_of_birth) > -1)||(data.previous_hep_b_test == false && ['Albania','Algeria','Angola','Azerbaijan','Bangladesh','Belarus','Belize','Benin','Bhutan','Brunei Darussalam','Bulgaria','Chad','Colombia','Congo','Cyprus','Djibouti','Dominican Republic','Ecuador','Equatorial Guinea','Eritrea','Ethiopia','Ghana','Italy','Jamaica','Kenya','Kiribati','Kosovo','Laos','Libya','Madagascar','Marshall Islands', 'Micronesia', 'Myanmar', 'Namibia', 'Naru', 'New Zealand', 'Niue', 'Oman', 'Pakistan', 'Peru', 'Samoa', 'Saudi Arabia', 'Sierra Leone', 'Singapore', 'Somalia', 'South Africa', 'South Korea', 'South Sudan', 'Sri Lanka', 'Sudan', 'Suriname', 'Swaziland', 'Syria', 'Tahiti', 'Tanzania', 'Thailand', 'Togo', 'Tunisia', 'Tuvalu', 'Uganda', 'Vietnam', 'Yemen', 'Zambia', 'Zimbabwe'].indexOf(data.country_of_birth) > -1);", "optionsLabelPosition": "right"}, {"key": "hepatitis_c_screening_recommendation", "type": "radio", "input": true, "label": "Based on your birth country and prior screening, we recommend a once in a lifetime screen for Hepatitis C.  Not testing carries the risk of having an infection undiagnosed.", "inline": false, "values": [{"label": "I would like to test for Hepatitis C.", "value": true, "shortcut": ""}, {"label": "I do not want to test for Hepatitis C.", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "refreshOnChange": true, "customConditional": "show = ((data.previous_hep_c_test == false && ['Burundi', 'Egypt', 'El Salvador', 'Estonia', 'Fed. Sts. Micronesia', 'Grenada', 'Guinea-Bissau', 'Hungary', 'Indonesia', 'Iraq', 'Japan', 'Jordan', 'Kiribati', 'Latvia', 'Lithuania', 'Macedonia FYR', 'Poland', 'São Tomé and Principe', 'St. Kitts and Nevis', 'Taiwan', 'Turkmenistan', 'Ukraine'].indexOf(data.country_of_birth) > -1) || (data.previous_hep_b_test == true && data.tested_for_hep_c == false && ['Burkina Faso','Cambodia','Cameroon','Cape Verde','Central African Republic', 'China', 'Côte D’Ivoire', 'De. Rep. Congo', 'Fiji', 'Gabon', 'Gambia','Georgia', 'Guniea', 'Haiti', 'Kazakhstan', 'Kyrgystan', 'Liberia', 'Malawi', 'Mali', 'Mauritania', 'Moldova', 'Mongolia', 'Mozambique', 'Niger', 'Nigeria', 'Palau', 'Papau New Guinea', 'Philippines', 'Romania', 'Russia', 'Rawanda', 'Senegal', 'Soloman Islands', 'Tajikstan', 'Tonga', 'Turkey', 'Uzbekistan', 'Vanuatu'].indexOf(data.country_of_birth) > -1) || (_.some(_.values(data.hep_c_risk_factors_since_last_test)) && !data.hep_c_risk_factors_since_last_test.No_New_HepC_Risk_Factors)) && !data.hep_c_positive;", "optionsLabelPosition": "right"}, {"key": "any_other_symptoms", "type": "radio", "input": true, "label": "Do you have any other symptoms not covered by other questions?", "inline": false, "values": [{"label": "Yes, I have additional symptoms I would like to discuss", "value": true, "shortcut": ""}, {"label": "No other symptoms/Asymptomatic", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": false, "defaultValue": false, "customConditional": "show = data.show_all;", "optionsLabelPosition": "right"}, {"key": "stated_other_symptoms", "type": "textarea", "input": true, "label": "Please use this area to describe any symptoms that we haven't discussed above that you feel is relevant to your story. Please leave this section blank if you have no other concerns.", "adminFlag": true, "tableView": true, "autoExpand": false, "placeholder": "No other relevant concerns", "customConditional": "show = data.show_all && data.any_other_symptoms === true;"}, {"key": "intake_denial_reasons", "type": "textfield", "input": true, "label": "Intake Denial Reasons:", "hidden": true, "disabled": true, "multiple": true, "adminFlag": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "value = _.concat((['fem_pn_uti', 'std_pn_uti'].includes(data.sku)?['sku.uti']:[]),(data.sku=='fem_pn_vswb'?['sku.fem_pn_vswb']:[]),(_.flatMap({'testing_indication':['notified_of_a_positive_result','early_test_window_period'],'type_of_sex':['did_not_understand']},(vs,k)=>{return _.map(_.filter(vs, v=>data[k]&&data[k][v]), v=>k+'.'+v)})),(_.map(_.filter(['std_red_flag_symptoms','uti_symptoms','other_std_symptoms'],k=>_.some(_.values(data[k]))),k=>k+'.some')),(_.map(_.filter(_.flatMap(['concerned_about_known_exposure','previous_syphilis_positive','previous_syphilis_treatment'], k=>[true, 'did_not_understand'].map(v=>[k,v])), kv=>data[kv[0]] === kv[1]),kv=>kv[0]+'.'+kv[1])),(['1-14_days', '15-30_days'].indexOf(data.last_sti_test)>=0?['last_sti_test.within_30_days']:[]),(data.any_other_symptoms === true && !!data.stated_other_symptoms && !/^\\s*$/.test(data.stated_other_symptoms)?['stated_other_symptoms.not_blank']:[]),(_.keys(_.pickBy(data.selected_bullets)).length < 2 ? ['less_than_2_assays_selected'] : []))", "refreshOnChange": true}, {"key": "intake_kind", "type": "textfield", "input": true, "label": "Intake Kind", "hidden": true, "disabled": true, "tableView": true, "clearOnHide": false, "calculateValue": "value = !data.intake_denial_reasons?.length ? 'mail' : 'chat';"}]}