 {
            "key": "cardiovascular_symptoms",
            "type": "selectboxes",
            "input": true,
            "label": "Do you have any of the following cardiovascular symptoms?",
            "values": [
              {
                "label": "Chest pain, tightness or discomfort",
                "value": "chest_pain",
                "shortcut": ""
              },
              {
                "label": "Palpitations",
                "value": "palpitations",
                "shortcut": ""
              },
              {
                "label": "Swelling in the legs, ankles, or feet",
                "value": "swelling",
                "shortcut": ""
              },
              {
                "label": "Dizziness or fainting",
                "value": "dizziness",
                "shortcut": ""
              }
            ],
            "adminFlag": true,
            "tableView": true,
            "optionsLabelPosition": "right"
          },
          {
            "key": "none_of_the_above_cardiovascular_symptoms",
            "type": "checkbox",
            "input": true,
            "label": "None of the above",
            "errors": {
              "custom": "required, or select a symptom."
            },
            "validate": {
              "custom": "valid = !!data.none_of_the_above_cardiovascular_symptoms || _.some(_.values(data.cardiovascular_symptoms));"
            },
            "tableView": true,
            "customClass": "mt-n3",
            "defaultValue": false
          },
          {
            "key": "cardiac_symptoms_not_present",
            "type": "textfield",
            "input": true,
            "label": "Patient indicated they DO NOT have the following cardiac symptoms:",
            "hidden": true,
            "disabled": true,
            "multiple": false,
            "hideLabel": false,
            "tableView": true,
            "spellcheck": false,
            "clearOnHide": false,
            "confirm_label": "I DO NOT have the following heart related symptoms:",
            "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.cardiovascular_symptoms, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"
          },