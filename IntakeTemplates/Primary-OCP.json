{"components": [{"key": "toRenewOrPrescribeBirthControlTeleTestRequiresAPhotoOfYourBloodPressureBloodPressureMeasurementsCanBeTakenOnAHomeBloodPressureCuffOrAtAPharmacyDoYouHaveAPhotoOfYourBloodPressureReading", "type": "radio", "input": true, "label": "To renew or prescribe birth control, TeleTest requires a photo of your blood pressure (taken on a home blood pressure cuff, at a gym or at a pharmacy).  Do you have a photo of your blood pressure reading? ", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}], "tableView": false, "optionsLabelPosition": "right"}, {"key": "whatIsYourPreferredBirthControlMethod", "type": "radio", "input": false, "label": "What is your preferred prescription birth control method?", "inline": false, "values": [{"label": "Birth Control Pills (Estrogen/Progesterone Pill, Progesterone Only Pill)", "value": "birthControlPills", "shortcut": ""}, {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Hormone Shot)", "value": "depoProveraHormoneShot", "shortcut": ""}, {"label": "Nuva Ring (Vaginal Ring)", "value": "nuvaRingVaginalRing", "shortcut": ""}, {"label": "<PERSON><PERSON><PERSON> (hormonal patch)", "value": "evraPatch", "shortcut": ""}, {"label": "IUD", "value": "iud", "shortcut": ""}, {"label": "I don't know and need further guidance", "value": "iDontKnowAndNeedFurtherGuidance", "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}], "tableView": false, "optionsLabelPosition": "right"}, {"key": "content2", "html": "<p>A healthcare provider is required to insert an IUD. Most healthcare providers who complete IUD insertions want to be involved in counselling about IUD selection. &nbsp;We would recommend seeking out a local provider (health unit, family doctor, OBGYN) for this, and TeleTest does not provide IUD prescriptions.</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "content", "html": "<p><strong>Please select your weight and height below:</strong></p>", "type": "content", "input": false, "label": "Content", "tableView": false, "conditional": {"eq": "evraPatch", "show": true, "when": "whatIsYourPreferredBirthControlMethod"}, "refreshOnChange": false}, {"key": "form", "_vid": 0, "name": "bmi", "path": "bmi", "type": "form", "input": true, "label": "Form", "title": "BMI", "display": "form", "settings": {}, "tableView": true, "components": [{"key": "bmiColumns", "type": "columns", "input": false, "label": "Columns", "columns": [{"pull": 0, "push": 0, "size": "md", "width": 4, "offset": 0, "components": [{"key": "height", "data": {"values": [{"label": "", "value": ""}, {"label": "≤ 150cm / ≤ 4'10\"", "value": 1.48}, {"label": "  150cm / 4'11\"  ", "value": 1.5}, {"label": "  152cm / 5'      ", "value": 1.52}, {"label": "  155cm / 5'1\"   ", "value": 1.55}, {"label": "  157cm / 5'2\"   ", "value": 1.57}, {"label": "  160cm / 5'3\"   ", "value": 1.6}, {"label": "  163cm / 5'4\"   ", "value": 1.63}, {"label": "  165cm / 5'5\"   ", "value": 1.65}, {"label": "  168cm / 5'6\"   ", "value": 1.68}, {"label": "  170cm / 5'7\"   ", "value": 1.7}, {"label": "  173cm / 5'8\"   ", "value": 1.73}, {"label": "  175cm / 5'9\"   ", "value": 1.75}, {"label": "  178cm / 5'10\"  ", "value": 1.78}, {"label": "  180cm / 5'11\"  ", "value": 1.8}, {"label": "  183cm / 6'      ", "value": 1.83}, {"label": "  185cm / 6'1\"   ", "value": 1.85}, {"label": "  188cm / 6'2\"   ", "value": 1.88}, {"label": "  190cm / 6'3\"   ", "value": 1.9}, {"label": "  193cm / 6'4\"   ", "value": 1.93}, {"label": "  195cm / 6'5\"   ", "value": 1.95}, {"label": "  198cm / 6'6\"   ", "value": 1.98}, {"label": "  200cm / 6'7\"   ", "value": 2}, {"label": "  203cm / 6'8\"   ", "value": 2.03}, {"label": "  205cm / 6'9\"   ", "value": 2.05}, {"label": "  208cm / 6'10\"  ", "value": 2.08}, {"label": "≥ 210cm / ≥ 6'11\"", "value": 2.1}]}, "type": "select", "input": true, "label": "Height", "widget": "html5", "tableView": true, "searchEnabled": false}], "currentWidth": 4}, {"pull": 0, "push": 0, "size": "md", "width": 3, "offset": 0, "components": [{"key": "weight", "type": "number", "input": true, "label": "Weight", "validate": {"max": 500, "min": 35, "step": 5}, "inputType": "text", "tableView": false}], "currentWidth": 3}, {"pull": 0, "push": 0, "size": "md", "width": 3, "offset": 0, "components": [{"key": "units", "data": {"values": [{"label": "lbs", "value": "lbs"}, {"label": "kg", "value": "kg"}]}, "type": "select", "input": true, "label": "kg / lbs", "widget": "html5", "tableView": true, "defaultValue": "lbs", "searchEnabled": false}], "currentWidth": 3}, {"pull": 0, "push": 0, "size": "md", "width": 2, "offset": 0, "components": [{"key": "bmi", "type": "textfield", "input": true, "label": "BMI", "disabled": true, "tableView": true, "displayMask": "99.9", "calculateValue": {"/": [{"/": [{"var": "data.weight"}, {"if": [{"==": [{"var": "data.units"}, "lbs"]}, 2.20462, 1]}]}, {"*": [{"var": "data.height"}, {"var": "data.height"}]}]}}], "currentWidth": 2}], "tableView": false}], "conditional": {"eq": "evraPatch", "show": true, "when": "whatIsYourPreferredBirthControlMethod"}}, {"key": "areYouInterestedInStartingOrRenewingBirthControlPills", "type": "radio", "input": true, "label": "Please select your interest in birth control pills:", "inline": false, "values": [{"label": "I am interested in starting birth control pills", "value": "iAmInterestedInStartingBirthControlPills", "shortcut": ""}, {"label": "I am interested in renewing birth control pills", "value": "renewPill", "shortcut": ""}, {"label": "I am interested in changing my brand of birth control pill", "value": "changePill", "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}], "tableView": false, "conditional": {"eq": "birthControlPills", "show": true, "when": "whatIsYourPreferredBirthControlMethod"}, "optionsLabelPosition": "right"}, {"key": "youSelectedOtherPleaseStateYourReasonBelow", "type": "textarea", "input": true, "label": "You selected other, please state your reason below:", "tableView": true, "autoExpand": false, "conditional": {"eq": "other", "show": true, "when": "areYouInterestedInStartingOrRenewingBirthControlPills"}}, {"key": "pleaseSelectYourCurrentBirthControlPill", "data": {"values": [{"label": "I'm not currently on a birth control pill", "value": "imNotCurrentlyOnABirthControlPill"}, {"label": "<PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>) [20/100]", "value": "alesseAlysenaAvianeEsmeLutera"}, {"label": "<PERSON><PERSON><PERSON><PERSON>  [0.5/35 or 1/35]", "value": "brevicon0535Or135"}, {"label": "Diane-35 (<PERSON><PERSON><PERSON>35, <PERSON><PERSON><PERSON>-35) [35/2]", "value": "diane35Cleo35Cyestra35"}, {"label": "<PERSON><PERSON> (<PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>) [30/150]", "value": "marvelonApriFreya<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Ovima, Portia) [30/150]", "value": "minOvralOvimaPortia"}, {"label": "Linessa (25/100-150)", "value": "linessa"}, {"label": "<PERSON><PERSON><PERSON> (30/1.5)", "value": "<PERSON><PERSON><PERSON>"}, {"label": "<PERSON><PERSON> (10/1)", "value": "lolo"}, {"label": "Micronor (<PERSON><PERSON><PERSON><PERSON>, Mo<PERSON><PERSON>) [0.35]", "value": "micronorJencyclaMovisse"}, {"label": "<PERSON><PERSON><PERSON> (35/0.5-1)", "value": "ortho"}, {"label": "Seasonale (Indayo) [30/150]", "value": "seasonaleIndayo"}, {"label": "<PERSON>-<PERSON><PERSON> [25/0.18-0.25]", "value": "triCyclenLo"}, {"label": "Tri-Cyclen [35/0.18-0.25]", "value": "triCyclen"}, {"label": "<PERSON><PERSON> [20/3]", "value": "yazYazPlusMya"}, {"label": "<PERSON><PERSON><PERSON> [30/3]", "value": "yasmin"}, {"label": "Other", "value": "other"}]}, "type": "select", "input": true, "label": "Please select your current birth control pill:", "widget": "<PERSON><PERSON><PERSON>", "tableView": true, "conditional": {"eq": "renewPill", "show": true, "when": "areYouInterestedInStartingOrRenewingBirthControlPills"}}, {"key": "submit", "type": "button", "input": true, "label": "Submit", "tableView": false, "disableOnInvalid": true}]}