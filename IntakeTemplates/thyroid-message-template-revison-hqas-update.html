{% load template_extras %}
{% with data=questionnaire.hpc.data %}
{% with summary=questionnaire.raw_formio_summary %}
{% with insured=questionnaire.insured_assays.all %}
{% with uninsured=questionnaire.uninsured_assays.all %}

<p>Hi {{ patient.name }},</p>

<p>This is {{ doctor.name }} (CPSO #{{ doctor.cpso_number }}) &amp; clinic contact phone {{ doctor.phone }}.</p>

<p>I've reviewed your intake regarding current or past use of performance‑enhancing drugs (PEDs). If you had difficulty with any questions or feel your answers didn't reflect your current use or goals, we can arrange a secure follow‑up chat. Otherwise, we'll proceed with the plan below.</p>

<h2 style="text-align:center;margin-top:30px;">Plan</h2>

<p>Please review your intake summary to ensure accuracy. If everything looks correct, you may proceed to download your lab requisition.</p>
<ol>
  <li>Select your preferred laboratory and complete the required blood work.</li>
  <li>Your results will be uploaded to the portal once available.</li>
  <li>If any abnormalities are identified, we'll contact you to arrange a secure follow‑up chat.</li>
</ol>


<h5>Lab Testing Requested</h5>

{% if insured %}
<p><strong>Insured Tests:</strong></p>
<ul>
  {% for a in insured %}<li>{{ a.name }} ({{ a.test_type }} test)</li>{% endfor %}
</ul>
{% endif %}

{% if uninsured %}
<p><strong>Uninsured Tests:</strong></p>
<ul>
  {% for a in uninsured %}<li>{{ a.name }} ({{ a.test_type }} test)</li>{% endfor %}
</ul>
<p>Uninsured testing may include non‑essential monitoring or private‑pay options not covered by OHIP. These can be declined at the lab.</p>
{% endif %}


<hr>

{% if data.mental_health_suicidal_check or data.cough_urgent_warning_understanding %}
<h2 style="text-align:left;margin-top:30px;">Recommendations</h2>
<ul>
  {% if data.mental_health_suicidal_check == "yes" %}
    <li>Mental Health: Seek emergency care for active thoughts of self‑harm.</li>
  {% elif data.mental_health_suicidal_check == "no" %}
    <li>Mental Health: Safety plan discussed — understands when to seek care if thoughts arise.</li>
  {% endif %}
  {% if data.cough_urgent_warning_understanding == "understand" %}
    <li>Respiratory Warning: Acknowledged understanding of when to seek urgent care.</li>
  {% endif %}
</ul>
{% endif %}

<h2 style="text-align:left;margin-top:30px;">General Thyroid-Care Advice</h2>
<ul>
  <li><strong>Best Time to Test:</strong> If you are on medication, ensure you schedule your thyroid testing (TSH) only <u>after you have been on the same dose for at least 6 weeks</u>—or 6 weeks after starting/stopping therapy. Earlier tests can appear “abnormal” even when your levels are actually trending to the right place.</li>
  <li><strong>Avoid Interfering Supplements:</strong> Stop biotin (hair/nail vitamins) for 2 days before bloodwork. Delay high-dose iodine/kelp, iron, calcium, magnesium, and antacids until <u>4 hours</u> after taking your thyroid pill; they block absorption.</li>
  <li><strong>Take Medication Consistently:</strong> Swallow your tablet at the <u>same time every morning</u> on an empty stomach with water. Wait at least 30 minutes before coffee, breakfast, or other meds. Small timing changes can swing TSH results and symptoms.</li>
  <li><strong>When to Seek In-Person Care:</strong> New or worsening symptoms—racing heart, palpitations, chest pain, excessive fatigue, neck swelling, unexplained weight change, or severe mood shifts—should be assessed promptly by a healthcare professional. <em>“Normal” lab values do not automatically rule out a thyroid (or other) problem, so arrange an in-person examination in parallel to this testing.</em></li>
</ul>

<p style="margin-top:30px;">
    <strong>Confirmation:</strong> By completing and submitting this intake form, you confirm that the information provided is accurate to the best of your knowledge. You acknowledge that this intake does not replace the need for in-person care when symptoms arise, and that follow-up may be required based on lab results. You agree to seek emergency or in-person medical care if symptoms worsen or new symptoms develop.
  </p>

<h2 style="text-align:center;margin-top:30px;">Thyroid Testing and Treatment Summary</h2>

{% with hqas=summary|confirm:"Reason for Thyroid Assessment:reason_for_thyroid_testing,reason_for_thyroid_testing_other,autoimmune_condition_type_v2,autoimmune_diagnosis_timing,family_thyroid_diagnosed_members,family_thyroid_condition_type,family_thyroid_cancer_type;Thyroid Medication History:thyroid_medication_status,thyroid_medication_type,thyroid_medication_start_time,thyroid_medication_dose_change,thyroid_medication_last_use,thyroid_medication_stopped_reason,thyroid_medication_followup_testing;Prior Thyroid / Lab Tests:prior_thyroid_result,thyroid_last_test_timing,prior_tests_completed,last_known_lab_timing,last_kidney_function_test,prior_kidney_function_value,last_urine_acr_test,last_lipid_profile_test,lipid_profile_abnormalities" %}
  {% for heading, qas in hqas.items %}
    <h6>{{ heading }}</h6>
    <ul>
      {% for qa in qas %}
        <li>{{ qa|safe }}</li>
      {% endfor %}
    </ul>
  {% endfor %}
{% endwith %}

<h2 style="text-align:center;margin-top:30px;">Symptom Summary</h2>

<!-- symptoms present -->
{% with hqas=summary|confirm:"Chest Pain:chest_pain_onset,chest_pain_character,chest_pain_triggers,chest_pain_relievers,chest_pain_location,chest_pain_radiation,chest_pain_pattern;Palpitations:palpitations_onset,palpitations_rhythm,palpitations_triggers,palpitations_duration,palpitations_associated_symptoms;Swelling:swelling_location,swelling_sidedness,swelling_onset,swelling_timing,swelling_pitting;Dizziness:dizziness_onset,dizziness_frequency,dizziness_character,dizziness_timing,fainting_loss_consciousness;Cough:cough_type,cough_duration,cough_coughing_blood,cough_feeling_unwell,cough_with_cold,cough_progression;Shortness of Breath:sob_triggers;Wheezing:wheezing_timing,wheezing_relief,wheezing_asthma_history;Abdominal Pain:abdominal_pain_location,onset_abdominal_pain,abdominal_pain_typical_vs_new;Constipation:symptom_start_constipation,constipation_frequency,constipation_stool_type,constipation_straining,constipation_incomplete,constipation_impact_on_life,constipation_laxative_use;Diarrhea:diarrhea_features,onset_diarrhea,diarrhea_typical_vs_new;Nausea:nausea_timing,onset_nausea,nausea_typical_vs_new;Rectal Bleeding:onset_rectal_bleeding,rectal_bleeding_appearance,rectal_bleeding_typical_vs_new;Vomiting:onset_vomiting,vomit_appearance,vomiting_typical_vs_new;Fatigue:symptom_start_fatigue,fatigue_time_of_day,fatigue_frequency,fatigue_affects_function,fatigue_daytime_sleepiness,fatigue_osa_risk_factors,fatigue_osa_evaluation;Cold Intolerance:symptom_start_cold_intolerance,cold_intolerance_description;Weight Gain:weight_gain_vs_difficulty_losing,symptom_start_weight_gain,weight_gain_description,weight_gain_amount,weight_gain_units;Dry Skin:symptom_start_dry_skin,dry_skin_description;Hair Loss:symptom_start_hair_loss,hair_loss_description,hair_loss_location,hair_loss_acceleration,hair_loss_circular_appearance;Menstrual Irregularities:symptom_start_menstrual_irregularity,menstrual_irregularity_pattern,menstrual_ultrasound_done,seen_gynecologist,bleeding_related_to_intercourse,last_pap_test_timing,last_pap_test_result;Brain Fog:symptom_start_brain_fog,brain_fog_description;Mental Health:mental_health_onset,mental_health_prior_diagnosis,mental_health_diagnosis_source,mental_health_prior_treatment,mental_health_therapy_discussion" %}
{% if hqas %}
<h3>Symptoms Present</h3>
<ul>
  {% for heading,qas in hqas.items %}
  <li>{{heading}}</li>
  <ul>
    {% for qa in qas %}<li>{{qa|safe}}</li>{% endfor %}
  </ul>
  {% endfor %}
</ul>
{% endif %}{% endwith %}
<!-- symptoms NOT present -->
<h3>Symptoms NOT Present</h3>
<ul>
  {% for qa in summary|confirm:"cardiac_symptoms_not_present,respiratory_symptoms_not_present,gastrointestinal_symptoms_not_present,hypothyroid_symptoms_not_present,mental_health_symptoms_not_present" %}
    <li>{{ qa|safe }}</li>
  {% endfor %}
</ul>
<!-- blood pressure & imaging -->
{% with hqas=summary|confirm:"Blood Pressure Monitoring:systolic_bp,diastolic_bp,bp_reading_date,bp_monitoring_frequency,bp_readings_per_sitting;Prior Imaging Tests:prior_imaging_tests" %}
{% if hqas %} {% for heading,qas in hqas.items %}
<h6>{{heading}}</h6>
<ul>
  {% for qa in qas %}<li>{{qa|safe}}</li>{% endfor %}
</ul>
{% endfor %}{% endif %}{% endwith %}


<p>Best regards,<br>{{ doctor.name }}</p>

{% endwith %}
{% endwith %}
{% endwith %}
{% endwith %}