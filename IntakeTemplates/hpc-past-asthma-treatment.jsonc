{"components": [{"key": "heading_past_asthma_treatments", "type": "content", "label": "Content", "input": false, "html": "</br><h1>Past Asthma Treatments</h1><p>Please tell us about every asthma medicine you've tried in the past-even ones that didn't seem to work or that you used only for a short time. This background helps us understand what has (and hasn't) helped you so far.</p>", "tableView": false, "refreshOnChange": false}, {"key": "past_rx_category_edu", "type": "content", "input": false, "label": "Education", "html": "<div style='border-left:4px solid #198754; background-color:#d1e7dd; padding:12px 14px; margin:8px 0 14px 0;'>\n  <p style='margin:0 0 6px 0;'>To jog your memory, here are the main groups of asthma medicines people are often prescribed:</p>\n  <ul style='margin:0 0 0 18px; padding-left:0;'>\n    <li><strong>Quick-relief (rescue) puffers</strong> - the blue inhaler such as Ventolin® / Albuterol you reach for when breathing suddenly gets tight.</li>\n    <li><strong>Daily preventer inhalers</strong> - everyday steroid-based puffers like Flovent®, Symbicort®, or Breo® that help stop flare-ups.</li>\n    <li><strong>Pills / tablets</strong> - medicines you swallow, for example Montelukast (Singulair®) or a short course of Prednisone.</li>\n  </ul>\n</div>", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.used_prescription_asthma_past === 'yes';"}, {"key": "used_prescription_asthma_past", "type": "radio", "input": true, "label": "Have you taken prescription medicines for asthma in the past (i.e. inhalers, pills)?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Prescription asthma meds used before:", "optionsLabelPosition": "right"}, {"key": "past_rx_category", "type": "selectboxes", "input": true, "label": "Which kind of asthma medicine did you use?", "values": [{"label": "Quick-relief puffers", "value": "short_acting"}, {"label": "Daily preventer inhalers", "value": "maintenance"}, {"label": "Pills / tablets", "value": "oral"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = data.used_prescription_asthma_past === 'yes';"}, {"key": "heading_past_short_acting", "type": "content", "input": false, "label": "Rescue Heading", "html": "</br><h4 class='mb-n2'>Rescue (Quick-relief) Puffers</h4></br><p>Please list <strong>every</strong> rescue inhaler you've ever tried-even ones that didn't seem to help or that you used only briefly. After you finish one entry, click “+ Add another rescue inhaler” to record the next product.</p></br>", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.past_rx_category?.short_acting === true"}, {"key": "past_short_acting_grid", "type": "<PERSON><PERSON><PERSON>", "label": "Past rescue inhalers:", "rowClass": "mb-2", "addAnother": "+ Add another rescue inhaler", "addAnotherPosition": "bottom", "tableView": true, "customConditional": "show = data.past_rx_category && data.past_rx_category.short_acting === true;", "headerTemplate": "<div class='row'><div class='col-sm-2'>Product</div><div class='col-sm-1'>Dose</div><div class='col-sm-1'>Puffs</div><div class='col-sm-1'>Using</div><div class='col-sm-2'>Effect</div><div class='col-sm-2'>Schedule</div><div class='col-sm-1'>Flare doses</div><div class='col-sm-2'>Last on</div></div>", "components": [{"key": "rx_name", "type": "select", "label": "Product", "confirm_label": "Rescue inhaler:", "data": {"values": [{"label": "Salbutamol / Albuterol (Ventolin®)", "value": "salbutamol"}, {"label": "Terbutaline (Bricanyl®)", "value": "terbutaline"}, {"label": "Ipratropium (Atrovent®)", "value": "ipratropium"}, {"label": "Other rescue inhaler", "value": "other"}]}, "widget": "html5", "validate": {"required": true}, "tableView": true}, {"key": "rx_other_name", "type": "textfield", "label": "Other product name", "validate": {"required": true}, "tableView": true, "customConditional": "show = (data.parent && data.parent.rx_name === 'other') || (data.rx_name === 'other');"}, {"key": "rx_dose", "type": "select", "confirm_label": "Rescue inhaler dose:", "label": "Dose / puff", "widget": "html5", "dataSrc": "custom", "data": {"custom": "var map = { salbutamol:[90,100,200], terbutaline:[500], ipratropium:[20] };\nif (!row.rx_name || row.rx_name === 'other') { return []; }\nreturn (map[row.rx_name] || []).map(function(v){ return { label: v + ' µg', value: String(v) }; }).concat([{ label:'Not sure', value:'unsure'}]);"}, "customConditional": "show = row.rx_name && row.rx_name !== 'other';", "validate": {"required": true}, "tableView": true}, {"key": "rx_other_dose", "type": "textfield", "confirm_label": "Rescue inhaler dose:", "label": "Do<PERSON> (if known)", "tableView": true, "customConditional": "show = ((data.parent && data.parent.rx_name) || data.rx_name) === 'other';"}, {"key": "rx_units", "type": "select", "label": "Puffs each time", "confirm_label": "Rescue inhaler puffs:", "data": {"values": [{"label": "1", "value": "1"}, {"label": "2", "value": "2"}, {"label": "3", "value": "3"}, {"label": "4", "value": "4"}, {"label": "5", "value": "5"}, {"label": "6", "value": "6"}, {"label": "7", "value": "7"}, {"label": "8", "value": "8"}, {"label": "9", "value": "9"}, {"label": "≥10", "value": "10_plus"}, {"label": "Not sure", "value": "unsure"}]}, "widget": "html5", "validate": {"required": true}, "tableView": true}, {"key": "rx_current", "type": "radio", "label": "Using", "confirm_label": "Rescue inhaler used:", "inline": true, "values": [{"label": "Currently", "value": "current"}, {"label": "Past", "value": "past"}], "validate": {"required": true}, "tableView": true}, {"key": "rx_effect", "type": "select", "confirm_label": "Rescue inhaler effect:", "label": "Effect", "data": {"values": [{"label": "Controlled symptoms well", "value": "controlled"}, {"label": "Some help", "value": "partial"}, {"label": "No benefit", "value": "none"}, {"label": "Made things worse", "value": "worse"}, {"label": "Not sure", "value": "unsure"}]}, "widget": "html5", "validate": {"required": true}, "tableView": true}, {"key": "rx_freq", "type": "select", "confirm_label": "Rescue inhaler schedule:", "label": "Prescribed schedule", "widget": "html5", "tableView": true, "validate": {"required": true}, "data": {"values": [{"label": "Only when I need it", "value": "prn"}, {"label": "Every 4 hours", "value": "q4h"}, {"label": "Every 6 hours", "value": "q6h"}, {"label": "4 times a day", "value": "qid"}, {"label": "2 times a day", "value": "bid"}, {"label": "Not sure", "value": "unsure"}]}}, {"key": "rx_flare_times", "type": "select", "confirm_label": "Rescue inhaler flare-up doses:", "label": "Times per day during a flare-up", "data": {"values": [{"label": "No extra doses", "value": "same"}, {"label": "+1 extra dose", "value": "plus1"}, {"label": "+2 or more extra doses", "value": "plus2"}, {"label": "Not sure", "value": "unsure"}]}, "widget": "html5", "validate": {"required": true}, "tableView": true}, {"key": "rx_last_used", "type": "select", "label": "Last used (approx.)", "confirm_label": "Last on:", "widget": "html5", "data": {"values": [{"label": "Today", "value": "today"}, {"label": "Yesterday", "value": "yesterday"}, {"label": "2 days ago", "value": "2d"}, {"label": "3 days ago", "value": "3d"}, {"label": "4 days ago", "value": "4d"}, {"label": "5 days ago", "value": "5d"}, {"label": "6 days ago", "value": "6d"}, {"label": "7 days ago", "value": "7d"}, {"label": "7+ days - 1 month", "value": "7d_1m"}, {"label": "1 - 3 months", "value": "1_3m"}, {"label": "3 - 6 months", "value": "3_6m"}, {"label": "6 - 12 months", "value": "6_12m"}, {"label": "1 - 2 years", "value": "1_2y"}, {"label": "2 - 5 years", "value": "2_5y"}, {"label": "5+ years", "value": "5y_plus"}, {"label": "Not sure", "value": "unsure"}]}, "validate": {"required": true}, "tableView": true, "customConditional": "show = row.rx_current === 'past';"}]}, {"key": "heading_past_maintenance", "type": "content", "input": false, "label": "Controller Heading", "html": "</br><h4 class='mb-n2'>Daily Preventer Inhalers</h4></br><p>Add all the daily (controller) puffers you've taken in the past. Include steroid-only inhalers (e.g.&nbsp;Flovent®, Alvesco®) and combination inhalers (e.g.&nbsp;Symbicort®, Advair®, Breo®)&nbsp;- even if they didn't work well for you.</p></br>", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.past_rx_category?.maintenance === true"}, {"key": "past_maintenance_grid", "type": "<PERSON><PERSON><PERSON>", "label": "Past daily controller inhalers:", "rowClass": "mb-2", "addAnother": "+ Add another controller inhaler", "addAnotherPosition": "bottom", "tableView": true, "customConditional": "show = data.past_rx_category && data.past_rx_category.maintenance === true;", "headerTemplate": "<div class='row'><div class='col-sm-2'>Product</div><div class='col-sm-1'>Dose</div><div class='col-sm-1'>Puffs</div><div class='col-sm-1'>Using</div><div class='col-sm-2'>Effect</div><div class='col-sm-2'>Schedule</div><div class='col-sm-1'>Flare doses</div><div class='col-sm-2'>Last on</div></div>", "components": [{"key": "rx_name", "type": "select", "confirm_label": "Controller inhaler:", "label": "Product", "data": {"values": [{"label": "Fluticasone (Flovent®)", "value": "fluticasone"}, {"label": "Budesonide (Pulmicort®)", "value": "budesonide"}, {"label": "Beclomethasone (Qvar®)", "value": "beclo"}, {"label": "Mometasone (Asmanex®)", "value": "mometasone"}, {"label": "Ciclesonide (Alvesco®)", "value": "ciclesonide"}, {"label": "Advair®", "value": "advair"}, {"label": "Symbicort®", "value": "symbicort"}, {"label": "Breo®", "value": "breo"}, {"label": "Spiriva Respimat®", "value": "tiotropium"}, {"label": "Other controller inhaler", "value": "other"}]}, "widget": "html5", "validate": {"required": true}, "tableView": true}, {"key": "rx_other_name", "type": "textfield", "confirm_label": "Controller inhaler:", "label": "Other product name", "validate": {"required": true}, "tableView": true, "customConditional": "show = (data.parent && data.parent.rx_name === 'other') || (data.rx_name === 'other');"}, {"key": "rx_dose", "type": "select", "confirm_label": "Controller inhaler dose:", "label": "Dose / puff", "widget": "html5", "dataSrc": "custom", "data": {"custom": "var map = {\n  fluticasone:[50,100,125,220,250],\n  budesonide:[50,100,200,400],\n  beclo:[40,50,100,150,200],\n  mometasone:[50,100,200,220],\n  ciclesonide:[80,160],\n  advair:['100/50','250/50','500/50'],\n  symbicort:['100/6','200/6','400/12'],\n  breo:['100/25','200/25'],\n  tiotropium:[2.5,5]\n};\nif (!row.rx_name || row.rx_name === 'other') { return []; }\nreturn (map[row.rx_name] || []).map(function(v){ return {label: String(v)+' µg', value: String(v)}; })\n  .concat([{label:'Not sure', value:'unsure'}]);"}, "customConditional": "show = row.rx_name && row.rx_name !== 'other';", "validate": {"required": true}, "tableView": true}, {"key": "rx_other_dose", "type": "textfield", "confirm_label": "Controller inhaler dose:", "label": "Do<PERSON> (if known)", "tableView": true, "customConditional": "show = ((data.parent && data.parent.rx_name) || data.rx_name) === 'other';"}, {"key": "rx_units", "type": "select", "confirm_label": "Controller inhaler puffs:", "label": "Puffs each time", "data": {"values": [{"label": "1", "value": "1"}, {"label": "2", "value": "2"}, {"label": "3", "value": "3"}, {"label": "4", "value": "4"}, {"label": "5", "value": "5"}, {"label": "6", "value": "6"}, {"label": "7", "value": "7"}, {"label": "8", "value": "8"}, {"label": "9", "value": "9"}, {"label": "≥10", "value": "10_plus"}, {"label": "Not sure", "value": "unsure"}]}, "widget": "html5", "validate": {"required": true}, "tableView": true}, {"key": "rx_current", "confirm_label": "Controller inhaler used:", "type": "radio", "label": "Using", "inline": true, "values": [{"label": "Currently", "value": "current"}, {"label": "Past", "value": "past"}], "validate": {"required": true}, "tableView": true}, {"key": "rx_effect", "confirm_label": "Controller inhaler effect:", "type": "select", "label": "Effect", "data": {"values": [{"label": "Controlled symptoms well", "value": "controlled"}, {"label": "Some help", "value": "partial"}, {"label": "No benefit", "value": "none"}, {"label": "Made things worse", "value": "worse"}, {"label": "Not sure", "value": "unsure"}]}, "widget": "html5", "validate": {"required": true}, "tableView": true}, {"key": "rx_freq", "confirm_label": "Controller inhaler schedule:", "type": "select", "label": "Prescribed schedule", "widget": "html5", "tableView": true, "validate": {"required": true}, "data": {"values": [{"label": "Once a day", "value": "qd"}, {"label": "Twice a day", "value": "bid"}, {"label": "Three times a day", "value": "tid"}, {"label": "Four times a day", "value": "qid"}, {"label": "Other / not sure", "value": "unsure"}]}}, {"key": "rx_flare_times", "confirm_label": "Controller inhaler flare-up doses:", "type": "select", "label": "Times per day during a flare-up", "data": {"values": [{"label": "No extra doses", "value": "same"}, {"label": "+1 extra dose", "value": "plus1"}, {"label": "+2 or more extra doses", "value": "plus2"}, {"label": "Not sure", "value": "unsure"}]}, "widget": "html5", "validate": {"required": true}, "tableView": true}, {"key": "rx_last_used", "type": "select", "label": "Last used (approx.)", "confirm_label": "Last on:", "widget": "html5", "data": {"values": [{"label": "Today", "value": "today"}, {"label": "Yesterday", "value": "yesterday"}, {"label": "2 days ago", "value": "2d"}, {"label": "3 days ago", "value": "3d"}, {"label": "4 days ago", "value": "4d"}, {"label": "5 days ago", "value": "5d"}, {"label": "6 days ago", "value": "6d"}, {"label": "7 days ago", "value": "7d"}, {"label": "7+ days - 1 month", "value": "7d_1m"}, {"label": "1 - 3 months", "value": "1_3m"}, {"label": "3 - 6 months", "value": "3_6m"}, {"label": "6 - 12 months", "value": "6_12m"}, {"label": "1 - 2 years", "value": "1_2y"}, {"label": "2 - 5 years", "value": "2_5y"}, {"label": "5+ years", "value": "5y_plus"}, {"label": "Not sure", "value": "unsure"}]}, "validate": {"required": true}, "tableView": true, "customConditional": "show = row.rx_current === 'past';"}]}, {"key": "heading_past_oral", "type": "content", "input": false, "label": "Oral Heading", "html": "</br><h4 class='mb-n2'>Pills / Tablets for Asthma</h4></br><p>Please record any asthma pills or short steroid bursts you have taken (for example Montelukast/Singulair®, low-dose Theophylline, long-term Azithromycin, or short courses of Prednisone).</p></br>", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.past_rx_category?.oral === true"}, {"key": "past_oral_grid", "type": "<PERSON><PERSON><PERSON>", "label": "Past oral / tablet asthma medicines:", "rowClass": "mb-2", "addAnother": "+ Add oral medication", "addAnotherPosition": "bottom", "tableView": true, "customConditional": "show = data.past_rx_category && data.past_rx_category.oral === true;", "headerTemplate": "<div class='row'><div class='col-sm-2'>Medication</div><div class='col-sm-1'>Dose</div><div class='col-sm-1'>Tabs</div><div class='col-sm-1'>Using</div><div class='col-sm-2'>Effect</div><div class='col-sm-2'>Schedule</div><div class='col-sm-1'>Flare doses</div><div class='col-sm-2'>Last on</div></div>", "components": [{"key": "rx_name", "type": "select", "label": "Medication", "confirm_label": "Oral asthma medication:", "data": {"values": [{"label": "Montelukast (Singulair®)", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "theophylline"}, {"label": "Azithromycin (long-term)", "value": "azith<PERSON>"}, {"label": "Low-dose prednisone", "value": "pred"}, {"label": "Other oral asthma med", "value": "other"}]}, "widget": "html5", "validate": {"required": true}, "tableView": true}, {"key": "rx_other_name", "confirm_label": "Oral asthma medication:", "type": "textfield", "label": "Other medication name", "validate": {"required": true}, "tableView": true, "customConditional": "show = (data.parent && data.parent.rx_name === 'other') || (data.rx_name === 'other');"}, {"key": "rx_dose", "type": "textfield", "confirm_label": "Oral asthma medication dose:", "label": "Strength (mg)", "placeholder": "e.g. 10 mg", "validate": {"required": true}, "tableView": true}, {"key": "rx_units", "type": "select", "label": "Tablets each time", "confirm_label": "Oral asthma medication tablets:", "data": {"values": [{"label": "½", "value": "0.5"}, {"label": "1", "value": "1"}, {"label": "2", "value": "2"}, {"label": "Not sure", "value": "unsure"}]}, "widget": "html5", "validate": {"required": true}, "tableView": true}, {"key": "rx_current", "confirm_label": "Oral asthma medication used:", "type": "radio", "label": "Using", "inline": true, "values": [{"label": "Currently", "value": "current"}, {"label": "Past", "value": "past"}], "validate": {"required": true}, "tableView": true}, {"key": "rx_effect", "confirm_label": "Oral asthma medication effect:", "type": "select", "label": "Effect", "data": {"values": [{"label": "Controlled symptoms well", "value": "controlled"}, {"label": "Some help", "value": "partial"}, {"label": "No benefit", "value": "none"}, {"label": "Made things worse", "value": "worse"}, {"label": "Not sure", "value": "unsure"}]}, "widget": "html5", "validate": {"required": true}, "tableView": true}, {"key": "rx_freq", "confirm_label": "Oral asthma medication schedule:", "type": "select", "label": "Prescribed schedule", "data": {"values": [{"label": "Once daily", "value": "qd"}, {"label": "Twice daily", "value": "bid"}, {"label": "Every other day", "value": "qod"}, {"label": "As directed", "value": "unsure"}]}, "widget": "html5", "validate": {"required": true}, "tableView": true}, {"key": "rx_flare_times", "confirm_label": "Oral asthma medication flare-up doses:", "type": "select", "label": "Times per day during a flare-up", "data": {"values": [{"label": "No change", "value": "same"}, {"label": "+1 extra dose", "value": "plus1"}, {"label": "+2 or more extra doses", "value": "plus2"}, {"label": "Not sure", "value": "unsure"}]}, "widget": "html5", "validate": {"required": true}, "tableView": true}, {"key": "rx_last_used", "type": "select", "label": "Last used (approx.)", "confirm_label": "Last on:", "widget": "html5", "data": {"values": [{"label": "Today", "value": "today"}, {"label": "Yesterday", "value": "yesterday"}, {"label": "2 days ago", "value": "2d"}, {"label": "3 days ago", "value": "3d"}, {"label": "4 days ago", "value": "4d"}, {"label": "5 days ago", "value": "5d"}, {"label": "6 days ago", "value": "6d"}, {"label": "7 days ago", "value": "7d"}, {"label": "7+ days - 1 month", "value": "7d_1m"}, {"label": "1 - 3 months", "value": "1_3m"}, {"label": "3 - 6 months", "value": "3_6m"}, {"label": "6 - 12 months", "value": "6_12m"}, {"label": "1 - 2 years", "value": "1_2y"}, {"label": "2 - 5 years", "value": "2_5y"}, {"label": "5+ years", "value": "5y_plus"}, {"label": "Not sure", "value": "unsure"}]}, "validate": {"required": true}, "tableView": true, "customConditional": "show = row.rx_current === 'past';"}]}]}