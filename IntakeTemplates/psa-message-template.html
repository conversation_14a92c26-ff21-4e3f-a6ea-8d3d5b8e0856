{% load template_extras %}
{% with rxts=questionnaire.rxts.all rxks=questionnaire.rxt_keys insured=questionnaire.insured_assays.all uninsured=questionnaire.uninsured_assays.all %}

<p>Hi {{ patient.name }},</p>
<p>
    This is {{ doctor.name }} (CPSO #{{ doctor.cpso_number }}) from our clinic. 
    If you have questions about your responses or feel your answers missed any symptoms or conditions, 
    we can arrange for secure real-time messaging. Otherwise, we can proceed with the plan below, but I need you to confirm the following from your medical history.
</p>
<!-- =================  PSA INTAKE SUMMARY  ================= -->
{% with data=questionnaire.hpc.data summary=questionnaire.raw_formio_summary %}
<h5>PSA Intake Summary</h5>
<ul>

  {# Reason(s) for PSA testing #}
  {% with reason_list=summary|confirm:"screening_indication_psa,other_screening_indication_psa" %}
  {% if reason_list %}
    <li><strong>Reason(s) for PSA testing</strong>
      <ul>
        {% for qa in reason_list %}
          {% if ":" in qa %}
            <li>{% autoescape off %}{{ qa|replace:": |: <strong>" }}{% endautoescape %}</strong></li>
          {% else %}
            <li>{{ qa|safe }}</li>
          {% endif %}
        {% endfor %}
      </ul>
    </li>
  {% endif %}
  {% endwith %}

  {# History of prostate cancer #}
  {% if data.screening_indication_psa and data.screening_indication_psa.history_prostate_cancer %}
    {% with pc_list=summary|confirm:"pc_dx_year,pc_treatment_received,pc_oncology_followup,pc_followup_type,pc_last_followup_range,pc_specialist_location" %}
    {% if pc_list %}
      <li><strong>History of prostate cancer</strong>
        <ul>
          {% for qa in pc_list %}
            {% if ":" in qa %}
              <li>{% autoescape off %}{{ qa|replace:": |: <strong>" }}{% endautoescape %}</strong></li>
            {% else %}
              <li>{{ qa|safe }}</li>
            {% endif %}
          {% endfor %}
        </ul>
      </li>
    {% endif %}
    {% endwith %}
  {% endif %}

  {# Previous elevated PSA #}
  {% if data.screening_indication_psa and data.screening_indication_psa.prior_elevated_psa %}
    {% with prior_list=summary|confirm:"prior_psa_biopsy_done,prior_psa_followup_specialist" %}
    {% if prior_list %}
      <li><strong>Previous elevated PSA</strong>
        <ul>
          {% for qa in prior_list %}
            {% if ":" in qa %}
              <li>{% autoescape off %}{{ qa|replace:": |: <strong>" }}{% endautoescape %}</strong></li>
            {% else %}
              <li>{{ qa|safe }}</li>
            {% endif %}
          {% endfor %}
        </ul>
      </li>
    {% endif %}
    {% endwith %}
  {% endif %}

  {# Routine screening details (family history & higher risk ancestry) #}
  {% if data.screening_indication_psa and data.screening_indication_psa.preventative_screening %}
    {% with screen_list=summary|confirm:"screening_family_history,fh_who_diagnosed,fh_father_age_dx,fh_brother_age_dx,fh_son_age_dx,screening_black_heritage" %}
    {% if screen_list %}
      <li><strong>Routine screening details</strong>
        <ul>
          {% for qa in screen_list %}
            {% if ":" in qa %}
              <li>{% autoescape off %}{{ qa|replace:": |: <strong>" }}{% endautoescape %}</strong></li>
            {% else %}
              <li>{{ qa|safe }}</li>
            {% endif %}
          {% endfor %}
        </ul>
      </li>
    {% endif %}
    {% endwith %}
  {% endif %}

  {# Monitoring while on testosterone or anabolic steroids #}
  {% if data.screening_indication_psa and data.screening_indication_psa.on_testosterone_or_anabolic_steroids %}
    {% with and_list=summary|confirm:"androgen_therapy_type,androgen_duration" %}
    {% if and_list %}
      <li><strong>Monitoring while on testosterone or anabolic steroids</strong>
        <ul>
          {% for qa in and_list %}
            {% if ":" in qa %}
              <li>{% autoescape off %}{{ qa|replace:": |: <strong>" }}{% endautoescape %}</strong></li>
            {% else %}
              <li>{{ qa|safe }}</li>
            {% endif %}
          {% endfor %}
        </ul>
      </li>
    {% endif %}
    {% endwith %}
  {% endif %}

  {# Prostate exam (DRE) #}
  {% with dre_list=summary|confirm:"prostate_exam_last_range,prostate_exam_performed_by,prostate_exam_findings,prostate_exam_abnormal_details" %}
  {% if dre_list %}
    <li><strong>Prostate exam (DRE)</strong>
      <ul>
        {% for qa in dre_list %}
          {% if ":" in qa %}
            <li>{% autoescape off %}{{ qa|replace:": |: <strong>" }}{% endautoescape %}</strong></li>
          {% else %}
            <li>{{ qa|safe }}</li>
          {% endif %}
        {% endfor %}
      </ul>
    </li>
  {% endif %}
  {% endwith %}

</ul>
{% endwith %}

<!-- =================  URINARY / PROSTATE SYMPTOMS SUMMARY  ================= -->
{% with data=questionnaire.hpc.data summary=questionnaire.raw_formio_summary uso=data.urinary_symptom_overview %}
<h5>Urinary / Prostate Symptoms Summary</h5>
<ul>

  {# No symptoms path #}
  {% if data.changes_urinary == 'no' %}
    {% with none_list=summary|confirm:"heading_confirm_no_urinary_symptoms,confirm_no_urinary_symptoms" %}
    {% if none_list %}
      <li><strong>No urinary/prostate symptoms</strong>
        <ul>
          {% for qa in none_list %}
            {% if ":" in qa %}
              <li>{% autoescape off %}{{ qa|replace:": |: <strong>" }}{% endautoescape %}</strong></li>
            {% else %}
              <li>{{ qa|safe }}</li>
            {% endif %}
          {% endfor %}
        </ul>
      </li>
    {% endif %}
    {% endwith %}
  {% endif %}

  {# Symptoms present path #}
  {% if data.changes_urinary == 'yes' %}
    {% with ov_list=summary|confirm:"urinary_symptoms_present,urinary_symptoms_not_present,stated_other_symptoms_urinary" %}
    {% if ov_list %}
      <li><strong>Symptom overview</strong>
        <ul>
          {% for qa in ov_list %}
            {% if ":" in qa %}
              <li>{% autoescape off %}{{ qa|replace:": |: <strong>" }}{% endautoescape %}</strong></li>
            {% else %}
              <li>{{ qa|safe }}</li>
            {% endif %}
          {% endfor %}
        </ul>
      </li>
    {% endif %}
    {% endwith %}

    {# Consistency note if "None of the above" + any symptom selected #}
    {% if data.none_of_the_above_urinary_symptom_overview %}
      {% if uso %}
        {% if uso.dysuria or uso.frequency or uso.urgency or uso.nocturia or uso.bladder_pain or uso.hematuria or uso.odour or uso.other %}
          <li><strong>Consistency note</strong>
            <ul>
              <li>You selected <strong>None of the above</strong> as well as specific symptoms. Please review answers for accuracy.</li>
            </ul>
          </li>
        {% endif %}
      {% endif %}
    {% endif %}
  {% endif %}

  {# =================  Dysuria  ================= #}
  {% if uso %}
    {% if uso.dysuria %}
      {% with dysuria_list=summary|confirm:"symptom_start_psa_dysuria,psa_dysuria_quality,psa_dysuria_frequency,psa_dysuria_frequency_other" %}
      {% if dysuria_list %}
        <li><strong>Burning/discomfort while urinating (dysuria)</strong>
          <ul>
            {% for qa in dysuria_list %}
              {% if ":" in qa %}
                <li>{% autoescape off %}{{ qa|replace:": |: <strong>" }}{% endautoescape %}</strong></li>
              {% else %}
                <li>{{ qa|safe }}</li>
              {% endif %}
            {% endfor %}
          </ul>
        </li>
      {% endif %}
      {% endwith %}
    {% endif %}

    {# ============  Frequency / Urgency  ============ #}
    {% if uso.frequency or uso.urgency %}
      {% with freq_list=summary|confirm:"symptom_start_psa_frequency,psa_urgency_pattern" %}
      {% if freq_list %}
        <li><strong>Frequent or urgent urination</strong>
          <ul>
            {% for qa in freq_list %}
              {% if ":" in qa %}
                <li>{% autoescape off %}{{ qa|replace:": |: <strong>" }}{% endautoescape %}</strong></li>
              {% else %}
                <li>{{ qa|safe }}</li>
              {% endif %}
            {% endfor %}
          </ul>
        </li>
      {% endif %}
      {% endwith %}
    {% endif %}

    {# =================  Nocturia  ================= #}
    {% if uso.nocturia %}
      {% with noct_list=summary|confirm:"symptom_start_psa_nocturia,psa_nocturia_frequency_now,psa_nocturia_nights_per_week,psa_nocturia_change_over_time,psa_nocturia_triggers,psa_nocturia_triggers_other_text,psa_nocturia_tried_fluids,psa_nocturia_fluids_effect,psa_nocturia_alcohol_effect,psa_nocturia_change_recent" %}
      {% if noct_list %}
        <li><strong>Waking at night to urinate (nocturia)</strong>
          <ul>
            {% for qa in noct_list %}
              {% if "Typical nightly wake-ups:" in qa %}
                <li>Typical nightly wake-ups: <strong>{{ qa|cut:"Typical nightly wake-ups: " }}</strong></li>
              {% elif "Nights per week:" in qa %}
                <li>Nights per week: <strong>{{ qa|cut:"Nights per week: " }}</strong></li>
              {% elif ":" in qa %}
                <li>{% autoescape off %}{{ qa|replace:": |: <strong>" }}{% endautoescape %}</strong></li>
              {% else %}
                <li>{{ qa|safe }}</li>
              {% endif %}
            {% endfor %}
          </ul>
        </li>
      {% endif %}
      {% endwith %}
    {% endif %}

    {# ============  Bladder pain / suprapubic discomfort  ============ #}
    {% if uso.bladder_pain %}
      {% with bp_list=summary|confirm:"symptom_start_psa_bladder_pain,psa_bladder_pain_quality,psa_bladder_pain_severity,psa_bladder_pain_frequency,psa_bladder_pain_frequency_other,psa_bladder_pain_timing,psa_bladder_pain_location,psa_bladder_pain_location_other,psa_bladder_pain_triggers,psa_bladder_pain_relief" %}
      {% if bp_list %}
        <li><strong>Bladder pressure / lower abdominal pain</strong>
          <ul>
            {% for qa in bp_list %}
              {% if ":" in qa %}
                <li>{% autoescape off %}{{ qa|replace:": |: <strong>" }}{% endautoescape %}</strong></li>
              {% else %}
                <li>{{ qa|safe }}</li>
              {% endif %}
            {% endfor %}
          </ul>
        </li>
      {% endif %}
      {% endwith %}
    {% endif %}

    {# =================  Hematuria  ================= #}
    {% if uso.hematuria %}
      {% with hem_list=summary|confirm:"symptom_start_psa_hematuria,psa_hematuria_appearance,psa_hematuria_frequency,psa_hematuria_lab_confirm,psa_hematuria_exam_done,psa_hematuria_ultrasound,psa_hematuria_prior_dx,psa_hematuria_seen_specialist" %}
      {% if hem_list %}
        <li><strong>Blood in urine (hematuria)</strong>
          <ul>
            {% for qa in hem_list %}
              {% if ":" in qa %}
                <li>{% autoescape off %}{{ qa|replace:": |: <strong>" }}{% endautoescape %}</strong></li>
              {% else %}
                <li>{{ qa|safe }}</li>
              {% endif %}
            {% endfor %}
          </ul>
        </li>
      {% endif %}
      {% endwith %}
    {% endif %}

    {# ============  Urinary odour / other symptoms  ============ #}
    {% if uso.odour or uso.other %}
      {% with od_list=summary|confirm:"symptom_start_psa_odour,stated_other_symptoms_urinary" %}
      {% if od_list %}
        <li><strong>Urinary odour / other symptoms</strong>
          <ul>
            {% for qa in od_list %}
              {% if ":" in qa %}
                <li>{% autoescape off %}{{ qa|replace:": |: <strong>" }}{% endautoescape %}</strong></li>
              {% else %}
                <li>{{ qa|safe }}</li>
              {% endif %}
            {% endfor %}
          </ul>
        </li>
      {% endif %}
      {% endwith %}
    {% endif %}
  {% endif %}

</ul>
{% endwith %}

<!-- =================  PRIOR PSA TEST RESULTS (tabular)  ================= -->
{% with data=questionnaire.hpc.data %}
{% if data.prior_psa_ever == 'yes' and data.psa_results_grid %}
  <h5>Prior PSA Test Results</h5>
  <table style="width:100%; border-collapse:collapse; font-size:0.95rem;">
    <thead>
      <tr>
        <th style="text-align:left; border-bottom:1px solid #ddd; padding:6px 4px;">Date</th>
        <th style="text-align:left; border-bottom:1px solid #ddd; padding:6px 4px;">Assay type</th>
        <th style="text-align:left; border-bottom:1px solid #ddd; padding:6px 4px;">Value</th>
      </tr>
    </thead>
    <tbody>
      {% for row in data.psa_results_grid %}
        <tr>
          <!-- Date (Month + Year) -->
          <td style="padding:6px 4px; border-bottom:1px solid #f1f1f1;">
            {% if row.psa_month == 'unknown' %}Unknown{% elif row.psa_month == 'jan' %}January
            {% elif row.psa_month == 'feb' %}February
            {% elif row.psa_month == 'mar' %}March
            {% elif row.psa_month == 'apr' %}April
            {% elif row.psa_month == 'may' %}May
            {% elif row.psa_month == 'jun' %}June
            {% elif row.psa_month == 'jul' %}July
            {% elif row.psa_month == 'aug' %}August
            {% elif row.psa_month == 'sep' %}September
            {% elif row.psa_month == 'oct' %}October
            {% elif row.psa_month == 'nov' %}November
            {% elif row.psa_month == 'dec' %}December
            {% else %}—{% endif %}
            {% if row.psa_year %} {{ row.psa_year }}{% endif %}
          </td>

          <!-- Assay type -->
          <td style="padding:6px 4px; border-bottom:1px solid #f1f1f1;">
            {% if row.psa_type == 'total' %}Total PSA
            {% elif row.psa_type == 'free' %}Free PSA
            {% elif row.psa_type == 'unsure' %}Not sure
            {% else %}—{% endif %}
          </td>

          <!-- Value + Units (0 or empty => Unknown) -->
          <td style="padding:6px 4px; border-bottom:1px solid #f1f1f1;">
            {% if row.psa_value == 0 or row.psa_value == None %}
              Unknown
            {% else %}
              {{ row.psa_value|floatformat:2 }}
              {% if row.psa_units == 'ng_ml' %} ng/mL (µg/L)
              {% elif row.psa_units == 'ug_l' %} µg/L
              {% elif row.psa_units == 'mcg_l' %} mcg/L
              {% endif %}
            {% endif %}
          </td>
        </tr>
      {% endfor %}
    </tbody>
  </table>
{% endif %}

{# Optional consistency note if user said "No" but entered rows anyway #}
{% if data.prior_psa_ever == 'no' and data.psa_results_grid %}
  <p><em><strong>Note:</strong> You selected “No prior PSA testing” but entered prior results. Please review for accuracy.</em></p>
{% endif %}
{% endwith %}


<!-- Plan Section -->
<h2 style="text-align:center; font-weight:bold;">Plan</h2>

{# ----------  TESTS / INVESTIGATIONS ---------- #}
<h2>Lab Testing Requested</h2>

{% if insured or uninsured %}
  {% if insured %}
    <p><strong>Insured tests (OHIP-covered)</strong></p>
    <ul>
      {% for a in insured %}
        <li>{{ a.name }} – {{ a.test_type }}</li>
      {% endfor %}
    </ul>
  {% endif %}
  {% if uninsured %}
    <p><strong>Uninsured tests (private-pay)</strong></p>
    <ul>
      {% for a in uninsured %}
        <li>{{ a.name }} – {{ a.test_type }}</li>
      {% endfor %}
    </ul>
  {% endif %}
{% else %}
  <p>No diagnostic tests required at this stage.</p>
{% endif %}

<!-- rxts: prescription -->
{% if rxts %}
<p>Based on your provided medical history you qualify for anti-viral medication with:</p>
<ul>
  {% for rx in rxts %}
    <li>{{ rx.display_name }}</li>
  {% endfor %}
</ul>
{% endif %}


<!-- ================  PSA SHARED-DECISION SUMMARY  ================ -->
{% with data=questionnaire.hpc.data summary=questionnaire.raw_formio_summary %}
<h5>PSA Testing Advice</h5>
<ul>

  {# Understanding acknowledgements #}
  {% with ack_list=summary|confirm:"psa_purpose_ack,psa_benefit_ack,psa_harm_ack,psa_reco_ack" %}
  {% if ack_list %}
    <li><strong>Understanding confirmed</strong>
      <ul>
        {% for qa in ack_list %}
          {% if ":" in qa %}
            <li>{% autoescape off %}{{ qa|replace:": |: <strong>" }}{% endautoescape %}</strong></li>
          {% else %}
            <li>{{ qa|safe }}</li>
          {% endif %}
        {% endfor %}
      </ul>
    </li>
  {% endif %}
  {% endwith %}

  {# Knowledge gaps (if any "I do not understand") #}
  {% if data.psa_purpose_ack == false or data.psa_benefit_ack == false or data.psa_harm_ack == false or data.psa_reco_ack == false %}
    <li><strong>Knowledge gaps noted</strong>
      <ul>
        {% if data.psa_purpose_ack == false %}<li>Would like clarification on the <strong>purpose</strong> of PSA testing.</li>{% endif %}
        {% if data.psa_benefit_ack == false %}<li>Would like clarification on potential <strong>benefits</strong>.</li>{% endif %}
        {% if data.psa_harm_ack == false %}<li>Would like clarification on potential <strong>harms</strong> (false positives, biopsies, overdiagnosis, side effects).</li>{% endif %}
        {% if data.psa_reco_ack == false %}<li>Would like clarification on the current <strong>recommendations</strong>.</li>{% endif %}
      </ul>
    </li>
  {% endif %}

  {# Final decision #}
  {% with decision_list=summary|confirm:"psa_decision" %}
  {% if decision_list %}
    <li><strong>Screening decision</strong>
      <ul>
        {% for qa in decision_list %}
          {% if ":" in qa %}
            <li>{% autoescape off %}{{ qa|replace:": |: <strong>" }}{% endautoescape %}</strong></li>
          {% else %}
            <li>{{ qa|safe }}</li>
          {% endif %}
        {% endfor %}
      </ul>
    </li>
  {% endif %}
  {% endwith %}

</ul>
{% endwith %}


<p class="mt-3">
    <strong>Confirmation:</strong>
    By completing and submitting this intake, you confirm that the information provided is accurate to the best of your knowledge, <u>that you have read and understand all advice contained in the intake form</u>, and that you agree to seek emergency or in-person medical care if severe side-effects or new symptoms develop.
  </p>
<p>Best regards,<br>{{ doctor.name }}</p>
{% endwith %}