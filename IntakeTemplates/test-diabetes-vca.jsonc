{"components": [{"key": "diagnosis_diabetes", "type": "radio", "input": true, "label": "Have you been diagnosed with diabetes in the past?", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "diabetes_testing_contraindication", "label": "Are you currently experiencing any of the following symptoms:", "values": [{"label": "Chest pain or heaviness", "value": "chest_pain", "shortcut": ""}, {"label": "Shortness of breath", "value": "dyspnea", "shortcut": ""}, {"label": "Feel lightheaded, faint or unwell", "value": "presy<PERSON><PERSON>", "shortcut": ""}, {"label": "Arm or leg swelling", "value": "limb_swelling", "shortcut": ""}, {"label": "Heart palpitations (slow or fast heart beat)", "value": "palpitations", "shortcut": ""}, {"label": "Feel excessively thirsty", "value": "polydipsia", "shortcut": ""}, {"label": "Blurry vision", "value": "vision_changes", "shortcut": ""}, {"label": "Urinating more than normal", "value": "polyuria", "shortcut": ""}], "input": true, "inputType": "checkbox", "customConditional": "show = data.diagnosis_diabetes == 'yes';", "optionsLabelPosition": "right", "tableView": true, "type": "selectboxes"}, {"key": "no_diabetes_contraindications", "customClass": "mt-n3", "defaultValue": false, "input": true, "label": "None of the above", "tableView": true, "type": "checkbox", "customConditional": "show = data.diagnosis_diabetes == 'yes';", "validate": {"custom": "valid = !!data.no_diabetes_contraindications || !!_.some(_.values(data.diabetes_testing_contraindication));"}, "errors": {"custom": "required, or select a symptom."}}]}