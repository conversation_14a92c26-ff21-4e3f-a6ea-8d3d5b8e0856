{"components": [{"key": "heading_intro_title", "html": "<h1><strong>Acne Intake - Your Current Concerns</strong></h1><p>To expedite your care, please answer a few quick questions about your acne. Your answers help us confirm what type of acne you have and recommend the most effective treatment.</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "heading_reason_consult", "html": "</br><h4>Why You're Reaching Out About Your Acne Today</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "consult_reason", "type": "selectboxes", "input": true, "label": "I would like help with:", "inline": false, "values": [{"label": "Renewal of a previous acne prescription", "value": "renewal"}, {"label": "Treatment for my current breakout", "value": "current_flare"}, {"label": "Preventive maintenance to avoid future breakouts", "value": "maintenance"}, {"label": "Managing side-effects of my current treatment", "value": "side_effects"}, {"label": "Change in treatment options (e.g., switching medications)", "value": "change_options"}, {"label": "Other", "value": "other"}, {"label": "None of the above", "value": "none", "customClass": "mt-n3"}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "confirm_label": "Reasons for consultation:", "optionsLabelPosition": "right"}, {"key": "consult_reason_other", "type": "textarea", "input": true, "label": "Please tell us more:", "tableView": true, "autoExpand": false, "confirm_label": "Other reason:", "customConditional": "show = data.consult_reason && (data.consult_reason.other === true || data.consult_reason.none === true);"}, {"key": "acne_diagnosis_type", "data": {"values": [{"label": "Regular acne", "value": "acne_vulgaris"}, {"label": "Hormonal acne", "value": "hormonal_acne"}, {"label": "Cystic or nodulocystic acne", "value": "cystic_acne"}, {"label": "Perioral dermatitis (bumps around the mouth)", "value": "perioral_dermatitis"}, {"label": "<PERSON><PERSON> (told it might be rosa<PERSON>)", "value": "rosacea"}, {"label": "Folliculitis (ingrown-hair bumps)", "value": "folliculitis"}, {"label": "Other skin condition", "value": "other"}]}, "type": "select", "input": true, "label": "Which diagnosis were you given?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Diagnosis given:", "customConditional": "show = data.skin_diagnosis_known === 'yes';"}, {"key": "acne_diagnosis_type_other", "type": "textarea", "input": true, "label": "Please specify the diagnosis:", "tableView": true, "autoExpand": false, "confirm_label": "Other diagnosis:", "customConditional": "show = data.acne_diagnosis_type === 'other';"}, {"key": "diagnosing_provider", "type": "radio", "input": true, "label": "Who provided this diagnosis?", "inline": false, "values": [{"label": "Family doctor", "value": "family_doctor"}, {"label": "Dermatologist", "value": "dermatologist"}, {"label": "Nurse practitioner", "value": "nurse_practitioner"}, {"label": "<PERSON><PERSON><PERSON>", "value": "naturopath"}, {"label": "Self-diagnosis", "value": "self"}, {"label": "Other", "value": "other"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Diagnosing provider:", "customConditional": "show = data.skin_diagnosis_known === 'yes';", "optionsLabelPosition": "right"}, {"key": "diagnosing_provider_other", "type": "textarea", "input": true, "label": "Please specify who diagnosed you:", "tableView": true, "autoExpand": false, "confirm_label": "Other provider:", "customConditional": "show = data.diagnosing_provider === 'other';"}, {"key": "heading_acne_onset_course", "html": "</br><h4>Current Acne Details</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "acne_start_time", "data": {"values": [{"label": "< 1 week ago", "value": "duration_days_lt7"}, {"label": "1-2 weeks ago", "value": "duration_weeks_1_2"}, {"label": "2-4 weeks ago", "value": "duration_weeks_2_4"}, {"label": "1-3 months ago", "value": "duration_months_1_3"}, {"label": "3-6 months ago", "value": "duration_months_3_6"}, {"label": "> 6 months ago", "value": "duration_months_gt6"}, {"label": "I'm not sure", "value": "duration_unknown"}]}, "type": "select", "input": true, "label": "When did this breakout begin?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Breakout started:"}, {"key": "acne_first_or_repeat", "type": "radio", "input": true, "label": "Is this your first breakout of this kind, or have you had similar breakouts before?", "inline": false, "values": [{"label": "First time", "value": "first"}, {"label": "I've had similar breakouts before", "value": "repeat"}, {"label": "Not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "First or repeat:", "customConditional": "show = !!data.acne_start_time;", "optionsLabelPosition": "right"}, {"key": "acne_onset_speed", "type": "radio", "input": true, "label": "Did the breakout appear suddenly or build up gradually?", "inline": false, "values": [{"label": "<PERSON><PERSON>", "value": "sudden"}, {"label": "Gradual", "value": "gradual"}, {"label": "Unsure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Onset speed:", "customConditional": "show = !!data.acne_first_or_repeat;", "optionsLabelPosition": "right"}, {"key": "acne_course_since_onset", "data": {"values": [{"label": "Been getting better", "value": "better"}, {"label": "Been getting worse", "value": "worse"}, {"label": "Gone up and down", "value": "fluctuating"}, {"label": "Stayed about the same", "value": "same"}]}, "type": "select", "input": true, "label": "Since it began, has your acne mostly:", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Course since onset:", "customConditional": "show = !!data.acne_onset_speed;"}, {"key": "acne_frequency_year", "data": {"values": [{"label": "< 1 breakout", "value": "freq_year_lt1"}, {"label": "1-2 breakouts", "value": "freq_year_1_2"}, {"label": "3-6 breakouts", "value": "freq_year_3_6"}, {"label": "Monthly", "value": "freq_monthly"}, {"label": "Almost all the time", "value": "freq_constant"}, {"label": "I don't know", "value": "freq_unknown"}]}, "type": "select", "input": true, "label": "In a typical year, how many notable breakouts do you get?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Breakouts per year:", "customConditional": "show = !!data.acne_course_since_onset;"}, {"key": "acne_duration_typical", "data": {"values": [{"label": "A few days", "value": "few_days"}, {"label": "About 1 week", "value": "week_1"}, {"label": "2-4 weeks", "value": "weeks_2_4"}, {"label": "1-3 months", "value": "months_1_3"}, {"label": "More than 3 months", "value": "months_gt3"}, {"label": "I'm not sure", "value": "unknown"}]}, "type": "select", "input": true, "label": "When you have a breakout, how long does it usually last?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Typical breakout duration:", "customConditional": "show = data.acne_first_or_repeat === 'repeat' && !!data.acne_frequency_year;"}, {"key": "breakout_resolution_pattern", "type": "radio", "input": true, "label": "How do your breakouts usually get better?", "inline": false, "values": [{"label": "They only improve with prescription treatment", "value": "requires_prescription"}, {"label": "They improve faster with prescription treatment", "value": "faster_with_prescription"}, {"label": "They sometimes improve on their own", "value": "sometimes_spontaneous"}, {"label": "They usually improve on their own, even without meds", "value": "usually_spontaneous"}, {"label": "I'm not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Breakout resolution pattern:", "customConditional": "show = data.acne_duration_typical && data.acne_duration_typical !== 'unknown';", "optionsLabelPosition": "right"}, {"key": "otc_effectiveness", "type": "radio", "input": true, "label": "Do over-the-counter acne products help your breakouts?", "inline": false, "values": [{"label": "Yes - they usually help", "value": "yes_often"}, {"label": "Sometimes - depends on the product", "value": "sometimes"}, {"label": "No - they don't seem to help", "value": "no_effect"}, {"label": "I haven't really tried", "value": "not_tried"}], "validate": {"required": true}, "tableView": true, "confirm_label": "OTC product effect:", "customConditional": "show = data.breakout_resolution_pattern && data.breakout_resolution_pattern !== 'unsure';", "optionsLabelPosition": "right"}, {"key": "heading_severity_distribution", "html": "</br><h4>Severity &amp; Body Areas</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "tenderness_severity", "data": {"values": [{"label": "None / not painful", "value": "pain_none"}, {"label": "Mild", "value": "pain_mild"}, {"label": "Moderate", "value": "pain_moderate"}, {"label": "Severe", "value": "pain_severe"}]}, "type": "select", "input": true, "label": "How tender or painful are your pimples right now?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Tenderness severity:"}, {"key": "lesion_types_present", "type": "selectboxes", "input": true, "label": "Which of these do you see right now? (Select all that apply)", "inline": false, "values": [{"label": "<PERSON><PERSON>", "value": "whiteheads"}, {"label": "Blackheads", "value": "blackheads"}, {"label": "Red bumps (papules)", "value": "papules"}, {"label": "Pus-filled bumps (pustules)", "value": "pustules"}, {"label": "Large deep bumps (>5 mm) / cysts", "value": "nodules_cysts"}, {"label": "Dark marks / hyperpigmentation", "value": "dark_marks"}, {"label": "Indented or raised scars", "value": "scars"}, {"label": "Oily / shiny skin", "value": "oily_skin"}, {"label": "Other", "value": "other"}, {"label": "None of the above", "value": "none", "customClass": "mt-n3"}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "confirm_label": "Lesion types:", "customConditional": "show = !!data.tenderness_severity;", "optionsLabelPosition": "right"}, {"key": "inflammatory_count", "type": "number", "input": true, "label": "Roughly how many red or pus-filled bumps can you count on all areas today?", "suffix": "pus-filled bumps", "validate": {"min": 0, "required": true}, "tableView": true, "confirm_label": "Inflammatory lesion count:", "customConditional": "show = !!data.lesion_types_present;"}, {"key": "cysts_ever", "type": "radio", "input": true, "label": "At this moment, do you feel any sore, swollen bumps beneath the skin-cysts or nodules that are deeper and more painful than a usual pimple?", "inline": false, "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Unsure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Recent cysts:", "customConditional": "show = !!data.inflammatory_count || !!data.tenderness_severity;", "optionsLabelPosition": "right"}, {"key": "cysts_warning_box", "tag": "div", "type": "content", "input": false, "content": "<div style=\"border:1px solid #ffc107;background:#fff3cd;color:#664d03;border-radius:6px;padding:0.9rem;margin:0.75rem 0;\"><strong>You indicated you can feel sore, deep bumps under the skin (cysts / nodules).</strong><br>Are you certain these are larger, painful bumps that sit deeper than ordinary pimples?</div>", "customConditional": "show = data.cysts_ever === 'yes';"}, {"key": "cysts_confirm", "type": "radio", "input": true, "label": "Confirmation:", "inline": false, "values": [{"label": "Yes - they're definitely larger and painful cysts / nodules", "value": "confirm"}, {"label": "No - they aren't that painful", "value": "not_painful"}, {"label": "I'm not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Painful cysts confirmed:", "customConditional": "show = data.cysts_ever === 'yes';", "optionsLabelPosition": "right"}, {"key": "lesion_types_other", "type": "textarea", "input": true, "label": "Please describe other skin changes:", "tableView": true, "autoExpand": false, "confirm_label": "Other lesion types:", "customConditional": "show = data.lesion_types_present && data.lesion_types_present.other;"}, {"key": "heading_acne_area_face_neck", "html": "</br><h4>Face &amp; Neck</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "acne_site_face_group", "type": "selectboxes", "input": true, "label": "Where on your face/neck do you currently have acne?", "values": [{"label": "Forehead / T-zone", "value": "forehead_tzone"}, {"label": "Cheeks", "value": "cheeks"}, {"label": "Jawline / chin", "value": "jawline_chin"}, {"label": "Neck", "value": "neck"}], "tableView": true, "confirm_label": "Face / neck areas:", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_acne_site_face_group", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "Select at least one option or choose 'None of the above'."}, "validate": {"custom": "valid = !!data.none_of_the_above_acne_site_face_group || _.some(_.values(data.acne_site_face_group));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "acne_face_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have acne on the following face/neck areas:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "Face/neck areas not affected:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.acne_site_face_group, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "heading_acne_area_chest_shoulders", "html": "</br><h4>Chest &amp; Shoulders</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "acne_site_chest_shoulders", "type": "selectboxes", "input": true, "label": "Is your acne affecting any of these areas?", "values": [{"label": "Chest", "value": "chest"}, {"label": "Shoulders", "value": "shoulders"}], "tableView": true, "confirm_label": "Chest / shoulders affected:", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_acne_site_chest_shoulders", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "Select at least one option or choose 'None of the above'."}, "validate": {"custom": "valid = !!data.none_of_the_above_acne_site_chest_shoulders || _.some(_.values(data.acne_site_chest_shoulders));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "acne_chest_shoulders_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have acne on the following chest/shoulder areas:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "Chest/shoulders not affected:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.acne_site_chest_shoulders, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "heading_acne_area_back", "html": "</br><h4>Back &amp; Buttocks</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "acne_site_back_group", "type": "selectboxes", "input": true, "label": "Is your acne affecting any of these areas?", "values": [{"label": "Upper back", "value": "upper_back"}, {"label": "Mid back", "value": "mid_back"}, {"label": "Lower back", "value": "lower_back"}, {"label": "Buttocks", "value": "buttocks"}], "tableView": true, "confirm_label": "Back / buttocks affected:", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_acne_site_back_group", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "Select at least one option or choose 'None of the above'."}, "validate": {"custom": "valid = !!data.none_of_the_above_acne_site_back_group || _.some(_.values(data.acne_site_back_group));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "acne_back_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have acne on the following back/buttock areas:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "Back/buttocks not affected:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.acne_site_back_group, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "acne_distribution_other", "type": "textarea", "input": true, "label": "Any other area(s) with acne?", "tableView": true, "autoExpand": false, "confirm_label": "Other areas:", "customConditional": "show = (data.acne_site_face_group || data.acne_site_chest_shoulders || data.acne_site_back_group);"}, {"key": "heading_triggers_exacerbating_factors", "html": "</br><h4>Triggers &amp; Breakout Factors</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "environmental_triggers", "type": "selectboxes", "input": true, "label": "Which environmental factors seem to trigger or worsen your acne? (Select all that apply)", "inline": false, "values": [{"label": "Heat or sweating (sports, hot climate)", "value": "heat_sweat"}, {"label": "High humidity", "value": "humidity"}, {"label": "Dry/cold air", "value": "dry_air"}, {"label": "Occlusive face mask / helmet", "value": "occlusion_mask"}, {"label": "Pollution or oily work environment", "value": "pollution"}, {"label": "Chlorine pools or hot tubs", "value": "chlorine"}, {"label": "Sun exposure", "value": "sun"}, {"label": "Other", "value": "other"}, {"label": "None of the above", "value": "none", "customClass": "mt-n3"}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "confirm_label": "Environmental triggers:", "optionsLabelPosition": "right"}, {"key": "environmental_triggers_other", "type": "textarea", "input": true, "label": "Please specify other environmental triggers:", "tableView": true, "autoExpand": false, "confirm_label": "Other environmental triggers:", "customConditional": "show = data.environmental_triggers && data.environmental_triggers.other;"}, {"key": "lifestyle_triggers", "type": "selectboxes", "input": true, "label": "Which lifestyle or dietary factors make your acne worse? (Select all that apply)", "inline": false, "values": [{"label": "High-glycemic foods / sugary drinks", "value": "high_gi_diet"}, {"label": "Dairy (milk, cheese, whey protein)", "value": "dairy"}, {"label": "Chocolate", "value": "chocolate"}, {"label": "Alcohol use", "value": "alcohol"}, {"label": "Smoking / vaping", "value": "smoking"}, {"label": "Stress", "value": "stress"}, {"label": "Poor sleep", "value": "poor_sleep"}, {"label": "Hormonal changes (periods, contraception change)", "value": "hormonal_changes"}, {"label": "Other", "value": "other"}, {"label": "None of the above", "value": "none", "customClass": "mt-n3"}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "confirm_label": "Lifestyle triggers:", "customConditional": "show = data.environmental_triggers && Object.values(data.environmental_triggers).some(<PERSON><PERSON><PERSON>);", "optionsLabelPosition": "right"}, {"key": "lifestyle_triggers_other", "type": "textarea", "input": true, "label": "Please specify other lifestyle triggers:", "tableView": true, "autoExpand": false, "confirm_label": "Other lifestyle triggers:", "customConditional": "show = data.lifestyle_triggers && data.lifestyle_triggers.other;"}, {"key": "product_triggers", "type": "selectboxes", "input": true, "label": "Have any products or medications triggered your acne? (Select all that apply)", "inline": false, "values": [{"label": "Oil-based makeup or skincare", "value": "comedogenic_makeup"}, {"label": "Hair oils or pomades touching skin", "value": "hair_products"}, {"label": "Heavy occlusive sunscreens", "value": "sunscreen"}, {"label": "Anabolic steroids / testosterone", "value": "steroids"}, {"label": "Oral corticosteroids", "value": "oral_steroids"}, {"label": "Lithium or certain seizure meds", "value": "lithium_others"}, {"label": "Picking / squeezing lesions", "value": "picking"}, {"label": "Other", "value": "other"}, {"label": "None of the above", "value": "none", "customClass": "mt-n3"}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "confirm_label": "Product/medication triggers:", "customConditional": "show = data.lifestyle_triggers && Object.values(data.lifestyle_triggers).some(<PERSON><PERSON><PERSON>);", "optionsLabelPosition": "right"}, {"key": "product_triggers_other", "type": "textarea", "input": true, "label": "Please specify other product or medication triggers:", "tableView": true, "autoExpand": false, "confirm_label": "Other product triggers:", "customConditional": "show = data.product_triggers && data.product_triggers.other;"}, {"key": "abx_allergy", "type": "selectboxes", "input": true, "label": "Do you have any allergies or major side-effects to the following antibiotics?", "values": [{"label": "Doxycycline", "value": "doxy"}, {"label": "Minocycline", "value": "mino"}, {"label": "Clindamycin", "value": "clinda"}, {"label": "Erythromycin", "value": "ery"}, {"label": "Trimethoprim-sulfamethoxazole", "value": "tmp_smx"}], "adminFlag": true, "tableView": true, "confirm_label": "ABX Allergy:", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_abx_allergy", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select an antibiotic."}, "validate": {"custom": "valid = !!data.none_of_the_above_abx_allergy || _.some(_.values(data.abx_allergy));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "abx_allergy_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have allergies or major side-effects to the following antibiotics:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "Antibiotics NOT an issue:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.abx_allergy, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "trigger_avoidance_attempted", "type": "radio", "input": true, "label": "Have you tried avoiding any of the factors you selected?", "inline": false, "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Partially", "value": "partially"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Avoidance attempted:", "customConditional": "show = (data.environmental_triggers && Object.values(data.environmental_triggers).some(<PERSON><PERSON>an)) || (data.lifestyle_triggers && Object.values(data.lifestyle_triggers).some(Boolean)) || (data.product_triggers && Object.values(data.product_triggers).some(<PERSON><PERSON><PERSON>));", "optionsLabelPosition": "right"}, {"key": "trigger_avoidance_effect", "data": {"values": [{"label": "No change", "value": "no_change"}, {"label": "Slight improvement", "value": "slight_improvement"}, {"label": "Marked improvement", "value": "marked_improvement"}]}, "type": "select", "input": true, "label": "If you tried avoidance, what effect did it have?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Avoidance effect:", "customConditional": "show = data.trigger_avoidance_attempted === 'yes' || data.trigger_avoidance_attempted === 'partially';"}, {"key": "pregnancy_status", "type": "radio", "input": true, "label": "Are you currently…", "inline": false, "values": [{"label": "Pregnant", "value": "pregnant"}, {"label": "Breast-feeding", "value": "breastfeeding"}, {"label": "Trying / might be soon", "value": "ttc"}, {"label": "None of the above", "value": "no"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Pregnancy / BF status:", "customConditional": "show = data.sex === 'female';", "optionsLabelPosition": "right"}, {"key": "heading_hormonal_acne", "html": "</br><h4>Hormonal Patterns</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.sex == 'female';"}, {"key": "cycle_flare_pattern", "type": "radio", "input": true, "label": "Do your breakouts usually worsen in the week before your period?", "inline": false, "values": [{"label": "Yes - almost every cycle", "value": "yes_regular"}, {"label": "Sometimes / occasional", "value": "sometimes"}, {"label": "No noticeable pattern", "value": "no_pattern"}, {"label": "I don't have periods (e.g., IUD, menopause)", "value": "no_periods"}, {"label": "Unsure / irregular cycles", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Pre-period flare:", "customConditional": "show = data.sex == 'female';", "optionsLabelPosition": "right"}, {"key": "cycle_regularity", "data": {"values": [{"label": "Regular (21-35 days, predictable)", "value": "regular"}, {"label": "Irregular / varying >7 days", "value": "irregular"}, {"label": "No periods >3 months (not pregnant)", "value": "amenorrhea"}, {"label": "Using medication or IUD that stops periods", "value": "suppressed"}, {"label": "Menopausal / post-menopausal", "value": "menopause"}]}, "type": "select", "input": true, "label": "How regular are your menstrual cycles?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Cycle regularity:", "customConditional": "show = data.sex == 'female';"}, {"key": "hormonal_changes_duration", "data": {"values": [{"label": "New (within the last 12 months)", "value": "recent"}, {"label": "Long-standing (>1 year)", "value": "longstanding"}, {"label": "Unsure", "value": "unsure"}]}, "type": "select", "input": true, "label": "Are these cycle-related changes new or long-standing?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Changes present since:", "customConditional": "show = data.sex == 'female' && (data.cycle_regularity && data.cycle_regularity !== 'regular');"}, {"key": "pcos_previous_diagnosis", "type": "radio", "input": true, "label": "Have you ever been told you have polycystic ovary syndrome (PCOS)?", "inline": false, "values": [{"label": "Yes - confirmed by a doctor", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Unsure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Previous PCOS diagnosis:", "customConditional": "show = data.sex == 'female' && (data.cycle_regularity && ['irregular','amenorrhea'].includes(data.cycle_regularity));", "optionsLabelPosition": "right"}, {"key": "rec_box_cycle_change", "html": "<div class=\"alert alert-success\"><p><strong>Based on the period changes you've reported, an in-person check-up is a good next step.</strong><br>Your family doctor-or a nearby walk-in clinic-can arrange hormone blood tests and, if needed, a pelvic ultrasound to look for the underlying cause.</p></div>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.sex === 'female' && (data.cycle_regularity === 'irregular' || data.cycle_regularity === 'amenorrhea') && data.hormonal_changes_duration === 'recent';"}, {"key": "rec_ack_cycle_change", "type": "radio", "input": true, "label": "Do you understand this recommendation?", "inline": false, "values": [{"label": "Yes, I understand", "value": "understand"}, {"label": "No, please clarify", "value": "dont_understand"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Understanding of cycle-change advice:", "customConditional": "show = data.sex === 'female' && (data.cycle_regularity === 'irregular' || data.cycle_regularity === 'amenorrhea') && data.hormonal_changes_duration === 'recent';", "optionsLabelPosition": "right"}, {"key": "rec_box_cycle_change_clarify", "html": "<div class=\"alert alert-info\" style=\"border:1px solid #0dcaf0;background:#cff4fc;color:#055160;border-radius:6px;padding:0.9rem;margin:0.75rem 0;\"><p><strong>Here’s a bit more detail:</strong></p><ul><li>Irregular or missed periods can be related to conditions such as polycystic ovary syndrome (PCOS) or thyroid changes.</li><li>To find the cause, we usually do: <br>&nbsp;&nbsp;• a brief physical exam<br>&nbsp;&nbsp;• hormone blood tests<br>&nbsp;&nbsp;• a pelvic ultrasound</li><li>Depending on the results, you might also be referred to a gynecologist for follow-up.</li><li>If you still have questions, you can ask our doctor through a real-time messaging chat.</li></ul></div>", "type": "content", "input": false, "label": "Cycle-change clarification", "tableView": false, "clearOnHide": false, "customConditional": "show = data.rec_ack_cycle_change === 'dont_understand';"}, {"key": "androgen_signs", "type": "selectboxes", "input": true, "label": "Have you noticed any of these body changes? (Select all that apply)", "values": [{"label": "More coarse hair growing on my chin or upper lip", "value": "hirsutism"}, {"label": "Thinning hair on my scalp or a receding hairline", "value": "androgenic_alopecia"}, {"label": "Unexplained weight gain or finding it hard to lose weight", "value": "weight_gain"}, {"label": "Dark, velvety skin patches on my neck or underarms", "value": "acanthosis"}], "adminFlag": true, "tableView": true, "confirm_label": "Body changes noted:", "customConditional": "show = data.sex == 'female';", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_androgen_signs", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "Select at least one sign or choose 'None of the above'."}, "validate": {"custom": "valid = !!data.none_of_the_above_androgen_signs || _.some(_.values(data.androgen_signs));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.sex == 'female';"}, {"key": "current_hormonal_contraception", "data": {"values": [{"label": "Combined pill (estrogen + progestin)", "value": "combined_pill"}, {"label": "Progestin-only pill", "value": "pop"}, {"label": "Hormonal IUD (Mirena®, Kyleena®, etc.)", "value": "iud_hormonal"}, {"label": "Depot injection (Depo-Provera®)", "value": "depo"}, {"label": "Hormonal implant (Nexplanon®)", "value": "implant"}, {"label": "No hormonal contraception", "value": "none"}, {"label": "Prefer not to say", "value": "na"}]}, "type": "select", "input": true, "label": "Are you currently using any hormonal contraception?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Hormonal contraception:", "customConditional": "show = data.sex == 'female';"}, {"key": "heading_medication_triggers", "html": "</br><h4>Could a Medicine Be Fueling Your Acne?</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "med_trigger_info", "html": "<div class=\"alert alert-info\"><strong>Heads-up:</strong> Certain medications-such as some birth-control methods, steroid medicines, or other hormone-related treatments-can trigger new acne or make existing breakouts worse.<br>Please tell us about any medication you <u>started or changed <em>before</em> your acne first appeared or flared up</u>, even if that was years ago and you didn't notice a link.</div>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "med_trigger_any", "type": "radio", "input": true, "label": "Did you start or switch <strong>any</strong> medication before your current acne began or got noticeably worse?", "inline": false, "values": [{"label": "Yes - at least one", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Unsure / can't recall", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Possible medication trigger:", "optionsLabelPosition": "right"}, {"key": "med_trigger_categories_common", "type": "selectboxes", "input": true, "label": "Which of these medication types apply? (Select all that apply)", "inline": false, "values": [{"label": "Oral steroids (e.g., prednisone)", "value": "oral_steroid"}, {"label": "Steroid creams / ointments used on skin", "value": "topical_steroid"}, {"label": "Testosterone or anabolic steroids", "value": "androgen"}, {"label": "Seizure or mood-stabiliser medication", "value": "lithium_or_antiepileptic"}, {"label": "Other medication", "value": "other"}, {"label": "None of the above", "value": "none", "customClass": "mt-n3"}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "confirm_label": "Medication categories:", "customConditional": "show = data.med_trigger_any === 'yes' || data.med_trigger_any === 'unsure';", "optionsLabelPosition": "right"}, {"key": "med_trigger_categories_female", "type": "selectboxes", "input": true, "label": "Additional options for birth-control or hormone methods (select any that apply)", "inline": false, "values": [{"label": "Birth-control pill", "value": "hormonal_pill"}, {"label": "Hormonal IUD / injection / implant", "value": "hormonal_long"}], "validate": {"required": false}, "inputType": "checkbox", "tableView": true, "confirm_label": "Female-specific categories:", "customConditional": "show = (data.med_trigger_any === 'yes' || data.med_trigger_any === 'unsure') && data.sex === 'female';", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_med_trigger_categories_female", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "Select at least one option or choose 'None of the above'."}, "validate": {"custom": "valid = !!data.none_of_the_above_med_trigger_categories_female || _.some(_.values(data.med_trigger_categories_female));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = (data.med_trigger_any === 'yes' || data.med_trigger_any === 'unsure') && data.sex === 'female';"}, {"key": "med_trigger_hormonal_pill_sublist", "type": "selectboxes", "input": true, "label": "Which pill are/were you taking?", "values": [{"label": "Combined pill (estrogen + progestin)", "value": "combined"}, {"label": "Progestin-only pill (mini-pill)", "value": "pop"}], "inputType": "checkbox", "tableView": true, "confirm_label": "Pill type:", "customConditional": "show = data.med_trigger_categories_female && data.med_trigger_categories_female.hormonal_pill;", "optionsLabelPosition": "right"}, {"key": "med_trigger_hormonal_long_sublist", "type": "selectboxes", "input": true, "label": "Which long-acting method?", "values": [{"label": "Hormonal IUD (Mirena®, Kyleena®)", "value": "iud"}, {"label": "Depot injection (Depo-Provera®)", "value": "depo"}, {"label": "Hormonal implant (Nexplanon®)", "value": "implant"}], "inputType": "checkbox", "tableView": true, "confirm_label": "Long-acting method:", "customConditional": "show = data.med_trigger_categories_female && data.med_trigger_categories_female.hormonal_long;", "optionsLabelPosition": "right"}, {"key": "med_trigger_timing", "data": {"values": [{"label": "< 1 month", "value": "lt1mo"}, {"label": "1-3 months", "value": "1to3mo"}, {"label": "3-6 months", "value": "3to6mo"}, {"label": "> 6 months", "value": "gt6mo"}, {"label": "Unsure", "value": "unsure"}]}, "type": "select", "input": true, "label": "About how long <u>before</u> the acne worsened did you start the medication(s) you selected?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Timing before acne:", "customConditional": "show = (_.some(_.omit(data.med_trigger_categories_common,['none'])) || _.some(data.med_trigger_categories_female || {}));"}, {"key": "med_trigger_current_use", "type": "radio", "input": true, "label": "Are you still taking any of the medication(s) you selected?", "inline": false, "values": [{"label": "Yes - still taking", "value": "still_on"}, {"label": "No - stopped", "value": "stopped"}, {"label": "Some yes / some no", "value": "mixed"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Currently taking:", "customConditional": "show = (_.some(_.omit(data.med_trigger_categories_common,['none'])) || _.some(data.med_trigger_categories_female || {}));", "optionsLabelPosition": "right"}, {"key": "med_trigger_other_text", "type": "textarea", "input": true, "label": "Please tell us the name of the other medication:", "tableView": true, "autoExpand": false, "confirm_label": "Other medication:", "customConditional": "show = data.med_trigger_categories_common && data.med_trigger_categories_common.other;"}, {"key": "rec_box_long_acting", "html": "<div class=\"alert alert-success\"><p><strong>This contraceptive method can contribute to acne.</strong><br>Stopping or switching to a non-hormonal option (e.g., copper IUD) or a combined estrogen + progestin pill often settles breakouts and can reduce the need for acne medication.<br><br>Please discuss these options with your family doctor or prescriber.</p></div>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.sex === 'female' && data.med_trigger_categories_female && data.med_trigger_categories_female.hormonal_long;"}, {"key": "rec_ack_long_acting", "type": "radio", "input": true, "label": "Do you understand this recommendation?", "inline": false, "values": [{"label": "Yes, I understand", "value": "understand"}, {"label": "No, please clarify", "value": "dont_understand"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Understanding of long-acting advice:", "customConditional": "show = data.sex === 'female' && data.med_trigger_categories_female && data.med_trigger_categories_female.hormonal_long;", "optionsLabelPosition": "right"}, {"key": "rec_box_pop", "html": "<div class=\"alert alert-success\"><p><strong>Progestin-only pills can worsen acne.</strong><br>Switching to an acne-friendly combined pill such as <em>Yaz®</em> or <em>Diane-35®</em> often improves skin and may lessen or eliminate the need for separate acne treatments.<br><br>Please discuss this switch with your family doctor or prescriber.</p></div>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.sex === 'female' && data.med_trigger_categories_female && data.med_trigger_categories_female.hormonal_pill && data.med_trigger_hormonal_pill_sublist && data.med_trigger_hormonal_pill_sublist.pop;"}, {"key": "rec_ack_pop", "type": "radio", "input": true, "label": "Do you understand this recommendation?", "inline": false, "values": [{"label": "Yes, I understand", "value": "understand"}, {"label": "No, please clarify", "value": "dont_understand"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Understanding of pill-switch advice:", "customConditional": "show = data.sex === 'female' && data.med_trigger_categories_female && data.med_trigger_categories_female.hormonal_pill && data.med_trigger_hormonal_pill_sublist && data.med_trigger_hormonal_pill_sublist.pop;", "optionsLabelPosition": "right"}, {"key": "heading_otc_skin_care_routine", "html": "</br><h4>Daily Skin Care &amp; Non-Prescription Acne Products</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "uses_face_cleanser", "type": "radio", "input": true, "label": "Do you wash your face with a cleanser?", "inline": false, "values": [{"label": "Yes, twice daily", "value": "twice_daily"}, {"label": "Yes, once daily", "value": "once_daily"}, {"label": "Yes, a few times/week", "value": "few_week"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Cleansing routine:", "optionsLabelPosition": "right"}, {"key": "cleanser_type", "data": {"values": [{"label": "Gentle non-medicated (e.g., CeraVe, Cetaphil)", "value": "gentle"}, {"label": "Benzoyl peroxide wash", "value": "bpo"}, {"label": "Salicylic acid wash", "value": "salicylic"}, {"label": "Sulfur wash", "value": "sulfur"}, {"label": "Other / unsure", "value": "other"}]}, "type": "select", "input": true, "label": "What kind of cleanser do you usually use?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Cleanser type:", "customConditional": "show = data.uses_face_cleanser !== 'no';"}, {"key": "uses_noncomedogenic_moisturizer", "type": "radio", "input": true, "label": "Do you use a <em>non-comedogenic</em> (won't-clog-pores) moisturizer?", "inline": false, "values": [{"label": "Yes, every morning &amp; night", "value": "twice_daily"}, {"label": "Yes, once daily", "value": "once_daily"}, {"label": "Occasionally", "value": "sometimes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Moisturizer use:", "optionsLabelPosition": "right"}, {"key": "uses_sunscreen", "type": "radio", "input": true, "label": "Do you apply an oil-free sunscreen on your face?", "inline": false, "values": [{"label": "Yes, daily", "value": "daily"}, {"label": "Yes, but not every day", "value": "sometimes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Sunscreen use:", "optionsLabelPosition": "right"}, {"key": "uses_makeup", "type": "radio", "input": true, "label": "Do you wear facial make-up or tinted products?", "inline": false, "values": [{"label": "Daily", "value": "daily"}, {"label": "Sometimes", "value": "sometimes"}, {"label": "Rarely / never", "value": "never"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Make-up use:", "optionsLabelPosition": "right"}, {"key": "makeup_noncomedogenic", "type": "radio", "input": true, "label": "Is your make-up labelled \"non-comedogenic\" or \"oil-free\"?", "inline": false, "values": [{"label": "Yes", "value": "yes"}, {"label": "No / not sure", "value": "no"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Non-comedogenic make-up:", "customConditional": "show = data.uses_makeup === 'daily' || data.uses_makeup === 'sometimes';", "optionsLabelPosition": "right"}, {"key": "photo_upload_header", "html": "</br><h2>Photo Upload</h2><p>Please upload a clear photo of <strong>each affected area</strong>. Our doctors <em>require</em> these images in order to prescribe treatment. Good lighting is helpful.</p>", "type": "content", "input": false, "label": "Content"}, {"key": "uploadUrl", "url": "/app/q/{qpk}/formio-files/{pk}/", "type": "file", "image": true, "input": true, "label": "Upload: URL", "webcam": true, "capture": "user", "storage": "url", "multiple": true, "fileTypes": [{"label": "", "value": ""}], "tableView": true, "validateWhenHidden": false}, {"key": "photo_confirm_face", "type": "checkbox", "input": true, "label": "I've uploaded at least one clear photo of my face / neck acne.", "validate": {"required": true}, "tableView": false, "customConditional": "show = _.some(_.values(data.acne_site_face_group || {}));"}, {"key": "photo_confirm_chest", "type": "checkbox", "input": true, "label": "I've uploaded at least one clear photo of my chest / shoulder acne.", "validate": {"required": true}, "tableView": false, "customConditional": "show = _.some(_.values(data.acne_site_chest_shoulders || {}));"}, {"key": "photo_confirm_back", "type": "checkbox", "input": true, "label": "I've uploaded at least one clear photo of my back acne.", "validate": {"required": true}, "tableView": false, "customConditional": "show = _.some(_.values(data.acne_site_back_group || {}));"}, {"key": "intake_denial_reasons", "type": "textfield", "input": true, "label": "Intake Denial Reasons:", "hidden": true, "disabled": true, "multiple": true, "adminFlag": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "value = [];", "refreshOnChange": true}, {"key": "intake_kind", "type": "textfield", "input": true, "label": "Intake Kind", "hidden": true, "disabled": true, "tableView": true, "clearOnHide": false, "calculateValue": "value = !data.intake_denial_reasons?.length ? 'mail' : 'chat';"}, {"key": "intake_mt_key", "type": "textfield", "input": true, "label": "Intake Mail Template Key", "hidden": true, "disabled": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.intake_kind=='mail'?'intake-acne':'intake-denial'"}, {"key": "allowed_rt_keys", "type": "textfield", "input": true, "label": "Allowed Rep<PERSON>", "hidden": true, "disabled": true, "multiple": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.intake_mt_key=='intake-denial'?[]:['get-req','get-rx','appointment-intake','edit-intake']"}]}