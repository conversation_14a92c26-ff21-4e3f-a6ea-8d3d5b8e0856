{"components": [{"key": "prior_heartburn", "type": "radio", "input": true, "label": "Have you previously been told by a doctor or nurse that you have persistent heartburn or acid reflux?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "red_flag_symptoms", "type": "selectboxes", "input": true, "label": "Are you currently experiencing any of the following?", "values": [{"label": "Severe or persistent chest discomfort", "value": "chest_pain"}, {"label": "Trouble breathing", "value": "shortness_breath"}, {"label": "Difficulty swallowing", "value": "difficulty_swallowing"}, {"label": "Significant unexplained weight change", "value": "weight_loss"}, {"label": "Throwing up blood", "value": "vomiting_blood"}, {"label": "Dark or tarry stool", "value": "dark_stool"}, {"label": "Continuing or severe nausea/vomiting", "value": "persistent_vomiting"}, {"label": "Low blood count (told by a doctor)", "value": "low_blood_count"}, {"label": "Severe or ongoing upper belly pain", "value": "severe_upper_pain"}, {"label": "A feeling like there’s a lump in your throat", "value": "lump_in_throat"}, {"label": "Persistent cough or hoarseness", "value": "chronic_cough"}], "inputType": "checkbox", "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = data.prior_heartburn === 'yes';"}, {"key": "no_red_flag_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "Please select any issues or confirm none apply."}, "validate": {"custom": "valid = !!data.no_red_flag_symptoms || !!_.some(_.values(data.red_flag_symptoms));"}, "tableView": true, "defaultValue": false, "customClass": "mt-n3", "customConditional": "show = data.prior_heartburn === 'yes';"}, {"key": "no_indications", "type": "textfield", "input": true, "label": "No Indications:", "hidden": true, "disabled": true, "multiple": false, "tableView": true, "clearOnHide": false, "defaultValue": true, "calculateValue": "value = (data.prior_heartburn === 'yes' && !_.some(_.values(data.red_flag_symptoms))) ? false : true;", "refreshOnChange": true}, {"key": "contraindication_keys", "type": "textfield", "input": true, "label": "Contraindication keys:", "hidden": true, "disabled": true, "multiple": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "value = (data.prior_heartburn === 'no' || !!_.some(_.values(data.red_flag_symptoms))) ? ['contraindication_present'] : [];", "refreshOnChange": true}, {"key": "rejected_html", "type": "content", "input": true, "tableView": false, "clearOnHide": false, "defaultValue": "<h3 class='text-red'>You’re ineligible to continue online at this time. Please see a doctor or nurse in person for more help.</h3>", "refreshOnChange": true}]}