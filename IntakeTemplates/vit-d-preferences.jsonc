{"components": [{"key": "vitd_preferences_header", "type": "content", "input": false, "label": "Content", "html": "<h2>Vitamin D - Supplementation or Testing</h2><p>Please choose how you’d like to proceed.</p>", "tableView": false}, {"key": "vitd_plan_choice", "type": "radio", "input": true, "label": "Which option best suits you?", "confirm_label": "Vitamin D Plan:", "values": [{"label": "Vitamin D testing only", "value": "testing_only"}, {"label": "Treatment only - Prescription Vitamin D", "value": "treatment_only"}, {"label": "Testing and treatment based on results", "value": "testing_if_low"}], "validate": {"required": true}, "tableView": true}, {"key": "vitd_treatment_header", "type": "content", "input": false, "label": "Content", "customConditional": "show = data.vitd_plan_choice === 'treatment_only';", "html": "<h3>Choose your prescription Vitamin D regimen</h3><p>Both options are safe and effective. The best choice depends on your recent levels and how you prefer to take supplements:</p><table border='1' cellpadding='6' cellspacing='0' style='border-collapse:collapse;width:100%;margin-top:10px;'><thead style='background-color:#f2f2f2;'><tr><th>Option</th><th>When it’s preferred</th></tr></thead><tbody><tr><td><strong>50,000 IU once weekly for 8 weeks</strong></td><td>- If your recent Vitamin D level was low<br>- If you want to raise levels more quickly</td></tr><tr><td><strong>50,000 IU once monthly for 12 months</strong></td><td>- If you need a long-term maintenance dose<br>- If you tend to forget daily over-the-counter supplements</td></tr></tbody></table>"}, {"key": "vitd_treatment_regimen", "type": "radio", "input": true, "label": "Preferred regimen:", "confirm_label": "Vitamin D treatment regimen:", "values": [{"label": "50,000 IU once monthly x 12 months (standard maintenance)", "value": "50000_monthly_12mo"}, {"label": "50,000 IU once weekly x 8 weeks", "value": "50000_weekly_8w"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.vitd_plan_choice === 'treatment_only';"}, {"key": "rxts", "type": "textfield", "input": true, "label": "Rx Templates:", "hidden": true, "disabled": true, "multiple": true, "adminFlag": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "value = (data.vitd_plan_choice === 'treatment_only' && data.vitd_treatment_regimen === '50000_monthly_12mo') ? ['vit-d-50000-monthly-12mo'] : (data.vitd_plan_choice === 'treatment_only' && data.vitd_treatment_regimen === '50000_weekly_8w') ? ['vit-d-50000-weekly-8-weeks'] : value;", "refreshOnChange": true}, {"key": "rxts", "type": "textfield", "input": true, "label": "Rx Templates:", "hidden": true, "disabled": true, "multiple": true, "adminFlag": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "value = (data.vitd_treatment_regimen === '50000_monthly_12mo') ? ['vit-d-50000-monthly'] : (data.vitd_treatment_regimen === '50000_weekly_8w' ? ['vit-d-50000-weekly-8-weeks'] : []);", "refreshOnChange": true}, {"key": "has_additional_questions", "type": "radio", "input": true, "label": "Do you have any additional questions for the doctor before testing?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "additional_questions", "type": "textarea", "input": true, "label": "Please include any questions here:", "tableView": true, "autoExpand": false, "customConditional": "show = data.has_additional_questions;"}]}