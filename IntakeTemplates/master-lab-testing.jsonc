{"components": [{"key": "content", "html": "<div style='text-align: center;'><h2>Diabetes Intake Form</h2><p>We kindly ask you to complete the following questionnaire to help us better manage your diabetes.</p></div>", "type": "content", "input": false, "label": "Instructions", "tableView": false, "refreshOnChange": false}, {"key": "basic_history_header", "type": "content", "html": "<h3>Diabetes History</h3>", "input": false, "label": "Basic History", "tableView": false, "refreshOnChange": false}, {"key": "diabetes_diagnosis", "type": "radio", "input": true, "label": "Have you been diagnosed with diabetes?", "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "diabetes_type", "type": "radio", "input": true, "label": "What type of diabetes have you been diagnosed with?", "values": [{"label": "Type 1", "value": "type_1", "shortcut": ""}, {"label": "Type 2", "value": "type_2", "shortcut": ""}, {"label": "Gestational Diabetes", "value": "gestational", "shortcut": ""}, {"label": "Pre-Diabetes", "value": "pre-diabetes", "shortcut": ""}, {"label": "PCOS", "value": "pcos", "shortcut": ""}, {"label": "I don't know", "value": "doesnt_know", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.diabetes_diagnosis === 'yes'"}, {"key": "diagnosis_age", "type": "number", "input": true, "label": "At what age were you diagnosed?", "validate": {"required": true}, "tableView": true, "customConditional": "show = !!data.diabetes_type"}, {"key": "diabetes_management_followup", "type": "selectboxes", "input": true, "label": "Do you have a medical professional that follows for your diabetes?", "values": [{"label": "I have a medical professional in Canada", "value": "professional_in_canada", "shortcut": ""}, {"label": "I have a medical professional outside of Canada", "value": "professional_outside_canada", "shortcut": ""}, {"label": "I don't have follow-up with any medical professional", "value": "no_follow_up", "shortcut": ""}], "tableView": true, "customConditional": "show = !!data.diagnosis_age", "optionsLabelPosition": "right"}, {"key": "medication_renewals", "type": "selectboxes", "input": true, "label": "Who do you see for your medication renewals?", "values": [{"label": "Family doctor", "value": "family_doctor", "shortcut": ""}, {"label": "Endocrinologist/Internist", "value": "endocrinologist_internist", "shortcut": ""}, {"label": "Nurse practitioner", "value": "nurse_practitioner", "shortcut": ""}, {"label": "Walk-in Clinic Physician", "value": "walk_in_clinic", "shortcut": ""}, {"label": "I'm not currently on medication", "value": "not_currently_on_medication", "shortcut": ""}, {"label": "Virtual Care Physician", "value": "virtual_care_physician", "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}], "tableView": true, "customConditional": "show = !!data.diabetes_management_followup && !data.diabetes_management_followup.no_follow_up", "optionsLabelPosition": "right"}, {"key": "other_medication_renewal", "type": "textfield", "input": true, "label": "Please specify other professional for medication renewals", "tableView": true, "customConditional": "show = !!data.medication_renewals && data.medication_renewals.other"}, {"key": "diabetes_management_header", "type": "content", "html": "<h3>Lab Testing</h3>", "input": false, "label": "Diabetes Management", "tableView": false, "refreshOnChange": false}, {"key": "last_hba1c_test", "type": "select", "input": true, "label": "When was your last <strong>HbA1c</strong> test?", "data": {"values": [{"label": "I don't remember", "value": "doesnt_remember"}, {"label": "Never had one", "value": "never_had"}, {"label": "<1 week ago", "value": "less_1_week"}, {"label": "2-4 weeks ago", "value": "2-4_weeks_ago"}, {"label": "4 weeks - 6 weeks ago", "value": "4-6_weeks"}, {"label": "6 weeks - 3 months ago", "value": "6_weeks-3_months"}, {"label": "3-6 months", "value": "3-6_months"}, {"label": "6-12 months", "value": "6-12_months"}, {"label": "12-24 months", "value": "12-24_months"}, {"label": "24+ months", "value": "24+_months"}]}, "widget": "html5", "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "last_hba1c_value", "type": "select", "input": true, "label": "What was your last A1c value?", "data": {"values": [{"label": "I don't know", "value": "doesn't_know"}, {"label": "<5.5%", "value": "less_5.5"}, {"label": "5.5%", "value": "5.5"}, {"label": "5.6%", "value": "5.6"}, {"label": "5.7%", "value": "5.7"}, {"label": "5.8%", "value": "5.8"}, {"label": "5.9%", "value": "5.9"}, {"label": "6.0%", "value": "6.0"}, {"label": "6.1%", "value": "6.1"}, {"label": "6.2%", "value": "6.2"}, {"label": "6.3%", "value": "6.3"}, {"label": "6.4%", "value": "6.4"}, {"label": "6.5%", "value": "6.5"}, {"label": "6.6%", "value": "6.6"}, {"label": "6.7%", "value": "6.7"}, {"label": "6.8%", "value": "6.8"}, {"label": "6.9%", "value": "6.9"}, {"label": "7.0%", "value": "7.0"}, {"label": "7.1%", "value": "7.1"}, {"label": "7.2%", "value": "7.2"}, {"label": "7.3%", "value": "7.3"}, {"label": "7.4%", "value": "7.4"}, {"label": "7.5%", "value": "7.5"}, {"label": "7.6%", "value": "7.6"}, {"label": "7.7%", "value": "7.7"}, {"label": "7.8%", "value": "7.8"}, {"label": "7.9%", "value": "7.9"}, {"label": "8.0%", "value": "8.0"}, {"label": "8.1%", "value": "8.1"}, {"label": "8.2%", "value": "8.2"}, {"label": "8.3%", "value": "8.3"}, {"label": "8.4%", "value": "8.4"}, {"label": "8.5%", "value": "8.5"}, {"label": "8.6%", "value": "8.6"}, {"label": "8.7%", "value": "8.7"}, {"label": "8.8%", "value": "8.8"}, {"label": "8.9%", "value": "8.9"}, {"label": "9.0%", "value": "9.0"}, {"label": "9.1%", "value": "9.1"}, {"label": "9.2%", "value": "9.2"}, {"label": "9.3%", "value": "9.3"}, {"label": "9.4%", "value": "9.4"}, {"label": "9.5%", "value": "9.5"}, {"label": "9.6%", "value": "9.6"}, {"label": "9.7%", "value": "9.7"}, {"label": "9.8%", "value": "9.8"}, {"label": "9.9%", "value": "9.9"}, {"label": "10.0%", "value": "10.0"}, {"label": "10.1%", "value": "10.1"}, {"label": "10.2%", "value": "10.2"}, {"label": "10.3%", "value": "10.3"}, {"label": "10.4%", "value": "10.4"}, {"label": "10.5%+", "value": "10.5_plus"}]}, "widget": "html5", "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = data.last_hba1c_test && data.last_hba1c_test !== 'doesn't_remember'"}, {"key": "previous_cholesterol_testing", "type": "radio", "input": true, "label": "Have you had cholesterol testing in the past?", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "I Don't Know", "value": "i_dont_know", "shortcut": ""}], "tableView": true, "optionsLabelPosition": "right"}, {"key": "last_cholesterol_test", "data": {"values": [{"label": "< 3 months ago", "value": "<3months"}, {"label": "3-6 months ago", "value": "3-6 months ago"}, {"label": "6-12 months ago", "value": "6-12 months ago"}, {"label": "1 - 2 years ago", "value": "1-2 years ago"}, {"label": "2 - 3 years ago", "value": "2-3 years ago"}, {"label": "3-5 years ago", "value": "3-5 years ago"}, {"label": "More than 5 years ago", "value": "5+_years"}]}, "type": "select", "input": true, "label": "When did you have your last cholesterol testing completed outside of using TeleTest?", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.previous_cholesterol_testing == 'yes';", "optionsLabelPosition": "right"}, {"key": "previous_abnormal_lipids", "type": "radio", "input": true, "label": "Do you recall if you your previous cholesterol testing was abnormal (i.e. high or borderline high cholesterol)?", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "I Don't Know", "value": "doesn't_know", "shortcut": ""}], "tableView": true, "customConditional": "show = data.previous_cholesterol_testing == 'yes' && !!data.last_cholesterol_test;", "optionsLabelPosition": "right"}, {"key": "select_abnormal_cholesterol_values", "type": "selectboxes", "input": true, "label": "Please check any of the following apply to your prior tests: </br><li>HDL = Good Cholesterol</li><li>LDL = Bad Cholesterol</li><li>Triglycerides = Fat in Blood</li></br>", "values": [{"label": "High LDL", "value": "high_ldl", "shortcut": ""}, {"label": "Low LDL", "value": "low_ldl", "shortcut": ""}, {"label": "High HDL", "value": "high_hdl", "shortcut": ""}, {"label": "Low HDL", "value": "low_hdl", "shortcut": ""}, {"label": "High Triglycerides", "value": "high_tg", "shortcut": ""}, {"label": "Low Triglycerides", "value": "low_tg", "shortcut": ""}, {"label": "I Don't Know", "value": "doesn't know", "shortcut": ""}], "inputType": "checkbox", "tableView": true, "customConditional": "show = data.previous_abnormal_lipids == 'yes';", "optionsLabelPosition": "right"}, {"key": "previous_lipoprotein_a", "type": "radio", "input": true, "label": "Have you had a blood test for Lipoprotein(a) in the past?", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "I Don't Know", "value": "i_dont_know", "shortcut": ""}], "tableView": true, "optionsLabelPosition": "right"}, {"key": "last_kidney_function_test", "type": "select", "input": true, "label": "When was your last <strong>kidney function</strong> test?", "data": {"values": [{"label": "Never had one", "value": "never_had"}, {"label": "<1 week ago", "value": "less_1_week"}, {"label": "2-4 weeks ago", "value": "2-4_weeks_ago"}, {"label": "4 weeks - 6 weeks ago", "value": "4-6_weeks"}, {"label": "6 weeks - 3 months ago", "value": "6_weeks-3_months"}, {"label": "3-6 months", "value": "3-6_months"}, {"label": "6-12 months", "value": "6-12_months"}, {"label": "12-24 months", "value": "12-24_months"}, {"label": "24+ months", "value": "24+_months"}]}, "widget": "html5", "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "last_kidney_function_test_result", "type": "select", "input": true, "label": "What were the results of your last <strong>kidney function</strong> test?", "data": {"values": [{"label": "Not sure", "value": "not_sure"}, {"label": "eGFR > 90", "value": "egfr_above_90"}, {"label": "eGFR 85-89", "value": "egfr_85_89"}, {"label": "eGFR 80-84", "value": "egfr_80_84"}, {"label": "eGFR 75-79", "value": "egfr_75_79"}, {"label": "eGFR 70-74", "value": "egfr_70_74"}, {"label": "eGFR 65-69", "value": "egfr_65_69"}, {"label": "eGFR 60-64", "value": "egfr_60_64"}, {"label": "eGFR 55-59", "value": "egfr_55_59"}, {"label": "eGFR 50-54", "value": "egfr_50_54"}, {"label": "eGFR 45-49", "value": "egfr_45_49"}, {"label": "eGFR 40-44", "value": "egfr_40_44"}, {"label": "eGFR 35-39", "value": "egfr_35_39"}, {"label": "eGFR 30-34", "value": "egfr_30_34"}, {"label": "eGFR 25-29", "value": "egfr_25_29"}, {"label": "eGFR 20-24", "value": "egfr_20_24"}, {"label": "eGFR 15-19", "value": "egfr_15_19"}, {"label": "eGFR < 15", "value": "egfr_below_15"}]}, "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.last_kidney_function_test && data.last_kidney_function_test !== 'never_had'"}, {"key": "physical_exam_header", "type": "content", "html": "<h3>Physical Exam</h3>", "input": false, "label": "Physical Exam", "tableView": false, "refreshOnChange": false}, {"key": "last_heart_lungs_bp_exam", "type": "select", "input": true, "label": "When was the last time a doctor listened to your heart, lungs and examined your blood pressure?", "data": {"values": [{"label": "<1 month ago", "value": "less_1_month"}, {"label": "1-3 months ago", "value": "1_3_months"}, {"label": "3-6 months ago", "value": "3_6_months"}, {"label": "6-12 months ago", "value": "6_12_months"}, {"label": "1-2 years ago", "value": "1_2_years"}, {"label": "2+ years ago", "value": "2_plus_years"}, {"label": "I don't remember", "value": "doesn't_remember"}, {"label": "I have never had this done", "value": "never_had"}]}, "widget": "html5", "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "diabetic_eye_exam", "type": "radio", "input": true, "label": "Have you ever had a diabetic eye exam (different than your usual vision eye-check up)?", "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = !!data.last_heart_lungs_bp_exam", "optionsLabelPosition": "right"}, {"key": "last_diabetic_eye_exam", "type": "select", "input": true, "label": "When was your last diabetic eye exam?", "data": {"values": [{"label": "<1 year ago", "value": "less_1_year"}, {"label": "1-2 years ago", "value": "1_2_years_ago"}, {"label": "2-5 years ago", "value": "2_5_years_ago"}, {"label": "5+ years ago", "value": "more_5_years_ago"}]}, "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.diabetic_eye_exam === 'yes'"}, {"key": "diabetic_changes_identified", "type": "radio", "input": true, "label": "Do you recall if you had any diabetic changes identified on the exam?", "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "Not sure", "value": "not_sure", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = !!data.last_diabetic_eye_exam"}, {"key": "home_monitoring_header", "type": "content", "html": "<h3>Home Monitoring</h3>", "input": false, "label": "Home Monitoring", "tableView": false, "refreshOnChange": false}, {"key": "monitor_blood_pressure", "type": "radio", "input": true, "label": "Do you regularly monitor your blood pressure at home?", "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "monitor_blood_pressure_frequency", "type": "radio", "input": true, "label": "If yes, how frequently do you monitor your blood pressure?", "values": [{"label": "Daily", "value": "daily", "shortcut": ""}, {"label": "A few times per week", "value": "few_times_per_week", "shortcut": ""}, {"label": "Once a week", "value": "once_a_week", "shortcut": ""}, {"label": "Once a month", "value": "once_a_month", "shortcut": ""}, {"label": "Rarely", "value": "rarely", "shortcut": ""}, {"label": "Never", "value": "never", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.monitor_blood_pressure === 'yes'"}, {"key": "systolic_blood_pressure_range", "type": "select", "input": true, "label": "Systolic (top number) blood pressure range (i.e. Blood pressure is written as 135/75 - your Systolic is '135')", "data": {"values": [{"label": "Below 90", "value": "below_90"}, {"label": "90-94", "value": "90_94"}, {"label": "95-99", "value": "95_99"}, {"label": "100-104", "value": "100_104"}, {"label": "105-109", "value": "105_109"}, {"label": "110-114", "value": "110_114"}, {"label": "115-119", "value": "115_119"}, {"label": "120-124", "value": "120_124"}, {"label": "125-129", "value": "125_129"}, {"label": "130-134", "value": "130_134"}, {"label": "135-139", "value": "135_139"}, {"label": "140-144", "value": "140_144"}, {"label": "145-149", "value": "145_149"}, {"label": "150-154", "value": "150_154"}, {"label": "155-159", "value": "155_159"}, {"label": "160-164", "value": "160_164"}, {"label": "165-169", "value": "165_169"}, {"label": "170-174", "value": "170_174"}, {"label": "175-179", "value": "175_179"}, {"label": "180+", "value": "180_plus"}]}, "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.monitor_blood_pressure === 'yes'"}, {"key": "diastolic_blood_pressure_range", "type": "select", "input": true, "label": "Diastolic (bottom number) blood pressure range (i.e. Blood pressure is written as 135/75 - your diastolic is '75')", "data": {"values": [{"label": "below_60", "value": "below_60"}, {"label": "60-64", "value": "60_64"}, {"label": "65-69", "value": "65_69"}, {"label": "70-74", "value": "70_74"}, {"label": "75-79", "value": "75_79"}, {"label": "80-84", "value": "80_84"}, {"label": "85-89", "value": "85_89"}, {"label": "90-94", "value": "90_94"}, {"label": "95-99", "value": "95_99"}, {"label": "100+", "value": "100_plus"}]}, "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.monitor_blood_pressure === 'yes'"}, {"key": "monitor_blood_sugar", "type": "radio", "input": true, "label": "Do you monitor your blood sugar at home?", "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = !!data.monitor_blood_pressure", "optionsLabelPosition": "right"}, {"key": "blood_sugar_device", "type": "selectboxes", "input": true, "label": "If yes, what device do you use?", "values": [{"label": "Dexcom G7", "value": "dexcom_g7", "shortcut": ""}, {"label": "Free-style Libre", "value": "free_style_libre", "shortcut": ""}, {"label": "<PERSON><PERSON><PERSON>", "value": "insulin_pump", "shortcut": ""}, {"label": "Finger Prick Testing", "value": "finger_prick_testing", "shortcut": ""}], "tableView": true, "customConditional": "show = data.monitor_blood_sugar === 'yes'"}, {"key": "blood_sugar_frequency", "type": "radio", "input": true, "label": "If yes, how frequently do you monitor your blood sugar?", "values": [{"label": "Daily", "value": "daily", "shortcut": ""}, {"label": "A few times per week", "value": "few_times_per_week", "shortcut": ""}, {"label": "Once a week", "value": "once_a_week", "shortcut": ""}, {"label": "Once a month", "value": "once_a_month", "shortcut": ""}, {"label": "Rarely", "value": "rarely", "shortcut": ""}, {"label": "Never", "value": "never", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.monitor_blood_sugar === 'yes'"}, {"key": "current_medications_header", "type": "content", "html": "<h3>Current Medications</h3>", "input": false, "label": "Current Medications", "tableView": false, "refreshOnChange": false}, {"key": "on_insulin", "type": "radio", "input": true, "label": "Are you on insulin?", "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "insulin_delivery_method", "type": "selectboxes", "input": true, "label": "Please select which of the following insulin delivery methods you use:", "values": [{"label": "Insulin pump", "value": "pump", "shortcut": ""}, {"label": "Injection", "value": "injection", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = data.on_insulin === 'yes'"}, {"key": "insulin_pump_header", "type": "content", "html": "<h3>Insulin Pump</h3>", "input": false, "label": "<PERSON><PERSON><PERSON>", "tableView": false, "customConditional": "show = !!data.insulin_delivery_method && data.insulin_delivery_method.pump"}, {"key": "insulin_pump_type", "type": "select", "input": true, "label": "What type of insulin do you use in your pump?", "data": {"values": [{"label": "<PERSON><PERSON><PERSON> (NovoLog)", "value": "aspart"}, {"label": "<PERSON><PERSON><PERSON> (Humalog)", "value": "lispro"}, {"label": "In<PERSON><PERSON> (Apidra)", "value": "glulisine"}, {"label": "Other", "value": "other"}]}, "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = !!data.insulin_delivery_method && data.insulin_delivery_method.pump"}, {"key": "daily_insulin_units", "type": "textfield", "input": true, "label": "On average, how many units of this insulin do you go through in a day?", "validate": {"required": true, "custom": "valid = (data.insulin_pump_type !== undefined && data.insulin_delivery_method === 'pump')"}, "tableView": true, "customConditional": "show = data.insulin_delivery_method === 'pump' && !!data.insulin_pump_type"}, {"key": "long_acting_basal_insulin", "type": "radio", "input": true, "label": "Do you use a long-acting basal insulin in addition to your insulin pump?", "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.insulin_delivery_method === 'pump'"}, {"key": "daily_basal_insulin_units", "type": "textfield", "input": true, "label": "How many units of basal insulin do you use in a day?", "validate": {"required": true, "custom": "valid = (data.long_acting_basal_insulin === 'yes')"}, "tableView": true, "customConditional": "show = data.insulin_delivery_method === 'pump' && data.long_acting_basal_insulin === 'yes'"}, {"key": "long_acting_insulins", "type": "selectboxes", "input": true, "label": "Please select from the following list of long-acting insulins:", "values": [{"label": "Lantus (insulin glargine)", "value": "lantus", "shortcut": ""}, {"label": "Levemir (insulin detemir)", "value": "leve<PERSON>", "shortcut": ""}, {"label": "<PERSON><PERSON><PERSON><PERSON> (insulin glargine)", "value": "<PERSON><PERSON><PERSON><PERSON>", "shortcut": ""}, {"label": "Tresiba (insulin degludec)", "value": "tresiba", "shortcut": ""}, {"label": "Basaglar (insulin glargine)", "value": "basaglar", "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}], "tableView": true, "customConditional": "show = data.on_insulin === 'yes'"}, {"key": "other_long_acting_insulin", "type": "textfield", "input": true, "label": "Please specify other long-acting insulin", "tableView": true, "customConditional": "show = data.long_acting_insulins.other"}, {"key": "no_long_acting_insulins", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a long-acting insulin."}, "validate": {"custom": "valid = !!data.no_long_acting_insulins || !!_.some(_.values(data.long_acting_insulins));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "pre_mixed_insulins", "type": "selectboxes", "input": true, "label": "Please select from the following list of pre-mixed insulins:", "values": [{"label": "Humalog Mix 75/25 (insulin lispro protamine and insulin lispro)", "value": "humalog_mix_75_25", "shortcut": ""}, {"label": "Humalog Mix 50/50 (insulin lispro protamine and insulin lispro)", "value": "humalog_mix_50_50", "shortcut": ""}, {"label": "Novolog Mix 70/30 (insulin aspart protamine and insulin aspart)", "value": "novolog_mix_70_30", "shortcut": ""}, {"label": "Novolin 70/30 (insulin isophane and insulin regular)", "value": "novolin_70_30", "shortcut": ""}, {"label": "Humulin 70/30 (insulin isophane and insulin regular)", "value": "humulin_70_30", "shortcut": ""}, {"label": "Other", "value": "other_pre_mix", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.on_insulin === 'yes'"}, {"key": "other_pre_mixed_insulin", "type": "textfield", "input": true, "label": "Please specify other pre-mixed insulin", "tableView": true, "customConditional": "show = data.pre_mixed_insulins.other_pre_mix"}, {"key": "no_pre_mixed_insulins", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a pre-mixed insulin."}, "validate": {"custom": "valid = !!data.no_pre_mixed_insulins || !!_.some(_.values(data.pre_mixed_insulins));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "medium_short_acting_insulins", "type": "selectboxes", "input": true, "label": "Please select from the following list of medium and short-acting insulins:", "values": [{"label": "Humulin R (insulin regular)", "value": "humulin_r", "shortcut": ""}, {"label": "Novolin R (insulin regular)", "value": "novolin_r", "shortcut": ""}, {"label": "Humalog (insulin lispro)", "value": "humalog", "shortcut": ""}, {"label": "Novolog (insulin aspart)", "value": "novolog", "shortcut": ""}, {"label": "Apidra (insulin glulisine)", "value": "apidra", "shortcut": ""}, {"label": "Other", "value": "other_medium_short", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.on_insulin === 'yes'"}, {"key": "other_medium_short_acting_insulin", "type": "textfield", "input": true, "label": "Please specify other medium or short-acting insulin", "tableView": true, "customConditional": "show = data.medium_short_acting_insulins.other_medium_short"}, {"key": "no_medium_short_acting_insulins", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a medium or short-acting insulin."}, "validate": {"custom": "valid = !!data.no_medium_short_acting_insulins || !!_.some(_.values(data.medium_short_acting_insulins));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "on_injectable_non_insulin", "type": "radio", "input": true, "label": "Are you any injectable diabetic medications (i.e. Ozempic, <PERSON>un<PERSON>o, etc)?", "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = !!data.on_insulin", "optionsLabelPosition": "right"}, {"key": "previous_medications_header", "type": "content", "html": "<h3>Previous Medications</h3>", "input": false, "label": "Previous Medications", "tableView": false, "refreshOnChange": false}, {"key": "current_medications", "type": "textfield", "input": true, "label": "What medications are you currently taking for diabetes?", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.diabetes_diagnosis === 'yes'"}, {"key": "diabetes_control", "type": "radio", "input": true, "label": "How well is your diabetes controlled?", "values": [{"label": "Well controlled", "value": "well_controlled", "shortcut": ""}, {"label": "Moderately controlled", "value": "moderately_controlled", "shortcut": ""}, {"label": "Poorly controlled", "value": "poorly_controlled", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.diabetes_diagnosis === 'yes'"}, {"key": "family_history", "type": "radio", "input": true, "label": "Do you have a family history of diabetes?", "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "family_member_diabetes", "type": "textfield", "input": true, "label": "Which family member(s) have diabetes?", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.family_history === 'yes'"}, {"key": "symptoms_experienced", "type": "selectboxes", "input": true, "label": "What symptoms have you experienced related to diabetes?", "values": [{"label": "Increased thirst", "value": "increased_thirst", "shortcut": ""}, {"label": "Frequent urination", "value": "frequent_urination", "shortcut": ""}, {"label": "Unexplained weight loss", "value": "weight_loss", "shortcut": ""}, {"label": "Fatigue", "value": "fatigue", "shortcut": ""}, {"label": "Blurred vision", "value": "blurred_vision", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.diabetes_diagnosis === 'yes'"}, {"key": "no_symptoms_experienced", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = !!data.no_symptoms_experienced || !!_.some(_.values(data.symptoms_experienced));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}]}