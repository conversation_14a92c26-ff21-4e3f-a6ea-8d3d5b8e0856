{"name": "redFlags", "type": "form", "title": "Red Flags", "display": "form", "components": [{"key": "patch_vs_pill", "type": "radio", "input": true, "label": "Please select which form of birth control you are interested in:", "inline": false, "values": [{"label": "Birth Control Pill", "value": "ocp_pill", "shortcut": ""}, {"label": "Birth Control Patch", "value": "birth_control_patch", "shortcut": ""}], "tableView": false, "optionsLabelPosition": "right"}, {"key": "birth_control_contraindication", "type": "selectboxes", "input": true, "label": "Do you have any of the following medical conditions?", "values": [{"label": "Pregnant", "value": "currently_pregnant", "shortcut": ""}, {"label": "Unsure if pregnant", "value": "possibly_pregnant", "shortcut": ""}, {"label": "Had a baby within the last 42 days", "value": "baby_recently", "shortcut": ""}, {"label": "High Blood Pressure", "value": "dx_htn", "shortcut": ""}, {"label": "Deep Vein Thrombosis (DVT)", "value": "dx_DVT", "shortcut": ""}, {"label": "Pulmonary Embolus (PE)", "value": "dx_PE", "shortcut": ""}, {"label": "Breast Cancer (Past or Present)", "value": "dx_breast_cancer", "shortcut": ""}], "inputType": "checkbox", "tableView": false, "customConditional": "show = data.bmi < 30 || data.patch_vs_pill == 'ocp_pill';", "optionsLabelPosition": "right"}, {"key": "smoking_status_35", "type": "checkbox", "input": true, "label": "I am 35+ and smoke tobacco products", "tableView": false, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.bmi < 30 || data.patch_vs_pill == 'ocp_pill';"}, {"key": "no_absolute_contraindications", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = !!data.no_absolute_contraindications || !!_.some(_.values(data.birth_control_contraindication)) || !!data.smoking_status_35;"}, "tableView": false, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.bmi < 30 || data.patch_vs_pill == 'ocp_pill';"}, {"key": "blood_pressure_reading", "type": "radio", "input": true, "label": "TeleTest requires a recent blood pressure measurement within the last 2 weeks. You can measure your blood pressure for free at a local pharmacy. <br/><br/><strong>Please confirm your blood pressure below:</strong>", "values": [{"label": "Less than 140/90", "value": "normotensive", "shortcut": ""}, {"label": "Greater than 140/90", "value": "hypertensive", "shortcut": ""}, {"label": "I haven't checked my blood pressure", "value": "bp_unknown", "shortcut": ""}], "tableView": false, "customConditional": "show = data.bmi < 30 || data.patch_vs_pill == 'ocp_pill';", "optionsLabelPosition": "right"}, {"key": "migraine_aura", "type": "radio", "input": true, "label": "Do you have a history of migraine headaches and experience vision changes with your headache?", "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "tableView": false, "customConditional": "show = data.bmi < 30 || data.patch_vs_pill == 'ocp_pill';", "optionsLabelPosition": "right"}]}