{"components": [{"key": "heading_main_section", "html": "<h1><strong>Hair Loss Bloodwork</strong></h1><p>To expedite your care, please answer the following questions to the best of your abilities. &nbsp;We ask these questions to provide the best possible care.&nbsp;</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "discussed_hair_loss_diagnosis", "type": "selectboxes", "input": true, "label": "Have you discussed your hair loss with any of the following medical professionals?", "values": [{"label": "Family Doctor / General Practitioner", "value": "general_practitioner"}, {"label": "Nurse Practitioner", "value": "nurse_practitioner"}, {"label": "<PERSON><PERSON><PERSON>", "value": "naturopath"}], "confirm_label": "Discussed Hair Loss Diagnosis:", "tableView": true, "optionsLabelPosition": "right"}, {"key": "none_of_the_above_discussed_hair_loss", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "Please select a provider or choose 'None of the above'."}, "validate": {"custom": "valid = !!data.none_of_the_above_discussed_hair_loss || _.some(_.values(data.discussed_hair_loss_diagnosis));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "providers_not_consulted_for_hair_loss", "type": "textfield", "input": true, "label": "Patient indicated they have NOT consulted the following professionals about hair loss:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "Hair loss providers not consulted:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.discussed_hair_loss_diagnosis, _.negate(_.identity))), _.startCase), ', '), /_/g, ' ');"}, {"key": "mphl_age", "data": {"values": [{"label": "<6 weeks", "value": "less_6_weeks"}, {"label": "6 weeks - 3 months", "value": "6weeks_3months"}, {"label": "3-6 months", "value": "3-6_months"}, {"label": "6-12 months", "value": "6_12_months"}, {"label": "12-24 months", "value": "12_24_months"}, {"label": "2-5 years", "value": "2_5_years"}, {"label": "5-10 years", "value": "5_10_years"}, {"label": "10+ years", "value": "10+_years"}, {"label": "I don't know", "value": "cannot_recall"}]}, "type": "select", "input": true, "label": "How long ago did you first notice hair loss?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Hair Loss Duration:", "optionsLabelPosition": "right"}, {"key": "aa_sudden_hair_loss", "type": "radio", "input": true, "label": "Have you experienced sudden hair loss that led to bald patches on your scalp?", "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "Do not know", "value": "do_not_know", "shortcut": ""}], "validate": {"required": true}, "inputType": "radio", "tableView": true, "confirm_label": "Sudden Hair Loss:", "optionsLabelPosition": "right"}, {"key": "traction_alopecia_hairline_temples", "type": "radio", "input": true, "label": "Have you noticed hair loss primarily around your hairline or at the temples?", "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "Do not know", "value": "do_not_know", "shortcut": ""}], "validate": {"required": true}, "inputType": "radio", "confirm_label": "Hair Loss Located at Hairline or Temples:", "tableView": true, "optionsLabelPosition": "right"}, {"key": "traction_alopecia_scalp_pain_itching_redness", "type": "radio", "input": true, "label": "Do you experience scalp pain, itching, or redness in areas where hair loss is occurring?", "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "Do not know", "value": "do_not_know", "shortcut": ""}], "validate": {"required": true}, "inputType": "radio", "tableView": true, "confirm_label": "Scalp Pain, Itching, or Redness:", "optionsLabelPosition": "right"}, {"key": "heading_physical changes", "html": "<h4>Observed Changes in Hair&nbsp;</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "aa_graying_hair", "type": "radio", "input": true, "label": "Have you experienced any graying of hair in association with hair loss?", "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "Do not know", "value": "do_not_know", "shortcut": ""}], "validate": {"required": true}, "inputType": "radio", "tableView": true, "confirm_label": "Graying of Hair:", "optionsLabelPosition": "right"}, {"key": "aa_exclamation_point_hairs", "type": "radio", "input": true, "label": "Have you noticed any \"exclamation point hairs\" - these are hairs that look like exclamation points near the bald patches and are usually short (2 to 3 millimeters long) and have a thick root with a thin shaft?<a href='https://www.canaaf.org/exclamation-point-hairs-a-sign-of-alopecia-areata/' target='_blank'> Learn more about exclamation point hairs.</a>", "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "Do not know", "value": "do_not_know", "shortcut": ""}], "validate": {"required": true}, "inputType": "radio", "tableView": true, "confirm_label": "Exclamation Point Hairs:", "optionsLabelPosition": "right"}, {"key": "hyperandrogenism_signs", "type": "radio", "input": true, "label": "Since the start of our hair loss, have you noticed an increase in body or facial hair, or new severe acne?", "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "Do not know", "value": "do_not_know", "shortcut": ""}], "validate": {"required": true}, "inputType": "radio", "tableView": true, "confirm_label": "Increase in Body or Facial Hair or Acne since Hair Loss Noted:", "optionsLabelPosition": "right"}, {"key": "telogen_effluvium_shedding", "type": "radio", "input": true, "label": "Have you experienced any rapid shedding or thinning of hair?", "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "Do not know", "value": "do_not_know", "shortcut": ""}], "validate": {"required": true}, "inputType": "radio", "tableView": true, "confirm_label": "Rapid Shedding or Thinning of Hair:", "optionsLabelPosition": "right"}, {"key": "heading_other_changes", "html": "<h4>Other Symptoms&nbsp;</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "hyperandrogenism_menstrual_cycle", "type": "radio", "input": true, "label": "Have you experienced any changes in your periods, or are you currently using any birth control pills or hormone treatments?", "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "Do not know", "value": "do_not_know", "shortcut": ""}], "validate": {"required": true}, "inputType": "radio", "tableView": true, "confirm_label": "Changes in Menstrual Cycle or Use of Birth Control Pills or Hormone Treatments:", "customConditional": "show = data.sex == 'female';", "optionsLabelPosition": "right"}, {"key": "deepening_voice", "type": "radio", "input": true, "label": "Have you experienced a deepening of your voice?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Deepening of Voice:", "optionsLabelPosition": "right"}, {"key": "increased_muscle_mass", "type": "radio", "input": true, "label": "Have you noticed an increase in muscle mass?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "not_sure"}], "validate": {"required": true}, "confirm_label": "Increased Muscle Mass:", "tableView": true, "optionsLabelPosition": "right"}, {"key": "male_pattern_baldness", "type": "radio", "input": true, "label": "Have you experienced hair loss or thinning, particularly on the top of your head or near the temples?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "not_sure"}], "validate": {"required": true}, "confirm_label": "Thinning near Temples or Top of Head:", "tableView": true, "optionsLabelPosition": "right"}, {"key": "clitoromegaly", "type": "radio", "input": true, "label": "Have you experienced an increase in the size of your clitoris (clitoromegaly)?  Clitoromegaly is an abnormal enlargement of the clitoris.", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Enlargement of Clitoris associated with Hair Loss:", "customConditional": "show = data.sex == 'female';", "optionsLabelPosition": "right"}, {"key": "heading_fm_medical_history", "html": "<h3>Family History&nbsp;</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "mphl_family_history", "type": "selectboxes", "input": true, "label": "Is there a family history of hair loss, and if so, which relatives are affected?", "values": [{"label": "Father/brother", "value": "father_brother", "shortcut": ""}, {"label": "Mother/sister", "value": "mother_sister", "shortcut": ""}, {"label": "Grandparents", "value": "grandparents", "shortcut": ""}, {"label": "Aunts/Uncles", "value": "aunts_uncles", "shortcut": ""}, {"label": "No family history", "value": "no_family_history", "shortcut": ""}, {"label": "Do not know", "value": "do_not_know", "shortcut": ""}], "validate": {"required": true}, "inputType": "checkbox", "confirm_label": "Family History of Hair Loss:", "tableView": true, "optionsLabelPosition": "right"}, {"key": "aa_fmhx", "type": "radio", "input": true, "label": "Have any family members experienced similar hair loss patterns or been diagnosed with alopecia areata?", "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "Do not know", "value": "do_not_know", "shortcut": ""}], "validate": {"required": true}, "inputType": "radio", "tableView": true, "confirm_label": "Family History of Similar Hair Loss Patterns or Alopecia Areata:", "optionsLabelPosition": "right"}, {"key": "heading_previous_diagnosis", "html": "<h4>Previous Diagnosis&nbsp;</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "aa_previous_diagnosis", "type": "radio", "input": true, "label": "Has a healthcare professional ever mentioned or discussed the possibility of alopecia areata, alopecia totalis, or alopecia universalis with you?", "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "Do not know", "value": "do_not_know", "shortcut": ""}], "validate": {"required": true}, "inputType": "radio", "tableView": true, "confirm_label": "Previous Diagnosis of Alopecia Areata, Alopecia Totalis, or Alopecia Universalis:", "optionsLabelPosition": "right"}, {"key": "hx_pcos_cah_cushing_tumor", "type": "selectboxes", "input": true, "label": "Please select if you have any of the following conditions:", "values": [{"label": "Polycystic Ovary Syndrome (PCOS)", "value": "pcos"}, {"label": "Congenital Adrenal Hyperplasia", "value": "congenital_adrenal_hyperplasia"}, {"label": "Cushing's Syndrome", "value": "cushings_syndrome"}, {"label": "<PERSON><PERSON><PERSON>", "value": "ovarian_androgen_secreting_tumors"}, {"label": "Adrenal Gland Tumors", "value": "adrenal_gland_tumors"}], "confirm_label": "Hormonal conditions:", "tableView": true, "optionsLabelPosition": "right"}, {"key": "none_of_the_above_hormonal_conditions", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "Please select at least one condition or choose 'None of the above'."}, "validate": {"custom": "valid = !!data.none_of_the_above_hormonal_conditions || _.some(_.values(data.hx_pcos_cah_cushing_tumor));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "hormonal_conditions_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following hormonal conditions:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "Hormonal conditions ruled out:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.hx_pcos_cah_cushing_tumor, _.negate(_.identity))), _.startCase), ', '), /_/g, ' ');"}, {"key": "aa_hx_atopy", "type": "selectboxes", "input": true, "label": "Do you have a history of any of the following conditions?", "values": [{"label": "Allergies", "value": "allergies"}, {"label": "Asthma", "value": "asthma"}, {"label": "<PERSON><PERSON><PERSON>", "value": "eczema"}], "adminFlag": true, "confirm_label": "Atopic history:", "tableView": true, "optionsLabelPosition": "right"}, {"key": "none_of_the_above_atopy", "type": "checkbox", "input": true, "label": "None of the above / Do not know", "errors": {"custom": "Please select at least one condition or choose “None of the above / Do not know”."}, "validate": {"custom": "valid = !!data.none_of_the_above_atopy || _.some(_.values(data.aa_hx_atopy));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "atopy_conditions_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following atopic conditions:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "Do not have the following conditions:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.aa_hx_atopy, _.negate(_.identity))), _.startCase), ', '), /_/g, ' ');"}, {"key": "aa_hx_dm_vitiligo_ra_hypothroidism_sle", "type": "selectboxes", "input": true, "label": "Please select if you have any of the following conditions:", "values": [{"label": "Vitiligo", "value": "vitiligo"}, {"label": "Diabetes", "value": "diabetes"}, {"label": "Hypothyroidism (low thyroid levels)", "value": "hypothroidism"}, {"label": "Rheumatoid arthritis", "value": "rheumatoid_arthritis"}, {"label": "Discoid lupus erythematosus", "value": "lupus"}], "adminFlag": true, "confirm_label": "Auto-immune / endocrine conditions:", "tableView": true, "optionsLabelPosition": "right"}, {"key": "none_of_the_above_autoimmune", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "Please select at least one condition or choose “None of the above”."}, "validate": {"custom": "valid = !!data.none_of_the_above_autoimmune || _.some(_.values(data.aa_hx_dm_vitiligo_ra_hypothroidism_sle));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "autoimmune_conditions_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following autoimmune / endocrine conditions:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "Do not have the following conditions:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.aa_hx_dm_vitiligo_ra_hypothroidism_sle, _.negate(_.identity))), _.startCase), ', '), /_/g, ' ');"}, {"key": "te_hx_vitamin_minteral_def", "type": "selectboxes", "input": true, "label": "Have you been told you have problems with any of the following?", "values": [{"label": "Low Hemoglobin", "value": "hemoglobin"}, {"label": "Low Iron", "value": "iron"}, {"label": "Overactive or Underactive Thyroid", "value": "thyroid"}, {"label": "Low Vitamin D", "value": "low_vit_d"}], "adminFlag": true, "confirm_label": "Vitamin / mineral / thyroid issues:", "tableView": true, "optionsLabelPosition": "right"}, {"key": "none_of_the_above_vitamin_mineral", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "Please select at least one option or choose “None of the above”."}, "validate": {"custom": "valid = !!data.none_of_the_above_vitamin_mineral || _.some(_.values(data.te_hx_vitamin_minteral_def));"}, "tableView": true, "confirm_label": "Vitamin / Mineral / Thyroid Issues:", "customClass": "mt-n3", "defaultValue": false}, {"key": "vitamin_mineral_deficiencies_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following deficiencies / issues:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "I have not had the following conditions:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.te_hx_vitamin_minteral_def, _.negate(_.identity))), _.startCase), ', '), /_/g, ' ');"}, {"key": "heading_potential_triggers", "html": "<h4>Additional Factors&nbsp;</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "te_recent_birth_or_pregnancy", "type": "radio", "input": true, "label": "Are you currently pregnant or have you recently given birth?", "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "Do not know", "value": "do_not_know", "shortcut": ""}], "validate": {"required": true}, "inputType": "radio", "tableView": true, "confirm_label": "Pregnancy or Recent Birth:", "customConditional": "show = data.sex == 'female';", "optionsLabelPosition": "right"}, {"key": "te_recent_acute_event", "type": "radio", "input": true, "label": "Have you recently undergone a major surgery, experienced significant weight loss, or been through a highly stressful event?", "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "Do not know", "value": "do_not_know", "shortcut": ""}], "validate": {"required": true}, "inputType": "radio", "tableView": true, "confirm_label": "Recent Major Surgery, Weight Loss, or Stressful Event:", "optionsLabelPosition": "right"}, {"key": "te_new_product_hair_care_routine", "type": "radio", "input": true, "label": "Have you experienced any changes in your hair care routine or used new hair products?", "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "Do not know", "value": "do_not_know", "shortcut": ""}], "validate": {"required": true}, "inputType": "radio", "tableView": true, "confirm_label": "Changes in Hair Care Routine or New Hair Products:", "optionsLabelPosition": "right"}, {"key": "traction_alopecia_heading", "html": "<h4>Risk Factors</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "tractional_tight_hairstyles", "type": "radio", "input": true, "label": "Have you worn tight hairstyles, such as braids, ponytails, or buns, frequently or for extended periods of time?", "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "Do not know", "value": "do_not_know", "shortcut": ""}], "validate": {"required": true}, "inputType": "radio", "tableView": true, "confirm_label": "Tight Hairstyles (<PERSON><PERSON><PERSON>, Ponytails, Buns):", "optionsLabelPosition": "right"}, {"key": "traction_alopecia_extensions_weaves_wigs", "type": "radio", "input": true, "label": "Have you used hair extensions, weaves, or wigs that apply tension to your hair and scalp?", "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "Do not know", "value": "do_not_know", "shortcut": ""}], "validate": {"required": true}, "inputType": "radio", "tableView": true, "confirm_label": "Use of Hair Extensions, Weaves, or Wigs:", "optionsLabelPosition": "right"}, {"key": "heading_lab_history", "html": "<h4>Lab Testing&nbsp;</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "heading_lab_history", "html": "<h5>Previous Lab Testing&nbsp;</h5>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "abnormal_cbc_risk_factors", "type": "selectboxes", "input": true, "label": "Please select if you have any of the following medical conditions confirmed by a doctor or previous testing:", "values": [{"label": "Alpha or Beta Thalassemia", "value": "thalassemia"}, {"label": "Sickle Cell Disease", "value": "sickle_cell_disease"}, {"label": "Sickle Cell Trait", "value": "sickle_cell_trait"}, {"label": "Benign Ethnic Neutropenia", "value": "benign_ethnic_neutropenia"}, {"label": "Anemia", "value": "anemia"}, {"label": "Low White Blood Cells", "value": "low_wbc"}, {"label": "Low Neutrophils", "value": "low_neutrophils"}, {"label": "Chronic Lymphocytic Leukemia", "value": "chronic_lymphocytic_leukemia"}], "adminFlag": true, "confirm_label": "Current metabolic conditions:", "tableView": true, "optionsLabelPosition": "right"}, {"key": "none_of_the_above_cbc", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "Please select at least one condition or choose “None of the above”."}, "validate": {"custom": "valid = !!data.none_of_the_above_cbc || _.some(_.values(data.abnormal_cbc_risk_factors));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "cbc_conditions_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following metabolic / hematologic conditions:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "I have not had the following conditions:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.abnormal_cbc_risk_factors, _.negate(_.identity))), _.startCase), ', '), /_/g, ' ');"}, {"key": "header_prior_tests", "type": "content", "input": false, "label": "Content", "html": "<h2>Prior Lab Testing</h2>", "tableView": false, "refreshOnChange": false}, {"key": "prior_tests_completed", "type": "selectboxes", "input": true, "label": "Have you had any of the following tests completed <strong>after</strong> your symptoms started?", "values": [{"label": "CBC (Complete Blood Count)", "value": "cbc"}, {"label": "Ferritin (Iron stores)", "value": "ferritin"}, {"label": "TSH (Thyroid Stimulating Hormone)", "value": "tsh"}, {"label": "Zinc", "value": "zinc"}, {"label": "Vitamin B12", "value": "vitamin_b12"}, {"label": "Selenium", "value": "selenium"}, {"label": "Folate", "value": "folate"}, {"label": "I have not had these tests completed", "value": "no_prior_tests"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Prior Tests Completed:", "optionsLabelPosition": "right"}, {"key": "last_known_lab_timing", "type": "select", "input": true, "label": "Do you recall when your last set of lab tests (bloodwork) was completed?", "widget": "html5", "data": {"values": [{"label": "<1 month ago", "value": "<1 month ago"}, {"label": "1-2 months ago", "value": "1-2 months ago"}, {"label": "2-3 months ago", "value": "2-3 months ago"}, {"label": "3-4 months ago", "value": "3-4 months ago"}, {"label": "4-5 months ago", "value": "4-5 months ago"}, {"label": "5-6 months ago", "value": "5-6 months ago"}, {"label": "6-12 months ago", "value": "6-12 months ago"}, {"label": "12-24 months ago", "value": "12-24 months ago"}, {"label": "24+ months ago", "value": "24+ months ago"}, {"label": "I haven't had lab testing", "value": "never"}]}, "validate": {"required": true}, "tableView": true, "confirm_label": "Last Known Lab Timing:", "customConditional": "show = data.prior_tests_completed?.no_prior_tests === true && Object.values(data.prior_tests_completed).filter(v => v === true).length === 1"}, {"key": "last_known_lab_tests_completed", "type": "textarea", "input": true, "label": "If you remember, which lab tests were completed at that time?", "placeholder": "e.g., liver enzymes, electrolytes, hormone panel", "tableView": true, "confirm_label": "Labs completed at that time:", "customConditional": "show = !!data.last_known_lab_timing && data.last_known_lab_timing !== 'never';"}, {"key": "heading_cbc", "type": "content", "input": false, "label": "Content", "html": "<h3>CBC (Complete Blood Count)</h3>", "tableView": false, "customConditional": "show = data.prior_tests_completed?.cbc;"}, {"key": "last_cbc_test", "type": "select", "input": true, "label": "When was your last CBC test completed?", "widget": "html5", "data": {"values": [{"label": "<1 month ago", "value": "<1 month ago"}, {"label": "1-2 months ago", "value": "1-2 months ago"}, {"label": "2-3 months ago", "value": "2-3 months ago"}, {"label": "3-4 months ago", "value": "3-4 months ago"}, {"label": "4-5 months ago", "value": "4-5 months ago"}, {"label": "5-6 months ago", "value": "5-6 months ago"}, {"label": "6-12 months ago", "value": "6-12 months ago"}, {"label": "12-24 months ago", "value": "12-24 months ago"}, {"label": "24+ months ago", "value": "24+ months ago"}, {"label": "Never had one", "value": "never"}]}, "validate": {"required": true}, "tableView": true, "confirm_label": "Last CBC Test:", "customConditional": "show = data.prior_tests_completed?.cbc;"}, {"key": "prior_cbc_value", "type": "select", "input": true, "label": "What was your most recent hemoglobin level (CBC result)?", "widget": "html5", "data": {"values": [{"label": "I don't remember my result", "value": "dont_remember"}, {"label": "< 70 g/L", "value": "< 70 g/L"}, {"label": "70-100 g/L", "value": "70-100 g/L"}, {"label": "101-120 g/L", "value": "101-120 g/L"}, {"label": "121-150 g/L", "value": "121-150 g/L"}, {"label": "> 150 g/L", "value": "> 150 g/L"}]}, "validate": {"required": true}, "tableView": true, "confirm_label": "Hemoglobin Level:", "customConditional": "show = data.prior_tests_completed?.cbc;"}, {"key": "heading_ferritin", "type": "content", "input": false, "label": "Content", "html": "<br><h3><PERSON><PERSON><PERSON> (Iron stores)</h3>", "tableView": false, "customConditional": "show = data.prior_tests_completed?.ferritin;"}, {"key": "last_ferritin_test", "type": "select", "input": true, "label": "When was your last ferritin test completed?", "widget": "html5", "data": {"values": [{"label": "<1 month ago", "value": "<1 month ago"}, {"label": "1-2 months ago", "value": "1-2 months ago"}, {"label": "2-3 months ago", "value": "2-3 months ago"}, {"label": "3-4 months ago", "value": "3-4 months ago"}, {"label": "4-5 months ago", "value": "4-5 months ago"}, {"label": "5-6 months ago", "value": "5-6 months ago"}, {"label": "6-12 months ago", "value": "6-12 months ago"}, {"label": "12-24 months ago", "value": "12-24 months ago"}, {"label": "24+ months ago", "value": "24+ months ago"}, {"label": "Never had one", "value": "never"}]}, "validate": {"required": true}, "tableView": true, "confirm_label": "Last Ferritin Test:", "customConditional": "show = data.prior_tests_completed?.ferritin;"}, {"key": "recent_ferritin_value", "type": "select", "input": true, "label": "What was your most recent ferritin result?", "widget": "html5", "data": {"values": [{"label": "I don't remember my result", "value": "dont_remember"}, {"label": "< 20 µg/L", "value": "< 20 µg/L"}, {"label": "20-49 µg/L", "value": "20-49 µg/L"}, {"label": "50-99 µg/L", "value": "50-99 µg/L"}, {"label": "100-199 µg/L", "value": "100-199 µg/L"}, {"label": "≥ 200 µg/L", "value": "≥ 200 µg/L"}]}, "validate": {"required": true}, "tableView": true, "confirm_label": "Ferritin Level:", "customConditional": "show = data.prior_tests_completed?.ferritin;"}, {"key": "heading_tsh", "type": "content", "input": false, "label": "Content", "html": "<br><h3>TSH (Thyroid Stimulating Hormone)</h3>", "tableView": false, "customConditional": "show = data.prior_tests_completed?.tsh;"}, {"key": "last_tsh_test", "type": "select", "input": true, "label": "When was your last TSH test completed?", "widget": "html5", "data": {"values": [{"label": "<1 month ago", "value": "<1 month ago"}, {"label": "1-2 months ago", "value": "1-2 months ago"}, {"label": "2-3 months ago", "value": "2-3 months ago"}, {"label": "3-4 months ago", "value": "3-4 months ago"}, {"label": "4-5 months ago", "value": "4-5 months ago"}, {"label": "5-6 months ago", "value": "5-6 months ago"}, {"label": "6-12 months ago", "value": "6-12 months ago"}, {"label": "12-24 months ago", "value": "12-24 months ago"}, {"label": "24+ months ago", "value": "24+ months ago"}, {"label": "Never had one", "value": "never"}]}, "validate": {"required": true}, "tableView": true, "confirm_label": "Last TSH Test:", "customConditional": "show = data.prior_tests_completed?.tsh;"}, {"key": "prior_tsh_value", "type": "select", "input": true, "label": "What was your most recent TSH level?", "widget": "html5", "data": {"values": [{"label": "I don't remember my result", "value": "dont_remember"}, {"label": "< 0.1 mIU/L", "value": "< 0.1"}, {"label": "0.1 - 0.3 mIU/L", "value": "0.1-0.3"}, {"label": "0.4 - 4.0 mIU/L", "value": "0.4-4.0"}, {"label": "4.1 - 5.0 mIU/L", "value": "4.1-5.0"}, {"label": "5.1 - 7.5 mIU/L", "value": "5.1-7.5"}, {"label": "7.6 - 10 mIU/L", "value": "7.6-10"}, {"label": "> 10 mIU/L", "value": "> 10"}]}, "validate": {"required": true}, "tableView": true, "confirm_label": "TSH Level:", "customConditional": "show = data.prior_tests_completed?.tsh;"}, {"key": "heading_zinc", "type": "content", "input": false, "label": "Content", "html": "<br><h3>Zinc</h3>", "tableView": false, "customConditional": "show = data.prior_tests_completed?.zinc;"}, {"key": "last_zinc_test", "type": "select", "input": true, "label": "When was your last zinc test completed?", "widget": "html5", "data": {"values": [{"label": "<1 month ago", "value": "<1 month ago"}, {"label": "1-2 months ago", "value": "1-2 months ago"}, {"label": "2-3 months ago", "value": "2-3 months ago"}, {"label": "3-4 months ago", "value": "3-4 months ago"}, {"label": "4-5 months ago", "value": "4-5 months ago"}, {"label": "5-6 months ago", "value": "5-6 months ago"}, {"label": "6-12 months ago", "value": "6-12 months ago"}, {"label": "12-24 months ago", "value": "12-24 months ago"}, {"label": "24+ months ago", "value": "24+ months ago"}, {"label": "Never had one", "value": "never"}]}, "validate": {"required": true}, "tableView": true, "confirm_label": "Last Zinc Test:", "customConditional": "show = data.prior_tests_completed?.zinc;"}, {"key": "recent_zinc_value", "type": "select", "input": true, "label": "What was your most recent zinc result?", "widget": "html5", "data": {"values": [{"label": "I don't remember my result", "value": "dont_remember"}, {"label": "< 8.5 µmol/L (low)", "value": "< 8.5"}, {"label": "8.5 - 11.5 µmol/L (borderline)", "value": "8.5-11.5"}, {"label": "> 11.5 µmol/L (normal)", "value": "> 11.5"}]}, "validate": {"required": true}, "tableView": true, "confirm_label": "Zinc Level:", "customConditional": "show = data.prior_tests_completed?.zinc;"}, {"key": "heading_b12", "type": "content", "input": false, "label": "Content", "html": "<br><h3>Vitamin B12</h3>", "tableView": false, "customConditional": "show = data.prior_tests_completed?.vitamin_b12;"}, {"key": "last_b12_test", "type": "select", "input": true, "label": "When was your last B12 test completed?", "widget": "html5", "data": {"values": [{"label": "<1 month ago", "value": "<1 month ago"}, {"label": "1-2 months ago", "value": "1-2 months ago"}, {"label": "2-3 months ago", "value": "2-3 months ago"}, {"label": "3-4 months ago", "value": "3-4 months ago"}, {"label": "4-5 months ago", "value": "4-5 months ago"}, {"label": "5-6 months ago", "value": "5-6 months ago"}, {"label": "6-12 months ago", "value": "6-12 months ago"}, {"label": "12-24 months ago", "value": "12-24 months ago"}, {"label": "24+ months ago", "value": "24+ months ago"}, {"label": "Never had one", "value": "never"}]}, "validate": {"required": true}, "tableView": true, "confirm_label": "Last B12 Test:", "customConditional": "show = data.prior_tests_completed?.vitamin_b12;"}, {"key": "prior_b12_value", "type": "select", "input": true, "label": "What was your most recent B12 level?", "widget": "html5", "data": {"values": [{"label": "I don't remember my result", "value": "dont_remember"}, {"label": "< 150 pmol/L", "value": "< 150"}, {"label": "150-200 pmol/L", "value": "150-200"}, {"label": "200-300 pmol/L", "value": "200-300"}, {"label": "> 300 pmol/L", "value": "> 300"}]}, "validate": {"required": true}, "tableView": true, "confirm_label": "B12 Level:", "customConditional": "show = data.prior_tests_completed?.vitamin_b12;"}, {"key": "heading_selenium", "type": "content", "input": false, "label": "Content", "html": "<br><h3>Selenium</h3>", "tableView": false, "customConditional": "show = data.prior_tests_completed?.selenium;"}, {"key": "last_selenium_test", "type": "select", "input": true, "label": "When was your last selenium test completed?", "widget": "html5", "data": {"values": [{"label": "<1 month ago", "value": "<1 month ago"}, {"label": "1-2 months ago", "value": "1-2 months ago"}, {"label": "2-3 months ago", "value": "2-3 months ago"}, {"label": "3-4 months ago", "value": "3-4 months ago"}, {"label": "4-5 months ago", "value": "4-5 months ago"}, {"label": "5-6 months ago", "value": "5-6 months ago"}, {"label": "6-12 months ago", "value": "6-12 months ago"}, {"label": "12-24 months ago", "value": "12-24 months ago"}, {"label": "24+ months ago", "value": "24+ months ago"}, {"label": "Never had one", "value": "never"}]}, "validate": {"required": true}, "tableView": true, "confirm_label": "Last Selenium Test:", "customConditional": "show = data.prior_tests_completed?.selenium;"}, {"key": "recent_selenium_value", "type": "select", "input": true, "label": "What was your most recent selenium result?", "widget": "html5", "data": {"values": [{"label": "I don't remember my result", "value": "dont_remember"}, {"label": "< 70 µg/L (low)", "value": "< 70"}, {"label": "70-110 µg/L (borderline)", "value": "70-110"}, {"label": "> 110 µg/L (normal)", "value": "> 110"}]}, "validate": {"required": true}, "tableView": true, "confirm_label": "Selenium Level:", "customConditional": "show = data.prior_tests_completed?.selenium;"}, {"key": "heading_folate", "type": "content", "input": false, "label": "Content", "html": "<br><h3>Folate</h3>", "tableView": false, "customConditional": "show = data.prior_tests_completed?.folate;"}, {"key": "last_folate_test", "type": "select", "input": true, "label": "When was your last folate test completed?", "widget": "html5", "data": {"values": [{"label": "<1 month ago", "value": "<1 month ago"}, {"label": "1-2 months ago", "value": "1-2 months ago"}, {"label": "2-3 months ago", "value": "2-3 months ago"}, {"label": "3-4 months ago", "value": "3-4 months ago"}, {"label": "4-5 months ago", "value": "4-5 months ago"}, {"label": "5-6 months ago", "value": "5-6 months ago"}, {"label": "6-12 months ago", "value": "6-12 months ago"}, {"label": "12-24 months ago", "value": "12-24 months ago"}, {"label": "24+ months ago", "value": "24+ months ago"}, {"label": "Never had one", "value": "never"}]}, "validate": {"required": true}, "tableView": true, "confirm_label": "Last Folate Test:", "customConditional": "show = data.prior_tests_completed?.folate;"}, {"key": "recent_folate_value", "type": "select", "input": true, "label": "What was your most recent folate result?", "widget": "html5", "data": {"values": [{"label": "I don't remember my result", "value": "dont_remember"}, {"label": "< 7 nmol/L (low)", "value": "< 7"}, {"label": "7-15 nmol/L (borderline)", "value": "7-15"}, {"label": "> 15 nmol/L (normal)", "value": "> 15"}]}, "validate": {"required": true}, "tableView": true, "confirm_label": "Folate Level:", "customConditional": "show = data.prior_tests_completed?.folate;"}, {"key": "heading_hair_loss_treatments", "html": "<h4>Hair Loss Treatments&nbsp;</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "prior_hl_treatments", "type": "selectboxes", "input": true, "label": "Have you tried any of the following hair loss treatments in the past?", "values": [{"label": "Minoxidil (Rogaine)", "value": "minoxidil", "shortcut": ""}, {"label": "Finasteride (Propecia)", "value": "finasteride", "shortcut": ""}, {"label": "Hair transplant surgery", "value": "hair_transplant", "shortcut": ""}, {"label": "Laser therapy", "value": "laser_therapy", "shortcut": ""}, {"label": "PRP (Platelet-rich plasma) therapy", "value": "prp_therapy", "shortcut": ""}, {"label": "None of the above", "value": "none", "shortcut": ""}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "confirm_label": "Prior Hair Loss Treatments:", "optionsLabelPosition": "right"}, {"key": "hair_loss_treatments", "type": "selectboxes", "input": true, "label": "Did you experience any long-term benefits?", "values": [{"label": "Yes, with long-term benefits", "value": "yes_long_term_benefits", "shortcut": ""}, {"label": "Yes, but without long-term benefits", "value": "yes_no_long_term_benefits", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "Do not know", "value": "do_not_know", "shortcut": ""}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "confirm_label": "Hair Loss Treatments:", "customConditional": "show = !data.prior_hl_treatments.none;", "optionsLabelPosition": "right"}, {"key": "skin_biopsy", "type": "radio", "input": true, "confirm_label": "Skin Biopsy Results:", "label": "Have you ever had a skin biopsy from an area of hair loss? If so, what were the results?", "values": [{"label": "Yes, positive for alopecia areata", "value": "yes_positive", "shortcut": ""}, {"label": "Yes, negative for alopecia areata", "value": "yes_negative", "shortcut": ""}, {"label": "Yes, but I don't know the results", "value": "yes_negative", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "Do not know", "value": "do_not_know", "shortcut": ""}], "validate": {"required": true}, "inputType": "radio", "tableView": true, "optionsLabelPosition": "right"}, {"key": "content2", "html": "<h2>Questions for the Doctor</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "additional_information", "type": "textarea", "input": true, "confirm_label": "Additional Information", "label": "Please provide any additional details about your symptoms, medical history, or medications that you think are relevant.", "tableView": true}, {"key": "photo_upload_header", "html": "<h2>Photo Upload</h2>", "type": "content", "input": false, "label": "Content"}, {"key": "uploadUrl", "url": "/app/q/{qpk}/formio-files/{pk}/", "type": "file", "image": true, "input": true, "label": "Please upload clear images of the affected areas (one overall photo and close-ups of each specific spot with hair loss—scalp, face, arms, or elsewhere).", "webcam": true, "capture": "user", "storage": "url", "multiple": true, "fileTypes": [{"label": "", "value": ""}], "tableView": true, "validate": {"required": true}, "validateWhenHidden": false}, {"key": "all_areas_uploaded", "type": "radio", "input": true, "label": "Did you upload photos of all areas where you're experiencing hair loss?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "confirm_label": "All Affected Areas Uploaded:", "optionsLabelPosition": "right"}, {"key": "photo_upload_warning", "type": "content", "input": false, "label": "Photo Upload Requirement", "html": "<div style='color:#856404;background-color:#fff3cd;border:1px solid #ffeeba;padding:10px;border-radius:4px;'><strong>Required:</strong> TeleTest physicians must review clear photos of <em>every</em> affected area <u>before</u> we can arrange your lab testing. Please upload the necessary images.</div>", "conditional": {"show": true, "when": "all_areas_uploaded", "eq": "no"}}, {"key": "intake_denial_reasons", "type": "textfield", "input": true, "label": "Intake Denial Reasons:", "hidden": true, "disabled": true, "multiple": true, "adminFlag": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "value=_.concat((data.any_other_questions === true && !!data.stated_other_questions && !/^\\s*$/.test(data.stated_other_questions)?['stated_other_questions.not_blank']:[]));", "refreshOnChange": true}, {"key": "intake_kind", "type": "textfield", "input": true, "label": "Intake Kind", "hidden": true, "disabled": true, "tableView": true, "clearOnHide": false, "calculateValue": "value = !data.intake_denial_reasons?.length ? 'mail' : 'chat';"}, {"key": "intake_mt_key", "type": "textfield", "input": true, "label": "Intake Mail Template Key", "hidden": true, "disabled": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.intake_kind=='mail'?'intake-hairloss':'intake-denial'"}, {"key": "allowed_rt_keys", "type": "textfield", "input": true, "label": "Allowed Rep<PERSON>", "hidden": true, "disabled": true, "multiple": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.intake_mt_key=='intake-denial'?[]:['get-req','appointment-intake','edit-intake']"}]}