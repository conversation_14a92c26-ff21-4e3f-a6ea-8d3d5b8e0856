{"components": [{"key": "diagnosis_of_allergic_conjunctivitis", "type": "radio", "input": true, "label": "Do you have a history of eye irritation caused by allergies?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "symptoms_more_than_7_days", "type": "radio", "input": true, "label": "Have your eye symptoms lasted for more than 7 days?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = (data.diagnosis_of_allergic_conjunctivitis === 'yes');"}, {"key": "eye_red_flag_symptoms", "type": "selectboxes", "input": true, "label": "Are you experiencing any of the following concerning eye symptoms?", "values": [{"label": "Light sensitivity", "value": "light_sensitivity"}, {"label": "Thick or pus-like discharge from your eye", "value": "pus_discharge"}, {"label": "Blurry or hazy vision", "value": "blurry_vision"}, {"label": "Loss of vision in one or both eyes", "value": "vision_loss"}, {"label": "Eye pain", "value": "eye_pain"}, {"label": "Concerned something is stuck in your eye", "value": "object_in_eye"}], "inputType": "checkbox", "tableView": true, "customConditional": "show = (data.diagnosis_of_allergic_conjunctivitis === 'yes' && data.symptoms_more_than_7_days === 'yes');", "optionsLabelPosition": "right"}, {"key": "no_eye_red_flag_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "Please select at least one symptom or confirm none apply."}, "validate": {"custom": "valid = !!data.no_eye_red_flag_symptoms || !!_.some(_.values(data.eye_red_flag_symptoms));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = (data.diagnosis_of_allergic_conjunctivitis === 'yes' && data.symptoms_more_than_7_days === 'yes');"}, {"key": "eye_symptoms_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following eye symptoms:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "I DO NOT have the following eye-related symptoms:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.eye_red_flag_symptoms, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "no_indications", "type": "textfield", "input": true, "label": "No Indication", "hidden": true, "disabled": true, "multiple": false, "tableView": true, "clearOnHide": false, "defaultValue": true, "calculateValue": "value = (data.diagnosis_of_allergic_conjunctivitis === 'yes' && data.symptoms_more_than_7_days === 'yes') ? false : true;", "refreshOnChange": true}, {"key": "contraindication_keys", "type": "textfield", "input": true, "label": "Contraindication Keys", "hidden": true, "disabled": true, "multiple": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "value = []; if (data.diagnosis_of_allergic_conjunctivitis === 'yes' && data.symptoms_more_than_7_days === 'no') { value.push('symptoms_less_than_7_days'); } if (_.some(_.values(data.eye_red_flag_symptoms))) { value.push('eye_red_flag_symptoms'); } value;", "refreshOnChange": true}, {"key": "approved_html", "type": "content", "input": true, "tableView": false, "clearOnHide": false, "defaultValue": "<h3 class='text-green'>You're eligible to discuss allergy eye drop medications.</h3><br><p><strong>Note:</strong> TeleTest physicians do not provide new diagnoses. We can continue or adjust therapy for known allergic conjunctivitis (i.e. eye allergies), including prescription antihistamine eye drops and other supportive measures.</p><p><strong>However, we do not provide antibiotic or steroid eye drops, such as:</strong></p><ul><li>Tobradex <em>(tobramycin/dexamethasone)</em></li><li>Fucidin <em>(fusidic acid)</em></li><li>Vigamox <em>(e.g., Moxifloxacin)</em></li></ul>"}, {"key": "rejected_html", "type": "content", "input": true, "tableView": false, "clearOnHide": false, "defaultValue": "<h3 class='text-red'>Your condition requires in-person medical care at this time. Please visit a local clinic or urgent care center for evaluation and treatment.</h3>", "refreshOnChange": true}]}