[{"rect": "['510.691', '751.771', '588.398', '770.243']", "type": "checkbox", "field_key": "Clear Form"}, {"rect": "['19.9675', '703.634', '237.398', '722.474']", "type": "text", "field_key": "name"}, {"rect": "['19.9675', '651.527', '237.398', '693.18']", "type": "text", "field_key": "address"}, {"rect": "['243.874', '649.347', '453.839', '666.39']", "type": "text", "field_key": "contactNumber1"}, {"rect": "['457.364', '650.514', '522.334', '662.139']", "type": "text", "field_key": "y_birth"}, {"type": "text", "field_key": "m_birth"}, {"type": "text", "field_key": "d_birth"}, {"rect": "['19.9675', '626.696', '147.888', '642.394']", "type": "text", "field_key": "ClinicianPractitioner Number"}, {"rect": "['148.448', '626.113', '238.568', '641.811']", "type": "text", "field_key": "CPSO  Registration No"}, {"rect": "['240.391', '624.816', '385.461', '641.859']", "type": "text", "field_key": "Health Number"}, {"rect": "['387.472', '624.816', '414.607', '641.859']", "type": "text", "field_key": "version"}, {"type": "radio_checkbox", "choices": ["M", "F"], "field_key": "sex"}, {"rect": "['474.613', '624.816', '531.944', '636.857']", "type": "text", "field_key": "y_birth2"}, {"rect": "['534.301', '624.816', '561.325', '636.024']", "type": "text", "field_key": "m_birth2"}, {"rect": "['563.237', '624.816', '591.071', '636.857']", "type": "text", "field_key": "d_birth2"}, {"type": "radio_checkbox", "choices": ["ohip", "un", "wsib"], "field_key": "checkOne"}, {"rect": "['244.111', '598.496', '269.848', '615.539']", "type": "text", "field_key": "Province"}, {"rect": "['272.791', '598.496', '445.984', '615.539']", "type": "text", "field_key": "Other Provincial Registration Number"}, {"rect": "['449.219', '598.496', '593.306', '615.539']", "type": "text", "field_key": "patientTelephone"}, {"rect": "['19.9675', '546.136', '238.83', '589.243']", "type": "text", "field_key": "Additional Clinical Information eg diagnosis"}, {"rect": "['242.53', '572.005', '593.404', '589.048']", "type": "text", "field_key": "Patient’s Last Name as per OHIP Card"}, {"rect": "['242.476', '545.47', '416.307', '562.513']", "type": "text", "field_key": "patien<PERSON><PERSON>"}, {"rect": "['419.435', '545.47', '593.266', '562.513']", "type": "text", "field_key": "patien<PERSON><PERSON>"}, {"rect": "['19.9675', '515.221', '130.043', '529.562']", "type": "text", "field_key": "lname"}, {"rect": "['20.8853', '534.933', '29.2446', '544.877']", "type": "checkbox", "field_key": "clinician"}, {"rect": "['133.691', '515.638', '237.386', '529.978']", "type": "text", "field_key": "fname"}, {"rect": "['242.303', '476.949', '592.313', '537.486']", "type": "text", "field_key": "Patient’s Address including Postal Code"}, {"rect": "['19.9675', '476.032', '237.992', '507.236']", "type": "text", "field_key": "address2a"}, {"rect": "['19.8584', '440.489', '31.3784', '451.485']", "type": "checkbox", "field_key": "row1.0"}, {"type": "radio_checkbox", "choices": ["ran", "fasting"], "field_key": "biochemistry"}, {"rect": "['19.8584', '428.445', '31.3784', '439.442']", "type": "checkbox", "field_key": "row1.1"}, {"rect": "['19.8584', '416.402', '31.3784', '427.398']", "type": "checkbox", "field_key": "row1.2"}, {"rect": "['19.8584', '404.358', '31.3784', '415.354']", "type": "checkbox", "field_key": "row1.3"}, {"rect": "['19.8584', '392.314', '31.3784', '403.311']", "type": "checkbox", "field_key": "row1.4"}, {"rect": "['19.8584', '379.271', '31.3784', '390.267']", "type": "checkbox", "field_key": "row1.5"}, {"rect": "['19.8584', '367.227', '31.3784', '378.224']", "type": "checkbox", "field_key": "row1.6"}, {"rect": "['19.8584', '355.184', '31.3784', '366.18']", "type": "checkbox", "field_key": "row1.7"}, {"rect": "['19.8584', '343.14', '31.3784', '354.136']", "type": "checkbox", "field_key": "row1.8"}, {"rect": "['19.8584', '331.096', '31.3784', '342.093']", "type": "checkbox", "field_key": "row1.9"}, {"rect": "['19.8584', '300.879', '31.3784', '323.918']", "type": "checkbox", "field_key": "row1.13"}, {"rect": "['19.8584', '281.879', '31.3784', '292.875']", "type": "checkbox", "field_key": "row1.14"}, {"rect": "['19.8584', '269.835', '31.3784', '280.831']", "type": "checkbox", "field_key": "row1.15"}, {"rect": "['19.8584', '256.791', '31.3784', '267.788']", "type": "checkbox", "field_key": "row1.16"}, {"rect": "['19.8584', '220.144', '31.3784', '253.133']", "type": "checkbox", "field_key": "row1.19"}, {"rect": "['19.8584', '208.101', '31.3784', '218.05']", "type": "checkbox", "field_key": "row1.20"}, {"rect": "['19.8584', '141.527', '31.3784', '204.912']", "type": "checkbox", "field_key": "row1.21"}, {"type": "radio_checkbox", "choices": ["ahep", "ch", "imm"], "field_key": "acuteHep"}, {"rect": "['470.799', '408.024', '480.748', '416.925']", "type": "checkbox", "field_key": "viralHepatits.3a"}, {"rect": "['470.799', '397.027', '480.748', '406.976']", "type": "checkbox", "field_key": "viralHepatits.3b"}, {"rect": "['470.799', '385.984', '480.748', '395.933']", "type": "checkbox", "field_key": "viralHepatits.3c"}, {"rect": "['93.3208', '245.659', '129.625', '257.179']", "type": "text", "field_key": "chidAge"}, {"rect": "['154.261', '245.02', '200.787', '256.54']", "type": "text", "field_key": "chidHours"}, {"rect": "['99.1257', '192.83', '237.645', '204.988']", "type": "text", "field_key": "drugName1"}, {"type": "radio_checkbox", "choices": ["TT", "F"], "field_key": "PSA"}, {"type": "radio_checkbox", "choices": ["i", "u"], "field_key": "insuredPSA"}, {"rect": "['352.949', '269.033', '415.177', '280.553']", "type": "text", "field_key": "specify1"}, {"rect": "['329.95', '256.257', '415.177', '268.415']", "type": "text", "field_key": "specify2"}, {"type": "radio_checkbox", "choices": ["i", "u"], "field_key": "insuredvitD"}, {"type": "text", "field_key": "clinicianTel"}, {"type": "text", "field_key": "patientTel"}, {"rect": "['99.1258', '180.053', '237.645', '191.573']", "type": "text", "field_key": "drugName2"}, {"rect": "['113.264', '167.166', '151.975', '178.047']", "type": "text", "field_key": "timeCollect1.0"}, {"rect": "['184.233', '167.166', '226.291', '178.686']", "type": "text", "field_key": "timeCollect2.0"}, {"rect": "['240.356', '441.012', '253.971', '452.009']", "type": "checkbox", "field_key": "hermatology1.0"}, {"rect": "['240.356', '428.969', '253.971', '439.965']", "type": "checkbox", "field_key": "hermatology1.1"}, {"rect": "['240.356', '404.882', '253.971', '415.878']", "type": "checkbox", "field_key": "hermatology1.3"}, {"rect": "['240.356', '392.838', '253.971', '403.834']", "type": "checkbox", "field_key": "hermatology1.4"}, {"rect": "['240.356', '380.795', '253.971', '391.791']", "type": "checkbox", "field_key": "hermatology1.5"}, {"rect": "['240.356', '355.751', '253.971', '378.791']", "type": "checkbox", "field_key": "hermatology1.6"}, {"rect": "['240.356', '343.707', '253.971', '354.704']", "type": "checkbox", "field_key": "hermatology1.7"}, {"rect": "['341.096', '218.843', '415.177', '230.363']", "type": "text", "field_key": "specify3"}, {"rect": "['419.855', '220.835', '593.448', '231.443']", "type": "text", "field_key": "Other Tests – one test per line, GC specify source"}, {"rect": "['419.855', '207.507', '593.448', '218.115']", "type": "text", "field_key": "Other Tests – one test per line, Sputum"}, {"rect": "['419.855', '195.267', '593.448', '205.875']", "type": "text", "field_key": "Other Tests – one test per line, <PERSON><PERSON><PERSON>"}, {"rect": "['419.855', '183.027', '593.448', '193.635']", "type": "text", "field_key": "Other Tests – one test per line, Wound specify source"}, {"rect": "['112.664', '155.238', '151.375', '166.119']", "type": "text", "field_key": "timeCollect1.1"}, {"rect": "['241.356', '304.664', '254.971', '315.66']", "type": "checkbox", "field_key": "hermatology1.8"}, {"rect": "['241.356', '292.62', '254.971', '303.616']", "type": "checkbox", "field_key": "hermatology1.9"}, {"rect": "['241.356', '280.577', '254.971', '291.573']", "type": "checkbox", "field_key": "hermatology1.10"}, {"rect": "['241.356', '268.014', '254.971', '279.01']", "type": "checkbox", "field_key": "hermatology1.11"}, {"rect": "['241.356', '256.489', '254.971', '267.486']", "type": "checkbox", "field_key": "hermatology1.12"}, {"rect": "['241.356', '244.446', '254.971', '255.442']", "type": "checkbox", "field_key": "hermatology1.13"}, {"rect": "['241.356', '231.402', '254.971', '242.398']", "type": "checkbox", "field_key": "hermatology1.14"}, {"rect": "['241.356', '218.359', '254.971', '229.355']", "type": "checkbox", "field_key": "hermatology1.15"}, {"rect": "['241.356', '206.315', '254.971', '217.311']", "type": "checkbox", "field_key": "hermatology1.16"}, {"rect": "['241.356', '194.271', '254.971', '205.268']", "type": "checkbox", "field_key": "hermatology1.17"}, {"rect": "['241.356', '182.228', '254.971', '193.224']", "type": "checkbox", "field_key": "hermatology1.18"}, {"rect": "['241.356', '169.184', '254.971', '180.181']", "type": "checkbox", "field_key": "hermatology1.19"}, {"rect": "['419.855', '169.987', '593.448', '180.595']", "type": "text", "field_key": "Other Tests – one test per line, Urine"}, {"rect": "['184.233', '154.599', '226.291', '166.119']", "type": "text", "field_key": "timeCollect2.1"}, {"rect": "['419.855', '156.547', '593.448', '167.155']", "type": "text", "field_key": "Other Tests – one test per line, Stool Culture"}, {"rect": "['112.664', '142.671', '151.375', '153.552']", "type": "text", "field_key": "timeCollect1.2"}, {"rect": "['184.233', '142.032', '226.93', '154.191']", "type": "text", "field_key": "timeCollect2.2"}, {"rect": "['241.717', '130.746', '413.596', '166.134']", "type": "text", "field_key": "OtherSwabs"}, {"rect": "['419.855', '144.68', '593.448', '155.288']", "type": "text", "field_key": "Other Tests – one test per line, Stool Ova & Parasites"}, {"rect": "['419.855', '132.44', '593.448', '143.048']", "type": "text", "field_key": "Other Tests – one test per line, Other Swabs  Pus specify source"}, {"rect": "['420.38', '120.2', '593.973', '130.808']", "type": "text", "field_key": "Other Tests – one test per line, Row 15"}, {"rect": "['420.105', '107.302', '593.698', '117.91']", "type": "text", "field_key": "Other Tests – one test per line, Row 16"}, {"rect": "['256.785', '97.16', '311.107', '107.869']", "type": "text", "field_key": "collectTime"}, {"rect": "['350.042', '95.979', '404.771', '107.869']", "type": "text", "field_key": "SCollectionDate"}, {"rect": "['419.695', '95.032', '593.288', '105.64']", "type": "text", "field_key": "Other Tests – one test per line, Row 17"}, {"rect": "['527.642', '60.3292', '580.531', '80.723']", "type": "checkbox", "field_key": "Print"}, {"rect": "['159.928', '37.9528', '238.244', '56.2451']", "type": "text", "field_key": "Date"}]