{"components": [{"key": "heading_finasteride_renewal", "html": "<h1 style=\"text-align:center;\"><strong>Hair-Loss Treatment Questionnaire</strong></h1><p>To provide the best possible care, please answer the following questions to the best of your ability.</p>", "type": "content", "input": false, "label": "Content", "tableView": false}, {"key": "heading_medical_history", "html": "<h2>Hair Loss History&nbsp;</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "mphl_age", "data": {"values": [{"label": "< 2 weeks", "value": "less_2_weeks"}, {"label": "2 - 4 weeks", "value": "2_4_weeks"}, {"label": "< 6 weeks", "value": "less_6_weeks"}, {"label": "6 weeks - 3 months", "value": "6weeks_3months"}, {"label": "3 - 6 months", "value": "3_6_months"}, {"label": "6 - 12 months", "value": "6_12_months"}, {"label": "12 - 24 months", "value": "12_24_months"}, {"label": "2 - 5 years", "value": "2_5_years"}, {"label": "5 - 10 years", "value": "5_10_years"}, {"label": "10 - 20 years", "value": "10_20_years"}, {"label": "20+ years", "value": "20+_years"}, {"label": "I don't know", "value": "cannot_recall"}]}, "type": "select", "input": true, "label": "How long ago did you first notice hair loss?", "confirm_label": "Hair Loss Onset:", "widget": "html5", "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "hair_loss_location", "type": "selectboxes", "input": true, "label": "Where did you first notice thinning or shedding of hair? (Select all that apply)", "values": [{"label": "Scalp / head", "value": "scalp"}, {"label": "Face or beard area", "value": "beard_face"}, {"label": "Eyebrows or eyelashes", "value": "brows_lashes"}, {"label": "Arms, legs, chest, back", "value": "body_hair"}, {"label": "Pubic region", "value": "pubic"}, {"label": "Other", "value": "other"}, {"label": "I’m not sure", "value": "unknown"}], "validate": {"required": true}, "inputType": "checkbox", "confirm_label": "Hair Loss Location:", "tableView": true, "optionsLabelPosition": "right"}, {"key": "scalp_hair_loss_sites", "type": "selectboxes", "input": true, "label": "Which part(s) of your scalp are affected? (Select all that apply)", "values": [{"label": "Temples / receding corners", "value": "temples"}, {"label": "Crown / top (vertex)", "value": "crown"}, {"label": "Middle of scalp", "value": "mid_scalp"}, {"label": "Diffuse thinning all over", "value": "diffuse"}, {"label": "Back of head", "value": "occipital"}, {"label": "Other scalp area", "value": "other"}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "customConditional": "show = data.hair_loss_location === 'scalp';", "optionsLabelPosition": "right"}, {"key": "hair_loss_other_detail", "type": "textarea", "input": true, "label": "Please describe the other location or scalp area:", "tableView": true, "autoExpand": false, "customConditional": "show = data.hair_loss_location === 'other' || (Array.isArray(data.scalp_hair_loss_sites) && data.scalp_hair_loss_sites.includes('other'));"}, {"key": "heading_current_treatment", "html": "<h3>Current Hair-Loss Treatment</h3>", "type": "content", "input": false, "label": "Content", "tableView": false}, {"key": "currently_on_meds", "type": "radio", "input": true, "label": "Are you currently taking any medication to prevent hair loss?", "values": [{"label": "Yes", "value": true}, {"label": "No", "value": false}], "validate": {"required": true}, "tableView": true, "confirm_label": "Current Hair Loss Medication:", "optionsLabelPosition": "right"}, {"key": "current_med_routes", "type": "selectboxes", "input": true, "label": "Which type(s) of treatment are you taking? (Select all that apply)", "values": [{"label": "Oral pill / capsule", "value": "oral"}, {"label": "Topical (gel / foam / liquid)", "value": "topical"}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "confirm_label": "Current Treatment Types:", "customConditional": "show = data.currently_on_meds === true;", "optionsLabelPosition": "right"}, {"key": "heading_current_oral", "html": "<h3>Current Oral Medications</h3>", "type": "content", "input": false, "tableView": false, "customConditional": "show = data.current_med_routes && data.current_med_routes.oral === true;"}, {"key": "current_oral_meds", "type": "selectboxes", "input": true, "label": "Select all oral medications you are taking:", "values": [{"label": "Finasteride 1 mg", "value": "finasteride_1mg"}, {"label": "Finasteride 1.25 mg (¼ × 5 mg tablet)", "value": "finasteride_1_25mg"}, {"label": "Dutasteride 0.5 mg", "value": "dutasteride"}, {"label": "Other oral medication", "value": "other"}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "confirm_label": "Current Oral Medications:", "customConditional": "show = data.current_med_routes && data.current_med_routes.oral === true;", "optionsLabelPosition": "right"}, {"key": "oral_med_duration", "data": {"values": [{"label": "< 3 months", "value": "<3_months"}, {"label": "3 - 6 months", "value": "3_6_months"}, {"label": "6 - 12 months", "value": "6_12_months"}, {"label": "1 - 2 years", "value": "1_2_years"}, {"label": "2 - 3 years", "value": "2_3_years"}, {"label": "3 - 5 years", "value": "3_5_years"}, {"label": "5 + years", "value": "5_plus_years"}]}, "type": "select", "input": true, "label": "How long have you been taking your oral medication?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Oral Medication Duration:", "customConditional": "show = data.current_med_routes && data.current_med_routes.oral === true;", "optionsLabelPosition": "right"}, {"key": "oral_consistency", "type": "radio", "input": true, "label": "How consistently do you take your oral medication?", "values": [{"label": "Every day, never miss", "value": "always"}, {"label": "Miss 1-2 doses per week", "value": "occasional_miss"}, {"label": "Miss ≥3 doses per week", "value": "frequent_miss"}], "validate": {"required": true}, "confirm_label": "Oral Medication Consistency:", "tableView": true, "customConditional": "show = data.current_med_routes && data.current_med_routes.oral === true;", "optionsLabelPosition": "right"}, {"key": "oral_breaks", "type": "radio", "input": true, "label": "Have you had any continuous break (no tablets at all) lasting a month or longer?", "values": [{"label": "Yes", "value": true}, {"label": "No", "value": false}], "validate": {"required": true}, "tableView": true, "confirm_label": "Oral Medication Breaks:", "customConditional": "show = data.current_med_routes && data.current_med_routes.oral === true;", "optionsLabelPosition": "right"}, {"key": "oral_break_length", "data": {"values": [{"label": "1 - 3 months", "value": "1_3_months"}, {"label": "3 - 6 months", "value": "3_6_months"}, {"label": "6 - 12 months", "value": "6_12_months"}, {"label": "Over 1 year", "value": "over_1_year"}]}, "type": "select", "input": true, "label": "Longest break from tablets:", "confirm_label": "Oral Medication Break Length:", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.oral_breaks === true;", "optionsLabelPosition": "right"}, {"key": "heading_current_topical", "html": "<h3>Current Topical Medications</h3>", "type": "content", "input": false, "tableView": false, "customConditional": "show = data.current_med_routes && data.current_med_routes.topical === true;"}, {"key": "current_topical_meds", "type": "selectboxes", "input": true, "label": "Select all topical treatments you are using:", "values": [{"label": "Finasteride 0.25 % + Minoxidil 5 % gel", "value": "fin_minox_gel"}, {"label": "Finasteride 0.25 % gel", "value": "fin_gel"}, {"label": "Minoxidil 5 % foam / solution", "value": "minox5"}, {"label": "Other topical treatment", "value": "other"}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "confirm_label": "Current Topical Medications:", "customConditional": "show = data.current_med_routes && data.current_med_routes.topical === true;", "optionsLabelPosition": "right"}, {"key": "topical_med_duration", "data": {"values": [{"label": "< 3 months", "value": "<3_months"}, {"label": "3 - 6 months", "value": "3_6_months"}, {"label": "6 - 12 months", "value": "6_12_months"}, {"label": "1 - 2 years", "value": "1_2_years"}, {"label": "2 - 3 years", "value": "2_3_years"}, {"label": "3 - 5 years", "value": "3_5_years"}, {"label": "5 + years", "value": "5_plus_years"}]}, "type": "select", "input": true, "label": "How long have you been using your topical treatment?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Topical Medication Duration:", "customConditional": "show = data.current_med_routes && data.current_med_routes.topical === true;", "optionsLabelPosition": "right"}, {"key": "topical_consistency", "type": "radio", "input": true, "label": "How consistently do you apply your topical treatment?", "values": [{"label": "Every day, never miss", "value": "always"}, {"label": "Miss 1-2 applications per week", "value": "occasional_miss"}, {"label": "Miss ≥3 applications per week", "value": "frequent_miss"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Topical Medication Consistency:", "customConditional": "show = data.current_med_routes && data.current_med_routes.topical === true;", "optionsLabelPosition": "right"}, {"key": "topical_breaks", "type": "radio", "input": true, "label": "Have you had any continuous break (no topical at all) lasting a month or longer?", "values": [{"label": "Yes", "value": true}, {"label": "No", "value": false}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.current_med_routes && data.current_med_routes.topical === true;", "optionsLabelPosition": "right"}, {"key": "topical_break_length", "data": {"values": [{"label": "1 - 3 months", "value": "1_3_months"}, {"label": "3 - 6 months", "value": "3_6_months"}, {"label": "6 - 12 months", "value": "6_12_months"}, {"label": "Over 1 year", "value": "over_1_year"}]}, "type": "select", "input": true, "label": "Longest break from topical treatment:", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Topical Medication Break Length:", "customConditional": "show = data.topical_breaks === true;", "optionsLabelPosition": "right"}, {"key": "current_med_other_text", "type": "textarea", "input": true, "label": "Please describe the other medication:", "tableView": true, "autoExpand": false, "confirm_label": "Other Medication Description:", "customConditional": "show = (data.current_oral_meds && data.current_oral_meds.other === true) || (data.current_topical_meds && data.current_topical_meds.other === true);"}, {"key": "heading_past_treatment_details", "html": "<h2>Past Hair-Loss Treatment Details</h2>", "type": "content", "input": false, "label": "Content", "tableView": false}, {"key": "previously_on_meds", "type": "radio", "input": true, "label": "Have you taken hair-loss medication in the past?", "values": [{"label": "Yes", "value": true}, {"label": "No", "value": false}], "validate": {"required": true}, "tableView": true, "confirm_label": "Past Hair Loss Medication:", "optionsLabelPosition": "right"}, {"key": "past_med_routes", "type": "selectboxes", "input": true, "label": "Which type(s) of treatment did you use? (Select all that apply)", "values": [{"label": "Oral pill / capsule", "value": "oral"}, {"label": "Topical gel / foam / liquid", "value": "topical"}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "confirm_label": "Past Treatment Types:", "customConditional": "show = data.previously_on_meds === true;", "optionsLabelPosition": "right"}, {"key": "heading_past_oral", "html": "<h3>Past Oral Medications</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.past_med_routes && data.past_med_routes.oral === true;"}, {"key": "past_oral_meds", "type": "selectboxes", "input": true, "label": "Select all oral medications you used:", "values": [{"label": "Finasteride 1 mg", "value": "finasteride_1mg"}, {"label": "Finasteride 1.25 mg (¼ × 5 mg tablet)", "value": "finasteride_1_25mg"}, {"label": "Dutasteride 0.5 mg", "value": "dutasteride"}, {"label": "Other oral medication", "value": "other"}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "confirm_label": "Past Oral Medications:", "customConditional": "show = data.past_med_routes && data.past_med_routes.oral === true;", "optionsLabelPosition": "right"}, {"key": "past_oral_duration", "data": {"values": [{"label": "< 3 months", "value": "<3_months"}, {"label": "3 - 6 months", "value": "3_6_months"}, {"label": "6 - 12 months", "value": "6_12_months"}, {"label": "1 - 2 years", "value": "1_2_years"}, {"label": "2 - 3 years", "value": "2_3_years"}, {"label": "3 - 5 years", "value": "3_5_years"}, {"label": "5 - 7 years", "value": "5_7_years"}, {"label": "7 - 10 years", "value": "7_10_years"}, {"label": "10 - 15 years", "value": "10_15_years"}, {"label": "15+ years", "value": "15_plus_years"}]}, "type": "select", "input": true, "confirm_label": "Past Oral Medication Duration:", "label": "Total time you used oral treatment:", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.past_med_routes && data.past_med_routes.oral === true;", "optionsLabelPosition": "right"}, {"key": "heading_past_topical", "html": "<h3>Past Topical Treatments</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.past_med_routes && data.past_med_routes.topical === true;"}, {"key": "past_topical_meds", "type": "selectboxes", "input": true, "label": "Select all topical treatments you used:", "values": [{"label": "Finasteride 0.25 % + Minoxidil 5 % gel", "value": "fin_minox_gel"}, {"label": "Finasteride 0.25 % gel", "value": "fin_gel"}, {"label": "Minoxidil 5 % foam / solution", "value": "minox5"}, {"label": "Other topical treatment", "value": "other"}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "confirm_label": "Past Topical Medications:", "customConditional": "show = data.past_med_routes && data.past_med_routes.topical === true;", "optionsLabelPosition": "right"}, {"key": "past_topical_duration", "data": {"values": [{"label": "< 3 months", "value": "<3_months"}, {"label": "3 - 6 months", "value": "3_6_months"}, {"label": "6 - 12 months", "value": "6_12_months"}, {"label": "1 - 2 years", "value": "1_2_years"}, {"label": "2 - 3 years", "value": "2_3_years"}, {"label": "3 - 5 years", "value": "3_5_years"}, {"label": "5 - 7 years", "value": "5_7_years"}, {"label": "7 - 10 years", "value": "7_10_years"}, {"label": "10 - 15 years", "value": "10_15_years"}, {"label": "15+ years", "value": "15_plus_years"}]}, "type": "select", "input": true, "label": "Total time you used topical treatment:", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Past Topical Medication Duration:", "customConditional": "show = data.past_med_routes && data.past_med_routes.topical === true;", "optionsLabelPosition": "right"}, {"key": "past_med_other_text", "type": "textarea", "input": true, "label": "Please specify the other medication:", "tableView": true, "autoExpand": false, "customConditional": "show = (data.past_oral_meds && data.past_oral_meds.other === true) || (data.past_topical_meds && data.past_topical_meds.other === true);"}, {"key": "reason_stopping_med", "type": "selectboxes", "input": true, "label": "Why did you stop the medication? (Select all that apply)", "values": [{"label": "Couldn’t get a prescription", "value": "access"}, {"label": "Stopped on my own decision", "value": "self"}, {"label": "Side-effects", "value": "side_effects"}, {"label": "Other", "value": "other"}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "confirm_label": "Reason for Stopping Medication:", "customConditional": "show = data.previously_on_meds === true;", "optionsLabelPosition": "right"}, {"key": "side_effects_list", "type": "selectboxes", "input": true, "label": "Which side-effects did you experience?", "values": [{"label": "Changes in libido", "value": "libido"}, {"label": "Erectile dysfunction", "value": "ed"}, {"label": "Mood changes / depression", "value": "mood"}, {"label": "Anxiety", "value": "anxiety"}, {"label": "Breast tenderness / swelling", "value": "gynecomastia"}, {"label": "Other side-effect", "value": "other"}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "confirm_label": "Side Effects Experienced:", "customConditional": "show = data.reason_stopping_med && data.reason_stopping_med.side_effects === true;", "optionsLabelPosition": "right"}, {"key": "side_effects_resolved", "type": "radio", "input": true, "label": "Have these side-effects resolved since stopping the medication?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Side Effects Resolved:", "customConditional": "show = data.side_effects_list !== undefined;", "optionsLabelPosition": "right"}, {"key": "reason_stopping_other_text", "type": "textarea", "input": true, "label": "Please describe the other reason:", "confirm_label": "Other Reason for Stopping Medication:", "tableView": true, "autoExpand": false, "customConditional": "show = data.reason_stopping_med && data.reason_stopping_med.other === true;"}, {"key": "other_medications_or_supplements", "type": "selectboxes", "input": true, "label": "Are you taking any other medications, supplements or using a dermaroller?", "values": [{"label": "Minoxidil <strong>(Brand: Rogaine)</strong>", "value": "minoxidil"}, {"label": "<PERSON><PERSON><PERSON>", "value": "biotin"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"label": "None of the above", "value": "none_of_the_above"}], "validate": {"required": true}, "inputType": "checkbox", "confirm_label": "Other Medications or Supplements:", "tableView": true, "optionsLabelPosition": "right"}, {"key": "heading_medication_side_effects", "html": "<h3>Medication Side Effects&nbsp;</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.currently_on_meds == true || data.previously_on_meds == true;"}, {"key": "side_effects", "type": "selectboxes", "input": true, "label": "Have you experienced any of the following side effects on finasteride or dutasteride:", "inline": false, "values": [{"label": "Changes In Libido", "value": "libido_changes"}, {"label": "Erectile Dysfunction", "value": "erectile_dysfunction"}, {"label": "Lower mood or depression", "value": "mde"}, {"label": "Anxiety", "value": "anxiety"}, {"label": "Suicidal thoughts", "value": "suicidal_thoughts"}, {"label": "None of the above", "value": "none_of_the_above"}], "validate": {"required": true}, "inputType": "checkbox", "confirm_label": "Medication Side Effects:", "tableView": true, "customConditional": "show = data.currently_on_meds == true || data.previously_on_meds == true;", "optionsLabelPosition": "right"}, {"key": "heading_diagnosis_origin", "type": "content", "input": false, "label": "Content", "tableView": false, "html": "<h3>Diagnosis History</h3>"}, {"key": "original_mphl_diagnosis", "type": "radio", "input": true, "label": "Who diagnosed you with Male Pattern Hair Loss (MPHL) originally?", "inline": false, "values": [{"label": "Family Doctor / General Practitioner", "value": "general_practitioner", "shortcut": ""}, {"label": "Nurse practitioner", "value": "nurse_practitioner", "shortcut": ""}, {"label": "<PERSON><PERSON><PERSON>", "value": "naturopath", "shortcut": ""}, {"label": "Self-Diagnosis", "value": "self-diagnosis", "shortcut": ""}, {"label": "Prefer not to disclose", "value": "prefer_not_to_disclose", "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "confirm_label": "Original Hair Loss Diagnosis Provided By:", "optionsLabelPosition": "right"}, {"key": "diagnosis_other_provider", "type": "textarea", "input": true, "label": "Please state who diagnosed you with Male Pattern Hair Loss (MPHL):", "tableView": true, "autoExpand": false, "customConditional": "show = data.original_mphl_diagnosis == 'other';"}, {"key": "male_pattern_hair_loss_characteristics", "type": "selectboxes", "input": true, "label": "Please select all the characteristics of male-pattern hair loss that apply to you:", "inputType": "checkbox", "values": [{"label": "Receding hairline", "value": "receding_hairline"}, {"label": "Thinning at the crown", "value": "thinning_crown"}, {"label": "Bald spot on the top of the head", "value": "bald_spot"}, {"label": "Generalised thinning", "value": "generalized_thinning"}, {"label": "Gradual hair loss over time", "value": "gradual_hair_loss"}, {"label": "Family history of male-pattern hair loss", "value": "family_history"}], "tableView": true, "confirm_label": "Hair Loss Characteristics:", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_mphl_chars", "type": "checkbox", "input": true, "label": "None of the above", "defaultValue": false, "customClass": "mt-n3", "errors": {"custom": "Please tick at least one box or select “None of the above”."}, "validate": {"custom": "valid = !!data.none_of_the_above_mphl_chars || _.some(_.values(data.male_pattern_hair_loss_characteristics));"}, "tableView": true}, {"key": "mphl_chars_not_present", "type": "textfield", "input": true, "label": "Patient indicated they do NOT have the following hair loss characteristics:", "hidden": true, "disabled": true, "tableView": true, "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.male_pattern_hair_loss_characteristics, _.negate(_.identity))), _.startCase), ', '), /_/g, ' ');"}, {"key": "mphl_chars_not_present", "type": "textfield", "input": true, "label": "Patient indicated they do NOT have the following MPHL characteristics:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "MPHL characteristics (absent):", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.male_pattern_hair_loss_characteristics, _.negate(_.identity))), _.startCase), ', '), /_/g, ' ');"}, {"key": "blood_tests_or_examinations", "type": "radio", "input": true, "label": "Have you had any blood tests or examinations related to your hair loss?", "inline": false, "values": [{"label": "Yes, Before Starting Treatment", "value": "before_treatment"}, {"label": "Yes, During Treatment", "value": "during_treatment"}, {"label": "Yes, Both Before and During Treatment", "value": "both_before_and_during"}, {"label": "No, I Haven't", "value": "no_examinations"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Blood Tests or Examinations:", "optionsLabelPosition": "right"}, {"key": "heading_tolerability", "type": "content", "input": false, "label": "Content", "tableView": false, "html": "<h2>Medication Tolerability &amp; Effectiveness</h2>", "customConditional": "show = (data.currently_on_meds === true) || (data.previously_on_meds === true);"}, {"key": "current_side_effects", "type": "selectboxes", "input": true, "label": "Have you experienced any of the following side-effects while taking your current medication?", "inputType": "checkbox", "values": [{"label": "Changes in libido", "value": "libido"}, {"label": "Erectile dysfunction", "value": "ed"}, {"label": "Mood changes or depression", "value": "mood"}, {"label": "Anxiety", "value": "anxiety"}, {"label": "Breast tenderness / swelling", "value": "gynecomastia"}, {"label": "Other side-effect", "value": "other"}], "tableView": true, "optionsLabelPosition": "right", "confirm_label": "Current Side Effects Experienced:", "customConditional": "show = data.currently_on_meds === true;"}, {"key": "none_of_the_above_current_side_effects", "type": "checkbox", "input": true, "label": "None of the above", "defaultValue": false, "customClass": "mt-n3", "errors": {"custom": "Please tick at least one box or select “None of the above”."}, "validate": {"custom": "valid = !!data.none_of_the_above_current_side_effects || _.some(_.values(data.current_side_effects));"}, "tableView": true, "customConditional": "show = data.currently_on_meds === true;"}, {"key": "current_side_effects_not_present", "type": "textfield", "input": true, "label": "Patient indicated they do NOT have the following side-effects:", "hidden": true, "disabled": true, "tableView": true, "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.current_side_effects, _.negate(_.identity))), _.startCase), ', '), /_/g, ' ');"}, {"key": "current_improvement", "type": "radio", "input": true, "label": "Since starting your current medication, have you noticed improvement in hair growth or reduced shedding?", "values": [{"label": "Yes - significant improvement", "value": "significant"}, {"label": "Yes - some improvement", "value": "some"}, {"label": "No noticeable change", "value": "none"}, {"label": "Hair loss has worsened", "value": "worsened"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Current Improvement in Hair Growth:", "optionsLabelPosition": "right", "customConditional": "show = data.currently_on_meds === true;"}, {"key": "duration_advice", "type": "content", "input": false, "label": "Content", "tableView": false, "html": "<p class=\"alert alert-info\">Best clinical practice recommends continuing treatment for at least six months before judging full benefit.</p>", "customConditional": "show = data.currently_on_meds === true && (['<3_months','3_6_months'].includes(data.oral_med_duration) || ['<3_months','3_6_months'].includes(data.topical_med_duration));"}, {"key": "past_side_effects", "type": "selectboxes", "input": true, "label": "When you were on the medication, did you experience any of the following side-effects?", "inputType": "checkbox", "values": [{"label": "Changes in libido", "value": "libido"}, {"label": "Erectile dysfunction", "value": "ed"}, {"label": "Mood changes or depression", "value": "mood"}, {"label": "Anxiety", "value": "anxiety"}, {"label": "Breast tenderness / swelling", "value": "gynecomastia"}, {"label": "Other side-effect", "value": "other"}], "tableView": true, "optionsLabelPosition": "right", "confirm_label": "Past Side-effects Experienced:", "customConditional": "show = data.previously_on_meds === true;"}, {"key": "none_of_the_above_past_side_effects", "type": "checkbox", "input": true, "label": "None of the above", "defaultValue": false, "customClass": "mt-n3", "errors": {"custom": "Please tick at least one box or select “None of the above”."}, "validate": {"custom": "valid = !!data.none_of_the_above_past_side_effects || _.some(_.values(data.past_side_effects));"}, "tableView": true, "customConditional": "show = data.previously_on_meds === true;"}, {"key": "past_side_effects_not_present", "type": "textfield", "input": true, "label": "Patient indicated they do NOT have the following past side-effects:", "hidden": true, "disabled": true, "tableView": true, "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.past_side_effects, _.negate(_.identity))), _.startCase), ', '), /_/g, ' ');"}, {"key": "aa_sudden_hair_loss", "type": "radio", "input": true, "label": "Have you experienced sudden hair loss (hair loss within the last 3 months) that led to bald patches on your scalp?", "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "Do not know", "value": "do_not_know", "shortcut": ""}], "validate": {"required": true}, "inputType": "radio", "tableView": true, "confirm_label": "Sudden Hair Loss:", "customConditional": "show = data.currently_on_meds == false && data.previously_on_meds == false;", "optionsLabelPosition": "right"}, {"key": "traction_alopecia_hairline_temples", "type": "radio", "input": true, "label": "Have you noticed hair loss primarily around your hairline or at the temples?", "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "Do not know", "value": "do_not_know", "shortcut": ""}], "validate": {"required": true}, "inputType": "radio", "tableView": true, "confirm_label": "Hair Loss at Hairline or Temples:", "customConditional": "show = data.currently_on_meds == false && data.previously_on_meds == false;", "optionsLabelPosition": "right"}, {"key": "traction_alopecia_scalp_pain_itching_redness", "type": "radio", "input": true, "label": "Do you experience scalp pain, itching, or redness in areas where hair loss is occurring?", "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "Do not know", "value": "do_not_know", "shortcut": ""}], "validate": {"required": true}, "inputType": "radio", "confirm_label": "Scalp Pain, Itching, or Redness:", "tableView": true, "customConditional": "show = data.currently_on_meds == false && data.previously_on_meds == false;", "optionsLabelPosition": "right"}, {"key": "heading_fm_medical_history", "html": "<h3>Family History&nbsp;</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.currently_on_meds == false && data.previously_on_meds == false;"}, {"key": "mphl_family_history", "type": "selectboxes", "input": true, "label": "Is there a family history of hair loss, and if so, which relatives are affected?", "values": [{"label": "Father/brother", "value": "father_brother", "shortcut": ""}, {"label": "Mother/sister", "value": "mother_sister", "shortcut": ""}, {"label": "Grandparents", "value": "grandparents", "shortcut": ""}, {"label": "Aunts/Uncles", "value": "aunts_uncles", "shortcut": ""}, {"label": "No family history", "value": "no_family_history", "shortcut": ""}, {"label": "Do not know", "value": "do_not_know", "shortcut": ""}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "confirm_label": "Family History of Hair Loss:", "customConditional": "show = data.currently_on_meds == false && data.previously_on_meds == false;", "optionsLabelPosition": "right"}, {"key": "family_balding_present", "type": "radio", "input": true, "label": "Have your father or brother actually experienced male-pattern balding (thinning or recession on the scalp)?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Unsure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Family Balding Present:", "optionsLabelPosition": "right", "customConditional": "show = data.mphl_family_history && data.mphl_family_history.father_brother === true;"}, {"key": "family_hair_medication", "type": "selectboxes", "input": true, "label": "Do you know if your father or brother has used any medication or treatment for hair loss? (Select all that apply)", "inputType": "checkbox", "values": [{"label": "Finasteride", "value": "finasteride"}, {"label": "Dutasteride", "value": "dutasteride"}, {"label": "Topical minoxidil", "value": "minoxidil"}, {"label": "Hair-transplant surgery", "value": "transplant"}, {"label": "Other treatment", "value": "other"}, {"label": "None / no treatment", "value": "none"}, {"label": "I’m not sure", "value": "unsure"}], "validate": {"custom": "valid = _.some(_.values(data.family_hair_medication));"}, "tableView": true, "optionsLabelPosition": "right", "confirm_label": "Family Hair Loss Medication:", "customConditional": "show = data.mphl_family_history && data.mphl_family_history.father_brother === true;"}, {"key": "family_hair_medication_other", "type": "textfield", "input": true, "label": "Please specify the other treatment:", "confirm_label": "Family Hair Loss Medication Other:", "tableView": true, "autoExpand": false, "customConditional": "show = data.family_hair_medication && data.family_hair_medication.other === true;"}, {"key": "heading_previous_diagnosis", "html": "<h4>Previous Diagnosis&nbsp;</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.currently_on_meds == false && data.previously_on_meds == false;"}, {"key": "aa_previous_diagnosis", "type": "radio", "input": true, "label": "Has a healthcare professional ever mentioned or discussed the possibility of alopecia areata, alopecia totalis, or alopecia universalis with you?", "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "Do not know", "value": "do_not_know", "shortcut": ""}], "validate": {"required": true}, "inputType": "radio", "confirm_label": "Previous Diagnosis of Alopecia Areata:", "tableView": true, "customConditional": "show = data.currently_on_meds == false && data.previously_on_meds == false;", "optionsLabelPosition": "right"}, {"key": "hx_pcos_cah_cushing_tumor", "type": "selectboxes", "input": true, "label": "Please select if you have any of the following conditions:", "inputType": "checkbox", "values": [{"label": "Congenital Adrenal Hyperplasia", "value": "congenital_adrenal_hyperplasia"}, {"label": "Cushing's Syndrome", "value": "cushings_syndrome"}, {"label": "Adrenal Gland Tumors", "value": "adrenal_gland_tumors"}], "tableView": true, "optionsLabelPosition": "right", "confirm_label": "Other Medical Conditions:", "adminFlag": true, "customConditional": "show = data.currently_on_meds == false && data.previously_on_meds == false;"}, {"key": "none_of_the_above_hx_conditions", "type": "checkbox", "input": true, "label": "None of the above", "defaultValue": false, "customClass": "mt-n3", "errors": {"custom": "Please tick at least one box or select “None of the above”."}, "validate": {"custom": "valid = !!data.none_of_the_above_hx_conditions || _.some(_.values(data.hx_pcos_cah_cushing_tumor));"}, "tableView": true, "customConditional": "show = data.currently_on_meds == false && data.previously_on_meds == false;"}, {"key": "hx_conditions_not_present", "type": "textfield", "input": true, "label": "Patient indicated they do NOT have the following conditions:", "hidden": true, "disabled": true, "tableView": true, "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.hx_pcos_cah_cushing_tumor, _.negate(_.identity))), _.startCase), ', '), /_/g, ' ');"}, {"key": "atopy_conditions", "type": "selectboxes", "input": true, "label": "Do you have (or have you ever been told you have) any of the following atopy-related conditions?", "inputType": "checkbox", "values": [{"label": "Allergies (seasonal or environmental)", "value": "allergies"}, {"label": "Asthma", "value": "asthma"}, {"label": "Eczema / atopic dermatitis", "value": "eczema"}], "tableView": true, "confirm_label": "Atopy Conditions:", "optionsLabelPosition": "right", "customConditional": "show = data.currently_on_meds == false && data.previously_on_meds == false;"}, {"key": "none_of_the_above_atopy", "type": "checkbox", "input": true, "label": "None of the above", "defaultValue": false, "customClass": "mt-n3", "errors": {"custom": "Please select a condition or tick “None of the above”."}, "validate": {"custom": "valid = !!data.none_of_the_above_atopy || _.some(_.values(data.atopy_conditions));"}, "tableView": true, "customConditional": "show = data.currently_on_meds == false && data.previously_on_meds == false;"}, {"key": "atopy_conditions_not_present", "type": "textfield", "input": true, "label": "Patient indicated they do NOT have the following atopy conditions:", "hidden": true, "disabled": true, "tableView": true, "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.atopy_conditions, _.negate(_.identity))), _.startCase), ', '), /_/g, ' ');"}, {"key": "autoimmune_conditions", "type": "selectboxes", "input": true, "label": "Please select if you have any of the following conditions:", "inputType": "checkbox", "values": [{"label": "Vitiligo", "value": "vitiligo"}, {"label": "Diabetes", "value": "diabetes"}, {"label": "Hypothyroidism (low thyroid)", "value": "hypothyroidism"}, {"label": "Rheumatoid arthritis", "value": "rheumatoid_arthritis"}, {"label": "Discoid lupus erythematosus", "value": "lupus"}], "tableView": true, "confirm_label": "Autoimmune Conditions:", "optionsLabelPosition": "right", "customConditional": "show = data.currently_on_meds == false && data.previously_on_meds == false;"}, {"key": "none_of_the_above_autoimmune", "type": "checkbox", "input": true, "label": "None of the above", "defaultValue": false, "customClass": "mt-n3", "errors": {"custom": "Please select a condition or tick “None of the above”."}, "validate": {"custom": "valid = !!data.none_of_the_above_autoimmune || _.some(_.values(data.autoimmune_conditions));"}, "tableView": true, "customConditional": "show = data.currently_on_meds == false && data.previously_on_meds == false;"}, {"key": "autoimmune_conditions_not_present", "type": "textfield", "input": true, "label": "Patient indicated they do NOT have the following conditions:", "hidden": true, "disabled": true, "tableView": true, "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.autoimmune_conditions, _.negate(_.identity))), _.startCase), ', '), /_/g, ' ');"}, {"key": "te_hx_vitamin_minteral_def", "type": "selectboxes", "input": true, "label": "Have you been told you have problems with any of the following?", "inputType": "checkbox", "values": [{"label": "Low hemoglobin", "value": "hemoglobin"}, {"label": "Low iron", "value": "iron"}, {"label": "Overactive or underactive thyroid", "value": "thyroid"}, {"label": "Low vitamin D", "value": "vitamin_d"}], "tableView": true, "confirm_label": "Vitamin or Mineral Deficiencies:", "optionsLabelPosition": "right", "customConditional": "show = data.currently_on_meds == false && data.previously_on_meds == false;"}, {"key": "none_of_the_above_vitamin_def", "type": "checkbox", "input": true, "label": "None of the above", "defaultValue": false, "customClass": "mt-n3", "errors": {"custom": "Please select a condition or tick “None of the above”."}, "validate": {"custom": "valid = !!data.none_of_the_above_vitamin_def || _.some(_.values(data.te_hx_vitamin_minteral_def));"}, "tableView": true, "customConditional": "show = data.currently_on_meds == false && data.previously_on_meds == false;"}, {"key": "vitamin_def_not_present", "type": "textfield", "input": true, "label": "Patient indicated they do NOT have the following vitamin/mineral problems:", "hidden": true, "disabled": true, "tableView": true, "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.te_hx_vitamin_minteral_def, _.negate(_.identity))), _.startCase), ', '), /_/g, ' ');"}, {"key": "heading_potential_triggers", "html": "<h4>Additional Factors&nbsp;</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.currently_on_meds == false && data.previously_on_meds == false;"}, {"key": "te_recent_acute_event", "type": "radio", "input": true, "label": "Have you recently undergone a major surgery, experienced significant weight loss, or been through a highly stressful event?", "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "Do not know", "value": "do_not_know", "shortcut": ""}], "validate": {"required": true}, "inputType": "radio", "confirm_label": "Recent Acute Event:", "tableView": true, "customConditional": "show = data.currently_on_meds == false && data.previously_on_meds == false;", "optionsLabelPosition": "right"}, {"key": "te_new_product_hair_care_routine", "type": "radio", "input": true, "label": "Have you experienced any changes in your hair care routine or used new hair products?", "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "Do not know", "value": "do_not_know", "shortcut": ""}], "validate": {"required": true}, "inputType": "radio", "tableView": true, "confirm_label": "New Hair Care Routine or Products:", "customConditional": "show = data.currently_on_meds == false && data.previously_on_meds == false;", "optionsLabelPosition": "right"}, {"key": "traction_alopecia_heading", "html": "<h4>Risk Factors</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.currently_on_meds == false && data.previously_on_meds == false;"}, {"key": "tractional_tight_hairstyles", "type": "radio", "input": true, "label": "Have you worn tight hairstyles, such as braids, ponytails, or buns, frequently or for extended periods of time?", "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "Do not know", "value": "do_not_know", "shortcut": ""}], "validate": {"required": true}, "inputType": "radio", "tableView": true, "confirm_label": "Tight Hairstyles:", "customConditional": "show = data.currently_on_meds == false && data.previously_on_meds == false;", "optionsLabelPosition": "right"}, {"key": "traction_alopecia_recommendation", "type": "content", "input": false, "label": "Content", "tableView": false, "html": "<p><strong>Recommendation:</strong> Frequently wearing tight hairstyles (braids, ponytails, buns) can cause traction alopecia - a form of reversible hair loss that can still contribute to long-term thinning. Loosening tension or varying styles helps protect hair follicles.</p>", "customConditional": "show = data.tractional_tight_hairstyles === 'yes';"}, {"key": "traction_alopecia_ack", "type": "radio", "input": true, "label": "I understand the recommendation to avoid tight hairstyles:", "values": [{"label": "I understand", "value": "understand"}, {"label": "I do not understand", "value": "do_not_understand"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "confirm_label": "Tight Hairstyles Acknowledgment:", "customConditional": "show = data.tractional_tight_hairstyles === 'yes';"}, {"key": "photo_upload_header", "type": "content", "input": false, "label": "Content", "tableView": false, "html": "</br><h2>Photo Upload&nbsp;<span style=\"color:#dc3545;\">(Required to proceed)</span></h2><p>To complete your assessment, please upload <strong>all&nbsp;three&nbsp;views</strong> in good lighting:</p><ul><li>The crown / top-back of your head</li><li>Your left temple</li><li>Your right temple</li></ul><p>Clear images help us confirm the pattern and recommend the right treatment.</p>"}, {"key": "uploadUrl", "url": "/app/q/{qpk}/formio-files/{pk}/", "type": "file", "image": true, "input": true, "label": "Upload: URL", "webcam": true, "capture": "user", "storage": "url", "multiple": true, "fileTypes": [{"label": "", "value": ""}], "tableView": true, "validateWhenHidden": false}, {"key": "intake_kind", "type": "textfield", "input": true, "label": "Intake Kind", "hidden": true, "disabled": true, "tableView": true, "clearOnHide": false, "calculateValue": "value = !data.intake_denial_reasons?.length ? 'mail' : 'chat';"}, {"key": "intake_mt_key", "type": "textfield", "input": true, "label": "Intake Mail Template Key", "hidden": true, "disabled": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.intake_kind=='mail'?'intake-hairloss-rx':'intake-denial'"}, {"key": "allowed_rt_keys", "type": "textfield", "input": true, "label": "Allowed Rep<PERSON>", "hidden": true, "disabled": true, "multiple": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.intake_mt_key=='intake-denial'?[]:['get-rx','appointment-intake','edit-intake']"}]}