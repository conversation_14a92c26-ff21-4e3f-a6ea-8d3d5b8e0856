{"components": [{"key": "heading_urinary_symptoms", "html": "<h2 class='text-center'>Cold Symptoms</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "changes_cold", "type": "radio", "input": true, "label": "Have you noticed any of the following cold or flu-like symptoms?<ul><li>Runny or stuffy nose</li><li>Sore throat or hoarseness</li><li>Cough or chest congestion</li><li>Headache, sinus pressure, or fatigue</li></ul>", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "cold_symptom_overview", "type": "selectboxes", "input": true, "label": "Are you currently experiencing any of the following symptoms?", "values": [{"label": "Runny or stuffy nose", "value": "runny_nose"}, {"label": "Sore throat or hoarseness", "value": "sore_throat"}, {"label": "Cough (dry or productive)", "value": "cough"}, {"label": "Sores in the mouth", "value": "mouth_sores"}, {"label": "Other cold or flu-like symptoms", "value": "other"}], "adminFlag": true, "inputType": "checkbox", "tableView": true, "customConditional": "show = data.changes_cold === 'yes'", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_cold_symptom_overview", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "Please select at least one symptom or check 'None of the above.'"}, "validate": {"custom": "valid = !!data.none_of_the_above_cold_symptom_overview || _.some(_.values(data.cold_symptom_overview));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.changes_cold === 'yes'"}, {"key": "cold_symptoms_present", "type": "textfield", "input": true, "label": "Patient indicated they have the following cold symptoms:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "You have the following cold symptoms:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.cold_symptom_overview)), _.capitalize), ', '), /_/g, ' ');"}, {"key": "cold_symptoms_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following cold symptoms:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "You DO NOT have the following cold symptoms:", "calculateValue": "value = _.replace(_.join(_.map(_.difference(_.keys(_.pickBy(data.cold_symptom_overview, _.negate(_.identity))),['other']), _.capitalize), ', '), /_/g, ' ');"}, {"key": "stated_other_symptoms_cold", "type": "textarea", "input": true, "label": "Please describe any other cold or flu-like symptoms you are experiencing:", "placeholder": "e.g. sneezing, sore muscles, chills, headache...", "adminFlag": true, "tableView": true, "autoExpand": false, "customConditional": "show = data.cold_symptom_overview?.other === true"}, {"key": "cold_symptom_contradiction_warning", "html": "<div style='border-left: 4px solid #dc3545; background-color: #f8d7da; padding: 10px; margin-bottom: 10px;'><strong>Notice:</strong> You indicated that you have cold symptoms, but then selected \"None of the above.\" Please review your answers above and make sure they reflect your current symptoms accurately.</div>", "type": "content", "input": false, "customConditional": "show = data.changes_cold === 'yes' && data.none_of_the_above_cold_symptom_overview === true"}, {"key": "cold_symptom_onset_pattern", "type": "radio", "input": true, "label": "Did your cold symptoms start around the same time or over separate days?", "values": [{"label": "All symptoms started around the same time", "value": "same_time"}, {"label": "Symptoms started on separate days", "value": "separate_days"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.cold_symptom_overview && _.sum(_.values(data.cold_symptom_overview).map(Number)) >= 2", "optionsLabelPosition": "right"}, {"key": "durations_cold", "type": "textfield", "input": true, "label": "Durations:", "hidden": true, "disabled": true, "multiple": true, "tableView": false, "clearOnHide": false, "calculateValue": [{"label": "Doesn't apply", "value": "doesn't_apply"}, {"label": "Last night", "value": "last_night"}, {"label": "Today", "value": "today"}, {"label": "1 day ago", "value": "1_day_ago"}, {"label": "2 days ago", "value": "2_days_ago"}, {"label": "3 days ago", "value": "3_days_ago"}, {"label": "4 days ago", "value": "4_days_ago"}, {"label": "5 days ago", "value": "5_days_ago"}, {"label": "6 days ago", "value": "6_days_ago"}, {"label": "7 days ago", "value": "7_days_ago"}, {"label": "8 days ago", "value": "8_days_ago"}, {"label": "9 days ago", "value": "9_days_ago"}, {"label": "10 days ago", "value": "10_days_ago"}, {"label": "11 days ago", "value": "11_days_ago"}, {"label": "12 days ago", "value": "12_days_ago"}, {"label": "13 days ago", "value": "13_days_ago"}, {"label": "14 days ago", "value": "14_days_ago"}, {"label": "14-21 days ago", "value": "14-21_days"}, {"label": "21-28 days ago", "value": "21-28_days"}, {"label": "28-60 days ago", "value": "28-60_days"}, {"label": "60-90 days ago", "value": "60-90_days"}, {"label": "90 days - 1 year ago", "value": "90_days-1_year"}, {"label": "1+ year ago", "value": "1+_year"}]}, {"key": "all_cold_symptoms_start", "data": {"custom": "values = data.durations_cold"}, "type": "select", "input": true, "label": "When did your cold symptoms start?", "widget": "html5", "dataSrc": "custom", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.cold_symptom_overview && data.cold_symptom_onset_pattern === 'same_time' && !data.none_of_the_above_cold_symptom_overview", "optionsLabelPosition": "right"}, {"key": "heading_cold_rhinorrhea", "html": "<h4 class='mb-n2'>Runny or Stuffy Nose</h4>", "type": "content", "input": false, "label": "R<PERSON>rr<PERSON>", "tableView": false, "refreshOnChange": true, "customConditional": "show = data.cold_symptom_overview && data.cold_symptom_overview.runny_nose === true"}, {"key": "symptom_start_cold_rhinorrhea", "data": {"custom": "values = [{label: `I've always had nasal congestion or a runny nose and this isn't new for me`, value: `always`}].concat(data.durations_cold)"}, "type": "select", "input": true, "label": "When did your nasal congestion or runny nose start?", "widget": "html5", "dataSrc": "custom", "validate": {"required": true}, "tableView": true, "customConditional": "show = !!data.cold_symptom_overview && data.cold_symptom_overview.runny_nose && (_.sum(_.values(data.cold_symptom_overview).map(Number)) < 2 || data.cold_symptom_onset_pattern === 'separate_days')", "optionsLabelPosition": "right"}, {"key": "cold_rhinorrhea_quality", "type": "selectboxes", "input": true, "label": "How would you describe the nasal symptoms? Please select all that apply:", "values": [{"label": "Clear runny discharge", "value": "clear"}, {"label": "Thick or yellow/green discharge", "value": "thick_coloured"}, {"label": "Stuffy nose or nasal congestion", "value": "congestion"}, {"label": "Post-nasal drip (drainage into the throat)", "value": "post_nasal_drip"}, {"label": "Sneezing", "value": "sneezing"}, {"label": "Other", "value": "other"}], "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = data.cold_symptom_overview?.runny_nose === true"}, {"key": "cold_rhinorrhea_new_or_recurrent", "type": "radio", "input": true, "label": "Is this an entirely new issue for you, or something you've experienced on and off before?", "values": [{"label": "This is completely new for me", "value": "new"}, {"label": "I've had this on and off in the past", "value": "recurrent"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.cold_symptom_overview?.runny_nose === true", "optionsLabelPosition": "right"}, {"key": "cold_rhinorrhea_prior_investigation_methods", "type": "selectboxes", "input": true, "label": "What type of evaluation have you had in the past for these nasal symptoms?", "values": [{"label": "COVID or flu test", "value": "viral_test"}, {"label": "Allergy testing", "value": "allergy_test"}, {"label": "Sinus X-ray or CT scan", "value": "imaging"}, {"label": "Seen by an ENT or allergist", "value": "specialist"}, {"label": "Other evaluation", "value": "other"}, {"label": "I don't know / don't remember", "value": "unknown"}], "optionsLabelPosition": "right", "tableView": true, "confirm_label": "You have had the following prior evaluation for nasal symptoms:", "customConditional": "show = data.cold_rhinorrhea_new_or_recurrent === 'recurrent'"}, {"key": "cold_rhinorrhea_prior_investigation_other", "type": "textarea", "input": true, "label": "Please describe the other type of evaluation you had:", "placeholder": "e.g. nasal endoscopy, culture swab, medication trial...", "customConditional": "show = data.cold_rhinorrhea_prior_investigation_methods?.other === true", "tableView": true, "autoExpand": false}, {"key": "cold_rhinorrhea_investigation_results_recall", "type": "radio", "input": true, "label": "Do you remember if anything abnormal was found in previous testing or evaluations?", "values": [{"label": "Yes - something abnormal was found", "value": "abnormal"}, {"label": "No - I was told everything was normal", "value": "normal"}, {"label": "I don't remember / I'm not sure", "value": "not_sure"}], "validate": {"required": true}, "customConditional": "show = data.cold_rhinorrhea_new_or_recurrent === 'recurrent'", "tableView": true, "optionsLabelPosition": "right"}, {"key": "cold_rhinorrhea_abnormal_findings_description", "type": "textarea", "input": true, "label": "Do you remember what the abnormal finding or diagnosis was?", "placeholder": "e.g. sinus infection, allergies, polyps, chronic rhinitis...", "customConditional": "show = data.cold_rhinorrhea_investigation_results_recall === 'abnormal'", "tableView": true, "autoExpand": false}, {"key": "heading_cold_sore_throat", "html": "<h4 class='mb-n2'>Sore Throat or Hoars<PERSON>ss</h4>", "type": "content", "input": false, "label": "Sore Throat Details", "tableView": false, "refreshOnChange": true, "customConditional": "show = data.cold_symptom_overview && data.cold_symptom_overview.sore_throat === true"}, {"key": "symptom_start_cold_sore_throat", "data": {"custom": "values = [{label: `I've always had a sore throat or hoarseness and this isn't new for me`, value: `always`}].concat(data.durations_cold)"}, "type": "select", "input": true, "label": "When did your sore throat or hoarseness begin?", "widget": "html5", "dataSrc": "custom", "validate": {"required": true}, "tableView": true, "customConditional": "show = !!data.cold_symptom_overview && data.cold_symptom_overview.sore_throat && (_.sum(_.values(data.cold_symptom_overview).map(Number)) < 2 || data.cold_symptom_onset_pattern === 'separate_days')", "optionsLabelPosition": "right"}, {"key": "cold_sore_throat_quality", "type": "selectboxes", "input": true, "label": "How would you describe the sore throat or hoarseness? Please select all that apply:", "values": [{"label": "Scratchy or dry throat", "value": "scratchy"}, {"label": "Painful swallowing", "value": "pain_swallowing"}, {"label": "Voice changes or hoarseness", "value": "hoarse"}, {"label": "Visible redness or swelling in throat", "value": "redness"}, {"label": "White patches or spots in throat", "value": "white_patches"}, {"label": "Lump sensation in throat", "value": "globus"}, {"label": "Other", "value": "other"}], "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = data.cold_symptom_overview?.sore_throat === true"}, {"key": "cold_sore_throat_new_or_recurrent", "type": "radio", "input": true, "label": "Is this a new sore throat for you, or something you've had in the past?", "values": [{"label": "This is completely new for me", "value": "new"}, {"label": "I've had this on and off in the past", "value": "recurrent"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.cold_symptom_overview?.sore_throat === true", "optionsLabelPosition": "right"}, {"key": "cold_sore_throat_prior_investigation_methods", "type": "selectboxes", "input": true, "label": "What kind of evaluation have you had before for throat or voice issues?", "values": [{"label": "COVID or strep throat test", "value": "throat_test"}, {"label": "Throat swab or culture", "value": "swab"}, {"label": "Seen by an ENT or voice specialist", "value": "specialist"}, {"label": "Scoping of the throat or vocal cords", "value": "scope"}, {"label": "Other evaluation", "value": "other"}, {"label": "I don't remember / not sure", "value": "unknown"}], "optionsLabelPosition": "right", "tableView": true, "customConditional": "show = data.cold_sore_throat_new_or_recurrent === 'recurrent'", "confirm_label": "You have had the following prior evaluation for sore throat or hoarseness:"}, {"key": "cold_sore_throat_prior_investigation_other", "type": "textarea", "input": true, "label": "Please describe the other evaluation you had:", "placeholder": "e.g. blood test, CT scan, medication trial...", "customConditional": "show = data.cold_sore_throat_prior_investigation_methods?.other === true", "tableView": true, "autoExpand": false}, {"key": "sore_throat_progression", "type": "radio", "input": true, "label": "Since your sore throat started, is it getting better, worse, or staying the same?", "values": [{"label": "Better", "value": "better"}, {"label": "Worse", "value": "worse"}, {"label": "Staying the same", "value": "same"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = data.cold_symptom_overview?.sore_throat === true"}, {"key": "recommend_strep_assessment_now", "type": "content", "input": false, "html": "<div style='border-left: 4px solid #dc3545; background-color: #f8d7da; padding: 10px; margin-bottom: 10px;'><strong>We recommend you get assessed in person today.</strong><br>If your sore throat is worsening, a same-day in-person visit is best to check for a possible strep throat infection.<br><br><strong>Many pharmacies now offer rapid strep testing kits</strong> that can be done without a doctor’s visit.<br>Getting tested early can help decide if antibiotics are needed.</div>", "customConditional": "show = data.sore_throat_progression === 'worse'"}, {"key": "recommend_strep_assessment_now_understanding", "type": "radio", "input": true, "label": "Do you understand this recommendation?", "values": [{"label": "I understand", "value": "understand"}, {"label": "I do not understand", "value": "do_not_understand"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.sore_throat_progression === 'worse'", "optionsLabelPosition": "right"}, {"key": "recommend_strep_if_persistent", "type": "content", "input": false, "html": "<div style='border-left: 4px solid #ffc107; background-color: #fff3cd; padding: 10px; margin-bottom: 10px;'><strong>Consider an in-person assessment if your symptoms are not improving.</strong><br>If your sore throat has stayed the same for a few days and isn't getting better — or if you've already tested negative for strep — it’s still worth seeing someone in person.<br><br>A repeat test or throat swab may be needed to rule out strep, especially if symptoms persist.</div>", "customConditional": "show = data.sore_throat_progression === 'same'"}, {"key": "recommend_strep_if_persistent_understanding", "type": "radio", "input": true, "label": "Do you understand this recommendation?", "values": [{"label": "I understand", "value": "understand"}, {"label": "I do not understand", "value": "do_not_understand"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.sore_throat_progression === 'same'", "optionsLabelPosition": "right"}, {"key": "heading_cold_cough", "html": "<h4 class='mb-n2'>Cough</h4>", "type": "content", "input": false, "label": "Cough Details", "tableView": false, "refreshOnChange": true, "customConditional": "show = data.cold_symptom_overview && data.cold_symptom_overview.cough === true"}, {"key": "symptom_start_cold_cough", "data": {"custom": "values = [{label: `I've always had a cough and this isn't new for me`, value: `always`}].concat(data.durations_cold)"}, "type": "select", "input": true, "label": "When did your cough start?", "widget": "html5", "dataSrc": "custom", "validate": {"required": true}, "tableView": true, "customConditional": "show = !!data.cold_symptom_overview && data.cold_symptom_overview.cough && (_.sum(_.values(data.cold_symptom_overview).map(Number)) < 2 || data.cold_symptom_onset_pattern === 'separate_days')", "optionsLabelPosition": "right"}, {"key": "cold_cough_quality", "type": "selectboxes", "input": true, "label": "How would you describe the cough? Please select all that apply:", "values": [{"label": "Dry cough (no mucus)", "value": "dry"}, {"label": "Wet or productive cough", "value": "wet"}, {"label": "Mucus is yellow or green", "value": "coloured_mucus"}, {"label": "Cough worse at night", "value": "worse_night"}, {"label": "Cough triggered by talking or laughing", "value": "triggered"}, {"label": "Shortness of breath or chest tightness", "value": "sob_or_tightness"}, {"label": "Other", "value": "other"}], "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = data.cold_symptom_overview?.cough === true"}, {"key": "cold_cough_new_or_recurrent", "type": "radio", "input": true, "label": "Is this a new cough, or something you've experienced before?", "values": [{"label": "This is completely new for me", "value": "new"}, {"label": "I've had this on and off in the past", "value": "recurrent"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.cold_symptom_overview?.cough === true", "optionsLabelPosition": "right"}, {"key": "cold_cough_prior_investigation_methods", "type": "selectboxes", "input": true, "label": "What kind of evaluation have you had before for this type of cough?", "values": [{"label": "COVID, flu, or strep test", "value": "viral_testing"}, {"label": "Chest X-ray", "value": "chest_xray"}, {"label": "Spirometry or asthma testing", "value": "asthma_test"}, {"label": "Specialist referral (e.g. respirologist)", "value": "specialist"}, {"label": "Other testing or evaluation", "value": "other"}, {"label": "I don't remember / not sure", "value": "unknown"}], "optionsLabelPosition": "right", "tableView": true, "customConditional": "show = data.cold_cough_new_or_recurrent === 'recurrent'", "confirm_label": "You have had the following prior evaluation for cough:"}, {"key": "cold_cough_prior_investigation_other", "type": "textarea", "input": true, "label": "Please describe the other evaluation you had:", "placeholder": "e.g. CT scan, throat swab, medication trial...", "customConditional": "show = data.cold_cough_prior_investigation_methods?.other === true", "tableView": true, "autoExpand": false}, {"key": "cold_cough_investigation_results_recall", "type": "radio", "input": true, "label": "Do you remember if anything abnormal was found on testing or exams?", "values": [{"label": "Yes - something abnormal was found", "value": "abnormal"}, {"label": "No - I was told everything was normal", "value": "normal"}, {"label": "I don't remember / not sure", "value": "not_sure"}], "validate": {"required": true}, "customConditional": "show = data.cold_cough_new_or_recurrent === 'recurrent'", "tableView": true, "optionsLabelPosition": "right"}, {"key": "cold_cough_abnormal_findings_description", "type": "textarea", "input": true, "label": "Do you remember what diagnosis or abnormal finding was mentioned?", "placeholder": "e.g. bronchitis, asthma, pneumonia, allergies, post-nasal drip...", "customConditional": "show = data.cold_cough_investigation_results_recall === 'abnormal'", "tableView": true, "autoExpand": false}, {"key": "cold_cough_progression", "type": "radio", "input": true, "label": "Since your cough started, has it been getting better, worse, or staying the same?", "values": [{"label": "Getting better", "value": "better"}, {"label": "Getting worse", "value": "worse"}, {"label": "Staying the same", "value": "same"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = data.cold_symptom_overview?.cough === true"}, {"key": "cough_red_flag_symptoms", "type": "selectboxes", "input": true, "label": "Are you experiencing any of the following symptoms along with your cough? Select all that apply:", "values": [{"label": "Coughing up blood", "value": "hemoptysis"}, {"label": "Chest pain when breathing deeply", "value": "pleuritic_pain"}, {"label": "Shortness of breath when sitting or walking slowly", "value": "sob_rest"}, {"label": "Waking up sweating at night (night sweats)", "value": "night_sweats"}], "customConditional": "show = data.cold_symptom_overview?.cough === true", "optionsLabelPosition": "right", "tableView": true}, {"key": "none_of_the_above_cough_red_flag_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "customClass": "mt-n3", "defaultValue": false, "validate": {"custom": "valid = !!data.none_of_the_above_cough_red_flag_symptoms || _.some(_.values(data.cough_red_flag_symptoms));"}, "errors": {"custom": "Please select at least one symptom or check 'None of the above.'"}, "customConditional": "show = data.cold_symptom_overview?.cough === true", "tableView": true}, {"key": "cough_chest_exam_warning", "type": "content", "input": false, "html": "<div style='border-left: 4px solid #dc3545; background-color: #f8d7da; padding: 10px; margin-bottom: 10px;'><strong>Important:</strong><br>Your symptoms may require in-person evaluation. We recommend a same-day visit for a physical chest exam and possibly a chest X-ray.<br><br>If your cough is worsening or includes symptoms like shortness of breath, fever, or chest pain, please visit a walk-in clinic, urgent care centre, or emergency room for further assessment.</div>", "customConditional": "show = data.cold_cough_progression === 'worse' || _.some(_.values(data.cough_red_flag_symptoms).slice(0, -1))"}, {"key": "cough_chest_exam_warning_acknowledge", "type": "radio", "input": true, "label": "Do you understand this recommendation?", "values": [{"label": "I understand", "value": "understand"}, {"label": "I do not understand", "value": "do_not_understand"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.cold_cough_progression === 'worse' || _.some(_.values(data.cough_red_flag_symptoms).slice(0, -1))", "optionsLabelPosition": "right"}, {"key": "cough_duration_warning_4w", "type": "content", "input": false, "html": "<div style='border-left: 4px solid #ffc107; background-color: #fff3cd; padding: 10px; margin-bottom: 10px;'><strong>Persistent cough warning:</strong><br>If your cough has lasted longer than <strong>4 weeks</strong>, we recommend an in-person medical exam and a chest X-ray. This is the standard of care for any cough that persists beyond 4 weeks to rule out infection, inflammation, or other underlying conditions.</div>", "customConditional": "show = data.symptom_start_cold_cough?.value && ['28-60_days', '60-90_days', '90_days-1_year', '1+_year'].includes(data.symptom_start_cold_cough.value)"}, {"key": "cough_duration_warning_4w_acknowledge", "type": "radio", "input": true, "label": "Do you understand this recommendation?", "values": [{"label": "I understand", "value": "understand"}, {"label": "I do not understand", "value": "do_not_understand"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.symptom_start_cold_cough?.value && ['28-60_days', '60-90_days', '90_days-1_year', '1+_year'].includes(data.symptom_start_cold_cough.value)", "optionsLabelPosition": "right"}, {"key": "heading_cold_mouth_sores", "html": "<h4 class='mb-n2'>So<PERSON> in the Mouth</h4>", "type": "content", "input": false, "label": "Mouth Sore Details", "tableView": false, "refreshOnChange": true, "customConditional": "show = data.cold_symptom_overview && data.cold_symptom_overview.mouth_sores === true"}, {"key": "symptom_start_cold_mouth_sores", "data": {"custom": "values = [{label: `I've always had periodic mouth sores and this isn't new for me`, value: `always`}].concat(data.durations_cold)"}, "type": "select", "input": true, "label": "When did you first notice the sore(s) in your mouth?", "widget": "html5", "dataSrc": "custom", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.cold_symptom_overview?.mouth_sores === true && (_.sum(_.values(data.cold_symptom_overview).map(Number)) < 2 || data.cold_symptom_onset_pattern === 'separate_days')", "optionsLabelPosition": "right"}, {"key": "mouth_sores_description", "type": "selectboxes", "input": true, "label": "How would you describe the sore(s)? Please select all that apply:", "values": [{"label": "Small, round or oval with a white or yellow centre", "value": "canker_sore"}, {"label": "Grouped blisters on the lips or edge of mouth", "value": "cold_sore"}, {"label": "Painful red ulcer with no white centre", "value": "red_ulcer"}, {"label": "Bleeding or crusting sore", "value": "crusting"}, {"label": "Located inside the cheeks or under the tongue", "value": "inner_mouth"}, {"label": "Located on lips or gums", "value": "outer_mouth"}, {"label": "Other", "value": "other"}], "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = data.cold_symptom_overview?.mouth_sores === true"}, {"key": "mouth_sores_description_other", "type": "textarea", "input": true, "label": "Please describe the sore(s) in your own words:", "placeholder": "e.g. burning before sore appears, deep ulcer, blister-like...", "customConditional": "show = data.mouth_sores_description?.other === true", "tableView": true, "autoExpand": false}, {"key": "mouth_sores_prior_history", "type": "radio", "input": true, "label": "Have you ever had similar sores before?", "values": [{"label": "Yes - I've had this kind of sore before", "value": "yes"}, {"label": "No - this is new for me", "value": "no"}, {"label": "I'm not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.cold_symptom_overview?.mouth_sores === true", "optionsLabelPosition": "right"}, {"key": "mouth_sores_prior_diagnosis", "type": "selectboxes", "input": true, "label": "Have you been previously diagnosed with any of the following?", "values": [{"label": "Canker sores (aphthous ulcers)", "value": "aphthous"}, {"label": "Cold sores (oral herpes / HSV-1)", "value": "herpes"}, {"label": "Oral thrush (yeast)", "value": "thrush"}, {"label": "Vitamin deficiency (e.g. B12, iron)", "value": "vitamin_deficiency"}, {"label": "Other", "value": "other"}], "customConditional": "show = data.mouth_sores_prior_history === 'yes'", "optionsLabelPosition": "right", "tableView": true}, {"key": "mouth_sores_prior_diagnosis_other", "type": "textarea", "input": true, "label": "Please describe the other condition you were told you had:", "placeholder": "e.g. autoimmune condition, trauma from braces...", "customConditional": "show = data.mouth_sores_prior_diagnosis?.other === true", "tableView": true, "autoExpand": false}, {"key": "photo_upload_header", "html": "</br><h2>Photo Upload</h2><p>If you're able, please upload a clear photo of the sore in your mouth or on your lips. This can help us better understand the cause and suggest the right treatment. Good lighting is helpful. If you're unable to take a photo, you can skip this step.</p>", "type": "content", "input": false, "label": "Content", "customConditional": "show = data.cold_symptom_overview?.mouth_sores === true"}, {"key": "uploadUrl", "url": "/app/q/{qpk}/formio-files/{pk}/", "type": "file", "image": true, "input": true, "label": "Upload: URL", "webcam": true, "capture": "user", "storage": "url", "multiple": true, "fileTypes": [{"label": "", "value": ""}], "tableView": true, "validateWhenHidden": false, "customConditional": "show = data.cold_symptom_overview?.mouth_sores === true"}, {"key": "oral_idrs", "type": "textfield", "input": true, "label": "Oral Intake Denial Reasons:", "hidden": true, "disabled": true, "multiple": true, "adminFlag": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "value = []", "refreshOnChange": true}]}