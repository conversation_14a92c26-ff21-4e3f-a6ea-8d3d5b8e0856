{"components": [{"key": "mobile_table_css", "tag": "style", "type": "htmlelement", "content": "@media (max-width:600px){\n  /* hide any header row inside either grid */\n  .flaretbl table thead,\n  .flaretbl .datagrid-table thead{display:none!important;}\n  /* turn rows into cards */\n  .flaretbl table tbody tr{display:block;margin:0 0 1rem;border:1px solid #e0e0e0;border-radius:4px;}\n  /* make each cell full-width and wrap text */\n  .flaretbl table tbody td{display:block;width:100%;border:none;padding:6px 12px;white-space:normal;}\n}"}, {"key": "psoriasis_header", "html": "<h1 style=\"text-align:center;\">Psoriasis Treatment Options</h1>", "type": "content", "input": false, "label": "Content"}, {"key": "psoriasis_overview", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "html": "<div class=\"alert alert-success\" style=\"padding:1rem;\"><h4>Step&nbsp;1: Treat the Flare First</h4><p>A psoriasis flare is like an <em>immune fire</em>. Put it out fast with an <strong>anti-inflammatory</strong>—usually a short course of topical steroid (or a steroid + vitamin-D blend such as <em>Dovobet® / Enstilar®</em>). On delicate skin you can use a steroid-free option like Protopic or Zoryve.</p><br><h4>Step&nbsp;2: Keep Control</h4><p>Once clear, switch to <em>“weekend therapy”</em> (the same steroid <strong>twice each weekend</strong> on your old trouble-spots) or stay on a gentle daily product such as a vitamin-D cream. Daily moisturising keeps new sparks from lighting.</p><p><em>We’ll start with steroid strengths that work for most people. If you already know you need something stronger, feel free to bump the potency up to match your past experience.</em></p><br><h4>Did&nbsp;You&nbsp;Know? Steroid-Free Choices</h4><ul><li><strong>Calcipotriol 50&nbsp;µg/g (Dovonex®)</strong> has been a vitamin-D favourite since 1993 and is safe for daily use.</li><li><strong>Protopic® 0.1&nbsp;% ointment</strong> (tacrolimus, 2001) calms inflammation <em>without</em> thinning skin—ideal for the face, folds and genitals (off-label for psoriasis).</li><li><strong>Zoryve® 0.3&nbsp;% cream</strong> (roflumilast, launched 2022-23) is once-daily, and is a highly effective new approach to treating psoriasis (<a href=\"https://docs.teletest.ca/psoriasis-treatment-guide#zoryve-r-roflumilast-0.3\" target=\"_blank\">read&nbsp;more</a>).</li></ul><p>Because these aren’t steroids, they’re gentler on thin skin—though they remain pricey (≈ $250-$350 per 60-g tube).</p></div>"}, {"key": "treatment_intro", "html": "<br><h4>Tell us a few details to see your best treatment options:</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "drug_coverage", "type": "radio", "input": true, "label": "Do you have a prescription-drug plan?", "inline": false, "values": [{"label": "OHIP+ (age ≤ 24)", "value": "ohip_plus"}, {"label": "Private drug plan", "value": "private_plan"}, {"label": "ODB - Ontario Drug Benefit (age ≥ 65)", "value": "odb"}, {"label": "No drug plan", "value": "none"}], "widget": "html5", "validate": {"required": true}, "tableView": true}, {"key": "steroid_free_interest", "type": "radio", "label": "Are you interested in trying a steroid-free psoriasis cream?", "values": [{"label": "Yes, show me the options", "value": "yes"}, {"label": "No, I prefer steroid creams only", "value": "no"}], "widget": "html5", "tableView": true, "validate": {"required": true}}, {"key": "steroid_free_choices", "type": "selectboxes", "label": "Which steroid-free treatment(s) would you like to consider?", "values": [{"label": "Protopic 0.1 % ointment", "value": "protopic"}, {"label": "Zoryve 0.3 % cream", "value": "zoryve"}], "widget": "html5", "tableView": true, "customConditional": "show = data.steroid_free_interest === 'yes';"}, {"key": "heading_body_areas", "html": "<br><h4>Which area of your body is affected?</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "body_areas", "type": "selectboxes", "label": "Which body area(s) are you treating today? (Select all that apply)", "values": [{"label": "Face / Groin / Genitals / Under-breast", "value": "face_groin"}, {"label": "Eyelids / Around eyes", "value": "eyelids"}, {"label": "Neck / Skin-folds", "value": "neck_folds"}, {"label": "Chest / Back / Arms / Legs / Hands (soft part)", "value": "trunk_limbs"}, {"label": "Palms & Soles (thick skin)", "value": "palms_soles"}, {"label": "Scalp (hair-bearing)", "value": "scalp"}, {"label": "Ear canal", "value": "ear_canal"}, {"label": "Nails", "value": "nails"}], "widget": "html5", "tableView": true, "validate": {"required": true}}, {"key": "rec_hint", "html": "<p><em>Tip:&nbsp;Options shown in <span style=\"color:#28a745;font-weight:bold;\">bold&nbsp;green</span> are our recommended choice, but you're welcome to pick <strong>any</strong> treatment you prefer.</em></p>", "type": "content", "input": false, "label": "Content"}, {"key": "heading_face_groin", "html": "<br><h4><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Under the Breast</h4>", "type": "content", "input": false, "label": "Content", "customConditional": "show = data.body_areas && data.body_areas.face_groin === true;"}, {"key": "face_groin_plan_yes", "type": "radio", "label": "When this area is inflamed, which medication will you use?", "values": [{"label": "<span style=\"color:#28a745;font-weight:bold;\">Hydrocortisone + Protopic 0.1% (Recommended)</span>", "value": "hc_protopic"}, {"label": "Hydrocortisone 1 % ointment", "value": "hc1"}, {"label": "Zoryve 0.3 % cream (daily)", "value": "zoryve_daily"}, {"label": "Protopic 0.1 % ointment", "value": "protopic"}, {"label": "Other (type below)", "value": "other"}], "widget": "html5", "tableView": true, "validate": {"required": true}, "customConditional": "show = data.body_areas && data.body_areas.face_groin === true && data.steroid_free_interest === 'yes';"}, {"key": "face_groin_plan_no", "type": "radio", "label": "When this area is inflamed, which medication will you use?", "values": [{"label": "<span style=\"color:#28a745;font-weight:bold;\">Hydrocortisone 1 % ointment <b>(Recommended)</b></span>", "value": "hc1"}, {"label": "Zoryve 0.3 % cream (daily)", "value": "zoryve_daily"}, {"label": "Other (type below)", "value": "other"}], "widget": "html5", "tableView": true, "validate": {"required": true}, "customConditional": "show = data.body_areas && data.body_areas.face_groin === true && data.steroid_free_interest === 'no';"}, {"key": "face_groin_plan_other", "rows": 3, "type": "textarea", "tableView": true, "label": "Describe your flare-time treatment:", "customConditional": "show = (data.face_groin_plan_yes === 'other' || data.face_groin_plan_no === 'other');"}, {"key": "face_groin_maint_yes", "tableView": true, "type": "radio", "label": "Once the skin is calm, which product will you use to keep it clear?", "values": [{"label": "<span style=\"color:#28a745;font-weight:bold;\">Protopic 0.1 % ointment (weekends only)</span>", "value": "protopic_weekend"}, {"label": "Zoryve 0.3 % cream (daily)", "value": "zoryve_daily"}, {"label": "None - I don't need ongoing treatment", "value": "none"}, {"label": "Other (type below)", "value": "other"}], "widget": "html5", "validate": {"required": true}, "customConditional": "show = data.body_areas && data.body_areas.face_groin === true && data.steroid_free_interest === 'yes';"}, {"key": "face_groin_maint_no", "tableView": true, "type": "radio", "label": "Once the skin is calm, which product will you use to keep it clear?", "values": [{"label": "<span style=\"color:#28a745;font-weight:bold;\">Zoryve® 0.3&nbsp;% cream (daily) <b>(Recommended)</b></span>", "value": "zoryve_daily"}, {"label": "None - I don't need ongoing treatment", "value": "none"}, {"label": "Other (type below)", "value": "other"}], "widget": "html5", "validate": {"required": true}, "customConditional": "show = data.body_areas && data.body_areas.face_groin === true && data.steroid_free_interest === 'no';"}, {"key": "face_groin_maint_other", "tableView": true, "rows": 3, "type": "textarea", "label": "Describe your maintenance option:", "customConditional": "show = (data.face_groin_maint_yes === 'other' || data.face_groin_maint_no === 'other');"}, {"key": "heading_eyelids", "html": "<br><h4>Eyelids &amp; Around the Eyes</h4>", "type": "content", "input": false, "label": "Content", "customConditional": "show = data.body_areas && data.body_areas.eyelids === true;"}, {"key": "eyelids_plan_yes", "tableView": true, "type": "radio", "label": "When your eyelids are inflamed, which medication will you use?", "values": [{"label": "<span style=\"color:#28a745;font-weight:bold;\">Protopic 0.1 % ointment (Recommended)</span>", "value": "protopic"}, {"label": "Zoryve 0.3 % cream (daily)", "value": "zoryve_daily"}, {"label": "Hydrocortisone 1 % ointment", "value": "hc1"}, {"label": "Other (type below)", "value": "other"}], "widget": "html5", "validate": {"required": true}, "customConditional": "show = data.body_areas && data.body_areas.eyelids === true && data.steroid_free_interest === 'yes';"}, {"key": "eyelids_plan_no", "tableView": true, "type": "radio", "label": "When your eyelids are inflamed, which medication will you use?", "values": [{"label": "<span style=\"color:#28a745;font-weight:bold;\">Protopic 0.1 % ointment (Recommended)</span>", "value": "protopic"}, {"label": "Zoryve 0.3 % cream (daily)", "value": "zoryve_daily"}, {"label": "Other (type below)", "value": "other"}], "widget": "html5", "validate": {"required": true}, "customConditional": "show = data.body_areas && data.body_areas.eyelids === true && data.steroid_free_interest === 'no';"}, {"key": "eyelids_plan_other", "tableView": true, "rows": 3, "type": "textarea", "label": "Describe your flare-time treatment:", "customConditional": "show = (data.eyelids_plan_yes === 'other' || data.eyelids_plan_no === 'other');"}, {"key": "eyelids_maint_yes", "tableView": true, "type": "radio", "label": "Once the skin is calm, which product will you use to keep it clear?", "values": [{"label": "<span style=\"color:#28a745;font-weight:bold;\">Protopic 0.1 % ointment (weekends only)</span>", "value": "protopic_weekend"}, {"label": "Zoryve 0.3 % cream (daily)", "value": "zoryve_daily"}, {"label": "None - I don't need ongoing treatment", "value": "none"}, {"label": "Other (type below)", "value": "other"}], "widget": "html5", "validate": {"required": true}, "customConditional": "show = data.body_areas && data.body_areas.eyelids === true && data.steroid_free_interest === 'yes';"}, {"key": "eyelids_maint_no", "tableView": true, "type": "radio", "label": "Once the skin is calm, which product will you use to keep it clear?", "values": [{"label": "<span style=\"color:#28a745;font-weight:bold;\">Zoryve® 0.3&nbsp;% cream (daily) <b>(Recommended)</b></span>", "value": "zoryve_daily"}, {"label": "None - I don't need ongoing treatment", "value": "none"}, {"label": "Other (type below)", "value": "other"}], "widget": "html5", "validate": {"required": true}, "customConditional": "show = data.body_areas && data.body_areas.eyelids === true && data.steroid_free_interest === 'no';"}, {"key": "eyelids_maint_other", "tableView": true, "rows": 3, "type": "textarea", "label": "Describe your maintenance option:", "customConditional": "show = (data.eyelids_maint_yes === 'other' || data.eyelids_maint_no === 'other');"}, {"key": "heading_neck_folds", "html": "<br><h4>Skin Folds - Neck &amp; Body Folds</h4>", "type": "content", "input": false, "label": "Content", "customConditional": "show = data.body_areas && data.body_areas.neck_folds === true;"}, {"key": "neck_folds_plan_yes", "type": "radio", "tableView": true, "label": "When this area flares, which medication will you use?", "values": [{"label": "<span style=\"color:#28a745;font-weight:bold;\">Betamethasone Valerate 0.05% (Betaderm) (Recommended)</span>", "value": "betaderm005"}, {"label": "Protopic 0.1 % ointment", "value": "protopic"}, {"label": "Zoryve 0.3 % cream (daily)", "value": "zoryve_daily"}, {"label": "Other (type below)", "value": "other"}], "widget": "html5", "validate": {"required": true}, "customConditional": "show = data.body_areas && data.body_areas.neck_folds === true && data.steroid_free_interest === 'yes';"}, {"key": "neck_folds_plan_no", "type": "radio", "tableView": true, "label": "When this area flares, which medication will you use?", "values": [{"label": "<span style=\"color:#28a745;font-weight:bold;\">Betamethasone Valerate 0.05% (Betaderm) (Recommended)</span>", "value": "betaderm005"}, {"label": "Betamethasone valerate 0.05 % (Betaderm®) + Calcitriol 3 µg/g (Silkis® ointment)", "value": "betaderm005_calcitriol"}, {"label": "Zoryve 0.3 % cream (daily)", "value": "zoryve_daily"}, {"label": "Other (type below)", "value": "other"}], "widget": "html5", "validate": {"required": true}, "customConditional": "show = data.body_areas && data.body_areas.neck_folds === true && data.steroid_free_interest === 'no';"}, {"key": "neck_folds_plan_other", "tableView": true, "rows": 3, "type": "textarea", "label": "Describe your flare-time treatment:", "customConditional": "show = (data.neck_folds_plan_yes === 'other' || data.neck_folds_plan_no === 'other');"}, {"key": "neck_folds_maint_yes", "type": "radio", "tableView": true, "label": "After it's calm, which product will you use to keep this area clear?", "values": [{"label": "<span style=\"color:#28a745;font-weight:bold;\">Calcitriol 3 µg/g ointment (Silkis®) - bedtime <em>Mon → Fri</em></span>", "value": "silkis"}, {"label": "Tacrolimus 0.1 % ointment (Protopic®) - evening <em>Tue &amp; Fri</em>", "value": "tacrolimus"}, {"label": "Betamethasone valerate 0.05 % cream (Betaderm®) - morning <em>Sat &amp; Sun only</em>", "value": "betaderm005"}, {"label": "Roflumilast 0.3 % cream (Zoryve®) - evening <em>Sat &amp; Sun</em>", "value": "zoryve"}, {"label": "None - I don't need maintenance", "value": "none"}, {"label": "Other (type below)", "value": "other"}], "widget": "html5", "validate": {"required": true}, "customConditional": "show = data.body_areas && data.body_areas.neck_folds === true && data.steroid_free_interest === 'yes';"}, {"key": "neck_folds_maint_no", "type": "radio", "tableView": true, "label": "After it's calm, which product will you use to keep this area clear?", "values": [{"label": "<span style=\"color:#28a745;font-weight:bold;\">Betamethasone valerate 0.05 % cream (Betaderm®) - Sat &amp; Sun mornings</span>", "value": "betaderm005"}, {"label": "Roflumilast 0.3 % cream (Zoryve®) - Sat &amp; Sun evenings", "value": "zoryve"}, {"label": "None - I don't need maintenance", "value": "none"}, {"label": "Other (type below)", "value": "other"}], "widget": "html5", "validate": {"required": true}, "customConditional": "show = data.body_areas && data.body_areas.neck_folds === true && data.steroid_free_interest === 'no';"}, {"key": "neck_folds_maint_other", "tableView": true, "rows": 3, "type": "textarea", "label": "Describe your maintenance option:", "customConditional": "show = (data.neck_folds_maint_yes === 'other' || data.neck_folds_maint_no === 'other');"}, {"key": "heading_trunk_limbs", "html": "<br><h4>Trunk, Arms &amp; Legs</h4>", "type": "content", "input": false, "label": "Content", "customConditional": "show = data.body_areas && data.body_areas.trunk_limbs === true;"}, {"key": "trunk_limbs_plan_yes", "type": "radio", "tableView": true, "label": "When this area flares, which medication will you use?", "values": [{"label": "<span style=\"color:#28a745;font-weight:bold;\">Calcipotriol + Betamethasone 0.064 % (Enstilar® spray foam) - nightly <b>(Recommended)</b></span>", "value": "enst<PERSON><PERSON>"}, {"label": "Mometasone furoate 0.1 % (Elocom®) - once daily", "value": "elocom010"}, {"label": "Betamethasone dipropionate 0.05 % (Diprosone®)", "value": "diprosone005"}, {"label": "Calcipotriol 50 µg/g + Betamethasone 0.064 % (Dovobet® gel)", "value": "dovobet"}, {"label": "Calcipotriol 50 µg/g (Dovonex®)", "value": "dovonex"}, {"label": "Calcitriol 3 µg/g (Silkis®)", "value": "silkis"}, {"label": "Roflumilast 0.3 % cream (Zoryve®)", "value": "zoryve"}, {"label": "Other (type below)", "value": "other"}], "widget": "html5", "validate": {"required": true}, "customConditional": "show = data.body_areas && data.body_areas.trunk_limbs === true && data.steroid_free_interest === 'yes';"}, {"key": "trunk_limbs_plan_no", "type": "radio", "tableView": true, "label": "When this area flares, which medication will you use?", "values": [{"label": "<span style=\"color:#28a745;font-weight:bold;\">Calcipotriol + Betamethasone 0.064 % (Enstilar® spray foam) - nightly <b>(Recommended)</b></span>", "value": "enst<PERSON><PERSON>"}, {"label": "Mometasone furoate 0.1 % (Elocom®)", "value": "elocom010"}, {"label": "Betamethasone dipropionate 0.05 % (Diprosone®)", "value": "diprosone005"}, {"label": "Calcipotriol 50 µg/g + Betamethasone 0.064 % (Dovobet® gel)", "value": "dovobet"}, {"label": "Calcipotriol 50 µg/g (Dovonex®)", "value": "dovonex"}, {"label": "Calcitriol 3 µg/g (Silkis®)", "value": "silkis"}, {"label": "Other (type below)", "value": "other"}], "widget": "html5", "validate": {"required": true}, "customConditional": "show = data.body_areas && data.body_areas.trunk_limbs === true && data.steroid_free_interest === 'no';"}, {"key": "trunk_limbs_plan_other", "type": "textarea", "rows": 3, "tableView": true, "label": "Describe your flare-time treatment:", "customConditional": "show = (data.trunk_limbs_plan_yes === 'other' || data.trunk_limbs_plan_no === 'other');"}, {"key": "trunk_limbs_maint_yes", "type": "radio", "tableView": true, "label": "After the skin is calm, which product will you use to keep this area clear?", "values": [{"label": "<span style=\"color:#28a745;font-weight:bold;\">Protopic 0.1 % ointment (weekends only)</span>", "value": "protopic_weekend"}, {"label": "Calcipotriol 50 µg/g (Dovonex®) - thin layer PM <em>Mon → Fri</em>", "value": "dovonex"}, {"label": "Calcipotriol 50 µg/g + Betamethasone 0.064 % (Dovobet® gel)", "value": "dovobet"}, {"label": "Calcipotriol + Betamethasone 0.064 % (Enstilar® spray foam)", "value": "enst<PERSON><PERSON>"}, {"label": "Betamethasone dipropionate 0.05 % (Diprosone®) - morning <em>Sun (spot rescue)</em>", "value": "diprosone005"}, {"label": "Roflumilast 0.3 % cream (Zoryve®) - evening <em>Tue &amp; Fri</em>", "value": "zoryve"}, {"label": "None - I don't need maintenance", "value": "none"}, {"label": "Other (type below)", "value": "other"}], "widget": "html5", "validate": {"required": true}, "customConditional": "show = data.body_areas && data.body_areas.trunk_limbs === true && data.steroid_free_interest === 'yes';"}, {"key": "trunk_limbs_maint_no", "type": "radio", "tableView": true, "label": "After the skin is calm, which product will you use to keep this area clear?", "values": [{"label": "<span style=\"color:#28a745;font-weight:bold;\">Enstilar® foam - bedtime <em>Wed &amp; Sat</em></span>", "value": "enst<PERSON><PERSON>"}, {"label": "Calcipotriol 50 µg/g (Dovonex®) - PM <em>Mon → Fri</em>", "value": "dovonex"}, {"label": "Calcipotriol 50 µg/g + Betamethasone 0.064 % (Dovobet® gel) (weekends only)", "value": "dovobet"}, {"label": "None - I don't need maintenance", "value": "none"}, {"label": "Other (type below)", "value": "other"}], "widget": "html5", "validate": {"required": true}, "customConditional": "show = data.body_areas && data.body_areas.trunk_limbs === true && data.steroid_free_interest === 'no';"}, {"key": "trunk_limbs_maint_other", "type": "textarea", "rows": 3, "tableView": true, "label": "Describe your maintenance option:", "customConditional": "show = (data.trunk_limbs_maint_yes === 'other' || data.trunk_limbs_maint_no === 'other');"}, {"key": "heading_palms_soles", "html": "<br><h4>Palms &amp; Soles - Thick Skin</h4>", "type": "content", "input": false, "label": "Content", "customConditional": "show = data.body_areas && data.body_areas.palms_soles === true;"}, {"key": "palms_soles_plan_yes", "type": "radio", "label": "When this area flares, which medication will you use?", "tableView": true, "values": [{"label": "<span style=\"color:#28a745;font-weight:bold;\">Clobetasol propionate 0.05 % ointment (Dermovate®) - once daily + cotton gloves/socks</span>", "value": "clobetasol005_ointment"}, {"label": "Clobetasol 0.05 % → Tacrolimus 0.1 % ointment (Protopic®) - steroid × 14 d, then tacrolimus twice daily", "value": "clob_then_tac"}, {"label": "Calcipotriol 50 µg/g ointment (Dovonex®) - thin layer twice daily", "value": "dovonex"}, {"label": "Other (type below)", "value": "other"}], "widget": "html5", "validate": {"required": true}, "customConditional": "show = data.body_areas && data.body_areas.palms_soles === true && data.steroid_free_interest === 'yes';"}, {"key": "palms_soles_plan_no", "type": "radio", "tableView": true, "label": "When this area flares, which medication will you use?", "values": [{"label": "<span style=\"color:#28a745;font-weight:bold;\">Clobetasol propionate 0.05 % ointment (Dermovate®) - once daily + cotton gloves/socks</span>", "value": "clobetasol005_ointment"}, {"label": "Calcipotriol 50 µg/g ointment (Dovonex®)", "value": "dovonex"}, {"label": "Other (type below)", "value": "other"}], "widget": "html5", "validate": {"required": true}, "customConditional": "show = data.body_areas && data.body_areas.palms_soles === true && data.steroid_free_interest === 'no';"}, {"key": "palms_soles_plan_other", "type": "textarea", "rows": 3, "tableView": true, "label": "Describe your flare-time treatment:", "customConditional": "show = (data.palms_soles_plan_yes === 'other' || data.palms_soles_plan_no === 'other');"}, {"key": "palms_soles_maint_yes", "type": "radio", "tableView": true, "label": "After the skin is calm, which product will you use to keep this area clear?", "values": [{"label": "<span style=\"color:#28a745;font-weight:bold;\">Tacrolimus 0.1 % ointment (Protopic®) + cotton gloves/socks - night <em><PERSON>, <PERSON><PERSON>, <PERSON>i</em></span>", "value": "tacrolimus"}, {"label": "Clobetasol propionate 0.05 % ointment (Dermovate®) - night <em>Sat &amp; Sun</em>", "value": "clobetasol005_ointment"}, {"label": "Calcipotriol 50 µg/g ointment (Dovonex®) - after morning wash <em>Mon → Fri</em>", "value": "dovonex"}, {"label": "None - I don't need maintenance", "value": "none"}, {"label": "Other (type below)", "value": "other"}], "widget": "html5", "validate": {"required": true}, "customConditional": "show = data.body_areas && data.body_areas.palms_soles === true && data.steroid_free_interest === 'yes';"}, {"key": "palms_soles_maint_no", "type": "radio", "tableView": true, "label": "After the skin is calm, which product will you use to keep this area clear?", "values": [{"label": "<span style=\"color:#28a745;font-weight:bold;\">Calcipotriol 50 µg/g ointment (Dovonex®) - AM <em>Mon → Fri</em> <b>(Recommended)</b></span>", "value": "dovonex"}, {"label": "Clobetasol propionate 0.05 % ointment (Dermovate®) - night <em>Sat &amp; Sun</em>", "value": "clobetasol005_ointment"}, {"label": "Barrier cream only (daily, over-the-counter)", "value": "barrier"}, {"label": "None - I don't need maintenance", "value": "none"}, {"label": "Other (type below)", "value": "other"}], "widget": "html5", "validate": {"required": true}, "customConditional": "show = data.body_areas && data.body_areas.palms_soles === true && data.steroid_free_interest === 'no';"}, {"key": "palms_soles_maint_other", "type": "textarea", "rows": 3, "tableView": true, "label": "Describe your maintenance option:", "customConditional": "show = (data.palms_soles_maint_yes === 'other' || data.palms_soles_maint_no === 'other');"}, {"key": "heading_scalp", "type": "content", "input": false, "label": "Content", "html": "<br><h4><PERSON><PERSON><PERSON> (Hair-Bearing Skin)</h4><p>Choose a product <strong>during a flare</strong>, then pick what you'll use <strong>to stay clear</strong>.</p>", "customConditional": "show = data.body_areas && data.body_areas.scalp === true;"}, {"key": "scalp_plan_yes", "type": "radio", "tableView": true, "label": "When your scalp is inflamed, which medication will you use?", "values": [{"label": "<span style=\"color:#28a745;font-weight:bold;\">Enstilar® foam (Calcipotriol + Betamethasone) - nightly <b>(Recommended)</b></span>", "value": "enst<PERSON><PERSON>"}, {"label": "Mometasone furoate 0.1 % lotion (Elocom®) - once daily", "value": "elocom010"}, {"label": "Dovobet® gel - nightly", "value": "dovobet"}, {"label": "Calcipotriol 50 µg/g (Dovonex® gel) - twice daily", "value": "dovonex"}, {"label": "Roflumilast 0.3 % foam (Zoryve®) - once daily", "value": "zoryve_foam"}, {"label": "Other (type below)", "value": "other"}], "widget": "html5", "validate": {"required": true}, "customConditional": "show = data.body_areas && data.body_areas.scalp === true && data.steroid_free_interest === 'yes';"}, {"key": "scalp_plan_no", "type": "radio", "tableView": true, "label": "When your scalp is inflamed, which medication will you use?", "values": [{"label": "<span style=\"color:#28a745;font-weight:bold;\">Enstilar® foam (Calcipotriol + Betamethasone) - nightly <b>(Recommended)</b></span>", "value": "enst<PERSON><PERSON>"}, {"label": "Mometasone furoate 0.1 % lotion (Elocom®)", "value": "elocom010"}, {"label": "Dovobet® gel", "value": "dovobet"}, {"label": "Calcipotriol 50 µg/g (Dovonex® gel)", "value": "dovonex"}, {"label": "Other (type below)", "value": "other"}], "widget": "html5", "validate": {"required": true}, "customConditional": "show = data.body_areas && data.body_areas.scalp === true && data.steroid_free_interest === 'no';"}, {"key": "scalp_plan_other", "type": "textarea", "rows": 3, "tableView": true, "label": "Describe your flare-time treatment:", "customConditional": "show = (data.scalp_plan_yes === 'other' || data.scalp_plan_no === 'other');"}, {"key": "scalp_maint_yes", "type": "radio", "tableView": true, "label": "After the flare settles, which product will you use to keep your scalp clear?", "values": [{"label": "<span style=\"color:#28a745;font-weight:bold;\">Enstilar® foam - bedtime <em>Wed &amp; Sat</em></span>", "value": "enst<PERSON><PERSON>"}, {"label": "Calcipotriol 50 µg/g (Dovonex® gel) - PM <em><PERSON>, <PERSON><PERSON>, <PERSON>hu</em>", "value": "dovonex"}, {"label": "Mometasone 0.1 % lotion (Elocom®) - Sun morning <em>rescue only</em>", "value": "elocom010"}, {"label": "Zoryve® foam 0.3 % - bedtime <em>Wed &amp; Sat</em>", "value": "zoryve_foam"}, {"label": "None - I don't need maintenance", "value": "none"}, {"label": "Other (type below)", "value": "other"}], "widget": "html5", "validate": {"required": true}, "customConditional": "show = data.body_areas && data.body_areas.scalp === true && data.steroid_free_interest === 'yes';"}, {"key": "scalp_maint_no", "type": "radio", "tableView": true, "label": "After the flare settles, which product will you use to keep your scalp clear?", "values": [{"label": "<span style=\"color:#28a745;font-weight:bold;\">Enstilar® foam - bedtime <em>Wed &amp; Sat</em></span>", "value": "enst<PERSON><PERSON>"}, {"label": "Roflumilast 0.3 % foam (Zoryve®) - bedtime <em>Wed &amp; Sat</em>", "value": "zoryve_foam"}, {"label": "Calcipotriol 50 µg/g (Dovonex® gel) - PM <em><PERSON>, <PERSON><PERSON>, <PERSON>hu</em>", "value": "dovonex"}, {"label": "Mometasone 0.1 % lotion (Elocom®) - Sun AM rescue", "value": "elocom010"}, {"label": "None - I don't need maintenance", "value": "none"}, {"label": "Other (type below)", "value": "other"}], "widget": "html5", "validate": {"required": true}, "customConditional": "show = data.body_areas && data.body_areas.scalp === true && data.steroid_free_interest === 'no';"}, {"key": "scalp_maint_other", "type": "textarea", "rows": 3, "tableView": true, "label": "Describe your maintenance option:", "customConditional": "show = (data.scalp_maint_yes === 'other' || data.scalp_maint_no === 'other');"}, {"key": "heading_ear_canal", "type": "content", "input": false, "label": "Content", "html": "<br><h4>Ears - Outer Canal ± Lobe</h4>", "customConditional": "show = data.body_areas && data.body_areas.ear_canal === true;"}, {"key": "ear_canal_plan", "type": "radio", "tableView": true, "label": "During a flare, which medication will you use?", "values": [{"label": "<span style=\"color:#28a745;font-weight:bold;\">Fluocinolone 0.01 % oil drops (DermOtic®) - 5 drops twice daily × 7-14 d</span>", "value": "dermotic"}, {"label": "Hydrocortisone 1 % lotion ➜ Tacrolimus 0.1 % ointment (Protopic®) - steroid × 14 d, then tacrolimus twice daily", "value": "hc_then_tac"}, {"label": "Other (type below)", "value": "other"}], "widget": "html5", "validate": {"required": true}, "customConditional": "show = data.body_areas && data.body_areas.ear_canal === true;"}, {"key": "ear_canal_plan_other", "type": "textarea", "rows": 3, "tableView": true, "label": "Describe your flare-time treatment:", "customConditional": "show = data.ear_canal_plan === 'other';"}, {"key": "ear_canal_maint_yes", "type": "radio", "tableView": true, "label": "Once calm, which product will you use for maintenance?", "values": [{"label": "<span style=\"color:#28a745;font-weight:bold;\">Tacrolimus 0.1 % ointment (Protopic®) - coat outer ear skin Wed &amp; Sat nights <b>(Recommended)</b></span>", "value": "tacrolimus"}, {"label": "Fluocinolone 0.01 % drops (DermOtic®) - 2 drops Sunday", "value": "dermotic"}, {"label": "None - I don't need maintenance", "value": "none"}, {"label": "Other (type below)", "value": "other"}], "widget": "html5", "validate": {"required": true}, "customConditional": "show = data.body_areas && data.body_areas.ear_canal === true && data.steroid_free_interest === 'yes';"}, {"key": "ear_canal_maint_no", "type": "radio", "tableView": true, "label": "Once calm, which product will you use for maintenance?", "values": [{"label": "<span style=\"color:#28a745;font-weight:bold;\">Fluocinolone&nbsp;0.01&nbsp;% drops (DermOtic®) - 2&nbsp;drops Sunday</span>", "value": "dermotic"}, {"label": "None - I don't need maintenance", "value": "none"}, {"label": "Other (type below)", "value": "other"}], "widget": "html5", "validate": {"required": true}, "customConditional": "show = data.body_areas && data.body_areas.ear_canal === true && data.steroid_free_interest === 'no';"}, {"key": "ear_canal_maint_other", "type": "textarea", "rows": 3, "tableView": true, "label": "Describe your maintenance option:", "customConditional": "show = (data.ear_canal_maint_yes === 'other' || data.ear_canal_maint_no === 'other');"}, {"key": "heading_nails", "type": "content", "input": false, "label": "Content", "html": "<br><h4>Nails (Finger&nbsp;&amp;&nbsp;Toe)</h4>", "customConditional": "show = data.body_areas && data.body_areas.nails === true;"}, {"key": "nails_plan_yes", "type": "radio", "tableView": true, "label": "When your nails flare, which medication will you use?", "values": [{"label": "<span style=\"color:#28a745;font-weight:bold;\">Tazarotene&nbsp;0.045&nbsp;% lotion (Arazlo™) - nightly, occlude</span>", "value": "tazarotene045"}, {"label": "Calcipotriol&nbsp;50 µg/g ointment (Dovonex®) - nightly, occlude", "value": "dovonex"}, {"label": "Clobetasol propionate 0.05 % solution (Dermovate®) - nightly drop under nail fold, occlude", "value": "clobetasol005_solution"}, {"label": "Tacrolimus 0.1 % ointment (Protopic®) - twice daily to nail &amp; base", "value": "tacrolimus"}, {"label": "Other (type below)", "value": "other"}], "widget": "html5", "validate": {"required": true}, "customConditional": "show = data.body_areas && data.body_areas.nails === true && data.steroid_free_interest === 'yes';"}, {"key": "nails_plan_no", "type": "radio", "tableView": true, "label": "When your nails flare, which medication will you use?", "values": [{"label": "<span style=\"color:#28a745;font-weight:bold;\">Tazarotene&nbsp;0.045&nbsp;% lotion (Arazlo™) - nightly, occlude</span>", "value": "tazarotene045"}, {"label": "Calcipotriol&nbsp;50 µg/g ointment (Dovonex®) - nightly, occlude", "value": "dovonex"}, {"label": "Clobetasol propionate 0.05 % solution (Dermovate®) - nightly drop under nail fold, occlude", "value": "clobetasol005_solution"}, {"label": "Other (type below)", "value": "other"}], "widget": "html5", "validate": {"required": true}, "customConditional": "show = data.body_areas && data.body_areas.nails === true && data.steroid_free_interest === 'no';"}, {"key": "nails_plan_other", "type": "textarea", "rows": 3, "tableView": true, "label": "Describe your flare-time treatment:", "customConditional": "show = (data.nails_plan_yes === 'other' || data.nails_plan_no === 'other');"}, {"key": "nails_maint_yes", "type": "radio", "tableView": true, "label": "After it settles, which product will you use long-term?", "values": [{"label": "<span style=\"color:#28a745;font-weight:bold;\">Tacrolimus 0.1 % ointment (Protopic®) - Tue&nbsp;&amp;&nbsp;Fri evenings</span>", "value": "tacrolimus"}, {"label": "Tazarotene&nbsp;0.045 % lotion (Arazlo™) - Mon, Wed, Fri nights", "value": "tazarotene045"}, {"label": "Calcipotriol&nbsp;50 µg/g ointment (Dovonex®) - Tue, Thu, Sat nights", "value": "dovonex"}, {"label": "None - I don’t need maintenance", "value": "none"}, {"label": "Other (type below)", "value": "other"}], "widget": "html5", "validate": {"required": true}, "customConditional": "show = data.body_areas && data.body_areas.nails === true && data.steroid_free_interest === 'yes';"}, {"key": "nails_maint_no", "type": "radio", "tableView": true, "label": "After it settles, which product will you use long-term?", "values": [{"label": "<span style=\"color:#28a745;font-weight:bold;\">Tazarotene&nbsp;0.045 % lotion (Arazlo™) - Mon, Wed, Fri nights</span>", "value": "tazarotene045"}, {"label": "Calcipotriol&nbsp;50 µg/g ointment (Dovonex®) - Tue, Thu, Sat nights", "value": "dovonex"}, {"label": "Clobetasol propionate 0.05 % solution - Sun AM rescue only", "value": "clobetasol005_solution"}, {"label": "None - I don’t need maintenance", "value": "none"}, {"label": "Other (type below)", "value": "other"}], "widget": "html5", "validate": {"required": true}, "customConditional": "show = data.body_areas && data.body_areas.nails === true && data.steroid_free_interest === 'no';"}, {"key": "nails_maint_other", "type": "textarea", "rows": 3, "tableView": true, "label": "Describe your maintenance option:", "customConditional": "show = (data.nails_maint_yes === 'other' || data.nails_maint_no === 'other');"}, {"key": "heading_dermotic", "type": "content", "input": false, "label": "Content", "html": "<br><h4>DermOtic® Safety Check</h4>", "customConditional": "show = (data.ear_canal_plan === 'dermotic') || (data.ear_canal_maint_yes === 'dermotic') || (data.ear_canal_maint_no === 'dermotic');"}, {"key": "dermotic_peanut_allergy", "type": "radio", "label": "DermOtic® contains purified peanut oil. Do you have a peanut allergy?", "adminFlag": true, "values": [{"label": "Yes", "value": true}, {"label": "No", "value": false}], "tableView": true, "validate": {"required": true}, "customConditional": "show = (data.ear_canal_plan === 'dermotic') || (data.ear_canal_maint_yes === 'dermotic') || (data.ear_canal_maint_no === 'dermotic');"}, {"key": "dermotic_allergy_warning", "type": "content", "input": false, "label": "Content", "html": "<div class=\"alert alert-danger\"><strong>Warning:</strong> DermOtic® is unsafe if you have a peanut allergy. Please go back and choose a different ear-canal treatment.</div>", "customConditional": "show = data.dermotic_peanut_allergy === true;"}, {"key": "dermotic_ear_drum", "type": "radio", "label": "Have you ever had, or do you currently have, a ruptured eardrum?", "values": [{"label": "Yes", "value": true}, {"label": "No", "value": false}], "adminFlag": true, "tableView": true, "validate": {"required": true}, "customConditional": "show = (data.dermotic_peanut_allergy === false) && ((data.ear_canal_plan === 'dermotic') || (data.ear_canal_maint_yes === 'dermotic') || (data.ear_canal_maint_no === 'dermotic'));"}, {"key": "dermotic_infection", "type": "radio", "label": "Have you noticed increased swelling, pus, or discharge from the ear canal?", "values": [{"label": "Yes", "value": true}, {"label": "No", "value": false}], "tableView": true, "adminFlag": true, "validate": {"required": true}, "customConditional": "show = (data.dermotic_peanut_allergy === false) && (data.dermotic_ear_drum === false);"}, {"key": "dermotic_infection_warning", "type": "content", "input": false, "label": "Content", "html": "<div class=\"alert alert-warning\"><strong>Please seek an in-person ear exam.</strong> A ruptured eardrum or active infection needs antibiotic/antifungal drops — DermOtic® should not be started until a clinician checks your ear.</div>", "customConditional": "show = (data.dermotic_ear_drum === true) || (data.dermotic_infection === true);"}, {"key": "dermotic_followup_advice", "type": "content", "input": false, "label": "Content", "html": "<div class=\"alert alert-info\" style=\"padding:0.75rem;\"><p><strong>Maintenance with DermOtic® once a week</strong><br>If you keep DermOtic® in your routine weekly, plan a quick ear check every <strong>3 – 6 months</strong>. Stop after <strong>12 weeks</strong> unless advised otherwise.<br><br>Tip: between DermOtic® doses, 1 – 2 drops of plain mineral oil can keep the canal comfortable and moisturised.</p></div>", "customConditional": "show = (data.ear_canal_maint_yes === 'dermotic' || data.ear_canal_maint_no === 'dermotic') && data.dermotic_infection === false;"}, {"key": "dermotic_followup_ack", "type": "radio", "tableView": true, "label": "I understand these DermOtic® follow-up requirements:", "values": [{"label": "Understood", "value": "understand"}, {"label": "I don’t understand", "value": "dont_understand"}], "widget": "html5", "validate": {"required": true}, "customConditional": "show = (data.ear_canal_maint_yes === 'dermotic' || data.ear_canal_maint_no === 'dermotic') && data.dermotic_infection === false;"}, {"key": "regimen_summary_notice", "html": "<br><h4>Here's a summary of your treatment regimen</h4><p>Don't worry—full instructions and pharmacy directions will be waiting for you in your TeleTest portal.</p>", "type": "content", "input": false, "label": "Content", "tableView": false}, {"key": "psoriasis_flare_table", "type": "datagrid", "input": true, "label": "🔥 Flare-time treatments", "displayAsTable": true, "reorder": false, "disabled": true, "addAnother": false, "tableView": true, "customClass": "flaretbl", "components": [{"key": "site", "type": "textarea", "label": "Application site(s)", "disabled": true, "tableView": true}, {"key": "med", "type": "textarea", "label": "Medication", "disabled": true, "tableView": true}, {"key": "frequency", "type": "textarea", "label": "Frequency / duration", "disabled": true, "tableView": true}], "calculateValue": "value = (() => {\n    /* ── Friendly names ── */\n    const NAME = {\n      hc1:'Hydrocortisone 1 % ointment',\n      hc_protopic:'Hydrocortisone → Protopic 0.1 %',\n      protopic:'Protopic 0.1 % ointment',\n      zoryve_daily:'Zoryve® 0.3 % cream',\n      betaderm005:'Betamethasone valerate 0.05 % cream',\n      betaderm005_calcitriol:'Betamethasone 0.05 % → Calcitriol 3 µg/g',\n      clobetasol005_ointment:'Clobetasol propionate 0.05 % ointment',\n      clobetasol005_solution:'Clobetasol propionate 0.05 % solution',\n      clobetasol005:'Clobetasol propionate 0.05 % ointment',\n      diprosone005:'Betamethasone dipropionate 0.05 %',\n      elocom010:'Mometasone furoate 0.1 % lotion',\n      betameth_lotion:'Betamethasone valerate 0.1 % lotion',\n      dovobet:'Dovobet® gel (Calcipotriol + Betamethasone)',\n      enstilar:'Enstilar® foam (Calcipotriol + Betamethasone)',\n      dovonex:'Calcipotriol 50 µg/g (Dovonex®)',\n      silkis:'Calcitriol 3 µg/g (Silkis®)',\n      dermotic:'Fluocinolone 0.01 % oil drops (DermOtic®)',\n      hc1_lotion:'Hydrocortisone 1 % lotion',\n      hc_then_tac:'Hydrocortisone → Tacrolimus 0.1 %',\n      clob_then_tac:'Clobetasol → Tacrolimus 0.1 %',\n      tacrolimus:'Tacrolimus 0.1 % ointment',\n      tazarotene045:'Tazarotene 0.045 % lotion (Arazlo™)',\n      zoryve_foam:'Zoryve® 0.3 % foam',\n      drug_free:'Drug-free rest day'\n    };\n\n    /* ── Plain-language flare schedules with monograph max duration ── */\n    const FREQ = {\n      hc1:'Twice daily × 7–10 days (max 2 weeks)',\n      hc_protopic:'HC twice daily × 10 days → Protopic twice daily (max 12 weeks total)',\n      protopic:'Twice daily (stop by 12 weeks)',\n      zoryve_daily:'Once daily (up to 8 weeks)',\n      betaderm005:'Twice daily × 7–14 days (max 2 weeks)',\n      betaderm005_calcitriol:'Steroid twice daily × 14 days → Calcitriol twice daily (stop by 12 weeks)',\n      clobetasol005_ointment:'Once daily × 2–4 weeks (do not exceed 50 g/week)',\n      clobetasol005_solution:'1 drop under nail fold nightly (cover) × 4 weeks',\n      clobetasol005:'Twice daily × 5–10 days (max 2 weeks)',\n      diprosone005:'Once daily (twice if very thick) – stop by 4 weeks',\n      elocom010:'Once daily – stop by 4 weeks',\n      betameth_lotion:'1–2 × daily – stop by 2 weeks',\n      dovobet:'Nightly × 4 weeks then weekends only',\n      enstilar:'Nightly up to 4 weeks',\n      dovonex:'Twice daily – stop by 12 weeks',\n      silkis:'Twice daily – stop by 12 weeks',\n      dermotic:'5 drops twice daily × 14 days (max 14 days)',\n      hc1_lotion:'2 drops twice daily × 7 days',\n      hc_then_tac:'HC once daily × 14 days → Tacrolimus twice daily (stop by 12 weeks)',\n      clob_then_tac:'Clobetasol once daily × 14 days → Tacrolimus twice daily (stop by 12 weeks)',\n      tacrolimus:'Twice daily – stop by 12 weeks',\n      tazarotene045:'Nightly under tape – up to 6 months',\n      zoryve_foam:'Once daily – up to 8 weeks',\n      drug_free:'—'\n    };\n\n    const pick = (y,n) => y || n || '';\n    const add  = (map,site,code) => {\n      if (!code) return;\n      if (!map.has(code)) map.set(code,{sites:new Set(),freq:FREQ[code] || 'Use exactly as directed'});\n      map.get(code).sites.add(site);\n    };\n\n    const a = data.body_areas || {}; const m = new Map();\n    if (a.face_groin)  add(m,'Face / Groin',  pick(data.face_groin_plan_yes,  data.face_groin_plan_no));\n    if (a.eyelids)     add(m,'Eyelids',       pick(data.eyelids_plan_yes,     data.eyelids_plan_no));\n    if (a.neck_folds)  add(m,'Neck / Folds',  pick(data.neck_folds_plan_yes,  data.neck_folds_plan_no));\n    if (a.trunk_limbs) add(m,'Trunk / Limbs', pick(data.trunk_limbs_plan_yes, data.trunk_limbs_plan_no));\n    if (a.palms_soles) add(m,'Palms / Soles', pick(data.palms_soles_plan_yes, data.palms_soles_plan_no));\n    if (a.scalp)       add(m,'Scalp',         pick(data.scalp_plan_yes,       data.scalp_plan_no));\n    if (a.ear_canal)   add(m,'Ear canal',     data.ear_canal_plan);\n    if (a.nails)       add(m,'Nails',         pick(data.nails_plan_yes,       data.nails_plan_no));\n\n    return [...m.entries()].map(([c,o]) => ({\n      med: NAME[c] || c,\n      site: [...o.sites].join(', '),\n      frequency: o.freq\n    }));\n  })()"}, {"key": "flare_to_maintenance_guidance", "type": "radio", "tableView": true, "label": "<p><strong>When to switch from flare treatment to maintenance</strong></p><ul><li>Move to your <em>maintenance</em> product as soon as plaques flatten and scaling stops.</li><li><strong>Do&nbsp;not</strong> use the high-strength flare steroid longer than the time shown in the table, if you are prescribed a steroid. If the area still isn’t under control, contact a clinician—you may need a stronger (or different) medicine instead of stretching the course.</li><li>If you're prescribed a non-steroid based medication, you can use it as directed.</li></ul>", "values": [{"label": "I understand", "value": true}, {"label": "I do not understand", "value": false}], "widget": "html5", "validate": {"required": true}}, {"key": "psoriasis_maintenance_table", "type": "datagrid", "input": true, "label": "🛡️ Maintenance treatments", "reorder": false, "disabled": true, "addAnother": false, "tableView": true, "customClass": "flaretbl", "components": [{"key": "site", "type": "textarea", "label": "Application site(s)", "disabled": true, "tableView": true}, {"key": "med", "type": "textarea", "label": "Medication", "disabled": true, "tableView": true}, {"key": "frequency", "type": "textarea", "label": "Frequency / duration", "disabled": true, "tableView": true}], "calculateValue": "value = (() => {\n    const NAME = {\n      protopic_weekend   :'Protopic 0.1 % ointment (weekends)',\n      tacrolimus         :'Tacrolimus 0.1 % ointment',\n      dovonex            :'Calcipotriol 50 µg/g (Dovonex®)',\n      dovobet            :'Dovobet® gel',\n      enstilar           :'Enstilar® foam',\n      diprosone005       :'Betamethasone dipropionate 0.05 % (spot rescue)',\n      silkis             :'Calcitriol 3 µg/g (Silkis®)',\n      zoryve             :'Zoryve® 0.3 % cream',\n      zoryve_daily       :'Zoryve® 0.3 % cream (daily)',\n      zoryve_foam        :'Zoryve® 0.3 % foam',\n      clobetasol005_ointment:'Clobetasol 0.05 % ointment (weekends)',\n      clobetasol005_solution:'Clobetasol 0.05 % solution (rescue)',\n      betaderm005        :'Betamethasone valerate 0.05 % cream (weekends)',\n      mometasone_weekend :'Mometasone 0.1 % lotion (weekends)',\n      dermotic           :'Fluocinolone 0.01 % drops (DermOtic®)',\n      barrier            :'Barrier cream (OTC)',\n      drug_free          :'Drug-free rest day',\n      tazarotene045      :'Tazarotene 0.045 % lotion (Arazlo™)'\n    };\n\n    const FREQ = {\n      protopic_weekend   :'Apply Saturday & Sunday',\n      tacrolimus         :'Wednesday & Saturday nights',\n      dovonex            :'Mon / Tue / Thu evenings',\n      dovobet            :'Sat & Sun nights',\n      enstilar           :'Bedtime Wed & Sat',\n      diprosone005       :'Sun morning as needed',\n      silkis             :'Bedtime Mon → Fri',\n      zoryve             :'Tue & Fri evenings',\n      zoryve_daily       :'Once daily (review every 8 wks)',\n      zoryve_foam        :'Bedtime Wed & Sat',\n      clobetasol005_ointment:'Night Saturday & Sunday',\n      clobetasol005_solution:'Sun morning rescue only',\n      betaderm005        :'Morning Saturday & Sunday',\n      mometasone_weekend :'Sun morning',\n      dermotic           :'5 drops in canal Sunday',\n      barrier            :'Apply once daily',\n      drug_free          :'Sunday – no medication',\n      tazarotene045      :'Mon, Wed & Fri nights – occlude'\n    };\n\n    const pick = (y,n) => y || n || '';\n    const SKIP = ['none'];\n    const add  = (m,s,c) => { if(!c || SKIP.includes(c)) return; if(!m.has(c)) m.set(c,{sites:new Set(),freq:FREQ[c]||'Use as directed'}); m.get(c).sites.add(s);} ;\n\n    const a = data.body_areas || {}; const m = new Map();\n    if(a.face_groin)  add(m,'Face / Groin',  pick(data.face_groin_maint_yes,  data.face_groin_maint_no));\n    if(a.eyelids)     add(m,'Eyelids',       pick(data.eyelids_maint_yes,     data.eyelids_maint_no));\n    if(a.neck_folds)  add(m,'Neck / Folds',  pick(data.neck_folds_maint_yes,  data.neck_folds_maint_no));\n    if(a.trunk_limbs) add(m,'Trunk / Limbs', pick(data.trunk_limbs_maint_yes, data.trunk_limbs_maint_no));\n    if(a.palms_soles) add(m,'Palms / Soles', pick(data.palms_soles_maint_yes, data.palms_soles_maint_no));\n    if(a.scalp)       add(m,'Scalp',         pick(data.scalp_maint_yes,       data.scalp_maint_no));\n    if(a.ear_canal)   add(m,'Ear canal',     pick(data.ear_canal_maint_yes,   data.ear_canal_maint_no));\n    if(a.nails)       add(m,'Nails',         pick(data.nails_maint_yes,       data.nails_maint_no));\n\n    if(m.size === 0) return [{med:'None',site:'—',frequency:'—'}];\n    return [...m.entries()].map(([c,o]) => ({\n      med : NAME[c] || c,\n      site: [...o.sites].join(', '),\n      frequency: o.freq\n    }));\n  })()"}, {"key": "intake_kind", "type": "textfield", "input": true, "label": "Intake Kind", "hidden": true, "disabled": true, "tableView": true, "clearOnHide": false, "calculateValue": "value = !data.intake_denial_reasons?.length ? 'mail' : 'chat';"}, {"key": "intake_mt_key", "type": "textfield", "input": true, "label": "Intake Mail Template Key", "hidden": true, "disabled": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.intake_kind=='mail'?'intake-psoriasis':'intake-denial'"}, {"key": "allowed_rt_keys", "type": "textfield", "input": true, "label": "Allowed Rep<PERSON>", "hidden": true, "disabled": true, "multiple": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.intake_mt_key=='intake-denial'?[]:['get-rx','appointment-intake','edit-intake']"}]}