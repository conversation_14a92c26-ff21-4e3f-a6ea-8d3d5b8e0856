{"components": [{"key": "contraindications_symptoms", "type": "selectboxes", "input": true, "label": "Are you experiencing any of the following symptoms:", "values": [{"label": "Dizziness or fainting spells", "value": "dizziness", "shortcut": ""}, {"label": "Feel unwell or have a fever", "value": "fever", "shortcut": ""}, {"label": "Palpitations or chest pain", "value": "palpitations", "shortcut": ""}, {"label": "Shortness of breath", "value": "dyspnea", "shortcut": ""}, {"label": "Chest tightness, especially if occurring during or after activity", "value": "chest_pain_with_sexual_activity", "shortcut": ""}], "inputType": "checkbox", "tableView": true, "optionsLabelPosition": "right"}, {"key": "asymptomatic", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = _.some(_.values(data.contraindications_symptoms)) || data.asymptomatic;"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "indication_always_true", "type": "hidden", "input": true, "label": "Indication (Always True)", "defaultValue": true}, {"key": "contraindication_keys", "type": "textfield", "input": true, "label": "Contraindication keys:", "hidden": true, "disabled": true, "multiple": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "value = []; if (_.keys(_.pickBy(data.contraindications_symptoms)).length > 0) { value = _.keys(_.pickBy(data.contraindications_symptoms)); }", "refreshOnChange": true}]}