{"components": [{"key": "changes_testicles", "type": "radio", "input": true, "label": "Have you noticed any changes in your testicles (discomfort, felt a lump, or noticed a change in size)?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.sex == 'male'"}, {"key": "testicular_symptom_overview", "type": "selectboxes", "input": true, "label": "Please clarify which of the following symptoms you've noticed:", "values": [{"label": "Pain or discomfort in the testicles", "value": "testicular_pain"}, {"label": "Noticed swelling of testicles", "value": "testicular_swelling"}, {"label": "Felt a lump or mass in the testicle", "value": "testicular_lump"}, {"label": "Pain with ejaculation", "value": "painful_ejaculation"}, {"label": "Blood in my semen", "value": "hematospermia"}, {"label": "Other symptoms", "value": "other_testicular_symptoms"}], "tableView": true, "customConditional": "show = data.sex == 'male' && !!data.changes_testicles && data.changes_testicles == 'yes'"}, {"key": "no_testicular_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = !!data.no_testicular_symptoms || !!_.some(_.values(data.testicular_symptom_overview));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.sex == 'male' && data.changes_testicles == 'yes'"}, {"key": "testicular_symptom_onset_pattern", "type": "radio", "input": true, "label": "Did your testicular symptoms start around the same time or on separate days?", "values": [{"label": "All symptoms started around the same time", "value": "same_time"}, {"label": "Symptoms started on separate days", "value": "separate_days"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.sex == 'male' && data.changes_testicles == 'yes' && !!data.testicular_symptom_overview && _.sum(_.values(data.testicular_symptom_overview).map(Number)) >= 2"}, {"key": "all_testicular_symptoms_start", "data": {"values": [{"label": "Doesn't apply", "value": "doesn't_apply"}, {"label": "Last night", "value": "last_night"}, {"label": "Today", "value": "today"}, {"label": "1 day ago", "value": "1_day_ago"}, {"label": "2 days ago", "value": "2_days_ago"}, {"label": "3 days ago", "value": "3_days_ago"}, {"label": "4 days ago", "value": "4_days_ago"}, {"label": "5 days ago", "value": "5_days_ago"}, {"label": "6 days ago", "value": "6_days_ago"}, {"label": "7 days ago", "value": "7_days_ago"}, {"label": "8 days ago", "value": "8_days_ago"}, {"label": "9 days ago", "value": "9_days_ago"}, {"label": "10 days ago", "value": "10_days_ago"}, {"label": "11 days ago", "value": "11_days_ago"}, {"label": "12 days ago", "value": "12_days_ago"}, {"label": "13 days ago", "value": "13_days_ago"}, {"label": "14 days ago", "value": "14_days_ago"}, {"label": "14-21 days ago", "value": "14-21_days"}, {"label": "21-28 days ago", "value": "21-28_days"}, {"label": "28-60 days ago", "value": "28-60_days"}, {"label": "60-90 days ago", "value": "60-90_days"}, {"label": "90 days - 1 year ago", "value": "90_days-1_year"}, {"label": "1+ year ago", "value": "1+_year"}]}, "type": "select", "input": true, "label": "When did your testicular symptoms start?", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show =  !!data.testicular_symptom_onset_pattern && data.changes_testicles == 'yes' && data.testicular_symptom_overview && data.testicular_symptom_onset_pattern == 'same_time' && !data.no_testicular_symptoms", "optionsLabelPosition": "right"}, {"key": "heading_pain_discomfort", "html": "</br><h5>Pain or Discomfort in the Testicles</h5>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.sex == 'male' && data.changes_testicles == 'yes' && data.testicular_symptom_overview.testicular_pain"}, {"key": "symptom_start_testicular_discomfort", "data": {"values": [{"label": "Doesn't apply", "value": "doesn't_apply"}, {"label": "Last night", "value": "last_night"}, {"label": "Today", "value": "today"}, {"label": "1 day ago", "value": "1_day_ago"}, {"label": "2 days ago", "value": "2_days_ago"}, {"label": "3 days ago", "value": "3_days_ago"}, {"label": "4 days ago", "value": "4_days_ago"}, {"label": "5 days ago", "value": "5_days_ago"}, {"label": "6 days ago", "value": "6_days_ago"}, {"label": "7 days ago", "value": "7_days_ago"}, {"label": "8 days ago", "value": "8_days_ago"}, {"label": "9 days ago", "value": "9_days_ago"}, {"label": "10 days ago", "value": "10_days_ago"}, {"label": "11 days ago", "value": "11_days_ago"}, {"label": "12 days ago", "value": "12_days_ago"}, {"label": "13 days ago", "value": "13_days_ago"}, {"label": "14 days ago", "value": "14_days_ago"}, {"label": "14-21 days ago", "value": "14-21_days"}, {"label": "21-28 days ago", "value": "21-28_days"}, {"label": "28-60 days ago", "value": "28-60_days"}, {"label": "60-90 days ago", "value": "60-90_days"}, {"label": "90 days - 1 year ago", "value": "90_days-1_year"}, {"label": "1+ year ago", "value": "1+_year"}]}, "type": "select", "input": true, "label": "When did your testicular discomfort start?", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.changes_testicles == 'yes' && data.testicular_symptom_overview.testicular_pain && (_.sum(_.values(data.testicular_symptom_overview).map(Number)) < 2 || data.testicular_symptom_onset_pattern == 'separate_days')", "optionsLabelPosition": "right"}, {"key": "which_testicle_affected", "type": "radio", "input": true, "label": "Which testicle is bothering you?", "values": [{"label": "Left", "value": "left"}, {"label": "Right", "value": "right"}, {"label": "Both", "value": "both"}], "tableView": true, "customConditional": "show = data.sex == 'male' && !!data.testicular_symptom_overview && data.testicular_symptom_overview.testicular_pain"}, {"key": "previous_discomfort", "type": "radio", "input": true, "label": "Have you had similar discomfort in the past?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "tableView": true, "customConditional": "show = data.sex == 'male' && !!data.which_testicle_affected"}, {"key": "past_testicular_diagnosis", "type": "selectboxes", "input": true, "label": "What was the diagnosis when you had similar symptoms?", "values": [{"label": "I don't know - I never saw a doctor/nurse practitioner", "value": "no_diagnosis_no_assessment"}, {"label": "We never figured it out", "value": "no_diagnosis"}, {"label": "Hernia", "value": "hernia"}, {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "epididymitis"}, {"label": "Groin sprain", "value": "groin_sprain"}, {"label": "Varicocele", "value": "varicocele"}, {"label": "Other", "value": "other"}], "tableView": true, "customConditional": "show = data.sex == 'male' && !!data.previous_discomfort && data.previous_discomfort == 'yes'"}, {"key": "no_past_testicular_diagnosis", "type": "checkbox", "input": true, "label": "None of the above", "validate": {"custom": "valid = !!data.no_past_testicular_diagnosis || !!_.some(_.values(data.past_testicular_diagnosis));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.sex == 'male' && !!data.previous_discomfort && data.previous_discomfort == 'yes'"}, {"key": "description_of_other_diagnosis", "type": "textarea", "input": true, "label": "Please describe the other diagnosis:", "tableView": true, "customConditional": "show = !!data.past_testicular_diagnosis && data.past_testicular_diagnosis.other"}, {"key": "testicular_diagnosis_tests", "type": "selectboxes", "input": true, "label": "At the time, did you have any of the following tests done?", "values": [{"label": "Ultrasound", "value": "ultrasound"}, {"label": "STI testing", "value": "sti_testing"}, {"label": "Referral to a urologist", "value": "referral_to_urologist"}], "tableView": true, "customConditional": "show = data.sex == 'male' && !!data.previous_discomfort && data.previous_discomfort == 'yes' && !!_.some(_.values(data.past_testicular_diagnosis))"}, {"key": "pain_onset", "data": {"values": [{"label": "Doesn't apply", "value": "doesn't_apply"}, {"label": "Last night", "value": "last_night"}, {"label": "Today", "value": "today"}, {"label": "1 day ago", "value": "1_day_ago"}, {"label": "2 days ago", "value": "2_days_ago"}, {"label": "3 days ago", "value": "3_days_ago"}, {"label": "4 days ago", "value": "4_days_ago"}, {"label": "5 days ago", "value": "5_days_ago"}, {"label": "6 days ago", "value": "6_days_ago"}, {"label": "7 days ago", "value": "7_days_ago"}, {"label": "8 days ago", "value": "8_days_ago"}, {"label": "9 days ago", "value": "9_days_ago"}, {"label": "10 days ago", "value": "10_days_ago"}, {"label": "11 days ago", "value": "11_days_ago"}, {"label": "12 days ago", "value": "12_days_ago"}, {"label": "13 days ago", "value": "13_days_ago"}, {"label": "14 days ago", "value": "14_days_ago"}, {"label": "14-21 days ago", "value": "14-21_days"}, {"label": "21-28 days ago", "value": "21-28_days"}, {"label": "28-60 days ago", "value": "28-60_days"}, {"label": "60-90 days ago", "value": "60-90_days"}, {"label": "90 days - 1 year ago", "value": "90_days-1_year"}, {"label": "1+ year ago", "value": "1+_year"}]}, "type": "select", "input": true, "label": "When did your current episode of discomfort start?", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.sex == 'male' && !!data.past_testicular_diagnosis"}, {"key": "pain_progress", "type": "radio", "input": true, "label": "Is the pain getting better, worse, or staying the same?", "values": [{"label": "Getting better", "value": "better"}, {"label": "Getting worse", "value": "worse"}, {"label": "Staying the same", "value": "same"}], "tableView": true, "customConditional": "show = data.sex == 'male' && !!data.pain_onset"}, {"key": "trauma_injury", "type": "radio", "input": true, "label": "Was there any trauma or injury to the testicle?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "tableView": true, "customConditional": "show = data.sex == 'male' && !!data.pain_onset"}, {"key": "heading_swelling", "html": "</br><h5>Swelling of Testicles</h5>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.sex == 'male' && data.changes_testicles == 'yes' && data.testicular_symptom_overview.testicular_swelling"}, {"key": "heading_lump_mass", "html": "</br><h5>Lump or Mass in the Testicle</h5>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.sex == 'male' && data.changes_testicles == 'yes' && data.testicular_symptom_overview.testicular_lump"}, {"key": "heading_pain_ejaculation", "html": "</br><h5>Pain with Ejaculation</h5>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.sex == 'male' && data.changes_testicles == 'yes' && data.testicular_symptom_overview.painful_ejaculation"}, {"key": "heading_blood_semen", "html": "</br><h5>Blood in Semen</h5>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.sex == 'male' && data.changes_testicles == 'yes' && data.testicular_symptom_overview.hematospermia"}]}