{"name": "Uninsured No Symptoms", "type": "form", "title": "Uninsured No Symptoms", "display": "form", "components": [{"key": "noSymptomsList", "type": "mylist", "input": true, "label": "", "multiple": true, "iconClass": "fa-solid fa-syringe", "iconColor": "grey", "tableView": false, "conditional": {"json": {"and": [{"some": [{"var": "data.assay_choices"}, {"in": [{"var": ""}, ["AMH", "B9", "D25", "PSA", "RT3", "SHBG", "TSH", "TT"]]}]}]}}, "defaultValue": [], "calculateValue": {"_intersection": [["AMH", "B9", "D25", "PSA", "RT3", "SHBG", "TSH", "TT"], {"var": "data.assay_choices"}]}, "clearOnRefresh": true}, {"key": "uninsuredNoSymptoms", "type": "radio", "input": true, "label": "I currently have no symptoms for the above tests & am interested in testing for surveillance purposes:", "hidden": false, "inline": false, "values": [{"label": "I Confirm I have no symptoms", "value": true, "shortcut": ""}, {"label": "I currently have symptoms", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": false, "conditional": {"json": {"and": [{"some": [{"var": "data.assay_choices"}, {"in": [{"var": ""}, ["AMH", "B9", "D", "PSA", "RT3", "SHBG", "TSH", "TT"]]}]}]}}, "customClass": "mt-n3", "defaultValue": false, "optionsLabelPosition": "right"}, {"key": "noTest", "type": "textfield", "input": true, "label": "No Test List", "hidden": true, "disabled": true, "multiple": true, "tableView": true, "spellcheck": false, "clearOnHide": false, "calculateValue": [{"!": {"var": "data.uninsuredNoSymptoms"}}]}]}