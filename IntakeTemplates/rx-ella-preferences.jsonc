{"components": [{"key": "ec_preferences_header", "html": "<h2>Emergency Contraception Preferences</h2><p>Please confirm which emergency contraception option you wish to obtain.</p>", "type": "content", "input": false, "label": "Content"}, {"key": "ec_confirm_choice", "type": "radio", "input": true, "label": "Which of the following emergency contraception options do you wish to obtain?", "confirm_label": "Emergency Contraception Choice:", "values": [{"label": "ELLA (Ulipristal Acetate)", "value": "ella-30mg"}, {"label": "Copper IUD (Non-Hormonal)", "value": "copper_iud"}], "validate": {"required": true}, "tableView": true}, {"key": "health_conditions_medication_allergy_header", "html": "<h2>Health Conditions, Medication and Allergy Confirmation</h2>", "type": "content", "input": false, "customConditional": "show = (!data.has_health_conditions || data.health_conditions_list === '') || (!data.has_medication_allergies || data.medication_allergies_list === '') || (!data.on_medications || data.medications_list === '');", "label": "Content"}, {"key": "confirm_no_medications", "type": "textarea", "input": true, "label": "You indicated you're not on any medications. Please confirm below by writing: 'No medications' or list your medications here:", "validate": {"required": true}, "tableView": false, "autoExpand": false, "confirm_label": "Medications:", "customConditional": "show = !data.on_medications || data.medications_list === '';"}, {"key": "confirm_no_allergies", "type": "textarea", "input": true, "label": "You indicated you do not have any medication allergies. Please confirm below by writing: 'No medication allergies' or list your allergies and reactions here:", "validate": {"required": true}, "tableView": false, "autoExpand": false, "confirm_label": "Allergies:", "customConditional": "show = !data.has_medication_allergies || data.medication_allergies_list === '';"}, {"key": "confirm_no_health_conditions", "type": "textarea", "input": true, "label": "You indicated you do not have any diagnosed health conditions. Please confirm below by writing: 'No diagnosed health conditions' or list your health conditions here:", "validate": {"required": true}, "tableView": false, "autoExpand": false, "confirm_label": "Health Conditions:", "customConditional": "show = !data.has_health_conditions || data.health_conditions_list === '';"}, {"key": "rxts", "type": "textfield", "input": true, "label": "Rx Templates:", "hidden": true, "disabled": true, "multiple": true, "adminFlag": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "value = data.ec_confirm_choice === 'ella-30mg' ? ['ella_rx_template'] : data.ec_confirm_choice === 'copper_iud' ? ['copper_iud_rx_template'] : [];", "refreshOnChange": true}]}