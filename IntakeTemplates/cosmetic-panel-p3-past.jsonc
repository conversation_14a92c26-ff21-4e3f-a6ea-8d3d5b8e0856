{"components": [{"key": "past_heading_current_treatments", "html": "<h1><strong>Past Treatments</strong></h1>", "type": "content", "input": false, "label": "Content"}, {"key": "past_heading_current_treatments_intro", "html": "<p>This section is for recording any treatments you <strong>previously used</strong>.</p>", "type": "content", "input": false, "label": "Content", "tableView": false}, {"key": "past_heading_medications", "html": "<h2><strong>Medications</strong></h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "doctor_prescribed_treatments", "type": "radio", "input": true, "label": "Were you previously on any medications prescribed by a doctor for your skin?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "past_medication_classes", "type": "selectboxes", "input": true, "label": "Which classes of skin medications did you previously use? (Select all that apply)", "values": [{"label": "Topical Retinoids (i.e. <PERSON><PERSON><PERSON>in, Adapalene, Tazarotene)", "value": "past_topical_retinoids"}, {"label": "Oral Retinoids (i.e. Accutane)", "value": "past_oral_retinoids"}, {"label": "Benzoyl Peroxide (BPO)", "value": "bpo"}, {"label": "Topical Antibiotics (i.e. Clindamycin)", "value": "past_topical_antibiotics"}, {"label": "Oral Antibiotics (i.e. Doxycycline)", "value": "oral_antibiotics"}, {"label": "Skin Lightening Agents (i.e. Hydroquinone, Triple Cream)", "value": "past_skin_lightening_agents"}], "tableView": true, "customConditional": "show = data.doctor_prescribed_treatments === 'yes';", "optionsLabelPosition": "right"}, {"key": "past_heading_past_topical_retinoids", "html": "<h3><strong>Topical Retinoids</strong></h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.past_medication_classes && data.past_medication_classes.past_topical_retinoids;"}, {"key": "past_topical_retinoids", "type": "selectboxes", "input": true, "label": "Which topical retinoids have you previously used? (Select all that apply)", "values": [{"label": "Tretinoin", "value": "tretinoin"}, {"label": "Adapal<PERSON>", "value": "adapalene"}, {"label": "Tazarotene", "value": "tazarotene"}, {"label": "None of the above", "value": "none_of_the_above"}, {"label": "Other", "value": "other"}], "tableView": true, "customConditional": "show = data.past_medication_classes && data.past_medication_classes.past_topical_retinoids;", "optionsLabelPosition": "right"}, {"key": "past_heading_tretinoin", "html": "<h4><strong>Tretinoin</strong></h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.past_topical_retinoids && data.past_topical_retinoids.tretinoin;"}, {"key": "past_tretinoin_strength", "type": "radio", "input": true, "label": "What is the maximum strength of Tretinoin that you used?", "values": [{"label": "0.025%", "value": "0.025"}, {"label": "0.05%", "value": "0.05"}, {"label": "0.1%", "value": "0.1"}], "tableView": true, "customConditional": "show = data.past_topical_retinoids && data.past_topical_retinoids.tretinoin;"}, {"key": "past_tretinoin_duration", "data": {"values": [{"label": "Less than 1 month", "value": "less_than_1_month"}, {"label": "1-3 months", "value": "1_3_months"}, {"label": "3-6 months", "value": "3_6_months"}, {"label": "6-12 months", "value": "6_12_months"}, {"label": "1-2 years", "value": "1_2_years"}, {"label": "More than 2 years", "value": "more_than_2_years"}]}, "type": "select", "widget": "html5", "input": true, "label": "What was the maximum duration you used Tretinoin?", "tableView": true, "customConditional": "show = data.past_topical_retinoids && data.past_topical_retinoids.tretinoin;"}, {"key": "past_tretinoin_frequency", "data": {"values": [{"label": "Daily", "value": "daily"}, {"label": "Every other day", "value": "every_other_day"}, {"label": "Twice a week", "value": "twice_weekly"}, {"label": "Weekly", "value": "weekly"}]}, "type": "select", "widget": "html5", "input": true, "label": "When you were using it, how often did you apply Tretinoin?", "tableView": true, "customConditional": "show = data.past_topical_retinoids && data.past_topical_retinoids.tretinoin;"}, {"key": "past_tretinoin_tolerability", "type": "radio", "input": true, "label": "How well did you tolerate Tretinoin?", "values": [{"label": "Very well (no irritation)", "value": "very_well"}, {"label": "Moderate (some irritation)", "value": "moderate"}, {"label": "Poorly (significant irritation)", "value": "poorly"}], "tableView": true, "customConditional": "show = data.past_topical_retinoids && data.past_topical_retinoids.tretinoin;"}, {"key": "past_tretinoin_improvement", "type": "radio", "input": true, "label": "Did you notice an improvement in your skin when using Tretinoin?", "values": [{"label": "Significant improvement", "value": "significant_improvement"}, {"label": "Moderate improvement", "value": "moderate_improvement"}, {"label": "Slight improvement", "value": "slight_improvement"}, {"label": "No improvement", "value": "no_improvement"}, {"label": "Worsening", "value": "worsening"}, {"label": "Unsure", "value": "unsure"}], "tableView": true, "customConditional": "show = data.past_topical_retinoids && data.past_topical_retinoids.tretinoin;"}, {"key": "past_heading_adapalene", "html": "<h4><strong><PERSON><PERSON><PERSON></strong></h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.past_topical_retinoids && data.past_topical_retinoids.adapalene;"}, {"key": "past_adapalene_strength", "type": "radio", "input": true, "label": "What strength of Adapalene did you use in the past?", "values": [{"label": "0.1%", "value": "0.1"}, {"label": "0.3%", "value": "0.3"}], "tableView": true, "customConditional": "show = data.past_topical_retinoids && data.past_topical_retinoids.adapalene;"}, {"key": "past_adapalene_duration", "data": {"values": [{"label": "Less than 1 month", "value": "less_than_1_month"}, {"label": "1-3 months", "value": "1_3_months"}, {"label": "3-6 months", "value": "3_6_months"}, {"label": "6-12 months", "value": "6_12_months"}, {"label": "1-2 years", "value": "1_2_years"}, {"label": "More than 2 years", "value": "more_than_2_years"}]}, "type": "select", "input": true, "widget": "html5", "label": "How long did you use Adapalene in the past?", "tableView": true, "customConditional": "show = data.past_topical_retinoids && data.past_topical_retinoids.adapalene;"}, {"key": "past_adapalene_frequency", "data": {"values": [{"label": "Daily", "value": "daily"}, {"label": "Every other day", "value": "every_other_day"}, {"label": "Twice a week", "value": "twice_weekly"}, {"label": "Weekly", "value": "weekly"}]}, "type": "select", "widget": "html5", "input": true, "label": "How often did you apply Adapalene?", "tableView": true, "customConditional": "show = data.past_topical_retinoids && data.past_topical_retinoids.adapalene;"}, {"key": "past_adapalene_tolerability", "type": "radio", "input": true, "label": "How well did you tolerate <PERSON><PERSON><PERSON>?", "values": [{"label": "Very well (no irritation)", "value": "very_well"}, {"label": "Moderate (some irritation)", "value": "moderate"}, {"label": "Poorly (significant irritation)", "value": "poorly"}], "tableView": true, "customConditional": "show = data.past_topical_retinoids && data.past_topical_retinoids.adapalene;"}, {"key": "past_adapalene_improvement", "type": "radio", "input": true, "label": "Did you notice an improvement in your skin since when using Adapalene?", "values": [{"label": "Significant improvement", "value": "significant_improvement"}, {"label": "Moderate improvement", "value": "moderate_improvement"}, {"label": "Slight improvement", "value": "slight_improvement"}, {"label": "No improvement", "value": "no_improvement"}, {"label": "Worsening", "value": "worsening"}, {"label": "Unsure", "value": "unsure"}], "tableView": true, "customConditional": "show = data.past_topical_retinoids && data.past_topical_retinoids.adapalene;"}, {"key": "past_heading_tazarotene", "html": "<h4><strong><PERSON><PERSON><PERSON><PERSON></strong></h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.past_topical_retinoids && data.past_topical_retinoids.tazarotene;"}, {"key": "past_tazarotene_strength", "type": "radio", "input": true, "label": "What strength of Tazarotene did you use?", "values": [{"label": "0.05%", "value": "0.05"}, {"label": "0.1%", "value": "0.1"}], "tableView": true, "customConditional": "show = data.past_topical_retinoids && data.past_topical_retinoids.tazarotene;"}, {"key": "past_tazarotene_duration", "data": {"values": [{"label": "Less than 1 month", "value": "less_than_1_month"}, {"label": "1-3 months", "value": "1_3_months"}, {"label": "3-6 months", "value": "3_6_months"}, {"label": "6-12 months", "value": "6_12_months"}, {"label": "1-2 years", "value": "1_2_years"}, {"label": "More than 2 years", "value": "more_than_2_years"}]}, "type": "select", "widget": "html5", "input": true, "label": "How long did you been use Tazarotene?", "tableView": true, "customConditional": "show = data.past_topical_retinoids && data.past_topical_retinoids.tazarotene;"}, {"key": "past_tazarotene_frequency", "data": {"values": [{"label": "Daily", "value": "daily"}, {"label": "Every other day", "value": "every_other_day"}, {"label": "Twice a week", "value": "twice_weekly"}, {"label": "Weekly", "value": "weekly"}]}, "type": "select", "input": true, "label": "How often did you apply Tazarotene?", "widget": "html5", "tableView": true, "customConditional": "show = data.past_topical_retinoids && data.past_topical_retinoids.tazarotene;"}, {"key": "past_tazarotene_tolerability", "type": "radio", "input": true, "label": "How well did you tolerate <PERSON><PERSON><PERSON><PERSON>?", "values": [{"label": "Very well (no irritation)", "value": "very_well"}, {"label": "Moderate (some irritation)", "value": "moderate"}, {"label": "Poorly (significant irritation)", "value": "poorly"}], "tableView": true, "customConditional": "show = data.past_topical_retinoids && data.past_topical_retinoids.tazarotene;"}, {"key": "past_tazarotene_improvement", "type": "radio", "input": true, "label": "Did you notice an improvement in your skin while using Tazarotene?", "values": [{"label": "Significant improvement", "value": "significant_improvement"}, {"label": "Moderate improvement", "value": "moderate_improvement"}, {"label": "Slight improvement", "value": "slight_improvement"}, {"label": "No improvement", "value": "no_improvement"}, {"label": "Worsening", "value": "worsening"}, {"label": "Unsure", "value": "unsure"}], "tableView": true, "customConditional": "show = data.past_topical_retinoids && data.past_topical_retinoids.tazarotene;"}, {"key": "past_heading_past_oral_retinoids", "html": "<h3><strong>Oral Retinoids</strong></h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.past_medication_classes && data.past_medication_classes.past_oral_retinoids;"}, {"key": "past_oral_retinoids", "type": "selectboxes", "input": true, "label": "Which oral retinoids did you use? (Select all that apply)", "values": [{"label": "Accutane", "value": "accutane"}, {"label": "<PERSON><PERSON><PERSON>", "value": "clarus"}, {"label": "Epuris", "value": "epuris"}, {"label": "Acitretin", "value": "acitretin"}, {"label": "None of the above", "value": "none_of_the_above"}, {"label": "Other", "value": "other"}], "tableView": true, "customConditional": "show = data.past_medication_classes && data.past_medication_classes.past_oral_retinoids;", "optionsLabelPosition": "right"}, {"key": "past_oral_retinoid_duration", "data": {"values": [{"label": "Less than 1 month", "value": "less_than_1_month"}, {"label": "1-3 months", "value": "1_3_months"}, {"label": "3-6 months", "value": "3_6_months"}, {"label": "6-12 months", "value": "6_12_months"}, {"label": "1-2 years", "value": "1_2_years"}, {"label": "More than 2 years", "value": "more_than_2_years"}]}, "type": "select", "widget": "html5", "input": true, "label": "How long ago did you take oral retinoids?", "tableView": true, "customConditional": "show = data.past_oral_retinoids && (_.some(_.values(data.past_oral_retinoids)));"}, {"key": "past_oral_retinoid_improvement", "type": "radio", "input": true, "label": "Did you notice an improvement in your skin while taking oral retinoids?", "values": [{"label": "Significant improvement", "value": "significant_improvement"}, {"label": "Moderate improvement", "value": "moderate_improvement"}, {"label": "Slight improvement", "value": "slight_improvement"}, {"label": "No improvement", "value": "no_improvement"}, {"label": "Worsening", "value": "worsening"}, {"label": "Unsure", "value": "unsure"}], "tableView": true, "customConditional": "show = data.past_oral_retinoids && (_.some(_.values(data.past_oral_retinoids)));"}, {"key": "past_oral_retinoid_tolerability", "type": "radio", "input": true, "label": "How well did you tolerate the oral retinoid?", "values": [{"label": "Very well (no side effects)", "value": "very_well"}, {"label": "Moderately well (mild side effects)", "value": "moderately_well"}, {"label": "Poorly (moderate side effects)", "value": "poorly"}, {"label": "Very poorly (severe side effects)", "value": "very_poorly"}], "tableView": true, "customConditional": "show = data.past_oral_retinoids && (_.some(_.values(data.past_oral_retinoids)));"}, {"key": "past_heading_bpo", "html": "<h3><strong>Benzoyl Peroxide (BPO)</strong></h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.past_medication_classes && data.past_medication_classes.bpo;"}, {"key": "past_bpo_products", "type": "selectboxes", "input": true, "label": "What types of Benzoyl Peroxide (BPO) products have you used? (Select all that apply)", "values": [{"label": "<PERSON>el", "value": "gel"}, {"label": "Cream", "value": "cream"}, {"label": "Foam", "value": "foam"}, {"label": "Wash", "value": "wash"}], "tableView": true, "customConditional": "show = data.past_medication_classes && data.past_medication_classes.bpo;", "optionsLabelPosition": "right"}, {"key": "past_bpo_strength", "type": "selectboxes", "input": true, "label": "What strength of Benzoyl Peroxide (BPO) did you use? (Select all that apply)", "values": [{"label": "2.5%", "value": "2_5_percent"}, {"label": "5%", "value": "5_percent"}, {"label": "10%", "value": "10_percent"}, {"label": "Other", "value": "other"}], "tableView": true, "customConditional": "show = data.past_bpo_products && _.some(_.values(data.past_bpo_products));"}, {"key": "past_bpo_application_area", "type": "selectboxes", "input": true, "label": "Where did you apply Benzoyl Peroxide?", "values": [{"label": "Face", "value": "face"}, {"label": "Chest", "value": "chest"}, {"label": "Back", "value": "back"}, {"label": "Other", "value": "other"}], "tableView": true, "customConditional": "show = data.past_bpo_strength && _.some(_.values(data.past_bpo_strength));"}, {"key": "past_bpo_frequency", "type": "select", "widget": "html5", "input": true, "label": "How often did you apply Benzoyl Peroxide?", "data": {"values": [{"label": "Once daily", "value": "once_daily"}, {"label": "Twice daily", "value": "twice_daily"}, {"label": "A few times a week", "value": "few_times_week"}, {"label": "Occasionally", "value": "occasionally"}]}, "tableView": true, "customConditional": "show = data.past_bpo_application_area && _.some(_.values(data.past_bpo_application_area));"}, {"key": "past_bpo_tolerability", "type": "radio", "input": true, "label": "How well did you tolerate Benzoyl Peroxide?", "values": [{"label": "Very well (no irritation)", "value": "very_well"}, {"label": "Moderately well (mild irritation)", "value": "moderately_well"}, {"label": "Poorly (moderate irritation)", "value": "poorly"}, {"label": "Very poorly (severe irritation)", "value": "very_poorly"}], "tableView": true, "customConditional": "show = data.past_bpo_frequency && data.past_bpo_frequency !== 'none';"}, {"key": "past_heading_past_topical_antibiotics", "html": "<h3><strong>Topical Antibiotics</strong></h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.past_medication_classes && data.past_medication_classes.past_topical_antibiotics;"}, {"key": "past_topical_antibiotics", "type": "selectboxes", "input": true, "label": "Which topical antibiotics have you used in the past? (Select all that apply)", "values": [{"label": "Clindamycin", "value": "clindamycin"}, {"label": "Erythromycin", "value": "erythromycin"}, {"label": "Metronidazole", "value": "metronidazole"}], "tableView": true, "customConditional": "show = data.past_medication_classes && data.past_medication_classes.past_topical_antibiotics;", "optionsLabelPosition": "right"}, {"key": "past_heading_clindam<PERSON>cin", "html": "<h4><strong>Clindamy<PERSON></strong></h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.past_topical_antibiotics && data.past_topical_antibiotics.clindamycin;"}, {"key": "past_clindamycin_strength", "type": "radio", "input": true, "label": "What strength of Clindamycin did you use?", "values": [{"label": "1%", "value": "1_percent"}, {"label": "2%", "value": "2_percent"}, {"label": "Other", "value": "other"}], "tableView": true, "customConditional": "show = data.past_topical_antibiotics && data.past_topical_antibiotics.clindamycin;"}, {"key": "past_clindamycin_application_area", "type": "selectboxes", "input": true, "label": "Where did you apply Clindamycin?", "values": [{"label": "Face", "value": "face"}, {"label": "Chest", "value": "chest"}, {"label": "Back", "value": "back"}, {"label": "Other", "value": "other"}], "tableView": true, "customConditional": "show = data.past_clindamycin_strength && _.some(_.values(data.past_clindamycin_strength));"}, {"key": "past_clindamycin_frequency", "type": "select", "input": true, "widget": "html5", "label": "How often did you apply Clindamycin?", "data": {"values": [{"label": "Once daily", "value": "once_daily"}, {"label": "Twice daily", "value": "twice_daily"}, {"label": "A few times a week", "value": "few_times_week"}, {"label": "Occasionally", "value": "occasionally"}]}, "tableView": true, "customConditional": "show = data.past_clindamycin_application_area && _.some(_.values(data.past_clindamycin_application_area));"}, {"key": "past_clindamycin_tolerability", "type": "radio", "input": true, "label": "How well did you tolerate Clindamy<PERSON>?", "values": [{"label": "Very well (no irritation)", "value": "very_well"}, {"label": "Moderately well (mild irritation)", "value": "moderately_well"}, {"label": "Poorly (moderate irritation)", "value": "poorly"}, {"label": "Very poorly (severe irritation)", "value": "very_poorly"}], "tableView": true, "customConditional": "show = data.past_clindamycin_frequency && data.past_clindamycin_frequency !== 'none';"}, {"key": "past_heading_erythromycin", "html": "<h4><strong>Erythromycin</strong></h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.past_topical_antibiotics && data.past_topical_antibiotics.erythromycin;"}, {"key": "past_erythromycin_strength", "type": "radio", "input": true, "label": "What strength of Erythromycin did you use?", "values": [{"label": "1%", "value": "1_percent"}, {"label": "2%", "value": "2_percent"}, {"label": "Other", "value": "other"}], "tableView": true, "customConditional": "show = data.past_topical_antibiotics && data.past_topical_antibiotics.erythromycin;"}, {"key": "past_erythromycin_application_area", "type": "selectboxes", "input": true, "label": "Where did you apply Erythromycin?", "values": [{"label": "Face", "value": "face"}, {"label": "Chest", "value": "chest"}, {"label": "Back", "value": "back"}, {"label": "Other", "value": "other"}], "tableView": true, "customConditional": "show = data.past_erythromycin_strength && _.some(_.values(data.past_erythromycin_strength));"}, {"key": "past_erythromycin_frequency", "type": "select", "input": true, "widget": "html5", "label": "How often did you apply Erythromycin?", "data": {"values": [{"label": "Once daily", "value": "once_daily"}, {"label": "Twice daily", "value": "twice_daily"}, {"label": "A few times a week", "value": "few_times_week"}, {"label": "Occasionally", "value": "occasionally"}]}, "tableView": true, "customConditional": "show = data.past_erythromycin_application_area && _.some(_.values(data.past_erythromycin_application_area));"}, {"key": "past_erythromycin_tolerability", "type": "radio", "input": true, "label": "How well did you tolerate Erythromycin?", "values": [{"label": "Very well (no irritation)", "value": "very_well"}, {"label": "Moderately well (mild irritation)", "value": "moderately_well"}, {"label": "Poorly (moderate irritation)", "value": "poorly"}, {"label": "Very poorly (severe irritation)", "value": "very_poorly"}], "tableView": true, "customConditional": "show = data.past_erythromycin_frequency && data.past_erythromycin_frequency !== 'none';"}, {"key": "past_heading_metroni<PERSON><PERSON>le", "html": "<h4><strong>Metronidazole</strong></h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.past_topical_antibiotics && data.past_topical_antibiotics.metronidazole;"}, {"key": "past_metronidazole_strength", "type": "radio", "input": true, "label": "What strength of Metronidazole did you use?", "values": [{"label": "0.75%", "value": "0_75_percent"}, {"label": "1%", "value": "1_percent"}, {"label": "Other", "value": "other"}], "tableView": true, "customConditional": "show = data.past_topical_antibiotics && data.past_topical_antibiotics.metronidazole;"}, {"key": "past_metronidazole_application_area", "type": "selectboxes", "input": true, "label": "Where did you apply Metronidazole?", "values": [{"label": "Face", "value": "face"}, {"label": "Chest", "value": "chest"}, {"label": "Back", "value": "back"}, {"label": "Other", "value": "other"}], "tableView": true, "customConditional": "show = data.past_metronidazole_strength && _.some(_.values(data.past_metronidazole_strength));"}, {"key": "past_metronidazole_frequency", "type": "select", "input": true, "widget": "html5", "label": "How often did you apply Metronidazole?", "data": {"values": [{"label": "Once daily", "value": "once_daily"}, {"label": "Twice daily", "value": "twice_daily"}, {"label": "A few times a week", "value": "few_times_week"}, {"label": "Occasionally", "value": "occasionally"}]}, "tableView": true, "customConditional": "show = data.past_metronidazole_application_area && _.some(_.values(data.past_metronidazole_application_area));"}, {"key": "past_metronidazole_tolerability", "type": "radio", "input": true, "label": "How well did you tolerate Metronidazo<PERSON>?", "values": [{"label": "Very well (no irritation)", "value": "very_well"}, {"label": "Moderately well (mild irritation)", "value": "moderately_well"}, {"label": "Poorly (moderate irritation)", "value": "poorly"}, {"label": "Very poorly (severe irritation)", "value": "very_poorly"}], "tableView": true, "customConditional": "show = data.past_metronidazole_frequency && data.past_metronidazole_frequency !== 'none';"}, {"key": "past_heading_oral_antibiotics_details", "html": "<h3><strong>Oral Antibiotics Details</strong></h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.past_medication_classes && data.past_medication_classes.oral_antibiotics;"}, {"key": "past_oral_antibiotic_types", "type": "selectboxes", "input": true, "label": "Which oral antibiotics have you used in the past? (Select all that apply)", "values": [{"label": "Doxycycline", "value": "doxycycline"}, {"label": "Minocycline", "value": "minocycline"}, {"label": "Tetracycline", "value": "tetracycline"}, {"label": "Other", "value": "other"}, {"label": "None of the above", "value": "none_of_the_above"}], "tableView": true, "customConditional": "show = data.past_medication_classes && data.past_medication_classes.oral_antibiotics;", "optionsLabelPosition": "right"}, {"key": "past_oral_antibiotics_other_details", "type": "textfield", "input": true, "label": "If other, please specify the name(s):", "tableView": true, "customConditional": "show = data.past_oral_antibiotic_types && data.past_oral_antibiotic_types.other;"}, {"key": "past_heading_doxycycline", "html": "<h4><strong>Doxycycline</strong></h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.past_oral_antibiotic_types && data.past_oral_antibiotic_types.doxycycline;"}, {"key": "past_doxycycline_duration", "type": "select", "input": true, "widget": "html5", "label": "How long did you take Doxycycline?", "data": {"values": [{"label": "Less than 1 month", "value": "less_than_1_month"}, {"label": "1-3 months", "value": "1_3_months"}, {"label": "3-6 months", "value": "3_6_months"}, {"label": "6-12 months", "value": "6_12_months"}, {"label": "1-2 years", "value": "1_2_years"}, {"label": "More than 2 years", "value": "more_than_2_years"}]}, "tableView": true, "customConditional": "show = data.past_oral_antibiotic_types && data.past_oral_antibiotic_types.doxycycline;"}, {"key": "past_doxycycline_dosage", "type": "select", "input": true, "widget": "html5", "label": "What dosage of Doxycycline were you prescribed?", "data": {"values": [{"label": "40 mg once daily", "value": "40mg_once_daily"}, {"label": "100 mg once daily", "value": "100mg_once_daily"}, {"label": "100 mg twice daily", "value": "100mg_twice_daily"}, {"label": "Other", "value": "other"}]}, "tableView": true, "customConditional": "show = data.past_oral_antibiotic_types && data.past_oral_antibiotic_types.doxycycline;"}, {"key": "past_doxycycline_dosage_other", "type": "textfield", "input": true, "label": "Please specify your dosage:", "tableView": true, "customConditional": "show = data.past_doxycycline_dosage && data.past_doxycycline_dosage === 'other';"}, {"key": "past_doxycycline_tolerability", "type": "radio", "input": true, "label": "How well did you tolerate Doxycycline?", "values": [{"label": "Very well (no side effects)", "value": "very_well"}, {"label": "Moderately well (mild side effects)", "value": "moderately_well"}, {"label": "Poorly (moderate side effects)", "value": "poorly"}, {"label": "Very poorly (severe side effects)", "value": "very_poorly"}], "tableView": true, "customConditional": "show = data.past_oral_antibiotic_types && data.past_oral_antibiotic_types.doxycycline;"}, {"key": "past_doxycycline_improvement", "type": "radio", "input": true, "label": "Did you notice an improvement in your skin while taking Doxycycline?", "values": [{"label": "Significant improvement", "value": "significant_improvement"}, {"label": "Moderate improvement", "value": "moderate_improvement"}, {"label": "Slight improvement", "value": "slight_improvement"}, {"label": "No improvement", "value": "no_improvement"}, {"label": "Worsening", "value": "worsening"}, {"label": "Unsure", "value": "unsure"}], "tableView": true, "customConditional": "show = data.past_oral_antibiotic_types && data.past_oral_antibiotic_types.doxycycline;"}, {"key": "past_heading_minocycline", "html": "<h4><strong>Minocycline</strong></h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.past_oral_antibiotic_types && data.past_oral_antibiotic_types.minocycline;"}, {"key": "past_minocycline_duration", "type": "select", "input": true, "widget": "html5", "label": "How long did you take Minocycline?", "data": {"values": [{"label": "Less than 1 month", "value": "less_than_1_month"}, {"label": "1-3 months", "value": "1_3_months"}, {"label": "3-6 months", "value": "3_6_months"}, {"label": "6-12 months", "value": "6_12_months"}, {"label": "1-2 years", "value": "1_2_years"}, {"label": "More than 2 years", "value": "more_than_2_years"}]}, "tableView": true, "customConditional": "show = data.past_oral_antibiotic_types && data.past_oral_antibiotic_types.minocycline;"}, {"key": "past_minocycline_dosage", "type": "select", "input": true, "widget": "html5", "label": "What dosage of Minocycline were you prescribed?", "data": {"values": [{"label": "50 mg once daily", "value": "50mg_once_daily"}, {"label": "50 mg twice daily", "value": "50mg_twice_daily"}, {"label": "100 mg once daily", "value": "100mg_once_daily"}, {"label": "100 mg twice daily", "value": "100mg_twice_daily"}, {"label": "Other", "value": "other"}]}, "tableView": true, "customConditional": "show = data.past_oral_antibiotic_types && data.past_oral_antibiotic_types.minocycline;"}, {"key": "past_minocycline_dosage_other", "type": "textfield", "input": true, "label": "Please specify your dosage:", "tableView": true, "customConditional": "show = data.past_minocycline_dosage && data.past_minocycline_dosage === 'other';"}, {"key": "past_minocycline_tolerability", "type": "radio", "input": true, "label": "How well did you tolerate Minocycline?", "values": [{"label": "Very well (no side effects)", "value": "very_well"}, {"label": "Moderately well (mild side effects)", "value": "moderately_well"}, {"label": "Poorly (moderate side effects)", "value": "poorly"}, {"label": "Very poorly (severe side effects)", "value": "very_poorly"}], "tableView": true, "customConditional": "show = data.past_oral_antibiotic_types && data.past_oral_antibiotic_types.minocycline;"}, {"key": "past_minocycline_improvement", "type": "radio", "input": true, "label": "Did you notice an improvement in your skin while taking Minocycline?", "values": [{"label": "Significant improvement", "value": "significant_improvement"}, {"label": "Moderate improvement", "value": "moderate_improvement"}, {"label": "Slight improvement", "value": "slight_improvement"}, {"label": "No improvement", "value": "no_improvement"}, {"label": "Worsening", "value": "worsening"}, {"label": "Unsure", "value": "unsure"}], "tableView": true, "customConditional": "show = data.past_oral_antibiotic_types && data.past_oral_antibiotic_types.minocycline;"}, {"key": "past_heading_tetracycline", "html": "<h4><strong>Tetracycline</strong></h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.past_oral_antibiotic_types && data.past_oral_antibiotic_types.tetracycline;"}, {"key": "past_tetracycline_dosage", "type": "select", "input": true, "widget": "html5", "label": "What dosage of Tetracycline were you prescribed?", "data": {"values": [{"label": "250 mg once daily", "value": "250mg_once_daily"}, {"label": "250 mg twice daily", "value": "250mg_twice_daily"}, {"label": "500 mg once daily", "value": "500mg_once_daily"}, {"label": "500 mg twice daily", "value": "500mg_twice_daily"}, {"label": "Other", "value": "other"}]}, "tableView": true, "customConditional": "show = data.past_oral_antibiotic_types && data.past_oral_antibiotic_types.tetracycline;"}, {"key": "past_tetracycline_dosage_other", "type": "textfield", "input": true, "label": "Please specify your dosage:", "tableView": true, "customConditional": "show = data.past_tetracycline_dosage && data.past_tetracycline_dosage === 'other';"}, {"key": "past_tetracycline_duration", "type": "select", "input": true, "widget": "html5", "label": "How long did you take Tetracycline?", "data": {"values": [{"label": "Less than 1 month", "value": "less_than_1_month"}, {"label": "1-3 months", "value": "1_3_months"}, {"label": "3-6 months", "value": "3_6_months"}, {"label": "6-12 months", "value": "6_12_months"}, {"label": "1-2 years", "value": "1_2_years"}, {"label": "More than 2 years", "value": "more_than_2_years"}]}, "tableView": true, "customConditional": "show = data.past_oral_antibiotic_types && data.past_oral_antibiotic_types.tetracycline;"}, {"key": "past_heading_past_skin_lightening_agents", "html": "<h3><strong>Skin Lightening Agents</strong></h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.past_medication_classes && data.past_medication_classes.past_skin_lightening_agents;"}, {"key": "past_skin_lightening_agents", "type": "selectboxes", "input": true, "label": "Which skin lightening agents have you used in the past? (Select all that apply)", "values": [{"label": "Hydroquinone", "value": "hydroquinone"}, {"label": "Triple Cream", "value": "triple_cream"}, {"label": "Azelaic Acid", "value": "azelaic_acid"}, {"label": "Kojic Acid", "value": "kojic_acid"}, {"label": "Niacinamide", "value": "niacinamide"}, {"label": "Vitamin C", "value": "vitamin_c"}, {"label": "Other", "value": "other"}, {"label": "None of the above", "value": "none_of_the_above"}], "tableView": true, "customConditional": "show = data.past_medication_classes && data.past_medication_classes.past_skin_lightening_agents;", "optionsLabelPosition": "right"}, {"key": "past_heading_hydroquinone", "html": "<h4>Hydroquinone</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.past_skin_lightening_agents && data.past_skin_lightening_agents.hydroquinone;"}, {"key": "past_hydroquinone_strength", "type": "radio", "input": true, "label": "What strength of Hydroquinone did you use?", "values": [{"label": "2%", "value": "2_percent"}, {"label": "4%", "value": "4_percent"}, {"label": "Other", "value": "other"}], "tableView": true, "customConditional": "show = data.past_skin_lightening_agents && data.past_skin_lightening_agents.hydroquinone;"}, {"key": "past_hydroquinone_frequency", "type": "select", "widget": "html5", "input": true, "label": "How often did you apply Hydroquinone?", "data": {"values": [{"label": "Once daily", "value": "once_daily"}, {"label": "Twice daily", "value": "twice_daily"}, {"label": "A few times a week", "value": "few_times_week"}, {"label": "Other", "value": "other"}]}, "tableView": true, "customConditional": "show = data.past_hydroquinone_strength;"}, {"key": "past_hydroquinone_duration", "type": "select", "widget": "html5", "input": true, "label": "How long did you apply Hydroquinone in the past?", "data": {"values": [{"label": "Less than 1 month", "value": "less_than_1_month"}, {"label": "1-3 months", "value": "1_3_months"}, {"label": "3-6 months", "value": "3_6_months"}, {"label": "6-12 months", "value": "6_12_months"}, {"label": "More than a year", "value": "more_than_a_year"}]}, "tableView": true, "customConditional": "show = data.past_hydroquinone_frequency;"}, {"key": "past_hydroquinone_application_area", "type": "textfield", "input": true, "label": "Which areas of your skin did you apply Hydroquinone?", "placeholder": "E.g., face, neck, hands", "tableView": true, "customConditional": "show = data.past_hydroquinone_duration;"}, {"key": "past_hydroquinone_tolerability", "type": "radio", "input": true, "label": "How well did you tolerate Hydroquinone?", "values": [{"label": "Very well (no side effects)", "value": "very_well"}, {"label": "Mild side effects", "value": "mild_side_effects"}, {"label": "Moderate side effects", "value": "moderate_side_effects"}, {"label": "Severe side effects", "value": "severe_side_effects"}], "tableView": true, "customConditional": "show = data.past_hydroquinone_application_area;"}, {"key": "past_hydroquinone_improvement", "type": "radio", "input": true, "label": "Have you noticed an improvement in your skin since using Hydroquinone?", "values": [{"label": "Significant improvement", "value": "significant_improvement"}, {"label": "Moderate improvement", "value": "moderate_improvement"}, {"label": "Slight improvement", "value": "slight_improvement"}, {"label": "No improvement", "value": "no_improvement"}, {"label": "Condition worsened", "value": "condition_worsened"}], "tableView": true, "customConditional": "show = data.past_hydroquinone_tolerability;"}, {"key": "past_heading_triple_cream", "html": "<h4>Triple Cream</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.past_skin_lightening_agents && data.past_skin_lightening_agents.triple_cream;"}, {"key": "past_triple_cream_strength", "type": "radio", "input": true, "label": "Which Triple Cream formulation did you use?", "values": [{"label": "Kligman Formula (2% Hydroquinone, 0.025% Tretinoin, 0.01% Dexamethasone)", "value": "kligman_formula"}, {"label": "Tri-Luma (4% Hydroquinone, 0.05% Tretinoin, 0.01% Fluocinolone)", "value": "tri_luma"}, {"label": "Custom formulation", "value": "custom_strength"}], "tableView": true, "customConditional": "show = data.past_skin_lightening_agents && data.past_skin_lightening_agents.triple_cream;"}, {"key": "past_triple_cream_custom_strength_details", "type": "textfield", "input": true, "label": "Please specify the custom formulation details:", "tableView": true, "customConditional": "show = data.past_skin_lightening_agents && data.past_skin_lightening_agents.triple_cream && data.past_triple_cream_strength === 'custom_strength';"}, {"key": "past_triple_cream_frequency", "type": "radio", "input": true, "label": "How often did you apply Triple Cream?", "values": [{"label": "Once daily", "value": "once_daily"}, {"label": "Twice daily", "value": "twice_daily"}, {"label": "A few times a week", "value": "few_times_week"}, {"label": "Other", "value": "other"}], "tableView": true, "customConditional": "show = data.past_skin_lightening_agents && data.past_skin_lightening_agents.triple_cream && data.past_triple_cream_strength;"}, {"key": "past_triple_cream_frequency_other_details", "type": "textfield", "input": true, "label": "Please specify the frequency details:", "tableView": true, "customConditional": "show = data.past_skin_lightening_agents && data.past_skin_lightening_agents.triple_cream && data.past_triple_cream_frequency === 'other';"}, {"key": "past_triple_cream_total_use_duration", "type": "select", "widget": "html5", "input": true, "label": "How long have you used Triple Cream in total over your lifetime?<br><br>• <strong>For example,</strong> if you used the cream for 3 months two years ago, and another 3 months this year, this would add up to a total of 6 months of use.<br>• Please select the option that best represents this total time.</br>", "data": {"values": [{"label": "Less than 4 weeks", "value": "less_than_4_weeks"}, {"label": "4-8 weeks", "value": "4_8_weeks"}, {"label": "8-12 weeks", "value": "8_12_weeks"}, {"label": "12-16 weeks", "value": "12_16_weeks"}, {"label": "16-20 weeks", "value": "16_20_weeks"}, {"label": "20-24 weeks", "value": "20_24_weeks"}, {"label": "6-9 months", "value": "6_9_months"}, {"label": "9-12 months", "value": "9_12_months"}, {"label": "1-1.5 years", "value": "1_1.5_years"}, {"label": "1.5-2 years", "value": "1.5_2_years"}, {"label": "More than 2 years", "value": "more_than_2_years"}]}, "tableView": true, "customConditional": "show = data.past_skin_lightening_agents && data.past_skin_lightening_agents.triple_cream && data.past_triple_cream_strength && data.past_triple_cream_frequency;"}, {"key": "past_triple_cream_improvement", "type": "radio", "input": true, "label": "Have you noticed an improvement in your skin since using Triple Cream?", "values": [{"label": "Significant improvement", "value": "significant_improvement"}, {"label": "Moderate improvement", "value": "moderate_improvement"}, {"label": "Slight improvement", "value": "slight_improvement"}, {"label": "No change", "value": "no_change"}, {"label": "Worsened condition", "value": "worsened_condition"}, {"label": "Not applicable", "value": "not_applicable"}], "tableView": true, "customConditional": "show = data.past_skin_lightening_agents && data.past_skin_lightening_agents.triple_cream && data.past_triple_cream_strength && data.past_triple_cream_frequency && data.past_triple_cream_total_use_duration;"}, {"key": "past_heading_azelaic_acid", "html": "<h4>Azelaic Acid</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.past_skin_lightening_agents && data.past_skin_lightening_agents.azelaic_acid;"}, {"key": "past_azelaic_acid_strength", "type": "radio", "input": true, "label": "What strength of Azelaic Acid did you use?", "values": [{"label": "10%", "value": "10_percent"}, {"label": "15%", "value": "15_percent"}, {"label": "20%", "value": "20_percent"}, {"label": "Other", "value": "other"}], "tableView": true, "customConditional": "show = data.past_skin_lightening_agents && data.past_skin_lightening_agents.azelaic_acid;"}, {"key": "past_azelaic_acid_custom_strength", "type": "textfield", "input": true, "label": "Please specify the custom strength of Azelaic Acid", "tableView": true, "customConditional": "show = data.past_azelaic_acid_strength === 'other';"}, {"key": "past_azelaic_acid_frequency", "type": "radio", "input": true, "label": "How often did you apply Azelaic Acid?", "values": [{"label": "Once daily", "value": "once_daily"}, {"label": "Twice daily", "value": "twice_daily"}, {"label": "A few times a week", "value": "few_times_week"}, {"label": "Other", "value": "other"}], "tableView": true, "customConditional": "show = data.past_azelaic_acid_strength && data.past_azelaic_acid_strength !== 'other';"}, {"key": "past_azelaic_acid_custom_frequency", "type": "textfield", "input": true, "label": "Please specify your custom application frequency", "tableView": true, "customConditional": "show = data.past_azelaic_acid_frequency === 'other';"}, {"key": "past_azelaic_acid_duration", "type": "select", "input": true, "widget": "html5", "label": "How long have you used Azelaic Acid in total over your lifetime? <br>• Example: If you used it for 3 months two years ago, and another 3 months this year, this would total 6 months of use. <br>• Select the option that best represents this total time.", "data": {"values": [{"label": "Less than 4 weeks", "value": "less_than_4_weeks"}, {"label": "4-8 weeks", "value": "4_8_weeks"}, {"label": "8-12 weeks", "value": "8_12_weeks"}, {"label": "12-16 weeks", "value": "12_16_weeks"}, {"label": "16-20 weeks", "value": "16_20_weeks"}, {"label": "20-24 weeks", "value": "20_24_weeks"}, {"label": "6-9 months", "value": "6_9_months"}, {"label": "9-12 months", "value": "9_12_months"}, {"label": "1-1.5 years", "value": "1_1.5_years"}, {"label": "1.5-2 years", "value": "1.5_2_years"}, {"label": "More than 2 years", "value": "more_than_2_years"}]}, "tableView": true, "customConditional": "show = data.past_azelaic_acid_frequency && data.past_azelaic_acid_frequency !== 'other';"}, {"key": "past_azelaic_acid_improvement", "type": "radio", "input": true, "label": "Did you notice an improvement in your skin while using Azelaic Acid?", "values": [{"label": "Significant improvement", "value": "significant_improvement"}, {"label": "Moderate improvement", "value": "moderate_improvement"}, {"label": "Slight improvement", "value": "slight_improvement"}, {"label": "No change", "value": "no_change"}, {"label": "Worsened condition", "value": "worsened_condition"}, {"label": "Not applicable", "value": "not_applicable"}], "tableView": true, "customConditional": "show = data.past_azelaic_acid_duration && data.past_azelaic_acid_duration !== 'never';"}, {"key": "past_heading_kojic_acid", "html": "<h4>Kojic Acid</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.past_skin_lightening_agents && data.past_skin_lightening_agents.kojic_acid;"}, {"key": "past_kojic_acid_frequency", "data": {"values": [{"label": "Once daily", "value": "once_daily"}, {"label": "Twice daily", "value": "twice_daily"}]}, "type": "select", "widget": "html5", "input": true, "label": "How often did you apply Kojic Acid?", "tableView": true, "customConditional": "show = data.past_skin_lightening_agents && data.past_skin_lightening_agents.kojic_acid;"}, {"key": "past_heading_niacinamide", "html": "<h4>Niacinamide</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.past_skin_lightening_agents && data.past_skin_lightening_agents.niacinamide;"}, {"key": "past_niacinamide_frequency", "data": {"values": [{"label": "Once daily", "value": "once_daily"}, {"label": "Twice daily", "value": "twice_daily"}]}, "type": "select", "widget": "html5", "input": true, "label": "How often did you apply Niacinamide?", "tableView": true, "customConditional": "show = data.past_skin_lightening_agents && data.past_skin_lightening_agents.niacinamide;"}, {"key": "past_heading_vitamin_c", "html": "<h4>Vitamin C</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.past_skin_lightening_agents && data.past_skin_lightening_agents.vitamin_c;"}, {"key": "past_vitamin_c_frequency", "data": {"values": [{"label": "Once daily", "value": "once_daily"}, {"label": "Twice daily", "value": "twice_daily"}]}, "type": "select", "widget": "html5", "input": true, "label": "How often did you apply Vitamin C?", "tableView": true, "customConditional": "show = data.past_skin_lightening_agents && data.past_skin_lightening_agents.vitamin_c;"}, {"key": "past_heading_procedures", "html": "<h2><strong>Procedures</strong></h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.doctor_prescribed_treatments === 'yes' || data.doctor_prescribed_treatments === 'no';"}, {"key": "past_recent_procedures", "type": "radio", "input": true, "label": "Did you complete cosmetic procedures (including chemical peels, botox, fillers, microneedling, lasers) in the past?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.doctor_prescribed_treatments === 'yes' || data.doctor_prescribed_treatments === 'no';", "optionsLabelPosition": "right"}, {"key": "details_past_procedures", "type": "selectboxes", "input": true, "label": "Please specify the cosmetic procedures you completed (Select all that apply):", "values": [{"label": "Chemical Peels", "value": "past_chemical_peels"}, {"label": "Botox", "value": "botox"}, {"label": "Fillers", "value": "fillers"}, {"label": "Microneedling", "value": "microneedling"}, {"label": "Lasers", "value": "lasers"}], "tableView": true, "customConditional": "show = data.past_recent_procedures === 'yes';", "optionsLabelPosition": "right"}, {"key": "past_heading_past_chemical_peels", "html": "<h3><strong>Chemical Peels</strong></h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.details_past_procedures && data.details_past_procedures.past_chemical_peels;"}, {"key": "past_peel_type", "type": "selectboxes", "input": true, "label": "Which type of peels did you use?", "values": [{"label": "At-home peels", "value": "at_home"}, {"label": "Professionally applied peels", "value": "professional"}, {"label": "None of the above", "value": "none"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.details_past_procedures && data.details_past_procedures.past_chemical_peels;"}, {"key": "past_heading_at_home_peels", "html": "<h4><strong>At-Home Peels</strong></h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.details_past_procedures && data.details_past_procedures.past_chemical_peels && data.past_peel_type && data.past_peel_type.at_home && !data.past_peel_type.none;"}, {"key": "past_peel_frequency_at_home", "data": {"values": [{"label": "Nightly", "value": "nightly"}, {"label": "Every few days", "value": "few_days"}, {"label": "Once a week", "value": "weekly"}, {"label": "Every 2-3 weeks", "value": "bi_to_triweekly"}, {"label": "Once a month", "value": "monthly"}, {"label": "Occasionally", "value": "occasionally"}, {"label": "Never", "value": "never"}]}, "type": "select", "input": true, "label": "How often did you use <strong>at-home</strong> chemical peels in the past?", "widget": "html5", "tableView": true, "customConditional": "show = data.details_past_procedures && data.details_past_procedures.past_chemical_peels && data.past_peel_type.at_home && !data.past_peel_type.none;"}, {"key": "past_at_home_peel_duration", "data": {"values": [{"label": "Less than 1 month", "value": "less_than_1_month"}, {"label": "1-3 months", "value": "1_to_3_months"}, {"label": "3-6 months", "value": "3_to_6_months"}, {"label": "6-12 months", "value": "6_to_12_months"}, {"label": "1-2 years", "value": "1_to_2_years"}, {"label": "More than 2 years", "value": "more_than_2_years"}]}, "type": "select", "input": true, "label": "How long did you incorporate at-home chemical peels into your routine?", "widget": "html5", "tableView": true, "customConditional": "show = data.details_past_procedures && data.details_past_procedures.past_chemical_peels && data.past_peel_type && data.past_peel_type.at_home && data.past_peel_frequency_at_home !== 'never';"}, {"key": "past_at_home_past_peel_types", "type": "selectboxes", "input": true, "label": "What types of at-home peels did you use? (Select all that apply)", "values": [{"label": "Glycolic Acid", "value": "glycolic_acid"}, {"label": "Salicylic Acid", "value": "salicylic_acid"}, {"label": "Lactic Acid", "value": "lactic_acid"}, {"label": "Man<PERSON>ic <PERSON>", "value": "mandelic_acid"}, {"label": "TCA (Trichloroacetic Acid)", "value": "tca"}, {"label": "Enzyme Peels", "value": "enzyme_peels"}, {"label": "Retinol Peels", "value": "retinol_peels"}, {"label": "None of the above", "value": "none_of_the_above"}], "tableView": true, "customConditional": "show = data.details_past_procedures && data.details_past_procedures.past_chemical_peels && data.past_peel_type.at_home && !data.past_peel_type.none && data.past_peel_frequency_at_home !== 'never';"}, {"key": "past_strength_glycolic_acid", "type": "radio", "input": true, "label": "What strength of Glycolic Acid did you use?", "values": [{"label": "Less than 10%", "value": "less_than_10"}, {"label": "10-20%", "value": "10_to_20"}, {"label": "20-30%", "value": "20_to_30"}, {"label": "Above 30%", "value": "above_30"}, {"label": "Not sure", "value": "not_sure"}], "tableView": true, "customConditional": "show = data.past_at_home_past_peel_types && data.past_at_home_past_peel_types.glycolic_acid;"}, {"key": "past_strength_salicylic_acid", "type": "radio", "input": true, "label": "What strength of Salicylic Acid did you use?", "values": [{"label": "Less than 1%", "value": "less_than_1"}, {"label": "1-2%", "value": "1_to_2"}, {"label": "Above 2%", "value": "above_2"}], "tableView": true, "customConditional": "show = data.past_at_home_past_peel_types && data.past_at_home_past_peel_types.salicylic_acid;"}, {"key": "past_strength_lactic_acid", "type": "radio", "input": true, "label": "What strength of Lactic Acid did you use?", "values": [{"label": "Less than 5%", "value": "less_than_5"}, {"label": "5-10%", "value": "5_to_10"}, {"label": "10-20%", "value": "10_to_20"}, {"label": "Above 20%", "value": "above_20"}, {"label": "Not sure", "value": "not_sure"}], "tableView": true, "customConditional": "show = data.past_at_home_past_peel_types && data.past_at_home_past_peel_types.lactic_acid;"}, {"key": "past_strength_mandelic_acid", "type": "radio", "input": true, "label": "What strength of Mandelic Acid did you use?", "values": [{"label": "Less than 5%", "value": "less_than_5"}, {"label": "5-10%", "value": "5_to_10"}, {"label": "10-20%", "value": "10_to_20"}, {"label": "Above 20%", "value": "above_20"}, {"label": "Not sure", "value": "not_sure"}], "tableView": true, "customConditional": "show = data.past_at_home_past_peel_types && data.past_at_home_past_peel_types.mandelic_acid;"}, {"key": "past_strength_tca", "type": "radio", "input": true, "label": "What strength of TCA (Trichloroacetic Acid) did you use?", "values": [{"label": "Less than 10%", "value": "less_than_10"}, {"label": "10-20%", "value": "10_to_20"}, {"label": "20-30%", "value": "20_to_30"}, {"label": "Above 30%", "value": "above_30"}, {"label": "Not sure", "value": "not_sure"}], "tableView": true, "customConditional": "show = data.past_at_home_past_peel_types && data.past_at_home_past_peel_types.tca;"}, {"key": "past_strength_enzyme_peels", "type": "radio", "input": true, "label": "What type of Enzyme Peels did you use?", "values": [{"label": "<PERSON><PERSON>", "value": "papaya"}, {"label": "Pumpkin Enzyme", "value": "pumpkin"}, {"label": "Pineapple Enzyme", "value": "pineapple"}, {"label": "Other Enzyme", "value": "other"}], "tableView": true, "customConditional": "show = data.past_at_home_past_peel_types && data.past_at_home_past_peel_types.enzyme_peels;"}, {"key": "past_strength_retinol_peels", "type": "radio", "input": true, "label": "What strength of Retinol Peel did you use?", "values": [{"label": "Less than 0.5%", "value": "less_than_0_5"}, {"label": "0.5-1%", "value": "0_5_to_1"}, {"label": "Above 1%", "value": "above_1"}, {"label": "Not sure", "value": "not_sure"}], "tableView": true, "customConditional": "show = data.past_at_home_past_peel_types && data.past_at_home_past_peel_types.retinol_peels;"}, {"key": "past_improvement_at_home_peels", "type": "radio", "input": true, "label": "Did you notice an improvement in your skin since incorporating at-home chemical peels into your routine?", "values": [{"label": "Significant improvement", "value": "significant_improvement"}, {"label": "Moderate improvement", "value": "moderate_improvement"}, {"label": "Slight improvement", "value": "slight_improvement"}, {"label": "No change", "value": "no_change"}, {"label": "It's worse than before", "value": "worsening"}, {"label": "Not sure", "value": "not_sure"}], "tableView": true, "customConditional": "show = data.details_past_procedures && data.details_past_procedures.past_chemical_peels && data.past_peel_type && data.past_peel_type.at_home && data.past_peel_frequency_at_home !== 'never';"}, {"key": "past_heading_professional_peels", "html": "<h4><strong>Professional Peels</strong></h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.details_past_procedures && data.details_past_procedures.past_chemical_peels && data.past_peel_type && data.past_peel_type.professional && !data.past_peel_type.none;"}, {"key": "past_peel_frequency_professional", "data": {"values": [{"label": "Once a week", "value": "weekly"}, {"label": "Every 2-3 weeks", "value": "bi_to_triweekly"}, {"label": "Once a month", "value": "monthly"}, {"label": "Every 1-2 months", "value": "one_to_two_months"}, {"label": "Every 3-4 months", "value": "three_to_four_months"}, {"label": "Every 5-6 months", "value": "five_to_six_months"}, {"label": "Every 6-12 months", "value": "six_to_twelve_months"}, {"label": "Once a year", "value": "yearly"}, {"label": "Occasionally (less than once a year)", "value": "occasionally"}, {"label": "Never", "value": "never"}]}, "type": "select", "input": true, "label": "How often did you receive <strong>professionally</strong> applied chemical peels?", "widget": "html5", "tableView": true, "customConditional": "show = data.details_past_procedures && data.details_past_procedures.past_chemical_peels && data.past_peel_type.professional && !data.past_peel_type.none;"}, {"key": "past_professional_peel_duration", "data": {"values": [{"label": "Less than 1 month", "value": "less_than_1_month"}, {"label": "1-3 months", "value": "1_to_3_months"}, {"label": "3-6 months", "value": "3_to_6_months"}, {"label": "6-12 months", "value": "6_to_12_months"}, {"label": "1-2 years", "value": "1_to_2_years"}, {"label": "More than 2 years", "value": "more_than_2_years"}]}, "type": "select", "input": true, "label": "How long have you incorporated professionally applied chemical peels into your routine?", "widget": "html5", "tableView": true, "customConditional": "show = data.details_past_procedures && data.details_past_procedures.past_chemical_peels && data.past_peel_type && data.past_peel_type.professional && data.past_peel_frequency_professional !== 'never';"}, {"key": "past_professional_past_peel_types", "type": "selectboxes", "input": true, "label": "What types of professionally applied peels did you receive? (Select all that apply)", "values": [{"label": "Glycolic Acid Peel", "value": "glycolic_acid_peel"}, {"label": "Salicylic Acid Peel", "value": "salicylic_acid_peel"}, {"label": "<PERSON>tic <PERSON>", "value": "lactic_acid_peel"}, {"label": "TCA (Trichloroacetic Acid) Peel", "value": "tca_peel"}, {"label": "<PERSON><PERSON>'s Peel", "value": "jess<PERSON>_peel"}, {"label": "None of the above", "value": "none_of_the_above"}], "tableView": true, "customConditional": "show = data.details_past_procedures && data.details_past_procedures.past_chemical_peels && data.past_peel_type.professional && !data.past_peel_type.none && data.past_peel_frequency_professional !== 'never';"}, {"key": "past_strength_glycolic_acid_peel", "type": "radio", "input": true, "label": "What strength of Glycolic Acid Peel did you receive?", "values": [{"label": "Less than 20%", "value": "less_than_20"}, {"label": "20-30%", "value": "20_to_30"}, {"label": "30-50%", "value": "30_to_50"}, {"label": "Above 50%", "value": "above_50"}, {"label": "Not sure", "value": "not_sure"}], "tableView": true, "customConditional": "show = data.past_professional_past_peel_types && data.past_professional_past_peel_types.glycolic_acid_peel;"}, {"key": "past_strength_salicylic_acid_peel", "type": "radio", "input": true, "label": "What strength of Salicylic Acid Peel did you receive?", "values": [{"label": "Less than 10%", "value": "less_than_10"}, {"label": "10-20%", "value": "10_to_20"}, {"label": "Above 20%", "value": "above_20"}, {"label": "Not sure", "value": "not_sure"}], "tableView": true, "customConditional": "show = data.past_professional_past_peel_types && data.past_professional_past_peel_types.salicylic_acid_peel;"}, {"key": "past_strength_lactic_acid_peel", "type": "radio", "input": true, "label": "What strength of Lactic Acid Peel did you receive?", "values": [{"label": "Less than 20%", "value": "less_than_20"}, {"label": "20-40%", "value": "20_to_40"}, {"label": "Above 40%", "value": "above_40"}, {"label": "Not sure", "value": "not_sure"}], "tableView": true, "customConditional": "show = data.past_professional_past_peel_types && data.past_professional_past_peel_types.lactic_acid_peel;"}, {"key": "past_strength_tca_peel", "type": "radio", "input": true, "label": "What strength of TCA (Trichloroacetic Acid) Peel did you receive?", "values": [{"label": "Less than 10%", "value": "less_than_10"}, {"label": "10-20%", "value": "10_to_20"}, {"label": "20-30%", "value": "20_to_30"}, {"label": "Above 30%", "value": "above_30"}, {"label": "Not sure", "value": "not_sure"}], "tableView": true, "customConditional": "show = data.past_professional_past_peel_types && data.past_professional_past_peel_types.tca_peel;"}, {"key": "past_strength_jessners_peel", "type": "radio", "input": true, "label": "What strength of <PERSON><PERSON>'s Peel did you receive?", "values": [{"label": "Standard Strength", "value": "standard_strength"}, {"label": "Modified (with lower concentration acids)", "value": "modified_strength"}, {"label": "Not sure", "value": "not_sure"}], "tableView": true, "customConditional": "show = data.past_professional_past_peel_types && data.past_professional_past_peel_types.jessners_peel;"}, {"key": "past_improvement_professional_peels", "type": "radio", "input": true, "label": "Have you noticed an improvement in your skin since incorporating professionally applied chemical peels into your routine?", "values": [{"label": "Significant improvement", "value": "significant_improvement"}, {"label": "Moderate improvement", "value": "moderate_improvement"}, {"label": "Slight improvement", "value": "slight_improvement"}, {"label": "No change", "value": "no_change"}, {"label": "It's worse than before", "value": "worsening"}, {"label": "Not sure", "value": "not_sure"}], "tableView": true, "customConditional": "show = data.details_past_procedures && data.details_past_procedures.past_chemical_peels && data.past_peel_type && data.past_peel_type.professional && data.past_peel_frequency_professional !== 'never';"}, {"key": "past_heading_botox", "html": "<h3><strong>Botox</strong></h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.details_past_procedures && data.details_past_procedures.botox;"}, {"key": "past_botox_frequency", "data": {"values": [{"label": "Every 3 months", "value": "every_3_months"}, {"label": "Every 4-6 months", "value": "every_4_6_months"}, {"label": "Every 6-12 months", "value": "every_6_12_months"}, {"label": "Once a year", "value": "yearly"}, {"label": "Occasionally (less than once a year)", "value": "occasionally"}, {"label": "Never", "value": "never"}]}, "type": "select", "input": true, "label": "How often did you receive Botox injections?", "widget": "html5", "tableView": true, "customConditional": "show = data.details_past_procedures && data.details_past_procedures.botox;"}, {"key": "past_botox_duration", "data": {"values": [{"label": "Less than 6 months", "value": "less_than_6_months"}, {"label": "6-12 months", "value": "6_12_months"}, {"label": "1-2 years", "value": "1_2_years"}, {"label": "2-5 years", "value": "2_5_years"}, {"label": "More than 5 years", "value": "more_than_5_years"}]}, "type": "select", "input": true, "label": "How long have you been receiving Botox injections?", "widget": "html5", "tableView": true, "customConditional": "show = data.past_botox_frequency"}, {"key": "botox_areas_treated", "type": "selectboxes", "input": true, "label": "Which areas did you typically treat with Botox? (Select all that apply)", "values": [{"label": "Forehead", "value": "forehead"}, {"label": "Glabella (between eyebrows)", "value": "glabella"}, {"label": "Crow's feet (around eyes)", "value": "crows_feet"}, {"label": "Jawline", "value": "jawline"}, {"label": "Neck", "value": "neck"}, {"label": "Other areas", "value": "other_areas"}], "tableView": true, "customConditional": "show = data.past_botox_duration"}, {"key": "past_botox_areas_other", "type": "textfield", "input": true, "label": "If other areas, please specify:", "tableView": true, "customConditional": "show = data.botox_areas_treated && data.botox_areas_treated.other_areas;"}, {"key": "past_botox_side_effects", "type": "selectboxes", "input": true, "label": "Have you experienced any side effects from Botox? (Select all that apply)", "values": [{"label": "Bruising", "value": "bruising"}, {"label": "Headaches", "value": "headaches"}, {"label": "Drooping eyelids", "value": "drooping_eyelids"}, {"label": "Muscle weakness", "value": "muscle_weakness"}, {"label": "None", "value": "none"}], "tableView": true, "customConditional": "show = data.botox_areas_treated"}, {"key": "past_botox_improvement", "type": "radio", "input": true, "label": "Have you noticed an improvement in your appearance since starting Botox treatments?", "values": [{"label": "Yes, significant improvement", "value": "significant_improvement"}, {"label": "Yes, some improvement", "value": "some_improvement"}, {"label": "No noticeable improvement", "value": "no_improvement"}, {"label": "Yes, but with some areas I would like improved further", "value": "partial_improvement"}, {"label": "No, I'm dissatisfied", "value": "dissatisfied"}, {"label": "Not applicable", "value": "not_applicable"}], "tableView": true, "customConditional": "show = data.past_botox_side_effects"}, {"key": "past_heading_fillers", "html": "<h3><strong>Fillers</strong></h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.details_past_procedures && data.details_past_procedures.fillers;"}, {"key": "past_filler_frequency", "data": {"values": [{"label": "Every 3 months", "value": "every_3_months"}, {"label": "Every 4-6 months", "value": "every_4_6_months"}, {"label": "Every 6-12 months", "value": "every_6_12_months"}, {"label": "Once a year", "value": "yearly"}, {"label": "Occasionally (less than once a year)", "value": "occasionally"}, {"label": "Never", "value": "never"}]}, "type": "select", "input": true, "label": "How often did you receive filler injections?", "widget": "html5", "tableView": true, "customConditional": "show = data.details_past_procedures && data.details_past_procedures.fillers;"}, {"key": "past_filler_duration", "data": {"values": [{"label": "Less than 6 months", "value": "less_than_6_months"}, {"label": "6-12 months", "value": "6_12_months"}, {"label": "1-2 years", "value": "1_2_years"}, {"label": "2-5 years", "value": "2_5_years"}, {"label": "More than 5 years", "value": "more_than_5_years"}]}, "type": "select", "input": true, "label": "How long have you been receiving filler injections?", "widget": "html5", "tableView": true, "customConditional": "show = data.past_filler_frequency"}, {"key": "past_filler_areas_treated", "type": "selectboxes", "input": true, "label": "Which areas did you typically treat with fillers? (Select all that apply)", "values": [{"label": "Cheeks", "value": "cheeks"}, {"label": "Lips", "value": "lips"}, {"label": "Nasolabial folds", "value": "nasolabial_folds"}, {"label": "Under-eye area", "value": "under_eye"}, {"label": "Jawline", "value": "jawline"}, {"label": "<PERSON>", "value": "chin"}, {"label": "Other areas", "value": "other_areas"}], "tableView": true, "customConditional": "show = data.past_filler_duration"}, {"key": "past_filler_areas_other", "type": "textfield", "input": true, "label": "If other areas, please specify:", "tableView": true, "customConditional": "show = data.past_filler_areas_treated && data.past_filler_areas_treated.other_areas;"}, {"key": "past_filler_types", "type": "selectboxes", "input": true, "label": "Which types of fillers have you used? (Select all that apply)", "values": [{"label": "Hyaluronic Acid (e.g., Juvederm, Restylane)", "value": "hyaluronic_acid"}, {"label": "Calcium Hydroxylapatite (e.g., Radiesse)", "value": "calcium_hydroxylapatite"}, {"label": "Poly-L-lactic Acid (e.g., Sculptra)", "value": "poly_l_lactic_acid"}, {"label": "Polymethylmethacrylate (PMMA)", "value": "pmma"}, {"label": "Fat Grafting", "value": "fat_grafting"}, {"label": "Other", "value": "other"}], "tableView": true, "customConditional": "show = data.past_filler_areas_treated"}, {"key": "past_filler_types_other", "type": "textfield", "input": true, "label": "If other, please specify:", "tableView": true, "customConditional": "show = data.past_filler_types && data.past_filler_types.other;"}, {"key": "past_filler_side_effects", "type": "selectboxes", "input": true, "label": "Have you experienced any side effects from fillers? (Select all that apply)", "values": [{"label": "Bruising", "value": "bruising"}, {"label": "Swelling", "value": "swelling"}, {"label": "Lumps or bumps", "value": "lumps_bumps"}, {"label": "Discoloration", "value": "discoloration"}, {"label": "Pain or tenderness", "value": "pain_tenderness"}, {"label": "None", "value": "none"}], "tableView": true, "customConditional": "show = data.past_filler_types"}, {"key": "past_filler_improvement", "type": "radio", "input": true, "label": "Have you noticed an improvement in your appearance since starting filler treatments?", "values": [{"label": "Yes, significant improvement", "value": "significant_improvement"}, {"label": "Yes, some improvement", "value": "some_improvement"}, {"label": "No noticeable improvement", "value": "no_improvement"}, {"label": "Yes, but with some areas I would like improved further", "value": "partial_improvement"}, {"label": "No, I'm dissatisfied", "value": "dissatisfied"}, {"label": "Not applicable", "value": "not_applicable"}], "tableView": true, "customConditional": "show = data.past_filler_side_effects"}, {"key": "past_heading_microneedling", "html": "<h3><strong>Microneedling</strong></h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.details_past_procedures && data.details_past_procedures.microneedling;"}, {"key": "past_microneedling_frequency", "data": {"values": [{"label": "Once a month", "value": "monthly"}, {"label": "Every 2-3 months", "value": "every_2_3_months"}, {"label": "Every 4-6 months", "value": "every_4_6_months"}, {"label": "Once a year", "value": "yearly"}, {"label": "Occasionally", "value": "occasionally"}, {"label": "Never", "value": "never"}]}, "type": "select", "input": true, "label": "How often did you receive microneedling treatments?", "widget": "html5", "tableView": true, "customConditional": "show = data.details_past_procedures && data.details_past_procedures.microneedling;"}, {"key": "past_microneedling_duration", "data": {"values": [{"label": "Less than 6 months", "value": "less_than_6_months"}, {"label": "6-12 months", "value": "6_12_months"}, {"label": "1-2 years", "value": "1_2_years"}, {"label": "2-5 years", "value": "2_5_years"}, {"label": "More than 5 years", "value": "more_than_5_years"}]}, "type": "select", "input": true, "label": "How long have you been receiving microneedling treatments?", "widget": "html5", "tableView": true, "customConditional": "show = data.past_microneedling_frequency"}, {"key": "past_microneedling_type", "type": "selectboxes", "input": true, "label": "What type of microneedling treatments did you receive? (Select all that apply)", "values": [{"label": "Standard Microneedling", "value": "standard_microneedling"}, {"label": "Microneedling with PRP (Platelet-Rich Plasma)", "value": "microneedling_prp"}, {"label": "Radiofrequency Microneedling", "value": "radiofrequency_microneedling"}, {"label": "Other", "value": "other"}], "tableView": true, "customConditional": "show = data.past_microneedling_duration"}, {"key": "past_microneedling_type_other", "type": "textfield", "input": true, "label": "If other, please specify:", "tableView": true, "customConditional": "show = data.past_microneedling_type && data.past_microneedling_type.other;"}, {"key": "past_microneedling_side_effects", "type": "selectboxes", "input": true, "label": "Have you experienced any side effects from microneedling? (Select all that apply)", "values": [{"label": "Redness", "value": "redness"}, {"label": "Swelling", "value": "swelling"}, {"label": "Bruising", "value": "bruising"}, {"label": "Infection", "value": "infection"}, {"label": "Skin irritation", "value": "skin_irritation"}, {"label": "None", "value": "none"}], "tableView": true, "customConditional": "show = data.past_microneedling_type"}, {"key": "past_microneedling_improvement", "type": "radio", "input": true, "label": "Have you noticed an improvement in your skin since starting microneedling treatments?", "values": [{"label": "Yes, significant improvement", "value": "significant_improvement"}, {"label": "Yes, some improvement", "value": "some_improvement"}, {"label": "No noticeable improvement", "value": "no_improvement"}, {"label": "Yes, but with some areas needing further improvement", "value": "partial_improvement"}, {"label": "No, I'm dissatisfied", "value": "dissatisfied"}, {"label": "Not applicable", "value": "not_applicable"}], "tableView": true, "customConditional": "show = data.past_microneedling_side_effects"}, {"key": "past_past_heading_laser_treatments", "html": "<h3><strong>Laser Treatments</strong></h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.details_past_procedures && data.details_past_procedures.lasers;"}, {"key": "past_laser_frequency", "data": {"values": [{"label": "Every month", "value": "monthly"}, {"label": "Every 2-3 months", "value": "bi_to_tri_monthly"}, {"label": "Every 4-6 months", "value": "four_to_six_months"}, {"label": "Once a year", "value": "yearly"}, {"label": "Occasionally", "value": "occasionally"}, {"label": "Never", "value": "never"}]}, "type": "select", "input": true, "label": "How often did you receive laser treatments?", "widget": "html5", "tableView": true, "customConditional": "show = data.details_past_procedures && data.details_past_procedures.lasers;"}, {"key": "past_laser_duration", "data": {"values": [{"label": "Less than 6 months", "value": "less_than_6_months"}, {"label": "6-12 months", "value": "6_12_months"}, {"label": "1-2 years", "value": "1_2_years"}, {"label": "2-5 years", "value": "2_5_years"}, {"label": "More than 5 years", "value": "more_than_5_years"}]}, "type": "select", "input": true, "label": "How long have you been receiving laser treatments?", "widget": "html5", "tableView": true, "customConditional": "show = data.past_laser_frequency"}, {"key": "past_laser_types", "type": "selectboxes", "input": true, "label": "What types of laser treatments did you receive? (Select all that apply)", "values": [{"label": "Ablative Laser (e.g., CO2, Erbium)", "value": "ablative"}, {"label": "Non-Ablative Laser (e.g., Nd:YAG, Diode)", "value": "non_ablative"}, {"label": "IPL (Intense Pulsed Light)", "value": "ipl"}, {"label": "Fractional Laser", "value": "fractional"}, {"label": "<PERSON><PERSON><PERSON> (PDL)", "value": "pdl"}, {"label": "Q-Switched Laser", "value": "q_switched"}, {"label": "Other", "value": "other"}], "tableView": true, "customConditional": "show = data.past_laser_duration"}, {"key": "past_laser_type_other", "type": "textfield", "input": true, "label": "If other, please specify the type:", "tableView": true, "customConditional": "show = data.past_laser_types && data.past_laser_types.other;"}, {"key": "past_laser_side_effects", "type": "selectboxes", "input": true, "label": "Have you experienced any side effects from laser treatments? (Select all that apply)", "values": [{"label": "Redness", "value": "redness"}, {"label": "Swelling", "value": "swelling"}, {"label": "Blisters", "value": "blisters"}, {"label": "Hyperpigmentation", "value": "hyperpigmentation"}, {"label": "Hypopigmentation", "value": "hypopigmentation"}, {"label": "<PERSON>arring", "value": "scarring"}, {"label": "Skin irritation", "value": "skin_irritation"}, {"label": "None", "value": "none"}], "tableView": true, "customConditional": "show = data.past_laser_types"}, {"key": "past_laser_improvement", "type": "radio", "input": true, "label": "Have you noticed an improvement in your skin since starting laser treatments?", "values": [{"label": "Yes, significant improvement", "value": "significant_improvement"}, {"label": "Yes, some improvement", "value": "some_improvement"}, {"label": "No noticeable improvement", "value": "no_improvement"}, {"label": "Yes, but with some areas needing further improvement", "value": "partial_improvement"}, {"label": "No, I'm dissatisfied", "value": "dissatisfied"}, {"label": "Not applicable", "value": "not_applicable"}], "tableView": true, "customConditional": "show = data.past_laser_side_effects"}, {"key": "past_prescription_retinoids", "type": "selectboxes", "input": true, "label": "Which retinoids are you currently using? (Select all that apply)", "values": [{"label": "Tretinoin", "value": "tretinoin"}, {"label": "Adapal<PERSON>", "value": "adapalene"}, {"label": "Tazarotene", "value": "tazarotene"}], "tableView": true, "customConditional": "show = data.general_prescription_treatments && data.general_prescription_treatments.retinoids;", "optionsLabelPosition": "right"}, {"key": "past_antibiotic_creams", "type": "selectboxes", "input": true, "label": "Which antibiotic creams are you currently using? (Select all that apply)", "values": [{"label": "Clindamycin", "value": "clindamycin"}, {"label": "Erythromycin", "value": "erythromycin"}, {"label": "Metronidazole", "value": "metronidazole"}], "tableView": true, "customConditional": "show = data.general_prescription_treatments && data.general_prescription_treatments.past_antibiotic_creams;", "optionsLabelPosition": "right"}, {"key": "past_skin_lightening_creams", "type": "selectboxes", "input": true, "label": "Which skin lightening creams are you currently using? (Select all that apply)", "values": [{"label": "Hydroquinone", "value": "hydroquinone"}, {"label": "Azelaic Acid", "value": "azelaic_acid"}, {"label": "Kojic Acid", "value": "kojic_acid"}, {"label": "Niacinamide", "value": "niacinamide"}], "tableView": true, "customConditional": "show = data.general_prescription_treatments && data.general_prescription_treatments.past_skin_lightening_creams;", "optionsLabelPosition": "right"}, {"key": "past_chemical_peels", "type": "selectboxes", "input": true, "label": "Which chemical peels are you currently undergoing? (Select all that apply)", "values": [{"label": "Glycolic Acid Peel", "value": "glycolic_acid_peel"}, {"label": "Salicylic Acid Peel", "value": "salicylic_acid_peel"}, {"label": "Other Peels", "value": "other_peels"}], "tableView": true, "customConditional": "show = data.general_procedures && data.general_procedures.peels;", "optionsLabelPosition": "right"}]}