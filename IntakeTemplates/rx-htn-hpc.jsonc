{"components": [{"key": "hypertension_medication_header", "html": "<h2><strong>Hypertension Medication Renewal</strong></h2><p>Please provide information to assist in renewing your hypertensive medication.</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "consultation_indication", "type": "selectboxes", "input": true, "label": "Please select your reason(s) for this consultation:", "values": [{"label": "Renew Blood Pressure Medication", "value": "renew_htn_medication", "shortcut": ""}, {"label": "Obtain a 24-hour ambulatory blood pressure monitor", "value": "abpm_request", "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "confirm_label": "Reason for Consultation", "optionsLabelPosition": "right"}, {"key": "consultation_indication_other", "type": "textarea", "input": true, "label": "Please state your other reason(s) for this consultation:", "tableView": true, "autoExpand": true, "confirm_label": "Other reason for consultation:", "customConditional": "show = !!(data.consultation_indication?.other);"}, {"key": "heading_diagnosis", "html": "<h2><strong>Diagnosis and History</strong></h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "prior_htn_diagnosis", "type": "radio", "input": true, "label": "Have you been previously diagnosed with hypertension by a healthcare professional (e.g., doctor, cardiologist, nurse practitioner)?", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "confirm_label": "Prior Hypertension Diagnosis", "optionsLabelPosition": "right"}, {"key": "htn_diagnosis_age", "type": "number", "input": true, "label": "How old were you when you were first diagnosed with hypertension?", "validate": {"required": true}, "tableView": true, "confirm_label": "Age of Hypertension Diagnosis:", "customConditional": "show = data.prior_htn_diagnosis === 'yes';"}, {"key": "heading_current_medications", "html": "<h2><strong>Current Medications</strong></h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "medication_type", "data": {"values": [{"label": "Combination pill", "value": "combo"}, {"label": "Individual medication(s)", "value": "individual"}, {"label": "Both combination and individual medications", "value": "both"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "Are you currently taking a combination blood pressure pill (with more than one ingredient), or individual medications?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Current Medication Type"}, {"key": "combo_bp_medications", "type": "selectboxes", "input": true, "label": "Which combination blood pressure medication(s) are you currently taking?", "values": [{"label": "Lisinopril/HCTZ (Zestoretic)", "value": "lisinopril_hctz"}, {"label": "Ramipril/HCTZ (Altace HCT)", "value": "ramipril_hctz"}, {"label": "Enalapril/HCTZ (Vaseretic)", "value": "enalapril_hctz"}, {"label": "Candesartan/HCTZ (Atacand Plus)", "value": "candesartan_hctz"}, {"label": "Telmisartan/HCTZ (Micardis Plus)", "value": "telmisartan_hctz"}, {"label": "Valsartan/HCTZ (Diovan HCT)", "value": "valsartan_hctz"}, {"label": "Losartan/HCTZ (Hyzaar)", "value": "losartan_hctz"}, {"label": "Perindopril/Indapamide (Coversyl Plus)", "value": "perindopril_indapamide"}, {"label": "Other combination pill", "value": "other"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Combination Blood Pressure Medications:", "customConditional": "show = data.medication_type === 'combo' || data.medication_type === 'both';"}, {"key": "combo_bp_other", "type": "textfield", "input": true, "label": "Please specify the name of your combination pill:", "tableView": true, "customConditional": "show = data.combo_bp_medications?.other === true;"}, {"key": "lisinopril_hctz_strength", "data": {"values": [{"label": "10 mg / 12.5 mg", "value": "10_12_5"}, {"label": "20 mg / 12.5 mg", "value": "20_12_5"}, {"label": "20 mg / 25 mg", "value": "20_25"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "What is the pill strength of your Lisinopril/HCTZ combination medication?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Lisinopril/HCTZ Pill Strength:", "customConditional": "show = data.combo_bp_medications?.lisinopril_hctz === true;"}, {"key": "lisinopril_hctz_tablet_amount", "data": {"values": [{"label": "1/2 tablet", "value": "half"}, {"label": "1 full tablet", "value": "full"}, {"label": "2 tablets", "value": "two"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "How much of the Lisinopril/HCTZ tablet do you take each time?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Lisinopril/HCTZ Tablet Amount:", "customConditional": "show = data.combo_bp_medications?.lisinopril_hctz === true && data.lisinopril_hctz_strength !== 'not_sure';"}, {"key": "lisinopril_hctz_doses_per_day", "data": {"values": [{"label": "Once daily", "value": "once"}, {"label": "Twice daily", "value": "twice"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "How many times per day do you take your Lisinopril/HCTZ combination pill?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Lisinopril/HCTZ Doses Per Day:", "customConditional": "show = data.combo_bp_medications?.lisinopril_hctz === true && data.lisinopril_hctz_strength !== 'not_sure';"}, {"key": "ramipril_hctz_strength", "data": {"values": [{"label": "5 mg / 12.5 mg", "value": "5 mg / 12.5 mg"}, {"label": "10 mg / 12.5 mg", "value": "10 mg / 12.5 mg"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "What is the pill strength of your Ramipril/HCTZ combination medication?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Ramipril/HCTZ Pill Strength:", "customConditional": "show = data.combo_bp_medications?.ramipril_hctz === true;"}, {"key": "ramipril_hctz_tablet_amount", "data": {"values": [{"label": "1/2 tablet", "value": "1/2 tablet"}, {"label": "1 full tablet", "value": "1 full tablet"}, {"label": "2 tablets", "value": "2 tablets"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "How much of the Ramipril/HCTZ tablet do you take each time?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Ramipril/HCTZ Tablet Amount:", "customConditional": "show = data.combo_bp_medications?.ramipril_hctz === true && data.ramipril_hctz_strength !== 'not_sure';"}, {"key": "ramipril_hctz_doses_per_day", "data": {"values": [{"label": "Once daily", "value": "once"}, {"label": "Twice daily", "value": "twice"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "How many times per day do you take your Ramipril/HCTZ combination pill?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Ramipril/HCTZ Doses Per Day:", "customConditional": "show = data.combo_bp_medications?.ramipril_hctz === true && data.ramipril_hctz_strength !== 'not_sure';"}, {"key": "enalapril_hctz_strength", "data": {"values": [{"label": "5 mg / 12.5 mg", "value": "5 mg / 12.5 mg"}, {"label": "10 mg / 25 mg", "value": "10 mg / 25 mg"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "What is the pill strength of your Enalapril/HCTZ combination medication?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Enalapril/HCTZ Pill Strength:", "customConditional": "show = data.combo_bp_medications?.enalapril_hctz === true;"}, {"key": "enalapril_hctz_tablet_amount", "data": {"values": [{"label": "1/2 tablet", "value": "1/2 tablet"}, {"label": "1 full tablet", "value": "1 full tablet"}, {"label": "2 tablets", "value": "2 tablets"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "How much of the Enalapril/HCTZ tablet do you take each time?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Enalapril/HCTZ Tablet Amount:", "customConditional": "show = data.combo_bp_medications?.enalapril_hctz === true && data.enalapril_hctz_strength !== 'not_sure';"}, {"key": "enalapril_hctz_doses_per_day", "data": {"values": [{"label": "Once daily", "value": "once"}, {"label": "Twice daily", "value": "twice"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "How many times per day do you take your Enalapril/HCTZ combination pill?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Enalapril/HCTZ Doses Per Day:", "customConditional": "show = data.combo_bp_medications?.enalapril_hctz === true && data.enalapril_hctz_strength !== 'not_sure';"}, {"key": "candesartan_hctz_strength", "data": {"values": [{"label": "16 mg / 12.5 mg", "value": "16 mg / 12.5 mg"}, {"label": "32 mg / 12.5 mg", "value": "32 mg / 12.5 mg"}, {"label": "32 mg / 25 mg", "value": "32 mg / 25 mg"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "What is the pill strength of your Candesartan/HCTZ combination medication?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Candesartan/HCTZ Pill Strength:", "customConditional": "show = data.combo_bp_medications?.candesartan_hctz === true;"}, {"key": "candesartan_hctz_tablet_amount", "data": {"values": [{"label": "1/2 tablet", "value": "1/2 tablet"}, {"label": "1 full tablet", "value": "1 full tablet"}, {"label": "2 tablets", "value": "2 tablets"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "How much of the Candesartan/HCTZ tablet do you take each time?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Candesartan/HCTZ Tablet Amount:", "customConditional": "show = data.combo_bp_medications?.candesartan_hctz === true && data.candesartan_hctz_strength !== 'not_sure';"}, {"key": "candesartan_hctz_doses_per_day", "data": {"values": [{"label": "Once daily", "value": "once"}, {"label": "Twice daily", "value": "twice"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "How many times per day do you take your Candesartan/HCTZ combination pill?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Candesartan/HCTZ Doses Per Day:", "customConditional": "show = data.combo_bp_medications?.candesartan_hctz === true && data.candesartan_hctz_strength !== 'not_sure';"}, {"key": "telmisartan_hctz_strength", "data": {"values": [{"label": "40 mg / 12.5 mg", "value": "40 mg / 12.5 mg"}, {"label": "80 mg / 12.5 mg", "value": "80 mg / 12.5 mg"}, {"label": "80 mg / 25 mg", "value": "80 mg / 25 mg"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "What is the pill strength of your Telmisartan/HCTZ combination medication?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Telmisartan/HCTZ Pill Strength:", "customConditional": "show = data.combo_bp_medications?.telmisartan_hctz === true;"}, {"key": "telmisartan_hctz_tablet_amount", "data": {"values": [{"label": "1/2 tablet", "value": "1/2 tablet"}, {"label": "1 full tablet", "value": "1 full tablet"}, {"label": "2 tablets", "value": "2 tablets"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "How much of the Telmisartan/HCTZ tablet do you take each time?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Telmisartan/HCTZ Tablet Amount:", "customConditional": "show = data.combo_bp_medications?.telmisartan_hctz === true && data.telmisartan_hctz_strength !== 'not_sure';"}, {"key": "telmisartan_hctz_doses_per_day", "data": {"values": [{"label": "Once daily", "value": "once"}, {"label": "Twice daily", "value": "twice"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "How many times per day do you take your Telmisartan/HCTZ combination pill?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Telmisartan/HCTZ Doses Per Day:", "customConditional": "show = data.combo_bp_medications?.telmisartan_hctz === true && data.telmisartan_hctz_strength !== 'not_sure';"}, {"key": "valsartan_hctz_strength", "data": {"values": [{"label": "80 mg / 12.5 mg", "value": "80 mg / 12.5 mg"}, {"label": "160 mg / 12.5 mg", "value": "160 mg / 12.5 mg"}, {"label": "160 mg / 25 mg", "value": "160 mg / 25 mg"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "What is the pill strength of your Valsartan/HCTZ combination medication?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Valsartan/HCTZ Pill Strength:", "customConditional": "show = data.combo_bp_medications?.valsartan_hctz === true;"}, {"key": "valsartan_hctz_tablet_amount", "data": {"values": [{"label": "1/2 tablet", "value": "1/2 tablet"}, {"label": "1 full tablet", "value": "1 full tablet"}, {"label": "2 tablets", "value": "2 tablets"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "How much of the Valsartan/HCTZ tablet do you take each time?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Valsartan/HCTZ Tablet Amount:", "customConditional": "show = data.combo_bp_medications?.valsartan_hctz === true && data.valsartan_hctz_strength !== 'not_sure';"}, {"key": "valsartan_hctz_doses_per_day", "data": {"values": [{"label": "Once daily", "value": "once"}, {"label": "Twice daily", "value": "twice"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "How many times per day do you take your Valsartan/HCTZ combination pill?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Valsartan/HCTZ Doses Per Day:", "customConditional": "show = data.combo_bp_medications?.valsartan_hctz === true && data.valsartan_hctz_strength !== 'not_sure';"}, {"key": "losartan_hctz_strength", "data": {"values": [{"label": "50 mg / 12.5 mg", "value": "50 mg / 12.5 mg"}, {"label": "100 mg / 12.5 mg", "value": "100 mg / 12.5 mg"}, {"label": "100 mg / 25 mg", "value": "100 mg / 25 mg"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "What is the pill strength of your Losartan/HCTZ combination medication?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Losartan/HCTZ Pill Strength:", "customConditional": "show = data.combo_bp_medications?.losartan_hctz === true;"}, {"key": "losartan_hctz_tablet_amount", "data": {"values": [{"label": "1/2 tablet", "value": "1/2 tablet"}, {"label": "1 full tablet", "value": "1 full tablet"}, {"label": "2 tablets", "value": "2 tablets"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "How much of the Losartan/HCTZ tablet do you take each time?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Losartan/HCTZ Tablet Amount:", "customConditional": "show = data.combo_bp_medications?.losartan_hctz === true && data.losartan_hctz_strength !== 'not_sure';"}, {"key": "losartan_hctz_doses_per_day", "data": {"values": [{"label": "Once daily", "value": "once"}, {"label": "Twice daily", "value": "twice"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "How many times per day do you take your Losartan/HCTZ combination pill?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Losartan/HCTZ Doses Per Day:", "customConditional": "show = data.combo_bp_medications?.losartan_hctz === true && data.losartan_hctz_strength !== 'not_sure';"}, {"key": "perindopril_indapamide_strength", "data": {"values": [{"label": "4 mg / 1.25 mg", "value": "4 mg / 1.25 mg"}, {"label": "8 mg / 2.5 mg", "value": "8 mg / 2.5 mg"}, {"label": "10 mg / 2.5 mg", "value": "10 mg / 2.5 mg"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "What is the pill strength of your Perindopril/Indapamide (Coversyl Plus) medication?", "widget": "html5", "validate": {"required": true}, "confirm_label": "Perindopril/Indapamide Pill Strength:", "customConditional": "show = data.combo_bp_medications?.perindopril_indapamide === true;"}, {"key": "perindopril_indapamide_tablet_amount", "data": {"values": [{"label": "1/2 tablet", "value": "1/2 tablet"}, {"label": "1 full tablet", "value": "1 full tablet"}, {"label": "2 tablets", "value": "2 tablets"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "How much of the Perindopril/Indapamide tablet do you take each time?", "widget": "html5", "validate": {"required": true}, "confirm_label": "Perindopril/Indapamide Tablet Amount:", "customConditional": "show = data.combo_bp_medications?.perindopril_indapamide === true && data.perindopril_indapamide_strength !== 'not_sure';"}, {"key": "perindopril_indapamide_doses_per_day", "data": {"values": [{"label": "Once daily", "value": "once"}, {"label": "Twice daily", "value": "twice"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "How many times per day do you take your Perindopril/Indapamide combination pill?", "widget": "html5", "validate": {"required": true}, "confirm_label": "Perindopril/Indapamide Doses Per Day:", "customConditional": "show = data.combo_bp_medications?.perindopril_indapamide === true && data.perindopril_indapamide_strength !== 'not_sure';"}, {"key": "ace_inhibitors", "type": "selectboxes", "input": true, "label": "Are you currently taking any of the following medications as <strong>separate pills</strong> (not as part of a combination pill)?", "values": [{"label": "Lisinopril (Prinivil, Zestril)", "value": "lisinopril"}, {"label": "Ramipril (Altace)", "value": "ram<PERSON>ril"}, {"label": "Enalapril (Vasotec)", "value": "enalapril"}, {"label": "None of the above", "value": "none_of_the_above"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Current ACE Inhibitors:", "customConditional": "show = data.medication_type === 'individual' || data.medication_type === 'both';"}, {"key": "lisinopril_dosage", "data": {"values": [{"label": "2.5 mg", "value": "2_5mg"}, {"label": "5 mg", "value": "5mg"}, {"label": "10 mg", "value": "10mg"}, {"label": "20 mg", "value": "20mg"}, {"label": "Combination pill format / unsure", "value": "combination_unsure"}]}, "type": "select", "input": true, "label": "What is your current pill strength for Lisinopril?", "widget": "html5", "validate": {"required": true}, "confirm_label": "<PERSON><PERSON><PERSON><PERSON> Strength:", "customConditional": "show = data.ace_inhibitors?.lisinopril;"}, {"key": "lisinopril_tablet_amount", "data": {"values": [{"label": "1/4 tablet", "value": "quarter"}, {"label": "1/2 tablet", "value": "half"}, {"label": "1 full tablet", "value": "full"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "How much of the Lisinopril tablet do you take per dose?", "widget": "html5", "validate": {"required": true}, "confirm_label": "Lisinopril Tablet Amount:", "customConditional": "show = data.ace_inhibitors?.lisinopril === true && data.lisinopril_dosage !== 'combination_unsure';"}, {"key": "lisinopril_doses_per_day", "data": {"values": [{"label": "Once daily", "value": "once"}, {"label": "Twice daily", "value": "twice"}, {"label": "Three times daily", "value": "thrice"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "How many times a day do you take Lisinopril?", "widget": "html5", "validate": {"required": true}, "confirm_label": "Lisinopril Do<PERSON> Per Day:", "customConditional": "show = data.ace_inhibitors?.lisinopril === true && data.lisinopril_dosage !== 'combination_unsure';"}, {"key": "ramipril_dosage", "data": {"values": [{"label": "1.25 mg", "value": "1.25 mg"}, {"label": "2.5 mg", "value": "2.5 mg"}, {"label": "5 mg", "value": "5 mg"}, {"label": "10 mg", "value": "10 mg"}, {"label": "Combination pill format / unsure", "value": "combination_unsure"}]}, "type": "select", "input": true, "label": "What is your current pill strength for Ram<PERSON><PERSON>?", "widget": "html5", "validate": {"required": true}, "confirm_label": "<PERSON><PERSON><PERSON> Strength:", "customConditional": "show = data.ace_inhibitors?.ramipril === true;"}, {"key": "ramipril_tablet_amount", "data": {"values": [{"label": "1/4 tablet", "value": "quarter"}, {"label": "1/2 tablet", "value": "half"}, {"label": "1 full tablet", "value": "full"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "How much of the Ramipril tablet do you take per dose?", "widget": "html5", "validate": {"required": true}, "confirm_label": "Ramipril Tablet Amount:", "customConditional": "show = data.ace_inhibitors?.ramipril === true && data.ramipril_dosage !== 'combination_unsure';"}, {"key": "ramipril_doses_per_day", "data": {"values": [{"label": "Once daily", "value": "once"}, {"label": "Twice daily", "value": "twice"}, {"label": "Three times daily", "value": "thrice"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "How many times a day do you take <PERSON><PERSON><PERSON>?", "widget": "html5", "validate": {"required": true}, "confirm_label": "<PERSON><PERSON><PERSON> Per Day:", "customConditional": "show = data.ace_inhibitors?.ramipril === true && data.ramipril_dosage !== 'combination_unsure';"}, {"key": "arb", "type": "selectboxes", "input": true, "label": "Are you currently taking any of the following medications as <strong>separate pills</strong> (not as part of a combination pill)?", "values": [{"label": "Losartan (Cozaar)", "value": "losartan"}, {"label": "Valsar<PERSON> (Diovan)", "value": "vals<PERSON>an"}, {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Avapro)", "value": "irb<PERSON><PERSON>an"}, {"label": "Candesartan (Atacand)", "value": "candesartan"}, {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Benicar)", "value": "o<PERSON><PERSON><PERSON><PERSON>"}, {"label": "Telmisartan (Micardis)", "value": "telmisartan"}, {"label": "E<PERSON><PERSON>artan (Teveten)", "value": "ep<PERSON><PERSON>an"}, {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Edarbi)", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"label": "None of the above", "value": "none_of_the_above"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Current ARBs:", "customConditional": "show = data.medication_type === 'individual' || data.medication_type === 'both';"}, {"key": "losartan_dosage", "data": {"values": [{"label": "25 mg", "value": "25 mg"}, {"label": "50 mg", "value": "50 mg"}, {"label": "100 mg", "value": "100 mg"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "What is your current pill strength for Losartan?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "<PERSON><PERSON><PERSON> Strength:", "customConditional": "show = data.arb?.losartan;"}, {"key": "losartan_tablet_amount", "data": {"values": [{"label": "1/4 tablet", "value": "quarter"}, {"label": "1/2 tablet", "value": "half"}, {"label": "1 full tablet", "value": "full"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "How much of the Losartan tablet do you take per dose?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Losartan Tablet Amount:", "customConditional": "show = data.losartan_dosage !== 'not_sure' && data.arb?.losartan;"}, {"key": "losartan_doses_per_day", "data": {"values": [{"label": "Once daily", "value": "once"}, {"label": "Twice daily", "value": "twice"}, {"label": "Three times daily", "value": "thrice"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "How many times a day do you take Losartan?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Losartan Do<PERSON> Per Day:", "customConditional": "show = data.losartan_dosage !== 'not_sure' && data.arb?.losartan;"}, {"key": "valsartan_dosage", "data": {"values": [{"label": "40 mg", "value": "40mg"}, {"label": "80 mg", "value": "80mg"}, {"label": "160 mg", "value": "160mg"}, {"label": "320 mg", "value": "320mg"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "What is your current pill strength for Valsartan?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Valsartan Pill Strength:", "customConditional": "show = data.arb?.valsartan;"}, {"key": "valsartan_tablet_amount", "data": {"values": [{"label": "1/4 tablet", "value": "quarter"}, {"label": "1/2 tablet", "value": "half"}, {"label": "1 full tablet", "value": "full"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "How much of the Valsartan tablet do you take per dose?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Valsartan Tablet Amount:", "customConditional": "show = data.valsartan_dosage !== 'not_sure' && data.arb?.valsartan;"}, {"key": "valsartan_doses_per_day", "data": {"values": [{"label": "Once daily", "value": "once"}, {"label": "Twice daily", "value": "twice"}, {"label": "Three times daily", "value": "thrice"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "How many times a day do you take Valsartan?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Valsartan Doses Per Day:", "customConditional": "show = data.valsartan_dosage !== 'not_sure' && data.arb?.valsartan;"}, {"key": "irbesartan_dosage", "data": {"values": [{"label": "75 mg", "value": "75mg"}, {"label": "150 mg", "value": "150mg"}, {"label": "300 mg", "value": "300mg"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "What is your current pill strength for Irb<PERSON>artan?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "<PERSON><PERSON><PERSON><PERSON><PERSON>rength:", "customConditional": "show = data.arb?.irbesartan;"}, {"key": "irbesartan_tablet_amount", "data": {"values": [{"label": "1/4 tablet", "value": "quarter"}, {"label": "1/2 tablet", "value": "half"}, {"label": "1 full tablet", "value": "full"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "How much of the Irbesartan tablet do you take per dose?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Irbesartan Tablet Amount:", "customConditional": "show = data.irbesartan_dosage !== 'not_sure' && data.arb?.irbesartan;"}, {"key": "irbesartan_doses_per_day", "data": {"values": [{"label": "Once daily", "value": "once"}, {"label": "Twice daily", "value": "twice"}, {"label": "Three times daily", "value": "thrice"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "How many times a day do you take Irbesartan?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Irb<PERSON><PERSON>an <PERSON> Per Day:", "customConditional": "show = data.irbesartan_dosage !== 'not_sure' && data.arb?.irbesartan;"}, {"key": "candesartan_dosage", "data": {"values": [{"label": "4 mg", "value": "4mg"}, {"label": "8 mg", "value": "8mg"}, {"label": "16 mg", "value": "16mg"}, {"label": "32 mg", "value": "32mg"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "What is your current pill strength for Candesartan?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Candesartan Pill Strength:", "customConditional": "show = data.arb?.candesartan;"}, {"key": "candesartan_tablet_amount", "data": {"values": [{"label": "1/4 tablet", "value": "quarter"}, {"label": "1/2 tablet", "value": "half"}, {"label": "1 full tablet", "value": "full"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "How much of the Candesartan tablet do you take per dose?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Candesartan Tablet Amount:", "customConditional": "show = data.candesartan_dosage !== 'not_sure' && data.arb?.candesartan;"}, {"key": "candesartan_doses_per_day", "data": {"values": [{"label": "Once daily", "value": "once"}, {"label": "Twice daily", "value": "twice"}, {"label": "Three times daily", "value": "thrice"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "How many times a day do you take Candesartan?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Candesartan Doses Per Day:", "customConditional": "show = data.candesartan_dosage !== 'not_sure' && data.arb?.candesartan;"}, {"key": "olmesartan_dosage", "data": {"values": [{"label": "5 mg", "value": "5mg"}, {"label": "10 mg", "value": "10mg"}, {"label": "20 mg", "value": "20mg"}, {"label": "40 mg", "value": "40mg"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "What is your current pill strength for <PERSON><PERSON><PERSON><PERSON><PERSON>?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "<PERSON><PERSON><PERSON><PERSON><PERSON> Strength:", "customConditional": "show = data.arb?.olmesartan;"}, {"key": "olmesartan_tablet_amount", "data": {"values": [{"label": "1/4 tablet", "value": "quarter"}, {"label": "1/2 tablet", "value": "half"}, {"label": "1 full tablet", "value": "full"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "How much of the Olmesartan tablet do you take per dose?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "<PERSON><PERSON><PERSON>artan Tablet Amount:", "customConditional": "show = data.olmesartan_dosage !== 'not_sure' && data.arb?.olmesartan;"}, {"key": "olmesartan_doses_per_day", "data": {"values": [{"label": "Once daily", "value": "once"}, {"label": "Twice daily", "value": "twice"}, {"label": "Three times daily", "value": "thrice"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "How many times a day do you take <PERSON><PERSON><PERSON>art<PERSON>?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "<PERSON><PERSON><PERSON><PERSON><PERSON> Per Day:", "customConditional": "show = data.olmesartan_dosage !== 'not_sure' && data.arb?.olmesartan;"}, {"key": "telmisartan_dosage", "data": {"values": [{"label": "20 mg", "value": "20mg"}, {"label": "40 mg", "value": "40mg"}, {"label": "80 mg", "value": "80mg"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "What is your current pill strength for Telmisartan?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Telmisartan Pill Strength:", "customConditional": "show = data.arb?.telmisartan;"}, {"key": "telmisartan_tablet_amount", "data": {"values": [{"label": "1/4 tablet", "value": "quarter"}, {"label": "1/2 tablet", "value": "half"}, {"label": "1 full tablet", "value": "full"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "How much of the Telmisartan tablet do you take per dose?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Telmisartan Tablet Amount:", "customConditional": "show = data.telmisartan_dosage !== 'not_sure' && data.arb?.telmisartan;"}, {"key": "telmisartan_doses_per_day", "data": {"values": [{"label": "Once daily", "value": "once"}, {"label": "Twice daily", "value": "twice"}, {"label": "Three times daily", "value": "thrice"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "How many times a day do you take Telmisartan?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Telmisartan Doses Per Day:", "customConditional": "show = data.telmisartan_dosage !== 'not_sure' && data.arb?.telmisartan;"}, {"key": "azilsartan_dosage", "data": {"values": [{"label": "40 mg", "value": "40mg"}, {"label": "80 mg", "value": "80mg"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "What is your current pill strength for Azilsartan?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "<PERSON><PERSON><PERSON><PERSON><PERSON> Strength:", "customConditional": "show = data.arb?.azilsartan;"}, {"key": "azilsartan_tablet_amount", "data": {"values": [{"label": "1/4 tablet", "value": "quarter"}, {"label": "1/2 tablet", "value": "half"}, {"label": "1 full tablet", "value": "full"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "How much of the Azilsartan tablet do you take per dose?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Azilsartan Tablet Amount:", "customConditional": "show = data.azilsartan_dosage !== 'not_sure' && data.arb?.azilsartan;"}, {"key": "azilsartan_doses_per_day", "data": {"values": [{"label": "Once daily", "value": "once"}, {"label": "Twice daily", "value": "twice"}, {"label": "Three times daily", "value": "thrice"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "How many times a day do you take A<PERSON><PERSON>artan?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Azilsartan Doses Per Day:", "customConditional": "show = data.azilsartan_dosage !== 'not_sure' && data.arb?.azilsartan;"}, {"key": "calcium_channel_blockers", "type": "selectboxes", "input": true, "label": "Are you currently taking any of the following medications as <strong>separate pills</strong> (not as part of a combination pill)?", "values": [{"label": "Amlodipine (Norvasc)", "value": "amlodipine"}, {"label": "Diltiazem (Cardizem)", "value": "diltiazem"}, {"label": "Nifedipine (Procardia)", "value": "nifedipine"}, {"label": "Verapamil (Calan)", "value": "verapamil"}, {"label": "None of the above", "value": "none_of_the_above"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Current Calcium Channel Blockers:", "customConditional": "show = data.medication_type === 'individual' || data.medication_type === 'both';"}, {"key": "amlodipine_dosage", "data": {"values": [{"label": "2.5 mg", "value": "2_5mg"}, {"label": "5 mg", "value": "5mg"}, {"label": "10 mg", "value": "10mg"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "What is your current pill strength for Amlodipine?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Amlodipine Pill Strength:", "customConditional": "show = data.calcium_channel_blockers?.amlodipine;"}, {"key": "amlodipine_tablet_amount", "data": {"values": [{"label": "1/4 tablet", "value": "quarter"}, {"label": "1/2 tablet", "value": "half"}, {"label": "1 full tablet", "value": "full"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "How much of the Amlodipine tablet do you take per dose?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Amlodipine Tablet Amount:", "customConditional": "show = data.amlodipine_dosage !== 'not_sure' && data.calcium_channel_blockers?.amlodipine;"}, {"key": "amlodipine_doses_per_day", "data": {"values": [{"label": "Once daily", "value": "once"}, {"label": "Twice daily", "value": "twice"}, {"label": "Three times daily", "value": "thrice"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "How many times a day do you take Amlodi<PERSON>?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Amlodipine Doses Per Day:", "customConditional": "show = data.amlodipine_dosage !== 'not_sure' && data.calcium_channel_blockers?.amlodipine;"}, {"key": "diltiazem_dosage", "data": {"values": [{"label": "30 mg", "value": "30mg"}, {"label": "60 mg", "value": "60mg"}, {"label": "90 mg", "value": "90mg"}, {"label": "120 mg", "value": "120mg"}, {"label": "180 mg", "value": "180mg"}, {"label": "240 mg", "value": "240mg"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "What is your current pill strength for Diltiazem?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Diltiazem Pill Strength:", "customConditional": "show = data.calcium_channel_blockers?.diltiazem;"}, {"key": "diltiazem_tablet_amount", "data": {"values": [{"label": "1/4 tablet", "value": "quarter"}, {"label": "1/2 tablet", "value": "half"}, {"label": "1 full tablet", "value": "full"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "How much of the Diltiazem tablet do you take per dose?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Diltiazem Tablet Amount:", "customConditional": "show = data.diltiazem_dosage !== 'not_sure' && data.calcium_channel_blockers?.diltiazem;"}, {"key": "diltiazem_doses_per_day", "data": {"values": [{"label": "Once daily", "value": "once"}, {"label": "Twice daily", "value": "twice"}, {"label": "Three times daily", "value": "thrice"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "How many times a day do you take Diltiazem?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Diltiazem Doses Per Day:", "customConditional": "show = data.diltiazem_dosage !== 'not_sure' && data.calcium_channel_blockers?.diltiazem;"}, {"key": "nifedipine_dosage", "data": {"values": [{"label": "10 mg", "value": "10mg"}, {"label": "20 mg", "value": "20mg"}, {"label": "30 mg (Extended Release)", "value": "30mg_er"}, {"label": "60 mg (Extended Release)", "value": "60mg_er"}, {"label": "90 mg (Extended Release)", "value": "90mg_er"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "What is your current pill strength for Nifedipine?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Nifedipine Pill Strength:", "customConditional": "show = data.calcium_channel_blockers?.nifedipine;"}, {"key": "nifedipine_tablet_amount", "data": {"values": [{"label": "1/4 tablet", "value": "quarter"}, {"label": "1/2 tablet", "value": "half"}, {"label": "1 full tablet", "value": "full"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "How much of the Nifedipine tablet do you take per dose?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Nifedipine Tablet Amount:", "customConditional": "show = data.nifedipine_dosage !== 'not_sure' && data.calcium_channel_blockers?.nifedipine;"}, {"key": "nifedipine_doses_per_day", "data": {"values": [{"label": "Once daily", "value": "once"}, {"label": "Twice daily", "value": "twice"}, {"label": "Three times daily", "value": "thrice"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "How many times a day do you take <PERSON><PERSON>di<PERSON>?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Nifedipine Doses Per Day:", "customConditional": "show = data.nifedipine_dosage !== 'not_sure' && data.calcium_channel_blockers?.nifedipine;"}, {"key": "verapamil_dosage", "data": {"values": [{"label": "40 mg", "value": "40mg"}, {"label": "80 mg", "value": "80mg"}, {"label": "120 mg", "value": "120mg"}, {"label": "180 mg (Extended Release)", "value": "180mg_er"}, {"label": "240 mg (Extended Release)", "value": "240mg_er"}, {"label": "360 mg (Extended Release)", "value": "360mg_er"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "What is your current pill strength for Verapamil?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Verapa<PERSON><PERSON> Pill Strength:", "customConditional": "show = data.calcium_channel_blockers?.verapamil;"}, {"key": "verapamil_tablet_amount", "data": {"values": [{"label": "1/4 tablet", "value": "quarter"}, {"label": "1/2 tablet", "value": "half"}, {"label": "1 full tablet", "value": "full"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "How much of the Verapamil tablet do you take per dose?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Verapamil Tablet Amount:", "customConditional": "show = data.verapamil_dosage !== 'not_sure' && data.calcium_channel_blockers?.verapamil;"}, {"key": "verapamil_doses_per_day", "data": {"values": [{"label": "Once daily", "value": "once"}, {"label": "Twice daily", "value": "twice"}, {"label": "Three times daily", "value": "thrice"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "How many times a day do you take Verapamil?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Verapamil Doses Per Day:", "customConditional": "show = data.verapamil_dosage !== 'not_sure' && data.calcium_channel_blockers?.verapamil;"}, {"key": "diuretics", "type": "selectboxes", "input": true, "label": "Are you currently taking any of the following medications as <strong>separate pills</strong> (not as part of a combination pill)?", "values": [{"label": "Hydrochlorothiazide (HCTZ)", "value": "hydrochlorothiazide"}, {"label": "Chlorthalidone", "value": "chlorthalidone"}, {"label": "Indapamide (Lozide)", "value": "indapamide"}, {"label": "Furosemide (Lasix)", "value": "furosemide"}, {"label": "Spironolactone (Aldactone)", "value": "spironolactone"}, {"label": "None of the above", "value": "none_of_the_above"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Current Diuretics:", "customConditional": "show = data.medication_type === 'individual' || data.medication_type === 'both';"}, {"key": "hydrochlorothiazide_dosage", "data": {"values": [{"label": "12.5 mg", "value": "12_5mg"}, {"label": "25 mg", "value": "25mg"}, {"label": "50 mg", "value": "50mg"}, {"label": "Combination pill format / unsure", "value": "combination_unsure"}]}, "type": "select", "input": true, "label": "What is your current pill strength for Hydrochlorothiazide (HCTZ)?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Hydrochlorothiazide Pill Strength:", "customConditional": "show = data.diuretics?.hydrochlorothiazide === true;"}, {"key": "hydrochlorothiazide_tablet_amount", "data": {"values": [{"label": "1/4 tablet", "value": "quarter"}, {"label": "1/2 tablet", "value": "half"}, {"label": "1 full tablet", "value": "full"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "How much of the Hydrochlorothiazide tablet do you take per dose?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Hydrochlorothiazide Tablet Amount:", "customConditional": "show = data.diuretics?.hydrochlorothiazide === true && data.hydrochlorothiazide_dosage !== 'combination_unsure';"}, {"key": "hydrochlorothiazide_doses_per_day", "data": {"values": [{"label": "Once daily", "value": "once"}, {"label": "Twice daily", "value": "twice"}, {"label": "Three times daily", "value": "thrice"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "How many times a day do you take Hydrochlorothiazide?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Hydrochlorothiazide Doses Per Day:", "customConditional": "show = data.diuretics?.hydrochlorothiazide === true && data.hydrochlorothiazide_dosage !== 'combination_unsure';"}, {"key": "chlorthalidone_dosage", "data": {"values": [{"label": "12.5 mg", "value": "12_5mg"}, {"label": "25 mg", "value": "25mg"}, {"label": "50 mg", "value": "50mg"}, {"label": "Combination pill format / unsure", "value": "combination_unsure"}]}, "type": "select", "input": true, "label": "What is your current pill strength for Chlorthalidone?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Chlorthalidone Pill Strength:", "customConditional": "show = data.diuretics?.chlorthalidone === true;"}, {"key": "chlorthalidone_tablet_amount", "data": {"values": [{"label": "1/4 tablet", "value": "quarter"}, {"label": "1/2 tablet", "value": "half"}, {"label": "1 full tablet", "value": "full"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "How much of the Chlorthalidone tablet do you take per dose?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Chlorthalidone Tablet Amount:", "customConditional": "show = data.chlorthalidone_dosage !== 'combination_unsure' && data.diuretics?.chlorthalidone;"}, {"key": "chlorthalidone_doses_per_day", "data": {"values": [{"label": "Once daily", "value": "once"}, {"label": "Twice daily", "value": "twice"}, {"label": "Three times daily", "value": "thrice"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "How many times a day do you take Chlorthalidone?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Chlorthalidone Doses Per Day:", "customConditional": "show = data.chlorthalidone_dosage !== 'combination_unsure' && data.diuretics?.chlorthalidone;"}, {"key": "indapamide_dosage", "data": {"values": [{"label": "1.25 mg", "value": "1_25mg"}, {"label": "2.5 mg", "value": "2_5mg"}, {"label": "Combination pill format / unsure", "value": "combination_unsure"}]}, "type": "select", "input": true, "label": "What is your current pill strength for Indapamide?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Indapamide Pill Strength:", "customConditional": "show = data.diuretics?.indapamide === true;"}, {"key": "indapamide_tablet_amount", "data": {"values": [{"label": "1/4 tablet", "value": "quarter"}, {"label": "1/2 tablet", "value": "half"}, {"label": "1 full tablet", "value": "full"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "How much of the Indapamide tablet do you take per dose?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Indapamide Tablet Amount:", "customConditional": "show = data.indapamide_dosage !== 'combination_unsure' && data.diuretics?.indapamide;"}, {"key": "indapamide_doses_per_day", "data": {"values": [{"label": "Once daily", "value": "once"}, {"label": "Twice daily", "value": "twice"}, {"label": "Three times daily", "value": "thrice"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "How many times a day do you take Indapamide?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Indapamide Doses Per Day:", "customConditional": "show = data.indapamide_dosage !== 'combination_unsure' && data.diuretics?.indapamide;"}, {"key": "furosemide_dosage", "data": {"values": [{"label": "20 mg", "value": "20mg"}, {"label": "40 mg", "value": "40mg"}, {"label": "80 mg", "value": "80mg"}, {"label": "Combination pill format / unsure", "value": "combination_unsure"}]}, "type": "select", "input": true, "label": "What is your current pill strength for Furosemide?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "<PERSON>rose<PERSON><PERSON> Strength:", "customConditional": "show = data.diuretics?.furosemide === true;"}, {"key": "furosemide_tablet_amount", "data": {"values": [{"label": "1/4 tablet", "value": "quarter"}, {"label": "1/2 tablet", "value": "half"}, {"label": "1 full tablet", "value": "full"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "How much of the Furosemide tablet do you take per dose?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Furosemide Tablet Amount:", "customConditional": "show = data.furosemide_dosage !== 'combination_unsure' && data.diuretics?.furosemide;"}, {"key": "furosemide_doses_per_day", "data": {"values": [{"label": "Once daily", "value": "once"}, {"label": "Twice daily", "value": "twice"}, {"label": "Three times daily", "value": "thrice"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "How many times a day do you take Furosemide?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Furosemide Doses Per Day:", "customConditional": "show = data.furosemide_dosage !== 'combination_unsure' && data.diuretics?.furosemide;"}, {"key": "spironolactone_dosage", "data": {"values": [{"label": "25 mg", "value": "25mg"}, {"label": "50 mg", "value": "50mg"}, {"label": "100 mg", "value": "100mg"}, {"label": "Combination pill format / unsure", "value": "combination_unsure"}]}, "type": "select", "input": true, "label": "What is your current pill strength for Spironolactone?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Spironolactone Pill Strength:", "customConditional": "show = data.diuretics?.spironolactone === true;"}, {"key": "spironolactone_tablet_amount", "data": {"values": [{"label": "1/4 tablet", "value": "quarter"}, {"label": "1/2 tablet", "value": "half"}, {"label": "1 full tablet", "value": "full"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "How much of the Spironolactone tablet do you take per dose?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Spironolactone Tablet Amount:", "customConditional": "show = data.spironolactone_dosage !== 'combination_unsure' && data.diuretics?.spironolactone;"}, {"key": "spironolactone_doses_per_day", "data": {"values": [{"label": "Once daily", "value": "once"}, {"label": "Twice daily", "value": "twice"}, {"label": "Three times daily", "value": "thrice"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "How many times a day do you take Spironolactone?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Spironolactone Doses Per Day:", "customConditional": "show = data.spironolactone_dosage !== 'combination_unsure' && data.diuretics?.spironolactone;"}, {"key": "beta_blockers", "type": "selectboxes", "input": true, "label": "Are you currently taking any of the following medications as <strong>separate pills</strong> (not as part of a combination pill)?", "values": [{"label": "Metoprolol", "value": "metoprolol"}, {"label": "Atenolol", "value": "atenolol"}, {"label": "Bisoprolol", "value": "bisoprolol"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>"}, {"label": "Nebivolol", "value": "nebivolol"}, {"label": "Propranolol", "value": "propranolol"}, {"label": "None of the above", "value": "none_of_the_above"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Current Beta Blockers:", "customConditional": "show = data.medication_type === 'individual' || data.medication_type === 'both';"}, {"key": "metoprolol_dosage", "data": {"values": [{"label": "25 mg", "value": "25mg"}, {"label": "50 mg", "value": "50mg"}, {"label": "100 mg", "value": "100mg"}, {"label": "200 mg (Extended Release)", "value": "200mg_er"}, {"label": "Combination pill format / unsure", "value": "combination_unsure"}]}, "type": "select", "input": true, "label": "What is your current pill strength for Metoprolol?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Metoprolol Pill Strength:", "customConditional": "show = data.beta_blockers?.metoprolol === true;"}, {"key": "metoprolol_tablet_amount", "data": {"values": [{"label": "1/4 tablet", "value": "quarter"}, {"label": "1/2 tablet", "value": "half"}, {"label": "1 full tablet", "value": "full"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "How much of the Metoprolol tablet do you take per dose?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Metoprolol Tablet Amount:", "customConditional": "show = data.metoprolol_dosage !== 'combination_unsure' && data.beta_blockers?.metoprolol;"}, {"key": "metoprolol_doses_per_day", "data": {"values": [{"label": "Once daily", "value": "once"}, {"label": "Twice daily", "value": "twice"}, {"label": "Three times daily", "value": "thrice"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "How many times a day do you take Metoprolol?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Metoprolol Doses Per Day:", "customConditional": "show = data.metoprolol_dosage !== 'combination_unsure' && data.beta_blockers?.metoprolol;"}, {"key": "atenolol_dosage", "data": {"values": [{"label": "25 mg", "value": "25mg"}, {"label": "50 mg", "value": "50mg"}, {"label": "100 mg", "value": "100mg"}, {"label": "Combination pill format / unsure", "value": "combination_unsure"}]}, "type": "select", "input": true, "label": "What is your current pill strength for Atenolol?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Atenolol Pill Strength:", "customConditional": "show = data.beta_blockers?.atenolol === true;"}, {"key": "atenolol_tablet_amount", "data": {"values": [{"label": "1/4 tablet", "value": "quarter"}, {"label": "1/2 tablet", "value": "half"}, {"label": "1 full tablet", "value": "full"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "How much of the Atenolol tablet do you take per dose?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Atenolol Tablet Amount:", "customConditional": "show = data.atenolol_dosage !== 'combination_unsure' && data.beta_blockers?.atenolol;"}, {"key": "atenolol_doses_per_day", "data": {"values": [{"label": "Once daily", "value": "once"}, {"label": "Twice daily", "value": "twice"}, {"label": "Three times daily", "value": "thrice"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "How many times a day do you take Atenolol?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Atenolol Doses Per Day:", "customConditional": "show = data.atenolol_dosage !== 'combination_unsure' && data.beta_blockers?.atenolol;"}, {"key": "bisoprolol_dosage", "data": {"values": [{"label": "2.5 mg", "value": "2_5mg"}, {"label": "5 mg", "value": "5mg"}, {"label": "10 mg", "value": "10mg"}, {"label": "Combination pill format / unsure", "value": "combination_unsure"}]}, "type": "select", "input": true, "label": "What is your current pill strength for Bisoprolol?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Bisoprolol Pill Strength:", "customConditional": "show = data.beta_blockers?.bisoprolol === true;"}, {"key": "bisoprolol_tablet_amount", "data": {"values": [{"label": "1/4 tablet", "value": "quarter"}, {"label": "1/2 tablet", "value": "half"}, {"label": "1 full tablet", "value": "full"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "How much of the Bisoprolol tablet do you take per dose?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Bisoprolol Tablet Amount:", "customConditional": "show = data.bisoprolol_dosage !== 'combination_unsure' && data.beta_blockers?.bisoprolol;"}, {"key": "bisoprolol_doses_per_day", "data": {"values": [{"label": "Once daily", "value": "once"}, {"label": "Twice daily", "value": "twice"}, {"label": "Three times daily", "value": "thrice"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "How many times a day do you take Bisoprolol?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Bisoprolol Doses Per Day:", "customConditional": "show = data.bisoprolol_dosage !== 'combination_unsure' && data.beta_blockers?.bisoprolol;"}, {"key": "carvedilol_dosage", "data": {"values": [{"label": "3.125 mg", "value": "3_125mg"}, {"label": "6.25 mg", "value": "6_25mg"}, {"label": "12.5 mg", "value": "12_5mg"}, {"label": "25 mg", "value": "25mg"}, {"label": "Combination pill format / unsure", "value": "combination_unsure"}]}, "type": "select", "input": true, "label": "What is your current pill strength for Carvedilol?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "<PERSON><PERSON><PERSON><PERSON> Strength:", "customConditional": "show = data.beta_blockers?.carvedilol === true;"}, {"key": "carvedilol_tablet_amount", "data": {"values": [{"label": "1/4 tablet", "value": "quarter"}, {"label": "1/2 tablet", "value": "half"}, {"label": "1 full tablet", "value": "full"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "How much of the Carvedilol tablet do you take per dose?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "<PERSON><PERSON><PERSON><PERSON> Strength:", "customConditional": "show = data.carvedilol_dosage !== 'combination_unsure' && data.beta_blockers?.carvedilol;"}, {"key": "carvedilol_doses_per_day", "data": {"values": [{"label": "Once daily", "value": "once"}, {"label": "Twice daily", "value": "twice"}, {"label": "Three times daily", "value": "thrice"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "How many times a day do you take Carvedilol?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Carvedilol Doses Per Day:", "customConditional": "show = data.carvedilol_dosage !== 'combination_unsure' && data.beta_blockers?.carvedilol;"}, {"key": "nebivolol_dosage", "data": {"values": [{"label": "2.5 mg", "value": "2_5mg"}, {"label": "5 mg", "value": "5mg"}, {"label": "10 mg", "value": "10mg"}, {"label": "20 mg", "value": "20mg"}, {"label": "Combination pill format / unsure", "value": "combination_unsure"}]}, "type": "select", "input": true, "label": "What is your current pill strength for Nebivolol?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Nebivolol Pill Strength:", "customConditional": "show = data.beta_blockers?.nebivolol === true;"}, {"key": "nebivolol_tablet_amount", "data": {"values": [{"label": "1/4 tablet", "value": "quarter"}, {"label": "1/2 tablet", "value": "half"}, {"label": "1 full tablet", "value": "full"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "How much of the Nebivolol tablet do you take per dose?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Nebivolol Tablet Amount:", "customConditional": "show = data.nebivolol_dosage !== 'combination_unsure' && data.beta_blockers?.nebivolol;"}, {"key": "nebivolol_doses_per_day", "data": {"values": [{"label": "Once daily", "value": "once"}, {"label": "Twice daily", "value": "twice"}, {"label": "Three times daily", "value": "thrice"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "How many times a day do you take Nebivolol?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Nebivolol Doses Per Day:", "customConditional": "show = data.nebivolol_dosage !== 'combination_unsure' && data.beta_blockers?.nebivolol;"}, {"key": "propranolol_dosage", "data": {"values": [{"label": "10 mg", "value": "10mg"}, {"label": "20 mg", "value": "20mg"}, {"label": "40 mg", "value": "40mg"}, {"label": "60 mg", "value": "60mg"}, {"label": "80 mg", "value": "80mg"}, {"label": "120 mg (Extended Release)", "value": "120mg_er"}, {"label": "160 mg (Extended Release)", "value": "160mg_er"}, {"label": "Combination pill format / unsure", "value": "combination_unsure"}]}, "type": "select", "input": true, "label": "What is your current pill strength for Propranolol?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Propranolol Pill Strength:", "customConditional": "show = data.beta_blockers?.propranolol === true;"}, {"key": "propranolol_tablet_amount", "data": {"values": [{"label": "1/4 tablet", "value": "quarter"}, {"label": "1/2 tablet", "value": "half"}, {"label": "1 full tablet", "value": "full"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "How much of the Propranolol tablet do you take per dose?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Propranolol Tablet Amount:", "customConditional": "show = data.propranolol_dosage !== 'combination_unsure' && data.beta_blockers?.propranolol;"}, {"key": "propranolol_doses_per_day", "data": {"values": [{"label": "Once daily", "value": "once"}, {"label": "Twice daily", "value": "twice"}, {"label": "Three times daily", "value": "thrice"}, {"label": "As needed", "value": "prn"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "How many times a day do you take Propranolol?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Propranolol Doses Per Day:", "customConditional": "show = data.propranolol_dosage !== 'combination_unsure' && data.beta_blockers?.propranolol;"}, {"key": "heading_symptoms", "html": "<h2>Current Symptoms and Medication Side Effects</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "cardiovascular_symptoms", "type": "selectboxes", "input": true, "label": "Have you had any of the following symptoms today or recently?", "values": [{"label": "Chest pain or discomfort", "value": "chest_pain", "shortcut": ""}, {"label": "Palpitations", "value": "palpitations", "shortcut": ""}, {"label": "Swelling in the legs, ankles, or feet", "value": "swelling", "shortcut": ""}, {"label": "Dizziness or fainting", "value": "dizziness", "shortcut": ""}], "adminFlag": true, "tableView": true, "confirm_label": "Current Cardiovascular Symptoms:", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_cardiovascular_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = !!data.none_of_the_above_cardiovascular_symptoms || _.some(_.values(data.cardiovascular_symptoms));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "cardiac_symptoms_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following cardiac symptoms:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "I DO NOT have the following heart related symptoms:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.cardiovascular_symptoms, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "respiratory_symptoms", "type": "selectboxes", "input": true, "label": "Have you had any of the following respiratory symptoms recently or today?", "values": [{"label": "<PERSON><PERSON>", "value": "cough", "shortcut": ""}, {"label": "Shortness of breath", "value": "shortness_of_breath", "shortcut": ""}, {"label": "Wheezing", "value": "wheezing", "shortcut": ""}, {"label": "Chest tightness", "value": "chest_tightness", "shortcut": ""}], "adminFlag": true, "tableView": true, "confirm_label": "Current Respiratory Symptoms:", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_respiratory_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = !!data.none_of_the_above_respiratory_symptoms || _.some(_.values(data.respiratory_symptoms));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "respiratory_symptoms_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following respiratory symptoms:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "I DO NOT have the following breathing related symptoms:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.respiratory_symptoms, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "heading_lab_testing", "html": "<h2><strong>Lab Testing</strong></h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "prior_tests_completed", "type": "selectboxes", "input": true, "label": "Have you had any of the following tests completed?", "values": [{"label": "Kidney function (eGFR)", "value": "egfr"}, {"label": "Urine <PERSON>in-to-Crea<PERSON>ine <PERSON> (ACR)", "value": "urine_acr"}, {"label": "Lipid Profile (Cholesterol levels)", "value": "lipid_profile"}, {"label": "Diabetes Testing (HbA1c)", "value": "a1c"}, {"label": "Fasting Blood Glucose (FBG)", "value": "fasting_glucose"}, {"label": "CBC (Complete Blood Count)", "value": "cbc"}, {"label": "I have not had these tests completed", "value": "no_prior_tests"}], "inputType": "checkbox", "tableView": true, "confirm_label": "Prior Tests Completed:", "optionsLabelPosition": "right"}, {"key": "last_known_lab_timing", "data": {"values": [{"label": "<1 month ago", "value": "Less than 1 month ago"}, {"label": "1-2 months ago", "value": "1-2 months ago"}, {"label": "2-3 months ago", "value": "2-3 months ago"}, {"label": "3-4 months ago", "value": "3-4 months ago"}, {"label": "4-5 months ago", "value": "4-5 months ago"}, {"label": "5-6 months ago", "value": "5-6 months ago"}, {"label": "6-12 months ago", "value": "6-12 months ago"}, {"label": "12-24 months ago", "value": "12-24 months ago"}, {"label": "24+ months ago", "value": "More than 24 months ago"}, {"label": "I haven't had lab testing", "value": "Haven't had lab testing"}]}, "type": "select", "input": true, "label": "Do you recall when your last set of lab tests (bloodwork) was completed?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Last Known Lab Timing:", "customConditional": "show = _.some(_.omit(data.prior_tests_completed || {}, ['no_prior_tests']));"}, {"key": "heading_kidney_function", "html": "<h3>Kidney Function (eGFR)</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.prior_tests_completed?.egfr;"}, {"key": "last_kidney_function_test", "data": {"values": [{"label": "<1 month ago", "value": "Less than 1 month ago"}, {"label": "1-2 months ago", "value": "1-2 months ago"}, {"label": "2-3 months ago", "value": "2-3 months ago"}, {"label": "3-4 months ago", "value": "3-4 months ago"}, {"label": "4-5 months ago", "value": "4-5 months ago"}, {"label": "5-6 months ago", "value": "5-6 months ago"}, {"label": "6-12 months ago", "value": "6-12 months ago"}, {"label": "12-24 months ago", "value": "12-24 months ago"}, {"label": "24+ months ago", "value": "More than 24 months ago"}, {"label": "Never had one", "value": "Never had one"}]}, "type": "select", "input": true, "label": "When was your last kidney function test completed?", "widget": "html5", "validate": {"required": true}, "tableView": true, "placeholder": "Select the time range", "confirm_label": "Last Kidney Function Test:", "customConditional": "show = data.prior_tests_completed?.egfr;"}, {"key": "prior_kidney_function_value", "data": {"values": [{"label": "I don't remember my result", "value": "I don't remember my result"}, {"label": "eGFR > 90", "value": "eGFR > 90"}, {"label": "eGFR 85-89", "value": "eGFR 85-89"}, {"label": "eGFR 80-84", "value": "eGFR 80-84"}, {"label": "eGFR 75-79", "value": "eGFR 75-79"}, {"label": "eGFR 70-74", "value": "eGFR 70-74"}, {"label": "eGFR 65-69", "value": "eGFR 65-69"}, {"label": "eGFR 60-64", "value": "eGFR 60-64"}, {"label": "eGFR 55-59", "value": "eGFR 55-59"}, {"label": "eGFR 50-54", "value": "eGFR 50-54"}, {"label": "eGFR 45-49", "value": "eGFR 45-49"}, {"label": "eGFR < 45", "value": "eGFR < 45"}]}, "type": "select", "input": true, "label": "What was your most recent eGFR measurement?", "widget": "html5", "validate": {"required": true}, "tableView": true, "placeholder": "Select the value range", "confirm_label": "eGFR Value Range:", "customConditional": "show = data.prior_tests_completed?.egfr;"}, {"key": "heading_urine_acr", "html": "</br><h3>Urine Albumin-to-Creatinine Ratio (ACR)</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.prior_tests_completed?.urine_acr;"}, {"key": "last_urine_acr_test", "data": {"values": [{"label": "<1 month ago", "value": "Less than 1 month ago"}, {"label": "1-2 months ago", "value": "1-2 months ago"}, {"label": "2-3 months ago", "value": "2-3 months ago"}, {"label": "3-4 months ago", "value": "3-4 months ago"}, {"label": "4-5 months ago", "value": "4-5 months ago"}, {"label": "5-6 months ago", "value": "5-6 months ago"}, {"label": "6-12 months ago", "value": "6-12 months ago"}, {"label": "12-24 months ago", "value": "12-24 months ago"}, {"label": "24+ months ago", "value": "More than 24 months ago"}, {"label": "Never had one", "value": "Never had one"}]}, "type": "select", "input": true, "label": "When was your last urine ACR test completed?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Last Urine ACR Test:", "customConditional": "show = data.prior_tests_completed?.urine_acr;"}, {"key": "heading_lipid_profile", "html": "</br><h3>Lipid Profile (Cholesterol)</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.prior_tests_completed?.lipid_profile;"}, {"key": "last_lipid_profile_test", "data": {"values": [{"label": "<1 month ago", "value": "Less than 1 month ago"}, {"label": "1-2 months ago", "value": "1-2 months ago"}, {"label": "2-3 months ago", "value": "2-3 months ago"}, {"label": "3-4 months ago", "value": "3-4 months ago"}, {"label": "4-5 months ago", "value": "4-5 months ago"}, {"label": "5-6 months ago", "value": "5-6 months ago"}, {"label": "6-12 months ago", "value": "6-12 months ago"}, {"label": "12-24 months ago", "value": "12-24 months ago"}, {"label": "24+ months ago", "value": "More than 24 months ago"}, {"label": "Never had one", "value": "Never had one"}]}, "type": "select", "input": true, "label": "When was your last lipid profile test completed?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Last Lipid Profile Test:", "customConditional": "show = data.prior_tests_completed?.lipid_profile;"}, {"key": "heading_lipid_profile_results", "html": "</br><h3>Lipid Profile Results</h3><p>Select any abnormalities found in your test results.</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.last_lipid_profile_test !== undefined;"}, {"key": "lipid_profile_abnormalities", "type": "selectboxes", "input": true, "label": "Were any of the following findings reported in your lipid profile?", "values": [{"label": "High LDL", "value": "high_ldl"}, {"label": "High Triglycerides", "value": "high_triglycerides"}, {"label": "High HDL", "value": "high_hdl"}, {"label": "Normal HDL", "value": "normal_hdl"}, {"label": "Normal lipid profile", "value": "normal_profile"}, {"label": "I don't remember my values", "value": "dont_remember"}, {"label": "I don't remember them but was told they were normal", "value": "dont_remember_normal"}], "tooltip": "Select all that apply based on your most recent test results.", "validate": {"required": true}, "tableView": true, "customClass": "mt-n3", "confirm_label": "Lipid Profile Abnormalities:", "customConditional": "show = data.last_lipid_profile_test !== undefined;"}, {"key": "heading_a1c", "html": "</br><h3>HbA1c (Diabetes Test)</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.prior_tests_completed?.a1c === true;"}, {"key": "last_a1c_test", "data": {"values": [{"label": "<1 month ago", "value": "Less than 1 month ago"}, {"label": "1-2 months ago", "value": "1-2 months ago"}, {"label": "2-3 months ago", "value": "2-3 months ago"}, {"label": "3-4 months ago", "value": "3-4 months ago"}, {"label": "4-5 months ago", "value": "4-5 months ago"}, {"label": "5-6 months ago", "value": "5-6 months ago"}, {"label": "6-12 months ago", "value": "6-12 months ago"}, {"label": "12-24 months ago", "value": "12-24 months ago"}, {"label": "24+ months ago", "value": "More than 24 months ago"}, {"label": "Never had one", "value": "Never had one"}]}, "type": "select", "input": true, "label": "When was your last HbA1c test completed?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Last A1c Test:", "customConditional": "show = data.prior_tests_completed?.a1c === true;"}, {"key": "recent_a1c_value", "data": {"values": [{"label": "I don't remember my result", "value": "dont_remember"}, {"label": "Below 5.7%", "value": "below_5_7"}, {"label": "5.7% - 6.4%", "value": "5_7_6_4"}, {"label": "6.5% - 6.9%", "value": "6_5_6_9"}, {"label": "7.0% - 7.9%", "value": "7_0_7_9"}, {"label": "8.0% - 8.9%", "value": "8_0_8_9"}, {"label": "9.0% - 9.9%", "value": "9_0_9_9"}, {"label": "10.0% or higher", "value": "10_or_higher"}]}, "type": "select", "input": true, "label": "What was your most recent HbA1c result?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Recent A1c Value:", "customConditional": "show = data.prior_tests_completed?.a1c === true;"}, {"key": "last_fasting_glucose_test", "data": {"values": [{"label": "<1 month ago", "value": "Less than 1 month ago"}, {"label": "1-2 months ago", "value": "1-2 months ago"}, {"label": "2-3 months ago", "value": "2-3 months ago"}, {"label": "3-4 months ago", "value": "3-4 months ago"}, {"label": "4-5 months ago", "value": "4-5 months ago"}, {"label": "5-6 months ago", "value": "5-6 months ago"}, {"label": "6-12 months ago", "value": "6-12 months ago"}, {"label": "12-24 months ago", "value": "12-24 months ago"}, {"label": "24+ months ago", "value": "More than 24 months ago"}, {"label": "Never had one", "value": "never_had_one"}]}, "type": "select", "input": true, "label": "When was your last fasting blood glucose test completed?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Last Fasting Glucose Test:", "customConditional": "show = data.prior_tests_completed?.fasting_glucose === true;"}, {"key": "recent_fbg_value", "data": {"values": [{"label": "I don't remember my result", "value": "dont_remember"}, {"label": "Below 5.6 mmol/L [<100 mg/dL]", "value": "Below 5.6 mmol/L"}, {"label": "5.6 - 6.9 mmol/L [100-125 mg/dL]", "value": "5.6 - 6.9 mmol/L"}, {"label": "7.0 - 7.9 mmol/L [126-142 mg/dL]", "value": "7.0 - 7.9 mmol/L"}, {"label": "8.0 - 8.9 mmol/L [143-160 mg/dL]", "value": "8.0 - 8.9 mmol/L"}, {"label": "9.0 - 9.9 mmol/L [161-178 mg/dL]", "value": "9.0 - 9.9 mmol/L"}, {"label": "10.0 - 11.9 mmol/L [179-214 mg/dL]", "value": "10.0 - 11.9 mmol/L"}, {"label": "12.0+ mmol/L [215+ mg/dL]", "value": "12.0+ mmol/L"}]}, "type": "select", "input": true, "label": "What was your most recent <strong>fasting blood glucose</strong> (FBG) result?", "widget": "html5", "validate": {"required": true}, "tableView": true, "description": "Select the range that matches your lab result. Canadian units shown (mmol/L), with U.S. units in [mg/dL].", "confirm_label": "Recent FBG Value:", "customConditional": "show = data.prior_tests_completed?.fasting_glucose === true && data.last_fasting_glucose_test !== 'never_had_one';"}, {"key": "heading_cbc", "html": "<h3>CBC (Complete Blood Count)</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.prior_tests_completed?.cbc;"}, {"key": "last_cbc_test", "data": {"values": [{"label": "<1 month ago", "value": "Less than 1 month ago"}, {"label": "1-2 months ago", "value": "1-2 months ago"}, {"label": "2-3 months ago", "value": "2-3 months ago"}, {"label": "3-4 months ago", "value": "3-4 months ago"}, {"label": "4-5 months ago", "value": "4-5 months ago"}, {"label": "5-6 months ago", "value": "5-6 months ago"}, {"label": "6-12 months ago", "value": "6-12 months ago"}, {"label": "12-24 months ago", "value": "12-24 months ago"}, {"label": "24+ months ago", "value": "More than 24 months ago"}, {"label": "Never had one", "value": "Never had one"}]}, "type": "select", "input": true, "label": "When was your last CBC test completed?", "widget": "html5", "validate": {"required": true}, "tableView": true, "placeholder": "Select the time range", "confirm_label": "Last CBC Test:", "customConditional": "show = data.prior_tests_completed?.cbc;"}, {"key": "prior_cbc_value", "data": {"values": [{"label": "I don't remember my result", "value": "I don't remember my result"}, {"label": "< 70 g/L", "value": "Less than 70 g/L"}, {"label": "70-100 g/L", "value": "70-100 g/L"}, {"label": "101-120 g/L", "value": "101-120 g/L"}, {"label": "121-150 g/L", "value": "121-150 g/L"}, {"label": "> 150 g/L", "value": "Greater than 150 g/L"}]}, "type": "select", "input": true, "label": "What was your most recent hemoglobin level (CBC result)?", "widget": "html5", "validate": {"required": true}, "tableView": true, "placeholder": "Select your hemoglobin level", "confirm_label": "Hemoglobin Level:", "customConditional": "show = data.prior_tests_completed?.cbc;"}, {"key": "heading_imaging_tests", "html": "</br><h2><strong>Imaging & Cardiac Tests</strong></h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "prior_imaging_tests", "type": "selectboxes", "input": true, "label": "Have you had any of the following imaging or cardiac tests completed?", "values": [{"label": "Electrocardiogram (ECG/EKG)", "value": "ecg"}, {"label": "Stress Test (Exercise or Chemical)", "value": "stress_test"}, {"label": "Echocardiogram (Heart Ultrasound)", "value": "echocardiogram"}, {"label": "I have not had these tests completed", "value": "no_prior_imaging"}], "inputType": "checkbox", "tableView": true, "confirm_label": "Prior Imaging Tests:", "optionsLabelPosition": "right"}, {"key": "heading_ecg", "html": "<h3>Electrocardiogram (ECG/EKG)</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.prior_imaging_tests?.ecg;"}, {"key": "last_ecg_test", "data": {"values": [{"label": "<1 month ago", "value": "Less than 1 month ago"}, {"label": "1-2 months ago", "value": "1-2 months ago"}, {"label": "2-3 months ago", "value": "2-3 months ago"}, {"label": "3-4 months ago", "value": "3-4 months ago"}, {"label": "4-5 months ago", "value": "4-5 months ago"}, {"label": "5-6 months ago", "value": "5-6 months ago"}, {"label": "6-12 months ago", "value": "6-12 months ago"}, {"label": "12-24 months ago", "value": "12-24 months ago"}, {"label": "24+ months ago", "value": "More than 24 months ago"}, {"label": "Never had one", "value": "Never had one"}]}, "type": "select", "input": true, "label": "When was your last ECG test completed?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Last ECG Test:", "customConditional": "show = data.prior_imaging_tests?.ecg;"}, {"key": "ecg_findings", "type": "selectboxes", "input": true, "label": "Were any of the following findings reported in your ECG?", "values": [{"label": "Normal ECG", "value": "normal_ecg"}, {"label": "Atrial fibrillation", "value": "afib"}, {"label": "Left ventricular hypertrophy (LVH)", "value": "lvh"}, {"label": "Bundle branch block", "value": "bbb"}, {"label": "ST-T wave abnormalities", "value": "st_t_wave"}, {"label": "I was told it was abnormal but don't know why", "value": "abnormal_unknown"}, {"label": "I don't remember, but I was told it was normal", "value": "dont_remember_normal"}, {"label": "Other", "value": "other"}], "tableView": true, "confirm_label": "ECG Findings:", "customConditional": "show = data.prior_imaging_tests?.ecg === true;"}, {"key": "ecg_other_findings", "type": "textarea", "input": true, "label": "Please describe any other ECG findings:", "tableView": true, "autoExpand": true, "customConditional": "show = data.ecg_findings?.other;"}, {"key": "heading_stress_test", "html": "<h3>Stress Test</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.prior_imaging_tests?.stress_test;"}, {"key": "stress_test_findings", "type": "selectboxes", "input": true, "label": "Were you told any of the following about your stress test results?", "values": [{"label": "It was normal", "value": "normal_stress_test"}, {"label": "There were signs of poor blood flow to the heart during activity", "value": "ischemia"}, {"label": "Your heart rhythm became irregular during the test", "value": "arrhythmia"}, {"label": "You were unable to exercise as long as expected", "value": "low_tolerance"}, {"label": "I was told it was abnormal but not given a clear reason", "value": "abnormal_unknown"}, {"label": "I don't remember the details, but I was told it was normal", "value": "dont_remember_normal"}, {"label": "Other", "value": "other"}], "tableView": true, "confirm_label": "Stress Test Findings:", "customConditional": "show = data.prior_imaging_tests?.stress_test === true;"}, {"key": "stress_test_other_findings", "type": "textarea", "input": true, "label": "Please describe any other stress test findings:", "tableView": true, "autoExpand": true, "confirm_label": "Stress Test Other Findings:", "customConditional": "show = data.stress_test_findings?.other;"}, {"key": "heading_echocardiogram", "html": "<h3>Echocardiogram</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.prior_imaging_tests?.echocardiogram;"}, {"key": "echocardiogram_findings", "type": "selectboxes", "input": true, "label": "Were any of the following findings reported in your echocardiogram?", "values": [{"label": "Normal echocardiogram", "value": "normal_echo"}, {"label": "Left ventricular hypertrophy (LVH)", "value": "lvh"}, {"label": "Reduced ejection fraction (EF)", "value": "low_ef"}, {"label": "Valve abnormalities", "value": "valve_disease"}, {"label": "I was told it was abnormal but don't know why", "value": "abnormal_unknown"}, {"label": "I don't remember, but I was told it was normal", "value": "dont_remember_normal"}, {"label": "Other", "value": "other"}], "tableView": true, "confirm_label": "Echocardiogram Findings:", "customConditional": "show = data.prior_imaging_tests?.echocardiogram === true;"}, {"key": "echocardiogram_other_findings", "type": "textarea", "input": true, "label": "Please describe any other echocardiogram findings:", "tableView": true, "autoExpand": true, "confirm_label": "Echocardiogram Other Findings:", "customConditional": "show = data.echocardiogram_findings?.other;"}, {"key": "bp_monitoring_frequency", "type": "radio", "input": true, "label": "How often do you check your blood pressure at home?", "values": [{"label": "Daily", "value": "daily"}, {"label": "A few times a week", "value": "few_times_week"}, {"label": "Once a week", "value": "weekly"}, {"label": "Occasionally / infrequently", "value": "infrequent"}, {"label": "Never check at home", "value": "never"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Blood Pressure Monitoring Frequency:"}, {"key": "bp_readings_per_sitting", "type": "radio", "input": true, "label": "How many blood pressure readings do you typically take in a single sitting?", "values": [{"label": "1 reading", "value": "1"}, {"label": "2 readings", "value": "2"}, {"label": "3 readings", "value": "3"}, {"label": "More than 3 readings", "value": "more_than_3"}, {"label": "Not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = !!data.bp_monitoring_frequency;"}, {"key": "heading_blood_pressure", "html": "<h2><strong>Blood Pressure Reading</strong></h2><p>Please enter your most recent blood pressure measurement (today, if possible).</p>", "type": "content", "input": false, "label": "Content", "tableView": false}, {"key": "blood_pressure_row", "type": "columns", "input": false, "label": "Blood Pressure Input", "columns": [{"width": 3, "components": [{"key": "systolic_bp", "type": "number", "input": true, "label": "Systolic (top number)", "validate": {"max": 250, "min": 50}, "tableView": true, "placeholder": "e.g., 130", "confirm_label": "Systolic BP:"}]}, {"width": 3, "components": [{"key": "diastolic_bp", "type": "number", "input": true, "label": "Diastolic (bottom number)", "validate": {"max": 150, "min": 30}, "tableView": true, "placeholder": "e.g., 80", "confirm_label": "Diastolic BP:"}]}]}, {"key": "bp_reading_date", "data": {"values": [{"label": "Today", "value": "today"}, {"label": "Within the last 48 hours", "value": "within_48h"}, {"label": "Within the last 7 days", "value": "within_7d"}, {"label": "Within the last 14 days", "value": "within_14d"}, {"label": "More than 30 days ago", "value": "more_than_30d"}]}, "type": "select", "input": true, "label": "When was this blood pressure reading taken?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "BP Reading Date:", "customConditional": "show = !!data.systolic_bp || !!data.diastolic_bp;"}, {"key": "bp_reading_confirmed", "type": "radio", "input": true, "label": "Are these blood pressure values accurate (based on your monitor reading today)?", "values": [{"label": "Yes, this is my accurate reading", "value": "yes"}, {"label": "No, I made a mistake or I'm not sure", "value": "no"}], "validate": {"required": true}, "tableView": true, "confirm_label": "BP Reading Confirmed:", "customConditional": "show = data.systolic_bp > 180 || data.diastolic_bp > 100;"}, {"key": "bp_symptoms_check", "type": "selectboxes", "input": true, "label": "Are you currently experiencing any of the following symptoms?", "values": [{"label": "Chest pain or pressure or tightness", "value": "chest_pain", "shortcut": ""}, {"label": "A headache", "value": "headache", "shortcut": ""}, {"label": "Shortness of breath", "value": "shortness_of_breath", "shortcut": ""}, {"label": "Trouble speaking or moving", "value": "neuro", "shortcut": ""}, {"label": "Dizziness or lightheadedness", "value": "dizziness", "shortcut": ""}, {"label": "Balance difficulties (difficulties with walking or coordinating)", "value": "balance", "shortcut": ""}, {"label": "Eye changes (double vision, loss of vision or blurry vision)", "value": "eyes", "shortcut": ""}, {"label": "A droopy face", "value": "face", "shortcut": ""}, {"label": "Arm/leg weakness or numbness", "value": "arm", "shortcut": ""}, {"label": "Speech difficulty or slurring", "value": "speech", "shortcut": ""}], "adminFlag": true, "tableView": true, "confirm_label": "BP Symptoms Check:", "customConditional": "show = data.bp_reading_confirmed === 'yes' && ((data.systolic_bp > 180 && data.systolic_bp <= 200) || (data.diastolic_bp > 100 && data.diastolic_bp <= 110));", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_bp_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = !!data.none_of_the_above_bp_symptoms || _.some(_.values(data.bp_symptoms_check));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.bp_reading_confirmed === 'yes' && ((data.systolic_bp > 180 && data.systolic_bp <= 200) || (data.diastolic_bp > 100 && data.diastolic_bp <= 110));"}, {"key": "bp_symptoms_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following BP-related symptoms:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "I DO NOT have the following high blood pressure related symptoms:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.bp_symptoms_check, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "bp_urgency_advice", "html": "<div style='background:#fff3cd; padding:10px; border-left:5px solid #ffeeba;'><strong>Important:</strong> Your blood pressure is elevated. Please visit a walk-in clinic or urgent care today to confirm your reading and ensure proper management.</div>", "type": "content", "input": false, "label": "BP Urgency Advisory", "customConditional": "show = data.bp_reading_confirmed === 'yes' && ((data.systolic_bp > 180 && data.systolic_bp <= 200) || (data.diastolic_bp > 100 && data.diastolic_bp <= 110)) && !Object.values(data.bp_symptoms_check || {}).includes(true);"}, {"key": "bp_emergency_advice", "html": "<div style='background:#f8d7da; padding:10px; border-left:5px solid #f5c6cb;'><strong>Emergency Alert:</strong> Your blood pressure is elevated sufficiently and/or symptoms suggest a medical emergency. Please call 911 and go to the <strong>nearest emergency department immediately</strong> to have your blood pressure checked and treated safely. If your blood pressure values were entered in error, you can revise them</div>", "type": "content", "input": false, "label": "BP Emergency Advisory", "customConditional": "show = data.bp_reading_confirmed === 'yes' && ((data.systolic_bp > 200 || data.diastolic_bp > 110) || Object.values(data.bp_symptoms_check || {}).includes(true));"}, {"key": "bp_low_emergency_advice", "html": "<div style='background:#f8d7da; padding:10px; border-left:5px solid #f5c6cb;'><strong>Emergency Alert:</strong> Your blood pressure appears to be dangerously low. If you are feeling faint, dizzy, weak, or unwell, please go to the <strong>nearest emergency department immediately</strong>.</div>", "type": "content", "input": false, "label": "Low Blood Pressure Emergency Alert", "customConditional": "show = data.bp_reading_confirmed === 'yes' && (data.systolic_bp < 70 || data.diastolic_bp < 50);"}, {"key": "bp_warning_understanding", "type": "radio", "input": true, "label": "Do you understand this recommendation to seek immediate in-person care?", "values": [{"label": "I understand", "value": "understand"}, {"label": "I do not understand", "value": "do_not_understand"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Understands BP Emergency/Urgent Care Advice:", "customConditional": "show = (data.bp_reading_confirmed === 'yes' && ((data.systolic_bp > 180 || data.diastolic_bp > 100)))"}, {"key": "heading_in_office_monitoring", "html": "</br><h2><strong>In-Office Monitoring and Follow-Up</strong></h2><p>Please provide information about recent checkups and whether you have a regular healthcare provider.</p>", "type": "content", "input": false, "label": "Content", "tableView": false}, {"key": "last_office_bp_check", "data": {"values": [{"label": "<1 month ago", "value": "Less than 1 month ago"}, {"label": "1-2 months ago", "value": "1-2 months ago"}, {"label": "2-3 months ago", "value": "2-3 months ago"}, {"label": "3-4 months ago", "value": "3-4 months ago"}, {"label": "4-5 months ago", "value": "4-5 months ago"}, {"label": "5-6 months ago", "value": "5-6 months ago"}, {"label": "6-12 months ago", "value": "6-12 months ago"}, {"label": "12-24 months ago", "value": "12-24 months ago"}, {"label": "24+ months ago", "value": "More than 24 months ago"}, {"label": "Never / not sure", "value": "never_or_not_sure"}]}, "type": "select", "input": true, "label": "When was your last in-office blood pressure check (e.g. at a doctor's office or pharmacy)?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Last Office BP Check:"}, {"key": "clinic_vs_home_reading", "type": "radio", "input": true, "label": "Was the reading taken in clinic or pharmacy similar to your home readings?", "values": [{"label": "Yes - it was similar", "value": "similar"}, {"label": "No - it was higher", "value": "higher"}, {"label": "No - it was lower", "value": "lower"}, {"label": "I don't remember", "value": "dont_remember"}, {"label": "Not applicable", "value": "not_applicable"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Clinic vs Home Reading:", "customConditional": "show = !!data.last_office_bp_check && data.last_office_bp_check !== 'never_or_not_sure';"}, {"key": "last_physical_exam", "data": {"values": [{"label": "<1 month ago", "value": "Less than 1 month ago"}, {"label": "1-2 months ago", "value": "1-2 months ago"}, {"label": "2-3 months ago", "value": "2-3 months ago"}, {"label": "3-4 months ago", "value": "3-4 months ago"}, {"label": "4-5 months ago", "value": "4-5 months ago"}, {"label": "5-6 months ago", "value": "5-6 months ago"}, {"label": "6-12 months ago", "value": "6-12 months ago"}, {"label": "12-24 months ago", "value": "12-24 months ago"}, {"label": "24+ months ago", "value": "More than 24 months ago"}, {"label": "Never had one", "value": "Never had one"}, {"label": "Never / not sure", "value": "never_or_unknown"}]}, "type": "select", "input": true, "label": "When was your last in-office heart and lung physical exam (by a doctor or nurse practitioner)?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Last Physical Exam:"}, {"key": "has_regular_provider", "type": "radio", "input": true, "label": "Do you have a regular family doctor or nurse practitioner you see for exams?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Regular Provider:"}, {"key": "exam_interval_warning", "html": "<div style='background:#fff3cd; padding:10px; border-left:5px solid #ffeeba;'><strong>Reminder:</strong> It is recommended to have a heart and lung exam at least once every 6 months. If it's been more than 3 months, please consider scheduling a check-up with your doctor, nurse practitioner or visit a local walk-in clinic.</div>", "type": "content", "input": false, "label": "Exam Frequency Advice", "customConditional": "show = _.includes(['3-4 months ago','4-5 months ago','5-6 months ago','6-12 months ago','12-24 months ago','More than 24 months ago','Never had one','never_or_unknown'], data.last_physical_exam);"}, {"key": "intake_kind", "type": "textfield", "input": true, "label": "Intake Kind", "hidden": true, "disabled": true, "tableView": true, "clearOnHide": false, "calculateValue": "value = !data.intake_denial_reasons?.length ? 'mail' : 'chat';"}, {"key": "intake_mt_key", "type": "textfield", "input": true, "label": "Intake Mail Template Key", "hidden": true, "disabled": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.intake_kind=='mail'?'intake-htn-treatment':'intake-denial'"}, {"key": "allowed_rt_keys", "type": "textfield", "input": true, "label": "Allowed Rep<PERSON>", "hidden": true, "disabled": true, "multiple": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.intake_mt_key=='intake-denial'?[]:['get-req','appointment-intake','edit-intake']"}]}