{"components": [{"key": "sex", "type": "textfield", "input": true, "label": "Sex", "hidden": true, "disabled": true, "hideLabel": true, "tableView": false, "clearOnHide": false}, {"key": "birthyear", "type": "number", "input": true, "label": "Birthday", "hidden": true, "disabled": true, "hideLabel": true, "tableView": false, "clearOnHide": false, "requireDecimal": false}, {"key": "on_medications", "type": "radio", "input": true, "label": "Are you on any medications (this includes birth control or IUDs)?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": false, "optionsLabelPosition": "right"}, {"key": "medications_list", "type": "textarea", "input": true, "label": "Please list your medications here (i.e. Nexium 40mg)", "validate": {"required": true}, "tableView": false, "autoExpand": false, "customConditional": "show = data.on_medications == true;"}, {"key": "medications", "type": "textfield", "input": true, "label": "Medications", "hidden": true, "disabled": true, "hideLabel": true, "tableView": true, "clearOnHide": false, "calculateValue": "value = !data.on_medications ? 'None' : data.medications_list;"}, {"key": "on_supplements", "type": "radio", "input": true, "label": "Are you on any supplements or vitamins?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": false, "optionsLabelPosition": "right"}, {"key": "supplements_list", "type": "textarea", "input": true, "label": "Please list your supplements here (i.e. Vitamin D 1000 IU daily, multivitamin, St. John's Wort)", "validate": {"required": true}, "tableView": false, "autoExpand": false, "customConditional": "show = data.on_supplements == true;"}, {"key": "supplements", "type": "textfield", "input": true, "label": "Supplements", "hidden": true, "disabled": true, "hideLabel": true, "tableView": true, "clearOnHide": false, "calculateValue": "value = !data.on_supplements ? 'None' : data.supplements_list;"}, {"key": "has_medication_allergies", "type": "radio", "input": true, "label": "Do you have any medication allergies?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": false, "optionsLabelPosition": "right"}, {"key": "medication_allergies_list", "rows": 2, "type": "textarea", "input": true, "label": "Please list your medication allergies and reaction (i.e. Penicillin - Rash)", "validate": {"required": true}, "tableView": false, "autoExpand": false, "customConditional": "show = data.has_medication_allergies == true;"}, {"key": "medication_allergies", "type": "textfield", "input": true, "label": "Medication Allergies", "hidden": true, "disabled": true, "hideLabel": true, "tableView": true, "clearOnHide": false, "calculateValue": "value = !data.has_medication_allergies ? 'NKDA' : data.medication_allergies_list;"}, {"key": "has_past_surgeries", "type": "radio", "input": true, "label": "Have you had any surgeries in the past?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": false, "optionsLabelPosition": "right"}, {"key": "past_surgeries_list", "type": "textarea", "input": true, "label": "Please list your previous surgeries (i.e. Tonsillectomy)", "validate": {"required": true}, "tableView": false, "autoExpand": false, "customConditional": "show = data.has_past_surgeries == true;"}, {"key": "past_surgeries", "type": "textfield", "input": true, "label": "past_surgeries", "hidden": true, "disabled": true, "hideLabel": true, "tableView": true, "clearOnHide": false, "calculateValue": "value = !data.has_past_surgeries ? 'None' : data.past_surgeries_list;"}, {"key": "has_health_conditions", "type": "radio", "input": true, "label": "Do you have any health conditions diagnosed by a doctor (i.e. asthma, anxiety, high blood pressure, etc)?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": false, "optionsLabelPosition": "right"}, {"key": "health_conditions_list", "type": "textarea", "input": true, "label": "Please list your health conditions below (i.e. diabetes, hypothyroidism):", "validate": {"required": true}, "tableView": false, "autoExpand": false, "customConditional": "show = data.has_health_conditions == true;"}, {"key": "health_conditions", "type": "textfield", "input": true, "label": "health_conditions", "hidden": true, "disabled": true, "hideLabel": true, "tableView": true, "clearOnHide": false, "calculateValue": "value = !data.has_health_conditions ? 'None' : data.health_conditions_list;"}, {"key": "current_smoker", "type": "radio", "input": true, "label": "Are you currently a smoker?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": false, "optionsLabelPosition": "right"}, {"key": "smoking_details", "type": "radio", "input": true, "label": "How much do you smoke?", "inline": false, "values": [{"label": "0-10 cigarettes/day", "value": "0-10_cigarettes_per_day", "shortcut": ""}, {"label": "11-25 cigarettes/day", "value": "11-25_cigarettes_per_day", "shortcut": ""}, {"label": "26-50 cigarettes/day", "value": "26-50_cigarettes_per_day", "shortcut": ""}, {"label": "Socially (occasionally)", "value": "socially_or_occasionally", "shortcut": ""}, {"label": "Vaping", "value": "vaping", "shortcut": ""}, {"label": "<PERSON><PERSON>/<PERSON><PERSON>a", "value": "hooka_or_sheisha", "shortcut": ""}], "validate": {"required": true}, "tableView": false, "customConditional": "show = data.current_smoker == true;", "optionsLabelPosition": "right"}, {"key": "social", "type": "textfield", "input": true, "label": "social", "hidden": true, "disabled": true, "hideLabel": true, "tableView": true, "clearOnHide": false, "calculateValue": "value = !data.current_smoker ? 'Non-Smoker' : `Smokes: ${data.smoking_details}`;"}]}