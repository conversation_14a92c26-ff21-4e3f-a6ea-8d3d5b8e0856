{"components": [{"key": "travel_start_date", "type": "datetime", "input": true, "label": "Select your travel start date:", "format": "yyyy-MM-dd", "widget": "calendar", "validate": {"required": true}, "tableView": true, "confirmLabel": "Travel Start Date", "customConditional": "show = data.sku == 'fem_pn_suppress_menses';"}, {"key": "uncertain_menses_date", "type": "radio", "input": true, "label": "Do you know the expected start date of your next period?", "inline": false, "values": [{"label": "Yes, I know the expected start date", "value": false}, {"label": "No, my cycle is irregular or uncertain", "value": true}], "validate": {"required": true}, "tableView": false, "confirmLabel": "Menses Date:", "defaultValue": false, "customConditional": "show = data.sku == 'fem_pn_suppress_menses';"}, {"key": "expected_menses_date", "type": "datetime", "input": true, "label": "Select the expected onset date of your menstrual cycle:", "format": "yyyy-MM-dd", "widget": "calendar", "validate": {"required": true}, "tableView": true, "confirmLabel": "Expected Menses Date:", "customConditional": "show = data.sku == 'fem_pn_suppress_menses' && data.uncertain_menses_date === false;"}, {"key": "travel_days", "type": "number", "input": true, "label": "Enter the number of days you plan to be away from home:", "validate": {"min": 1, "required": true}, "tableView": true, "confirmLabel": "Travel Days:", "customConditional": "show = data.sku == 'fem_pn_suppress_menses';"}, {"key": "pill_start_date", "type": "textfield", "input": true, "label": "Start taking your pills on:", "disabled": true, "tableView": true, "confirmLabel": "Pill Start Date:", "calculateValue": "if (data.travel_start_date) { var travelDate = new Date(data.travel_start_date); var startPillDate = (data.uncertain_menses_date || !data.expected_menses_date) ? new Date(travelDate.getTime() - (5 * 24 * 60 * 60 * 1000)) : new Date(new Date(data.expected_menses_date).getTime() - (5 * 24 * 60 * 60 * 1000)); var daysOfWeek = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']; value = daysOfWeek[startPillDate.getDay()] + ', ' + startPillDate.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' }); }", "customConditional": "show = data.sku == 'fem_pn_suppress_menses' && data.travel_start_date;"}, {"key": "total_pill_days", "type": "textfield", "input": true, "label": "Total number of days you will need to take medication:", "disabled": true, "tableView": true, "confirmLabel": "Total Pill Days:", "calculateValue": "if (data.travel_start_date && data.travel_days) { var travelDate = new Date(data.travel_start_date); var startPillDate = (data.uncertain_menses_date || !data.expected_menses_date) ? new Date(travelDate.getTime() - (5 * 24 * 60 * 60 * 1000)) : new Date(new Date(data.expected_menses_date).getTime() - (5 * 24 * 60 * 60 * 1000)); var totalDays = Math.ceil((travelDate.getTime() - startPillDate.getTime()) / (1000 * 60 * 60 * 24)) + data.travel_days; value = totalDays; }", "customConditional": "show = data.sku == 'fem_pn_suppress_menses' && data.travel_start_date && data.travel_days;"}, {"key": "rx_preference_suppress_menses", "type": "radio", "input": true, "label": "MPA and NETA are progesterone medications, and both are reasonable options to delay your menstrual cycle. At TeleTest, we preferentially prescribe MPA pills over NETA. When taken orally, some NETA is converted into estrogen by your body, and carries the same risks associated with using birth control pills (i.e. a higher risk of blood clots, stroke, etc). If you have been on NETA in the past, and tolerated this medication well, it remains a safe and reasonable option.</br></br>Norethindrone Acetate (NETA)</br><li>Officially Approved in the United Kindom for Delay of Menstruation</li><li>Dosing: Remember to take it three times per day (i.e. every 8 hours)</li><li>Approximate Cost: $6/day + Pharmacy Dispensing Fee</li></br></br>Medroxyprogesterone Acetate (MPA)</br><li>Used 'off-label' for Delay of Menstruation</li><li>Dosing: Once or twice daily </li><li>Approximate Cost: $0.25-$0.50/day + Pharmacy Dispensing Fee</li></br></br>Which medication would you prefer to use?", "inline": false, "values": [{"label": "MPA (recommended)", "value": "MPA"}, {"label": "NETA", "value": "NETA"}, {"label": "Not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "confirmLabel": "Medication Preference:", "customConditional": "show = data.sku == 'fem_pn_suppress_menses';", "optionsLabelPosition": "right"}, {"key": "rx_preference_ocp", "type": "radio", "input": true, "label": "Do you have any preference for medication?", "inline": false, "values": [{"label": "<PERSON><PERSON><PERSON> 21", "value": "alysena-21"}, {"label": "<PERSON><PERSON><PERSON> 28 (Most Popular)", "value": "alysena-28"}, {"label": "<PERSON><PERSON><PERSON>", "value": "evra-200mcg35mcg-1year"}, {"label": "<PERSON><PERSON>", "value": "lolo-1mg10mcg10mcg"}, {"label": "Slynd", "value": "slynd"}, {"label": "Yaz", "value": "yaz-3mg002mg"}, {"label": "<PERSON><PERSON><PERSON>", "value": "yasmin-28-year"}, {"label": "Other", "value": "other"}, {"label": "Not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.sku == 'fem_pn_ocp'", "optionsLabelPosition": "right"}, {"key": "rx_preference_ocp_other", "type": "textarea", "input": true, "label": "You selected “other”, please specify:", "validate": {"required": true}, "tableView": true, "autoExpand": false, "customConditional": "show = data.sku == 'fem_pn_ocp' && data.rx_preference_ocp == 'other'"}, {"key": "rxts", "type": "textfield", "input": true, "label": "Rx Templates:", "hidden": true, "disabled": true, "multiple": true, "adminFlag": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "if (data.sku == 'fem_pn_ocp') { value = ['other','not_sure'].includes(data.rx_preference_ocp) ? [] : [data.rx_preference_ocp]; } else if (data.sku == 'fem_pn_suppress_menses') {value = data.rx_preference_suppress_menses =='NETA'?[`neta-5mg-tid-${data.total_pill_days}-days`]:[`medroxyprogesterone-acetate-10mg-${data.total_pill_days}-days`];} else { value = []; }", "refreshOnChange": true}]}