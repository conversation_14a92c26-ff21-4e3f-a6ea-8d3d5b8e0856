{"name": "result", "type": "form", "title": "Result", "display": "form", "components": [{"key": "skus", "type": "textfield", "input": true, "label": "SKUs", "hidden": true, "visible": false, "disabled": true, "multiple": true, "hideLabel": true, "tableView": false, "spellcheck": false, "clearOnHide": false}, {"key": "originalAssays", "type": "textfield", "input": true, "label": "Original Assays", "hidden": true, "visible": false, "disabled": true, "multiple": true, "hideLabel": true, "tableView": false, "spellcheck": false, "clearOnHide": false}, {"key": "in_person_care_required", "type": "textfield", "input": true, "label": "In person care required", "hidden": true, "disabled": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = !!data.symptoms_greater_than_7_days || !!data.pregnant || !!data.immunosuppression;"}, {"key": "showSubmit", "type": "textfield", "input": true, "label": "", "hidden": true, "visible": false, "disabled": true, "hideLabel": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = !data.needle_stick && !data.in_person_care_required && !_.some(_.values(data.std_red_flag_symptoms)) && !data.no_herpes_medication && !data.no_birth_control && !data.no_gardasil_9 && !data.no_twinrix && !data.no_inr && !data.no_hsv_test && !data.no_thyroid && !data.no_testosterone_bloodwork && !data.no_shingrix && !data.no_diabetes_monitoring && !data.no_vit_d_monitoring && !data.no_celiac_test && !data.no_hair_panel && !data.no_ped_panel && !data.no_finasteride && !data.no_dukoral && !data.no_bmd && !data.no_latisse &&!data.no_colon_cancer && !data.no_lead && !data.no_psa && !data.no_wart_tx && !data.no_mgen;"}, {"key": "show_birth_control", "type": "htmlelement", "attrs": [{"attr": "", "value": ""}], "input": false, "label": "HTML", "content": "You're eligible to discuss prescription birth control medication.", "tableView": false, "customConditional": "show = data.assay_choices.indexOf('RX-OCP') > -1 && !data.no_birth_control;"}, {"key": "show_bmd", "type": "htmlelement", "attrs": [{"attr": "", "value": ""}], "input": false, "label": "HTML", "content": "<h3 class='text-green'> You're eligible to discuss Bone Mineral Density (BMD) testing. Your doctor will review your risk factors and provide a requisition to complete BMD Testing if medically appropriate.</h3></br><h5 class='text-blue'>Bone Mineral Density testing is only recommended if:</br></br><li>You are over 65</li><li>Age 50-64: You have risk factors. </li><li>Age 18-49: You have specific medical conditions.</li></br>Please read more about BMD Testing <a href='https://docs.teletest.ca/bone-mineral-density-and-osteoperosis' target='_blank'>(HERE)</a></h5>", "tableView": false, "customConditional": "show = data.assay_choices.indexOf('BMD-TEST') > -1 && !data.no_bmd;"}, {"key": "show_wart_tx", "type": "htmlelement", "attrs": [{"attr": "", "value": ""}], "input": false, "label": "HTML", "content": "<h3 class='text-green'> You're eligible to discuss Anogenital Wart treatment.</h3></br><h5 class='text-blue'>TeleTest only prescribes treatment if:</br></br><li>You have a confirmed case of anogenital warts</li></br>Please read more about Anogenital Wart treatment before your appointment <a href='https://docs.teletest.ca/hpv-and-vaccination' target='_blank'>(HERE)</a></h5>", "tableView": false, "customConditional": "show = (data.assay_choices.indexOf('RX-PODOPHYLLOTOXIN') > -1 || data.assay_choices.indexOf('RX-SINECATECHINS') > -1) && !data.no_wart_tx;"}, {"key": "show_psa", "type": "htmlelement", "attrs": [{"attr": "", "value": ""}], "input": false, "label": "HTML", "content": "<h3 class='text-green'> You're eligible to discuss Prostate Specific Antigen (PSA) testing.</h3></br><h5 class='text-blue'>PSA testing is only recommended if:</br></br><li>You currently have prostate symptoms</li><li>You have weighed the risks and benefits of screening and would still like to test.</br></br>Please read more about PSA Testing <a href='https://docs.teletest.ca/prostate-specific-antigen-psa' target='_blank'>(HERE)</a></h5>", "tableView": false, "customConditional": "show = data.assay_choices.indexOf('PSA') > -1 && !data.no_psa;"}, {"key": "show_hsv_test", "type": "htmlelement", "attrs": [{"attr": "", "value": ""}], "input": false, "label": "HTML", "content": "You're eligible to discuss herpes simplex blood test. Please read our <a href=\"https://docs.teletest.ca/about-the-herpes-virus\" target='_blank'>FAQ Guide</a> about commonly asked questions related to herpes testing. HSV testing is not right for everyone.<li>In Ontario, over 70% of adults test positive for HSV by age 40.</li><li>Testing and getting a positive result can lead to unnecessary anxiety and make conversations with sexual partners difficult.</li>", "tableView": false, "customConditional": "show = (data.assay_choices.indexOf('HSV1') > -1 || data.assay_choices.indexOf('HSV2') > -1) && !data.no_hsv_test;"}, {"key": "show_latisse", "type": "htmlelement", "attrs": [{"attr": "", "value": ""}], "input": false, "label": "HTML", "content": "You're eligible to discuss a prescription for Latisse. Please read our <a href=\"https://docs.teletest.ca/eyelash-growth-and-latisse\" target='_blank'>FAQ Guide</a> about commonly asked questions related to <PERSON><PERSON><PERSON> before your consultation. Latisse is not right for everyone.", "tableView": false, "customConditional": "show = data.assay_choices.indexOf('RX-LATISSE') > -1 && !data.no_latisse;"}, {"key": "show_lead", "type": "htmlelement", "attrs": [{"attr": "", "value": ""}], "input": false, "label": "HTML", "content": "You're eligible to discuss monitoring of your blood lead levels. We do not currently offer other heavy metal testing at this time.", "tableView": false, "customConditional": "show = data.assay_choices.indexOf('LEAD') > -1 && !data.no_lead;"}, {"key": "show_thyroid", "type": "htmlelement", "attrs": [{"attr": "", "value": ""}], "input": false, "label": "HTML", "content": "You're eligible to discuss thyroid testing and renewal of your thyroid medication.", "tableView": false, "customConditional": "show = (data.assay_choices.indexOf('TSH') > -1 || data.assay_choices.indexOf('FT3') > -1 || data.assay_choices.indexOf('FT4') > -1 || data.assay_choices.indexOf('RT3') > -1 ) && !data.no_thyroid;"}, {"key": "show_finasteride", "type": "htmlelement", "attrs": [{"attr": "", "value": ""}], "input": false, "label": "HTML", "content": "You're eligible to discuss renewal of your hair loss medication.", "tableView": false, "customConditional": "show = (data.assay_choices.indexOf('FINASTERIDE') > -1) && !data.no_finasteride;"}, {"key": "show_diabetes_monitoring", "type": "htmlelement", "attrs": [{"attr": "", "value": ""}], "input": false, "label": "HTML", "content": "You're eligible to discuss monitoring of your diabetes.", "tableView": false, "customConditional": "show = (data.assay_choices.indexOf('Q-DM-MON') > -1) && !data.no_diabetes_monitoring;"}, {"key": "show_celiac_test", "type": "htmlelement", "attrs": [{"attr": "", "value": ""}], "input": false, "label": "HTML", "content": "You're eligible to discuss celiac screening.", "tableView": false, "customConditional": "show = (data.assay_choices.indexOf('TTG-IGA') > -1) && !data.no_celiac_test;"}, {"key": "show_dukoral", "type": "htmlelement", "attrs": [{"attr": "", "value": ""}], "input": false, "label": "HTML", "content": "You're eligible to discuss a prescription of the Dukoral vaccine.", "tableView": false, "customConditional": "show = (data.assay_choices.indexOf('VX-DUKORAL') > -1) && !data.no_dukoral;"}, {"key": "show_vit_d", "type": "htmlelement", "attrs": [{"attr": "", "value": ""}], "input": false, "label": "HTML", "content": "You're eligible to discuss vitamin D testing and/or a prescription of Vitamin D.", "tableView": false, "customConditional": "show = (data.assay_choices.indexOf('VIT-D-PANEL') > -1) && !data.no_vit_d_monitoring;"}, {"key": "show_twinrix", "type": "htmlelement", "attrs": [{"attr": "", "value": ""}], "input": false, "label": "HTML", "content": "You're eligible to discuss a prescription of Twinrix. TeleTest does not provide a comprehensive travel assessment at this time.  If you are looking for travel advice, please find a local travel clinic for further guidance.", "tableView": false, "customConditional": "show = data.assay_choices.indexOf('VX-HEP-AB') > -1 && !data.no_twinrix;"}, {"key": "show_inr", "type": "htmlelement", "attrs": [{"attr": "", "value": ""}], "input": false, "label": "HTML", "content": "You're eligible to discuss prescription INR testing.", "tableView": false, "customConditional": "show = data.assay_choices.indexOf('INR') > -1 && !data.no_inr;"}, {"key": "show_gardasil_9", "type": "htmlelement", "attrs": [{"attr": "", "value": ""}], "input": false, "label": "HTML", "content": "You're eligible for a Gardasil 9 Prescription.  Each vaccine dose costs approximately $200.  Some private insurance plans cover the cost of the vaccine.", "tableView": false, "customConditional": "show = data.assay_choices.indexOf('VX-GARD') > -1 && !data.no_gardasil_9;"}, {"key": "show_twinrix", "type": "htmlelement", "attrs": [{"attr": "", "value": ""}], "input": false, "label": "HTML", "content": "You're eligible to discuss vaccination against shingles and obtain a Shingrix prescription.", "tableView": false, "customConditional": "show = data.assay_choices.indexOf('VX-SHRX') > -1 && !data.no_shingrix;"}, {"key": "show_testosterone_monitoring", "type": "htmlelement", "attrs": [{"attr": "", "value": ""}], "input": false, "label": "HTML", "content": "<h3 class='text-green'> You're eligible to discuss testosterone monitoring.</h3></br><h5 class='text-red'>TeleTest does not currently offer testosterone testing if you are not on TRT. If you are looking to diagnose testosterone deficiency, please proceed to a local walk-in clinic for further review.</h5>", "tableView": false, "customConditional": "show = data.assay_choices.indexOf('TT') > -1 && !data.no_testosterone_bloodwork;"}, {"key": "show_ped_pnl", "type": "htmlelement", "attrs": [{"attr": "", "value": ""}], "input": false, "label": "HTML", "content": "<h3 class='text-green'> You're eligible to discuss Performance Enhancing Drug (PED) monitoring.</h3></br><h5 class='text-blue'>TeleTest physicians do not:</br></br><li>Endorse the use of performance enhancing drugs</li><li>Provide guidance about dosing or drug regimens</li><li>Renew or provide prescriptions for PEDs</li></br>We're here to support you in reducing harm through open, non-judgemental communication and testing. Please read more about why some testing is not insured through OHIP <a href='https://docs.teletest.ca/performance-and-enhancing-drugs-peds#test-frequency' target='_blank'>(HERE)</a></h5>", "tableView": false, "customConditional": "show = data.assay_choices.indexOf('PED-PNL') > -1 && !data.no_ped_panel;"}, {"key": "show_mgen_pnl", "type": "htmlelement", "attrs": [{"attr": "", "value": ""}], "input": false, "label": "HTML", "content": "<h3 class='text-green'> You're eligible to discuss Mycoplasma Genitalium (M.Gen) testing.</h3></br><h5 class='text-blue'>TeleTest physicians do not:</br></br><li>Have access to antibiotic sensitivity testing for Mycoplasma Genitalium</li><li>Provide treatment of Mycoplasma Genitalium without a positive test result</li><li>Arrange Mycoplasma Genitalium testing if you have not had prior STI testing for trichomonas, chlamydia or gonorrhea</li><li> Life Labs charges $80 for Mycoplasma Genitalium testing.</li></br>Please read more about why Mycoplasma Genitalium testing is not routinely completed <a href='https://docs.teletest.ca/mycoplasma-genitalium' target='_blank'>(HERE)</a></h5>", "tableView": false, "customConditional": "show = data.assay_choices.indexOf('MGEN') > -1 && !data.no_mgen;"}, {"key": "show_colon_cancer", "type": "htmlelement", "attrs": [{"attr": "", "value": ""}], "input": false, "label": "HTML", "content": "<h3 class='text-green'> You're eligible to discuss referral for a screening colonoscopy or the Fecal Immunochemical Test (FIT).</h3></br><h5 class='text-blue'>Colonoscopy or FIT testing through TeleTest is not appropriate if you are experiencing symptoms, as more urgent follow up and an in-person examination should be completed.</br></br><FIT testing is offered to select individuals between 50-74 with OHIP insurance</li></br>Colonoscopy screening is recommended if you have a previous or family history of colon cancer, have polyps that need surveillance or have other risk factors.</br></br>The doctor will help determine what is the most appropriate test for you.</h5>", "tableView": false, "customConditional": "show = data.assay_choices.indexOf('COLON-CANCER') > -1 && !data.no_colon_cancer;"}, {"key": "no_diabetes_monitoring", "type": "htmlelement", "attrs": [{"attr": "", "value": ""}], "input": true, "content": "<h3 class='text-red'>We cannot currently offer you monitoring for diabetes. Please proceed to your local walk-in clinic or urgent care centre for evaluation. If you are interested in screening for diabetes, please select the 'Metabolic Panel'</h3>", "hideLabel": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.assay_choices.indexOf('Q-DM-MON') > -1 && (_.some(_.values(data.diabetes_testing_contraindication)) || data.diagnosis_diabetes == 'no');", "customConditional": "show = !!data.no_diabetes_monitoring"}, {"key": "no_bmd", "type": "htmlelement", "attrs": [{"attr": "", "value": ""}], "input": true, "content": "<h3 class='text-red'>We cannot currently offer you bone mineral density testing. If you are concerned about a new fracture, please visit a local urgent care centre or emergency room.</h3>", "hideLabel": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.assay_choices.indexOf('BMD-TEST') > -1 && (data.current_fracture == true || data.bmd_current_year == true);", "customConditional": "show = !!data.no_bmd"}, {"key": "no_gardasil_9", "type": "htmlelement", "attrs": [{"attr": "", "value": ""}], "input": true, "content": "<h3 class='text-red'>We cannot currently offer you a prescription for the Gardasil 9 Vaccine</h3>", "hideLabel": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.assay_choices.indexOf('VX-GARD') > -1 && (data.gardasil_contraindication == true || data.three_doses_past == true);", "customConditional": "show = !!data.no_gardasil_9"}, {"key": "insuredDisplay", "type": "mylist", "input": true, "label": "", "multiple": true, "iconClass": "fa-regular fa-circle-check", "iconColor": "green", "tableView": false, "defaultValue": [], "calculateValue": {"var": "data.userBulletKeys"}, "clearOnRefresh": true, "customConditional": "show = !!data.showSubmit && !!data.validInsurance && !!_.some(data.userBulletKeys);"}, {"key": "uninsuredSummary", "type": "uninsuredsummary", "input": true, "label": "", "multiple": true, "tableView": false, "defaultValue": [], "calculateValue": {"_difference": [{"var": "data.uninsured"}, {"if": [{"!=": [{"var": "data.sex"}, "female"]}, ["TRICH"], []]}]}, "customConditional": "show = !!data.showSubmit && !!_.some(data.uninsured);"}, {"key": "vswbRecommendedNote", "type": "htmlelement", "attrs": [{"attr": "", "value": ""}], "input": false, "label": "HTML", "content": "Based on your symptoms we recommend <button type='submit' class='btn btn-link-primary' name='add-bv-test'>adding a BV test (click to add)</button><br/><br/>If you have already had a negative vaginal swab result in the past 7 days we recommend a speculum examination at a walk-in-clinic for further assessment", "tableView": false, "customConditional": "show = !!data.showSubmit && data.assay_choices && data.assay_choices.indexOf('VSWB') == -1 && data.sex == 'female' && !!_.some(_.values(data.other_std_symptoms));"}, {"key": "stdRecommendedNote", "type": "htmlelement", "attrs": [{"attr": "", "value": ""}], "input": false, "label": "HTML", "content": "Based on your symptoms we recommend <button type='submit' class='btn btn-link-primary' name='add-std-test'>adding a STD test (click to add)</button>", "tableView": false, "customConditional": "show = !!data.showSubmit && data.assay_choices && data.assay_choices.indexOf('CT') < 0 && !!_.some(_.values(data.other_std_symptoms));"}, {"key": "redFlagsNote", "type": "htmlelement", "attrs": [{"attr": "", "value": ""}], "input": false, "label": "HTML", "content": "<h3 class='text-red'>Please seek immediate attention at an emergency clinic!</h3><p class='text-red'>We are not able to offer you screening. You have symptoms which may be indicative of a serious condition.</p>", "tableView": false, "customConditional": "show = !data.needle_stick && !!_.some(_.values(data.std_red_flag_symptoms));"}, {"key": "noTestNote", "type": "htmlelement", "attrs": [{"attr": "", "value": ""}], "input": false, "label": "HTML", "content": "<h3>We cannot currently offer you screening</h3><p>We are not able to offer you screening as your condition requires in-person care.</p>", "tableView": false, "customConditional": "show = !data.needle_stick && !_.some(_.values(data.std_red_flag_symptoms)) && !!data.in_person_care_required;"}, {"key": "needle_stickNote", "type": "htmlelement", "attrs": [{"attr": "", "value": ""}], "input": false, "label": "HTML", "content": "<h3 class='text-red'>Please seek immediate attention at an emergency clinic!</h3><p class='text-red'>Exposure to a needle stick or contaminated blood/blood products may require post-exposure prophylaxis urgently.</p>", "tableView": false, "customConditional": "show = !!data.needle_stick;"}, {"key": "hsv_test_note_swab", "type": "htmlelement", "attrs": [{"attr": "", "value": ""}], "input": false, "label": "HTML", "content": "<h3 class='text-red'>Please seek immediate attention at an urgent care clinic!</h3><p class='text-red'>Diagnosis of a herpes outbreak requires a time-sensitive PCR swab.</p>", "tableView": false, "customConditional": "show = !!data.diagnosis_new_hsv || !!data.diagnosis_uncertain_hsv;"}, {"key": "hsv_test_note_treatment", "type": "htmlelement", "attrs": [{"attr": "", "value": ""}], "input": false, "label": "HTML", "content": "<h3 class='text-red'>Please seek immediate attention at an urgent care clinic!</h3><p class='text-red'>Treatment of a herpes or shigles outbreak requires immediate assessment</p>", "tableView": false, "customConditional": "show = !!data.diagnosis_shingles || !!data.ocular_herpes;"}, {"key": "no_herpes_medication", "type": "htmlelement", "attrs": [{"attr": "", "value": ""}], "input": true, "label": "HTML", "content": "<h3 class='text-red'>We cannot currently offer prescription medication for herpes. We advise in person assessment with a physician</h3>", "hideLabel": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = (data.assay_choices.indexOf('RX-HSVS') > -1 || data.assay_choices.indexOf('RX-HSVO') > -1) && (_.some(_.values(data.herpes_contraindications)) || data.diagnosis_new_hsv == true || data.diagnosis_shingles == true || data.ocular_herpes || data.diagnosis_uncertain_hsv == true);", "customConditional": "show = !!data.no_herpes_medication"}, {"key": "no_testosterone_bloodwork", "type": "htmlelement", "attrs": [{"attr": "", "value": ""}], "input": true, "label": "HTML", "content": "<h3 class='text-red'>We cannot currently offer you testosterone monitoring. Please seek in-person care with a physician.</h3>", "hideLabel": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = (data.assay_choices.indexOf('TT') > -1) && (_.some(_.values(data.testosterone_contraindication)) || data.sex == 'female' || !!data.on_anabolic_steroids);", "customConditional": "show = !!data.no_testosterone_bloodwork"}, {"key": "no_colon_cancer", "type": "htmlelement", "attrs": [{"attr": "", "value": ""}], "input": true, "label": "HTML", "content": "<h3 class='text-red'>We cannot currently offer you surveillance testing for colon cancer. Please seek in-person care with a physician for further assessment.</h3>", "hideLabel": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = (data.assay_choices.indexOf('COLON-CANCER') > -1) && (_.some(_.values(data.colonoscopy_contraindications)) || (data.current_age == '<50' && !!data.no_colonoscopy_rf && data.family_history_colon_cancer == false));", "customConditional": "show = !!data.no_colon_cancer"}, {"key": "no_psa", "type": "htmlelement", "attrs": [{"attr": "", "value": ""}], "input": true, "label": "HTML", "content": "<h3 class='text-red'>We cannot currently offer you PSA monitoring at this time. Please seek in-person care with a physician for further assessment to determine if PSA testing is right for you.</h3>", "hideLabel": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = (data.assay_choices.indexOf('PSA') > -1) && (_.some(_.values(data.psa_red_flag)) || data.current_prostate_cancer == true);", "customConditional": "show = !!data.no_psa"}, {"key": "no_lead", "type": "htmlelement", "attrs": [{"attr": "", "value": ""}], "input": true, "label": "HTML", "content": "<h3 class='text-red'>We cannot currently offer you lead level monitoring at this time. Please seek in-person care with a physician for further assessment to determine if lead level monitoring is right for you.</h3>", "hideLabel": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = (data.assay_choices.indexOf('LEAD') > -1) && (_.some(_.values(data.lead_contraindications)) || !!data.no_lead_rf);", "customConditional": "show = !!data.no_lead"}, {"key": "no_finasteride", "type": "htmlelement", "attrs": [{"attr": "", "value": ""}], "input": true, "label": "HTML", "content": "<h3 class='text-red'>We cannot currently offer you a prescription for finasteride or oral hair loss medication. Please seek in-person care with a physician.</h3>", "hideLabel": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = (data.assay_choices.indexOf('FINASTERIDE') > -1) && (data.biological_sex_birth == 'female' || data.currently_taking_finasteride == false);", "customConditional": "show = !!data.no_finasteride"}, {"key": "no_ped_panel", "type": "htmlelement", "attrs": [{"attr": "", "value": ""}], "input": true, "label": "HTML", "content": "<h3 class='text-red'>We cannot currently offer monitoring for Performance Enhancing Drugs. Please seek in-person care with a physician. </h3></br></br><h5 class='text-red'>If you are experiencing thoughts of suicide, harming yourself or others, please visit your local emergency room.</h5>", "hideLabel": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = (data.assay_choices.indexOf('PED-PNL') > -1) && (_.some(_.values(data.ped_contraindication)) || !data.current_ped || !!data.si_hi_screen);", "customConditional": "show = !!data.no_ped_panel"}, {"key": "no_celiac_test", "type": "htmlelement", "attrs": [{"attr": "", "value": ""}], "input": true, "label": "HTML", "content": "<h4 class='text-red'>We cannot currently offer you celiac testing. Please seek in-person care with a physician.</h4>", "hideLabel": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = (data.assay_choices.indexOf('TTG-IGA') > -1) && (_.some(_.values(data.celiac_red_flags)) || data.currently_celiac_diagnosis == true || data.on_gluten_free_diet == true || (data.family_member_celiac == false && !_.some(_.values(data.celiac_symptoms))) || (data.family_member_celiac == false && !!_.some(_.values(data.celiac_symptoms)) && data.symptoms_greater_12_months == false));", "customConditional": "show = !!data.no_celiac_test"}, {"key": "no_celiac_test_gluten_challenge", "type": "htmlelement", "attrs": [{"attr": "", "value": ""}], "input": true, "label": "HTML", "content": "Celiac testing requires a gluten challenge, or you can have a false negative result. This consists of taking 8-10 grams (4-6 slices of bread per day) for 6-8 weeks before completing an celiac test.", "hideLabel": true, "tableView": false, "clearOnHide": false, "customConditional": "show = !!data.no_celiac_test && !!data.on_gluten_free_diet"}, {"key": "no_celiac_test_previous_celiac", "type": "htmlelement", "attrs": [{"attr": "", "value": ""}], "input": true, "label": "HTML", "content": "Celiac testing is not medically indicated if you have already been diagnosed with celiac disease.", "hideLabel": true, "tableView": false, "clearOnHide": false, "customConditional": "show = !!data.no_celiac_test && !!data.data.currently_celiac_diagnosis"}, {"key": "no_vit_d_monitoring", "type": "htmlelement", "attrs": [{"attr": "", "value": ""}], "input": true, "label": "HTML", "content": "<h3 class='text-red'>We cannot currently offer you Vitamin D testing or a prescription for Vitamin D. </h3> <li>If you are looking for additional testing, please select the appropriate panel on our site or visit a local walk-in clinic for further assessment.</li> <li>If you currently have symptoms, please follow up for in-person care with a physician.</li>", "hideLabel": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = (data.assay_choices.indexOf('VIT-D-PANEL') > -1) && (_.some(_.values(data.vitamin_d_in_person)) || data.interested_other_tests == true);", "customConditional": "show = !!data.no_vit_d_monitoring"}, {"key": "no_mgen", "type": "htmlelement", "attrs": [{"attr": "", "value": ""}], "input": true, "label": "HTML", "content": "<h3 class='text-red'>We cannot currently offer you Mycoplasma Genitalium testing. </h3> <li>If you currently have symptoms, TeleTest requires routine STI and UTI/swab testing before arranging testing.</li> <li>Routine testing for Mycoplasma Genitalium without exposure is not recommended.</li><li>If you are currently experiencing abdominal/pelvic pain or cramping, please visit a local walk-in clinic for an exam as you may have Pelvic Inflammatory Disease</li></br>If you require STI or UTI testing prior to completing testing, you can select from the appropriate panel <a href='https://teletest.ca/app/care/std/' target='_blank'>(HERE)</a>", "hideLabel": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = (data.assay_choices.indexOf('MGEN') > -1) && ((data.current_symptoms == false && data.confirmed_exposure == false && data.work_testing == false) || (data.current_symptoms == true && data.no_mycoplasma_contraindications == false) || (data.current_symptoms == true && data.no_mycoplasma_contraindications == true && data.no_list_current_symptoms == true) || (data.current_symptoms == true && data.no_mycoplasma_contraindications == true && data.no_list_current_symptoms == false && data.completed_sti_testing == false) || (data.current_symptoms == true && data.no_mycoplasma_contraindications == true && data.no_list_current_symptoms == false && data.completed_sti_testing == true && data.completed_uti_testing == false) || (data.current_symptoms == true && data.no_mycoplasma_contraindications == true && data.no_list_current_symptoms == false && data.completed_sti_testing == true && data.completed_uti_testing == true && data.completed_swab_testing == false));", "customConditional": "show = !!data.no_mgen"}, {"key": "no_thyroid", "type": "htmlelement", "attrs": [{"attr": "", "value": ""}], "input": true, "label": "HTML", "content": "<h3 class='text-red'>We cannot currently offer you bloodwork for thyroid testing or a prescription for thyroid medication. Please visit a local walk-in clinic or urgent care centre for further guidance.</h3>", "hideLabel": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = (data.assay_choices.indexOf('TSH') > -1 || data.assay_choices.indexOf('FT3') > -1 || data.assay_choices.indexOf('FT4') > -1 || data.assay_choices.indexOf('RT3') > -1) && (_.some(_.values(data.thyroid_in_person)) || data.currently_pregnant == true || data.current_hyperthyroidism_meds == 'methimazole' || data.current_hyperthyroidism_meds == 'tapazole' || data.hyperthyroidism_meds_past == 'methimazole' || data.hyperthyroidism_meds_past == 'tapazole') || (data.currently_thyroid_med == false && data.thyroid_med_past == false && data.auto_immune_risk_factors.none_of_the_above && data.no_other_rf);", "customConditional": "show = !!data.no_thyroid"}, {"key": "no_hsv_test", "type": "htmlelement", "attrs": [{"attr": "", "value": ""}], "input": true, "label": "HTML", "content": "<h3 class='text-red'>We cannot currently offer you bloodwork for herpes. Testing for with bloodwork may not be appropriate for you.</h3>", "hideLabel": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = (data.assay_choices.indexOf('HSV1') > -1 || data.assay_choices.indexOf('HSV2') > -1) && (!_.some(_.values(data.herpes_test_indication)) || data.diagnosis_new_hsv == true || data.diagnosis_shingles == true || data.ocular_herpes || data.diagnosis_uncertain_hsv == true);", "customConditional": "show = !!data.no_hsv_test"}, {"key": "no_inr", "type": "htmlelement", "attrs": [{"attr": "", "value": ""}], "input": true, "label": "HTML", "content": "<h3 class='text-red'>We cannot currently offer you INR testing. We advise in person assessment with a physician</h3>", "hideLabel": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.assay_choices.indexOf('INR') > -1 && (_.some(_.values(data.inr_contraindication)) || data.anticoagulant_type != 'warfarin' || data.blood_pressure_reading != 'normotensive');", "customConditional": "show = !!data.no_inr"}, {"key": "no_birth_control", "type": "htmlelement", "attrs": [{"attr": "", "value": ""}], "input": true, "content": "<h3 class='text-red'>We cannot currently offer you birth control medication</h3>", "hideLabel": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.assay_choices.indexOf('RX-OCP') > -1 && (_.some(_.values(data.birth_control_contraindication)) || data.blood_pressure_reading != 'normotensive' || data.smoking_status == true);", "customConditional": "show = !!data.no_birth_control"}, {"key": "no_latisse", "type": "htmlelement", "attrs": [{"attr": "", "value": ""}], "input": true, "content": "<h3 class='text-red'>We cannot currently offer you a prescription for Latisse.</h3>", "hideLabel": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.assay_choices.indexOf('RX-LATISSE') > -1 && (data.pregnancy_breastfeeding == true || data.glaucoma_hx == true || data.alopecia_eyelashes == true || data.latisse_allergy || data.uveitis_iritis_herpes == true || data.bmd_screen == true);", "customConditional": "show = !!data.no_latisse"}, {"key": "no_wart_tx", "type": "htmlelement", "attrs": [{"attr": "", "value": ""}], "input": true, "content": "<h3 class='text-red'>We cannot currently offer you a prescription for anogenital wart treatment.</h3>", "hideLabel": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = (data.assay_choices.indexOf('RX-SINECATECHINS') > -1 || data.assay_choices.indexOf('RX-PODOPHYLLOTOXIN') > -1) && (data.diagnosis_health_care_provider == false || data.diagnosis_uncertain_warts_molloscum == true || data.desires_n2_laser == true || data.desires_treatment_cream == false || data.diagnosis_pregnant == true);", "customConditional": "show = !!data.no_wart_tx"}, {"key": "no_hair_panel", "type": "htmlelement", "attrs": [{"attr": "", "value": ""}], "input": true, "content": "<h3 class='text-red'>We cannot currently offer you bloodwork for hair loss. Please seek in-person care with a doctor.</h3>", "hideLabel": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.assay_choices.indexOf('HL-PNL') > -1 && (data.hair_loss == false || _.some(_.values(data.hair_loss_contraindication)));", "customConditional": "show = !!data.no_hair_panel"}, {"key": "no_twinrix", "type": "htmlelement", "attrs": [{"attr": "", "value": ""}], "input": true, "content": "<h4 class='text-red'>We cannot currently offer you a Twinrix Prescription.  Please seek in-person care for further counselling. </h4>", "hideLabel": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.assay_choices.indexOf('VX-HEP-AB') > -1 && (data.twinrix_three_doses == true || data.twinrix_contraindication == true || data.confirmed_hep_b_exposure == true);", "customConditional": "show = !!data.no_twinrix"}, {"key": "no_shingrix", "type": "htmlelement", "attrs": [{"attr": "", "value": ""}], "input": true, "content": "<h4 class='text-red'>We cannot currently offer you a Shingrix Prescription.  Please seek in-person care for further counselling. </h4>", "hideLabel": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.assay_choices.indexOf('VX-SHRX') > -1 && (data.shingrix_two_doses == true || data.shingrix_contraindication == true || data.confirmed_shingles_outbreak == true);", "customConditional": "show = !!data.no_shingrix"}, {"key": "no_dukoral", "type": "htmlelement", "attrs": [{"attr": "", "value": ""}], "input": true, "content": "<h4 class='text-red'>We cannot currently offer you a Dukoral Prescription.  Please seek in-person care for further counselling. </h4>", "hideLabel": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.assay_choices.indexOf('VX-DUKORAL') > -1 && (data.dukoral_pregnancy == true || data.vaccine_allergy == true || data.acte_illness_contraindication == true);", "customConditional": "show = !!data.no_dukoral"}, {"key": "pleaseClickSubmit", "type": "htmlelement", "attrs": [{"attr": "", "value": ""}], "input": false, "label": "HTML", "content": "To continue please click “Submit Form”", "tableView": false, "refreshOnChange": false, "customConditional": "show = !!data.showSubmit;"}]}