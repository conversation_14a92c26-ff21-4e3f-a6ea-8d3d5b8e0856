{"components": [{"key": "heading_intro_title", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "html": "<h1><strong>Keratosis Pilaris Intake - Your Current Concerns</strong></h1><p>To speed up your care, please answer a few quick questions about your skin. Your responses help us confirm that these bumps are KP and guide you to the best treatment.</p>"}, {"key": "heading_reason_consult", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "html": "</br><h4>Why You're Reaching Out About KP Today</h4>"}, {"key": "consult_reason_kp", "type": "selectboxes", "input": true, "label": "I would like help with:", "values": [{"label": "Renewal of a previous KP prescription", "value": "renewal"}, {"label": "Treatment for my current flare-up", "value": "current_flare"}, {"label": "Long-term maintenance to keep bumps down", "value": "maintenance"}, {"label": "Managing side-effects of my current treatment", "value": "side_effects"}, {"label": "Change in treatment options (e.g., switching products)", "value": "change_options"}, {"label": "Other", "value": "other"}, {"label": "None of the above", "value": "none", "customClass": "mt-n3"}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "confirm_label": "Reasons for consultation:", "optionsLabelPosition": "right"}, {"key": "consult_reason_kp_other", "type": "textarea", "input": true, "label": "Please tell us more:", "tableView": true, "autoExpand": false, "confirm_label": "Other reason:", "customConditional": "show = data.consult_reason_kp && (data.consult_reason_kp.other || data.consult_reason_kp.none);"}, {"key": "kp_diagnosis_known", "type": "radio", "input": true, "label": "Have you been formally diagnosed with keratosis pilaris by a healthcare provider?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "likely_kp_rash", "type": "radio", "input": true, "label": "KP looks like tiny, rough “chicken-skin” bumps (often on the backs of arms, thighs, or cheeks). Do you have a rash that matches this description?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "conditional": {"json": {"==": [{"var": "data.kp_diagnosis_known"}, "no"]}}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "rash_3_months", "type": "radio", "input": true, "label": "Have these bumps persisted for more than 3 months?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "conditional": {"json": {"==": [{"var": "data.kp_diagnosis_known"}, "no"]}}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "diagnosing_provider_kp", "type": "radio", "input": true, "label": "Who provided this diagnosis?", "values": [{"label": "Family doctor", "value": "family_doctor"}, {"label": "Dermatologist", "value": "dermatologist"}, {"label": "Nurse practitioner", "value": "nurse_practitioner"}, {"label": "Self-diagnosis", "value": "self"}, {"label": "Other", "value": "other"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Diagnosing provider:", "customConditional": "show = data.kp_diagnosis_known === 'yes';", "optionsLabelPosition": "right"}, {"key": "diagnosing_provider_kp_other", "type": "textarea", "input": true, "label": "Please specify who diagnosed you:", "tableView": true, "autoExpand": false, "confirm_label": "Other provider:", "customConditional": "show = data.diagnosing_provider_kp === 'other';"}, {"key": "heading_kp_onset_course", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "html": "</br><h4>Current KP Details</h4>"}, {"key": "kp_start_time", "type": "select", "input": true, "label": "When did you first notice these bumps?", "data": {"values": [{"label": "< 6 months ago", "value": "lt6mo"}, {"label": "6-12 months ago", "value": "6to12mo"}, {"label": "1-5 years ago", "value": "1to5y"}, {"label": "More than 5 years ago", "value": "gt5y"}, {"label": "Since childhood", "value": "since_childhood"}, {"label": "I'm not sure", "value": "unknown"}]}, "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "KP began:"}, {"key": "kp_course_since_onset", "type": "select", "input": true, "label": "Since it began, have your KP bumps mostly:", "data": {"values": [{"label": "Gotten better", "value": "better"}, {"label": "Gotten worse", "value": "worse"}, {"label": "Fluctuated up & down", "value": "fluctuating"}, {"label": "Stayed about the same", "value": "same"}]}, "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Course since onset:", "customConditional": "show = !!data.kp_start_time;"}, {"key": "kp_itch_severity", "type": "select", "input": true, "label": "How itchy are the bumps right now?", "data": {"values": [{"label": "Not itchy", "value": "itch_none"}, {"label": "Mild itch", "value": "itch_mild"}, {"label": "Moderate", "value": "itch_mod"}, {"label": "Severe", "value": "itch_severe"}]}, "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Itch level:", "customConditional": "show = !!data.kp_start_time;"}, {"key": "heading_kp_area_arms", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "html": "</br><h4>Arms &amp; Shoulders</h4>"}, {"key": "kp_site_arms", "type": "selectboxes", "input": true, "label": "Where on your arms / shoulders do you have KP bumps?", "values": [{"label": "Upper arms", "value": "upper_arms"}, {"label": "Forearms", "value": "forearms"}, {"label": "Shoulders", "value": "shoulders"}], "tableView": true, "confirm_label": "Arms / shoulders affected:", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_kp_site_arms", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "Select at least one option or choose 'None of the above'."}, "validate": {"custom": "valid = !!data.none_of_the_above_kp_site_arms || _.some(_.values(data.kp_site_arms));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "heading_kp_area_legs", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "html": "</br><h4>Legs &amp; Buttocks</h4>"}, {"key": "kp_site_legs", "type": "selectboxes", "input": true, "label": "Where on your legs / buttocks?", "values": [{"label": "Thighs", "value": "thighs"}, {"label": "<PERSON><PERSON>", "value": "calves"}, {"label": "Buttocks", "value": "buttocks"}], "tableView": true, "confirm_label": "Legs / buttocks affected:", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_kp_site_legs", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "Select at least one option or choose 'None of the above'."}, "validate": {"custom": "valid = !!data.none_of_the_above_kp_site_legs || _.some(_.values(data.kp_site_legs));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "kp_distribution_other", "type": "textarea", "input": true, "label": "Any other area(s) with KP?", "tableView": true, "autoExpand": false, "confirm_label": "Other areas:", "customConditional": "show = (data.kp_site_arms || data.kp_site_legs);"}, {"key": "heading_kp_triggers", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "html": "</br><h4>Triggers &amp; Worsening Factors</h4>"}, {"key": "kp_triggers_environment", "type": "selectboxes", "input": true, "label": "Which factors seem to worsen your KP? (Select all that apply)", "values": [{"label": "Cold or dry winter air", "value": "dry_air"}, {"label": "Hot showers / baths", "value": "hot_showers"}, {"label": "Friction from tight clothing", "value": "friction"}, {"label": "Sweating / exercise", "value": "sweat"}, {"label": "Chlorinated pools / hot tubs", "value": "chlorine"}, {"label": "Other", "value": "other"}, {"label": "None of the above", "value": "none", "customClass": "mt-n3"}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "confirm_label": "Environmental triggers:", "optionsLabelPosition": "right"}, {"key": "kp_triggers_environment_other", "type": "textarea", "input": true, "label": "Please specify other triggers:", "tableView": true, "autoExpand": false, "confirm_label": "Other triggers:", "customConditional": "show = data.kp_triggers_environment && data.kp_triggers_environment.other;"}, {"key": "heading_kp_products", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "html": "</br><h4>Treatments &amp; Products You've Tried</h4>"}, {"key": "kp_products_tried", "type": "selectboxes", "input": true, "label": "Which treatments have you used on your KP? (Select all that apply)", "values": [{"label": "Regular moisturizers (no active acids)", "value": "plain_moisturizer"}, {"label": "Urea creams (10-20%)", "value": "urea"}, {"label": "Lactic-acid lotions (e.g., AmLactin®)", "value": "lactic"}, {"label": "Salicylic-acid or BHA products", "value": "salicylic"}, {"label": "Glycolic-acid creams or pads", "value": "glycolic"}, {"label": "Topical retinoids (e.g., adapalene)", "value": "retinoid"}, {"label": "Physical scrubs / loofah / dry brushing", "value": "physical_exfoliant"}, {"label": "Other", "value": "other"}, {"label": "None of the above", "value": "none", "customClass": "mt-n3"}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "confirm_label": "Products tried:", "optionsLabelPosition": "right"}, {"key": "kp_products_tried_other", "type": "textarea", "input": true, "label": "Please specify other products:", "tableView": true, "autoExpand": false, "confirm_label": "Other products:", "customConditional": "show = data.kp_products_tried && data.kp_products_tried.other;"}, {"key": "kp_product_effectiveness", "type": "radio", "input": true, "label": "Have any of the products you tried helped noticeably?", "values": [{"label": "Yes - they help a lot", "value": "yes_good"}, {"label": "Somewhat helpful", "value": "yes_some"}, {"label": "No - no real change", "value": "no_effect"}, {"label": "Haven't tried treatments", "value": "not_tried"}], "validate": {"required": true}, "tableView": true, "confirm_label": "OTC effect:", "customConditional": "show = data.kp_products_tried && Object.values(data.kp_products_tried).some(Bo<PERSON>an);", "optionsLabelPosition": "right"}, {"key": "heading_kp_skin_care", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "html": "</br><h4>Daily Skin-Care Habits</h4>"}, {"key": "kp_uses_body_cleanser", "type": "radio", "input": true, "label": "Do you wash affected areas with a body cleanser?", "values": [{"label": "Yes, daily", "value": "daily"}, {"label": "Yes, a few times/wk", "value": "few_week"}, {"label": "Rarely / never", "value": "never"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Cleansing routine:", "optionsLabelPosition": "right"}, {"key": "kp_uses_moisturizer_freq", "type": "radio", "input": true, "label": "How often do you apply moisturizer to affected skin?", "values": [{"label": "Twice daily", "value": "twice_daily"}, {"label": "Once daily", "value": "once_daily"}, {"label": "Occasionally", "value": "sometimes"}, {"label": "Rarely", "value": "rarely"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Moisturizer use:", "optionsLabelPosition": "right"}, {"key": "photo_upload_header_kp", "type": "content", "input": false, "label": "Content", "html": "</br><h2>Photo Upload</h2><p>Please upload a clear photo of <strong>each affected area</strong>. Our doctors <em>require</em> these images in order to prescribe treatment. Good lighting is helpful.</p>"}, {"key": "uploadUrl", "url": "/app/q/{qpk}/formio-files/{pk}/", "type": "file", "image": true, "input": true, "label": "Upload: URL", "webcam": true, "capture": "user", "storage": "url", "multiple": true, "fileTypes": [{"label": "", "value": ""}], "tableView": true, "validateWhenHidden": false}, {"key": "photo_confirm_arms", "type": "checkbox", "input": true, "label": "I've uploaded at least one clear photo of my arm / shoulder KP.", "validate": {"required": true}, "tableView": false, "customConditional": "show = _.some(_.values(data.kp_site_arms || {}));"}, {"key": "photo_confirm_legs", "type": "checkbox", "input": true, "label": "I've uploaded at least one clear photo of my leg / buttocks KP.", "validate": {"required": true}, "tableView": false, "customConditional": "show = _.some(_.values(data.kp_site_legs || {}));"}, {"key": "intake_denial_reasons", "type": "textfield", "input": true, "label": "Intake Denial Reasons", "hidden": true, "disabled": true, "multiple": true, "adminFlag": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "value = [];", "refreshOnChange": true}, {"key": "intake_kind", "type": "textfield", "input": true, "label": "Intake Kind", "hidden": true, "disabled": true, "tableView": true, "clearOnHide": false, "calculateValue": "value = !data.intake_denial_reasons?.length ? 'mail' : 'chat';"}, {"key": "intake_mt_key", "type": "textfield", "input": true, "label": "Intake Mail Template Key", "hidden": true, "disabled": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.intake_kind === 'mail' ? 'intake-kp' : 'intake-denial';"}, {"key": "allowed_rt_keys", "type": "textfield", "input": true, "label": "Allowed Rep<PERSON>", "hidden": true, "disabled": true, "multiple": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.intake_mt_key === 'intake-denial' ? [] : ['get-req','get-rx','appointment-intake','edit-intake'];"}]}