{"components": [{"key": "vitd_interest", "type": "selectboxes", "input": true, "label": "Are you interested in any of the following?", "values": [{"label": "Vitamin D blood test", "value": "vitd_testing"}, {"label": "Prescription for Vitamin D", "value": "vitd_prescription"}], "adminFlag": true, "confirm_label": "Vitamin D Request:", "tableView": true, "optionsLabelPosition": "right"}, {"key": "none_of_the_above_vitd_interest", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select an option."}, "validate": {"custom": "valid = !!data.none_of_the_above_vitd_interest || _.some(_.values(data.vitd_interest));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "ci_vit_d_pnl", "type": "selectboxes", "input": true, "label": "Are you experiencing any of the following symptoms", "values": [{"label": "General malaise / feeling unwell", "value": "malaise"}, {"label": "Joint pain or muscle aches", "value": "myalgias"}, {"label": "Chest pain, palpitations, or shortness of breath", "value": "cardiac_respiratory_symptoms"}, {"label": "Abdominal Pain", "value": "abdo_pain"}, {"label": "Constipation, Diarrea or Vomiting", "value": "constipation_diarrhea_vomiting"}, {"label": "Frequent urination above my baseline/normal", "value": "polyuria"}, {"label": "Lightheaded/feeling faint", "value": "presy<PERSON><PERSON>"}], "adminFlag": true, "confirm_label": "Vitamin D Panel Symptoms:", "tableView": true, "optionsLabelPosition": "right"}, {"key": "none_of_the_above_ci_vit_d_pnl", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = !!data.none_of_the_above_ci_vit_d_pnl || _.some(_.values(data.ci_vit_d_pnl));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "ci_vit_d_pnl_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following symptoms:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "Vitamin D related symptoms not present", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.ci_vit_d_pnl, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "no_indications", "type": "textfield", "input": true, "label": "No Indication:", "hidden": true, "disabled": true, "multiple": false, "tableView": true, "clearOnHide": false, "defaultValue": true, "calculateValue": "value = !( _.some(_.values(data.vitd_interest)) );", "refreshOnChange": true}, {"key": "contraindication_keys", "type": "textfield", "input": true, "label": "Contraindication keys:", "hidden": true, "disabled": true, "multiple": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "value = _.keys(_.pickBy(data.ci_vit_d_pnl));", "refreshOnChange": true}, {"key": "approved_html", "type": "content", "input": true, "tableView": false, "clearOnHide": false, "defaultValue": "<h3 class='text-green'>You're eligible for Vitamin D testing.</h3></br><p>It's important to know:</p><ul><li>Vitamin D testing is <strong>not routinely recommended</strong> for most healthy adults</li><li>Best practice is to take <strong>1,200 IU daily supplementation on an ongoing basis (since we don't make enough from the sun in Canada)</strong></li><li>Testing is most helpful <strong>after at least 3 months of consistent supplementation</strong></li></ul><p>We can also provide <strong>prescription-strength Vitamin D</strong> when needed to correct low levels. Some people prefer a higher-dose option, such as <strong>50,000 IU once monthly</strong> on an ongoing basis, or <strong>once weekly for 8 weeks</strong> if levels are very low and a more rapid increase is desired.</p>"}, {"key": "rejected_html", "type": "content", "input": true, "tableView": false, "clearOnHide": false, "defaultValue": "<h3 class='text-red'>You are ineligible for Vitamin D testing at this time.</h3></br><p class='text-red'>Please seek in-person medical care for further evaluation.</p>", "refreshOnChange": true}]}