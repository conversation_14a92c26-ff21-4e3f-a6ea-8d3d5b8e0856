{"components": [{"key": "current_needlestick", "type": "radio", "input": true, "label": "Have you had a needlestick injury that occured within the last 7 days?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "immunity_testing_contraindications", "type": "selectboxes", "input": true, "label": "Are you concerned about recent exposure to any of the following diseases:", "values": [{"label": "<PERSON><PERSON><PERSON>", "value": "measles", "shortcut": ""}, {"label": "<PERSON><PERSON>", "value": "mumps", "shortcut": ""}, {"label": "<PERSON><PERSON><PERSON>", "value": "rubella", "shortcut": ""}, {"label": "Varicella (Chicken Pox or Shingles)", "value": "aids", "shortcut": ""}, {"label": "Hepatitis A", "value": "muscle_aches_cramps", "shortcut": ""}, {"label": "Hepatitis B", "value": "chest_pain_pressure", "shortcut": ""}, {"label": "Hepatitis C", "value": "palpitations", "shortcut": ""}, {"label": "HIV", "value": "hiv", "shortcut": ""}, {"label": "Syphilis", "value": "vdrl", "shortcut": ""}], "inputType": "checkbox", "tableView": false, "customConditional": "show = data.current_needlestick == false;", "optionsLabelPosition": "right"}, {"key": "unexposed", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = _.some(_.values(data.immunity_testing_contraindications)) || data.unexposed;"}, "tableView": false, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.current_needlestick == false;"}, {"key": "indication_testing", "type": "radio", "input": true, "label": "Do you currently require bloodwork for school or employment purposes?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = !_.some(_.values(data.immunity_testing_contraindications)) && data.current_needlestick == false;", "optionsLabelPosition": "right"}]}