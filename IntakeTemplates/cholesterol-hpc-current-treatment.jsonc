{"components": [{"key": "heading_current_treatments", "html": "<h3>Current Cholesterol Medicines</h3><p>Tell us what you are taking right now. This helps us match your plan to your goals and lab results.</p>", "type": "content", "input": false, "label": "Content", "tableView": true, "refreshOnChange": false}, {"key": "on_lipid_medications", "type": "radio", "input": true, "label": "Are you currently taking any cholesterol medication?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Currently on cholesterol medication:", "refreshOnChange": false}, {"key": "heading_current_meds_grid", "html": "<h3>List Your Current Medicines</h3><p>Add one medicine per card. Choose the name, pick the dose, and how often you take it. If you are not sure of the exact dose, choose the closest option or select Other.</p>", "type": "content", "input": false, "label": "Content", "tableView": true, "refreshOnChange": false, "customConditional": "show = data.on_lipid_medications === 'yes';"}, {"key": "current_meds_grid", "type": "<PERSON><PERSON><PERSON>", "label": "Add each current medication:", "removeRow": true, "tableView": false, "templates": {"row": "<div class='card shadow-sm w-100'><div class='card-body py-2 px-3'><div class='d-flex justify-content-between align-items-start small'><div class='me-2'><div class='fw-bold mb-1'>{{ row.medication_label || 'Medication ?' }}{{ (row.dose && row.dose.label) ? (' — ' + row.dose.label) : (row.dose && row.dose.value) ? (' — ' + row.dose.value) : (row.dose ? (' — ' + row.dose) : '') }}{{ row.frequency ? (' (' + ({daily:'daily', q48h:'every other day', bid:'twice daily', weekly:'weekly', q2w:'every 2 weeks', q4w:'monthly', q6mo:'every 6 months', other:'other'}[row.frequency] || row.frequency) + ')') : '' }}</div><div>Started: {{ (row.start_month==='unknown' ? 'Month ?' : _.startCase(row.start_month)) }} {{ row.start_year || '' }} &nbsp; • &nbsp; Current dose duration: {{ ({w2_4:'2–4 weeks', m1_3:'1–3 months', m3_6:'3–6 months', m6_12:'6–12 months', y1_3:'1–3 years', y3p:'3+ years'}[row.current_dose_duration] || 'Not specified') }}</div><div class='mt-1'>Side effects: {{ (function(){ if(row.none_of_the_above_side_effects) return 'None'; var list=[]; var g=row.med_side_effects_general||{}; list=list.concat(_.keys(_.pickBy(g))); if(['evolocumab','alirocumab'].includes(row.med_key) && row.injection_site_reaction){ list.push('injection_site'); } if(!list.length) return 'Not specified'; return _.startCase(list.join(', ').replace(/_/g,' ')); })() }}</div></div><div><button class='btn btn-sm btn-outline-danger removeRow'>Delete</button></div></div></div></div>", "header": ""}, "addAnother": "+ Add Another Medication", "components": [{"key": "med_and_dose_row", "type": "columns", "columns": [{"width": 7, "components": [{"key": "medication", "type": "radio", "input": true, "label": "Select the medication", "values": [{"label": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Lipitor)", "value": "atorvastatin"}, {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Crestor)", "value": "ros<PERSON><PERSON><PERSON><PERSON>"}, {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Zocor)", "value": "simvas<PERSON>in"}, {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Pravachol)", "value": "pravastatin"}, {"label": "Ezetimibe (Ezetrol)", "value": "ezetimibe"}, {"label": "Evolocumab (Repatha)", "value": "evolocumab"}, {"label": "Alirocumab (Praluent)", "value": "aliro<PERSON><PERSON>"}, {"label": "Other (specify)", "value": "other"}], "validate": {"required": true}, "tableView": true}, {"key": "med_key", "type": "textfield", "input": true, "label": "Selected medication key", "hidden": true, "tableView": true, "clearOnHide": false, "calculateValue": "value = row.medication || '';"}, {"key": "medication_label", "type": "textfield", "input": true, "label": "Selected medication label", "hidden": true, "tableView": true, "clearOnHide": false, "calculateValue": "var map={atorvastatin:'<PERSON><PERSON><PERSON><PERSON><PERSON> (Lipitor)', rosuvastatin:'<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Crestor)', simvastatin:'<PERSON><PERSON><PERSON><PERSON><PERSON> (Zocor)', pravastatin:'<PERSON><PERSON><PERSON><PERSON><PERSON> (Pravachol)', ezetimibe:'<PERSON><PERSON><PERSON><PERSON><PERSON> (Ezetrol)', evolocumab:'<PERSON><PERSON><PERSON><PERSON><PERSON> (Repatha)', alirocumab:'<PERSON><PERSON><PERSON><PERSON> (Praluent)', other:(row.other_medication_name||'Other')}; value = map[row.med_key] || '';"}, {"key": "other_medication_name", "type": "textfield", "input": true, "label": "Other medication name", "tableView": true, "placeholder": "e.g., Fluvastatin (Lescol)", "customConditional": "show = row.med_key === 'other';"}]}, {"width": 5, "components": [{"key": "dose", "data": {"custom": "var map={atorvastatin:['5 mg','10 mg','20 mg','40 mg','80 mg'],rosuvastatin:['5 mg','10 mg','20 mg','40 mg'],simvastatin:['10 mg','20 mg','40 mg'],pravastatin:['10 mg','20 mg','40 mg','80 mg'],ezetimibe:['10 mg']}; var k=row.med_key||''; var arr=(map[k]||[]).map(function(d){return {label:d, value:d};}); if(k==='other'){arr=[{label:'1 mg',value:'1 mg'},{label:'2 mg',value:'2 mg'},{label:'5 mg',value:'5 mg'},{label:'10 mg',value:'10 mg'},{label:'20 mg',value:'20 mg'},{label:'40 mg',value:'40 mg'}];} values = arr;"}, "type": "select", "input": true, "label": "<PERSON><PERSON>", "widget": "html5", "dataSrc": "custom", "tableView": true, "valueProperty": "value", "calculateValue": "if (value && typeof value === 'object') { value = value.value || value.label || ''; }", "customConditional": "show = ['atorvastatin','rosuvastatin','simvastatin','pravastatin','ezetimibe'].includes(row.med_key) || row.med_key === 'other';"}, {"key": "dose_na_note", "html": "<div class='text-muted small'>Dose not required for this selection.</div>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = ['evolocumab','alirocumab'].includes(row.med_key);"}]}], "tableView": false}, {"key": "frequency", "data": {"values": [{"label": "Daily", "value": "daily"}, {"label": "Every other day", "value": "q48h"}, {"label": "Twice daily", "value": "bid"}, {"label": "Weekly", "value": "weekly"}, {"label": "Every 2 weeks", "value": "q2w"}, {"label": "Monthly", "value": "q4w"}, {"label": "Every 6 months", "value": "q6mo"}, {"label": "Other", "value": "other"}]}, "type": "select", "input": true, "label": "How often do you take it?", "widget": "html5", "tableView": true, "calculateValue": "if(!value){ var k=row.med_key||''; if(['atorvastatin','rosuvastatin','simvastatin','pravastatin','ezetimibe','other'].includes(k)) value='daily'; else if(['evolocumab','alirocumab'].includes(k)) value='q2w'; }"}, {"key": "start_date_row", "type": "columns", "columns": [{"width": 6, "components": [{"key": "start_month", "data": {"values": [{"label": "Unknown", "value": "unknown"}, {"label": "January", "value": "jan"}, {"label": "February", "value": "feb"}, {"label": "March", "value": "mar"}, {"label": "April", "value": "apr"}, {"label": "May", "value": "may"}, {"label": "June", "value": "jun"}, {"label": "July", "value": "jul"}, {"label": "August", "value": "aug"}, {"label": "September", "value": "sep"}, {"label": "October", "value": "oct"}, {"label": "November", "value": "nov"}, {"label": "December", "value": "dec"}]}, "type": "select", "input": true, "label": "Start month", "widget": "html5", "tableView": true}]}, {"width": 6, "components": [{"key": "start_year", "type": "number", "input": true, "label": "Start year (YYYY)", "validate": {"max": 2100, "min": 1900}, "tableView": true, "placeholder": "e.g., 2024"}]}]}, {"key": "current_dose_duration", "data": {"values": [{"label": "2–4 weeks", "value": "w2_4"}, {"label": "1–3 months", "value": "m1_3"}, {"label": "3–6 months", "value": "m3_6"}, {"label": "6–12 months", "value": "m6_12"}, {"label": "1–3 years", "value": "y1_3"}, {"label": "3+ years", "value": "y3p"}]}, "type": "select", "input": true, "label": "How long at the current dose?", "widget": "html5", "tableView": true}, {"key": "med_side_effects_general", "type": "selectboxes", "input": true, "label": "Have you had any of the following side effects while taking this medication?", "values": [{"label": "Muscle pain/aches (myalgia)", "value": "muscle_pain"}, {"label": "Weakness or cramps", "value": "weakness_cramps"}, {"label": "Dark urine or severe muscle pain", "value": "dark_urine"}, {"label": "Abdominal pain or nausea", "value": "abdominal_pain"}, {"label": "Elevated liver enzymes noted", "value": "elevated_lfts"}, {"label": "Memory or concentration changes", "value": "cognitive"}, {"label": "Rash or allergy", "value": "rash"}, {"label": "Other side effect", "value": "other_effect"}], "tableView": true, "optionsLabelPosition": "right"}, {"key": "injection_site_reaction", "type": "checkbox", "input": true, "label": "Injection site reaction", "tableView": true, "customConditional": "show = ['evolocumab','alirocumab'].includes(row.med_key);"}, {"key": "none_of_the_above_side_effects", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a side effect."}, "validate": {"custom": "var anyGen=_.some(_.values(row.med_side_effects_general)); var anyInj=(['evolocumab','alirocumab'].includes(row.med_key)) ? !!row.injection_site_reaction : false; valid = !!row.none_of_the_above_side_effects || anyGen || anyInj;"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "other_side_effect_text", "type": "textfield", "input": true, "label": "If other, please describe", "tableView": true, "customConditional": "show = !!(row.med_side_effects_general && row.med_side_effects_general.other_effect);"}], "customClass": "treatment-nohdr", "defaultOpen": true, "rowTemplate": "<div class='card shadow-sm w-100'><div class='card-body py-2 px-3'><div class='d-flex justify-content-between align-items-start small'><div class='me-2'><div class='fw-bold mb-1'>{{ row.medication_label || 'Medication ?' }}{{ (row.dose && row.dose.label) ? (' — ' + row.dose.label) : (row.dose && row.dose.value) ? (' — ' + row.dose.value) : (row.dose ? (' — ' + row.dose) : '') }}{{ row.frequency ? (' (' + ({daily:'daily', q48h:'every other day', bid:'twice daily', weekly:'weekly', q2w:'every 2 weeks', q4w:'monthly', q6mo:'every 6 months', other:'other'}[row.frequency] || row.frequency) + ')') : '' }}</div><div>Started: {{ (row.start_month==='unknown' ? 'Month ?' : _.startCase(row.start_month)) }} {{ row.start_year || '' }} &nbsp; • &nbsp; Current dose duration: {{ ({w2_4:'2–4 weeks', m1_3:'1–3 months', m3_6:'3–6 months', m6_12:'6–12 months', y1_3:'1–3 years', y3p:'3+ years'}[row.current_dose_duration] || 'Not specified') }}</div><div class='mt-1'>Side effects: {{ (function(){ if(row.none_of_the_above_side_effects) return 'None'; var list=[]; var g=row.med_side_effects_general||{}; list=list.concat(_.keys(_.pickBy(g))); if(['evolocumab','alirocumab'].includes(row.med_key) && row.injection_site_reaction){ list.push('injection_site'); } if(!list.length) return 'Not specified'; return _.startCase(list.join(', ').replace(/_/g,' ')); })() }}</div></div><div><button class='btn btn-sm btn-outline-danger removeRow'>Delete</button></div></div></div></div>", "openWhenEmpty": true, "displayAsTable": false, "useRowTemplate": true, "refreshOnChange": false, "customConditional": "show = data.on_lipid_medications === 'yes';"}, {"key": "current_meds_summary", "type": "textarea", "input": true, "label": "Current medications (summary)", "hidden": true, "disabled": true, "tableView": true, "autoExpand": true, "clearOnHide": false, "confirm_label": "Current Medications:", "calculateValue": "var rows = data.current_meds_grid || []; var freqMap={daily:'daily', q48h:'every other day', bid:'twice daily', weekly:'weekly', q2w:'every 2 weeks', q4w:'monthly', q6mo:'every 6 months', other:'other'}; var durMap={w2_4:'2–4 weeks', m1_3:'1–3 months', m3_6:'3–6 months', m6_12:'6–12 months', y1_3:'1–3 years', y3p:'3+ years'}; var mon={jan:'Jan', feb:'Feb', mar:'Mar', apr:'Apr', may:'May', jun:'Jun', jul:'Jul', aug:'Aug', sep:'Sep', oct:'Oct', nov:'Nov', dec:'Dec', unknown:'?'}; value = rows.map(function(r){ var dose = r.dose && r.dose.label ? r.dose.label : (r.dose && r.dose.value ? r.dose.value : (r.dose || '')); var started = (mon[r.start_month]||'') + (r.start_year?(' '+r.start_year):''); var seList=[]; if(r.none_of_the_above_side_effects){seList=['None'];} else { var g=r.med_side_effects_general||{}; seList=_.keys(_.pickBy(g)); if(['evolocumab','alirocumab'].includes(r.med_key) && r.injection_site_reaction){ seList.push('injection_site'); } if(!seList.length){ seList=['Not specified']; } } var seText=_.startCase(seList.join(', ').replace(/_/g,' ')); return (r.medication_label||'Medication ?') + (dose?(' — '+dose):'') + (r.frequency?(' ('+(freqMap[r.frequency]||r.frequency)+')'):'') + ' | Started: ' + (started||'—') + ' | Current dose duration: ' + (durMap[r.current_dose_duration]||'—') + ' | SE: ' + seText; }).join('\\n');"}, {"key": "divider_to_dose_changes", "html": "<hr class='my-4'/>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.on_lipid_medications === 'yes';"}, {"key": "heading_dose_changes", "html": "<h3>Changes to Your Dose</h3><p>If your medicine was increased, decreased, stopped, restarted, or switched <strong>in the last 12 months</strong>, add the change below.</p>", "type": "content", "input": false, "label": "Content", "tableView": true, "refreshOnChange": false, "customConditional": "show = data.on_lipid_medications === 'yes';"}, {"key": "dose_change_last_12mo", "type": "radio", "input": true, "label": "In the last 12 months, has any cholesterol medicine dose been changed?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Dose change in last 12 months:", "refreshOnChange": false, "customConditional": "show = data.on_lipid_medications === 'yes';"}, {"key": "dose_change_grid", "type": "<PERSON><PERSON><PERSON>", "label": "Add prior dose changes", "removeRow": true, "tableView": false, "templates": {"row": "<div class='card shadow-sm w-100'><div class='card-body py-2 px-3'><div class='d-flex justify-content-between align-items-start small w-100'><div><div class='fw-bold mb-1'>{{ row.medication_label || 'Medication ?' }} — {{ (row.prior_dose && row.prior_dose.label) ? row.prior_dose.label : (row.prior_dose && row.prior_dose.value) ? row.prior_dose.value : (row.prior_dose || '?') }} {{ (row.change_type==='stopped') ? '→ stopped' : ('→ ' + ((row.new_dose && row.new_dose.label) ? row.new_dose.label : (row.new_dose && row.new_dose.value) ? row.new_dose.value : (row.new_dose || '?'))) }}</div><div>{{ (row.change_month==='unknown' ? 'Month ?' : _.startCase(row.change_month)) }} {{ row.change_year || '' }} • Type: {{ ({increased:'Dose increased', decreased:'Dose decreased', stopped:'Stopped this medicine', restarted:'Restarted this medicine'}[row.change_type] || '—') }} • Reason(s): {{ (function(){ var r=_.keys(_.pickBy(row.reason_for_change||{})); return r.length?_.startCase(r.join(', ').replace(/_/g,' ')):'Not specified'; })() }}</div><div class='mt-1'>Side effects at the time: {{ (function(){ var list=[]; var g=row.history_side_effects_general||{}; list=list.concat(_.keys(_.pickBy(g))); if(['evolocumab','alirocumab'].includes(row.med_key) && row.history_injection_site_reaction){ list.push('injection_site'); } return list.length?_.startCase(list.join(', ').replace(/_/g,' ')):'None reported'; })() }}</div></div><div><button class='btn btn-sm btn-outline-danger removeRow'>Delete</button></div></div></div></div>", "header": ""}, "addAnother": "+ Add Dose Change", "components": [{"key": "medication", "type": "radio", "input": true, "label": "Medication changed", "values": [{"label": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Lipitor)", "value": "atorvastatin"}, {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Crestor)", "value": "ros<PERSON><PERSON><PERSON><PERSON>"}, {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Zocor)", "value": "simvas<PERSON>in"}, {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Pravachol)", "value": "pravastatin"}, {"label": "Ezetimibe (Ezetrol)", "value": "ezetimibe"}, {"label": "Evolocumab (Repatha)", "value": "evolocumab"}, {"label": "Alirocumab (Praluent)", "value": "aliro<PERSON><PERSON>"}, {"label": "Other (specify)", "value": "other"}], "validate": {"required": true}, "tableView": true}, {"key": "med_key", "type": "textfield", "input": true, "label": "Selected medication key", "hidden": true, "tableView": true, "clearOnHide": false, "calculateValue": "value = row.medication || '';"}, {"key": "medication_label", "type": "textfield", "input": true, "label": "Selected medication label", "hidden": true, "tableView": true, "clearOnHide": false, "calculateValue": "var map={atorvastatin:'<PERSON><PERSON><PERSON><PERSON><PERSON> (Lipitor)', rosuvastatin:'<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Crestor)', simvastatin:'<PERSON><PERSON><PERSON><PERSON><PERSON> (Zocor)', pravastatin:'<PERSON><PERSON><PERSON><PERSON><PERSON> (Pravachol)', ezetimibe:'<PERSON><PERSON><PERSON><PERSON><PERSON> (Ezetrol)', evolocumab:'<PERSON><PERSON><PERSON><PERSON><PERSON> (Repatha)', alirocumab:'<PERSON><PERSON><PERSON><PERSON> (Praluent)', other:(row.other_medication_name||'Other')}; value = map[row.med_key] || '';"}, {"key": "other_medication_name", "type": "textfield", "input": true, "label": "Other medication name", "tableView": true, "placeholder": "e.g., Fluvastatin (Lescol)", "customConditional": "show = row.med_key === 'other';"}, {"key": "change_type", "type": "radio", "input": true, "label": "Type of change", "values": [{"label": "Dose increased", "value": "increased"}, {"label": "Dose decreased", "value": "decreased"}, {"label": "Stopped this medicine", "value": "stopped"}, {"label": "Restarted this medicine", "value": "restarted"}], "validate": {"required": true}, "tableView": true}, {"key": "dose_from_to_row", "type": "columns", "columns": [{"width": 6, "components": [{"key": "prior_dose", "data": {"custom": "var map={atorvastatin:['5 mg','10 mg','20 mg','40 mg','80 mg'],rosuvastatin:['5 mg','10 mg','20 mg','40 mg'],simvastatin:['10 mg','20 mg','40 mg'],pravastatin:['10 mg','20 mg','40 mg','80 mg'],ezetimibe:['10 mg'],other:['1 mg','2 mg','5 mg','10 mg','20 mg','40 mg']}; var k=row.med_key||''; values=(map[k]||[]).map(function(d){return {label:d, value:d};});"}, "type": "select", "input": true, "label": "Prior dose", "widget": "html5", "dataSrc": "custom", "tableView": true, "valueProperty": "value", "calculateValue": "if (value && typeof value === 'object') { value = value.value || value.label || ''; }"}]}, {"width": 6, "components": [{"key": "new_dose", "data": {"custom": "var map={atorvastatin:['5 mg','10 mg','20 mg','40 mg','80 mg'],rosuvastatin:['5 mg','10 mg','20 mg','40 mg'],simvastatin:['10 mg','20 mg','40 mg'],pravastatin:['10 mg','20 mg','40 mg','80 mg'],ezetimibe:['10 mg'],other:['1 mg','2 mg','5 mg','10 mg','20 mg','40 mg']}; var k=row.med_key||''; values=(map[k]||[]).map(function(d){return {label:d, value:d};});"}, "type": "select", "input": true, "label": "New dose", "widget": "html5", "dataSrc": "custom", "tableView": true, "valueProperty": "value", "calculateValue": "if (value && typeof value === 'object') { value = value.value || value.label || ''; }", "customConditional": "show = row.change_type !== 'stopped';"}]}]}, {"key": "change_month", "data": {"values": [{"label": "Unknown", "value": "unknown"}, {"label": "January", "value": "jan"}, {"label": "February", "value": "feb"}, {"label": "March", "value": "mar"}, {"label": "April", "value": "apr"}, {"label": "May", "value": "may"}, {"label": "June", "value": "jun"}, {"label": "July", "value": "jul"}, {"label": "August", "value": "aug"}, {"label": "September", "value": "sep"}, {"label": "October", "value": "oct"}, {"label": "November", "value": "nov"}, {"label": "December", "value": "dec"}]}, "type": "select", "input": true, "label": "Change month", "widget": "html5", "tableView": true}, {"key": "change_year", "type": "number", "input": true, "label": "Change year (YYYY)", "validate": {"max": 2100, "min": 1900}, "tableView": true, "placeholder": "e.g., 2024"}, {"key": "reason_for_change", "type": "selectboxes", "input": true, "label": "Why did the dose change?", "values": [{"label": "Side effects at higher dose", "value": "side_effects"}, {"label": "Not enough improvement in cholesterol", "value": "limited_benefit"}, {"label": "Doctor/clinic recommendation", "value": "provider_recommendation"}, {"label": "Cost or insurance issues", "value": "cost_access"}, {"label": "Hard to take regularly", "value": "adherence"}, {"label": "I chose not to keep taking it", "value": "patient_preference"}, {"label": "Other", "value": "other"}], "tableView": true, "optionsLabelPosition": "right"}, {"key": "history_side_effects_general", "type": "selectboxes", "input": true, "label": "Side effects at the time (if any)", "errors": {"custom": "Select \"None\" by itself, or choose side effects."}, "values": [{"label": "None", "value": "none"}, {"label": "Muscle pain/aches (myalgia)", "value": "muscle_pain"}, {"label": "Weakness or cramps", "value": "weakness_cramps"}, {"label": "Dark urine or severe muscle pain", "value": "dark_urine"}, {"label": "Abdominal pain or nausea", "value": "abdominal_pain"}, {"label": "Elevated liver enzymes noted", "value": "elevated_lfts"}, {"label": "Memory or concentration changes", "value": "cognitive"}, {"label": "Rash or allergy", "value": "rash"}, {"label": "Other side effect", "value": "other_effect"}], "validate": {"custom": "var v=row.history_side_effects_general||{}; var keys=Object.keys(v).filter(function(k){return v[k];}); var inj=(['evolocumab','alirocumab'].includes(row.med_key) && row.history_injection_site_reaction); if(keys.indexOf('none')>-1 && (keys.length>1 || inj)){ valid=false; } else { valid=true; }"}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "history_injection_site_reaction", "type": "checkbox", "input": true, "label": "Injection site reaction", "tableView": true, "customConditional": "show = ['evolocumab','alirocumab'].includes(row.med_key);"}, {"key": "history_notes", "type": "textfield", "input": true, "label": "Notes (optional)", "tableView": true}], "customClass": "dosehistory-nohdr", "defaultOpen": false, "rowTemplate": "<div class='card shadow-sm w-100'><div class='card-body py-2 px-3'><div class='d-flex justify-content-between align-items-start small w-100'><div><div class='fw-bold mb-1'>{{ row.medication_label || 'Medication ?' }} — {{ (row.prior_dose && row.prior_dose.label) ? row.prior_dose.label : (row.prior_dose && row.prior_dose.value) ? row.prior_dose.value : (row.prior_dose || '?') }} {{ (row.change_type==='stopped') ? '→ stopped' : ('→ ' + ((row.new_dose && row.new_dose.label) ? row.new_dose.label : (row.new_dose && row.new_dose.value) ? row.new_dose.value : (row.new_dose || '?'))) }}</div><div>{{ (row.change_month==='unknown' ? 'Month ?' : _.startCase(row.change_month)) }} {{ row.change_year || '' }} • Type: {{ ({increased:'Dose increased', decreased:'Dose decreased', stopped:'Stopped this medicine', restarted:'Restarted this medicine'}[row.change_type] || '—') }} • Reason(s): {{ (function(){ var r=_.keys(_.pickBy(row.reason_for_change||{})); return r.length?_.startCase(r.join(', ').replace(/_/g,' ')):'Not specified'; })() }}</div><div class='mt-1'>Side effects at the time: {{ (function(){ var list=[]; var g=row.history_side_effects_general||{}; list=list.concat(_.keys(_.pickBy(g))); if(['evolocumab','alirocumab'].includes(row.med_key) && row.history_injection_site_reaction){ list.push('injection_site'); } return list.length?_.startCase(list.join(', ').replace(/_/g,' ')):'None reported'; })() }}</div></div><div><button class='btn btn-sm btn-outline-danger removeRow'>Delete</button></div></div></div></div>", "openWhenEmpty": false, "displayAsTable": false, "useRowTemplate": true, "refreshOnChange": false, "customConditional": "show = data.on_lipid_medications === 'yes' && data.dose_change_last_12mo === 'yes';"}]}