{"name": "redFlags", "type": "form", "title": "Red Flags", "display": "form", "components": [{"key": "new_std_symptoms", "type": "radio", "input": true, "label": "Since the time of your appointment booking, have you developed any new symptoms?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": false, "clearOnHide": false, "defaultValue": false, "customConditional": "show = !data.vca;", "optionsLabelPosition": "right"}, {"key": "home-kit", "type": "checkbox", "input": true, "label": "I understand TeleTest does NOT send out home STI kits unless I require oral or anal swabs for site-specific screening.", "hidden": false, "validate": {"required": true}, "tableView": false, "clearOnHide": false, "defaultValue": true, "customConditional": "show = !!data.vca;"}, {"key": "std_red_flag_symptoms", "type": "selectboxes", "input": true, "label": "I have the following symptoms:", "values": [{"label": "Abdominal/pelvic pain or cramping, back pain, or rectal pain", "value": "abdominal_pain_back_pain_or_rectal_pain", "shortcut": ""}, {"label": "Blood in my urine", "value": "blood_in_urine", "shortcut": ""}, {"label": "Fever", "value": "fever", "shortcut": ""}, {"label": "<PERSON>well", "value": "feel_unwell", "shortcut": ""}, {"label": "Unable to urinate, leaking urine, or cannot control my bladder", "value": "unable_to_urinate_or_leaking_urine", "shortcut": ""}, {"label": "Excessive Thirst", "value": "excessive_thirst", "shortcut": ""}, {"label": "<PERSON><PERSON>", "value": "genital_rash", "shortcut": ""}, {"label": "Testicular Pain", "value": "testicular_pain", "shortcut": "", "customConditional": "show = data.sex == 'male';"}], "inputType": "checkbox", "tableView": false, "customConditional": "show = !data.needle_stick;", "optionsLabelPosition": "right"}, {"key": "red_flags_std", "type": "textfield", "input": true, "label": "Patient indicated they had the following red flag symptoms:", "hidden": true, "disabled": true, "multiple": false, "adminFlag": true, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "calculateValue": "value = !_.some(_.values(data.std_red_flag_symptoms)) ? 'None' : _.replace(_.join(_.map(_.keys(_.pickBy(data.std_red_flag_symptoms)), _.capitalize), ', '), /_/g, ' ');"}, {"key": "std_red_flag_symptoms_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following red flag symptoms:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "I DO NOT have the following symptoms:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.std_red_flag_symptoms, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "uti_symptoms", "type": "selectboxes", "input": true, "label": "UTI symptoms:", "values": [{"label": "Burning or pain when urinating", "value": "burning_or_pain_when_urinating", "shortcut": ""}, {"label": "Frequent urination", "value": "frequent_urination", "shortcut": ""}, {"label": "Urgency to urinate", "value": "urgency_to_urinate", "shortcut": ""}, {"label": "A feeling of incomplete emptying after urination", "value": "feeling_of_incomplete_emptying", "shortcut": ""}], "adminFlag": true, "hideLabel": true, "inputType": "checkbox", "tableView": true, "customClass": "mt-n3", "customConditional": "show = !data.needle_stick;", "optionsLabelPosition": "right"}, {"key": "other_std_symptoms", "type": "selectboxes", "input": true, "label": "Other STD symptoms:", "values": [{"label": "Bleeding after sex", "value": "bleeding_after_sex", "shortcut": "", "customConditional": "show = data.sex == 'female'"}, {"label": "Vaginal discharge or itchiness", "value": "vaginal_discharge_or_itchiness", "shortcut": "", "customConditional": "show = data.sex == 'female'"}, {"label": "Penile discharge", "value": "penile_discharge", "shortcut": "", "customConditional": "show = data.sex == 'male'"}, {"label": "Tingling at the tip of the penis", "value": "penile_tingling", "shortcut": "", "customConditional": "show = data.sex == 'male'"}], "adminFlag": true, "hideLabel": true, "inputType": "checkbox", "tableView": true, "customClass": "mt-n3", "refreshOnChange": true, "customConditional": "show = !data.needle_stick;", "optionsLabelPosition": "right"}, {"key": "no_std_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = !!data.no_std_symptoms || !!_.some(_.values(data.std_red_flag_symptoms)) || !!_.some(_.values(data.uti_symptoms)) || !!_.some(_.values(data.other_std_symptoms));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = !data.needle_stick;"}, {"key": "symptoms_greater_than_7_days", "type": "radio", "input": true, "label": "Have you had symptoms for longer than 7 days?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": false, "customConditional": "show = !data.needle_stick && (!!_.some(_.values(data.std_red_flag_symptoms)) || !!_.some(_.values(data.uti_symptoms)) || !!_.some(_.values(data.other_std_symptoms)));", "optionsLabelPosition": "right"}, {"key": "pregnant", "type": "radio", "input": true, "label": "Are you pregnant?", "inline": false, "values": [{"label": "Yes/don’t know", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": false, "defaultValue": false, "confirm_label": "Pregnant:", "customConditional": "show = data.sex != 'male' && !data.needle_stick && !_.some(_.values(data.std_red_flag_symptoms));", "optionsLabelPosition": "right"}, {"key": "immunosuppression", "type": "radio", "input": true, "label": "Are you on immunosuppression?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No/don’t know what that is", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": false, "defaultValue": false, "customConditional": "show = !data.needle_stick && !_.some(_.values(data.std_red_flag_symptoms));", "optionsLabelPosition": "right"}]}