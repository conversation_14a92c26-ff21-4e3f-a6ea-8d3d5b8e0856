{% load template_extras %}
{% with data=questionnaire.hpc.data %}
{% with insured=questionnaire.insured_assays.all %}
{% with uninsured=questionnaire.uninsured_assays.all %}
<p>Hi {{patient.name}},</p>
<p>This is {{doctor.name}} (CPSO #{{doctor.cpso_number}}) &amp; clinic contact phone {{doctor.phone}}.</p>

<p>I've reviewed your responses. If you felt that you did not understand any questions or that your answers didn’t fully describe your symptoms, blood pressure history, or medication details, we can arrange a secure messaging appointment. Otherwise, we can proceed with the plan outlined below.</p>

<p class="mb-0"><strong>By continuing, you confirm:</strong></p>
<ul>
    <li>You are requesting a renewal for your blood pressure medication</li>
    <li>You understand that dose changes or adjustments may require in-person monitoring</li>
    <li>You will seek urgent or emergency care if you develop concerning symptoms 
        (such as chest pain, shortness of breath, fainting, dizziness, swelling, vision changes, or severe headache)</li>
    <li>You understand the importance of routine follow-up and monitoring</li>
    <li>You agree that the information provided in this intake is accurate and complete</li>
</ul>

{% with rxts=questionnaire.rxts.all %}
{% if rxts %}
  <h3 style="text-align:center;"><strong>Prescription Issued</strong></h3>
  <p>Based on your medical history, recent monitoring, and current assessment, the following prescription has been provided:</p>
  <ul>
    {% for rx in rxts %}
      <li>{{ rx.display_name }}</li>
    {% endfor %}
  </ul>
{% endif %}
{% endwith %}

{% if insured.exists or uninsured.exists %}
  <p class="mb-0">Lab testing for the following:</p>

  {% if insured.exists %}
    <strong class="mb-0">Insured</strong>
    <ul>
      {% for a in insured %}
        <li>{{a.name}} ({{a.test_type}} test)</li>
      {% endfor %}
    </ul>
  {% endif %}

  {% if uninsured.exists %}
    <strong class="mb-0">Uninsured</strong>
    <ul>
      {% for a in uninsured %}
        <li>{{a.name}} ({{a.test_type}} test)</li>
      {% endfor %}
    </ul>
  {% endif %}
{% endif %}


<!-- =================  MEDICAL HISTORY SUMMARY (HTN)  ================= -->
{% with data=questionnaire.hpc.data summary=questionnaire.raw_formio_summary %}
<h3 style="text-align:center;"><strong>Medical History Summary</strong></h3>
<ul>
  {# Reason for consultation #}
  {% with ind_list=summary|confirm:"consultation_indication,consultation_indication_other" %}
  {% if ind_list %}
    <li><strong>Reason for consultation</strong>
      <ul>
        {% for qa in ind_list %}
          {% if ":" in qa %}
            <li>{% autoescape off %}{{ qa|replace:": |: <strong>" }}{% endautoescape %}</strong></li>
          {% else %}
            <li>{{ qa|safe }}</li>
          {% endif %}
        {% endfor %}
      </ul>
    </li>
  {% endif %}
  {% endwith %}

  {# Diagnosis & history #}
  {% with dx_list=summary|confirm:"prior_htn_diagnosis,htn_diagnosis_age" %}
  {% if dx_list %}
    <li><strong>Diagnosis & history</strong>
      <ul>
        {% for qa in dx_list %}
          {% if ":" in qa %}
            <li>{% autoescape off %}{{ qa|replace:": |: <strong>" }}{% endautoescape %}</strong></li>
          {% else %}
            <li>{{ qa|safe }}</li>
          {% endif %}
        {% endfor %}
      </ul>
    </li>
  {% endif %}
  {% endwith %}

  {# Current medication type #}
  {% with medtype=summary|confirm:"medication_type" %}
  {% if medtype %}
    <li><strong>Current medication type</strong>
      <ul>
        {% for qa in medtype %}
          {% if ":" in qa %}
            <li>{% autoescape off %}{{ qa|replace:": |: <strong>" }}{% endautoescape %}</strong></li>
          {% else %}
            <li>{{ qa|safe }}</li>
          {% endif %}
        {% endfor %}
      </ul>
    </li>
  {% endif %}
  {% endwith %}

  {# Combination pills details #}
  {% with combo_list=summary|confirm:"combo_bp_medications,combo_bp_other,lisinopril_hctz_strength,lisinopril_hctz_tablet_amount,lisinopril_hctz_doses_per_day,ramipril_hctz_strength,ramipril_hctz_tablet_amount,ramipril_hctz_doses_per_day,enalapril_hctz_strength,enalapril_hctz_tablet_amount,enalapril_hctz_doses_per_day,candesartan_hctz_strength,candesartan_hctz_tablet_amount,candesartan_hctz_doses_per_day,telmisartan_hctz_strength,telmisartan_hctz_tablet_amount,telmisartan_hctz_doses_per_day,valsartan_hctz_strength,valsartan_hctz_tablet_amount,valsartan_hctz_doses_per_day,losartan_hctz_strength,losartan_hctz_tablet_amount,losartan_hctz_doses_per_day,perindopril_indapamide_strength,perindopril_indapamide_tablet_amount,perindopril_indapamide_doses_per_day" %}
  {% if combo_list %}
    <li><strong>Combination pill(s)</strong>
      <ul>
        {% for qa in combo_list %}
          {% if ":" in qa %}
            <li>{% autoescape off %}{{ qa|replace:": |: <strong>" }}{% endautoescape %}</strong></li>
          {% else %}
            <li>{{ qa|safe }}</li>
          {% endif %}
        {% endfor %}
      </ul>
    </li>
  {% endif %}
  {% endwith %}

 {# ================= ACE inhibitors ================= #}
{% if data.ace_inhibitors and not data.ace_inhibitors.none_of_the_above %}
  {% with ace_list=summary|confirm:"ace_inhibitors,lisinopril_dosage,lisinopril_tablet_amount,lisinopril_doses_per_day,ramipril_dosage,ramipril_tablet_amount,ramipril_doses_per_day" %}
  {% if ace_list %}
    <li><strong>ACE inhibitor(s)</strong>
      <ul>
        {% for qa in ace_list %}
          {% if not "None of the above" in qa %}
            {% if ":" in qa %}
              <li>{% autoescape off %}{{ qa|replace:": |: <strong>" }}{% endautoescape %}</strong></li>
            {% else %}
              <li>{{ qa|safe }}</li>
            {% endif %}
          {% endif %}
        {% endfor %}
      </ul>
    </li>
  {% endif %}
  {% endwith %}
{% endif %}

{# ================= ARBs ================= #}
{% if data.arb and not data.arb.none_of_the_above %}
  {% with arb_list=summary|confirm:"arb,losartan_dosage,losartan_tablet_amount,losartan_doses_per_day,valsartan_dosage,valsartan_tablet_amount,valsartan_doses_per_day,irbesartan_dosage,irbesartan_tablet_amount,irbesartan_doses_per_day,candesartan_dosage,candesartan_tablet_amount,candesartan_doses_per_day,olmesartan_dosage,olmesartan_tablet_amount,olmesartan_doses_per_day,telmisartan_dosage,telmisartan_tablet_amount,telmisartan_doses_per_day,azilsartan_dosage,azilsartan_tablet_amount,azilsartan_doses_per_day" %}
  {% if arb_list %}
    <li><strong>ARB(s)</strong>
      <ul>
        {% for qa in arb_list %}
          {% if not "None of the above" in qa %}
            {% if ":" in qa %}
              <li>{% autoescape off %}{{ qa|replace:": |: <strong>" }}{% endautoescape %}</strong></li>
            {% else %}
              <li>{{ qa|safe }}</li>
            {% endif %}
          {% endif %}
        {% endfor %}
      </ul>
    </li>
  {% endif %}
  {% endwith %}
{% endif %}

{# ================= Calcium Channel Blockers ================= #}
{% if data.calcium_channel_blockers and not data.calcium_channel_blockers.none_of_the_above %}
  {% with ccb_list=summary|confirm:"calcium_channel_blockers,amlodipine_dosage,amlodipine_tablet_amount,amlodipine_doses_per_day,diltiazem_dosage,diltiazem_tablet_amount,diltiazem_doses_per_day,nifedipine_dosage,nifedipine_tablet_amount,nifedipine_doses_per_day,verapamil_dosage,verapamil_tablet_amount,verapamil_doses_per_day" %}
  {% if ccb_list %}
    <li><strong>Calcium channel blocker(s)</strong>
      <ul>
        {% for qa in ccb_list %}
          {% if not "None of the above" in qa %}
            {% if ":" in qa %}
              <li>{% autoescape off %}{{ qa|replace:": |: <strong>" }}{% endautoescape %}</strong></li>
            {% else %}
              <li>{{ qa|safe }}</li>
            {% endif %}
          {% endif %}
        {% endfor %}
      </ul>
    </li>
  {% endif %}
  {% endwith %}
{% endif %}

{# ================= Diuretics ================= #}
{% if data.diuretics and not data.diuretics.none_of_the_above %}
  {% with diur_list=summary|confirm:"diuretics,hydrochlorothiazide_dosage,hydrochlorothiazide_tablet_amount,hydrochlorothiazide_doses_per_day,chlorthalidone_dosage,chlorthalidone_tablet_amount,chlorthalidone_doses_per_day,indapamide_dosage,indapamide_tablet_amount,indapamide_doses_per_day,furosemide_dosage,furosemide_tablet_amount,furosemide_doses_per_day,spironolactone_dosage,spironolactone_tablet_amount,spironolactone_doses_per_day" %}
  {% if diur_list %}
    <li><strong>Diuretic(s)</strong>
      <ul>
        {% for qa in diur_list %}
          {% if not "None of the above" in qa %}
            {% if ":" in qa %}
              <li>{% autoescape off %}{{ qa|replace:": |: <strong>" }}{% endautoescape %}</strong></li>
            {% else %}
              <li>{{ qa|safe }}</li>
            {% endif %}
          {% endif %}
        {% endfor %}
      </ul>
    </li>
  {% endif %}
  {% endwith %}
{% endif %}

{# ================= Beta Blockers ================= #}
{% if data.beta_blockers and not data.beta_blockers.none_of_the_above %}
  {% with bb_list=summary|confirm:"beta_blockers,metoprolol_dosage,metoprolol_tablet_amount,metoprolol_doses_per_day,atenolol_dosage,atenolol_tablet_amount,atenolol_doses_per_day,bisoprolol_dosage,bisoprolol_tablet_amount,bisoprolol_doses_per_day,carvedilol_dosage,carvedilol_tablet_amount,carvedilol_doses_per_day,nebivolol_dosage,nebivolol_tablet_amount,nebivolol_doses_per_day,propranolol_dosage,propranolol_tablet_amount,propranolol_doses_per_day" %}
  {% if bb_list %}
    <li><strong>Beta blocker(s)</strong>
      <ul>
        {% for qa in bb_list %}
          {% if not "None of the above" in qa %}
            {% if ":" in qa %}
              <li>{% autoescape off %}{{ qa|replace:": |: <strong>" }}{% endautoescape %}</strong></li>
            {% else %}
              <li>{{ qa|safe }}</li>
            {% endif %}
          {% endif %}
        {% endfor %}
      </ul>
    </li>
  {% endif %}
  {% endwith %}
{% endif %}

  {# Current symptoms (cardiac & respiratory, incl. "not present") #}
  {% with sx_list=summary|confirm:"cardiovascular_symptoms,cardiac_symptoms_not_present,respiratory_symptoms,respiratory_symptoms_not_present" %}
  {% if sx_list %}
    <li><strong>Current symptoms</strong>
      <ul>
        {% for qa in sx_list %}
          {% if ":" in qa %}
            <li>{% autoescape off %}{{ qa|replace:": |: <strong>" }}{% endautoescape %}</strong></li>
          {% else %}
            <li>{{ qa|safe }}</li>
          {% endif %}
        {% endfor %}
      </ul>
    </li>
  {% endif %}
  {% endwith %}

  {# Lab testing overview (cleaner output) #}
{% with tests_list=summary|confirm:"prior_tests_completed" %}
{% if tests_list %}
  {% if data.prior_tests_completed.no_prior_tests %}
    <li><strong>Prior tests not completed</strong>
      <ul>
        <li>Kidney function (eGFR)</li>
        <li>Urine Albumin-to-Creatinine Ratio (ACR)</li>
        <li>Lipid Profile (Cholesterol levels)</li>
        <li>Diabetes Testing (HbA1c)</li>
        <li>Fasting Blood Glucose (FBG)</li>
        <li>CBC (Complete Blood Count)</li>
      </ul>
    </li>
  {% else %}
    <li><strong>Prior tests completed</strong>
      <ul>
        {% for qa in tests_list %}
          {% if not "I have not had these tests completed" in qa %}
            {% if ":" in qa %}
              <li>{% autoescape off %}{{ qa|replace:": |: <strong>" }}{% endautoescape %}</strong></li>
            {% else %}
              <li>{{ qa|safe }}</li>
            {% endif %}
          {% endif %}
        {% endfor %}
      </ul>
    </li>
  {% endif %}
{% endif %}
{% endwith %}

  {# Kidney function (eGFR) #}
  {% with egfr_list=summary|confirm:"last_kidney_function_test,prior_kidney_function_value" %}
  {% if egfr_list %}
    <li><strong>Kidney function (eGFR)</strong>
      <ul>
        {% for qa in egfr_list %}
          {% if ":" in qa %}
            <li>{% autoescape off %}{{ qa|replace:": |: <strong>" }}{% endautoescape %}</strong></li>
          {% else %}
            <li>{{ qa|safe }}</li>
          {% endif %}
        {% endfor %}
      </ul>
    </li>
  {% endif %}
  {% endwith %}

  {# Urine ACR #}
  {% with acr_list=summary|confirm:"last_urine_acr_test" %}
  {% if acr_list %}
    <li><strong>Urine ACR</strong>
      <ul>
        {% for qa in acr_list %}
          {% if ":" in qa %}
            <li>{% autoescape off %}{{ qa|replace:": |: <strong>" }}{% endautoescape %}</strong></li>
          {% else %}
            <li>{{ qa|safe }}</li>
          {% endif %}
        {% endfor %}
      </ul>
    </li>
  {% endif %}
  {% endwith %}

  {# Lipid profile #}
  {% with lipid_list=summary|confirm:"last_lipid_profile_test,lipid_profile_abnormalities" %}
  {% if lipid_list %}
    <li><strong>Lipid profile</strong>
      <ul>
        {% for qa in lipid_list %}
          {% if ":" in qa %}
            <li>{% autoescape off %}{{ qa|replace:": |: <strong>" }}{% endautoescape %}</strong></li>
          {% else %}
            <li>{{ qa|safe }}</li>
          {% endif %}
        {% endfor %}
      </ul>
    </li>
  {% endif %}
  {% endwith %}

  {# Diabetes tests #}
  {% with dm_list=summary|confirm:"last_a1c_test,recent_a1c_value,last_fasting_glucose_test,recent_fbg_value" %}
  {% if dm_list %}
    <li><strong>Diabetes testing</strong>
      <ul>
        {% for qa in dm_list %}
          {% if ":" in qa %}
            <li>{% autoescape off %}{{ qa|replace:": |: <strong>" }}{% endautoescape %}</strong></li>
          {% else %}
            <li>{{ qa|safe }}</li>
          {% endif %}
        {% endfor %}
      </ul>
    </li>
  {% endif %}
  {% endwith %}

  {# CBC #}
  {% with cbc_list=summary|confirm:"last_cbc_test,prior_cbc_value" %}
  {% if cbc_list %}
    <li><strong>CBC</strong>
      <ul>
        {% for qa in cbc_list %}
          {% if ":" in qa %}
            <li>{% autoescape off %}{{ qa|replace:": |: <strong>" }}{% endautoescape %}</strong></li>
          {% else %}
            <li>{{ qa|safe }}</li>
          {% endif %}
        {% endfor %}
      </ul>
    </li>
  {% endif %}
  {% endwith %}

  {# Imaging & cardiac tests (cleaner output) #}
{% with img_list=summary|confirm:"prior_imaging_tests" %}
{% if img_list %}
  {% if data.prior_imaging_tests.no_prior_imaging %}
    <li><strong>Imaging & cardiac tests not completed</strong>
      <ul>
        <li>Electrocardiogram (ECG/EKG)</li>
        <li>Stress Test (Exercise or Chemical)</li>
        <li>Echocardiogram (Heart Ultrasound)</li>
      </ul>
    </li>
  {% else %}
    <li><strong>Imaging & cardiac tests</strong>
      <ul>
        {% for qa in img_list %}
          {% if not "I have not had these tests completed" in qa %}
            {% if ":" in qa %}
              <li>{% autoescape off %}{{ qa|replace:": |: <strong>" }}{% endautoescape %}</strong></li>
            {% else %}
              <li>{{ qa|safe }}</li>
            {% endif %}
          {% endif %}
        {% endfor %}
      </ul>
    </li>
  {% endif %}
{% endif %}
{% endwith %}

  {# Home BP monitoring & latest reading #}
  {% with bp_list=summary|confirm:"bp_monitoring_frequency,bp_readings_per_sitting,systolic_bp,diastolic_bp,bp_reading_date,bp_reading_confirmed,bp_symptoms_check,bp_symptoms_not_present" %}
  {% if bp_list %}
    <li><strong>Blood pressure monitoring</strong>
      <ul>
        {% for qa in bp_list %}
          {% if ":" in qa %}
            <li>{% autoescape off %}{{ qa|replace:": |: <strong>" }}{% endautoescape %}</strong></li>
          {% else %}
            <li>{{ qa|safe }}</li>
          {% endif %}
        {% endfor %}
      </ul>
    </li>
  {% endif %}
  {% endwith %}

  {# In-office monitoring & provider #}
  {% with office_list=summary|confirm:"last_office_bp_check,clinic_vs_home_reading,last_physical_exam,has_regular_provider" %}
  {% if office_list %}
    <li><strong>In-office monitoring & provider</strong>
      <ul>
        {% for qa in office_list %}
          {% if ":" in qa %}
            <li>{% autoescape off %}{{ qa|replace:": |: <strong>" }}{% endautoescape %}</strong></li>
          {% else %}
            <li>{{ qa|safe }}</li>
          {% endif %}
        {% endfor %}
      </ul>
    </li>
  {% endif %}
  {% endwith %}
</ul>
{% endwith %}

<h3 style="text-align:center;"><strong>Education Summary</strong></h3>

<table style="width:100%; border-collapse:collapse;">
  <tbody>
    <tr>
      <td style="width:30%; vertical-align:top; font-weight:bold;">How to Monitor BP at Home</td>
      <td style="width:70%;">
        <ul style="margin:0; padding-left:20px;">
          <li>Sit quietly for 5 minutes before starting</li>
          <li>Take <strong>5 readings in a row</strong>, 1–2 minutes apart</li>
          <li>Use the <strong>average of the last 3 readings</strong></li>
          <li>If readings are high, check twice daily for 4 weeks & review with a doctor</li>
        </ul>
      </td>
    </tr>
    <tr>
      <td style="vertical-align:top; font-weight:bold;">Recommended Lab Monitoring</td>
      <td>
        <ul style="margin:0; padding-left:20px;">
          <li>Annual blood & urine tests (kidney, electrolytes)</li>
          <li>Urine test for kidney stress</li>
          <li>In-person BP check every 6 months</li>
          <li>Routine checkup for weight, heart, and overall health</li>
        </ul>
      </td>
    </tr>
    <tr>
      <td style="vertical-align:top; font-weight:bold;">Lifestyle Modifications</td>
      <td>
        <ul style="margin:0; padding-left:20px;">
          <li>Lose 5–10% body weight to lower BP</li>
          <li>Exercise regularly (30 min/day)</li>
          <li>Limit sodium to under 2,000 mg/day</li>
          <li>Limit alcohol to 1–2 drinks/day or less</li>
        </ul>
      </td>
    </tr>
    <tr>
      <td style="vertical-align:top; font-weight:bold;">Sleep Apnea Awareness</td>
      <td>
        <ul style="margin:0; padding-left:20px;">
          <li>Consider testing if snoring, daytime fatigue, or pauses in breathing</li>
          <li>Treating sleep apnea may reduce or eliminate BP meds</li>
        </ul>
      </td>
    </tr>
    <tr>
      <td style="vertical-align:top; font-weight:bold;">Common Side Effects</td>
      <td>
        <ul style="margin:0; padding-left:20px;">
          <li>Dizziness or lightheadedness</li>
          <li>Fatigue or tiredness</li>
          <li>Increased urination</li>
          <li>Dry cough</li>
          <li>Swelling in the legs</li>
          <li>Slow heart rate, cold hands or feet</li>
          <li>Changes in potassium or kidney function</li>
        </ul>
        <p style="margin:4px 0 0 0;"><em>If you develop concerning symptoms, contact a provider. Never stop your medication without medical advice.</em></p>
      </td>
    </tr>
  </tbody>
</table>


<p class="mt-3">
    <strong>Confirmation:</strong>
    By completing and submitting this intake, you confirm that the information provided is accurate to the best of your knowledge, <u>that you have read and understand all advice contained in the intake form</u>, and that you agree to seek emergency or in-person medical care if severe side-effects or new symptoms develop.
  </p>

<p>Best regards,<br>{{doctor.name}}</p>

{% endwith %}
{% endwith %}
{% endwith %}