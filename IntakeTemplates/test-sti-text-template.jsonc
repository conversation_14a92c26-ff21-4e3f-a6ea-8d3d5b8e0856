{"components": [{"key": "heading_testosterone_section", "html": "<h1><center><strong>Performance Enhancing Drugs - Monitoring Labwork</strong></h1><center><p>To expedite your care, please answer the following questions to the best of your abilities. &nbsp;We ask these questions to provide the best possible care.&nbsp;</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "heading_ped_history", "html": "<h4>PED History&nbsp;</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "ped_drug", "type": "selectboxes", "input": true, "label": "Please select which of the following category of PEDs are you are currently on:", "values": [{"label": "Oral or Injectable Anabolic Androgenic Steroids (AAS)", "value": "aas", "shortcut": ""}, {"label": "Selective Estrogen Receptor Modulators (SERM)", "value": "serm", "shortcut": ""}, {"label": "Selective Androgen Receptor Modulators (SARM)", "value": "sarm", "shortcut": ""}, {"label": "Erythropoietin (EPO) or Derivatives", "value": "epo", "shortcut": ""}, {"label": "Aromatase Inhibitors (AI)", "value": "ai", "shortcut": ""}, {"label": "Human chorionic gonadotropin (hCG) or Derivatives", "value": "hcg", "shortcut": ""}, {"label": "Fat Burning Compounds (T3, Clenbuterol, and DNP)", "value": "fat_burning", "shortcut": ""}], "inputType": "checkbox", "tableView": true, "optionsLabelPosition": "right"}, {"key": "no_ped_drug", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = !!data.no_ped_drug || !!_.some(_.values(data.ped_drug));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "primary_reason_for_use", "type": "selectboxes", "input": true, "label": "What is the primary reason for your use of anabolic steroids or other performance-enhancing drugs? Please select all that apply.", "values": [{"label": "Prefer Not to Disclose", "value": "prefer_not_to_disclose", "shortcut": ""}, {"label": "Increase Muscle Mass", "value": "increase_muscle_mass", "shortcut": ""}, {"label": "Improve Athletic Performance", "value": "improve_athletic_performance", "shortcut": ""}, {"label": "<PERSON><PERSON><PERSON> Physical Appearance", "value": "enhance_physical_appearance", "shortcut": ""}, {"label": "Aid in recovery from injury or surgery", "value": "aid_in_recovery", "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "confirm_label": "Primary Reason for Use:", "optionsLabelPosition": "right"}, {"key": "other_primary_reason_for_use", "type": "textarea", "input": true, "label": "Please specify your reason for using PEDs:", "tableView": true, "autoExpand": false, "customConditional": "show = data.primary_reason_for_use && data.primary_reason_for_use.other;"}, {"key": "sought_professional_advice", "type": "radio", "input": true, "label": "Have you ever sought professional advice or guidance before starting or during your use of these substances (e.g., from a healthcare provider, coach, or experienced user)?", "inline": false, "values": [{"label": "Prefer Not to Disclose", "value": "prefer_not_to_disclose", "shortcut": ""}, {"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "obtain_ped", "type": "selectboxes", "input": true, "label": "How do you obtain the performance-enhancing drugs you use?", "values": [{"label": "Prefer Not To Disclose", "value": "prefer_not_to_disclose", "shortcut": ""}, {"label": "Prescription From a Healthcare Provider", "value": "prescription_from_healthcare_provider", "shortcut": ""}, {"label": "Purchased Online", "value": "purchased_online", "shortcut": ""}, {"label": "Purchased from a Gym or Fitness Center", "value": "purchased_from_gym_or_fitness_center", "shortcut": ""}, {"label": "Obtained from Friends or Acquaintances", "value": "obtained_from_friends_or_acquaintances", "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "optionsLabelPosition": "right"}, {"key": "heading_ped_therapy", "html": "<h3>Current PED Medication&nbsp;</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "heading_aas_hgh_hcg_sarm_serm", "html": "<h4>Anabolic Steroids, HGH, hCG, SARMS or SERMS&nbsp;</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "use_aa_hgh_hcg_sarm_serm", "type": "radio", "input": true, "label": "Are you currently or have recently been on any of the following: Anabolic Steroids, HGH, hCG, SARMS or SERMS?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "confirm_label": "Currently on AA, HGH, SARM or SERMS:", "optionsLabelPosition": "right"}, {"key": "select_aa_hgh_hcg_sarm_serm", "type": "selectboxes", "input": true, "label": "Please select if you are currently or have recently been on any of the following:", "values": [{"label": "Anabolic Steroids (e.g., testosterone, nandrolone, trenbolone)", "value": "anabolic_steroids", "shortcut": ""}, {"label": "Human Growth Hormone (HGH)", "value": "hgh", "shortcut": ""}, {"label": "HCG, FSH, GNRH, LH", "value": "hcg", "shortcut": ""}, {"label": "Selective Androgen Receptor Modulators (SARMs)", "value": "sarms", "shortcut": ""}, {"label": "Selective Estrogen Receptor Modulators (SERMS)", "value": "serms", "shortcut": ""}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "customConditional": "show = data.use_aa_hgh_hcg_sarm_serm;", "optionsLabelPosition": "right"}, {"key": "heading_anabolic_steroids", "html": "<h5>Anabolic Steroids</h5>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.use_aa_hgh_hcg_sarm_serm && data.select_aa_hgh_hcg_sarm_serm.anabolic_steroids;"}, {"key": "injectable_vs_oral_aas", "type": "selectboxes", "input": true, "label": "Are you currently on injectable, oral AAS or both?", "inline": false, "values": [{"label": "Injectable", "value": "injectable", "shortcut": ""}, {"label": "Oral", "value": "oral", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "confirm_label": "Injectable or oral AAS:", "customConditional": "show = data.use_aa_hgh_hcg_sarm_serm && data.select_aa_hgh_hcg_sarm_serm.anabolic_steroids;", "optionsLabelPosition": "right"}, {"key": "specific_inj_anabolic_steroids", "type": "selectboxes", "input": true, "label": "Which specific injectable anabolic steroids do you use? Please select from the options below:", "values": [{"label": "Anadrol (Oxymetholone)", "value": "anadrol"}, {"label": "<PERSON><PERSON> (Oxandrolone)", "value": "<PERSON><PERSON><PERSON>"}, {"label": "Dianabol (Methandrostenolone)", "value": "dianabol"}, {"label": "Equipoise (Boldenone)", "value": "equipoise"}, {"label": "<PERSON>on (Drostanolone)", "value": "masteron"}, {"label": "Nandrolone (Deca-Durabolin)", "value": "nandrolone"}, {"label": "Primobolan (Methenolone)", "value": "primobolan"}, {"label": "Testosterone", "value": "testosterone"}, {"label": "Trenbolone", "value": "trenbolone"}, {"label": "Winstrol (Stanozolol)", "value": "winstrol"}, {"label": "Other", "value": "other", "shortcut": ""}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "confirm_label": "Current Injectable Anabolic Steroids:", "customConditional": "show = data.use_aa_hgh_hcg_sarm_serm && data.select_aa_hgh_hcg_sarm_serm.anabolic_steroids && data.injectable_vs_oral_aas.injectable;"}, {"key": "other_aas", "type": "textarea", "input": true, "label": "Please specify any other oral anabolic steroids:", "tableView": true, "autoExpand": false, "customConditional": "show = data.specific_inj_anabolic_steroids && data.specific_inj_anabolic_steroids.other;"}, {"key": "specific_oral_anabolic_steroids", "type": "selectboxes", "input": true, "label": "Which specific oral anabolic steroids do you use? Please select from the options below:", "values": [{"label": "Anadrol (Oxymetholone)", "value": "anadrol"}, {"label": "<PERSON><PERSON> (Oxandrolone)", "value": "<PERSON><PERSON><PERSON>"}, {"label": "Dianabol (Methandrostenolone)", "value": "dianabol"}, {"label": "Turinabol (Chlorodehydromethyltestosterone)", "value": "turinabol"}, {"label": "Winstrol (Stanozolol)", "value": "winstrol"}, {"label": "Halotestin (Fluoxymesterone)", "value": "halotestin"}, {"label": "Superdrol (Methasterone)", "value": "superdrol"}, {"label": "Proviron (Mesterolone)", "value": "proviron"}, {"label": "Methenolone Acetate (Primobolan)", "value": "methenolone_acetate"}, {"label": "Other", "value": "other", "shortcut": ""}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "confirm_label": "Current Oral Anabolic Steroids:", "customConditional": "show = data.use_aa_hgh_hcg_sarm_serm && data.select_aa_hgh_hcg_sarm_serm.anabolic_steroids && (data.injectable_vs_oral_aas.oral || data.injectable_vs_oral_aas.injectable_and_oral);"}, {"key": "other_aas", "type": "textarea", "input": true, "label": "Please specify any other oral anabolic steroids:", "tableView": true, "autoExpand": false, "customConditional": "show = data.specific_oral_anabolic_steroids && data.specific_oral_anabolic_steroids.other;"}, {"key": "duration_anabolic_therapy", "data": {"values": [{"label": "Prefer Not To Disclose", "value": "prefer_not_to_disclose"}, {"label": "<1 months", "value": "<1_months"}, {"label": "1-3 months", "value": "1-3_months"}, {"label": "3-6 months", "value": "3-6_months"}, {"label": "6-12 months", "value": "6-12_months"}, {"label": "1-3 years", "value": "1-3_years"}, {"label": "3+ years", "value": "3+_years"}]}, "type": "select", "input": true, "label": "Please specify how long you have been on anabolic steroids (total years of use, not current cycle length):", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.select_aa_hgh_hcg_sarm_serm && data.select_aa_hgh_hcg_sarm_serm.anabolic_steroids;", "optionsLabelPosition": "right"}, {"key": "heading_hgh_related_compounds", "html": "<h5>Human Growth Hormone (HGH) and Related Compounds</h5>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.select_aa_hgh_hcg_sarm_serm && data.select_aa_hgh_hcg_sarm_serm.hgh;"}, {"key": "specific_hgh_related_compounds", "type": "selectboxes", "input": true, "label": "Which specific HGH or related compounds do you use? Please select from the options below:", "values": [{"label": "Somatropin (HGH)", "value": "somatropin"}, {"label": "Insulin-like Growth Factor-1 (IGF-1)", "value": "igf_1"}, {"label": "Mechano Growth Factor (MGF)", "value": "mgf"}, {"label": "CJC-1295", "value": "cjc_1295"}, {"label": "Ipamorelin", "value": "ipamorelin"}, {"label": "<PERSON><PERSON><PERSON><PERSON> (MK-677)", "value": "ibutamoren"}, {"label": "GHRP-6", "value": "ghrp_6"}, {"label": "GHRP-2", "value": "ghrp_2"}, {"label": "Other", "value": "other", "shortcut": ""}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "confirm_label": "Current HGH Related Compounds:", "customConditional": "show = data.select_aa_hgh_hcg_sarm_serm && data.select_aa_hgh_hcg_sarm_serm.hgh;"}, {"key": "other_hgh_related_compounds", "type": "textarea", "input": true, "label": "Please specify the HGH or related compounds you have used:", "tableView": true, "autoExpand": false, "customConditional": "show = data.specific_hgh_related_compounds && data.specific_hgh_related_compounds.other;"}, {"key": "duration_hgh_related_compounds", "data": {"values": [{"label": "Prefer Not To Disclose", "value": "prefer_not_to_disclose"}, {"label": "<1 months", "value": "<1_months"}, {"label": "1-3 months", "value": "1-3_months"}, {"label": "3-6 months", "value": "3-6_months"}, {"label": "6-12 months", "value": "6-12_months"}, {"label": "1-3 years", "value": "1-3_years"}, {"label": "3+ years", "value": "3+_years"}]}, "type": "select", "input": true, "label": "Please specify how long you have been on HGH and derivatives (total years of use, not current cycle length)", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.select_aa_hgh_hcg_sarm_serm && data.select_aa_hgh_hcg_sarm_serm.hgh;", "optionsLabelPosition": "right"}, {"key": "heading_hcg_similar_compounds", "html": "<h5>Human Chorionic Gonadotropin (HCG) and Similar Compounds</h5>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.select_aa_hgh_hcg_sarm_serm && data.select_aa_hgh_hcg_sarm_serm.hcg;"}, {"key": "specific_hcg_similar_compounds", "type": "selectboxes", "input": true, "label": "Which specific HCG or similar compounds do you use? Please select from the options below:", "values": [{"label": "Human Chorionic Gonadotropin (HCG)", "value": "hcg"}, {"label": "Luteinizing Hormone (LH)", "value": "lh"}, {"label": "Follicle Stimulating Hormone (FSH)", "value": "fsh"}, {"label": "Gonadotropin Releasing Hormone (GnRH)", "value": "gnrh"}, {"label": "Other", "value": "other", "shortcut": ""}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "confirm_label": "Current HCG Related Compounds:", "customConditional": "show = data.select_aa_hgh_hcg_sarm_serm && data.select_aa_hgh_hcg_sarm_serm.hcg;"}, {"key": "other_hcg_similar_compounds", "type": "textarea", "input": true, "label": "Please specify the HCG or similar compounds you have used:", "tableView": true, "autoExpand": false, "customConditional": "show = data.specific_hcg_similar_compounds && data.specific_hcg_similar_compounds.other;"}, {"key": "duration_hcg_similar_compounds", "data": {"values": [{"label": "Prefer Not To Disclose", "value": "prefer_not_to_disclose"}, {"label": "<1 months", "value": "<1_months"}, {"label": "1-3 months", "value": "1-3_months"}, {"label": "3-6 months", "value": "3-6_months"}, {"label": "6-12 months", "value": "6-12_months"}, {"label": "1-3 years", "value": "1-3_years"}, {"label": "3+ years", "value": "3+_years"}]}, "type": "select", "input": true, "label": "Please specify how long you have been on hCG and derivatives (total years of use, not current cycle length)", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.select_aa_hgh_hcg_sarm_serm && data.select_aa_hgh_hcg_sarm_serm.hcg;", "optionsLabelPosition": "right"}, {"key": "heading_sarms", "html": "<h5>Selective Androgen Receptor Modulators (SARMs)</h5>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.use_aa_hgh_hcg_sarm_serm && data.select_aa_hgh_hcg_sarm_serm.sarms;"}, {"key": "specific_sarms", "type": "selectboxes", "input": true, "label": "Which specific SARMs do you use? Please select from the options below:", "values": [{"label": "Ligandrol (LGD-4033)", "value": "ligandrol"}, {"label": "<PERSON><PERSON><PERSON> (MK-2866)", "value": "ostarine"}, {"label": "<PERSON><PERSON><PERSON> (S4)", "value": "and<PERSON><PERSON>"}, {"label": "Testolone (RAD-140)", "value": "testolone"}, {"label": "YK-11", "value": "yk11"}, {"label": "S-23", "value": "s23"}, {"label": "ACP-105", "value": "acp105"}, {"label": "Other", "value": "other", "shortcut": ""}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "confirm_label": "Current SARM Related Compounds:", "customConditional": "show = data.use_aa_hgh_hcg_sarm_serm && data.select_aa_hgh_hcg_sarm_serm.sarms;"}, {"key": "duration_sarms_therapy", "data": {"values": [{"label": "Prefer Not To Disclose", "value": "prefer_not_to_disclose"}, {"label": "<1 months", "value": "<1_months"}, {"label": "1-3 months", "value": "1-3_months"}, {"label": "3-6 months", "value": "3-6_months"}, {"label": "6-12 months", "value": "6-12_months"}, {"label": "1-3 years", "value": "1-3_years"}, {"label": "3+ years", "value": "3+_years"}]}, "type": "select", "input": true, "label": "Please specify how long you have been on SARMS (total years of use, not current cycle length)", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.select_aa_hgh_hcg_sarm_serm && data.select_aa_hgh_hcg_sarm_serm.sarms;", "optionsLabelPosition": "right"}, {"key": "other_sarms", "type": "textarea", "input": true, "label": "Please specify your current or recent SARMs:", "tableView": true, "autoExpand": false, "customConditional": "show = data.specific_sarms && data.specific_sarms.other;"}, {"key": "heading_serms", "html": "<h5>Selective Estrogen Receptor Modulators (SERMs)</h5>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.use_aa_hgh_hcg_sarm_serm && data.select_aa_hgh_hcg_sarm_serm.serms;"}, {"key": "specific_serms", "type": "selectboxes", "input": true, "label": "Which specific SERMs do you use? Please select from the options below:", "values": [{"label": "Clomiphene (Clomid)", "value": "clomiphene"}, {"label": "Tamoxifen (Nolvadex)", "value": "tamoxifen"}, {"label": "<PERSON><PERSON><PERSON><PERSON> (Fareston)", "value": "toremifene"}, {"label": "Raloxifene (Evista)", "value": "raloxifene"}, {"label": "Other", "value": "other", "shortcut": ""}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "confirm_label": "Current SERMS:", "customConditional": "show = data.use_aa_hgh_hcg_sarm_serm && data.select_aa_hgh_hcg_sarm_serm.serms;"}, {"key": "other_serms", "type": "textarea", "input": true, "label": "Please specify your current or recent SERMs:", "tableView": true, "autoExpand": false, "customConditional": "show = data.specific_serms && data.specific_serms.other;"}, {"key": "duration_serms_therapy", "data": {"values": [{"label": "Prefer Not To Disclose", "value": "prefer_not_to_disclose"}, {"label": "<1 months", "value": "<1_months"}, {"label": "1-3 months", "value": "1-3_months"}, {"label": "3-6 months", "value": "3-6_months"}, {"label": "6-12 months", "value": "6-12_months"}, {"label": "1-3 years", "value": "1-3_years"}, {"label": "3+ years", "value": "3+_years"}]}, "type": "select", "input": true, "label": "Please specify how long you have been on SERMS (total years of use, not current cycle length):", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.use_aa_hgh_hcg_sarm_serm && data.select_aa_hgh_hcg_sarm_serm.serms;", "optionsLabelPosition": "right"}, {"key": "heading_fat_burning_ai_epo", "html": "<h4>Fat Burning Compounds, Beta-2 Agonists, EPO, Aromatase Inhibitors&nbsp;</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "use_fat_b2_epo_ai_oil", "type": "radio", "input": true, "label": "Are you currently or have recently taken any fat burning compounds (T3, DNP), Beta-2 Agonists (clenbuterol), EPO, Aromatase Inhibitors (AI) or do you use site enhancement oils?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "confirm_label": "Currently on Fat Burning Compounds, Beta-2 Agonists, EPO, Aromatase Inhibitors or Site enhancement oils:", "optionsLabelPosition": "right"}, {"key": "current_fat_b2_epo_ai_oil", "type": "selectboxes", "input": true, "label": "Please select which compounds you have been on:", "values": [{"label": "Fat Burning Compounds (T3 and DNP)", "value": "fat_burning_compounds", "shortcut": ""}, {"label": "Beta-2 Agonists (Clenbuterol, etc)", "value": "beta_2_agonists", "shortcut": ""}, {"label": "Erythropoietin (EPO) or Equivalents", "value": "epo", "shortcut": ""}, {"label": "Aromatase Inhibitors (Anastrozole, Letrozole, etc)", "value": "aromatase_inhibitors", "shortcut": ""}, {"label": "Site Enhancement Oil", "value": "site_enhancement_oil", "shortcut": ""}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "customConditional": "show = data.use_fat_b2_epo_ai_oil && data.use_fat_b2_epo_ai_oil == true;", "optionsLabelPosition": "right"}, {"key": "heading_fat_burning_compounds", "html": "<h5>Fat Burning Compounds</h5>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.current_fat_b2_epo_ai_oil && data.current_fat_b2_epo_ai_oil.fat_burning_compounds;"}, {"key": "specific_fat_burning_compounds", "type": "selectboxes", "input": true, "label": "Which specific fat burning compounds do you use? Please select from the options below:", "values": [{"label": "T3 (Liothyronine)", "value": "t3"}, {"label": "Clenbuterol", "value": "clenbuterol"}, {"label": "DNP (2,4-Dinitrophenol)", "value": "dnp"}, {"label": "Ephedrine", "value": "ephedrine"}, {"label": "Other", "value": "other", "shortcut": ""}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "confirm_label": "Current Fat Burning Compounds:", "customConditional": "show = data.current_fat_b2_epo_ai_oil && data.current_fat_b2_epo_ai_oil.fat_burning_compounds;"}, {"key": "other_fat_burning_compounds", "type": "textarea", "input": true, "label": "Please specify the fat burning compounds you have used:", "tableView": true, "autoExpand": false, "customConditional": "show = data.specific_fat_burning_compounds && data.specific_fat_burning_compounds.other;"}, {"key": "duration_fat_burning_compounds_therapy", "data": {"values": [{"label": "Prefer Not To Disclose", "value": "prefer_not_to_disclose"}, {"label": "<1 months", "value": "<1_months"}, {"label": "1-3 months", "value": "1-3_months"}, {"label": "3-6 months", "value": "3-6_months"}, {"label": "6-12 months", "value": "6-12_months"}, {"label": "1-3 years", "value": "1-3_years"}, {"label": "3+ years", "value": "3+_years"}]}, "type": "select", "input": true, "label": "How long have you been using fat burning compounds (total duration, not current cycle length)?", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.current_fat_b2_epo_ai_oil && data.current_fat_b2_epo_ai_oil.fat_burning_compounds;", "optionsLabelPosition": "right"}, {"key": "heading_beta2_agonists", "html": "<h5>Beta-2 Agonists</h5>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.current_fat_b2_epo_ai_oil && data.current_fat_b2_epo_ai_oil.beta_2_agonists;"}, {"key": "specific_beta_2_agonists", "type": "selectboxes", "input": true, "label": "Which specific Beta-2 Agonists do you use? Please select from the options below:", "values": [{"label": "Clenbuterol", "value": "clenbuterol"}, {"label": "Albuterol", "value": "albuterol"}, {"label": "Salbutamol", "value": "salbutamol"}, {"label": "Other", "value": "other", "shortcut": ""}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "confirm_label": "Current Beta-2 Agonists:", "customConditional": "show = data.current_fat_b2_epo_ai_oil && data.current_fat_b2_epo_ai_oil.beta_2_agonists;"}, {"key": "other_beta2_agonists", "type": "textarea", "input": true, "label": "Please specify the Beta-2 Agonists you have used:", "tableView": true, "autoExpand": false, "customConditional": "show = data.specific_beta_2_agonists && data.specific_beta_2_agonists.other;"}, {"key": "duration_beta2_agonists_therapy", "data": {"values": [{"label": "Prefer Not To Disclose", "value": "prefer_not_to_disclose"}, {"label": "<1 months", "value": "<1_months"}, {"label": "1-3 months", "value": "1-3_months"}, {"label": "3-6 months", "value": "3-6_months"}, {"label": "6-12 months", "value": "6-12_months"}, {"label": "1-3 years", "value": "1-3_years"}, {"label": "3+ years", "value": "3+_years"}]}, "type": "select", "input": true, "label": "Please specify how long you have been on Beta-2 Agonists (total years of use, not current cycle length):", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.current_fat_b2_epo_ai_oil && data.current_fat_b2_epo_ai_oil.beta_2_agonists;", "optionsLabelPosition": "right"}, {"key": "heading_epo_equivalent", "html": "<h5>EPO and Equivalent</h5>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.current_fat_b2_epo_ai_oil  && data.current_fat_b2_epo_ai_oil.epo;"}, {"key": "specific_epo_equivalent", "type": "selectboxes", "input": true, "label": "Which specific EPO or equivalent compounds do you use? Please select from the options below:", "values": [{"label": "Erythropoietin (EPO)", "value": "erythropoietin"}, {"label": "Dar<PERSON><PERSON><PERSON><PERSON> (Aranesp)", "value": "da<PERSON><PERSON><PERSON><PERSON>"}, {"label": "Methoxy polyethylene glycol-epoetin beta (Mircera)", "value": "mircera"}, {"label": "EPO-mimetics", "value": "epo_mimetics"}, {"label": "Other", "value": "other", "shortcut": ""}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "confirm_label": "Current EPO Equivalents:", "customConditional": "show = data.current_fat_b2_epo_ai_oil  && data.current_fat_b2_epo_ai_oil.epo;"}, {"key": "other_epo_equivalent", "type": "textarea", "input": true, "label": "Please specify the EPO or equivalent compounds you have used:", "tableView": true, "autoExpand": false, "customConditional": "show = data.specific_epo_equivalent && data.specific_epo_equivalent.other;"}, {"key": "duration_epo_equivalent_therapy", "data": {"values": [{"label": "Prefer Not To Disclose", "value": "prefer_not_to_disclose"}, {"label": "<1 months", "value": "<1_months"}, {"label": "1-3 months", "value": "1-3_months"}, {"label": "3-6 months", "value": "3-6_months"}, {"label": "6-12 months", "value": "6-12_months"}, {"label": "1-3 years", "value": "1-3_years"}, {"label": "3+ years", "value": "3+_years"}]}, "type": "select", "input": true, "label": "Please specify how long you have been on EPO and derivatives (total years of use, not current cycle length)", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.current_fat_b2_epo_ai_oil  && data.current_fat_b2_epo_ai_oil.epo;", "optionsLabelPosition": "right"}, {"key": "heading_aromatase_inhibitors", "html": "<h5>Aromatase Inhibitors</h5>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.current_fat_b2_epo_ai_oil && data.current_fat_b2_epo_ai_oil.aromatase_inhibitors;"}, {"key": "specific_aromatase_inhibitors", "type": "selectboxes", "input": true, "label": "Which specific aromatase inhibitors do you use? Please select from the options below:", "values": [{"label": "Anastrozole (Arimidex)", "value": "anastrozole"}, {"label": "Letrozole (Femara)", "value": "letrozole"}, {"label": "Exemestane (Aromasin)", "value": "exemestane"}, {"label": "Other", "value": "other", "shortcut": ""}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "confirm_label": "Current Aromatase Inhibitors:", "customConditional": "show = data.current_fat_b2_epo_ai_oil && data.current_fat_b2_epo_ai_oil.aromatase_inhibitors;"}, {"key": "other_aromatase_inhibitors", "type": "textarea", "input": true, "label": "Please specify the aromatase inhibitors you have used:", "tableView": true, "autoExpand": false, "customConditional": "show = data.specific_aromatase_inhibitors && data.specific_aromatase_inhibitors.other;"}, {"key": "duration_aromatase_inhibitors", "data": {"values": [{"label": "Prefer Not To Disclose", "value": "prefer_not_to_disclose"}, {"label": "<1 months", "value": "<1_months"}, {"label": "1-3 months", "value": "1-3_months"}, {"label": "3-6 months", "value": "3-6_months"}, {"label": "6-12 months", "value": "6-12_months"}, {"label": "1-3 years", "value": "1-3_years"}, {"label": "3+ years", "value": "3+_years"}]}, "type": "select", "input": true, "label": "Please specify how long you have been on Aromatase Inhibitors (total years of use, not current cycle length):", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.current_fat_b2_epo_ai_oil && data.current_fat_b2_epo_ai_oil.aromatase_inhibitors;", "optionsLabelPosition": "right"}, {"key": "heading_symptoms", "html": "<h2>Current Symptoms and Medication Side Effects</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "cardiovascular_symptoms", "type": "selectboxes", "input": true, "label": "Do you have any of the following cardiovascular symptoms?", "values": [{"label": "Chest pain, tightness or discomfort", "value": "chest_pain", "shortcut": ""}, {"label": "Palpitations", "value": "palpitations", "shortcut": ""}, {"label": "Swelling in the legs, ankles, or feet", "value": "swelling", "shortcut": ""}, {"label": "Dizziness or fainting", "value": "dizziness", "shortcut": ""}], "adminFlag": true, "tableView": true, "optionsLabelPosition": "right"}, {"key": "none_of_the_above_cardiovascular_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = !!data.none_of_the_above_cardiovascular_symptoms || _.some(_.values(data.cardiovascular_symptoms));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "cardiac_symptoms_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following cardiac symptoms:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "I DO NOT have the following heart related symptoms:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.cardiovascular_symptoms, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "respiratory_symptoms", "type": "selectboxes", "input": true, "label": "Do you have any of the following respiratory symptoms?", "values": [{"label": "<PERSON><PERSON>", "value": "cough", "shortcut": ""}, {"label": "Shortness of breath", "value": "shortness_of_breath", "shortcut": ""}, {"label": "Wheezing", "value": "wheezing", "shortcut": ""}, {"label": "Chest tightness", "value": "chest_tightness", "shortcut": ""}], "adminFlag": true, "tableView": true, "optionsLabelPosition": "right"}, {"key": "none_of_the_above_respiratory_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = !!data.none_of_the_above_respiratory_symptoms || _.some(_.values(data.respiratory_symptoms));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "respiratory_symptoms_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following respiratory symptoms:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "I DO NOT have the following breathing related symptoms:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.respiratory_symptoms, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "abdominal_gastrointestinal_symptoms", "type": "selectboxes", "input": true, "label": "Do you have any of the following abdominal or gastrointestinal symptoms?", "values": [{"label": "Abdominal pain", "value": "abdominal_pain", "shortcut": ""}, {"label": "<PERSON><PERSON><PERSON>", "value": "nausea", "shortcut": ""}, {"label": "Vomiting", "value": "vomiting", "shortcut": ""}, {"label": "Diarrhea", "value": "diarrhea", "shortcut": ""}, {"label": "Constipation", "value": "constipation", "shortcut": ""}, {"label": "Bloating or gas", "value": "bloating_gas", "shortcut": ""}, {"label": "Rectal bleeding", "value": "rectal_bleeding", "shortcut": ""}], "adminFlag": true, "tableView": true, "optionsLabelPosition": "right"}, {"key": "none_of_the_above_abdominal_gastrointestinal_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = !!data.none_of_the_above_abdominal_gastrointestinal_symptoms || _.some(_.values(data.abdominal_gastrointestinal_symptoms));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "gastrointestinal_symptoms_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following gastrointestinal symptoms:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "I DO NOT have the following gastrointestinal symptoms:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.abdominal_gastrointestinal_symptoms, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "liver_dysfunction_symptoms", "type": "selectboxes", "input": true, "label": "Do you have any of the following symptoms?", "values": [{"label": "Jaundice (yellowing of the skin or eyes)", "value": "jaundice", "shortcut": ""}, {"label": "Cola-colored urine", "value": "dark_urine", "shortcut": ""}, {"label": "Pale or clay-colored stools", "value": "pale_stools", "shortcut": ""}], "adminFlag": true, "tableView": true, "optionsLabelPosition": "right"}, {"key": "none_of_the_above_liver_dysfunction_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = !!data.none_of_the_above_liver_dysfunction_symptoms || _.some(_.values(data.liver_dysfunction_symptoms));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "liver_symptoms_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following liver symptoms:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "I DO NOT have the following liver symptoms:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.liver_dysfunction_symptoms, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "mental_health_symptoms", "type": "selectboxes", "input": true, "label": "Have you experienced any of the following symptoms?", "values": [{"label": "Low mood for the last 4 weeks", "value": "low_mood", "shortcut": ""}, {"label": "Increased anger or aggression", "value": "anger", "shortcut": ""}, {"label": "<PERSON>lt worried for the last 4 weeks", "value": "anxiety", "shortcut": ""}, {"label": "See things, hear voices, or feel things on my skin that others do not", "value": "psychosis", "shortcut": ""}], "adminFlag": true, "tableView": true, "optionsLabelPosition": "right"}, {"key": "none_of_the_above_mental_health_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "You must select at least one symptom or 'None of the above'."}, "validate": {"custom": "valid = !!data.none_of_the_above_mental_health_symptoms || _.some(_.values(data.mental_health_symptoms));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "mental_health_symptoms_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following mental health symptoms:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "I DO NOT have the following mental health symptoms:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.mental_health_symptoms, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "heading_safety", "html": "<h5>Safe Injection</h5>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "shared_needles_non_sterile_equipment", "type": "radio", "input": true, "label": "Have you ever shared needles or used non-sterile injection equipment for administering these substances?", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "heading_substance", "html": "<h5>Substances</h5>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "total_drinks", "data": {"values": [{"label": "Prefer Not To Disclose", "value": "prefer_not_to_disclose"}, {"label": "I don't drink alcohol", "value": "doesn't_drink"}, {"label": "<1", "value": "<1"}, {"label": "1", "value": "1"}, {"label": "2", "value": "2"}, {"label": "3", "value": "3"}, {"label": "4", "value": "4"}, {"label": "4+", "value": "4+"}]}, "type": "select", "input": true, "label": "Please specify how many alcoholic drinks you consume on a daily basis (required to interpret your liver enzyme levels)", "widget": "html5", "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "heading_previous_testing", "html": "<h4>Previous Testing&nbsp;</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "last_test", "data": {"values": [{"label": "<30 days", "value": "<30_days"}, {"label": "1-3 months", "value": "1-3_months"}, {"label": "3-6 months", "value": "3-6_months"}, {"label": "6-12 months", "value": "6-12_months"}, {"label": "1-3 years", "value": "1-3_years"}, {"label": "3+ years", "value": "3+_years"}, {"label": "I don't know", "value": "doesn't_know"}, {"label": "I haven't never been tested", "value": "not_tested"}]}, "type": "select", "input": true, "label": "When was your last PED bloodwork outside of using TeleTest?", "widget": "html5", "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "previous_diagnoses", "type": "selectboxes", "input": true, "label": "Have you been diagnosed wih any of the following:", "values": [{"label": "Hepatic Adenoma", "value": "hepatic_adenoma", "shortcut": ""}, {"label": "Irregular Heart Rhythm", "value": "irregular_rhythm", "shortcut": ""}, {"label": "Liver Disease", "value": "liver_disease", "shortcut": ""}, {"label": "Chronic Kidney Disease", "value": "ckd", "shortcut": ""}], "inputType": "checkbox", "tableView": true, "confirm_label": "Previously diagnosed conditions:", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_previous_diagnoses", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a diagnosis."}, "validate": {"custom": "valid = !!data.none_of_the_above_previous_diagnoses || _.some(_.values(data.previous_diagnoses));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"intake_template_key": "hx-polycythemia"}, {"key": "last_t2_measurement", "type": "radio", "input": true, "label": "Was your last testosterone measurement <strong>high, normal or low</strong>?", "inline": false, "values": [{"label": "High", "value": "high", "shortcut": ""}, {"label": "Normal", "value": "normal", "shortcut": ""}, {"label": "Low", "value": "low", "shortcut": ""}, {"label": "I Don't Know", "value": "doesn't_know", "shortcut": ""}, {"label": "I Don't Understand This Question", "value": "doesnt_understand", "shortcut": ""}, {"label": "Not Applicable", "value": "not_applicable", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "imaging_studies_related_to_ped", "type": "radio", "input": true, "label": "Have you ever had any imaging studies (e.g., ultrasound, CT scan, MRI) performed related to your use of performance-enhancing drugs or associated health concerns?", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "current_creatine", "type": "radio", "input": true, "label": "Are you currently on creatine supplements?", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "monitor_vital_signs", "type": "radio", "input": true, "label": "Do you regularly monitor your blood pressure, heart rate, or other vital signs at home or during visits to a healthcare provider?", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "last_blood_pressure_reading", "type": "textfield", "input": true, "label": "Last Blood Pressure Reading", "validate": {"required": true}, "tableView": true, "placeholder": "Enter your last blood pressure reading (e.g., 120/80)"}, {"key": "content2", "html": "<h2>Questions for the Doctor</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "any_other_questions", "type": "radio", "input": true, "label": "Do you have any specific questions for the healthcare provider?", "inline": false, "values": [{"label": "Yes, I have additional questions I would like to discuss", "value": true, "shortcut": ""}, {"label": "No, I am interested primarily in lab testing", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "defaultValue": false, "optionsLabelPosition": "right"}, {"key": "stated_other_questions", "type": "textarea", "input": true, "label": "Please use this area to ask any questions that you might have for your health care provider:", "adminFlag": true, "tableView": true, "autoExpand": false, "customConditional": "show = data.any_other_questions === true;"}, {"key": "intake_denial_reasons", "type": "textfield", "input": true, "label": "Intake Denial Reasons:", "hidden": true, "disabled": true, "multiple": true, "adminFlag": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "value = _.concat((data.has_health_conditions===true?['has_health_conditions']:[]),(!data.sku_freq||!data.sku_freq[data.sku]?['new_user']:[]),(data.last_test==='<30_days'?['last_test.<30_days']:[]),(data.any_other_questions === true && !!data.stated_other_questions && !/^\\s*$/.test(data.stated_other_questions)?['stated_other_questions.not_blank']:[]),(_.keys(_.pickBy(data.selected_bullets)).length < 2 ? ['less_than_2_assays_selected'] : []),(_.map(_.filter(['previous_diagnoses',..._.map(['cardiovascular','respiratory','abdominal_gastrointestinal','liver_dysfunction','mental_health'],k=>k+'_symptoms')],k=>_.some(_.values(data[k]))),k=>k+'.some')),(_.map(_.filter(_.flatMap(['history_polycythemia'],k=>[true,'did_not_understand'].map(v=>[k,v])),kv=>data[kv[0]]===kv[1]),kv=>kv[0]+'.'+kv[1])))", "refreshOnChange": true}, {"key": "intake_kind", "type": "textfield", "input": true, "label": "Intake Kind", "hidden": true, "disabled": true, "tableView": true, "clearOnHide": false, "calculateValue": "value = !data.intake_denial_reasons?.length ? 'mail' : 'chat';"}]}