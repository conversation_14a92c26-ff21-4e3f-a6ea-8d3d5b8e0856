{"components": [{"key": "heading_prior_testing", "type": "content", "input": false, "label": "Content", "tableView": true, "refreshOnChange": false, "html": "<h3>Prior Test Results</h3><p>If you've ever completed an advanced lipoprotein test (NMR/LipoProfile), you can enter any values you remember below. <em>It's okay to leave items blank if you don't know them.</em></p>"}, {"key": "prior_lipids_ever", "type": "radio", "input": true, "label": "Have you completed cholesterol/lipid testing in the past?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "unsure"}], "tableView": true, "refreshOnChange": false, "validate": {"required": true}, "confirm_label": "Prior lipid testing:"}, {"key": "heading_prior_lipid_grid", "type": "content", "input": false, "label": "Content", "tableView": true, "refreshOnChange": false, "html": "<h3>Lipid Test Details</h3><p>Add each test you remember. If one was an NMR/LipoProfile, choose <strong>Yes</strong> under \"Was this an NMR test?\" and enter any NMR values you know (optional).</p>", "customConditional": "show = data.prior_lipids_ever === 'yes';"}, {"key": "lipid_results_grid", "type": "<PERSON><PERSON><PERSON>", "label": "Add each prior cholesterol/lipid test:", "addAnother": "+ Add Another Test", "customConditional": "show = data.prior_lipids_ever === 'yes';", "openWhenEmpty": true, "defaultOpen": true, "displayAsTable": false, "useRowTemplate": true, "tableView": false, "refreshOnChange": false, "removeRow": true, "customClass": "lipid-nohdr", "templates": {"header": "", "row": "<div class='card shadow-sm w-100'><div class='card-body py-2 px-3'><div class='d-flex justify-content-between align-items-start small'><div class='me-2'><div class='fw-bold mb-1'>{{ (row.lipid_month==='unknown' ? 'Month ?' : _.startCase(row.lipid_month)) }} {{ row.lipid_year || '' }}</div><div>LDL: {{ row.ldl_value || 0 }} {{ ({mmol_l:'mmol/L — CA', mg_dl:'mg/dL — US', unknown:'?' }[row.panel_units] || '') }} &nbsp; HDL: {{ row.hdl_value || 0 }} {{ ({mmol_l:'mmol/L — CA', mg_dl:'mg/dL — US', unknown:'?' }[row.panel_units] || '') }} &nbsp; TG: {{ row.tg_value || 0 }} {{ ({mmol_l:'mmol/L — CA', mg_dl:'mg/dL — US', unknown:'?' }[row.panel_units] || '') }}</div><div class='mt-1'>ApoB: {{ row.apob_value || 0 }} {{ ({g_l:'g/L — CA', mg_dl:'mg/dL — US', unknown:'?' }[row.apob_units] || '') }} &nbsp; Lp(a): {{ row.lpa_value || 0 }} {{ ({nmol_l:'nmol/L — CA', mg_dl:'mg/dL — US', unknown:'?' }[row.lpa_units] || '') }}</div><div class='mt-1' style='{{ row.nmr_done==='yes' ? '' : 'display:none;' }}'>NMR: LDL-P {{ row.nmr_ldl_p || 0 }} nmol/L &nbsp; Small LDL-P {{ row.nmr_small_ldl_p || 0 }} nmol/L &nbsp; LDL Size {{ row.nmr_ldl_size || 0 }} nm &nbsp; HDL-P (total) {{ row.nmr_hdl_p_total || 0 }} µmol/L &nbsp; LP-IR {{ row.nmr_lp_ir_score || 0 }}</div></div><div><button class='btn btn-sm btn-outline-danger removeRow'>Delete</button></div></div></div></div>"}, "rowTemplate": "<div class='card shadow-sm w-100'><div class='card-body py-2 px-3'><div class='d-flex justify-content-between align-items-start small'><div class='me-2'><div class='fw-bold mb-1'>{{ (row.lipid_month==='unknown' ? 'Month ?' : _.startCase(row.lipid_month)) }} {{ row.lipid_year || '' }}</div><div>LDL: {{ row.ldl_value || 0 }} {{ ({mmol_l:'mmol/L — CA', mg_dl:'mg/dL — US', unknown:'?' }[row.panel_units] || '') }} &nbsp; HDL: {{ row.hdl_value || 0 }} {{ ({mmol_l:'mmol/L — CA', mg_dl:'mg/dL — US', unknown:'?' }[row.panel_units] || '') }} &nbsp; TG: {{ row.tg_value || 0 }} {{ ({mmol_l:'mmol/L — CA', mg_dl:'mg/dL — US', unknown:'?' }[row.panel_units] || '') }}</div><div class='mt-1'>ApoB: {{ row.apob_value || 0 }} {{ ({g_l:'g/L — CA', mg_dl:'mg/dL — US', unknown:'?' }[row.apob_units] || '') }} &nbsp; Lp(a): {{ row.lpa_value || 0 }} {{ ({nmol_l:'nmol/L — CA', mg_dl:'mg/dL — US', unknown:'?' }[row.lpa_units] || '') }}</div><div class='mt-1' style='{{ row.nmr_done==='yes' ? '' : 'display:none;' }}'>NMR: LDL-P {{ row.nmr_ldl_p || 0 }} nmol/L &nbsp; Small LDL-P {{ row.nmr_small_ldl_p || 0 }} nmol/L &nbsp; LDL Size {{ row.nmr_ldl_size || 0 }} nm &nbsp; HDL-P (total) {{ row.nmr_hdl_p_total || 0 }} µmol/L &nbsp; LP-IR {{ row.nmr_lp_ir_score || 0 }}</div></div><div><button class='btn btn-sm btn-outline-danger removeRow'>Delete</button></div></div></div></div>", "components": [{"key": "lipid_month", "type": "select", "label": "Month", "widget": "html5", "tableView": true, "data": {"values": [{"label": "Unknown", "value": "unknown"}, {"label": "January", "value": "jan"}, {"label": "February", "value": "feb"}, {"label": "March", "value": "mar"}, {"label": "April", "value": "apr"}, {"label": "May", "value": "may"}, {"label": "June", "value": "jun"}, {"label": "July", "value": "jul"}, {"label": "August", "value": "aug"}, {"label": "September", "value": "sep"}, {"label": "October", "value": "oct"}, {"label": "November", "value": "nov"}, {"label": "December", "value": "dec"}]}, "validate": {"required": true}}, {"key": "lipid_year", "type": "number", "label": "Year (YYYY)", "placeholder": "e.g., 2024", "tableView": true, "validate": {"min": 1900, "max": 2100}}, {"type": "columns", "key": "hdl_row", "columns": [{"width": 6, "components": [{"key": "hdl_value", "type": "number", "label": "HDL", "placeholder": "0 if unknown", "tableView": true, "validate": {"min": 0, "step": 1}}]}, {"width": 6, "components": [{"key": "panel_units", "type": "select", "label": "Units (HDL/LDL/TG)", "widget": "html5", "tableView": true, "tooltip": "Canada uses mmol/L. US uses mg/dL.", "defaultValue": "", "calculateValue": "if (!value) { var c = (data.patient_country||data.country||data.residence_country||'').toUpperCase(); value = (c==='US' || c==='USA') ? 'mg_dl' : 'mmol_l'; }", "data": {"values": [{"label": "mmol/L — Canada", "value": "mmol_l"}, {"label": "mg/dL — United States", "value": "mg_dl"}, {"label": "I don't know", "value": "unknown"}]}}]}]}, {"type": "columns", "key": "ldl_row", "columns": [{"width": 6, "components": [{"key": "ldl_value", "type": "number", "label": "LDL", "placeholder": "0 if unknown", "tableView": true, "validate": {"min": 0, "step": 1}}]}, {"width": 6, "components": []}]}, {"type": "columns", "key": "tg_row", "columns": [{"width": 6, "components": [{"key": "tg_value", "type": "number", "label": "Triglycerides", "placeholder": "0 if unknown", "tableView": true, "validate": {"min": 0, "step": 1}}]}, {"width": 6, "components": []}]}, {"type": "columns", "key": "apob_row", "columns": [{"width": 6, "components": [{"key": "apob_value", "type": "number", "label": "ApoB", "placeholder": "0 if unknown", "tableView": true, "validate": {"min": 0, "step": 1}}]}, {"width": 6, "components": [{"key": "apob_units", "type": "select", "label": "ApoB units", "widget": "html5", "tableView": true, "tooltip": "Canada uses g/L. US uses mg/dL.", "defaultValue": "", "calculateValue": "if (!value) { var c = (data.patient_country||data.country||data.residence_country||'').toUpperCase(); value = (c==='US' || c==='USA') ? 'mg_dl' : 'g_l'; }", "data": {"values": [{"label": "g/L — Canada", "value": "g_l"}, {"label": "mg/dL — United States", "value": "mg_dl"}, {"label": "I don't know", "value": "unknown"}]}}]}]}, {"type": "columns", "key": "lpa_row", "columns": [{"width": 6, "components": [{"key": "lpa_value", "type": "number", "label": "Lp(a)", "placeholder": "0 if unknown", "tableView": true, "validate": {"min": 0, "step": 1}}]}, {"width": 6, "components": [{"key": "lpa_units", "type": "select", "label": "Lp(a) units", "widget": "html5", "tableView": true, "tooltip": "Canada commonly uses nmol/L. US often mg/dL; some labs report nmol/L.", "defaultValue": "", "calculateValue": "if (!value) { var c = (data.patient_country||data.country||data.residence_country||'').toUpperCase(); value = (c==='US' || c==='USA') ? 'mg_dl' : 'nmol_l'; }", "data": {"values": [{"label": "nmol/L — Canada", "value": "nmol_l"}, {"label": "mg/dL — United States (legacy)", "value": "mg_dl"}, {"label": "I don't know", "value": "unknown"}]}}]}]}, {"key": "nmr_heading", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "html": "<hr class='my-2'><strong>NMR / LipoProfile (optional)</strong> — only fill in if this test was done."}, {"key": "nmr_done", "type": "radio", "input": true, "label": "Was this an NMR (LipoProfile®) test?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "unsure"}], "tableView": true, "refreshOnChange": true}, {"type": "columns", "key": "nmr_row_1", "customConditional": "show = data.lipid_results_grid[rowIndex].nmr_done === 'yes';", "columns": [{"width": 6, "components": [{"key": "nmr_ldl_p", "type": "number", "label": "LDL-P (nmol/L)", "placeholder": "0 if unknown", "tableView": true, "validate": {"min": 0, "step": 1}}]}, {"width": 6, "components": [{"key": "nmr_small_ldl_p", "type": "number", "label": "Small LDL-P (nmol/L)", "placeholder": "0 if unknown", "tableView": true, "validate": {"min": 0, "step": 1}}]}]}, {"type": "columns", "key": "nmr_row_2", "customConditional": "show = data.lipid_results_grid[rowIndex].nmr_done === 'yes';", "columns": [{"width": 6, "components": [{"key": "nmr_ldl_size", "type": "number", "label": "LDL Size (nm)", "placeholder": "0 if unknown", "tableView": true, "validate": {"min": 0}}]}, {"width": 6, "components": [{"key": "nmr_hdl_p_total", "type": "number", "label": "HDL-P (total, µmol/L)", "placeholder": "0 if unknown", "tableView": true, "validate": {"min": 0}}]}]}, {"type": "columns", "key": "nmr_row_3", "customConditional": "show = data.lipid_results_grid[rowIndex].nmr_done === 'yes';", "columns": [{"width": 6, "components": [{"key": "nmr_ldl_c_nih", "type": "number", "label": "LDL-C (NIH calc, mg/dL)", "placeholder": "0 if unknown", "tableView": true, "validate": {"min": 0}}]}, {"width": 6, "components": [{"key": "nmr_lp_ir_score", "type": "number", "label": "LP-IR Score (0–100)", "placeholder": "0 if unknown", "tableView": true, "validate": {"min": 0, "max": 100, "step": 1}}]}]}]}, {"key": "lipid_results_summary", "type": "textarea", "input": true, "label": "Prior lipid tests (summary)", "autoExpand": true, "hidden": true, "disabled": true, "clearOnHide": false, "tableView": true, "adminFlag": true, "confirm_label": "Lipid Test History:", "calculateValue": "if (data.prior_lipids_ever !== 'yes') { value = ''; } else { var rows = data.lipid_results_grid || []; var mon = {jan:'Jan', feb:'Feb', mar:'Mar', apr:'Apr', may:'May', jun:'Jun', jul:'Jul', aug:'Aug', sep:'Sep', oct:'Oct', nov:'Nov', dec:'Dec', unknown:'?'}; var panelU = {mmol_l:'mmol/L', mg_dl:'mg/dL', unknown:'?'}; var apobU = {g_l:'g/L', mg_dl:'mg/dL', unknown:'?'}; var lpaU = {nmol_l:'nmol/L', mg_dl:'mg/dL', unknown:'?'}; value = rows.map(function(r){ var date = ((mon[r.lipid_month] || '') + (r.lipid_year ? (' ' + r.lipid_year) : '')).trim(); if (!date) { date = 'Date ?'; } var pu = panelU[r.panel_units] || ''; var apu = apobU[r.apob_units] || ''; var lpu = lpaU[r.lpa_units] || ''; var ldl = (r.ldl_value && r.ldl_value !== 0) ? (r.ldl_value + ' ' + pu) : 'Unknown'; var hdl = (r.hdl_value && r.hdl_value !== 0) ? (r.hdl_value + ' ' + pu) : 'Unknown'; var tg  = (r.tg_value  && r.tg_value  !== 0) ? (r.tg_value  + ' ' + pu) : 'Unknown'; var apb = (r.apob_value && r.apob_value !== 0) ? (r.apob_value + ' ' + apu) : 'Unknown'; var lpa = (r.lpa_value && r.lpa_value !== 0) ? (r.lpa_value + ' ' + lpu) : 'Unknown'; var nmr = ''; if (r.nmr_done === 'yes') { var parts = []; parts.push('LDL-P: ' + ((r.nmr_ldl_p && r.nmr_ldl_p !== 0) ? (r.nmr_ldl_p + ' nmol/L') : 'Unknown')); parts.push('Small LDL-P: ' + ((r.nmr_small_ldl_p && r.nmr_small_ldl_p !== 0) ? (r.nmr_small_ldl_p + ' nmol/L') : 'Unknown')); parts.push('LDL size: ' + ((r.nmr_ldl_size && r.nmr_ldl_size !== 0) ? (r.nmr_ldl_size + ' nm') : 'Unknown')); parts.push('HDL-P (total): ' + ((r.nmr_hdl_p_total && r.nmr_hdl_p_total !== 0) ? (r.nmr_hdl_p_total + ' µmol/L') : 'Unknown')); parts.push('LDL-C (NIH): ' + ((r.nmr_ldl_c_nih && r.nmr_ldl_c_nih !== 0) ? (r.nmr_ldl_c_nih + ' mg/dL') : 'Unknown')); parts.push('LP-IR: ' + ((r.nmr_lp_ir_score && r.nmr_lp_ir_score !== 0) ? r.nmr_lp_ir_score : 'Unknown')); nmr = ' | NMR → ' + parts.join(' | '); } return date + ' — LDL: ' + ldl + ' | HDL: ' + hdl + ' | TG: ' + tg + ' | ApoB: ' + apb + ' | Lp(a): ' + lpa + nmr; }).join('\\n'); }"}]}