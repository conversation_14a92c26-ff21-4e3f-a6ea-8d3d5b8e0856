{"components": [{"key": "heading_results_review", "type": "content", "input": false, "html": "<h1><strong>Results Review</strong></h1>"}, {"key": "confirm_chlamydia_result", "type": "radio", "input": true, "label": "Did you have a chance to review your test results?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}}, {"key": "chlamydia_positive_warning", "type": "content", "input": false, "html": "<div style='border-left: 4px solid #ffc107; background-color: #fff3cd; padding: 10px; margin-bottom: 10px;'>Your test result is <strong>positive for chlamydia</strong>.</br></br>Here's what you should know:<ul><li>Chlamydia is a very common sexually transmitted infection (STI).</li><li>It often has no symptoms.</li><li>It is <strong>curable</strong> with antibiotics.</li><li>Untreated chlamydia can lead to complications like pelvic inflammatory disease (PID) or infertility.</li><li>Treatment is important even if you feel well.</li></ul></div>", "customConditional": "show = data.confirm_chlamydia_result === 'yes' || data.confirm_chlamydia_result === 'no';"}, {"key": "confirm_chlamydia_result", "type": "radio", "input": true, "label": "Do you acknowledge that your test result is positive for chlamydia?", "values": [{"label": "Yes, I understand my result is positive", "value": "yes"}, {"label": "No, I did not understand that", "value": "no"}], "validate": {"required": true}}, {"key": "prior_chlamydia_testing", "type": "radio", "input": true, "label": "Have you ever tested positive for chlamydia before?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "I'm not sure", "value": "unsure"}], "validate": {"required": true}}, {"key": "prior_positive_recent", "type": "radio", "input": true, "label": "Was your previous positive test within the last 30 days?", "values": [{"label": "Yes, within 30 days", "value": "within_30"}, {"label": "No, it was more than 30 days ago", "value": "over_30"}, {"label": "I'm not sure", "value": "not_sure"}], "customConditional": "show = data.prior_chlamydia_testing === 'yes';", "validate": {"required": true}}, {"key": "heading_treatment", "type": "content", "input": false, "html": "</br><h2><strong>Treatment</strong></h2>"}, {"key": "prior_chlamydia_treatment", "type": "radio", "input": true, "label": "Have you already received treatment for this positive chlamydia result?", "values": [{"label": "Yes, I've been treated", "value": "treated"}, {"label": "No, I have not been treated", "value": "not_treated"}, {"label": "I'm not sure", "value": "unsure"}], "validate": {"required": true}}, {"key": "chlamydia_treatment_provider", "type": "radio", "input": true, "label": "Who provided the treatment?", "values": [{"label": "Public health unit", "value": "health_unit"}, {"label": "Walk-in clinic", "value": "walk_in_clinic"}, {"label": "Family doctor", "value": "family_doctor"}, {"label": "Nurse practitioner", "value": "nurse_practitioner"}, {"label": "Emergency department", "value": "er"}, {"label": "Other", "value": "other"}], "customConditional": "show = data.prior_chlamydia_treatment === 'treated';", "validate": {"required": true}}, {"key": "chlamydia_treatment_date", "type": "datetime", "input": true, "label": "When did you receive the treatment?", "format": "yyyy-MM-dd", "enableTime": false, "widget": {"type": "calendar", "displayInTimezone": "viewer", "locale": "en", "useLocaleSettings": false}, "customConditional": "show = data.prior_chlamydia_treatment === 'treated';", "validate": {"required": true}}, {"key": "confirm_treatment_details", "type": "select", "input": true, "label": "To confirm, which antibiotic were you treated with?", "data": {"values": [{"label": "Doxycycline 100 mg twice daily for 7 days", "value": "doxycycline"}, {"label": "Azithromycin 1g single dose", "value": "azithromycin"}, {"label": "Metronidazole (Flagyl)", "value": "metronidazole"}, {"label": "Nitrofurantoin (Macrobid)", "value": "macrobid"}, {"label": "Trimethoprim/Sulfamethoxazole (Septra or Bactrim)", "value": "septra"}, {"label": "Ciprofloxacin (Cipro)", "value": "ciprofloxacin"}, {"label": "Other antibiotic", "value": "other_antibiotic"}, {"label": "I'm not sure", "value": "unsure"}]}, "widget": "html5", "customConditional": "show = data.prior_chlamydia_treatment === 'treated';", "validate": {"required": true}}, {"key": "confirm_selected_doxycycline", "type": "radio", "input": true, "label": "Just to confirm, you were treated with Doxycycline 100 mg twice daily for 7 days — is that correct?", "values": [{"label": "Yes, that's correct", "value": "yes"}, {"label": "No, that's not correct", "value": "no"}], "customConditional": "show = data.confirm_treatment_details === 'doxycycline';", "validate": {"required": true}}, {"key": "confirm_selected_azithromycin", "type": "radio", "input": true, "label": "Just to confirm, you were treated with Azithromycin 1g single dose — is that correct?", "values": [{"label": "Yes, that's correct", "value": "yes"}, {"label": "No, that's not correct", "value": "no"}], "customConditional": "show = data.confirm_treatment_details === 'azithromycin';", "validate": {"required": true}}, {"key": "confirm_not_doxy_or_azithro_from_other", "type": "radio", "input": true, "label": "Just to confirm, you were not treated with either Doxycycline or Azithromycin — is that correct?", "values": [{"label": "Yes, I was not treated with either", "value": "yes"}, {"label": "No, I was actually treated with Doxycycline or Azithromycin", "value": "no"}], "customConditional": "show = ['metronidazole', 'macrobid', 'septra', 'ciprofloxacin', 'other_antibiotic'].includes(data.confirm_treatment_details);", "validate": {"required": true}}, {"key": "revise_treatment_selection_warning", "type": "content", "input": false, "html": "<div style='border-left: 4px solid #dc3545; background-color: #f8d7da; padding: 10px; margin-bottom: 10px;'><strong>Please review your previous answer:</strong><br>It looks like you indicated you were treated with a different antibiotic, but now you’ve selected that you were actually treated with Doxycycline or Azithromycin. Please go back and revise your treatment selection to reflect the correct antibiotic.</div>", "customConditional": "show = data.confirm_not_doxy_or_azithro_from_other === 'no';"}, {"key": "inappropriate_treatment_advice", "type": "content", "input": false, "html": "<div style='border-left: 4px solid #198754; background-color: #d1e7dd; padding: 10px; margin-bottom: 10px;'><strong>Recommendation:</strong> The only antibiotics that effectively treat chlamydia are <strong>Doxycycline</strong> or <strong>Azithromycin</strong>.<br><br>Based on your response, it sounds like you may not have received the correct treatment. We strongly recommend starting the appropriate medication to fully clear the infection and prevent complications.</div>", "customConditional": "show = data.confirm_not_doxy_or_azithro_from_other === 'yes';"}, {"key": "acknowledge_need_for_treatment", "type": "radio", "input": true, "label": "Do you understand and wish to proceed with treatment?", "values": [{"label": "I understand and would like to obtain treatment", "value": "understand_and_want_treatment"}, {"label": "I do not understand", "value": "do_not_understand"}], "customConditional": "show = data.confirm_not_doxy_or_azithro_from_other === 'yes';", "validate": {"required": true}}, {"key": "chlamydia_treatment_proposal", "type": "radio", "input": true, "label": "Would you like us to prescribe treatment for chlamydia?", "values": [{"label": "Yes, I need treatment", "value": "yes"}, {"label": "No, I already received treatment elsewhere", "value": "already_treated"}, {"label": "No, I will seek treatment elsewhere", "value": "decline"}], "customConditional": "show = data.prior_chlamydia_treatment === 'not_treated';", "validate": {"required": true}}, {"key": "heading_eligibility_medication", "type": "content", "input": false, "html": "</br><h4>Eligibility for Medication</h4>", "customConditional": "show = !(data.prior_chlamydia_treatment === 'treated' && (data.confirm_treatment_details === 'doxycycline' || data.confirm_treatment_details === 'azithromycin'));"}, {"key": "doxycycline_allergy_check", "type": "selectboxes", "input": true, "label": "Have you ever had an allergy to any of the following antibiotics?", "values": [{"label": "Doxycycline", "value": "doxycycline", "shortcut": ""}, {"label": "Minocycline", "value": "minocycline", "shortcut": ""}, {"label": "Tetracycline", "value": "tetracycline", "shortcut": ""}], "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = !(data.prior_chlamydia_treatment === 'treated' && (data.confirm_treatment_details === 'doxycycline' || data.confirm_treatment_details === 'azithromycin'));"}, {"key": "no_doxycycline_allergy", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "Please select at least one option or confirm 'None of the above'."}, "validate": {"custom": "valid = !!data.no_doxycycline_allergy || _.some(_.values(data.doxycycline_allergy_check));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = !(data.prior_chlamydia_treatment === 'treated' && (data.confirm_treatment_details === 'doxycycline' || data.confirm_treatment_details === 'azithromycin'));"}, {"key": "azithromycin_allergy_check", "type": "selectboxes", "input": true, "label": "Have you ever had an allergy to any of the following antibiotics?", "values": [{"label": "Azithromycin", "value": "azithromycin", "shortcut": ""}, {"label": "Erythromycin", "value": "erythromycin", "shortcut": ""}, {"label": "Clarithromycin", "value": "clarithromycin", "shortcut": ""}], "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = !(data.prior_chlamydia_treatment === 'treated' && (data.confirm_treatment_details === 'doxycycline' || data.confirm_treatment_details === 'azithromycin'));"}, {"key": "no_azithromycin_allergy", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "Please select at least one option or confirm 'None of the above'."}, "validate": {"custom": "valid = !!data.no_azithromycin_allergy || _.some(_.values(data.azithromycin_allergy_check));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = !(data.prior_chlamydia_treatment === 'treated' && (data.confirm_treatment_details === 'doxycycline' || data.confirm_treatment_details === 'azithromycin'));"}, {"key": "azithromycin_contraindications", "type": "selectboxes", "input": true, "label": "Have you ever been told you have any of the following conditions that would prevent you from safely taking azithromycin?", "values": [{"label": "History of QT prolongation or heart rhythm disorder", "value": "qt_prolongation", "shortcut": ""}, {"label": "Severe liver disease or liver failure", "value": "liver_disease", "shortcut": ""}, {"label": "Previous serious reaction to azithromycin", "value": "prior_reaction_macrolide", "shortcut": ""}], "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = !(data.prior_chlamydia_treatment === 'treated' && (data.confirm_treatment_details === 'doxycycline' || data.confirm_treatment_details === 'azithromycin'));"}, {"key": "no_azithromycin_contraindications", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "Please select at least one condition or confirm 'None of the above'."}, "validate": {"custom": "valid = !!data.no_azithromycin_contraindications || _.some(_.values(data.azithromycin_contraindications));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = !(data.prior_chlamydia_treatment === 'treated' && (data.confirm_treatment_details === 'doxycycline' || data.confirm_treatment_details === 'azithromycin'));"}, {"key": "doxycycline_contraindications", "type": "selectboxes", "input": true, "label": "Have you ever been told you have any of the following conditions that would prevent you from safely taking doxycycline?", "values": [{"label": "Currently pregnant", "value": "pregnancy", "shortcut": ""}, {"label": "Breastfeeding an infant", "value": "breastfeeding", "shortcut": ""}, {"label": "Prior serious reaction to doxycycline", "value": "prior_reaction_tetracycline", "shortcut": ""}], "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = !(data.prior_chlamydia_treatment === 'treated' && (data.confirm_treatment_details === 'doxycycline' || data.confirm_treatment_details === 'azithromycin'));"}, {"key": "no_doxycycline_contraindications", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "Please select at least one condition or confirm 'None of the above'."}, "validate": {"custom": "valid = !!data.no_doxycycline_contraindications || _.some(_.values(data.doxycycline_contraindications));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = !(data.prior_chlamydia_treatment === 'treated' && (data.confirm_treatment_details === 'doxycycline' || data.confirm_treatment_details === 'azithromycin'));"}, {"key": "heading_treatment_options", "type": "content", "input": false, "html": "</br><h4>Chlamydia Treatment Options</h4>", "customConditional": "show = !(data.prior_chlamydia_treatment === 'treated' && (data.confirm_treatment_details === 'doxycycline' || data.confirm_treatment_details === 'azithromycin'));"}, {"key": "chlamydia_treatment_explanation", "type": "content", "input": false, "html": "<div style='border-left: 4px solid #198754; background-color: #d1e7dd; padding: 10px; margin-bottom: 10px;'><strong>Treatment Options:</strong><br>Doxycycline and Azithromycin are both effective treatments for chlamydia.<ul><li><strong>Doxycycline</strong> has a higher cure rate for infections outside of the genitals (oral or anal areas).</li><li><strong>Azithromycin</strong> may be preferred if you're forgetful with pills or unable to take antibiotics for 7 days in a row.</li></ul></div>", "customConditional": "show = data.no_doxycycline_allergy && data.no_azithromycin_allergy && data.no_doxycycline_contraindications && data.no_azithromycin_contraindications && !(data.prior_chlamydia_treatment === 'treated' && (data.confirm_treatment_details === 'doxycycline' || data.confirm_treatment_details === 'azithromycin'));"}, {"key": "doxycycline_blocked_explanation", "type": "content", "input": false, "html": "<div style='border-left: 4px solid #198754; background-color: #d1e7dd; padding: 10px; margin-bottom: 10px;'><strong>Recommended Option:</strong><br>You have reported an allergy or medical reason you cannot take doxycycline. Based on this, <strong>Azithromycin 1g single dose</strong> is recommended.</div>", "customConditional": "show = (!data.no_doxycycline_allergy || !data.no_doxycycline_contraindications) && !(data.prior_chlamydia_treatment === 'treated' && (data.confirm_treatment_details === 'doxycycline' || data.confirm_treatment_details === 'azithromycin'));"}, {"key": "azithromycin_blocked_explanation", "type": "content", "input": false, "html": "<div style='border-left: 4px solid #198754; background-color: #d1e7dd; padding: 10px; margin-bottom: 10px;'><strong>Recommended Option:</strong><br>You have reported an allergy or medical reason you cannot take azithromycin. Based on this, <strong>Doxycycline 100 mg twice daily for 7 days</strong> is recommended.</div>", "customConditional": "show = (!data.no_azithromycin_allergy || !data.no_azithromycin_contraindications) && !(data.prior_chlamydia_treatment === 'treated' && (data.confirm_treatment_details === 'doxycycline' || data.confirm_treatment_details === 'azithromycin'));"}, {"key": "preferred_ct_both_eligible", "type": "radio", "input": true, "label": "Please select how you'd like to proceed with treatment:", "values": [{"label": "Doxycycline 100 mg twice daily for 7 days (preferred, unless contraindicated)", "value": "doxycycline-tablets-100mg-bid-7"}, {"label": "Azithromycin 1g single dose (alternative)", "value": "azithromycin-1000mg-od"}, {"label": "I need further guidance before deciding", "value": "need_more_guidance"}], "customConditional": "show = data.no_doxycycline_allergy && data.no_doxycycline_contraindications && data.no_azithromycin_allergy && data.no_azithromycin_contraindications && !(data.prior_chlamydia_treatment === 'treated' && (data.confirm_treatment_details === 'doxycycline' || data.confirm_treatment_details === 'azithromycin'));", "validate": {"required": true}}, {"key": "preferred_ct_doxy_only", "type": "radio", "input": true, "label": "Please select how you'd like to proceed with treatment:", "values": [{"label": "Doxycycline 100 mg twice daily for 7 days (preferred, unless contraindicated)", "value": "doxycycline-tablets-100mg-bid-7"}, {"label": "I need further guidance before deciding", "value": "need_more_guidance"}], "customConditional": "show = data.no_doxycycline_allergy && data.no_doxycycline_contraindications && (!data.no_azithromycin_allergy || !data.no_azithromycin_contraindications) && !(data.prior_chlamydia_treatment === 'treated' && (data.confirm_treatment_details === 'doxycycline' || data.confirm_treatment_details === 'azithromycin'));", "validate": {"required": true}}, {"key": "preferred_ct_azithro_only", "type": "radio", "input": true, "label": "Please select how you'd like to proceed with treatment:", "values": [{"label": "Azithromycin 1g single dose (alternative)", "value": "azithromycin-1000mg-od"}, {"label": "I need further guidance before deciding", "value": "need_more_guidance"}], "customConditional": "show = data.no_azithromycin_allergy && data.no_azithromycin_contraindications && (!data.no_doxycycline_allergy || !data.no_doxycycline_contraindications) && !(data.prior_chlamydia_treatment === 'treated' && (data.confirm_treatment_details === 'doxycycline' || data.confirm_treatment_details === 'azithromycin'));", "validate": {"required": true}}, {"key": "heading_symptoms", "type": "content", "input": false, "html": "</br><h2><strong>Symptoms</strong></h2>"}, {"key": "female_pelvic_symptoms", "type": "selectboxes", "input": true, "label": "Are you currently experiencing any of the following symptoms?", "values": [{"label": "Lower abdominal or pelvic pain", "value": "pelvic_pain", "shortcut": ""}, {"label": "Menstrual cramping that is worse than usual", "value": "cramping", "shortcut": ""}, {"label": "Pain during sex (deep or entry) that is new", "value": "dyspareunia", "shortcut": ""}, {"label": "Fever or chills", "value": "fever_chills", "shortcut": ""}], "customConditional": "show = data.sex === 'female';", "adminFlag": true, "tableView": true, "optionsLabelPosition": "right"}, {"key": "no_female_pelvic_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "Please select at least one symptom or confirm 'None of the above'."}, "validate": {"custom": "valid = !!data.no_female_pelvic_symptoms || _.some(_.values(data.female_pelvic_symptoms));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.sex === 'female';"}, {"key": "pid_exam_guidance", "type": "content", "input": false, "html": "<div style='border-left: 4px solid #0d6efd; background-color: #e7f1ff; padding: 10px; margin-bottom: 10px;'><strong>Important:</strong><br>Your symptoms may be a sign of <strong>Pelvic Inflammatory Disease (PID)</strong>, a serious infection that occurs as a result of chlamydia that can cause infertility, chronic pelvic pain, and hospitalization if not treated promptly.<br><br>A proper <strong>pelvic exam</strong> is needed to confirm or rule out PID. During this exam, a doctor checks for tenderness at the cervix. If the cervix is tender to touch, this suggests PID.<br><br><strong>Treatment for PID is different</strong> — it includes an injection followed by 14 days of oral antibiotics, which is more effective for upper reproductive tract infections.<br><br>We strongly recommend that you see a doctor in person for this exam. This can be done at:<ul><li>An urgent care centre</li><li>A walk-in clinic</li><li>An emergency department</li><li>Your family doctor</li></ul></div>", "customConditional": "show = _.some(_.values(data.female_pelvic_symptoms)) && !data.no_female_pelvic_symptoms;"}, {"key": "pid_exam_acknowledgement", "type": "radio", "input": true, "label": "Do you understand and plan to seek a pelvic exam based on the symptoms you've reported?", "values": [{"label": "I understand and will seek out an in-person exam", "value": "will_seek_exam"}, {"label": "I understand but I cannot access in-person care right now", "value": "cannot_access_exam"}], "customConditional": "show = _.some(_.values(data.female_pelvic_symptoms)) && !data.no_female_pelvic_symptoms;", "validate": {"required": true}}, {"key": "cannot_access_pid_warning", "type": "content", "input": false, "html": "<div style='border-left: 4px solid #ffc107; background-color: #fff3cd; padding: 10px; margin-bottom: 10px;'><strong>Warning:</strong><br>If you are unable to access care, please understand that <strong>Pelvic Inflammatory Disease (PID)</strong> is a serious and time-sensitive condition.<br><br><strong>Delaying treatment may increase your risk of:</strong><ul><li>Infertility (inability to get pregnant)</li><li>Chronic pelvic pain</li><li>Ectopic pregnancy (a pregnancy outside the uterus)</li><li>Hospitalization for more serious infection</li></ul><strong>Please do not wait for a routine family doctor appointment.</strong> We strongly recommend going to a walk-in clinic, urgent care centre, or emergency department as soon as possible (i.e. today) to get assessed and treated.</div>", "customConditional": "show = data.pid_exam_acknowledgement === 'cannot_access_exam';"}, {"key": "accept_risk_untreated_pid", "type": "radio", "input": true, "label": "How would you like to proceed given the possibility of PID and need for an in-person exam?", "values": [{"label": "Yes, I accept the risks of not getting assessed right now and still want to be treated for chlamydia", "value": "accept_risk_wants_ct_treatment"}, {"label": "I will go for an in-person exam but would like to receive treatment for chlamydia in the meantime", "value": "will_get_exam_wants_ct_treatment"}, {"label": "I will go for an in-person exam and decide about treatment afterward", "value": "will_get_exam_and_decide_later"}], "customConditional": "show = data.pid_exam_acknowledgement === 'cannot_access_exam' && data.prior_chlamydia_treatment !== 'treated';", "validate": {"required": true}}, {"key": "accept_risk_pid_treated_already", "type": "radio", "input": true, "label": "You’ve already received treatment for chlamydia. However, your symptoms could indicate PID, which requires in-person care. How would you like to proceed?", "values": [{"label": "I will go for an in-person exam to clarify if <PERSON><PERSON> is present", "value": "will_go_in_person_exam"}, {"label": "I require more counselling before deciding", "value": "need_more_counselling"}], "customConditional": "show = data.pid_exam_acknowledgement === 'cannot_access_exam' && data.prior_chlamydia_treatment === 'treated' && (data.confirm_treatment_details === 'doxycycline' || data.confirm_treatment_details === 'azithromycin');", "validate": {"required": true}}, {"key": "confirm_same_day_exam_urgency", "type": "radio", "input": true, "label": "We recommend a same-day in-person exam to assess for possible PID and avoid delays in care. Do you understand this recommendation?", "values": [{"label": "Yes, I understand the importance of same-day assessment", "value": "understands_same_day"}, {"label": "No, I do not understand and would like further explanation", "value": "does_not_understand"}], "customConditional": "show = data.accept_risk_untreated_pid === 'will_get_exam_and_decide_later' || data.accept_risk_pid_treated_already === 'will_go_in_person_exam';", "validate": {"required": true}}, {"key": "heading_pregnancy", "type": "content", "input": false, "html": "</br><h4>Pregnancy</h4>", "customConditional": "show = data.sex === 'female';"}, {"key": "currently_pregnant", "type": "radio", "input": true, "label": "Are you currently pregnant?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "I'm not sure", "value": "unsure"}], "validate": {"required": true}, "customConditional": "show = data.sex === 'female';"}, {"key": "pregnancy_notification_advice", "type": "content", "input": false, "html": "<div style='border-left: 4px solid #0d6efd; background-color: #e7f1ff; padding: 10px; margin-bottom: 10px;'>If you are pregnant, it's important to inform your obstetrician (OB-GYN), midwife, or family doctor. Chlamydia treatment during pregnancy may be different, and they should be aware of your diagnosis to ensure appropriate care and follow-up.</div>", "customConditional": "show = data.currently_pregnant === 'yes';"}, {"key": "pregnancy_notification_confirmation", "type": "radio", "input": true, "label": "Have you notified or do you plan to notify your OB-GYN or family doctor?", "values": [{"label": "Yes, I've already notified them", "value": "yes"}, {"label": "No, I haven't notified them but plan to", "value": "no"}, {"label": "No, I don't plan to notify them", "value": "no_plan"}], "customConditional": "show = data.currently_pregnant === 'yes';", "validate": {"required": true}}, {"key": "heading_counselling", "type": "content", "input": false, "html": "</br><h4>Counselling</h4>"}, {"key": "partner_notification_info", "type": "content", "input": false, "html": "<div style='border-left: 4px solid #0d6efd; background-color: #e7f1ff; padding: 10px; margin-bottom: 10px;'><strong>Partner Notification:</strong><ul><li>Public Health advises you to notify all sexual partners from the last 60 days.</li><li>If you have not had any partners in that time, then to notify your most recent partner.</li><li>If you don't feel comfortable, Public Health can notify partners anonymously with contact info from you.</li></ul></div>"}, {"key": "counselling_partner_notification", "type": "radio", "input": true, "label": "How would you like to handle notifying your partners?", "values": [{"label": "I understand and will notify my partners myself", "value": "self_notify"}, {"label": "I want Public Health to notify my partners anonymously", "value": "public_health_notify"}, {"label": "I'm not able to notify partners and I do not have their contact information", "value": "unable_notify"}], "validate": {"required": true}}, {"key": "abstinence_guidance", "type": "content", "input": false, "html": "<div style='border-left: 4px solid #0d6efd; background-color: #e7f1ff; padding: 10px; margin-bottom: 10px;'><strong>When to Resume Sexual Activity:</strong><ul><li>Do not have any sexual contact (oral, anal, or vaginal) until <strong>7 full days after starting treatment</strong>.</li><li>Your partners must also be treated and wait 7 days before any sexual contact resumes.</li></ul></div>"}, {"key": "counselling_abstinence", "type": "radio", "input": true, "label": "Will you follow the advice to abstain from sex for 7 days after starting treatment and until all partners are treated?", "values": [{"label": "I understand and will abstain for 7 days", "value": "understand_abstain"}, {"label": "I do not understand", "value": "do_not_understand"}], "validate": {"required": true}}, {"key": "treatment_timing_guidance", "type": "content", "input": false, "html": "<div style='border-left: 4px solid #0d6efd; background-color: #e7f1ff; padding: 10px; margin-bottom: 10px;'><strong>When is it safe to resume sexual activity?</strong><ul><li><strong>If you are taking doxycycline:</strong> wait until <strong>24 hours after your last dose</strong>. This means sex is safe on day 8 after starting treatment.</li><li><strong>If you are taking azithromycin (1g single dose):</strong> wait <strong7 full days</strong> after taking the dose before having any sexual contact.</li><li>Your sexual partners also need to be treated and wait the full duration before resuming contact.</li></ul></div>", "customConditional": "show = data.preferred_chlamydia_treatment === 'doxycycline-tablets-100mg-bid-7' || data.preferred_chlamydia_treatment === 'azithromycin-1000mg-od';"}, {"key": "counselling_timing_understanding", "type": "radio", "input": true, "label": "Will you follow the advice about when it’s safe to resume sexual activity based on your treatment?", "values": [{"label": "Yes, I understand and will wait the appropriate number of days", "value": "understand"}, {"label": "I do not understand", "value": "do_not_understand"}], "customConditional": "show = data.preferred_chlamydia_treatment === 'doxycycline-tablets-100mg-bid-7' || data.preferred_chlamydia_treatment === 'azithromycin-1000mg-od';", "validate": {"required": true}}, {"key": "test_of_cure_info", "type": "content", "input": false, "html": "<div style='border-left: 4px solid #0d6efd; background-color: #e7f1ff; padding: 10px; margin-bottom: 10px;'><strong>Test of Cure:</strong><ul><li>A follow-up test is recommended <strong>30 to 60 days</strong> after treatment to confirm the infection has cleared.</li><li><strong>If you took your medication as prescribed and it worked, you are no longer infectious after day 7</strong>.</li><li>The earliest we recommend retesting is <strong>21 days</strong> after your last pill. This is because our STI tests detect DNA fragments that may still be present — even when the infection is gone.</li><li>If you test too early (between day 7 and day 21), you could receive a <strong>false positive</strong> result due to residual DNA.</li><li>You can complete your follow-up test at a walk-in clinic, with your family doctor, through your local public health unit, or by requesting a new consultation with TeleTest when you’re due for retesting.</li></ul></div>"}, {"key": "counselling_test_of_cure", "type": "radio", "input": true, "label": "Will you follow the advice to complete a test of cure 30-60 days after treatment?", "values": [{"label": "Yes, I understand and will complete follow-up testing", "value": "understand_test_of_cure"}, {"label": "I do not understand", "value": "do_not_understand"}], "validate": {"required": true}}, {"key": "heading_medical_history", "type": "content", "input": false, "html": "<h3><strong>Medical History</strong></h3><p>Please complete this section accurately to ensure there are no potential medication conflicts or treatment risks. This helps us identify any conditions, allergies, or surgeries that could affect your care plan.</p>"}, {"key": "no_medical_conditions", "type": "radio", "input": true, "label": "<strong>Medical Conditions</strong>", "values": [{"label": "I have no medical conditions", "value": "none"}, {"label": "I have one or more medical conditions", "value": "has_conditions"}], "validate": {"required": true}}, {"key": "user_confirms_no_medical_conditions", "type": "textfield", "input": true, "label": "To confirm you have no medical conditions, please type the word 'none':", "customConditional": "show = data.no_medical_conditions === 'none';", "validate": {"custom": "valid = data.user_confirms_no_medical_conditions && data.user_confirms_no_medical_conditions.toLowerCase().trim() === 'none';", "customMessage": "Please type the word 'none' to confirm."}}, {"key": "medical_conditions", "type": "select", "input": true, "label": "Please select your medical conditions:", "multiple": true, "widget": "<PERSON><PERSON><PERSON>", "search": true, "clearOnHide": false, "data": {"values": [{"label": "High Blood Pressure (Hypertension)", "value": "hypertension"}, {"label": "Heart Disease (Coronary Artery Disease)", "value": "heart_disease"}, {"label": "Heart Failure (CHF)", "value": "chf"}, {"label": "Irregular Heartbeat (Atrial Fibrillation)", "value": "atrial_fibrillation"}, {"label": "High Cholesterol", "value": "high_cholesterol"}, {"label": "Peripheral Artery Disease", "value": "pad"}, {"label": "<PERSON><PERSON>", "value": "angina"}, {"label": "Aortic Aneurysm", "value": "aortic_aneurysm"}, {"label": "Valve Disease", "value": "valve_disease"}, {"label": "Cardiomyopathy", "value": "cardiomyopathy"}, {"label": "Pulmonary Hypertension", "value": "pulmonary_hypertension"}, {"label": "Congenital Heart Defect", "value": "congenital_heart_defect"}, {"label": "Asthma", "value": "asthma"}, {"label": "Chronic Obstructive Pulmonary Disease (COPD)", "value": "copd"}, {"label": "Sleep Apnea", "value": "sleep_apnea"}, {"label": "Chronic Bronchitis", "value": "chronic_bronchitis"}, {"label": "Emphysema", "value": "emphysema"}, {"label": "Pulmonary Fibrosis", "value": "pulmonary_fibrosis"}, {"label": "Cystic Fibrosis", "value": "cystic_fibrosis"}, {"label": "Allergic Rhinitis", "value": "allergic_rhinitis"}, {"label": "Sinusitis (Chronic)", "value": "chronic_sinusitis"}, {"label": "Bronchiectasis", "value": "bronchiectasis"}, {"label": "<PERSON><PERSON>", "value": "lung_nodules"}, {"label": "Pulmonary Sarcoidosis", "value": "pulmonary_sarcoidosis"}, {"label": "Irritable Bowel Syndrome (IBS)", "value": "ibs"}, {"label": "Acid Reflux (GERD)", "value": "gerd"}, {"label": "Celiac Disease", "value": "celiac_disease"}, {"label": "<PERSON><PERSON><PERSON>'s Disease", "value": "crohns"}, {"label": "Ulcerative Colitis", "value": "ulcerative_colitis"}, {"label": "Chronic Constipation", "value": "chronic_constipation"}, {"label": "Diverticulitis", "value": "diverticulitis"}, {"label": "Gallstones", "value": "gallstones"}, {"label": "Fatty Liver Disease (NAFLD)", "value": "nafld"}, {"label": "Chronic Pancreatitis", "value": "chronic_pancreatitis"}, {"label": "Peptic Ulcer Disease", "value": "peptic_ulcer"}, {"label": "<PERSON>'s Esophagus", "value": "barretts_esophagus"}, {"label": "Type 2 Diabetes", "value": "type_2_diabetes"}, {"label": "Type 1 Diabetes", "value": "type_1_diabetes"}, {"label": "Underactive Thyroid (Hypothyroidism)", "value": "hypothyroidism"}, {"label": "Overactive Thyroid (Hyperthyroidism)", "value": "hyperthyroidism"}, {"label": "Polycystic Ovary Syndrome (PCOS)", "value": "pcos"}, {"label": "Adrenal Insufficiency", "value": "adrenal_insufficiency"}, {"label": "Cushing's Syndrome", "value": "cushings_syndrome"}, {"label": "Hyperparathyroidism", "value": "hyperparathyroidism"}, {"label": "Hypoparathyroidism", "value": "hypoparathyroidism"}, {"label": "Acromegaly", "value": "acromegaly"}, {"label": "Goiter", "value": "goiter"}, {"label": "Metabolic Syndrome", "value": "metabolic_syndrome"}, {"label": "Chronic Kidney Disease", "value": "ckd"}, {"label": "Kidney Stones", "value": "kidney_stones"}, {"label": "Urinary Incontinence", "value": "urinary_incontinence"}, {"label": "Recurrent UTIs", "value": "chronic_uti"}, {"label": "Enlarged Prostate (BPH)", "value": "bph"}, {"label": "Interstitial Cystitis", "value": "interstitial_cystitis"}, {"label": "Overactive Bladder", "value": "overactive_bladder"}, {"label": "Chronic Prostatitis", "value": "chronic_prostatitis"}, {"label": "Blood in Urine (Chronic Hematuria)", "value": "chronic_hematuria"}, {"label": "Erectile Dysfunction", "value": "ed"}, {"label": "Hydronephrosis", "value": "hydronephrosis"}, {"label": "Neurogenic Bladder", "value": "neurogenic_bladder"}, {"label": "Oste<PERSON>th<PERSON>is", "value": "osteoarthritis"}, {"label": "Rheumatoid Arthritis", "value": "rheumatoid_arthritis"}, {"label": "Osteoporosis", "value": "osteoporosis"}, {"label": "Fibromyalgia", "value": "fibromyalgia"}, {"label": "Chronic Back Pain", "value": "chronic_back_pain"}, {"label": "Chronic Neck Pain", "value": "chronic_neck_pain"}, {"label": "<PERSON><PERSON><PERSON>", "value": "tendinitis"}, {"label": "Carpal Tunnel Syndrome", "value": "carpal_tunnel"}, {"label": "<PERSON><PERSON><PERSON>", "value": "scoliosis"}, {"label": "Gout", "value": "gout"}, {"label": "Ankylosing Spondylitis", "value": "ankylosing_spondylitis"}, {"label": "Joint Hypermobility", "value": "joint_hypermobility"}, {"label": "Migra<PERSON>s", "value": "migraine"}, {"label": "Epilepsy (Seizure Disorder)", "value": "epilepsy"}, {"label": "Multiple Sclerosis (MS)", "value": "multiple_sclerosis"}, {"label": "Parkinson’s Disease", "value": "parkinsons"}, {"label": "Alzheimer’s Disease (Dementia)", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"label": "Peripheral Neuropathy", "value": "peripheral_neuropathy"}, {"label": "History of Stroke", "value": "stroke"}, {"label": "Essential Tremor", "value": "essential_tremor"}, {"label": "Trigeminal Neuralgia", "value": "trigeminal_neuralgia"}, {"label": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> (Recurring)", "value": "bells_palsy"}, {"label": "Cerebral Palsy", "value": "cerebral_palsy"}, {"label": "Brain Aneurysm (Monitored)", "value": "brain_aneurysm"}, {"label": "Anemia", "value": "anemia"}, {"label": "Sickle Cell Disease", "value": "sickle_cell"}, {"label": "Hemophilia", "value": "hemophilia"}, {"label": "Thalassemia", "value": "thalassemia"}, {"label": "<PERSON><PERSON> (SLE)", "value": "lupus"}, {"label": "HIV/AIDS", "value": "hiv"}, {"label": "Leukemia (Chronic)", "value": "chronic_leukemia"}, {"label": "Lymphoma (Chronic)", "value": "chronic_lymphoma"}, {"label": "ITP (Immune <PERSON>hrombocytopenia)", "value": "itp"}, {"label": "Hemochromatosis (Iron Overload)", "value": "hemochromatosis"}, {"label": "Sarcoidosis", "value": "sarcoidosis"}, {"label": "Common Variable Immunodeficiency (CVID)", "value": "cvid"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "psoriasis"}, {"label": "<PERSON><PERSON>ema (Atopic Dermatitis)", "value": "eczema"}, {"label": "<PERSON><PERSON>", "value": "rosacea"}, {"label": "Vitiligo", "value": "vitiligo"}, {"label": "Ch<PERSON> (Urticaria)", "value": "urticaria"}, {"label": "Seborrheic Dermatitis", "value": "seborrheic_dermatitis"}, {"label": "<PERSON><PERSON><PERSON>", "value": "keloids"}, {"label": "Severe Chronic Acne", "value": "chronic_acne"}, {"label": "Skin Cancer (Basal or Squamous Cell)", "value": "skin_cancer"}, {"label": "History of Melanoma", "value": "melanoma"}, {"label": "<PERSON><PERSON>", "value": "lichen_planus"}, {"label": "Chronic Nail <PERSON> (Onychomycosis)", "value": "onychomycosis"}, {"label": "Depression", "value": "depression"}, {"label": "Generalized Anxiety Disorder", "value": "anxiety"}, {"label": "Post-Traumatic Stress Disorder (PTSD)", "value": "ptsd"}, {"label": "Bipolar Disorder", "value": "bipolar"}, {"label": "Schizophrenia", "value": "schizophrenia"}, {"label": "Obsessive Compulsive Disorder (OCD)", "value": "ocd"}, {"label": "Panic Disorder", "value": "panic_disorder"}, {"label": "Eating Disorder", "value": "eating_disorder"}, {"label": "Borderline Personality Disorder", "value": "bpd"}, {"label": "ADHD (Attention Deficit Hyperactivity Disorder)", "value": "adhd"}, {"label": "Substance Use Disorder", "value": "substance_use"}, {"label": "Autism Spectrum Disorder", "value": "autism"}, {"label": "Endometriosis", "value": "endometriosis"}, {"label": "Polycystic Ovary Syndrome (PCOS)", "value": "pcos"}, {"label": "Chronic Pelvic Pain Syndrome", "value": "chronic_pelvic_pain"}, {"label": "Uterine Fibroids", "value": "fibroids"}, {"label": "Irregular Menstrual Cycles", "value": "irregular_periods"}, {"label": "Menopause Symptoms", "value": "menopause"}, {"label": "Female Infertility", "value": "female_infertility"}, {"label": "Male Infertility", "value": "male_infertility"}, {"label": "Erectile Dysfunction", "value": "ed"}, {"label": "Premature Ejaculation", "value": "pe"}, {"label": "Low Testosterone", "value": "low_testosterone"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "vaginismus"}, {"label": "Glaucoma", "value": "glaucoma"}, {"label": "Macular Degeneration", "value": "macular_degeneration"}, {"label": "Cataracts", "value": "cataracts"}, {"label": "Chronic Dry Eyes", "value": "dry_eyes"}, {"label": "<PERSON><PERSON><PERSON> (Ringing in the Ears)", "value": "tinnitus"}, {"label": "Hearing Loss", "value": "hearing_loss"}, {"label": "Vertigo (Balance Issues)", "value": "vertigo"}, {"label": "<PERSON><PERSON><PERSON>’s Disease", "value": "menieres_disease"}, {"label": "Chronic Nasal Congestion", "value": "nasal_congestion"}, {"label": "Uveitis (Eye Inflammation)", "value": "uveitis"}, {"label": "Color Blindness", "value": "color_blindness"}, {"label": "Retinitis Pigmentosa", "value": "retinitis_pigmentosa"}]}, "customConditional": "show = data.no_medical_conditions === 'has_conditions';"}, {"key": "medical_conditions_complete", "type": "radio", "input": true, "label": "Is this a complete list of your medical conditions?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No, I have other conditions not listed", "value": "no"}], "customConditional": "show = data.no_medical_conditions === 'has_conditions';", "validate": {"required": true}}, {"key": "other_medical_conditions", "type": "textarea", "input": true, "label": "Please list any other medical conditions:", "customConditional": "show = data.medical_conditions_complete === 'no';"}, {"key": "no_allergies", "type": "radio", "input": true, "label": "<strong>Allergies</strong>", "values": [{"label": "I have no allergies", "value": "none"}, {"label": "I have one or more allergies", "value": "has_allergies"}], "validate": {"required": true}}, {"key": "user_confirms_no_allergies", "type": "textfield", "input": true, "label": "To confirm you have no allergies, please type the word 'none':", "customConditional": "show = data.no_allergies === 'none';", "validate": {"custom": "valid = data.user_confirms_no_allergies && data.user_confirms_no_allergies.toLowerCase().trim() === 'none';", "customMessage": "Please type the word 'none' to confirm."}}, {"key": "allergies", "type": "select", "input": true, "label": "Please select your allergies:", "data": {"values": [{"label": "Penicillin", "value": "penicillin"}, {"label": "Sulfa drugs", "value": "sulfa"}, {"label": "Aspirin/NSAIDs", "value": "nsaids"}, {"label": "Latex", "value": "latex"}, {"label": "Peanuts", "value": "peanuts"}]}, "widget": "<PERSON><PERSON><PERSON>", "multiple": true, "customConditional": "show = data.no_allergies === 'has_allergies';"}, {"key": "allergies_complete", "type": "radio", "input": true, "label": "Is this a complete list of your allergies?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No, I have other allergies not listed", "value": "no"}], "customConditional": "show = data.no_allergies === 'has_allergies';", "validate": {"required": true}}, {"key": "other_allergies", "type": "textarea", "input": true, "label": "Please list any other allergies:", "customConditional": "show = data.allergies_complete === 'no';"}, {"key": "no_medications", "type": "radio", "input": true, "label": "<strong>Current Medications</strong>", "values": [{"label": "I am not taking any medications", "value": "none"}, {"label": "I take one or more medications", "value": "has_medications"}], "validate": {"required": true}}, {"key": "user_confirms_no_medications", "type": "textfield", "input": true, "label": "To confirm you have no allergies, please type the word 'none':", "customConditional": "show = data.no_medications === 'none';", "validate": {"custom": "valid = data.user_confirms_no_medications && data.user_confirms_no_medications.toLowerCase().trim() === 'none';", "customMessage": "Please type the word 'none' to confirm."}}]}