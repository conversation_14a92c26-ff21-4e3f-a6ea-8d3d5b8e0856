{"components": [{"key": "prior_metabolic_testing", "type": "radio", "input": true, "label": "Have you completed metabolic screening before?", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "prior_metabolic_testing_warning", "html": "<p class='text-red'>The panel you have selected is for individuals who have previously completed metabolic screening by a physician, nurse practioner or TeleTest. </p><ul><li class='text-black'>To obtain routine preventative bloodwork, please select our <a href='https://teletest.ca/app/care/preventative/' target='_blank'>Metabolic Panel</a>.</li></ul>", "type": "content", "input": true, "tableView": false, "clearOnHide": false, "customConditional": "show = data.prior_metabolic_testing && data.prior_metabolic_testing == 'no';"}, {"key": "longevity_contraindications_symptoms", "type": "selectboxes", "input": true, "label": "Are you experiencing any of the following symptoms:", "values": [{"label": "Fatigue or weakness", "value": "fatigue", "shortcut": ""}, {"label": "Dizziness or fainting spells", "value": "dizziness", "shortcut": ""}, {"label": "Feel unwell or have a fever", "value": "fever", "shortcut": ""}, {"label": "Palpitations or chest pain", "value": "palpitations", "shortcut": ""}, {"label": "Shortness of breath", "value": "dyspnea", "shortcut": ""}, {"label": "Chest tightness, especially if occurring during or after  activity", "value": "chest_pain_with_sexual_activity", "shortcut": ""}], "inputType": "checkbox", "tableView": true, "customConditional": "show = data.prior_metabolic_testing == 'yes';", "optionsLabelPosition": "right"}, {"key": "asymptomatic_longevity", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = _.some(_.values(data.longevity_contraindications_symptoms)) || data.asymptomatic_longevity;"}, "tableView": true, "customConditional": "show = data.prior_metabolic_testing == 'yes';", "customClass": "mt-n3", "defaultValue": false}, {"key": "healthy_living_choices", "type": "radio", "input": true, "label": "This bloodwork listed on this panel isn't routinely recommended, but is offered to individuals who feel monitoring these levels will guide your choices for healthy living. Do you feel these understanding these values will help you make better lifestyle choices?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.asymptomatic_longevity == true;", "optionsLabelPosition": "right"}, {"key": "test_frequency", "html": "<p class='text-green'>You may request this bloodwork on a quarterly basis. Please note that you will incur seperate charges at the lab depending on the tests you select from this panel. </p><ul><li class='text-black'>We have more information about lab test pricing <a href='https://teletest.ca/blog/cost-of-blood-test-in-ontario-canada/' target='_blank'>here</a>.</li></ul>", "type": "content", "input": true, "tableView": false, "clearOnHide": false, "customConditional": "show = data.healthy_living_choices == true;"}, {"key": "no_indications", "type": "textfield", "input": true, "label": "No Indication:", "hidden": true, "disabled": true, "multiple": false, "tableView": true, "clearOnHide": false, "calculateValue": "value = data.indication_for_testing.none_of_the_above && data.no_pylori_contraindication;", "refreshOnChange": true}, {"key": "contraindication_keys", "type": "textfield", "input": true, "label": "Contraindication keys:", "hidden": true, "disabled": true, "multiple": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "value = []; if (data.indication_for_testing.none_of_the_above) { value.push('no_indication_for_testing'); } if (data.duration_of_symptoms === 'less_than_6_months') { value.push('duration_of_symptoms_less_than_6_months'); } if (_.keys(_.pickBy(data.pylori_contraindication)).length > 0) { value.push('has_pylori_contraindication'); }", "refreshOnChange": true}]}