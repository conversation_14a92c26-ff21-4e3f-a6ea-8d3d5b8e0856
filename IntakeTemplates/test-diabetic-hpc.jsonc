{"components": [{"key": "content", "html": "<h1>Instructions</h1><p>Please complete the following questionnaire about your interest in diabetic monitoring.</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "header_lipid_questions", "html": "<h1>Cholesterol History&nbsp;</h1>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "previous_cholesterol_testing", "type": "radio", "input": true, "label": "Have you had cholesterol testing in the past?", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "I Don't Know", "value": "doesn't_know", "shortcut": ""}], "tableView": true, "optionsLabelPosition": "right"}, {"key": "previous_lipoprotein_a", "type": "radio", "input": true, "label": "Have you had a blood test for Lipoprotein(a) in the past?", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "I Don't Know", "value": "i_dont_know", "shortcut": ""}], "tableView": true, "optionsLabelPosition": "right"}, {"key": "last_cholesterol_test", "data": {"values": [{"label": "< 3 months ago", "value": "<3months"}, {"label": "3-6 months ago", "value": "3-6 months ago"}, {"label": "6-12 months ago", "value": "6-12 months ago"}, {"label": "1 - 2 years ago", "value": "1-2 years ago"}, {"label": "2 - 3 years ago", "value": "2-3 years ago"}, {"label": "3-5 years ago", "value": "3-5 years ago"}, {"label": "More than 5 years ago", "value": "5+_years"}]}, "type": "select", "input": true, "label": "When did you have your last cholesterol testing completed outside of using TeleTest?", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.previous_cholesterol_testing == 'yes';", "optionsLabelPosition": "right"}, {"key": "previous_abnormal_lipids", "type": "radio", "input": true, "label": "Do you recall if your previous cholesterol testing was abnormal (i.e. high or borderline high cholesterol)?", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "I Don't Know", "value": "doesn't_know", "shortcut": ""}], "tableView": true, "customConditional": "show = data.previous_cholesterol_testing == 'yes';", "optionsLabelPosition": "right"}, {"key": "select_abnormal_cholesterol_values", "type": "selectboxes", "input": true, "label": "Please check any of the following apply to your prior tests (HDL = good cholesterol, LDL = bad cholesterol, Triglycerides = fat in blood):", "values": [{"label": "High LDL", "value": "high_ldl", "shortcut": ""}, {"label": "Low LDL", "value": "low_ldl", "shortcut": ""}, {"label": "High HDL", "value": "high_hdl", "shortcut": ""}, {"label": "Low HDL", "value": "low_hdl", "shortcut": ""}, {"label": "High Triglycerides", "value": "high_tg", "shortcut": ""}, {"label": "Low Triglycerides", "value": "low_tg", "shortcut": ""}, {"label": "I Don't Know", "value": "doesn't know", "shortcut": ""}], "inputType": "checkbox", "tableView": true, "customConditional": "show = data.previous_abnormal_lipids == 'yes';", "optionsLabelPosition": "right"}, {"key": "header_diabetic_questions", "html": "<h1>Diabetic Testing&nbsp;</h1>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "header_history_questions", "html": "<h3>Diagnostic History&nbsp;</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "previous_diabetic_diagnosis", "type": "radio", "input": true, "label": "Have you been diagnosed with diabetes in the past?", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "I Don't Know", "value": "i_dont_know", "shortcut": ""}], "tableView": true, "optionsLabelPosition": "right"}, {"key": "time_of_dm_diagnosis", "data": {"values": [{"label": "< 3 months ago", "value": "<3months"}, {"label": "3-6 months ago", "value": "3-6_months"}, {"label": "6-12 months ago", "value": "6-12_months"}, {"label": "1 - 2 years ago", "value": "1-2_years"}, {"label": "2 - 3 years ago", "value": "2-3_years"}, {"label": "3-5 years ago", "value": "3-5_years"}, {"label": "5-10 years ago", "value": "5-10_years"}, {"label": "10-15 years ago", "value": "10-15_years"}, {"label": "More than 15 years", "value": "15+_years"}]}, "type": "select", "input": true, "label": "How many years ago were you diagnosed with diabetes?", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.previous_diabetic_diagnosis == 'yes';", "optionsLabelPosition": "right"}, {"key": "original_dm_diagnosis", "type": "radio", "input": true, "label": "Who diagnosed you with diabetes originally?", "inline": false, "values": [{"label": "Family Doctor / General Practitioner", "value": "general_practitioner", "shortcut": ""}, {"label": "Nurse practitioner", "value": "nurse_practitioner", "shortcut": ""}, {"label": "<PERSON><PERSON><PERSON>", "value": "naturopath", "shortcut": ""}, {"label": "Self-Diagnosis", "value": "self-diagnosis", "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.previous_diabetic_diagnosis == 'yes';", "optionsLabelPosition": "right"}, {"key": "diagnosis_other_provider", "type": "textarea", "input": true, "label": "Please state who diagnosed you with diabetes:", "tableView": true, "autoExpand": false, "customConditional": "show = data.original_dm_diagnosis == 'other';"}, {"key": "regular_dm_monitoring", "type": "radio", "input": true, "label": "Have you been getting your diabetes monitored regularly?", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}], "tableView": true, "customConditional": "show = data.previous_diabetic_diagnosis == 'yes';", "optionsLabelPosition": "right"}, {"key": "last_diabetic_test", "data": {"values": [{"label": "< 3 months ago", "value": "<3months"}, {"label": "3-6 months ago", "value": "3-6 months ago"}, {"label": "6-12 months ago", "value": "6-12 months ago"}, {"label": "1 - 2 years ago", "value": "1-2 years ago"}, {"label": "2 - 3 years ago", "value": "2-3 years ago"}, {"label": "3-5 years ago", "value": "3-5 years ago"}, {"label": "More than 5 years ago", "value": "5+_years"}]}, "type": "select", "input": true, "label": "When did you have your last diabetic test completed outside of using TeleTest?", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.previous_diabetic_diagnosis == 'yes';", "optionsLabelPosition": "right"}, {"key": "header_bloodwork_questions", "html": "<h3>Lab Testing&nbsp;</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.previous_diabetic_diagnosis == 'yes';"}, {"key": "last_diabetic_test_a1c", "data": {"values": [{"label": "I don't recall", "value": "doesn't_know"}, {"label": "<5.5%", "value": "<5.5%"}, {"label": "5.6%", "value": "5.6%"}, {"label": "5.7%", "value": "5.7%"}, {"label": "5.8%", "value": "5.8%"}, {"label": "5.9%", "value": "5.9%"}, {"label": "6.0%", "value": "6.0%"}, {"label": "6.1%", "value": "6.1%"}, {"label": "6.2%", "value": "6.2%"}, {"label": "6.3%", "value": "6.3%"}, {"label": "6.4%", "value": "6.4%"}, {"label": "6.5%", "value": "6.5%"}, {"label": "6.6%", "value": "6.6%"}, {"label": "6.7%", "value": "6.7%"}, {"label": "6.8%", "value": "6.8%"}, {"label": "6.9%", "value": "6.9%"}, {"label": "7.0%", "value": "7.0%"}, {"label": "7.1%", "value": "7.1%"}, {"label": "7.2%", "value": "7.2%"}, {"label": "7.3%", "value": "7.3%"}, {"label": "7.4%", "value": "7.4%"}, {"label": "7.5%", "value": "7.5%"}, {"label": "7.6%", "value": "7.6%"}, {"label": "7.7%", "value": "7.7%"}, {"label": "7.8%", "value": "7.8%"}, {"label": "7.9%", "value": "7.9%"}, {"label": "8.0%", "value": "8.0%"}, {"label": "8.1%", "value": "8.1%"}, {"label": "8.2%", "value": "8.2%"}, {"label": "8.3%", "value": "8.3%"}, {"label": "8.4%", "value": "8.4%"}, {"label": "8.5%", "value": "8.5%"}, {"label": "8.6%", "value": "8.6%"}, {"label": "8.7%", "value": "8.7%"}, {"label": "8.8%", "value": "8.8%"}, {"label": "8.9%", "value": "8.9%"}, {"label": "9+%", "value": "9+%"}]}, "type": "select", "input": true, "label": "Please report your last <strong>A1c</strong> level if you can recall the measurement. If you do not recall the number, select 'I don't recall'.", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.previous_diabetic_diagnosis == 'yes';", "optionsLabelPosition": "right"}, {"key": "stable_a1c_bloodwork", "type": "radio", "input": true, "label": "Have you had stable HbA1c values over several blood tests?", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "Don't know", "value": "don't_know", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.previous_diabetic_diagnosis == 'yes';", "optionsLabelPosition": "right"}, {"key": "header_urine_questions", "html": "<h3>Urine&nbsp;</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.previous_diabetic_diagnosis == 'yes';"}, {"key": "previous_screening_acr", "type": "radio", "input": true, "label": "Have you had testing of your urine with your routine diabetic screening?", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "I don't know the answer", "value": "doesn't_know", "shortcut": ""}], "tableView": true, "customConditional": "show = data.previous_diabetic_diagnosis == 'yes';", "optionsLabelPosition": "right"}, {"key": "last_urine_acr_test", "data": {"values": [{"label": "I don't know", "value": "doesn't_know"}, {"label": "< 3 months ago", "value": "<3months"}, {"label": "3-6 months ago", "value": "3-6_months"}, {"label": "6-12 months ago", "value": "6-12_months"}, {"label": "12 + months ago", "value": "12+_months"}]}, "type": "select", "input": true, "label": "When did you have your last diabetic urine test completed?", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.previous_diabetic_diagnosis == 'yes' && data.previous_screening_acr == 'yes';", "optionsLabelPosition": "right"}, {"key": "previous_abnormal_acr", "type": "radio", "input": true, "label": "Do you recall if you your previous urine testing was abnormal (i.e. high urine ACR)?", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "I don't know", "value": "doesn't_know", "shortcut": ""}], "tableView": true, "customConditional": "show = data.previous_screening_acr == 'yes';", "optionsLabelPosition": "right"}, {"key": "header_eye_exam_questions", "html": "<h3>Eye Exam&nbsp;</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.previous_diabetic_diagnosis == 'yes';"}, {"key": "previous_dm_eye_exam", "type": "radio", "input": true, "label": "Have you had a diabetic eye exam in the past? (Note: A diabetic eye exam is different than a usual check-up for prescription glasses.  It is much longer than a normal eye exam and involves taking multiple pictures of the back of your eye or retina).", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "I Don't Know", "value": "i_dont_know", "shortcut": ""}], "tableView": true, "customConditional": "show = data.previous_diabetic_diagnosis == 'yes';", "optionsLabelPosition": "right"}, {"key": "previous_dm_eye_disease", "type": "radio", "input": true, "label": "Have you been told you have diabetic eye disease?", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "I don't know the answer", "value": "doesn't_know", "shortcut": ""}], "tableView": true, "customConditional": "show = data.previous_dm_eye_exam == 'yes';", "optionsLabelPosition": "right"}, {"key": "header_treatment_diabetes", "html": "<h2>Diabetes Treatment&nbsp;</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.previous_diabetic_diagnosis == 'yes';"}, {"key": "diabetes_management", "type": "selectboxes", "input": true, "label": "How do you manage your diabetes:", "values": [{"label": "Exercise", "value": "exercise", "shortcut": ""}, {"label": "Weight Loss", "value": "weight_loss", "shortcut": ""}, {"label": "Dietary Changes", "value": "diet", "shortcut": ""}, {"label": "Medications", "value": "medications", "shortcut": ""}, {"label": "I don't do anything to manage my diabetes", "value": "does_nothing", "shortcut": ""}], "inputType": "checkbox", "tableView": true, "customConditional": "show = data.previous_diabetic_diagnosis == 'yes';", "optionsLabelPosition": "right"}, {"key": "currently_on_meds", "type": "radio", "input": true, "label": "Are you currently on diabetes medication?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.previous_diabetic_diagnosis == 'yes';", "optionsLabelPosition": "right"}, {"key": "types_of_diabetes_medications", "type": "selectboxes", "input": true, "label": "What types of medication do you currently use to manage your diabetes:", "values": [{"label": "Pills", "value": "pills", "shortcut": ""}, {"label": "<PERSON><PERSON><PERSON>", "value": "insulin", "shortcut": ""}, {"label": "Non-insulin injections (i.e. ozempic, victoza, trulicity)", "value": "diet", "shortcut": ""}, {"label": "None of the above", "value": "none_of_the_above", "shortcut": ""}], "inputType": "checkbox", "tableView": true, "customConditional": "show = data.currently_on_meds == true;", "optionsLabelPosition": "right"}, {"key": "list_dm_medications", "type": "textarea", "input": true, "label": "Please include a list of your diabetic medications here:", "tableView": true, "autoExpand": false, "customConditional": "show = data.currently_on_meds == true;"}, {"key": "duration_current_dose", "type": "radio", "input": true, "label": "How long have you been taking your medication at the current daily dose?", "inline": false, "values": [{"label": "< 6 weeks", "value": "<6_weeks"}, {"label": "6 weeks - 3 months", "value": "6_weeks-3_months"}, {"label": "3-6 months", "value": "3_months-6_months"}, {"label": "6-12 months", "value": "6-12_months"}, {"label": "12+ months", "value": "12+_months"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.currently_on_meds == true;", "optionsLabelPosition": "right"}, {"key": "previously_on_meds", "type": "radio", "input": true, "label": "Were you previously on diabetic medication?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.currently_on_meds == false && data.previous_diabetic_diagnosis == 'yes';", "optionsLabelPosition": "right"}, {"key": "last_time_took_medication", "type": "radio", "input": true, "label": "When were you last on diabetic medication?", "inline": false, "values": [{"label": "< 6 weeks", "value": "<6_weeks"}, {"label": "6 weeks - 3 months", "value": "6_weeks-3_months"}, {"label": "3-6 months", "value": "3_months-6_months"}, {"label": "6-12 months", "value": "6-12_months"}, {"label": "12+ months", "value": "12+_months"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.previously_on_meds == true && data.previous_diabetic_diagnosis == 'yes';", "optionsLabelPosition": "right"}, {"key": "types_of_diabetes_medications", "type": "selectboxes", "input": true, "label": "What types of medication were you previously using to manage your diabetes:", "values": [{"label": "Pills", "value": "pills", "shortcut": ""}, {"label": "<PERSON><PERSON><PERSON>", "value": "insulin", "shortcut": ""}, {"label": "Non-insulin injections (i.e. ozempic, victoza, trulicity)", "value": "diet", "shortcut": ""}, {"label": "None of the above", "value": "none_of_the_above", "shortcut": ""}], "inputType": "checkbox", "tableView": true, "customConditional": "show = data.previously_on_meds == true;", "optionsLabelPosition": "right"}, {"key": "reason_stopping", "type": "radio", "input": true, "label": "Please select your reason(s) for stopping your meds:", "values": [{"label": "I ran out of medication", "value": "ran_out_of_medication", "shortcut": ""}, {"label": "Didn't have anyone to monitor my diabetes", "value": "no_one_monitor_tsh", "shortcut": ""}, {"label": "Had a normal A1c after running out of medication", "value": "normal_tsh_after_cessation", "shortcut": ""}, {"label": "I wasn't sure I needed them anymore", "value": "felt_didn't_need_them", "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "confirm_label": "Reason(s) for stopping:", "customConditional": "show = data.previously_on_meds == true;", "optionsLabelPosition": "right"}, {"key": "stopped_med_other", "type": "textarea", "input": true, "label": "Please state your “other” reason(s) for stopping your medication:", "tableView": true, "autoExpand": false, "customConditional": "show = data.reason_stopping == 'other';"}, {"key": "header_blood_pressure", "html": "<h3>Blood Pressure&nbsp;</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "blood_pressure", "type": "radio", "input": true, "label": "Are you currently on blood pressure medication?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "blood_pressure_measurement", "type": "radio", "input": true, "label": "Do you monitor your blood pressure at home, the pharmacy or the gym?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "high_blood_pressure_measurement", "type": "radio", "input": true, "label": "Is your blood pressure measurement above 130/80 (either number above the cut-off counts as high) ?", "inline": false, "values": [{"label": "Yes - my blood pressure is <strong>above</strong> 130/80", "value": "yes", "shortcut": ""}, {"label": "No - my blood pressure is <strong>below</strong> 130/80", "value": "no", "shortcut": ""}, {"label": "I Don't Know", "value": "doesn't_know", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.blood_pressure_measurement == true;", "optionsLabelPosition": "right"}, {"key": "header_medication_side_effects", "html": "<h2>Diabetes & Medication Side Effects&nbsp;</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.previous_diabetic_diagnosis == 'yes';"}, {"key": "hypoglycemic_episodes", "type": "radio", "input": true, "label": "Have you ever had symptoms low blood sugar (feeling lightheaded, faint, nausea or shakiness assiciated with a low blood sugar)?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.currently_on_meds == true;", "optionsLabelPosition": "right"}, {"key": "dm_event_list", "type": "selectboxes", "input": true, "label": "Have you been diagnosed with, or need any of the following:", "values": [{"label": "Heart Attack (MI)", "value": "prior_mi", "shortcut": ""}, {"label": "Stroke (CVA)", "value": "prior_stroke", "shortcut": ""}, {"label": "Mini-Stroke (TIA)", "value": "prior_tia", "shortcut": ""}, {"label": "Cardiovascular Disease (plaque build-up in heart)", "value": "cardiovascular_disease", "shortcut": ""}, {"label": "Peripheral Vascular Disease (plaque build-up in arms or legs)", "value": "peripheral_disease", "shortcut": ""}, {"label": "Cerebro-Vascular Disease (plaque build-up in the brain)", "value": "cerebrovascular_disease", "shortcut": ""}, {"label": "Cardiac Bypass", "value": "bypass", "shortcut": ""}, {"label": "Cardiac Stenting", "value": "cardiac_stenting", "shortcut": ""}, {"label": "None of the Above", "value": "none_of_the_above", "shortcut": ""}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "confirm_label": "Current Symptoms:", "customConditional": "show = data.previous_diabetic_diagnosis == 'yes';", "optionsLabelPosition": "right"}, {"key": "dm_symptom_list", "type": "selectboxes", "input": true, "label": "Do you have any of the following symptoms:", "values": [{"label": "Burning or numbness in my toes", "value": "paresthesias", "shortcut": ""}, {"label": "Chest tightness/pressure with walking", "value": "chest_tightness", "shortcut": ""}, {"label": "Shortness of breath with walking or sitting", "value": "shortness_of_breath", "shortcut": ""}, {"label": "Blurry vision that is new", "value": "blurry_vision", "shortcut": ""}, {"label": "Urinating more frequently than normal", "value": "urinary_frequency", "shortcut": ""}, {"label": "Feel lightheaded/faint", "value": "presy<PERSON><PERSON>", "shortcut": ""}, {"label": "Difficulties getting or maintaining erections", "value": "erectile_dysfunction", "shortcut": "", "customConditional": "show = data.sex == 'male';"}, {"label": "None of the above", "value": "none_of_the_above", "shortcut": ""}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "confirm_label": "Current Symptoms:", "customConditional": "show = data.previous_diabetic_diagnosis == 'yes';", "optionsLabelPosition": "right"}, {"key": "header_risk_factors", "html": "<h3>Risk Factors&nbsp;</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "repeat_testing_risk_factors", "type": "selectboxes", "input": true, "label": "Please check any of the following apply to you since your last cholesterol/diabetic testing:", "values": [{"label": "Gained more than 5% of my body weight", "value": "weight gain", "shortcut": ""}, {"label": "Started smoking cigarettes", "value": "started_smoking", "shortcut": ""}, {"label": "Started on a medication that requires monitoring", "value": "medication_monitoring_indication", "shortcut": ""}, {"label": "None of the Above", "value": "none_of_the_above", "shortcut": ""}], "inputType": "checkbox", "tableView": true, "optionsLabelPosition": "right"}, {"key": "header_hemoglobinopathy", "html": "<h3>Diseases of Hemoglobin&nbsp;</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "hemoglobinopathy_screen", "type": "selectboxes", "input": true, "label": "Have you been diagnosed with any of the following:", "values": [{"label": "Alpha Thalassemia", "value": "alpha_thalassemia", "shortcut": ""}, {"label": "Beta Thalassemia", "value": "beta_thalassemia", "shortcut": ""}, {"label": "Sickle Cell Trait", "value": "sickle_cell_trait", "shortcut": ""}, {"label": "Sickle Cell Disease", "value": "sickle_cell_disease", "shortcut": ""}, {"label": "Iron Deficiency Anemia", "value": "iron_deficiency_anemia", "shortcut": ""}, {"label": "None of the Above", "value": "none_of_the_above", "shortcut": ""}], "inputType": "checkbox", "tableView": true, "optionsLabelPosition": "right"}]}