{"_vid": 0, "name": "canRiskTool", "path": "canrisktool", "tags": [], "type": "form", "title": "CanRisk Tool", "display": "form", "settings": {}, "revisions": "", "components": [{"key": "riskFactors", "type": "plainpanel", "input": false, "label": "RiskFactors", "title": "Risk Factors", "hideLabel": true, "tableView": true, "components": [{"key": "fm_hx_dm", "type": "selectboxes", "input": true, "label": "Please select from the following list of family members (blood relatives only) if they have been diagnosed with diabetes. ", "values": [{"label": "Mother", "value": "mother", "shortcut": ""}, {"label": "Father", "value": "father", "shortcut": ""}, {"label": "Sibling", "value": "sibling", "shortcut": ""}, {"label": "Child", "value": "child", "shortcut": ""}], "inputType": "checkbox", "tableView": true, "defaultValue": {"child": false, "father": false, "mother": false, "sibling": false, "children": false, "brotherSister": false}, "optionsLabelPosition": "right"}, {"key": "ethnic_group", "type": "radio", "input": true, "label": "Please select your ethnic group. For mixed race individuals, please choose the first one that applies to you (highest risk at top, lowest risk at bottom)", "inline": false, "values": [{"label": "[Highest risk] South Asian (East Indian, Pakistani, Sri Lankan, etc.)", "value": "southAsian", "shortcut": ""}, {"label": "East Asian (Chinese, Vietnamese, Filipino, Korean, etc.)", "value": "eastAsian", "shortcut": ""}, {"label": "Black (Afro-Caribbean)", "value": "black", "shortcut": ""}, {"label": "Aboriginal", "value": "aboriginal", "shortcut": ""}, {"label": "Other non-white (Latin American, Arab, West Asian)", "value": "other", "shortcut": ""}, {"label": "[Lowest risk] White (Caucasian)", "value": "white", "shortcut": ""}], "tableView": true, "optionsLabelPosition": "right"}, {"key": "dm_rf", "type": "selectboxes", "input": true, "label": "Please select any risk factors that apply to you:", "values": [{"label": "History of Coronary Artery Disease", "value": "hx_cad", "shortcut": ""}, {"label": "Current Smoker", "value": "current_smoker", "shortcut": ""}, {"label": "History of Diabetes in Pregnancy", "value": "dm_pregnancy", "shortcut": ""}, {"label": "Kidney Disease", "value": "kidney_disease", "shortcut": ""}], "inputType": "checkbox", "tableView": true, "defaultValue": {"hx_cad": false, "dm_pregnancy": false, "current_smoker": false, "kidney_disease": false}, "optionsLabelPosition": "right"}, {"key": "dm_associated_diseases", "type": "selectboxes", "input": true, "label": "Please select if you have any of the following medical conditions:", "values": [{"label": "Pancreatitis", "value": "pancreatitis", "shortcut": ""}, {"label": "Polycystic Ovarian Syndrome (PCOS)", "value": "pcos", "shortcut": ""}, {"label": "Gout", "value": "gout", "shortcut": ""}, {"label": "Fatty Liver Disease", "value": "fatty_liver", "shortcut": ""}, {"label": "Schizophrenia", "value": "schizophrenia", "shortcut": ""}, {"label": "HIV", "value": "hiv", "shortcut": ""}, {"label": "Bipolar ", "value": "bipolar", "shortcut": ""}, {"label": "Depression", "value": "depression", "shortcut": ""}, {"label": "Sleep Apnea", "value": "sleepApnea", "shortcut": ""}, {"label": "Cystic Fibrosis", "value": "cystic_fibrosis", "shortcut": ""}, {"label": "Acromegaly", "value": "acromegaly", "shortcut": ""}, {"label": "Aldosteronoma", "value": "aldosteronoma", "shortcut": ""}, {"label": "Somatostatinoma", "value": "somatostatinoma", "shortcut": ""}, {"label": "Glucogonoma", "value": "glucogonoma", "shortcut": ""}, {"label": "Hyperthyroidism", "value": "hyperthyroidism", "shortcut": ""}, {"label": "Pheochromocytoma", "value": "pheochromocytoma", "shortcut": ""}], "inputType": "checkbox", "tableView": true, "defaultValue": {"hiv": false, "gout": false, "pcos": false, "bipolar": false, "acromegaly": false, "depression": false, "sleepApnea": false, "fatty_liver": false, "glucogonoma": false, "pancreatitis": false, "aldosteronoma": false, "schizophrenia": false, "cystic_fibrosis": false, "hyperthyroidism": false, "somatostatinoma": false, "pheochromocytoma": false}, "optionsLabelPosition": "right"}, {"key": "dm_associated_meds", "type": "selectboxes", "input": true, "label": "Select any medications from the list that you are currently taking:", "values": [{"label": "Prednisone", "value": "prednisone", "shortcut": ""}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "olanzpine", "shortcut": ""}, {"label": "<PERSON><PERSON><PERSON> (rosuvastatin)", "value": "ros<PERSON><PERSON><PERSON><PERSON>", "shortcut": ""}, {"label": "Prograf (tacrolimus)", "value": "tacrolimus", "shortcut": ""}, {"label": "HIV HAART Therapy", "value": "haart_therapy", "shortcut": ""}, {"label": "Risperidone", "value": "risperidone", "shortcut": ""}, {"label": "Quetiapine", "value": "quetiapine", "shortcut": ""}, {"label": "<PERSON><PERSON><PERSON> (atorvastatin)", "value": "lipitorAtorvastatin", "shortcut": ""}, {"label": "<PERSON><PERSON><PERSON>", "value": "dilantin", "shortcut": ""}, {"label": "Thyroid <PERSON>e ", "value": "thyroid_hormone", "shortcut": ""}, {"label": "Hydrochlorothiazide (HCTZ)", "value": "hctz", "shortcut": ""}], "inputType": "checkbox", "tableView": true, "defaultValue": {"hctz": false, "dilantin": false, "olanzpine": false, "prednisone": false, "quetiapine": false, "tacrolimus": false, "risperidone": false, "rosuvastatin": false, "haart_therapy": false, "thyroid_hormone": false, "lipitorAtorvastatin": false}, "optionsLabelPosition": "right"}, {"key": "dm_rfList", "type": "textfield", "input": true, "label": "Risk Factors List", "disabled": true, "tableView": true, "spellcheck": false, "calculateValue": {"_join": [{"_compact": [[{"if": [{">": [{"var": "data.age"}, 40]}, "Age > 40"]}, {"if": [{"_some": {"_values": {"var": "data.fm_hx_dm"}}}, "Family History"]}, {"if": [{"and": [{"var": "data.ethnic_group"}, {"!=": [{"var": "data.ethnic_group"}, "white"]}]}, "Ethnicity"]}, {"if": [{">=": [{"var": "data.bmi"}, 25]}, "Overweight"]}, {"if": [{"var": "data.dm_rf.hx_cad"}, "Coronary Artery Disease History"]}, {"if": [{"var": "data.dm_rf.current_smoker"}, "Current Smoker"]}, {"if": [{"var": "data.dm_rf.dm_pregnancy"}, "History of Diabetes in Pregnancy"]}, {"if": [{"var": "data.dm_rf.kidney_disease"}, "Kidney Disease"]}, {"if": [{"_some": {"_values": {"var": "data.dm_associated_meds"}}}, "Associated Medication"]}, {"if": [{"_some": {"_values": {"var": "data.dm_associated_diseases"}}}, "Associated Disease or Condition"]}]]}, ", "]}}], "collapsible": false}, {"key": "can_risk_score", "type": "plainpanel", "input": false, "label": "can_risk_score", "title": "CanRisk Score", "hideLabel": true, "tableView": true, "components": [{"key": "waist_male", "type": "radio", "input": true, "label": "What is your waist circumference? Measure at your belly button after breathing out (do not hold your breath). Note this is <em>not</em> the same as the waist size on pants.", "inline": false, "values": [{"label": "Less than 94cm (37 inches)", "value": "small", "shortcut": ""}, {"label": "Between 94-102 cm (37-40 inches)", "value": "medium", "shortcut": ""}, {"label": "Greater than 102 cm (40 inches)", "value": "large", "shortcut": ""}], "tableView": true, "conditional": {"eq": "female", "show": false, "when": "sex"}, "optionsLabelPosition": "right"}, {"key": "waist_female", "type": "radio", "input": true, "label": "What is your waist circumference? Measure at your belly button after breathing out (do not hold your breath). Note this is <em>not</em> the same as the waist size on pants.", "inline": false, "values": [{"label": "Less than 80cm (31.5 inches)", "value": "small", "shortcut": ""}, {"label": "Between 80-88 cm (31-35 inches)", "value": "medium", "shortcut": ""}, {"label": "Greater than 88 cm (35 inches)", "value": "large", "shortcut": ""}], "tableView": true, "conditional": {"eq": "male", "show": false, "when": "sex"}, "optionsLabelPosition": "right"}, {"key": "activity_less_30_min", "type": "radio", "input": true, "label": "What is your daily activity level (exertion equivalent to a brisk walk or greater)?", "inline": false, "values": [{"label": "30 minutes or more", "value": false, "shortcut": ""}, {"label": "Less than 30 minutes", "value": true, "shortcut": ""}], "tableView": true, "optionsLabelPosition": "right"}, {"key": "no_daily_fruits_vegetables", "type": "radio", "input": true, "label": "Do you eat fruits or vegetables daily?", "inline": false, "values": [{"label": "Yes", "value": false, "shortcut": ""}, {"label": "No", "value": true, "shortcut": ""}], "tableView": true, "optionsLabelPosition": "right"}, {"key": "htn_hx", "type": "radio", "input": true, "label": "Do you have a history of high blood pressure or are on blood pressure pills?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No or don't know", "value": false, "shortcut": ""}], "tableView": true, "optionsLabelPosition": "right"}, {"key": "given_birth_obese_baby", "type": "radio", "input": true, "label": "Have you ever given birth to a baby more than 9 lbs (4.1 kg)?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No/Doesn't Apply", "value": false, "shortcut": ""}], "tableView": true, "conditional": {"eq": "male", "show": false, "when": "sex"}, "optionsLabelPosition": "right"}, {"key": "prior_elevated_sugars", "type": "radio", "input": true, "label": "Have you ever been found to have a high blood sugar either from a blood test, during an illness, or during pregnancy?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No/Don't Know", "value": false, "shortcut": ""}], "tableView": true, "optionsLabelPosition": "right"}, {"key": "educationLevel", "type": "radio", "input": true, "label": "What is your education level?", "inline": false, "values": [{"label": "Some high school or less", "value": "some_high_school", "shortcut": ""}, {"label": "High school diploma", "value": "high_school_diploma", "shortcut": ""}, {"label": "Some college or university", "value": "some_college_university", "shortcut": ""}, {"label": "University or college degree", "value": "college_university_degree", "shortcut": ""}], "tableView": true, "optionsLabelPosition": "right"}, {"key": "columns", "type": "columns", "input": false, "label": "Columns", "columns": [{"pull": 0, "push": 0, "size": "sm", "width": 6, "offset": 0, "components": [{"key": "can_risk_score", "type": "textfield", "input": true, "label": "CANRISK Score", "disabled": true, "tableView": true, "clearOnHide": false, "calculateValue": {"+": [{"if": [{"<=": [45, {"var": "data.age"}, 54]}, 7, 0]}, {"if": [{"<=": [55, {"var": "data.age"}, 64]}, 13, 0]}, {"if": [{"<=": [65, {"var": "data.age"}]}, 15, 0]}, {"if": [{"==": ["male", {"var": "data.sex"}]}, 6, 0]}, {"if": [{"<=": [25, {"var": "data.bmi"}, 29]}, 4, 0]}, {"if": [{"<=": [30, {"var": "data.bmi"}, 34]}, 9, 0]}, {"if": [{"<=": [30, {"var": "data.bmi"}, 34]}, 9, 0]}, {"if": [{"<=": [35, {"var": "data.bmi"}]}, 14, 0]}, {"if": [{"or": [{"==": ["medium", {"var": "data.waist_male"}]}, {"==": ["medium", {"var": "data.waist_female"}]}]}, 4, 0]}, {"if": [{"or": [{"==": ["large", {"var": "data.waist_male"}]}, {"==": ["large", {"var": "data.waist_female"}]}]}, 6, 0]}, {"if": [{"var": "data.activity_less_30_min"}, 1, 0]}, {"if": [{"var": "data.no_daily_fruits_vegetables"}, 2, 0]}, {"if": [{"var": "data.htn_hx"}, 4, 0]}, {"if": [{"var": "data.given_birth_obese_baby"}, 1, 0]}, {"if": [{"var": "data.prior_elevated_sugars"}, 14, 0]}, {"if": [{"var": "data.fm_hx_dm.mother"}, 2, 0]}, {"if": [{"var": "data.fm_hx_dm.father"}, 2, 0]}, {"if": [{"var": "data.fm_hx_dm.sibling"}, 2, 0]}, {"if": [{"var": "data.fm_hx_dm.child"}, 2, 0]}, {"if": [{"==": ["southAsian", {"var": "data.ethnic_group"}]}, 11, {"if": [{"==": ["eastAsian", {"var": "data.ethnic_group"}]}, 10, {"if": [{"==": ["black", {"var": "data.ethnic_group"}]}, 5, {"if": [{"or": [{"==": ["aboriginal", {"var": "data.ethnic_group"}]}, {"==": ["other", {"var": "data.ethnic_group"}]}]}, 3, 0]}]}]}]}, {"if": [{"==": ["some_high_school", {"var": "data.educationLevel"}]}, 5, {"if": [{"==": ["high_school_diploma", {"var": "data.educationLevel"}]}, 1, 0]}]}]}}], "currentWidth": 6}, {"pull": 0, "push": 0, "size": "sm", "width": 6, "offset": 0, "components": [{"key": "canriskLevel", "type": "textfield", "input": true, "label": "CANRISK Risk Level", "disabled": true, "tableView": true, "clearOnHide": false, "calculateValue": {"if": [{"<=": [0, {"var": "data.can_risk_score"}, 32]}, "Low to moderate risk", {"if": [{"<=": [33, {"var": "data.can_risk_score"}, 42]}, "High risk", "Very high risk"]}]}}], "currentWidth": 6}], "tableView": true}], "collapsible": false}, {"key": "recTestFreqMonths_Diabetes", "type": "textfield", "input": true, "label": "recTestFreqMonths_Diabetes", "hidden": true, "disabled": true, "hideLabel": true, "clearOnHide": false, "calculateValue": {"if": [{"!!": {"var": "data.dm_rfList"}}, 6, {"if": [{"==": [{"var": "data.canriskLevel"}, "Very high risk"]}, 6, {"if": [{"==": [{"var": "data.canriskLevel"}, "High risk"]}, 36, {"if": [{">": [{"var": "data.age"}, 40]}, 36, 101]}]}]}]}}], "controller": "", "properties": {}, "submissionAccess": []}