{"components": [{"key": "content8", "html": "<h2 style=\"text-align:center;\"><strong>General Sexual Health Counselling</strong></h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.show_all;"}, {"key": "content1", "html": "<p>This section provides some common sexual health advice that you would normally receive in a doctor's office or sexual health clinic.</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.show_all;"}, {"key": "content15", "html": "<h2>PAP Testing</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.show_all && data.sex == 'female';"}, {"key": "pap_advice", "type": "radio", "input": true, "label": "HPV testing screens for cervical cancer. In Ontario, screening is recommended for everyone with a cervix who has ever been sexually active (including oral sex, use of sex toys, genital touching), starting at age 25 and then every 5 years. If you have questions, please discuss them with your health-care provider.", "inline": false, "values": [{"label": "I understand", "value": true, "shortcut": ""}, {"label": "I do not understand", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.show_all && data.sex == 'female';", "optionsLabelPosition": "right"}, {"key": "content19", "html": "<h3>HPV (Human Papillomavirus)&nbsp;</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.show_all;"}, {"key": "HPV_advice", "type": "radio", "input": true, "label": "HPV (Human Papilloma Virus) is the most common sexually transmitted infection. It can cause genital warts, cervical cancer in women, penile cancer in men and throat cancer with oral sex. HPV vaccination protects against potentially new strains you might be exposed to through new partners, but does not help eliminate HPV from strains that you have already been exposed to. If you have more questions about HPV Vaccination, you can ask your health care provider. ", "inline": false, "values": [{"label": "I understand", "value": true, "shortcut": ""}, {"label": "I do not understand", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.show_all;", "optionsLabelPosition": "right"}, {"key": "content10", "html": "<h3>Use Condoms</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.show_all;"}, {"key": "condom_advice", "type": "radio", "input": true, "label": "Condoms and dental dams reduce the transmission of sexually transmitted infections, and are strongly encouraged with new sexual partners whose sexual status is unknown. Condoms do not fully protect against genital herpes or HPV. ", "inline": false, "values": [{"label": "I understand", "value": true, "shortcut": ""}, {"label": "I do not understand", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "confirm_label": "Condom advice:", "customConditional": "show = data.show_all;", "optionsLabelPosition": "right"}, {"key": "content25", "html": "<h3><PERSON><PERSON> Instructions</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.show_all;"}, {"key": "Sample_Instructions", "type": "radio", "input": true, "label": "If you are providing a urine sample for STI testing please provide the 1st catch (1st part of the urinary stream) after holding your urine for 2 hours and between 20-30ml of urine.", "inline": false, "values": [{"label": "I understand", "value": true, "shortcut": ""}, {"label": "I do not understand", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.show_all;", "optionsLabelPosition": "right"}, {"key": "content45", "html": "<h3>Swabs for Anal Sex and Unprotected Oral Sex</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.show_all && data.route_of_exposure && (data.route_of_exposure.perform_oral || data.route_of_exposure.receive_anal);"}, {"key": "content101", "html": "<h3>Site-Specific Testing</h3><p style=\"margin-left:0.0px;\">Urine testing for gonorrhea, chlamydia and trichomonas only tests for infections in the genital area.&nbsp; Urine sampling does not identify infections in the rectal or oral areas.&nbsp; Swab testing is required to identify infections in these areas.&nbsp; &nbsp;</p><p style=\"margin-left:0.0px;\">Swab testing is only recommended if you have unprotected exposure and have one of the following risk factors: &nbsp;</p><ul><li>Gay, bisexual, and men who have sex with men, including trans women</li><li>Individuals engaged in sex work or have had sexual contact with someone engaging in sex work</li><li>Individuals who are known contacts of those infected with chlamydia or gonorrhea;</li><li>Individuals who have signs or symptoms of rectal or pharyngeal infection (i.e. throat infection, anal discharge)</li></ul>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.show_all;"}, {"key": "Site_Specific_Advice", "type": "radio", "input": true, "label": "Please confirm", "inline": false, "values": [{"label": "I understand site-specific testing and will reach out to a health unit if I would like swab testing", "value": true, "shortcut": ""}, {"label": "I do not understand site-specific testing and would like further guidance ", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.show_all;", "optionsLabelPosition": "right"}, {"key": "content13", "html": "<h3>PrEP (Pre-exposure Prophylaxis)</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.show_all;"}, {"key": "prep_advice", "type": "radio", "input": true, "label": "PrEP (Pre-exposure Prophylaxis) is a once-daily pill that can reduce your risk of acquiring HIV from unprotected sex or injection drug use by up to 99%. TeleTest now offers PrEP — both new starts and renewals of existing prescriptions. You can learn more or request PrEP through our <a href=\"https://teletest.ca/app/care/std/\" target=\"_blank\">STD &amp; HIV PrEP service</a>. A list of individuals who should consider PrEP is provided below. Importantly, anyone who wants to be on PrEP can request it.</br></br><li>Have sex with an HIV-positive partner</li><li>Have sex with one or more individuals of unknown HIV status and inconsistent condom use</li><li>Engage in sex work</li><li>Use injection drugs</li><li>Had an STI in the last 6 months</li>", "inline": false, "values": [{"label": "I understand", "value": true, "shortcut": ""}, {"label": "I do not understand", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "confirm_label": "PrEP advice:", "customConditional": "show = data.show_all;", "optionsLabelPosition": "right"}, {"key": "content14", "html": "<h2>Purpose of Testing</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.show_all;"}, {"key": "Testing_Purpose_Advice", "type": "radio", "input": true, "label": "I am aware the purpose of STI testing is to look for sexually transmitted infections that can be tested for, and some infections are not tested for as part of routine screening (i.e. HPV, herpes, genital warts, molluscum contagiosum, etc).", "inline": false, "values": [{"label": "I understand", "value": true, "shortcut": ""}, {"label": "I do not understand", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.show_all;", "optionsLabelPosition": "right"}, {"key": "has_additional_questions", "type": "radio", "input": true, "label": "Do you have any additional questions for the doctor before testing?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.show_all && data.chief_complaint == 'testing';", "optionsLabelPosition": "right"}, {"key": "additional_questions", "type": "textarea", "input": true, "label": "Please include any questions here:", "tableView": true, "autoExpand": false, "customConditional": "show = data.show_all && data.has_additional_questions;"}]}