{"components": [{"key": "header_lab_testing_abnormalities", "html": "<h1>Complete Blood Count</h1>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "prior_cbc_testing", "type": "radio", "input": true, "label": "Your bloodwork indicated you had some values outside of the normal range. Have you completed a Complete Blood Count (CBC) in the past?", "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "I don't know", "value": "dont_know", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "last_cbc_test", "type": "select", "input": true, "label": "When was your last <strong> Complete Blood Count (CBC)</strong> completed?", "tooltip": "A CBC includes a number of tests including your hemoglobin, hematocrit, and various white cell counts.", "data": {"values": [{"label": "<1 week ago", "value": "less_1_week"}, {"label": "2-4 weeks ago", "value": "2-4_weeks_ago"}, {"label": "4 weeks - 6 weeks ago", "value": "4-6_weeks"}, {"label": "6 weeks - 3 months ago", "value": "6_weeks-3_months"}, {"label": "3-6 months", "value": "3-6_months"}, {"label": "6-12 months", "value": "6-12_months"}, {"label": "12-24 months", "value": "12-24_months"}, {"label": "24+ months", "value": "24+_months"}, {"label": "Never had one", "value": "never_had"}]}, "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.prior_cbc_testing == 'yes'", "optionsLabelPosition": "right"}, {"key": "header_hemoglobin_abnormalities", "html": "<h2>Hemoglobin Count</h2>", "type": "content", "input": false, "label": "Content", "customConditional": "show = data.prior_cbc_testing == 'yes'", "tableView": false, "refreshOnChange": false}, {"key": "prior_abnormal_hemoglobin_count", "type": "radio", "input": true, "label": "Was your last <strong>Hemoblogin(Hb)</strong> count normal or abnormal (i.e above or velow the reference range)?", "values": [{"label": "It was normal", "value": "normal", "shortcut": ""}, {"label": "It was low (below the normal range)", "value": "low", "shortcut": ""}, {"label": "It was high (above the normal range)", "value": "high", "shortcut": ""}, {"label": "I don't know", "value": "doesnt_know", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.prior_cbc_testing == 'yes'", "optionsLabelPosition": "right"}, {"key": "last_hemoglobin_level", "type": "select", "widget": "html5", "customConditional": "show = data.prior_cbc_testing == 'yes' && !!data.prior_abnormal_hemoglobin_count", "input": true, "label": "What was your last <strong>Hemoglobin Count (HB)</strong>? (If known, please select the closest range in g/L)", "data": {"values": [{"label": "I don't know", "value": "doesnt_know"}, {"label": "70 - 80 g/L (7.0 - 8.0 g/dL)", "value": "70_80_g_l"}, {"label": "80 - 90 g/L (8.0 - 9.0 g/dL)", "value": "80_90_g_l"}, {"label": "90 - 100 g/L (9.0 - 10.0 g/dL)", "value": "90_100_g_l"}, {"label": "100 - 110 g/L (10.0 - 11.0 g/dL)", "value": "100_110_g_l"}, {"label": "110 - 120 g/L (11.0 - 12.0 g/dL)", "value": "110_120_g_l"}, {"label": "120 - 130 g/L (12.0 - 13.0 g/dL)", "value": "120_130_g_l"}, {"label": "130 - 140 g/L (13.0 - 14.0 g/dL)", "value": "130_140_g_l"}, {"label": "140 - 150 g/L (14.0 - 15.0 g/dL)", "value": "140_150_g_l"}, {"label": "150 - 170 g/L (15.0 - 17.0 g/dL)", "value": "150_170_g_l"}, {"label": "170 - 190 g/L (17.0 - 19.0 g/dL)", "value": "170_190_g_l"}, {"label": "190 - 210 g/L (19.0 - 21.0 g/dL)", "value": "190_210_g_l"}, {"label": "> 210 g/L (> 21.0 g/dL)", "value": "greater_210_g_l"}]}, "tableView": true}, {"key": "prior_abnormal_hemoglobin_count", "type": "radio", "input": true, "label": "How many times have you had a similar value on previous labwork?", "values": [{"label": "Once", "value": "once", "shortcut": ""}, {"label": "Multiple times", "value": "multiple_times", "shortcut": ""}, {"label": "I don't know", "value": "doesnt_know", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.prior_cbc_testing == 'yes' && data.prior_abnormal_hemoglobin_count != 'normal'", "optionsLabelPosition": "right"}, {"key": "header_neutrophil_abnormalities", "html": "<h2>Neutrophil Count</h2>", "type": "content", "input": false, "label": "Content", "customConditional": "show = data.prior_cbc_testing == 'yes'", "tableView": false, "refreshOnChange": false}, {"key": "prior_abnormal_neutrophil_count", "type": "radio", "input": true, "label": "Was your last <strong>neutrophil</strong> count normal or abnormal (i.e outside of the reference range)?", "values": [{"label": "It was normal", "value": "normal", "shortcut": ""}, {"label": "It was low (below the normal range)", "value": "low", "shortcut": ""}, {"label": "It was high (above the normal range)", "value": "high", "shortcut": ""}, {"label": "I don't know", "value": "doesnt_know", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.prior_cbc_testing == 'yes'", "optionsLabelPosition": "right"}, {"key": "last_neutrophil_level", "type": "select", "widget": "html5", "input": true, "label": "What was your last <strong>Neutrophil Count</strong>? (If known, please select the closest range in g/L)", "data": {"values": [{"label": "I don't know", "value": "doesnt_know"}, {"label": "70 - 80 g/L (7.0 - 8.0 g/dL)", "value": "70_80_g_l"}, {"label": "80 - 90 g/L (8.0 - 9.0 g/dL)", "value": "80_90_g_l"}, {"label": "90 - 100 g/L (9.0 - 10.0 g/dL)", "value": "90_100_g_l"}, {"label": "100 - 110 g/L (10.0 - 11.0 g/dL)", "value": "100_110_g_l"}, {"label": "110 - 120 g/L (11.0 - 12.0 g/dL)", "value": "110_120_g_l"}, {"label": "120 - 130 g/L (12.0 - 13.0 g/dL)", "value": "120_130_g_l"}, {"label": "130 - 140 g/L (13.0 - 14.0 g/dL)", "value": "130_140_g_l"}, {"label": "140 - 150 g/L (14.0 - 15.0 g/dL)", "value": "140_150_g_l"}, {"label": "150 - 170 g/L (15.0 - 17.0 g/dL)", "value": "150_170_g_l"}, {"label": "170 - 190 g/L (17.0 - 19.0 g/dL)", "value": "170_190_g_l"}, {"label": "190 - 210 g/L (19.0 - 21.0 g/dL)", "value": "190_210_g_l"}, {"label": "> 210 g/L (> 21.0 g/dL)", "value": "greater_210_g_l"}]}, "tableView": true}, {"key": "prior_abnormal_neutrophil_count", "type": "radio", "input": true, "label": "How many times have you had a similar value on previous labwork?", "values": [{"label": "Once", "value": "once", "shortcut": ""}, {"label": "Multiple times", "value": "multiple_times", "shortcut": ""}, {"label": "I don't know", "value": "doesnt_know", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.prior_cbc_testing == 'yes' && data.prior_abnormal_neutrophil_count != 'normal'", "optionsLabelPosition": "right"}, {"key": "header_lymphocyte_abnormalities", "html": "<h2>Lymphocyte Count</h2>", "type": "content", "input": false, "label": "Content", "customConditional": "show = data.prior_cbc_testing == 'yes'", "tableView": false, "refreshOnChange": false}, {"key": "prior_abnormal_lymphocyte_count", "type": "radio", "input": true, "label": "Was your last <strong>Lymphocyte</strong> count normal or abnormal (i.e outside of the reference range)?", "values": [{"label": "It was normal", "value": "normal", "shortcut": ""}, {"label": "It was low (below the normal range)", "value": "low", "shortcut": ""}, {"label": "It was high (above the normal range)", "value": "high", "shortcut": ""}, {"label": "I don't know", "value": "doesnt_know", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.prior_cbc_testing == 'yes'", "optionsLabelPosition": "right"}, {"key": "last_lymphocyte_level", "type": "select", "widget": "html5", "input": true, "label": "What was your last Lymphocyte Count? (If known, please select the closest range in x10^9/L)", "data": {"values": [{"label": "I don't know", "value": "doesnt_know"}, {"label": "< 1.0 x10^9/L", "value": "less_1_0"}, {"label": "1.0 - 1.5 x10^9/L", "value": "1_0_1_5"}, {"label": "1.5 - 2.0 x10^9/L", "value": "1_5_2_0"}, {"label": "2.0 - 2.5 x10^9/L", "value": "2_0_2_5"}, {"label": "2.5 - 3.0 x10^9/L", "value": "2_5_3_0"}, {"label": "3.0 - 3.5 x10^9/L", "value": "3_0_3_5"}, {"label": "3.5 - 4.0 x10^9/L", "value": "3_5_4_0"}, {"label": "4.0 - 4.5 x10^9/L", "value": "4_0_4_5"}, {"label": "4.5 - 5.0 x10^9/L", "value": "4_5_5_0"}, {"label": "> 5.0 x10^9/L", "value": "greater_5_0"}]}, "tableView": true}, {"key": "prior_abnormal_lymphocyte_count", "type": "radio", "input": true, "label": "How many times have you had a similar value on previous labwork?", "values": [{"label": "Once", "value": "once", "shortcut": ""}, {"label": "Multiple times", "value": "multiple_times", "shortcut": ""}, {"label": "I don't know", "value": "doesnt_know", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.prior_cbc_testing == 'yes' && data.prior_abnormal_lymphocyte_count != 'normal'", "optionsLabelPosition": "right"}, {"key": "header_monocyte_abnormalities", "html": "<h2>Monocyte Count</h2>", "type": "content", "input": false, "label": "Content", "customConditional": "show = data.prior_cbc_testing == 'yes'", "tableView": false, "refreshOnChange": false}, {"key": "prior_abnormal_monocyte_count", "type": "radio", "input": true, "label": "Was your last <strong>Monocyte</strong> count normal or abnormal (i.e outside of the reference range)?", "values": [{"label": "It was normal", "value": "normal", "shortcut": ""}, {"label": "It was low (below the normal range)", "value": "low", "shortcut": ""}, {"label": "It was high (above the normal range)", "value": "high", "shortcut": ""}, {"label": "I don't know", "value": "doesnt_know", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.prior_cbc_testing == 'yes'", "optionsLabelPosition": "right"}, {"key": "last_monocyte_level", "type": "select", "widget": "html5", "input": true, "label": "What was your last Monocyte Count? (If known, please select the closest range in x10^9/L)", "data": {"values": [{"label": "I don't know", "value": "doesnt_know"}, {"label": "< 0.2 x10^9/L", "value": "less_0_2"}, {"label": "0.2 - 0.4 x10^9/L", "value": "0_2_0_4"}, {"label": "0.4 - 0.6 x10^9/L", "value": "0_4_0_6"}, {"label": "0.6 - 0.8 x10^9/L", "value": "0_6_0_8"}, {"label": "0.8 - 1.0 x10^9/L", "value": "0_8_1_0"}, {"label": "1.0 - 1.2 x10^9/L", "value": "1_0_1_2"}, {"label": "1.2 - 1.4 x10^9/L", "value": "1_2_1_4"}, {"label": "1.4 - 1.6 x10^9/L", "value": "1_4_1_6"}, {"label": "1.6 - 1.8 x10^9/L", "value": "1_6_1_8"}, {"label": "1.8 - 2.0 x10^9/L", "value": "1_8_2_0"}, {"label": "> 2.0 x10^9/L", "value": "greater_2_0"}]}, "tableView": true}, {"key": "prior_abnormal_monocyte_count", "type": "radio", "input": true, "label": "How many times have you had a similar value on previous labwork?", "values": [{"label": "Once", "value": "once", "shortcut": ""}, {"label": "Multiple times", "value": "multiple_times", "shortcut": ""}, {"label": "I don't know", "value": "doesnt_know", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.prior_cbc_testing == 'yes' && data.prior_abnormal_monocyte_count != 'normal'", "optionsLabelPosition": "right"}, {"key": "header_basophil_abnormalities", "html": "<h2>Basophil Count</h2>", "type": "content", "input": false, "label": "Content", "customConditional": "show = data.prior_cbc_testing == 'yes'", "tableView": false, "refreshOnChange": false}, {"key": "prior_abnormal_basophil_count", "type": "radio", "input": true, "label": "Was your last <strong>Basophil</strong> count normal or abnormal (i.e outside of the reference range)?", "values": [{"label": "It was normal", "value": "normal", "shortcut": ""}, {"label": "It was low (below the normal range)", "value": "low", "shortcut": ""}, {"label": "It was high (above the normal range)", "value": "high", "shortcut": ""}, {"label": "I don't know", "value": "doesnt_know", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.prior_cbc_testing == 'yes'", "optionsLabelPosition": "right"}, {"key": "last_basophil_level", "type": "select", "widget": "html5", "input": true, "label": "What was your last <strong>Basophil Count</strong>? (If known, please select the closest range in g/L)", "data": {"values": [{"label": "I don't know", "value": "doesnt_know"}, {"label": "70 - 80 g/L (7.0 - 8.0 g/dL)", "value": "70_80_g_l"}, {"label": "80 - 90 g/L (8.0 - 9.0 g/dL)", "value": "80_90_g_l"}, {"label": "90 - 100 g/L (9.0 - 10.0 g/dL)", "value": "90_100_g_l"}, {"label": "100 - 110 g/L (10.0 - 11.0 g/dL)", "value": "100_110_g_l"}, {"label": "110 - 120 g/L (11.0 - 12.0 g/dL)", "value": "110_120_g_l"}, {"label": "120 - 130 g/L (12.0 - 13.0 g/dL)", "value": "120_130_g_l"}, {"label": "130 - 140 g/L (13.0 - 14.0 g/dL)", "value": "130_140_g_l"}, {"label": "140 - 150 g/L (14.0 - 15.0 g/dL)", "value": "140_150_g_l"}, {"label": "150 - 170 g/L (15.0 - 17.0 g/dL)", "value": "150_170_g_l"}, {"label": "170 - 190 g/L (17.0 - 19.0 g/dL)", "value": "170_190_g_l"}, {"label": "190 - 210 g/L (19.0 - 21.0 g/dL)", "value": "190_210_g_l"}, {"label": "> 210 g/L (> 21.0 g/dL)", "value": "greater_210_g_l"}]}, "tableView": true}, {"key": "prior_abnormal_basophil_count", "type": "radio", "input": true, "label": "How many times have you had a similar value on previous labwork?", "values": [{"label": "Once", "value": "once", "shortcut": ""}, {"label": "Multiple times", "value": "multiple_times", "shortcut": ""}, {"label": "I don't know", "value": "doesnt_know", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.prior_cbc_testing == 'yes' && data.prior_abnormal_basophil_count != 'normal'", "optionsLabelPosition": "right"}, {"key": "header_platelet_abnormalities", "html": "<h2>Platelet Count</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "prior_abnormal_platelet_count", "type": "radio", "input": true, "label": "Was your last <strong>Platelet</strong> count normal or abnormal (i.e outside of the reference range)?", "values": [{"label": "It was normal", "value": "normal", "shortcut": ""}, {"label": "It was low (below the normal range)", "value": "low", "shortcut": ""}, {"label": "It was high (above the normal range)", "value": "high", "shortcut": ""}, {"label": "I don't know", "value": "doesnt_know", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.prior_cbc_testing == 'yes'", "optionsLabelPosition": "right"}, {"key": "last_platelet_level", "type": "select", "widget": "html5", "input": true, "label": "What was your last <strong>Platelet Count</strong>? (If known, please select the closest range in g/L)", "data": {"values": [{"label": "I don't know", "value": "doesnt_know"}, {"label": "70 - 80 g/L (7.0 - 8.0 g/dL)", "value": "70_80_g_l"}, {"label": "80 - 90 g/L (8.0 - 9.0 g/dL)", "value": "80_90_g_l"}, {"label": "90 - 100 g/L (9.0 - 10.0 g/dL)", "value": "90_100_g_l"}, {"label": "100 - 110 g/L (10.0 - 11.0 g/dL)", "value": "100_110_g_l"}, {"label": "110 - 120 g/L (11.0 - 12.0 g/dL)", "value": "110_120_g_l"}, {"label": "120 - 130 g/L (12.0 - 13.0 g/dL)", "value": "120_130_g_l"}, {"label": "130 - 140 g/L (13.0 - 14.0 g/dL)", "value": "130_140_g_l"}, {"label": "140 - 150 g/L (14.0 - 15.0 g/dL)", "value": "140_150_g_l"}, {"label": "150 - 170 g/L (15.0 - 17.0 g/dL)", "value": "150_170_g_l"}, {"label": "170 - 190 g/L (17.0 - 19.0 g/dL)", "value": "170_190_g_l"}, {"label": "190 - 210 g/L (19.0 - 21.0 g/dL)", "value": "190_210_g_l"}, {"label": "> 210 g/L (> 21.0 g/dL)", "value": "greater_210_g_l"}]}, "tableView": true}, {"key": "prior_abnormal_platelet_count", "type": "radio", "input": true, "label": "How many times have you had a similar value on previous labwork?", "values": [{"label": "Once", "value": "once", "shortcut": ""}, {"label": "Multiple times", "value": "multiple_times", "shortcut": ""}, {"label": "I don't know", "value": "doesnt_know", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.prior_cbc_testing == 'yes' && data.prior_abnormal_platelet_count != 'normal'", "optionsLabelPosition": "right"}, {"key": "header_mcv_abnormalities", "html": "<h2>Mean Corpuscul Volume (MCV)</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "prior_abnormal_mcv_count", "type": "radio", "input": true, "label": "Was your last <strong>MCV</strong> resuult normal or abnormal (i.e outside of the reference range)?", "values": [{"label": "It was normal", "value": "normal", "shortcut": ""}, {"label": "It was low (below the normal range)", "value": "low", "shortcut": ""}, {"label": "It was high (above the normal range)", "value": "high", "shortcut": ""}, {"label": "I don't know", "value": "doesnt_know", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.prior_cbc_testing == 'yes'", "optionsLabelPosition": "right"}, {"key": "last_platelet_level", "type": "select", "widget": "html5", "input": true, "label": "What was your last <strong>MCV</strong>? (If known, please select the closest range in g/L)", "data": {"values": [{"label": "I don't know", "value": "doesnt_know"}, {"label": "70 - 80 g/L (7.0 - 8.0 g/dL)", "value": "70_80_g_l"}, {"label": "80 - 90 g/L (8.0 - 9.0 g/dL)", "value": "80_90_g_l"}, {"label": "90 - 100 g/L (9.0 - 10.0 g/dL)", "value": "90_100_g_l"}, {"label": "100 - 110 g/L (10.0 - 11.0 g/dL)", "value": "100_110_g_l"}, {"label": "110 - 120 g/L (11.0 - 12.0 g/dL)", "value": "110_120_g_l"}, {"label": "120 - 130 g/L (12.0 - 13.0 g/dL)", "value": "120_130_g_l"}, {"label": "130 - 140 g/L (13.0 - 14.0 g/dL)", "value": "130_140_g_l"}, {"label": "140 - 150 g/L (14.0 - 15.0 g/dL)", "value": "140_150_g_l"}, {"label": "150 - 170 g/L (15.0 - 17.0 g/dL)", "value": "150_170_g_l"}, {"label": "170 - 190 g/L (17.0 - 19.0 g/dL)", "value": "170_190_g_l"}, {"label": "190 - 210 g/L (19.0 - 21.0 g/dL)", "value": "190_210_g_l"}, {"label": "> 210 g/L (> 21.0 g/dL)", "value": "greater_210_g_l"}]}, "tableView": true}, {"key": "prior_abnormal_mcv_count", "type": "radio", "input": true, "label": "How many times have you had a similar value on previous labwork?", "values": [{"label": "Once", "value": "once", "shortcut": ""}, {"label": "Multiple times", "value": "multiple_times", "shortcut": ""}, {"label": "I don't know", "value": "doesnt_know", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.prior_cbc_testing == 'yes' && data.prior_abnormal_mcv_count != 'normal'", "optionsLabelPosition": "right"}, {"key": "inherited_disorders", "type": "selectboxes", "input": true, "label": "Have you been diagnosed with any of the following medical conditions:", "values": [{"label": "Thalassemia", "value": "thalassemia", "shortcut": ""}, {"label": "Sickle Cell Disease", "value": "sickle_cell_disease", "shortcut": ""}, {"label": "Iron deficiency", "value": "iron_deficiency", "shortcut": ""}], "tableView": true, "optionsLabelPosition": "right"}, {"key": "no_mcv_disorders", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a disorder."}, "validate": {"custom": "valid = !!data.no_mcv_disorders || !!_.some(_.values(data.mcv_disorders));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "header_identifiable_triggers", "html": "<h3>Identifiable Triggers</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "recent_health_conditions", "type": "selectboxes", "input": true, "label": "Have you had any of the following medical conditions within the last 4 weeks?", "values": [{"label": "Colds, sinus infections or bronchitis/pneumonia", "value": "colds", "shortcut": ""}, {"label": "Urinary Tract Infections (UTIs)", "value": "utis", "shortcut": ""}, {"label": "Skin Infections", "value": "skin_infections", "shortcut": ""}, {"label": "Significant Acne Outbreaks", "value": "acne_outbreaks", "shortcut": ""}, {"label": "Surgery", "value": "surgery", "shortcut": ""}], "tableView": true, "optionsLabelPosition": "right"}, {"key": "no_recent_health_conditions", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a condition."}, "validate": {"custom": "valid = !!data.no_recent_health_conditions || !!_.some(_.values(data.recent_health_conditions));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "header_environmental_triggers", "html": "<h4>Environmental Triggers</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "allergies_or_eosinophilia_conditions", "type": "selectboxes", "input": true, "label": "Do you have any of the following diagnosed medical conditions:", "values": [{"label": "Asthma", "value": "asthma", "shortcut": ""}, {"label": "<PERSON><PERSON><PERSON>", "value": "eczema", "shortcut": ""}, {"label": "Seasonal Allergies", "value": "seasonal_allergies", "shortcut": ""}], "tableView": true, "optionsLabelPosition": "right"}, {"key": "no_allergies_or_eosinophilia_conditions", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a condition."}, "validate": {"custom": "valid = !!data.no_allergies_or_eosinophilia_conditions || !!_.some(_.values(data.allergies_or_eosinophilia_conditions));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "header_medication_triggers", "html": "<h4>Medication Triggers</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "new_medications_last_12_weeks", "type": "radio", "input": true, "label": "Have you started on any new medications within the last 12 weeks?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "header_dietary_triggers", "html": "<h4>Dietary Triggers</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "dietary_restrictions", "type": "select", "input": true, "label": "Are you a vegetarian or vegan?", "values": [{"label": "Vegetarian", "value": "vegetarian"}, {"label": "Vegan", "value": "vegan"}, {"label": "Neither", "value": "neither"}], "adminFlag": true, "tableView": true, "widget": "html5", "validate": {"required": true}}, {"key": "header_condition_triggers", "html": "<h4>Previous Medical Conditions</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "header_associated_symptoms", "html": "<h3>Current or Recent Symptoms</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "symptoms_low_platelets", "type": "selectboxes", "input": true, "label": "Have you had any of the following symptoms?", "values": [{"label": "Bruise more easily than usual", "value": "bruising", "shortcut": ""}, {"label": "Bleed more than usual from small cuts", "value": "prolonged_bleeding", "shortcut": ""}, {"label": "Bleeding from my gums", "value": "spontaneous_bleeding", "shortcut": ""}, {"label": "Heavy periods", "value": "heavy_menstrual_flow", "customConditional": "show = data.sex == 'female'", "shortcut": ""}, {"label": "Blood in your urine", "value": "hematuria", "shortcut": ""}, {"label": "Blood in your my stool (i.e. poo)", "value": "hematochezia", "shortcut": ""}], "tableView": true, "optionsLabelPosition": "right"}, {"key": "no_symptoms_low_platelets", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = !!data.no_symptoms_low_platelets || !!_.some(_.values(data.symptoms_low_platelets));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "b_symptoms", "type": "selectboxes", "input": true, "label": "Have you had any of the following symptoms?", "values": [{"label": "Unexplained weight loss (i.e. losing weight without exercising or dieting)", "value": "weight_loss", "shortcut": ""}, {"label": "Fevers without an infection (i.e. a fever without cold symptoms)", "value": "fever", "shortcut": ""}, {"label": "Night sweats (i.e. soaking the bed in sweat)", "value": "night_sweats", "shortcut": ""}, {"label": "Loss of appetite", "value": "loss_of_appetite", "shortcut": ""}], "tableView": true, "optionsLabelPosition": "right"}, {"key": "no_b_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = !!data.no_b_symptoms || !!_.some(_.values(data.b_symptoms));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "header_family_history", "html": "<h3>Family History</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "ethnicity_thalassemia_prevalence", "type": "radio", "input": true, "label": "Please select your ethnicity, or if mixed race, if one of your parents is from any of the following regions where thalassemia is common:", "values": [{"label": "Mediterranean (e.g., Italy, Greece)", "value": "mediterranean"}, {"label": "Middle East (e.g., Saudi Arabia, Iran)", "value": "middle_east"}, {"label": "South Asia (e.g., India, Pakistan)", "value": "south_asia"}, {"label": "Southeast Asia (e.g., Thailand, Malaysia)", "value": "southeast_asia"}, {"label": "Africa", "value": "africa"}, {"label": "None of the above", "value": "none"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "prior_abnormal_mcv_count", "type": "radio", "input": true, "label": "How many times have you had a similar value on previous labwork?", "values": [{"label": "Once", "value": "once", "shortcut": ""}, {"label": "Multiple times", "value": "multiple_times", "shortcut": ""}, {"label": "I don't know", "value": "doesnt_know", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.prior_cbc_testing == 'yes' && data.prior_abnormal_mcv_count != 'normal'", "optionsLabelPosition": "right"}, {"key": "header_recommendations", "html": "<h3>Medical Recommendations</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "header_prior_lab_tesing", "html": "<h1>Prior Lab testing</h1>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "hx_neutropenia", "type": "radio", "input": true, "label": "Have you been told you have had low neutrophils in the past?", "inline": false, "values": [{"label": "No", "value": false, "shortcut": ""}, {"label": "Yes/Don't Know", "value": true, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "anemia_in_person", "type": "selectboxes", "input": true, "label": "Are you experiencing any of the following symptoms:", "values": [{"label": "Chest pain or pressure", "value": "chest_pain", "shortcut": ""}, {"label": "Feel lightheaded or faint", "value": "presy<PERSON><PERSON>", "shortcut": ""}, {"label": "Palpitations (i.e. rapid, slow or irregular heart beat)", "value": "palpitations", "shortcut": ""}, {"label": "Shortness of breath or difficulty breathing", "value": "dyspnea", "shortcut": ""}, {"label": "Feel unwell", "value": "malaise", "shortcut": ""}, {"label": "Rectal bleeding (i.e. blood in stool)", "value": "rectal_bleeding", "shortcut": ""}], "inputType": "checkbox", "tableView": true, "customConditional": "show = data.hx_anemia == true;", "optionsLabelPosition": "right"}]}