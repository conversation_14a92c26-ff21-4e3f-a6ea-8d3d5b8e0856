{"components": [{"key": "chol_preferences_header", "type": "content", "input": false, "label": "Content", "html": "<h2>Cholesterol — Testing or Medication</h2><p>Please choose how you’d like to proceed.</p>", "tableView": false, "refreshOnChange": false}, {"key": "chol_plan_choice", "type": "radio", "input": true, "label": "Which option best suits you?", "confirm_label": "Cholesterol plan:", "values": [{"label": "Testing only", "value": "testing_only"}, {"label": "Treatment only (no testing right now)", "value": "treatment_only"}, {"label": "Testing and treatment now", "value": "testing_and_treatment_now"}, {"label": "Testing now; treatment only if results show it", "value": "testing_if_abnormal"}], "validate": {"required": true}, "tableView": true}, {"key": "chol_renewal_header", "type": "content", "input": false, "label": "Content", "html": "<h3>Select medication(s)</h3><p>Choose all that apply, then pick your dose for each medication.</p>", "tableView": false, "customConditional": "show = (data.chol_plan_choice === 'treatment_only' || data.chol_plan_choice === 'testing_and_treatment_now');"}, {"key": "chol_med_select", "type": "selectboxes", "input": true, "label": "Medication(s):", "tableView": true, "values": [{"label": "Atorvas<PERSON><PERSON>", "value": "atorvastatin", "shortcut": ""}, {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "ros<PERSON><PERSON><PERSON><PERSON>", "shortcut": ""}, {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "simvas<PERSON>in", "shortcut": ""}, {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "pravastatin", "shortcut": ""}, {"label": "Ezetimibe", "value": "ezetimibe", "shortcut": ""}, {"label": "Other (please specify)", "value": "other", "shortcut": ""}], "validate": {"required": true}, "customConditional": "show = (data.chol_plan_choice === 'treatment_only' || data.chol_plan_choice === 'testing_and_treatment_now');", "refreshOnChange": true}, {"key": "chol_other_med", "type": "textfield", "input": true, "label": "Other medication name and dose (e.g., bempedoic acid 180 mg)", "tableView": true, "customConditional": "show = data.chol_med_select && data.chol_med_select.other === true;", "refreshOnChange": true}, {"key": "atorvastatin_dose", "type": "select", "widget": "html5", "input": true, "label": "Atorvastatin dose", "tableView": true, "data": {"values": [{"label": "10 mg", "value": "10mg"}, {"label": "20 mg", "value": "20mg"}, {"label": "40 mg", "value": "40mg"}, {"label": "80 mg", "value": "80mg"}]}, "validate": {"required": true}, "customConditional": "show = data.chol_med_select && data.chol_med_select.atorvastatin === true;", "refreshOnChange": true}, {"key": "rosuvastatin_dose", "type": "select", "widget": "html5", "input": true, "label": "Rosuvastatin dose", "tableView": true, "data": {"values": [{"label": "5 mg", "value": "5mg"}, {"label": "10 mg", "value": "10mg"}, {"label": "20 mg", "value": "20mg"}, {"label": "40 mg", "value": "40mg"}]}, "validate": {"required": true}, "customConditional": "show = data.chol_med_select && data.chol_med_select.rosuvastatin === true;", "refreshOnChange": true}, {"key": "simvastatin_dose", "type": "select", "widget": "html5", "input": true, "label": "Simvastatin dose", "tableView": true, "data": {"values": [{"label": "10 mg", "value": "10mg"}, {"label": "20 mg", "value": "20mg"}, {"label": "40 mg", "value": "40mg"}]}, "validate": {"required": true}, "customConditional": "show = data.chol_med_select && data.chol_med_select.simvastatin === true;", "refreshOnChange": true}, {"key": "pravastatin_dose", "type": "select", "widget": "html5", "input": true, "label": "Pravastatin dose", "tableView": true, "data": {"values": [{"label": "10 mg", "value": "10mg"}, {"label": "20 mg", "value": "20mg"}, {"label": "40 mg", "value": "40mg"}, {"label": "80 mg", "value": "80mg"}]}, "validate": {"required": true}, "customConditional": "show = data.chol_med_select && data.chol_med_select.pravastatin === true;", "refreshOnChange": true}, {"key": "ezetimibe_dose", "type": "select", "widget": "html5", "input": true, "label": "Ezetimibe dose", "tableView": true, "data": {"values": [{"label": "10 mg", "value": "10mg"}]}, "validate": {"required": true}, "customConditional": "show = data.chol_med_select && data.chol_med_select.ezetimibe === true;", "refreshOnChange": true}, {"key": "rxts", "type": "textfield", "input": true, "label": "Rx Templates:", "hidden": true, "disabled": true, "multiple": true, "adminFlag": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "var out = []; var plan = data.chol_plan_choice; if (plan === 'treatment_only' || plan === 'testing_and_treatment_now') { if (data.chol_med_select && data.chol_med_select.atorvastatin && data.atorvastatin_dose) { out.push('atorvastatin-' + data.atorvastatin_dose); } if (data.chol_med_select && data.chol_med_select.rosuvastatin && data.rosuvastatin_dose) { out.push('rosuvastatin-' + data.rosuvastatin_dose); } if (data.chol_med_select && data.chol_med_select.simvastatin && data.simvastatin_dose) { out.push('simvastatin-' + data.simvastatin_dose); } if (data.chol_med_select && data.chol_med_select.pravastatin && data.pravastatin_dose) { out.push('pravastatin-' + data.pravastatin_dose); } if (data.chol_med_select && data.chol_med_select.ezetimibe && data.ezetimibe_dose) { out.push('ezetimibe-' + data.ezetimibe_dose); } if (data.chol_med_select && data.chol_med_select.other && data.chol_other_med) { out.push(data.chol_other_med); } } value = out;", "refreshOnChange": true}, {"key": "medication_safety_header", "type": "content", "input": false, "label": "Medication Safety", "html": "<h3>Medication Safety</h3>", "tableView": false, "refreshOnChange": false, "customConditional": "show = (data.chol_plan_choice === 'treatment_only' || data.chol_plan_choice === 'testing_and_treatment_now');"}, {"key": "chol_contraindications", "type": "selectboxes", "input": true, "label": "Do any of the following apply to you? (Select all that apply)", "values": [{"label": "Pregnant, trying to get pregnant, or unsure if pregnant", "value": "pregnancy", "customConditional": "show = data.sex==='female';"}, {"label": "Breastfeeding", "value": "breastfeeding", "customConditional": "show = data.sex==='female';"}, {"label": "Liver disease", "value": "liver_disease"}, {"label": "Cirr<PERSON>is", "value": "cirrhosis"}, {"label": "Liver enzymes that are higher than normal", "value": "elevated_liver_enzymes"}, {"label": "Past severe muscle injury from a statin (rhabdomyolysis or hospital stay)", "value": "severe_statin_reaction"}], "adminFlag": true, "confirm_label": "Possible contraindications/cautions:", "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = (data.chol_plan_choice === 'treatment_only' || data.chol_plan_choice === 'testing_and_treatment_now');", "refreshOnChange": true, "calculateValue": "var v = value || {}; if (data.sex !== 'female') { v.pregnancy = false; v.breastfeeding = false; } value = v;"}, {"key": "chol_interaction_meds_text", "type": "textfield", "input": true, "label": "List interacting medicines (if known):", "tableView": true, "customConditional": "show = (data.chol_plan_choice === 'treatment_only' || data.chol_plan_choice === 'testing_and_treatment_now') && data.chol_contraindications && data.chol_contraindications.major_interactions === true;"}, {"key": "none_of_the_above_chol_contraindications", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a contraindication/caution."}, "validate": {"custom": "valid = !!data.none_of_the_above_chol_contraindications || _.some(_.values(data.chol_contraindications || {}));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = (data.chol_plan_choice === 'treatment_only' || data.chol_plan_choice === 'testing_and_treatment_now');"}, {"key": "side_effects_header", "type": "content", "input": false, "label": "Side Effects", "html": "<h3>Side Effects</h3>", "tableView": false, "refreshOnChange": false, "customConditional": "show = (data.chol_plan_choice === 'treatment_only' || data.chol_plan_choice === 'testing_and_treatment_now');"}, {"key": "side_effects_statins", "type": "content", "input": false, "html": "<p><strong>Statins</strong> (atorvastatin, rosuvastatin, simvastatin, pravastatin) can cause side effects. Most improve with a dose change or by switching medicines.</p><ul><li>Muscle aches or stiffness</li><li>Headache</li><li>Upset stomach</li><li>Abnormal liver tests</li><li>Severe muscle injury (very weak, dark urine) — <em>rare</em>; seek care immediately</li><li>Severe allergic reaction (hives, swelling, trouble breathing) — <em>rare</em>; seek care immediately</li></ul>", "customConditional": "show = (data.chol_plan_choice === 'treatment_only' || data.chol_plan_choice === 'testing_and_treatment_now') && data.chol_med_select && (data.chol_med_select.atorvastatin || data.chol_med_select.rosuvastatin || data.chol_med_select.simvastatin || data.chol_med_select.pravastatin);"}, {"key": "side_effects_statins_ack", "type": "radio", "input": true, "label": "I have reviewed the statin side-effects above:", "values": [{"label": "✔ I understand these risks and wish to proceed", "value": "understand_proceed"}, {"label": "✖ I'm unsure / need more counselling before proceeding", "value": "need_counselling"}], "validate": {"required": true}, "tableView": true, "customClass": "mt-2", "optionsLabelPosition": "right", "confirm_label": "Statin side-effects acknowledgement:", "customConditional": "show = (data.chol_plan_choice === 'treatment_only' || data.chol_plan_choice === 'testing_and_treatment_now') && data.chol_med_select && (data.chol_med_select.atorvastatin || data.chol_med_select.rosuvastatin || data.chol_med_select.simvastatin || data.chol_med_select.pravastatin);"}, {"key": "side_effects_ezetimibe", "type": "content", "input": false, "html": "<p><strong>Ezetimibe</strong> is usually well-tolerated. Possible effects include:</p><ul><li>Upset stomach or diarrhea</li><li>Muscle aches</li><li>Abnormal liver tests (more likely if taken with a statin)</li><li>Severe allergic reaction (hives, swelling, trouble breathing) — <em>rare</em>; seek care immediately</li></ul>", "customConditional": "show = (data.chol_plan_choice === 'treatment_only' || data.chol_plan_choice === 'testing_and_treatment_now') && data.chol_med_select && data.chol_med_select.ezetimibe === true;"}, {"key": "side_effects_ezetimibe_ack", "type": "radio", "input": true, "label": "I have reviewed the ezetimibe side-effects above:", "values": [{"label": "✔ I understand these risks and wish to proceed", "value": "understand_proceed"}, {"label": "✖ I'm unsure / need more counselling before proceeding", "value": "need_counselling"}], "validate": {"required": true}, "tableView": true, "customClass": "mt-2", "optionsLabelPosition": "right", "confirm_label": "Ezetimibe side-effects acknowledgement:", "customConditional": "show = (data.chol_plan_choice === 'treatment_only' || data.chol_plan_choice === 'testing_and_treatment_now') && data.chol_med_select && data.chol_med_select.ezetimibe === true;"}, {"key": "confirmations_header", "type": "content", "input": false, "label": "Confirmations", "html": "<h3>Confirmations</h3>", "tableView": false, "refreshOnChange": false, "customConditional": "show = (data.chol_plan_choice === 'treatment_only' || data.chol_plan_choice === 'testing_and_treatment_now');"}, {"key": "med_info_accuracy_ack", "type": "checkbox", "input": true, "label": "I confirm my medical conditions, current medicines, and drug allergies are complete and accurate. I understand TeleTest clinicians will rely on this information when making prescribing recommendations.", "validate": {"required": true}, "errors": {"required": "Please confirm the accuracy of your medical information before continuing."}, "tableView": true, "confirm_label": "My information is complete and accurate:", "defaultValue": false, "customClass": "mt-2", "customConditional": "show = (data.chol_plan_choice === 'treatment_only' || data.chol_plan_choice === 'testing_and_treatment_now');"}, {"key": "proceed_with_chol_treatment", "type": "checkbox", "confirm_label": "I wish to proceed with cholesterol treatment now:", "input": true, "label": "I wish to proceed with the cholesterol medicine(s) selected above.", "errors": {"required": "Please confirm you wish to proceed before continuing."}, "validate": {"required": true}, "tableView": true, "defaultValue": false, "customConditional": "show = (data.chol_plan_choice === 'treatment_only' || data.chol_plan_choice === 'testing_and_treatment_now');"}, {"key": "has_additional_questions", "type": "radio", "input": true, "label": "Do you have any additional questions before we proceed?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "additional_questions", "type": "textarea", "input": true, "label": "Please include any questions here:", "tableView": true, "autoExpand": false, "customConditional": "show = data.has_additional_questions === true;"}]}