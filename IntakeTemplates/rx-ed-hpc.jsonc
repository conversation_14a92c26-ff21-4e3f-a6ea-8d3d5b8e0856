{"components": [{"key": "heading_ed_medication", "html": "<h1><center><strong>Erectile Dysfunction Medication</strong></center></h1><p>To assist in providing the best care, please respond to the following questions regarding your health and medical history related to erectile dysfunction.</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "header_hpi", "html": "<h2>Medical History</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "previous_ed_diagnosis", "type": "radio", "input": true, "label": "Have you been previously diagnosed with erectile dysfunction?", "values": [{"label": "Yes", "value": true}, {"label": "No", "value": false}, {"label": "I don't know", "value": "dont_know"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Previous ED diagnosis:", "optionsLabelPosition": "right"}, {"key": "ed_diagnosis_provider", "type": "selectboxes", "input": true, "label": "Who diagnosed you with erectile dysfunction? (Select all that apply)", "values": [{"label": "Family physician / primary-care provider", "value": "family_physician"}, {"label": "Urologist (specialist in the urinary tract and male reproductive system)", "value": "urologist"}, {"label": "Endocrinologist", "value": "endocrinologist"}, {"label": "Nurse practitioner", "value": "nurse_practitioner"}, {"label": "Self-diagnosed", "value": "self_diagnosis"}, {"label": "Other", "value": "other"}], "adminFlag": true, "confirm_label": "Diagnosing provider(s):", "tableView": true, "optionsLabelPosition": "right", "validate": {"required": true}, "customConditional": "show = data.previous_ed_diagnosis === true"}, {"key": "ed_diagnosis_provider_other", "type": "textfield", "input": true, "label": "Please specify the other diagnosing provider:", "tableView": true, "customConditional": "show = data.ed_diagnosis_provider && data.ed_diagnosis_provider.other"}, {"key": "ed_diagnosis_timing", "type": "select", "input": true, "label": "How long ago were you first diagnosed with erectile dysfunction?", "data": {"values": [{"label": "Less than 1 month ago", "value": "lt_1_month"}, {"label": "1 – 3 months ago", "value": "1_3_months"}, {"label": "3 – 6 months ago", "value": "3_6_months"}, {"label": "6 – 9 months ago", "value": "6_9_months"}, {"label": "9 – 12 months ago", "value": "9_12_months"}, {"label": "1 – 2 years ago", "value": "1_2_years"}, {"label": "2 – 3 years ago", "value": "2_3_years"}, {"label": "3 – 5 years ago", "value": "3_5_years"}, {"label": "5 – 10 years ago", "value": "5_10_years"}, {"label": "More than 10 years ago", "value": "gt_10_years"}]}, "adminFlag": true, "confirm_label": "Time since diagnosis:", "tableView": true, "widget": "html5", "validate": {"required": true}, "optionsLabelPosition": "right", "customConditional": "show = data.ed_diagnosis_provider && (data.ed_diagnosis_provider.family_physician || data.ed_diagnosis_provider.urologist || data.ed_diagnosis_provider.endocrinologist || data.ed_diagnosis_provider.nurse_practitioner || data.ed_diagnosis_provider.other);"}, {"key": "header_symptom_overview", "html": "<h2>Symptoms</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "ed_symptoms", "type": "selectboxes", "input": true, "label": "Which erectile‑dysfunction symptoms are you currently experiencing? (Select all that apply)", "adminFlag": true, "confirm_label": "Erectile‑dysfunction symptoms:", "optionsLabelPosition": "right", "tableView": true, "values": [{"label": "Difficulty achieving an erection", "value": "difficulty_achieving"}, {"label": "Erections take longer to develop than before", "value": "delayed_onset"}, {"label": "Difficulty maintaining an erection", "value": "difficulty_maintaining"}, {"label": "Reduced sexual desire", "value": "reduced_desire"}, {"label": "Erections are not firm enough for penetration", "value": "insufficient_rigidity"}, {"label": "Cannot achieve an erection at any time", "value": "complete_inability"}, {"label": "Erections occur only during sleep or on waking", "value": "nocturnal_only"}]}, {"key": "none_of_the_above_ed_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "defaultValue": false, "tableView": true, "customClass": "mt-n3", "errors": {"custom": "Please select at least one symptom *or* provide an explanation."}, "validate": {"custom": "valid = _.some(_.values(data.ed_symptoms)) || (!!data.none_of_the_above_ed_symptoms && !!data.ed_symptoms_none_explanation && data.ed_symptoms_none_explanation.trim().length > 0);"}}, {"key": "ed_symptoms_none_explanation", "type": "textarea", "input": true, "label": "You selected “None of the above.” Please confirm you have no erectile‑dysfunction symptoms and explain why you’re requesting medication renewal (or list any other symptom we missed):", "rows": 4, "tableView": true, "customConditional": "show = !!data.none_of_the_above_ed_symptoms;"}, {"key": "morning_erections", "type": "radio", "input": true, "label": "Do you experience morning erections?", "confirm_label": "Morning erections:", "tableView": true, "validate": {"required": true}, "values": [{"label": "Regularly", "value": "regularly"}, {"label": "Sometimes", "value": "sometimes"}, {"label": "Rarely", "value": "rarely"}, {"label": "Never", "value": "never"}, {"label": "Not sure", "value": "not_sure"}], "customConditional": "show = (data.none_of_the_above_ed_symptoms === true) || (data.ed_symptoms && Object.values(data.ed_symptoms).some(v => v));"}, {"key": "ed_intercourse_pattern", "type": "selectboxes", "input": true, "label": "When do your erectile‑dysfunction symptoms occur during sexual activity? (Select all that apply)", "adminFlag": true, "confirm_label": "Intercourse‑related symptom pattern:", "optionsLabelPosition": "right", "tableView": true, "values": [{"label": "Every sexual encounter", "value": "every_encounter"}, {"label": "Most encounters", "value": "most_encounters"}, {"label": "Only with new partners", "value": "new_partner"}, {"label": "Only with a specific partner", "value": "specific_partner"}, {"label": "Only when anxious or stressed", "value": "anxious_or_stressed"}, {"label": "Only after alcohol or drugs", "value": "after_substances"}, {"label": "Only in certain positions / activities", "value": "certain_positions"}], "customConditional": "show = data.morning_erections !== undefined;"}, {"key": "none_of_the_above_intercourse_pattern", "type": "checkbox", "input": true, "label": "None of the above", "defaultValue": false, "tableView": true, "customClass": "mt-n3", "errors": {"custom": "required, or select at least one pattern."}, "validate": {"custom": "valid = !!data.none_of_the_above_intercourse_pattern || _.some(_.values(data.ed_intercourse_pattern));"}, "customConditional": "show = data.morning_erections !== undefined;"}, {"key": "peyronies_symptoms", "type": "selectboxes", "input": true, "label": "Have you noticed any of the following changes in penile shape with erections? (Select all that apply)", "adminFlag": true, "confirm_label": "Penile‑shape / <PERSON><PERSON><PERSON><PERSON>’s symptoms:", "optionsLabelPosition": "right", "tableView": true, "values": [{"label": "New curvature or bending", "value": "curvature"}, {"label": "Narrowing or shortening", "value": "narrowing_shortening"}, {"label": "Painful erections", "value": "painful_erections"}, {"label": "Lumps or hard areas within the shaft", "value": "lumps_hard_areas"}], "customConditional": "show = (data.none_of_the_above_intercourse_pattern === true) || (data.ed_intercourse_pattern && Object.values(data.ed_intercourse_pattern).some(v => v));"}, {"key": "none_of_the_above_peyronies_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "defaultValue": false, "tableView": true, "customClass": "mt-n3", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = !!data.none_of_the_above_peyronies_symptoms || _.some(_.values(data.peyronies_symptoms));"}, "customConditional": "show = (data.none_of_the_above_intercourse_pattern === true) || (data.ed_intercourse_pattern && Object.values(data.ed_intercourse_pattern).some(v => v));"}, {"key": "peyronies_exam_recommendation", "type": "content", "input": false, "html": "<div style='border-left: 4px solid #198754; background-color: #d1e7dd; padding: 10px; margin-bottom: 10px;'><strong>Recommendation:</strong> Changes in penile shape may reflect plaque build-up. An in-person physical exam with your family doctor or a walk-in clinic is the next step to confirm this and discuss a referral to a urologist if found.</div>", "tableView": false, "customConditional": "show = !data.none_of_the_above_peyronies_symptoms && data.peyronies_symptoms && (data.peyronies_symptoms.curvature || data.peyronies_symptoms.narrowing_shortening || data.peyronies_symptoms.painful_erections || data.peyronies_symptoms.lumps_hard_areas);"}, {"key": "peyronies_exam_understanding", "type": "radio", "input": true, "label": "Do you understand this recommendation to obtain an in-person exam?", "confirm_label": "Understands need for exam:", "values": [{"label": "I understand", "value": "understand"}, {"label": "I do not understand", "value": "do_not_understand"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = !data.none_of_the_above_peyronies_symptoms && data.peyronies_symptoms && (data.peyronies_symptoms.curvature || data.peyronies_symptoms.narrowing_shortening || data.peyronies_symptoms.painful_erections || data.peyronies_symptoms.lumps_hard_areas);"}, {"key": "testicular_symptoms", "type": "selectboxes", "input": true, "label": "Have you noticed any of the following in your testicles? (Select all that apply)", "adminFlag": true, "confirm_label": "Testicular symptoms:", "optionsLabelPosition": "right", "tableView": true, "values": [{"label": "Lump or swelling", "value": "lump_swelling"}, {"label": "Change in size or shape", "value": "size_change"}, {"label": "Heaviness or dull ache", "value": "heaviness_ache"}, {"label": "Sudden pain", "value": "sudden_pain"}], "customConditional": "show = (data.none_of_the_above_peyronies_symptoms === true) || (data.peyronies_symptoms && Object.values(data.peyronies_symptoms).some(v => v));"}, {"key": "none_of_the_above_testicular_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "defaultValue": false, "tableView": true, "customClass": "mt-n3", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = !!data.none_of_the_above_testicular_symptoms || _.some(_.values(data.testicular_symptoms));"}, "customConditional": "show = (data.none_of_the_above_peyronies_symptoms === true) || (data.peyronies_symptoms && Object.values(data.peyronies_symptoms).some(v => v));"}, {"key": "testicular_exam_recommendation", "type": "content", "input": false, "html": "<div style='border-left: 4px solid #198754; background-color: #d1e7dd; padding: 10px; margin-bottom: 10px;'><strong>Recommendation:</strong> A lump, change, ache, or sudden pain in a testicle should be checked in person without delay. A quick physical exam—often followed by a painless ultrasound—helps find the cause and guide treatment.</div>", "tableView": false, "customConditional": "show = !data.none_of_the_above_testicular_symptoms && data.testicular_symptoms && (data.testicular_symptoms.lump_swelling || data.testicular_symptoms.size_change || data.testicular_symptoms.heaviness_ache || data.testicular_symptoms.sudden_pain);"}, {"key": "peyronies_exam_understanding", "type": "radio", "input": true, "label": "Do you understand this recommendation to obtain an in-person exam?", "confirm_label": "Understands need for exam:", "values": [{"label": "I understand", "value": "understand"}, {"label": "I do not understand", "value": "do_not_understand"}], "validate": {"required": true}, "clearOnHide": true, "tableView": true, "customConditional": "show = !data.none_of_the_above_peyronies_symptoms && data.peyronies_symptoms && (data.peyronies_symptoms.curvature || data.peyronies_symptoms.narrowing_shortening || data.peyronies_symptoms.painful_erections || data.peyronies_symptoms.lumps_hard_areas);"}, {"key": "duration_of_ed_symptoms", "type": "select", "input": true, "label": "For how long have you been experiencing erectile‑dysfunction symptoms?", "confirm_label": "Duration of ED symptoms:", "adminFlag": true, "widget": "html5", "tableView": true, "data": {"values": [{"label": "Less than 1 month", "value": "lt_1_month"}, {"label": "1 – 3 months", "value": "1_3_months"}, {"label": "3 – 6 months", "value": "3_6_months"}, {"label": "6 – 9 months", "value": "6_9_months"}, {"label": "9 – 12 months", "value": "9_12_months"}, {"label": "1 – 2 years", "value": "1_2_years"}, {"label": "2 – 3 years", "value": "2_3_years"}, {"label": "3 – 5 years", "value": "3_5_years"}, {"label": "5 – 10 years", "value": "5_10_years"}, {"label": "More than 10 years", "value": "gt_10_years"}]}, "validate": {"required": true}, "customConditional": "show = (data.none_of_the_above_testicular_symptoms === true) || (data.testicular_symptoms && Object.values(data.testicular_symptoms).some(v => v));"}, {"key": "stress_impact_on_ed", "type": "radio", "input": true, "label": "Do you feel that stress affects your erectile dysfunction?", "confirm_label": "Stress impact on ED:", "tableView": true, "validate": {"required": true}, "values": [{"label": "Yes, significantly", "value": "yes_significantly"}, {"label": "Yes, somewhat", "value": "yes_somewhat"}, {"label": "Not sure", "value": "not_sure"}, {"label": "No", "value": "no"}], "customConditional": "show = data.duration_of_ed_symptoms !== undefined;"}, {"key": "ed_anxiety_stress_relation", "type": "radio", "input": true, "label": "Do your erectile dysfunction symptoms improve or resolve when performance anxiety or stress is reduced?", "confirm_label": "Anxiety and Stress Relation to ED:", "tableView": true, "validate": {"required": true}, "values": [{"label": "Yes, symptoms improve", "value": "improve_with_reduction"}, {"label": "No, symptoms persist", "value": "persist_regardless"}, {"label": "Sometimes, it varies", "value": "varies"}, {"label": "Not sure", "value": "not_sure"}], "customConditional": "show = data.stress_impact_on_ed !== undefined;"}, {"key": "impact_of_ed", "type": "radio", "input": true, "label": "How much does erectile dysfunction affect your quality of life?", "confirm_label": "Impact of ED on quality of life:", "tableView": true, "validate": {"required": true}, "values": [{"label": "Significantly", "value": "significantly"}, {"label": "Moderately", "value": "moderately"}, {"label": "<PERSON><PERSON><PERSON>", "value": "slightly"}, {"label": "Not at all", "value": "not_at_all"}], "customConditional": "show = data.ed_anxiety_stress_relation !== undefined;"}, {"key": "header_medication_history", "html": "</br><h2>Current Medications</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "current_medications", "type": "selectboxes", "input": true, "label": "Are you currently taking any of the following medications? (Select all that apply)", "values": [{"label": "Blood-pressure medications", "value": "blood_pressure_medications"}, {"label": "Diabetes medications", "value": "diabetes_medications"}, {"label": "Cholesterol-lowering medications", "value": "cholesterol_medications"}, {"label": "Antidepressants", "value": "antidepressants"}, {"label": "Other", "value": "other"}], "adminFlag": true, "confirm_label": "Current medications:", "tableView": true, "optionsLabelPosition": "right"}, {"key": "none_of_the_above_current_medications", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "Please select at least one medication class or tick ‘None of the above’."}, "validate": {"custom": "valid = !!data.none_of_the_above_current_medications || _.some(_.values(data.current_medications));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "current_medications_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT take the following medications:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "Medications not taken:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.current_medications, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "header_blood_pressure_medication_history", "html": "<h2>Blood Pressure Medications</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.current_medications.blood_pressure_medications"}, {"key": "correlation_bp_meds", "type": "radio", "input": true, "label": "Did you notice the onset or worsening of erectile dysfunction symptoms after starting or changing the dosage of your blood pressure medications?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "not_sure"}], "tableView": true, "confirm_label": "Blood pressure medication correlation:", "validate": {"required": true}, "customConditional": "show = data.current_medications.blood_pressure_medications"}, {"key": "header_diabetes_history", "html": "<h2>Diabetes History</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.current_medications.diabetes_medications"}, {"key": "type_of_diabetes", "type": "radio", "input": true, "label": "What type of diabetes do you have?", "values": [{"label": "Type 1 Diabetes", "value": "type_1"}, {"label": "Type 2 Diabetes", "value": "type_2"}, {"label": "I take diabetes medications for weight loss", "value": "weight_loss"}, {"label": "Not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Type of diabetes:", "customConditional": "show = data.current_medications && data.current_medications.diabetes_medications"}, {"key": "diabetes_complications", "type": "selectboxes", "input": true, "label": "Have you experienced any of the following complications related to diabetes? (Select all that apply)", "values": [{"label": "Neuropathy (nerve damage)", "value": "neuropathy"}, {"label": "Retinopathy (eye problems)", "value": "retinopathy"}, {"label": "Nephropathy (kidney issues)", "value": "nephropathy"}, {"label": "Cardiovascular problems (heart issues)", "value": "cardiovascular"}], "adminFlag": true, "confirm_label": "Diabetes complications:", "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = data.current_medications && data.current_medications.diabetes_medications"}, {"key": "none_of_the_above_diabetes_complications", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "Please select at least one complication or tick ‘None of the above’."}, "validate": {"custom": "valid = !!data.none_of_the_above_diabetes_complications || _.some(_.values(data.diabetes_complications));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.current_medications && data.current_medications.diabetes_medications"}, {"key": "diabetes_complications_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following diabetes complications:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "Diabetes complications (not present):", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.diabetes_complications, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "header_anti_depressant_medication_history", "html": "</br><h2>Anti-Depressant Medications</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.current_medications.antidepressants"}, {"key": "correlation_antidepressants", "type": "radio", "input": true, "confirm_label": "Antidepressant correlation:", "label": "Did you notice the onset or worsening of erectile dysfunction symptoms after starting or changing the dosage of your antidepressants?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "not_sure"}], "tableView": true, "validate": {"required": true}, "customConditional": "show = data.current_medications.antidepressants"}, {"key": "header_previous_investigations", "html": "</br><h2>Previous Investigations</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "previous_ed_tests", "type": "selectboxes", "input": true, "label": "Which of the following tests have you undergone for erectile dysfunction? (Select all that apply)", "values": [{"label": "Blood tests for hormone levels", "value": "blood_hormone_levels"}, {"label": "Penile ultrasound / Doppler study", "value": "ultrasound"}, {"label": "Nerve-function tests", "value": "nerve_function_tests"}, {"label": "Nocturnal-penile-tumescence (NPT) test", "value": "nocturnal_penile_tumescence"}], "adminFlag": true, "confirm_label": "Previous ED investigations:", "tableView": true, "optionsLabelPosition": "right"}, {"key": "none_of_the_above_previous_ed_tests", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "Please select at least one investigation or tick ‘None of the above’."}, "validate": {"custom": "valid = !!data.none_of_the_above_previous_ed_tests || _.some(_.values(data.previous_ed_tests));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "previous_ed_tests_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following ED investigations:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "ED investigations (not performed):", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.previous_ed_tests, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "results_blood_hormone_levels", "confirm_label": "Results of Blood Tests for Hormone Levels", "type": "selectboxes", "input": true, "label": "Which specific blood tests for hormone levels have you undergone? (Select all that apply)", "values": [{"label": "Testosterone levels", "value": "testosterone_levels"}, {"label": "Prolactin levels", "value": "prolactin_levels"}, {"label": "Thyroid function tests (TSH)", "value": "thyroid_tests"}, {"label": "Luteinizing hormone (LH)", "value": "luteinizing_hormone"}, {"label": "Follicle-stimulating hormone (FSH)", "value": "follicle_stimulating_hormone"}, {"label": "Other hormone tests", "value": "other_hormone_tests"}, {"label": "Not sure / Don't remember", "value": "not_sure"}], "tableView": true, "validate": {"required": true}, "customConditional": "show = data.previous_ed_tests.blood_hormone_levels"}, {"key": "results_ultrasound", "type": "selectboxes", "input": true, "confirm_label": "Results of Penile Ultrasound / Doppler Study:", "label": "Results of Penile Ultrasound", "values": [{"label": "Normal blood flow", "value": "normal_blood_flow"}, {"label": "Reduced or inadequate blood flow", "value": "reduced_blood_flow"}, {"label": "Structural abnormalities", "value": "structural_abnormalities"}, {"label": "Venous leak", "value": "venous_leak"}, {"label": "Other findings", "value": "other_findings"}, {"label": "Not sure", "value": "not_sure"}], "widget": "html5", "tableView": true, "validate": {"required": true}, "customConditional": "show = data.previous_ed_tests.ultrasound"}, {"key": "results_nocturnal_penile_tumescence", "data": {"values": [{"label": "Normal erectile function during sleep", "value": "normal_erectile_function"}, {"label": "Reduced or absent erections during sleep", "value": "reduced_absent_erections"}, {"label": "Erections present but not sustained", "value": "erections_not_sustained"}, {"label": "Inconclusive results", "value": "inconclusive_results"}, {"label": "Never tested", "value": "never_tested"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "Results of Nocturnal Penile Tumescence Test:", "widget": "html5", "tableView": true, "validate": {"required": true}, "customConditional": "show = data.previous_ed_tests.nocturnal_penile_tumescence"}, {"key": "header_lifestyle_factors", "html": "<h1>Lifestyle Factors</h1>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "sleep_patterns", "type": "select", "input": true, "label": "How would you describe your regular sleep patterns?", "confirm_label": "Sleep Patterns:", "data": {"values": [{"label": "Regular and restful", "value": "regular_restful"}, {"label": "Occasionally disrupted", "value": "occasionally_disrupted"}, {"label": "Frequently disrupted", "value": "frequently_disrupted"}, {"label": "Very poor", "value": "very_poor"}]}, "validate": {"required": true}, "widget": "html5", "tableView": true}, {"key": "smoking_status", "type": "radio", "input": true, "label": "Do you smoke?", "confirm_label": "Smoking status:", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "I used to smoke", "value": "used_to"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = !!data.sleep_patterns;"}, {"key": "smoking_cessation_recommendation", "type": "content", "input": false, "label": "Content", "html": "<div class='alert alert-info'><strong>Recommendation:</strong> Smoking is a well‑known contributor to erectile dysfunction because it damages blood vessels. Quitting can significantly improve erectile and cardiovascular health.</div>", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.smoking_status === 'yes';"}, {"key": "smoking_cessation_acknowledge", "type": "radio", "input": true, "label": "Do you understand why quitting smoking is recommended and how it may improve erectile function?", "confirm_label": "Acknowledged smoking‑cessation advice:", "values": [{"label": "I understand", "value": "understand"}, {"label": "I do not understand", "value": "do_not_understand"}], "validate": {"required": true}, "optionsLabelPosition": "right", "adminFlag": true, "tableView": true, "customConditional": "show = data.smoking_status === 'yes';"}, {"key": "alcohol_consumption", "type": "radio", "input": true, "label": "How often do you consume alcohol?", "confirm_label": "Alcohol Consumption:", "values": [{"label": "Daily", "value": "daily"}, {"label": "Several times a week", "value": "several_times_week"}, {"label": "Once a week", "value": "once_week"}, {"label": "Less than once a week", "value": "less_once_week"}, {"label": "Never", "value": "never"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = !!data.smoking_status;"}, {"key": "alcohol_use_recommendation", "type": "content", "input": false, "label": "Content", "html": "<div class='alert alert-info'><strong>Recommendation:</strong> Frequent alcohol intake can worsen erectile dysfunction by disrupting hormones and reducing blood flow. Cutting back often improves erectile function and overall health.</div>", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.alcohol_consumption === 'daily' || data.alcohol_consumption === 'several_times_week';"}, {"key": "alcohol_use_acknowledge", "type": "radio", "input": true, "label": "Do you understand why reducing alcohol use is recommended and how it may improve erectile function?", "confirm_label": "Acknowledged alcohol‑reduction advice:", "values": [{"label": "I understand", "value": "understand"}, {"label": "I do not understand", "value": "do_not_understand"}], "validate": {"required": true}, "optionsLabelPosition": "right", "adminFlag": true, "tableView": true, "customConditional": "show = data.alcohol_consumption === 'daily' || data.alcohol_consumption === 'several_times_week';"}, {"key": "exercise_frequency", "type": "radio", "input": true, "label": "How often do you exercise?", "confirm_label": "Exercise Frequency:", "values": [{"label": "Daily", "value": "daily"}, {"label": "Several times a week", "value": "several_times_week"}, {"label": "Once a week", "value": "once_week"}, {"label": "Less than once a week", "value": "less_once_week"}, {"label": "Never", "value": "never"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = !!data.alcohol_consumption;"}, {"key": "stress_levels", "type": "radio", "input": true, "label": "How often do you feel stressed? (Select one)", "confirm_label": "Stress Levels:", "values": [{"label": "Daily", "value": "daily"}, {"label": "Several times a week", "value": "several_times_week"}, {"label": "Once a week", "value": "once_week"}, {"label": "Less than once a week", "value": "less_once_week"}, {"label": "Rarely", "value": "rarely"}, {"label": "Never", "value": "never"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = !!data.exercise_frequency;"}, {"key": "heading_symptoms", "html": "</br><h2>General Symptoms</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "heading_heart_health", "html": "</br><h4>Heart Health</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "cardiovascular_symptoms", "type": "selectboxes", "input": true, "label": "Do you have any of the following cardiovascular symptoms?", "values": [{"label": "Chest pain, tightness or discomfort when sitting or with exercise", "value": "chest_pain", "shortcut": ""}, {"label": "Palpitations", "value": "palpitations", "shortcut": ""}, {"label": "Swelling in the legs, ankles, or feet", "value": "swelling", "shortcut": ""}, {"label": "Dizziness or fainting", "value": "dizziness", "shortcut": ""}], "adminFlag": true, "tableView": true, "confirm_label": "Cardiovascular symptoms:", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_cardiovascular_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = !!data.none_of_the_above_cardiovascular_symptoms || _.some(_.values(data.cardiovascular_symptoms));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "cardiac_symptoms_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following cardiac symptoms:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "I DO NOT have the following heart related symptoms:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.cardiovascular_symptoms, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "heading_chest_pain", "html": "</br><h4>Chest Pain</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.chest_pain === true;"}, {"key": "chest_pain_onset", "data": {"values": [{"label": "Today", "value": "today"}, {"label": "1 day ago", "value": "1_day"}, {"label": "2 days ago", "value": "2_days"}, {"label": "3 days ago", "value": "3_days"}, {"label": "4 days ago", "value": "4_days"}, {"label": "5 days ago", "value": "5_days"}, {"label": "6 days ago", "value": "6_days"}, {"label": "7 days ago", "value": "7_days"}, {"label": "1-2 weeks ago", "value": "1_2_weeks"}, {"label": "2-4 weeks ago", "value": "2_4_weeks"}, {"label": "1-3 months ago", "value": "1_3_months"}, {"label": "More than 3 months ago", "value": "3_plus_months"}]}, "type": "select", "input": true, "label": "When did the chest pain or discomfort start?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Chest pain onset:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.chest_pain === true;"}, {"key": "chest_pain_triggers", "type": "selectboxes", "input": true, "label": "What brings the chest pain on or makes it worse?", "values": [{"label": "Physical activity or exertion", "value": "exertion"}, {"label": "Stress or anxiety", "value": "stress"}, {"label": "Eating a heavy meal", "value": "meal"}, {"label": "Lying down", "value": "lying"}, {"label": "Breathing deeply or coughing", "value": "breathing"}, {"label": "Unknown or unpredictable", "value": "unknown"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Chest pain triggers:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.chest_pain === true;"}, {"key": "chest_pain_relievers", "type": "selectboxes", "input": true, "label": "What makes the chest pain better?", "values": [{"label": "Rest", "value": "rest"}, {"label": "Lying down", "value": "lying"}, {"label": "Standing upright", "value": "standing"}, {"label": "Medication (e.g., nitroglycerin, pain relievers)", "value": "medication"}, {"label": "Nothing helps", "value": "nothing"}, {"label": "Unsure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Chest pain relief methods:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.chest_pain === true;"}, {"key": "chest_pain_character", "type": "radio", "input": true, "label": "How would you describe the pain?", "values": [{"label": "<PERSON>", "value": "sharp"}, {"label": "Dull/aching", "value": "dull"}, {"label": "Tight/pressure-like", "value": "pressure"}, {"label": "Burning", "value": "burning"}, {"label": "Stabbing", "value": "stabbing"}, {"label": "Other", "value": "other"}], "confirm_label": "Chest pain character:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.chest_pain === true;"}, {"key": "chest_pain_location", "type": "selectboxes", "input": true, "label": "Where is the pain located?", "values": [{"label": "Centre of chest", "value": "centre_chest"}, {"label": "Left side of chest", "value": "left_chest"}, {"label": "Right side of chest", "value": "right_chest"}, {"label": "Upper chest or sternum", "value": "upper_chest"}, {"label": "Other", "value": "other"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Chest pain location:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.chest_pain === true;"}, {"key": "chest_pain_radiation", "type": "selectboxes", "input": true, "label": "Does the pain radiate to any of the following areas?", "values": [{"label": "Left arm", "value": "left_arm"}, {"label": "Right arm", "value": "right_arm"}, {"label": "<PERSON><PERSON>", "value": "jaw"}, {"label": "Neck", "value": "neck"}, {"label": "Back", "value": "back"}, {"label": "No radiation", "value": "none"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Chest pain radiation:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.chest_pain === true;"}, {"key": "chest_pain_pattern", "type": "radio", "input": true, "label": "Is the chest pain constant or does it come and go?", "values": [{"label": "Constant", "value": "constant"}, {"label": "Comes and goes (intermittent)", "value": "intermittent"}, {"label": "Unsure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Chest pain pattern:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.chest_pain === true;"}, {"key": "heading_palpitations", "html": "</br><h4>Palpitations</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.palpitations === true;"}, {"key": "palpitations_onset", "data": {"values": [{"label": "Today", "value": "today"}, {"label": "1 day ago", "value": "1_day"}, {"label": "2-3 days ago", "value": "2_3_days"}, {"label": "4-7 days ago", "value": "4_7_days"}, {"label": "1-2 weeks ago", "value": "1_2_weeks"}, {"label": "2-4 weeks ago", "value": "2_4_weeks"}, {"label": "More than a month ago", "value": "1_month_plus"}]}, "type": "select", "input": true, "label": "When did your palpitations begin?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Palpitations onset:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.palpitations === true;"}, {"key": "palpitations_rhythm", "type": "radio", "input": true, "label": "How would you describe the rhythm of your palpitations?", "values": [{"label": "Regular and fast", "value": "regular_fast"}, {"label": "Irregular and fast", "value": "irregular_fast"}, {"label": "Skipped beats", "value": "skipped_beats"}, {"label": "Flutters", "value": "flutters"}, {"label": "Other", "value": "other"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Palpitations rhythm:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.palpitations === true;"}, {"key": "palpitations_triggers", "type": "selectboxes", "input": true, "label": "What triggers the palpitations?", "values": [{"label": "Exercise or physical activity", "value": "exercise"}, {"label": "Stress or anxiety", "value": "stress"}, {"label": "Caffeine or stimulants", "value": "caffeine"}, {"label": "Alcohol", "value": "alcohol"}, {"label": "Occur at rest", "value": "rest"}, {"label": "Occur at night", "value": "night"}, {"label": "No clear trigger", "value": "unknown"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Palpitations triggers:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.palpitations === true;"}, {"key": "palpitations_duration", "data": {"values": [{"label": "A few seconds", "value": "seconds"}, {"label": "Less than 5 minutes", "value": "less_5_min"}, {"label": "5-30 minutes", "value": "5_30_min"}, {"label": "30 minutes to a few hours", "value": "30min_hours"}, {"label": "More than a few hours", "value": "many_hours"}, {"label": "Constant", "value": "constant"}, {"label": "Varies", "value": "varies"}]}, "type": "select", "input": true, "label": "How long do the palpitations usually last?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Palpitations duration:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.palpitations === true;"}, {"key": "palpitations_associated_symptoms", "type": "selectboxes", "input": true, "label": "Do you experience any of the following during palpitations?", "values": [{"label": "Dizziness or lightheadedness", "value": "dizziness"}, {"label": "Shortness of breath", "value": "sob"}, {"label": "Chest pain", "value": "chest_pain"}, {"label": "Sweating", "value": "sweating"}, {"label": "Fainting or near-fainting", "value": "fainting"}, {"label": "No other symptoms", "value": "none"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Palpitations associated symptoms:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.palpitations === true;"}, {"key": "heading_swelling", "html": "</br><h4>Swelling</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.swelling === true;"}, {"key": "swelling_location", "type": "selectboxes", "input": true, "label": "Where is the swelling located?", "values": [{"label": "Feet", "value": "feet"}, {"label": "<PERSON><PERSON>", "value": "ankles"}, {"label": "Lower legs", "value": "lower_legs"}, {"label": "Thighs", "value": "thighs"}, {"label": "Abdomen", "value": "abdomen"}, {"label": "Other", "value": "other"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Swelling location:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.swelling === true;"}, {"key": "swelling_sidedness", "type": "radio", "input": true, "label": "Is the swelling on one side or both sides?", "values": [{"label": "Both sides", "value": "bilateral"}, {"label": "One side only)", "value": "unilateral"}, {"label": "Varies", "value": "varies"}, {"label": "Unsure", "value": "unsure"}], "tableView": true, "confirm_label": "Swelling sidedness:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.swelling === true;"}, {"key": "swelling_onset", "data": {"values": [{"label": "Today", "value": "today"}, {"label": "1 day ago", "value": "1_day"}, {"label": "2-3 days ago", "value": "2_3_days"}, {"label": "4-7 days ago", "value": "4_7_days"}, {"label": "1-2 weeks ago", "value": "1_2_weeks"}, {"label": "More than 2 weeks ago", "value": "2_plus_weeks"}]}, "type": "select", "input": true, "label": "When did the swelling begin?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Swelling onset:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.swelling === true;"}, {"key": "swelling_timing", "type": "radio", "input": true, "label": "When is the swelling most noticeable?", "values": [{"label": "By the end of the day", "value": "evening"}, {"label": "In the morning", "value": "morning"}, {"label": "Constant throughout the day", "value": "constant"}, {"label": "Varies day to day", "value": "varies"}, {"label": "Unsure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Swelling timing:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.swelling === true;"}, {"key": "swelling_pitting", "type": "radio", "input": true, "label": "When you press on the swollen area, does it leave a dent (pitting)?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "unsure"}], "tableView": true, "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.swelling === true;"}, {"key": "heading_dizziness", "html": "</br><h4>Dizziness or Fainting</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.dizziness === true;"}, {"key": "dizziness_onset", "data": {"values": [{"label": "Today", "value": "today"}, {"label": "1 day ago", "value": "1_day"}, {"label": "2-3 days ago", "value": "2_3_days"}, {"label": "4-7 days ago", "value": "4_7_days"}, {"label": "1-2 weeks ago", "value": "1_2_weeks"}, {"label": "More than 2 weeks ago", "value": "2_plus_weeks"}]}, "type": "select", "input": true, "label": "When did the dizziness or fainting episodes begin?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Dizziness onset:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.dizziness === true;"}, {"key": "dizziness_frequency", "type": "radio", "input": true, "label": "How often do you experience dizziness or fainting?", "values": [{"label": "Once", "value": "once"}, {"label": "Occasionally (less than once a week)", "value": "occasional"}, {"label": "Frequently (once or more per week)", "value": "frequent"}, {"label": "Daily or almost daily", "value": "daily"}, {"label": "Unsure", "value": "unsure"}], "tableView": true, "confirm_label": "Dizziness frequency:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.dizziness === true;"}, {"key": "dizziness_character", "type": "radio", "input": true, "label": "How would you describe the dizziness?", "values": [{"label": "Lightheadedness or feeling faint", "value": "lightheaded"}, {"label": "Spinning or vertigo", "value": "spinning"}, {"label": "Unsteady or off balance", "value": "unsteady"}, {"label": "Hard to describe", "value": "hard_to_describe"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Dizziness character:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.dizziness === true;"}, {"key": "dizziness_timing", "type": "selectboxes", "input": true, "label": "When does the dizziness or fainting usually happen?", "values": [{"label": "After standing up", "value": "standing_up"}, {"label": "After exertion or exercise", "value": "exertion"}, {"label": "At rest", "value": "rest"}, {"label": "With dehydration or hunger", "value": "dehydration"}, {"label": "Without a clear trigger", "value": "unknown"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Dizziness timing:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.dizziness === true;"}, {"key": "fainting_loss_consciousness", "type": "radio", "input": true, "label": "Have you ever fully lost consciousness during one of these episodes?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Unsure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Fainting/loss of consciousness:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.dizziness === true;"}, {"key": "heading_lung_health", "html": "</br><h4>Lung Health</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "respiratory_symptoms", "type": "selectboxes", "input": true, "label": "Do you have any of the following respiratory symptoms?", "values": [{"label": "<PERSON><PERSON>", "value": "cough", "shortcut": ""}, {"label": "Shortness of breath", "value": "shortness_of_breath", "shortcut": ""}, {"label": "Wheezing", "value": "wheezing", "shortcut": ""}], "adminFlag": true, "tableView": true, "confirm_label": "Respiratory symptoms:", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_respiratory_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = !!data.none_of_the_above_respiratory_symptoms || _.some(_.values(data.respiratory_symptoms));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "respiratory_symptoms_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following respiratory symptoms:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "I DO NOT have the following breathing related symptoms:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.respiratory_symptoms, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "heading_cough", "html": "</br><h4>Cough</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.respiratory_symptoms && data.respiratory_symptoms.cough === true;"}, {"key": "cough_type", "type": "radio", "input": true, "label": "Is your cough dry or productive?", "values": [{"label": "Dry (no phlegm)", "value": "dry"}, {"label": "Productive (with phlegm)", "value": "productive"}, {"label": "Varies", "value": "varies"}, {"label": "Unsure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Cough type:", "customConditional": "show = data.respiratory_symptoms && data.respiratory_symptoms.cough === true;"}, {"key": "cough_duration", "data": {"values": [{"label": "Less than 1 week", "value": "lt_1wk"}, {"label": "1-2 weeks", "value": "1_2wk"}, {"label": "2-4 weeks", "value": "2_4wk"}, {"label": "More than 4 weeks", "value": "gt_4wk"}]}, "type": "select", "input": true, "label": "How long have you had the cough?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Cough duration:", "customConditional": "show = data.respiratory_symptoms && data.respiratory_symptoms.cough === true;"}, {"key": "cough_coughing_blood", "type": "radio", "input": true, "label": "Have you noticed any blood when coughing?", "values": [{"label": "Yes - bright red blood", "value": "bright_red"}, {"label": "Yes - dark or coffee ground appearance", "value": "coffee_ground"}, {"label": "I think so - unsure of colour or source", "value": "unsure_appearance"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "description": "This includes coughing up blood that appears bright red or looks like coffee grounds (which may suggest bleeding in the lungs or stomach).", "confirm_label": "Coughing up blood:", "customConditional": "show = data.respiratory_symptoms && data.respiratory_symptoms.cough === true;"}, {"key": "cough_feeling_unwell", "type": "radio", "input": true, "label": "Do you feel generally unwell with your cough (e.g. fatigue, fever, weakness)?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Feeling unwell with cough:", "customConditional": "show = data.respiratory_symptoms && data.respiratory_symptoms.cough === true;"}, {"key": "cough_with_cold", "type": "radio", "input": true, "label": "Did your cough begin at the same time as a cold or viral illness (e.g. sore throat, congestion, fever)?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Cough with cold:", "customConditional": "show = data.respiratory_symptoms && data.respiratory_symptoms.cough === true;"}, {"key": "cough_progression", "type": "radio", "input": true, "label": "Is your cough improving, getting worse, or staying the same?", "values": [{"label": "Improving", "value": "improving"}, {"label": "Getting worse", "value": "worsening"}, {"label": "No change", "value": "no_change"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Cough progression:", "customConditional": "show = data.cough_with_cold === 'yes';"}, {"key": "cough_urgent_warning", "html": "<div style='border-left: 4px solid #ffc107; background-color: #fff3cd; padding: 10px; margin-bottom: 10px;'><strong>Warning:</strong> If you are experiencing any of the following — worsening cough, shortness of breath, coughing up blood, feeling generally unwell, or if your cough has lasted more than 4 weeks — we advise seeking same-day care in an emergency department. These may be signs of a more serious condition that should not be delayed.</div>", "type": "content", "input": false, "label": "Content", "customConditional": "show = (data.cough_progression === 'worsening') || (data.respiratory_symptoms && data.respiratory_symptoms.shortness_of_breath === true) || (data.cough_coughing_blood === true) || (data.cough_feeling_unwell === true) || (data.cough_duration === 'gt_4wk');"}, {"key": "cough_urgent_warning_understanding", "type": "radio", "input": true, "label": "Do you understand this warning about when to seek emergency care?", "values": [{"label": "I understand", "value": "understand"}, {"label": "I do not understand", "value": "do_not_understand"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Understands Emergency Care Warning:", "customConditional": "show = (data.cough_progression === 'worsening') || (data.respiratory_symptoms && data.respiratory_symptoms.shortness_of_breath === true) || (data.cough_coughing_blood === true) || (data.cough_feeling_unwell === true) || (data.cough_duration === 'gt_4wk');"}, {"key": "heading_shortness_of_breath", "html": "</br><h4>Shortness of Breath</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.respiratory_symptoms && data.respiratory_symptoms.shortness_of_breath === true;"}, {"key": "sob_triggers", "type": "selectboxes", "input": true, "label": "When do you typically feel short of breath?", "values": [{"label": "At rest", "value": "rest"}, {"label": "With mild activity (e.g. walking)", "value": "mild_activity"}, {"label": "With moderate or strenuous activity", "value": "exercise"}, {"label": "While lying flat", "value": "lying_flat"}, {"label": "At night (waking from sleep)", "value": "nocturnal"}, {"label": "Unsure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Shortness of breath triggers:", "customConditional": "show = data.respiratory_symptoms && data.respiratory_symptoms.shortness_of_breath === true;"}, {"key": "heading_wheezing", "html": "</br><h4>Wheezing</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.respiratory_symptoms && data.respiratory_symptoms.wheezing === true;"}, {"key": "wheezing_timing", "type": "selectboxes", "input": true, "label": "When is the wheezing most noticeable?", "values": [{"label": "During exercise", "value": "exercise"}, {"label": "At rest", "value": "rest"}, {"label": "At night", "value": "night"}, {"label": "In cold weather", "value": "cold_weather"}, {"label": "When lying down", "value": "lying_down"}, {"label": "When exposed to irritants (e.g. smoke, dust)", "value": "irritants"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Wheezing timing:", "customConditional": "show = data.respiratory_symptoms && data.respiratory_symptoms.wheezing === true;"}, {"key": "wheezing_relief", "type": "radio", "input": true, "label": "Do you use any medications to relieve wheezing?", "values": [{"label": "Yes - inhaler or nebulizer", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Occasionally", "value": "occasional"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Wheezing relief medications:", "customConditional": "show = data.respiratory_symptoms && data.respiratory_symptoms.wheezing === true;"}, {"key": "wheezing_asthma_history", "type": "radio", "input": true, "label": "Have you ever been diagnosed with asthma (currently or in the past)?", "values": [{"label": "Yes - currently diagnosed", "value": "current"}, {"label": "Yes - past diagnosis only", "value": "past"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Wheezing asthma history:", "customConditional": "show = data.respiratory_symptoms && data.respiratory_symptoms.wheezing === true;"}, {"key": "header_blood_pressure", "html": "</br><h2>Blood Pressure</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "bp_monitoring_frequency", "type": "radio", "input": true, "label": "How often do you check your blood pressure at home?", "values": [{"label": "Daily", "value": "daily"}, {"label": "A few times a week", "value": "A few times a week"}, {"label": "Once a week", "value": "weekly"}, {"label": "Occasionally / infrequently", "value": "infrequent"}, {"label": "Never check at home", "value": "never"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Blood Pressure Monitoring Frequency:"}, {"key": "bp_readings_per_sitting", "type": "radio", "input": true, "label": "How many blood pressure readings do you typically take in a single sitting?", "values": [{"label": "1 reading", "value": "1"}, {"label": "2 readings", "value": "2"}, {"label": "3 readings", "value": "3"}, {"label": "More than 3 readings", "value": "more_than_3"}, {"label": "Not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = !!data.bp_monitoring_frequency;"}, {"key": "heading_blood_pressure", "html": "<h2><strong>Blood Pressure Reading</strong></h2><p>Please enter your most recent blood pressure measurement (today, if possible).</p>", "type": "content", "input": false, "label": "Content", "tableView": false}, {"key": "blood_pressure_row", "type": "columns", "input": false, "label": "Blood Pressure Input", "columns": [{"width": 3, "components": [{"key": "systolic_bp", "type": "number", "input": true, "label": "Systolic (top number)", "validate": {"max": 250, "min": 50}, "tableView": true, "placeholder": "e.g., 130", "confirm_label": "Systolic BP:"}]}, {"width": 3, "components": [{"key": "diastolic_bp", "type": "number", "input": true, "label": "Diastolic (bottom number)", "validate": {"max": 150, "min": 30}, "tableView": true, "placeholder": "e.g., 80", "confirm_label": "Diastolic BP:"}]}], "tableView": true}, {"key": "bp_reading_date", "data": {"values": [{"label": "Today", "value": "today"}, {"label": "Within the last 48 hours", "value": "Within the last 48 hours"}, {"label": "Within the last 7 days", "value": "Within the last 7 days"}, {"label": "Within the last 14 days", "value": "Within the last 14 days"}, {"label": "More than 30 days ago", "value": "More than 30 days ago"}]}, "type": "select", "input": true, "label": "When was this blood pressure reading taken?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "BP Reading Date:", "customConditional": "show = !!data.systolic_bp || !!data.diastolic_bp;"}, {"key": "bp_reading_confirmed", "type": "radio", "input": true, "label": "Are these blood pressure values accurate (based on your monitor reading today)?", "values": [{"label": "Yes, this is my accurate reading", "value": "yes"}, {"label": "No, I made a mistake or I'm not sure", "value": "no"}], "validate": {"required": true}, "tableView": true, "confirm_label": "BP Reading Confirmed:", "customConditional": "show = data.systolic_bp > 180 || data.diastolic_bp > 100;"}, {"key": "bp_symptoms_check", "type": "selectboxes", "input": true, "label": "Are you currently experiencing any of the following symptoms?", "values": [{"label": "Chest pain or pressure", "value": "chest_pain"}, {"label": "Severe headache", "value": "headache"}, {"label": "Shortness of breath", "value": "shortness_of_breath"}, {"label": "Blurred vision", "value": "vision"}, {"label": "Trouble speaking or moving", "value": "neuro"}, {"label": "Dizziness or lightheadedness", "value": "dizziness"}], "tooltip": "These symptoms could indicate a hypertensive emergency.", "tableView": true, "confirm_label": "BP Symptoms Check:", "customConditional": "show = data.bp_reading_confirmed === 'yes' && ((data.systolic_bp > 180 && data.systolic_bp <= 200) || (data.diastolic_bp > 100 && data.diastolic_bp <= 110));"}, {"key": "bp_urgency_advice", "html": "<div style='background:#fff3cd; padding:10px; border-left:5px solid #ffeeba;'><strong>Important:</strong> Your blood pressure is elevated. Please visit a walk-in clinic or urgent care today to confirm your reading and ensure proper management.</div>", "type": "content", "input": false, "label": "BP Urgency Advisory", "customConditional": "show = data.bp_reading_confirmed === 'yes' && ((data.systolic_bp > 180 && data.systolic_bp <= 200) || (data.diastolic_bp > 100 && data.diastolic_bp <= 110)) && !Object.values(data.bp_symptoms_check || {}).includes(true);"}, {"key": "bp_urgency_advice_understanding", "type": "radio", "input": true, "label": "Do you understand the recommendation to follow up with a walk-in clinic or urgent care today?", "values": [{"label": "I understand", "value": "understand"}, {"label": "I do not understand", "value": "do_not_understand"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Understands BP Urgency Advisory:", "customConditional": "show = data.bp_reading_confirmed === 'yes' && ((data.systolic_bp > 180 && data.systolic_bp <= 200) || (data.diastolic_bp > 100 && data.diastolic_bp <= 110)) && !Object.values(data.bp_symptoms_check || {}).includes(true);"}, {"key": "bp_emergency_advice", "html": "<div style='background:#f8d7da; padding:10px; border-left:5px solid #f5c6cb;'><strong>Emergency Alert:</strong> Your blood pressure is elevated sufficient and/or symptoms suggest a medical emergency. Please go to the <strong>nearest emergency department immediately</strong> to have your blood pressure checked and treated safely. If your blood pressure values were entered in error, you can revise them</div>", "type": "content", "input": false, "label": "BP Emergency Advisory", "tableView": true, "customConditional": "show = data.bp_reading_confirmed === 'yes' && ((data.systolic_bp > 200 || data.diastolic_bp > 110) || Object.values(data.bp_symptoms_check || {}).includes(true));"}, {"key": "bp_emergency_advice_understanding", "type": "radio", "input": true, "label": "Do you understand this recommendation to go to the emergency department immediately?", "values": [{"label": "I understand", "value": "understand"}, {"label": "I do not understand", "value": "do_not_understand"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Understands BP Emergency Advisory:", "customConditional": "show = data.bp_reading_confirmed === 'yes' && ((data.systolic_bp > 200 || data.diastolic_bp > 110) || Object.values(data.bp_symptoms_check || {}).includes(true));"}, {"key": "bp_low_emergency_advice", "html": "<div style='background:#f8d7da; padding:10px; border-left:5px solid #f5c6cb;'><strong>Emergency Alert:</strong> Your blood pressure appears to be dangerously low. If you are feeling faint, dizzy, weak, or unwell, please go to the <strong>nearest emergency department immediately</strong>.</div>", "type": "content", "input": false, "label": "Low Blood Pressure Emergency Alert", "customConditional": "show = data.bp_reading_confirmed === 'yes' && (data.systolic_bp < 70 || data.diastolic_bp < 50);"}, {"key": "heading_in_office_monitoring", "html": "</br><h2><strong>In-Office Monitoring and Follow-Up</strong></h2><p>Please provide information about recent checkups and whether you have a regular healthcare provider.</p>", "type": "content", "input": false, "label": "Content", "tableView": false}, {"key": "last_office_bp_check", "data": {"values": [{"label": "< 1 month ago", "value": "< 1 month ago"}, {"label": "1-2 months ago", "value": "1-2 months ago"}, {"label": "2-3 months ago", "value": "2-3 months ago"}, {"label": "3-6 months ago", "value": "3-6 months ago"}, {"label": "6-12 months ago", "value": "6-12 months ago"}, {"label": "More than a year ago", "value": "More than a year ago"}, {"label": "Never / not sure", "value": "Never / not sure"}]}, "type": "select", "input": true, "label": "When was your last in-office blood pressure check (e.g. at a doctor's office or pharmacy)?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Last Office BP Check:"}, {"key": "clinic_vs_home_reading", "type": "radio", "input": true, "label": "Was the reading taken in clinic or pharmacy similar to your home readings?", "values": [{"label": "Yes - it was similar", "value": "similar"}, {"label": "No - it was higher", "value": "higher"}, {"label": "No - it was lower", "value": "lower"}, {"label": "I don't remember", "value": "dont_remember"}, {"label": "Not applicable", "value": "not_applicable"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Clinic vs Home Reading:", "customConditional": "show = data.last_office_bp_check && data.last_office_bp_check !== 'Never / not sure';"}, {"key": "last_physical_exam", "data": {"values": [{"label": "< 1 month ago", "value": "< 1 month ago"}, {"label": "1-2 months ago", "value": "1-2 months ago"}, {"label": "2-3 months ago", "value": "2-3 months ago"}, {"label": "3-6 months ago", "value": "3-6 months ago"}, {"label": "6-12 months ago", "value": "6-12 months ago"}, {"label": "More than a year ago", "value": "More than a year ago"}, {"label": "Never / not sure", "value": "Never / not sure"}]}, "type": "select", "input": true, "label": "When was your last in-office heart and lung physical exam (by a doctor or nurse practitioner)?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Last Physical Exam:"}, {"key": "has_regular_provider", "type": "radio", "input": true, "label": "Do you have a regular family doctor or nurse practitioner you see for exams?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Regular Provider:"}, {"key": "subheader_lab_testing", "html": "</br><h3>Laboratory Testing</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "prior_tests_completed", "type": "selectboxes", "input": true, "label": "Have you had any of the following tests completed <strong>after</strong> your symptoms started?", "values": [{"label": "Kidney function (eGFR)", "value": "egfr"}, {"label": "Lipid Profile (Cholesterol levels)", "value": "lipid_profile"}, {"label": "Diabetes Testing (HbA1c)", "value": "a1c"}, {"label": "Fasting Blood Glucose (FBG)", "value": "fasting_glucose"}, {"label": "CBC (Complete Blood Count)", "value": "cbc"}, {"label": "Total Testosterone", "value": "testosterone"}, {"label": "TSH (Thyroid Stimulating Hormone)", "value": "tsh"}, {"label": "I have not had these tests completed", "value": "no_prior_tests"}], "inputType": "checkbox", "tableView": true, "validate": {"required": true}, "confirm_label": "Prior Tests Completed:", "optionsLabelPosition": "right"}, {"key": "last_known_lab_timing", "data": {"values": [{"label": "<1 month ago", "value": "Less than 1 month ago"}, {"label": "1-2 months ago", "value": "1-2 months ago"}, {"label": "2-3 months ago", "value": "2-3 months ago"}, {"label": "3-4 months ago", "value": "3-4 months ago"}, {"label": "4-5 months ago", "value": "4-5 months ago"}, {"label": "5-6 months ago", "value": "5-6 months ago"}, {"label": "6-12 months ago", "value": "6-12 months ago"}, {"label": "12-24 months ago", "value": "12-24 months ago"}, {"label": "24+ months ago", "value": "More than 24 months ago"}, {"label": "I haven't had lab testing", "value": "Haven't had lab testing"}]}, "type": "select", "input": true, "label": "Do you recall when your last set of lab tests (bloodwork) was completed?", "widget": "html5", "validate": {"required": true}, "confirm_label": "Last Known Lab Timing:", "tableView": true, "customConditional": "show = data.prior_tests_completed?.no_prior_tests === true && Object.values(data.prior_tests_completed).filter(v => v === true).length === 1"}, {"key": "heading_kidney_function", "html": "<h3>Kidney Function (eGFR)</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.prior_tests_completed?.egfr;"}, {"key": "last_kidney_function_test", "data": {"values": [{"label": "<1 month ago", "value": "Less than 1 month ago"}, {"label": "1-2 months ago", "value": "1-2 months ago"}, {"label": "2-3 months ago", "value": "2-3 months ago"}, {"label": "3-4 months ago", "value": "3-4 months ago"}, {"label": "4-5 months ago", "value": "4-5 months ago"}, {"label": "5-6 months ago", "value": "5-6 months ago"}, {"label": "6-12 months ago", "value": "6-12 months ago"}, {"label": "12-24 months ago", "value": "12-24 months ago"}, {"label": "24+ months ago", "value": "More than 24 months ago"}, {"label": "Never had one", "value": "Never had one"}]}, "type": "select", "input": true, "label": "When was your last kidney function test completed?", "widget": "html5", "validate": {"required": true}, "tableView": true, "placeholder": "Select the time range", "confirm_label": "Last Kidney Function Test:", "customConditional": "show = data.prior_tests_completed?.egfr;"}, {"key": "prior_kidney_function_value", "data": {"values": [{"label": "I don't remember my result", "value": "I don't remember my result"}, {"label": "eGFR > 90", "value": "eGFR > 90"}, {"label": "eGFR 85-89", "value": "eGFR 85-89"}, {"label": "eGFR 80-84", "value": "eGFR 80-84"}, {"label": "eGFR 75-79", "value": "eGFR 75-79"}, {"label": "eGFR 70-74", "value": "eGFR 70-74"}, {"label": "eGFR 65-69", "value": "eGFR 65-69"}, {"label": "eGFR 60-64", "value": "eGFR 60-64"}, {"label": "eGFR 55-59", "value": "eGFR 55-59"}, {"label": "eGFR 50-54", "value": "eGFR 50-54"}, {"label": "eGFR 45-49", "value": "eGFR 45-49"}, {"label": "eGFR < 45", "value": "eGFR < 45"}]}, "type": "select", "input": true, "label": "What was your most recent eGFR measurement?", "widget": "html5", "validate": {"required": true}, "tableView": true, "placeholder": "Select the value range", "confirm_label": "eGFR Value Range:", "customConditional": "show = data.prior_tests_completed?.egfr;"}, {"key": "heading_urine_acr", "html": "</br><h3>Urine Albumin-to-Creatinine Ratio (ACR)</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.prior_tests_completed?.urine_acr;"}, {"key": "last_urine_acr_test", "data": {"values": [{"label": "<1 month ago", "value": "Less than 1 month ago"}, {"label": "1-2 months ago", "value": "1-2 months ago"}, {"label": "2-3 months ago", "value": "2-3 months ago"}, {"label": "3-4 months ago", "value": "3-4 months ago"}, {"label": "4-5 months ago", "value": "4-5 months ago"}, {"label": "5-6 months ago", "value": "5-6 months ago"}, {"label": "6-12 months ago", "value": "6-12 months ago"}, {"label": "12-24 months ago", "value": "12-24 months ago"}, {"label": "24+ months ago", "value": "More than 24 months ago"}, {"label": "Never had one", "value": "Never had one"}]}, "type": "select", "input": true, "label": "When was your last urine ACR test completed?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Last Urine ACR Test:", "customConditional": "show = data.prior_tests_completed?.urine_acr;"}, {"key": "heading_lipid_profile", "html": "</br><h3>Lipid Profile (Cholesterol)</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.prior_tests_completed?.lipid_profile;"}, {"key": "last_lipid_profile_test", "data": {"values": [{"label": "<1 month ago", "value": "Less than 1 month ago"}, {"label": "1-2 months ago", "value": "1-2 months ago"}, {"label": "2-3 months ago", "value": "2-3 months ago"}, {"label": "3-4 months ago", "value": "3-4 months ago"}, {"label": "4-5 months ago", "value": "4-5 months ago"}, {"label": "5-6 months ago", "value": "5-6 months ago"}, {"label": "6-12 months ago", "value": "6-12 months ago"}, {"label": "12-24 months ago", "value": "12-24 months ago"}, {"label": "24+ months ago", "value": "More than 24 months ago"}, {"label": "Never had one", "value": "Never had one"}]}, "type": "select", "input": true, "label": "When was your last lipid profile test completed?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Last Lipid Profile Test:", "customConditional": "show = data.prior_tests_completed?.lipid_profile;"}, {"key": "heading_lipid_profile_results", "html": "</br><h3>Lipid Profile Results</h3><p>Select any abnormalities found in your test results.</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.last_lipid_profile_test !== undefined;"}, {"key": "lipid_profile_abnormalities", "type": "selectboxes", "input": true, "label": "Were any of the following findings reported in your lipid profile?", "values": [{"label": "High LDL", "value": "high_ldl"}, {"label": "High Triglycerides", "value": "high_triglycerides"}, {"label": "High HDL", "value": "high_hdl"}, {"label": "Normal HDL", "value": "normal_hdl"}, {"label": "Normal lipid profile", "value": "normal_profile"}, {"label": "I don't remember my values", "value": "dont_remember"}, {"label": "I don't remember them but was told they were normal", "value": "dont_remember_normal"}], "tooltip": "Select all that apply based on your most recent test results.", "validate": {"required": true}, "tableView": true, "customClass": "mt-n3", "confirm_label": "Lipid Profile Abnormalities:", "customConditional": "show = data.last_lipid_profile_test !== undefined;"}, {"key": "heading_diabetes_tests", "html": "</br><h3>Diabetes Testing (HbA1c or Fasting Blood Glucose)</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.prior_tests_completed?.a1c === true || data.prior_tests_completed?.fasting_glucose === true;"}, {"key": "last_a1c_test", "data": {"values": [{"label": "<1 month ago", "value": "Less than 1 month ago"}, {"label": "1-2 months ago", "value": "1-2 months ago"}, {"label": "2-3 months ago", "value": "2-3 months ago"}, {"label": "3-4 months ago", "value": "3-4 months ago"}, {"label": "4-5 months ago", "value": "4-5 months ago"}, {"label": "5-6 months ago", "value": "5-6 months ago"}, {"label": "6-12 months ago", "value": "6-12 months ago"}, {"label": "12-24 months ago", "value": "12-24 months ago"}, {"label": "24+ months ago", "value": "More than 24 months ago"}, {"label": "Never had one", "value": "Never had one"}]}, "type": "select", "input": true, "label": "When was your last HbA1c test completed?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Last A1c Test:", "customConditional": "show = data.prior_tests_completed?.a1c === true;"}, {"key": "recent_a1c_value", "data": {"values": [{"label": "I don't remember my result", "value": "dont_remember"}, {"label": "Below 5.7%", "value": "below_5_7"}, {"label": "5.7% - 6.4%", "value": "5_7_6_4"}, {"label": "6.5% - 6.9%", "value": "6_5_6_9"}, {"label": "7.0% - 7.9%", "value": "7_0_7_9"}, {"label": "8.0% - 8.9%", "value": "8_0_8_9"}, {"label": "9.0% - 9.9%", "value": "9_0_9_9"}, {"label": "10.0% or higher", "value": "10_or_higher"}]}, "type": "select", "input": true, "label": "What was your most recent HbA1c result?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Recent A1c Value:", "customConditional": "show = data.prior_tests_completed?.a1c === true;"}, {"key": "last_fasting_glucose_test", "data": {"values": [{"label": "<1 month ago", "value": "Less than 1 month ago"}, {"label": "1-2 months ago", "value": "1-2 months ago"}, {"label": "2-3 months ago", "value": "2-3 months ago"}, {"label": "3-4 months ago", "value": "3-4 months ago"}, {"label": "4-5 months ago", "value": "4-5 months ago"}, {"label": "5-6 months ago", "value": "5-6 months ago"}, {"label": "6-12 months ago", "value": "6-12 months ago"}, {"label": "12-24 months ago", "value": "12-24 months ago"}, {"label": "24+ months ago", "value": "More than 24 months ago"}, {"label": "Never had one", "value": "Never had one"}]}, "type": "select", "input": true, "label": "When was your last fasting blood glucose test completed?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Last Fasting Glucose Test:", "customConditional": "show = data.prior_tests_completed?.fasting_glucose === true;"}, {"key": "recent_fbg_value", "data": {"values": [{"label": "I don't remember my result", "value": "dont_remember"}, {"label": "Below 5.6 mmol/L [<100 mg/dL]", "value": "Below 5.6 mmol/L"}, {"label": "5.6 - 6.9 mmol/L [100-125 mg/dL]", "value": "5.6 - 6.9 mmol/L"}, {"label": "7.0 - 7.9 mmol/L [126-142 mg/dL]", "value": "7.0 - 7.9 mmol/L"}, {"label": "8.0 - 8.9 mmol/L [143-160 mg/dL]", "value": "8.0 - 8.9 mmol/L"}, {"label": "9.0 - 9.9 mmol/L [161-178 mg/dL]", "value": "9.0 - 9.9 mmol/L"}, {"label": "10.0 - 11.9 mmol/L [179-214 mg/dL]", "value": "10.0 - 11.9 mmol/L"}, {"label": "12.0+ mmol/L [215+ mg/dL]", "value": "12.0+ mmol/L"}]}, "type": "select", "input": true, "label": "What was your most recent <strong>fasting blood glucose</strong> (FBG) result?", "widget": "html5", "validate": {"required": true}, "tableView": true, "description": "Select the range that matches your lab result. Canadian units shown (mmol/L), with U.S. units in [mg/dL].", "confirm_label": "Recent FBG Value:", "customConditional": "show = data.prior_tests_completed?.fasting_glucose === true && data.last_fasting_glucose_test !== 'never_had';"}, {"key": "heading_cbc", "html": "<h3>CBC (Complete Blood Count)</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.prior_tests_completed?.cbc;"}, {"key": "last_cbc_test", "data": {"values": [{"label": "<1 month ago", "value": "Less than 1 month ago"}, {"label": "1-2 months ago", "value": "1-2 months ago"}, {"label": "2-3 months ago", "value": "2-3 months ago"}, {"label": "3-4 months ago", "value": "3-4 months ago"}, {"label": "4-5 months ago", "value": "4-5 months ago"}, {"label": "5-6 months ago", "value": "5-6 months ago"}, {"label": "6-12 months ago", "value": "6-12 months ago"}, {"label": "12-24 months ago", "value": "12-24 months ago"}, {"label": "24+ months ago", "value": "More than 24 months ago"}, {"label": "Never had one", "value": "Never had one"}]}, "type": "select", "input": true, "label": "When was your last CBC test completed?", "widget": "html5", "validate": {"required": true}, "tableView": true, "placeholder": "Select the time range", "confirm_label": "Last CBC Test:", "customConditional": "show = data.prior_tests_completed?.cbc;"}, {"key": "prior_cbc_value", "data": {"values": [{"label": "I don't remember my result", "value": "I don't remember my result"}, {"label": "< 70 g/L", "value": "Less than 70 g/L"}, {"label": "70-100 g/L", "value": "70-100 g/L"}, {"label": "101-120 g/L", "value": "101-120 g/L"}, {"label": "121-150 g/L", "value": "121-150 g/L"}, {"label": "> 150 g/L", "value": "Greater than 150 g/L"}]}, "type": "select", "input": true, "label": "What was your most recent hemoglobin level (CBC result)?", "widget": "html5", "validate": {"required": true}, "tableView": true, "placeholder": "Select your hemoglobin level", "confirm_label": "Hemoglobin Level:", "customConditional": "show = data.prior_tests_completed?.cbc;"}, {"key": "heading_testosterone", "html": "</br><h3>Total Testosterone</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.prior_tests_completed?.testosterone === true;"}, {"key": "last_testosterone_test", "type": "select", "input": true, "label": "When was your last <strong>testosterone</strong> blood test completed?", "data": {"values": [{"label": "<1 month ago", "value": "Less than 1 month ago"}, {"label": "1-2 months ago", "value": "1-2 months ago"}, {"label": "2-3 months ago", "value": "2-3 months ago"}, {"label": "3-4 months ago", "value": "3-4 months ago"}, {"label": "4-5 months ago", "value": "4-5 months ago"}, {"label": "5-6 months ago", "value": "5-6 months ago"}, {"label": "6-12 months ago", "value": "6-12 months ago"}, {"label": "12-24 months ago", "value": "12-24 months ago"}, {"label": "24+ months ago", "value": "More than 24 months ago"}, {"label": "Never had one", "value": "Never had one"}]}, "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Last Testosterone Test:", "customConditional": "show = data.prior_tests_completed?.testosterone === true;"}, {"key": "recent_testosterone_value", "type": "select", "input": true, "label": "What was your most recent <strong>total testosterone</strong> result?", "data": {"values": [{"label": "I don't remember my result", "value": "dont_remember"}, {"label": "Below 8 nmol/L (<230 ng/dL)", "value": "lt_8"}, {"label": "8–12 nmol/L (230-345 ng/dL)", "value": "8_12"}, {"label": "12–18 nmol/L (346-520 ng/dL)", "value": "12_18"}, {"label": "Above 18 nmol/L (>520 ng/dL)", "value": "gt_18"}]}, "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Testosterone Level:", "description": "Ranges shown in Canadian units (nmol/L) with U.S. units (ng/dL) in parentheses.", "customConditional": "show = data.prior_tests_completed?.testosterone === true && data.last_testosterone_test !== 'Never had one';"}, {"key": "heading_tsh", "html": "</br><h3>TSH (Thyroid Stimulating Hormone)</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.prior_tests_completed?.tsh;"}, {"key": "last_tsh_test", "type": "select", "input": true, "label": "When was your last TSH test completed?", "widget": "html5", "validate": {"required": true}, "tableView": true, "data": {"values": [{"label": "<1 month ago", "value": "Less than 1 month ago"}, {"label": "1-2 months ago", "value": "1-2 months ago"}, {"label": "2-3 months ago", "value": "2-3 months ago"}, {"label": "3-4 months ago", "value": "3-4 months ago"}, {"label": "4-5 months ago", "value": "4-5 months ago"}, {"label": "5-6 months ago", "value": "5-6 months ago"}, {"label": "6-12 months ago", "value": "6-12 months ago"}, {"label": "12-24 months ago", "value": "12-24 months ago"}, {"label": "24+ months ago", "value": "More than 24 months ago"}, {"label": "Never had one", "value": "Never had one"}]}, "confirm_label": "Last TSH Test:", "customConditional": "show = data.prior_tests_completed?.tsh;"}, {"key": "prior_tsh_value", "type": "select", "input": true, "label": "What was your most recent TSH level?", "widget": "html5", "validate": {"required": true}, "tableView": true, "data": {"values": [{"label": "I don't remember my result", "value": "dont_remember"}, {"label": "Below 0.1 mIU/L (very low)", "value": "lt_0.1"}, {"label": "0.1 - 0.3 mIU/L (low)", "value": "0.1_0.3"}, {"label": "0.4 - 4.0 mIU/L (normal range)", "value": "0.4_4.0"}, {"label": "4.1 - 5.0 mIU/L (mildly elevated)", "value": "4.1_5.0"}, {"label": "5.1 - 7.5 mIU/L (borderline high)", "value": "5.1_7.5"}, {"label": "7.6 - 10 mIU/L (moderately high)", "value": "7.6_10"}, {"label": "Above 10 mIU/L (high, likely hypothyroidism)", "value": "gt_10"}]}, "confirm_label": "TSH Level:", "customConditional": "show = data.prior_tests_completed?.tsh;"}, {"key": "overdue_labs", "type": "textfield", "input": true, "hidden": true, "disabled": true, "tableView": true, "clearOnHide": false, "refreshOn": "data", "calculateValue": "var pc = data.prior_tests_completed || {};   \nvar overdue = [];\nfunction isOld(v){ return ['12-24 months ago','More than 24 months ago','24+ months ago','Never had one'].includes(v); }\nfunction add(n){ if(!overdue.includes(n)) overdue.push(n); }\nfunction maybeAdd(flag, lastVal, name){ if(!flag || isOld(lastVal)) add(name); }\nmaybeAdd(pc.egfr,            data.last_kidney_function_test,   'kidney function (eGFR)');\nmaybeAdd(pc.lipid_profile,   data.last_lipid_profile_test,    'lipid profile');\nmaybeAdd(pc.a1c,             data.last_a1c_test,              'HbA1c');\nmaybeAdd(pc.fasting_glucose, data.last_fasting_glucose_test,  'fasting glucose');\nmaybeAdd(pc.cbc,             data.last_cbc_test,              'CBC');\nmaybeAdd(pc.testosterone,    data.last_testosterone_test,     'total testosterone');\nmaybeAdd(pc.tsh,             data.last_tsh_test,              'TSH');\nvalue = overdue.join(', ');"}, {"key": "lab_recommendation_list", "type": "content", "input": false, "label": "Content", "html": "<div class='alert alert-info'><strong>Up-to-date blood work can uncover silent issues—such as plaque build-up, high cholesterol, or high blood sugar—that often contribute to erection problems and overall health. Given your testing history, we recommend the following tests:</strong> {{ data.overdue_labs }}</div>", "tableView": false, "refreshOn": "data.overdue_labs", "refreshOnChange": true, "customConditional": "show = data.overdue_labs && data.overdue_labs.length > 0 && data.prior_tests_completed && Object.values(data.prior_tests_completed).some(v => v === true);"}, {"key": "offer_lab_requisition", "type": "radio", "input": true, "label": "Would you like us to include a laboratory requisition for these test(s) with your medication plan? Lab testing is covered with your OHIP health card.", "confirm_label": "Lab requisition requested:", "values": [{"label": "Yes, please send a requisition", "value": "yes"}, {"label": "No, I’ll arrange testing elsewhere / decline", "value": "no"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "customClass": "alert alert-success p-3", "refreshOn": "data.overdue_labs", "refreshOnChange": true, "clearOnHide": true, "customConditional": "show = data.overdue_labs && data.overdue_labs.length > 0 && data.prior_tests_completed && Object.values(data.prior_tests_completed).some(v => v === true);"}, {"key": "intake_denial_reasons", "type": "textfield", "input": true, "label": "Intake Denial Reasons:", "hidden": true, "disabled": true, "multiple": true, "adminFlag": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "value = [];", "refreshOnChange": true}, {"key": "intake_kind", "type": "textfield", "input": true, "label": "Intake Kind", "hidden": true, "disabled": true, "tableView": true, "clearOnHide": false, "calculateValue": "value = !data.intake_denial_reasons?.length ? 'mail' : 'chat';"}, {"key": "intake_mt_key", "type": "textfield", "input": true, "label": "Intake Mail Template Key", "hidden": true, "disabled": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.intake_kind=='mail'?'intake-ed':'intake-denial'"}, {"key": "allowed_rt_keys", "type": "textfield", "input": true, "label": "Allowed Rep<PERSON>", "hidden": true, "disabled": true, "multiple": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.intake_mt_key=='intake-denial'?[]:['get-req','get-rx','appointment-intake','edit-intake']"}]}