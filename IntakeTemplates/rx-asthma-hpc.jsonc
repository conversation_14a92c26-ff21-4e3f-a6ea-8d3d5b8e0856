{"components": [{"key": "heading_asthma", "html": "<h1><strong>Asthma Care</strong></h1><p>Please answer the following questions to assist us in providing you with the best care for your asthma. Your responses are vital for a tailored treatment plan.</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "heading_reason_consult", "html": "</br><h4>Why You're Reaching Out About Your Asthma Today</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "consult_reason", "type": "selectboxes", "input": true, "label": "I would like help with:", "inline": false, "values": [{"label": "Renewal of a previous asthma prescription", "value": "renewal"}, {"label": "Managing a current asthma flare-up", "value": "current_flare"}, {"label": "Preventive maintenance to avoid future exacerbations", "value": "maintenance"}, {"label": "Managing side-effects of my current treatment", "value": "side_effects"}, {"label": "Change in treatment options (e.g., switching inhalers or medicines)", "value": "change_options"}, {"label": "Other", "value": "other"}, {"label": "None of the above", "value": "none", "customClass": "mt-n3"}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "confirm_label": "Reasons for consultation:", "optionsLabelPosition": "right"}, {"key": "consult_reason_other", "type": "textarea", "input": true, "label": "Please tell us more:", "tableView": true, "autoExpand": false, "confirm_label": "Other reason:", "customConditional": "show = data.consult_reason && (data.consult_reason.other === true || data.consult_reason.none === true);"}, {"key": "heading_diagnosis_details", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "html": "<h3><strong>Your Diagnosis & Testing Details</strong></h3>"}, {"key": "age_at_diagnosis_or_first_puffer", "type": "number", "input": true, "label": "How old were you when you were first told you had asthma - or, if no one has formally diagnosed you, when you first started using an asthma puffer/inhaler?", "widget": "html5", "validate": {"min": 0, "integer": true}, "suffix": "years", "confirm_label": "Age at diagnosis or first puffer use:", "tableView": true}, {"key": "asthma_diagnosis_type", "type": "selectboxes", "input": true, "inputType": "checkbox", "label": "If a clinician has labelled your asthma, which description did they use? (Select all that apply)", "values": [{"label": "Exercise-induced asthma (symptoms mainly during or after physical activity)", "value": "exercise_induced"}, {"label": "Cold-triggered asthma (symptoms mainly in cold air or when you have a cold/flu)", "value": "cold_triggered"}, {"label": "Allergy-triggered asthma (pollen, pets, dust, etc.)", "value": "allergy_triggered"}, {"label": "Irritant-triggered asthma (smoke, pollution, strong smells)", "value": "irritant_triggered"}, {"label": "Not sure / doctor didn't specify", "value": "unknown"}, {"label": "Other", "value": "other"}], "tableView": true, "confirm_label": "Asthma type:", "validate": {"required": true}, "optionsLabelPosition": "right"}, {"key": "asthma_diagnosis_type_other", "type": "textarea", "input": true, "label": "Please specify:", "tableView": true, "autoExpand": false, "confirm_label": "Other description:", "customConditional": "show = data.asthma_diagnosis_type?.other === true;"}, {"key": "diagnosing_provider", "type": "radio", "input": true, "label": "Who provided this diagnosis?", "inline": false, "values": [{"label": "Family doctor", "value": "family_doctor"}, {"label": "Respirologist / Pulmonologist", "value": "pulmonologist"}, {"label": "Nurse practitioner", "value": "nurse_practitioner"}, {"label": "Allergist / Immunologist", "value": "allergist"}, {"label": "Self-diagnosis", "value": "self"}, {"label": "Other", "value": "other"}], "optionsLabelPosition": "right", "tableView": true, "confirm_label": "Diagnosing provider:", "validate": {"required": true}}, {"key": "diagnosing_provider_other", "type": "textarea", "input": true, "label": "Please specify who diagnosed you:", "tableView": true, "autoExpand": false, "confirm_label": "Other provider:", "customConditional": "show = data.diagnosing_provider === 'other';"}, {"key": "testing_journey_blurb", "type": "content", "input": false, "label": "Info box", "tableView": false, "refreshOnChange": false, "html": "<div class='alert alert-info'><h4><strong>Common Tests When Asthma Is First Suspected</strong></h4><ul><li><strong>Clinic breathing test</strong> (sometimes called a spirometry or PFT) - you blow into a tube so a machine measures how well your lungs move air in and out.</li><li><strong>Allergy tests</strong> - small skin pricks or a blood sample look for things you might be allergic to.</li><li><strong>Chest imaging</strong> - an X-ray or CT scan checks for other causes of breathing trouble.</li><li><strong>Other tests</strong> - for example, a gentle &quot;challenge&quot; test where you inhale a safe medicine to see if your airways tighten.</li></ul><p><em>Even if you were never formally tested, many people notice asthma-like symptoms after a bad chest infection (such as COVID-19) and get relief by using a rescue inhaler. Let us know below what applies to you.</em></p></div>"}, {"key": "diagnostic_tests_completed", "type": "selectboxes", "input": true, "label": "Have you ever had any of these tests? (Select all that apply)", "inline": false, "values": [{"label": "Clinic breathing test (spirometry / PFT)", "value": "breathing_test"}, {"label": "Allergy test (skin prick or blood)", "value": "allergy_test"}, {"label": "Chest X-ray or CT scan", "value": "imaging"}, {"label": "No formal tests", "value": "no_tests"}, {"label": "Other", "value": "other"}], "optionsLabelPosition": "right", "tableView": true, "confirm_label": "Diagnostic tests completed:", "validate": {"required": true}}, {"key": "diagnostic_tests_completed_other", "type": "textarea", "input": true, "label": "Please describe any other tests:", "tableView": true, "autoExpand": false, "confirm_label": "Other tests:", "customConditional": "show = data.diagnostic_tests_completed && data.diagnostic_tests_completed.other === true;"}, {"key": "methacholine_challenge_test", "type": "radio", "confirm_label": "Methacholine challenge test:", "input": true, "label": "During your clinic breathing test, did you do a “methacholine challenge”?<br/><ul><li>You breathe in tiny doses of a medicine called methacholine.</li><li>If your airways tighten, it helps confirm asthma.</li></ul>", "inline": false, "values": [{"label": "Yes", "value": "yes"}, {"label": "No / Not that I know", "value": "no"}, {"label": "I'm not sure", "value": "unsure"}], "optionsLabelPosition": "right", "tableView": true, "customConditional": "show = data.diagnostic_tests_completed && data.diagnostic_tests_completed.breathing_test === true;"}, {"key": "testing_location_in_na", "type": "radio", "input": true, "confirm_label": "Testing location in North America:", "label": "Were these asthma-related tests done in Canada or the United States?", "inline": false, "values": [{"label": "Yes - in Canada or the U.S.", "value": "north_america"}, {"label": "No - outside Canada / the U.S. or not sure", "value": "outside_or_unsure"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = (data.prior_asthma_diagnosis === 'yes') || (data.diagnostic_tests_completed && !data.diagnostic_tests_completed.no_tests);"}, {"key": "control_check_heading", "type": "content", "input": false, "label": "Content", "html": "<br/><h4>Asthma Control</h4>", "tableView": false, "refreshOnChange": false}, {"key": "rescue_puffs_week", "type": "radio", "confirm_label": "Weekly rescue puffs:", "input": true, "label": "In a typical week, about how many <strong>TOTAL</strong> puffs of your blue puffer do you take (exclude puffs taken just before exercise)?", "values": [{"label": "0-1 puff", "value": "0_1"}, {"label": "2-4 puffs", "value": "2_4"}, {"label": "5-8 puffs", "value": "5_8"}, {"label": "More than 8 puffs", "value": "9_plus"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "rescue_max_day", "type": "radio", "confirm_label": "Rescue puffs on busiest day:", "input": true, "label": "On your busiest breathing day in the past 4&nbsp;weeks, how many TIMES did you need the blue puffer (count separate uses at least 3&nbsp;hours apart)?", "values": [{"label": "0-1 time", "value": "0_1"}, {"label": "2 times", "value": "2"}, {"label": "3-4 times", "value": "3_4"}, {"label": "5 or more", "value": "5_plus"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "rescue_canisters_year", "type": "radio", "confirm_label": "Blue-puffer canisters in last year:", "input": true, "label": "Roughly how many full blue-puffer canisters (200-dose) did you use in the last 12&nbsp;months?", "values": [{"label": "0-1 canister", "value": "0_1"}, {"label": "2 canisters", "value": "2"}, {"label": "3-5 canisters", "value": "3_5"}, {"label": "6 or more", "value": "6_plus"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "step_up_recommendation", "type": "content", "input": false, "label": "Recommendation", "html": "<div class='alert alert-success' style='border-left:4px solid #28a745;padding:0.75rem;'>\n <p><strong>Your asthma could be better controlled.</strong></p>\n <p>A daily preventer (maintenance) inhaler-steroid-only or combination-usually helps keep symptoms in check.</p>\n</div>", "tableView": false, "customConditional": "show = (\n /* > 2 blue-puffer DAYS per week /\n (data.rescue_days_week === '3_6' || data.rescue_days_week === '7') ||\n / ≥ 2 NIGHT-wakes per week /\n (data.night_wake_nights === '2_3' || data.night_wake_nights === '4_plus') ||\n / ≥ 2 PUFFS per week /\n (data.rescue_puffs_week && data.rescue_puffs_week !== '0_1') ||\n / ≥ 2 USES on any single day /\n (data.rescue_max_day && data.rescue_max_day !== '0_1') ||\n / ≥ 3 CANISTERS in a year */\n (data.rescue_canisters_year === '3_5' || data.rescue_canisters_year === '6_plus')\n);"}, {"key": "heading_inhaler_technique", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "html": "<h3>Inhaler Technique</h3>"}, {"key": "inhaler_technique_confidence", "type": "radio", "input": true, "inline": false, "label": "Are you confident you use your inhaler correctly?", "values": [{"label": "Yes - I'm comfortable with my technique", "value": "confident"}, {"label": "No - I'm not confident / would like a review", "value": "review"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Inhaler technique confidence:", "optionsLabelPosition": "right"}, {"key": "inhaler_technique_recommendation", "type": "content", "input": false, "label": "Recommendation", "tableView": false, "customConditional": "show = data.inhaler_technique_confidence === 'review';", "html": "<div class='alert alert-success' style='border-left:4px solid #28a745;padding:0.75rem;'><strong>Recommendation:</strong> When you pick up your medication, ask the pharmacist to watch a practice puff and review your inhalation technique. A quick demonstration greatly improves medication delivery and asthma control.</div>"}, {"key": "intake_denial_reasons", "type": "textfield", "input": true, "label": "Intake Denial Reasons:", "hidden": true, "disabled": true, "multiple": true, "adminFlag": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "value=_.concat((data.any_other_questions === true && !!data.stated_other_questions && !/^\\s*$/.test(data.stated_other_questions)?['stated_other_questions.not_blank']:[]));", "refreshOnChange": true}, {"key": "intake_kind", "type": "textfield", "input": true, "label": "Intake Kind", "hidden": true, "disabled": true, "tableView": true, "clearOnHide": false, "calculateValue": "value = !data.intake_denial_reasons?.length ? 'mail' : 'chat';"}, {"key": "intake_mt_key", "type": "textfield", "input": true, "label": "Intake Mail Template Key", "hidden": true, "disabled": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.intake_kind=='mail'?'intake-asthma-rx':'intake-denial'"}, {"key": "allowed_rt_keys", "type": "textfield", "input": true, "label": "Allowed Rep<PERSON>", "hidden": true, "disabled": true, "multiple": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.intake_mt_key=='intake-denial'?[]:['get-rx','appointment-intake','edit-intake']"}]}