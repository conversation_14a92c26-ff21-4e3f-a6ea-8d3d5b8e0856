{"components": [{"key": "mercury_dietary_exposures", "type": "selectboxes", "input": true, "label": "Do you regularly eat any of the following? (Select all that apply)", "confirm_label": "Dietary mercury exposure:", "widget": "html5", "optionsLabelPosition": "right", "values": [{"label": "Large predatory ocean fish (tuna, swordfish, shark, marlin)", "value": "predatory_fish"}, {"label": "Frequent sport-caught freshwater fish from advisory areas", "value": "freshwater_fish"}, {"label": "Shellfish harvested near industrial sites", "value": "shellfish_industrial"}], "tableView": true, "adminFlag": true}, {"key": "none_of_the_above_dietary_exposures", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "Choose an exposure or tick “None of the above”."}, "validate": {"custom": "valid = !!data.none_of_the_above_dietary_exposures || _.some(_.values(data.mercury_dietary_exposures));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "mercury_workplace_exposures", "type": "selectboxes", "input": true, "label": "Do any of these apply to your current / recent employment? (Select all that apply)", "confirm_label": "Workplace mercury exposure:", "widget": "html5", "optionsLabelPosition": "right", "values": [{"label": "Dentistry - I remove / place amalgam fillings", "value": "dental_amalgam_work"}, {"label": "Mining or smelting", "value": "mining_smelting"}, {"label": "Battery, fluorescent-bulb, or switch manufacturing", "value": "battery_bulb_mfg"}, {"label": "Laboratory work with mercury products", "value": "lab_mercury"}], "tableView": true, "adminFlag": true, "customConditional": "show = data.none_of_the_above_dietary_exposures === true && !_.some(_.values(data.mercury_dietary_exposures));"}, {"key": "none_of_the_above_workplace_exposures", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "Choose an exposure or tick “None of the above”."}, "validate": {"custom": "valid = !!data.none_of_the_above_workplace_exposures || _.some(_.values(data.mercury_workplace_exposures));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.none_of_the_above_dietary_exposures === true && !_.some(_.values(data.mercury_dietary_exposures));"}, {"key": "mercury_household_personal_exposures", "type": "selectboxes", "input": true, "label": "Do any of the following apply to you? (Select all that apply)", "confirm_label": "Household / personal mercury exposure:", "widget": "html5", "optionsLabelPosition": "right", "values": [{"label": "Have broken a mercury thermometer, thermostat, or CFL bulb", "value": "broken_mercury_item"}, {"label": "Have more than 5 dental amalgam fillings", "value": "multiple_amalgam_fillings"}, {"label": "Used imported skin-lightening creams from outside Canada", "value": "skin_lightening_creams"}, {"label": "Live near a gold-mining or metal-smelting site", "value": "environmental_nearby"}, {"label": "Consumed food known to be mercury-contaminated (e.g., psyllium)", "value": "contaminated_food"}], "tableView": true, "adminFlag": true, "customConditional": "show = (data.none_of_the_above_dietary_exposures === true && !_.some(_.values(data.mercury_dietary_exposures))) && (data.none_of_the_above_workplace_exposures === true && !_.some(_.values(data.mercury_workplace_exposures)));"}, {"key": "none_of_the_above_household_exposures", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "Choose an exposure or tick “None of the above”."}, "validate": {"custom": "valid = !!data.none_of_the_above_household_exposures || _.some(_.values(data.mercury_household_personal_exposures));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = (data.none_of_the_above_dietary_exposures === true && !_.some(_.values(data.mercury_dietary_exposures))) && (data.none_of_the_above_workplace_exposures === true && !_.some(_.values(data.mercury_workplace_exposures)));"}, {"key": "mercury_symptoms", "type": "selectboxes", "input": true, "label": "In the past 3 months have you experienced any of the following symptoms:", "confirm_label": "Mercury-related symptoms:", "widget": "html5", "optionsLabelPosition": "right", "values": [{"label": "Tremor or shakiness", "value": "tremor"}, {"label": "Pins-and-needles sensations", "value": "paresthesia"}, {"label": "Memory problems or difficulty concentrating", "value": "cognitive"}, {"label": "Unexplained fatigue", "value": "fatigue"}, {"label": "Gum inflammation", "value": "oral"}, {"label": "Skin rash / peeling", "value": "dermatitis"}], "tableView": true, "customConditional": "show = ['mercury_dietary_exposures','mercury_workplace_exposures','mercury_household_personal_exposures']\n  .some(function(k){ return data[k] && _.some(_.values(data[k])); });"}, {"key": "none_of_the_above_mercury_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "Choose a symptom or tick “None of the above”."}, "validate": {"custom": "valid = !!data.none_of_the_above_mercury_symptoms || _.some(_.values(data.mercury_symptoms));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = ['mercury_dietary_exposures','mercury_workplace_exposures','mercury_household_personal_exposures']\n  .some(function(k){ return data[k] && _.some(_.values(data[k])); });"}, {"key": "no_indications", "type": "textfield", "input": true, "label": "No Indication:", "hidden": true, "disabled": true, "multiple": false, "tableView": true, "clearOnHide": false, "defaultValue": true, "calculateValue": "value = !_.some(\n  ['mercury_dietary_exposures', 'mercury_workplace_exposures', 'mercury_household_personal_exposures'],\n  function(k){ return data[k] && _.some(_.values(data[k])); }\n);", "refreshOnChange": true}, {"key": "contraindication_keys", "type": "textfield", "input": true, "label": "Contraindication keys:", "hidden": true, "disabled": true, "multiple": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "value = _.keys(_.pickBy(data.mercury_symptoms));", "refreshOnChange": true}, {"key": "approved_html", "type": "content", "input": true, "tableView": false, "clearOnHide": false, "defaultValue": "<h3 class='text-green'>You're eligible for mercury laboratory screening.</h3><p>Your answers show a potential exposure to mercury <em>without</em> current symptoms. A blood or urine mercury test can quantify your exposure.</p><p><strong>Next steps:</strong></p><ul><li>We'll send a lab requisition to your preferred facility (fasting not required).</li><li>Attend at your convenience; results return in 3-5 business days.</li><li>A physician will review your results and message you securely with guidance.</li></ul><p>If you develop symptoms before testing, please seek in-person care.</p>"}, {"key": "rejected_html", "type": "content", "input": true, "tableView": false, "clearOnHide": false, "defaultValue": "<h3 class='text-red'>We're unable to proceed with mercury screening at this time.</h3><p>Your request was declined for one or more of the following reasons:</p><ul><li>No confirmed mercury-exposure risk in the past year.</li><li>Reported symptoms that need in-person assessment before lab testing.</li></ul><p>If you believe you selected an option in error, please go back and adjust your responses. Otherwise, consult a local clinic or emergency department if symptoms worsen.</p>"}]}