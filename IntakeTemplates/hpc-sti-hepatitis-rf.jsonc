{"components": [{"key": "hepatitis_heading", "html": "<h2 class='text-center'><strong>Hepatitis Intake History</strong></h2><p>Hepatitis B & C screening is <strong>covered by OHIP</strong> for at-risk individuals and recommended based on risk factors including country of birth. TeleTest has identified Hepatitis B & C in individuals who were previously unaware that they had it.</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.show_all && data.chief_complaint != 'treatment_only'"}, {"key": "interested_in_hep_screening", "type": "radio", "input": true, "label": "Would you like to determine your hepatitis risk for screening?", "inline": false, "values": [{"label": "I would like to determine my risk/complete screening for Hepatitis B & C", "value": true}, {"label": "I DO NOT want to determine my risk/complete screening for Hepatitis B & C", "value": false}], "validate": {"required": true}, "tableView": true, "refreshOnChange": true, "confirm_label": "Interested in hepatitis screening:", "customConditional": "show = data.show_all && data.chief_complaint != 'treatment_only'", "optionsLabelPosition": "right"}, {"key": "no_hep_b_c_birth_country_risk", "type": "radio", "input": true, "label": "Were you born in Canada, United States, United Kingdom, France, Portugal, Germany, The Netherlands, Jamaica, Mexico, Colombia, Guyana, Trinidad and Tobago, Algeria, Morocco, Lebanon, Israel, Iran, Hong Kong, India?", "inline": false, "values": [{"label": "Yes", "value": true}, {"label": "No", "value": false}], "validate": {"required": true}, "tableView": true, "defaultValue": true, "customConditional": "show = data.show_all && !!data.interested_in_hep_screening", "optionsLabelPosition": "right"}, {"key": "countries_hep_b", "type": "textfield", "input": true, "label": "Hep B endemic countries from https://www.albertadoctors.org/Advocacy/Hepatitis_B_Endemic_Countries.pdf, https://www.globalhep.org/data-profiles", "hidden": true, "disabled": true, "multiple": true, "tableView": false, "clearOnHide": false, "calculateValue": ["Albania", "Algeria", "Angola", "Azerbaijan", "Bangladesh", "Belarus", "Belize", "Benin", "Bhutan", "Brunei", "Bulgaria", "Chad", "Colombia", "Congo", "Djibouti", "Dominican Republic", "Ecuador", "Equatorial Guinea", "Eritrea", "Ethiopia", "Ghana", "Jamaica", "Kenya", "Kiribati", "Kosovo", "Laos", "Libya", "Madagascar", "Marshall Islands", "Micronesia", "Myanmar", "Namibia", "<PERSON><PERSON>", "New Zealand", "Niue", "Oman", "Pakistan", "Peru", "Samoa", "Saudi Arabia", "Sierra Leone", "Singapore", "Somalia", "South Africa", "South Korea", "South Sudan", "Sri Lanka", "Sudan", "Suriname", "Swaziland", "Syria", "Tahiti", "Tanzania", "Thailand", "Togo", "Tunisia", "Tuvalu", "Uganda", "Vietnam", "Yemen", "Zambia", "Zimbabwe"]}, {"key": "countries_hep_c", "type": "textfield", "input": true, "label": "Hep B endemic countries from https://www.albertadoctors.org/Advocacy/Hepatitis_B_Endemic_Countries.pdf, https://www.globalhep.org/data-profiles", "hidden": true, "disabled": true, "multiple": true, "tableView": false, "clearOnHide": false, "calculateValue": ["Burundi", "Egypt", "El Salvador", "Estonia", "Fed. Sts. Micronesia", "Grenada", "Guinea-Bissau", "Hungary", "Indonesia", "Iraq", "Japan", "Jordan", "Kiribati", "Latvia", "Lithuania", "Macedonia FYR", "Poland", "São Tomé and Principe", "St. Kitts and Nevis", "Taiwan", "Turkmenistan", "Ukraine"]}, {"key": "countries_hep_bc", "type": "textfield", "input": true, "label": "Hep B endemic countries from https://www.albertadoctors.org/Advocacy/Hepatitis_B_Endemic_Countries.pdf, https://www.globalhep.org/data-profiles", "hidden": true, "disabled": true, "multiple": true, "tableView": false, "clearOnHide": false, "calculateValue": ["Burkina Faso", "Cambodia", "Cameroon", "Cape Verde", "Central African Republic", "China", "Côte D’Ivoire", "De. Rep. Congo", "Fiji", "Gabon", "Gambia", "Georgia", "<PERSON><PERSON><PERSON>", "Haiti", "Kazakhstan", "Kyrgystan", "Liberia", "Malawi", "Mali", "Mauritania", "Moldova", "Mongolia", "Mozambique", "Niger", "Nigeria", "<PERSON><PERSON>", "Papau New Guinea", "Philippines", "Romania", "Russia", "Rwanda", "Senegal", "Soloman Islands", "Tajikstan", "Tonga", "Turkey", "Uzbekistan", "Vanuatu"]}, {"key": "country_of_birth", "data": {"custom": "values = ['NOT ON THE LIST'].concat((data.countries_hep_bc.concat(data.countries_hep_c,data.countries_hep_b)).sort())"}, "type": "select", "input": true, "label": "Please select the country of your birth (or “NOT ON LIST”):", "widget": "html5", "dataSrc": "custom", "tableView": true, "customConditional": "show = data.show_all && !!data.interested_in_hep_screening && !data.no_hep_b_c_birth_country_risk"}, {"key": "previous_hep_c_test", "type": "radio", "input": true, "label": "Have you ever been tested for Hepatitis C?", "inline": false, "values": [{"label": "Yes", "value": true}, {"label": "No/Don't know", "value": false}], "validate": {"required": true}, "tableView": true, "confirm_label": "Previous Hepatitis C Test:", "customConditional": "show = data.show_all && !!data.interested_in_hep_screening", "optionsLabelPosition": "right"}, {"key": "previous_hep_b_test", "type": "radio", "input": true, "confirm_label": "Previous Hepatitis B Test:", "label": "Have you ever been tested for Hepatitis B?", "values": [{"label": "Yes", "value": true}, {"label": "No/Don't know", "value": false}], "tableView": true, "customConditional": "show = data.show_all && !!data.interested_in_hep_screening"}, {"key": "hep_b_vaccinated", "type": "radio", "input": true, "label": "Have you been fully vaccinated for Hepatitis B? (Hepatitis B vaccination is given in many countries at birth and in school in Ontario)", "values": [{"label": "Yes", "value": true}, {"label": "No/Don't know", "value": false}], "tableView": true, "confirm_label": "Fully vaccinated for Hepatitis B:", "customConditional": "show = data.show_all && !!data.interested_in_hep_screening;", "optionsLabelPosition": "right"}, {"key": "hep_b_vaccine_advice", "type": "radio", "input": true, "label": "<p>Please review your immunization records to confirm your vaccination status.</p><p>If you cannot confirm you have recieved the vaccination, or have never had Hepatitis B vaccination, it is strongly recommended.</p><p>Vaccination is free for the following individuals though health units and walk-in clinics: anyone with a history of an STI, you are a household or sexual contact of a confirmed case of Hepatitis B, you have a history of IV drug use, you were born to a mother with hepatitis B, you have liver disease, you are a man who has sex with men, you have multiple sex partners, you have had a needlestick injury in a non-healthcare setting, you have a health condition requiring frequent blood products.</p>", "values": [{"label": "I understand", "value": true}, {"label": "I do not understand", "value": false}], "tableView": true, "confirm_label": "Understands Hepatitis B vaccination advice:", "refreshOnChange": true, "customConditional": "show = data.show_all && !!data.interested_in_hep_screening && data.hep_b_vaccinated === false"}, {"key": "hep_c_positive", "type": "radio", "input": true, "label": "Have you ever tested positive or been treated for Hepatitis C?", "inline": false, "values": [{"label": "Yes", "value": true}, {"label": "No", "value": false}], "tableView": true, "confirm_label": "Hepatitis C positive:", "customConditional": "show = data.show_all && !!data.interested_in_hep_screening && data.past_std == true", "optionsLabelPosition": "right"}, {"key": "hep_c_risk_factors_since_last_test", "type": "selectboxes", "input": true, "label": "Have you had any of the following risk factors for Hepatitis C exposure ever or since your last test?", "values": [{"label": "Shared personal care items (i.e. Shared razors, toothbrushes, nail clippers and other household items that might have infected blood on them)", "value": "shared_personal_care_items"}, {"label": "Received medical care where non-sterile equipment may have been used ", "value": "medical_equipment"}, {"label": "Have been exposed to another partner's blood during sexual activity", "value": "have_been_exposed_to_blood_during_sex"}, {"label": "Received personal services (e.g., tattooing or piercing), with nonsterile or suspected non sterile equipment", "value": "non_sterile_equipment"}, {"label": "Shared drug use equipment (i.e. needles, cookers, filters, swabs)", "value": "shared_drug_equipment"}, {"label": "Engage in sex work / adult film work and it has been more than 6 months since my last screen", "value": "repeat_screen_sex_work"}, {"label": "None of the above", "value": "no_new_hepc_risk_factors"}], "validate": {"custom": "valid = (!input.no_new_hepc_risk_factors || _.keys(_.pickBy(input)).length == 1) ? true : 'If “None of the above” is checked no other boxes should be checked.'", "required": true}, "inputType": "checkbox", "tableView": true, "confirm_label": "Hepatitis C risk factors:", "customConditional": "show = data.show_all && !!data.interested_in_hep_screening", "optionsLabelPosition": "right"}, {"key": "please_explain_how_you_were_exposed_through_equipment", "type": "textfield", "input": true, "label": "Please explain how you were exposed through equipment:", "tableView": true, "autoExpand": false, "confirm_label": "Other exposure through equipment:", "customConditional": "show = data.hep_c_risk_factors_since_last_test && data.hep_c_risk_factors_since_last_test.medical_equipment;"}, {"key": "medical_recommendation_heading", "html": "<h2 style='text-align:center;'><strong><p>Medical Recommendations</strong></h2></p>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": true, "customConditional": "show = data.hep_c_recommended || data.rswb_recommended"}, {"key": "hep_c_recommended", "html": "<h2>Hepatitis C Testing</h2><p>Based on your response, we would recommend Hepatitis C testing a minimum of 60 days after your last exposure, and a repeat test at 180 days.</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "calculateValue": "value = _.some(_.values(data.hep_c_risk_factors_since_last_test)) && !data.hep_c_risk_factors_since_last_test.no_new_hepc_risk_factors", "refreshOnChange": true, "customConditional": "show = data.hep_c_recommended"}, {"key": "rswb_recommended", "html": "<p>Based on your response, it is recommend that you complete site specific testing (anal swab) for gonorrhoea/chlamydia. You can proceed to a local health clinic for swabbing by a health care provider.</p>", "type": "content", "input": false, "label": "content", "tableView": true, "calculateValue": "value = data.type_of_sex && data.type_of_sex.receive_anal", "refreshOnChange": true, "customConditional": "show = data.rswb_recommended"}, {"key": "recommend_hepb", "type": "radio", "input": true, "label": "Based on your birth country and prior screening, we recommend a once in a lifetime screen for Hepatitis B", "inline": false, "values": [{"label": "I would like to test for Hepatitis B", "value": true}, {"label": "I do not want to test for Hepatitis B", "value": false}], "validate": {"required": true}, "tableView": true, "refreshOnChange": true, "confirm_label": "Recommend Hepatitis B testing:", "customConditional": "show = data.previous_hep_b_test===false && (data.countries_hep_b.includes(data.country_of_birth) || data.countries_hep_bc.includes(data.country_of_birth))", "optionsLabelPosition": "right"}, {"key": "recommend_hepc", "type": "radio", "input": true, "label": "Based on your birth country and prior screening, we recommend a once in a lifetime screen for Hepatitis C", "inline": false, "values": [{"label": "I would like to test for Hepatitis C", "value": true}, {"label": "I do not want to test for Hepatitis C", "value": false}], "validate": {"required": true}, "tableView": true, "confirm_label": "Recommend Hepatitis C testing:", "refreshOnChange": true, "customConditional": "show = !data.hep_c_positive && ((data.previous_hep_c_test===false && (data.countries_hep_c.includes(data.country_of_birth) || data.countries_hep_bc.includes(data.country_of_birth))) || (_.some(_.values(data.hep_c_risk_factors_since_last_test)) && !data.hep_c_risk_factors_since_last_test.no_new_hepc_risk_factors))", "optionsLabelPosition": "right"}]}