{"key": "tab_epipen_quantity", "type": "panel", "title": "EpiPen Quantity & Dispensing", "input": false, "tableView": false, "components": [{"key": "epipen_quantity_header", "type": "content", "input": false, "label": "Content", "html": "</br><h4>EpiPen Quantity & Dispensing Preference</h4><p>Please tell us how many EpiPens you need. Prescriptions can be provided for up to <strong>three pens</strong>. If you need more than one, choose whether you want them dispensed together <em>(for different settings like home, work, or school)</em> or one now with a refill to pick up later.</p>", "tableView": false}, {"key": "epipen_quantity", "type": "select", "input": true, "label": "How many EpiPens do you require?", "widget": "html5", "multiple": false, "data": {"values": [{"label": "1 pen", "value": "1"}, {"label": "2 pens", "value": "2"}, {"label": "3 pens", "value": "3"}]}, "validate": {"required": true}, "tableView": true, "confirm_label": "Number of EpiPens requested:"}, {"key": "epipen_dispense_preference", "type": "radio", "input": true, "label": "If you need 2 pens, how would you like them dispensed?", "values": [{"label": "Two pens dispensed now (for different settings such as home and office/school)", "value": "two_now"}, {"label": "One pen now with a refill to pick up later", "value": "one_with_refill"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "confirm_label": "Dispensing preference:", "customConditional": "show = (data.epipen_quantity && data.epipen_quantity.toString() === '2');"}, {"key": "epipen_dispense_summary", "type": "textfield", "input": true, "label": "Patient’s EpiPen quantity/dispensing summary:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "Quantity & dispensing summary:", "calculateValue": "if (data.epipen_quantity && data.epipen_quantity.toString()==='1'){ value='1 pen requested.'; } else if (data.epipen_quantity && data.epipen_quantity.toString()==='2' && data.epipen_dispense_preference==='two_now'){ value='2 pens dispensed now (for different settings like home and work/school).'; } else if (data.epipen_quantity && data.epipen_quantity.toString()==='2' && data.epipen_dispense_preference==='one_with_refill'){ value='1 pen now + 1 refill to pick up later.'; } else if (data.epipen_quantity && data.epipen_quantity.toString()==='3'){ value='3 pens requested.'; } else { value=''; }"}, {"key": "rxts", "type": "textfield", "input": true, "label": "Rx Templates:", "hidden": true, "disabled": true, "multiple": true, "adminFlag": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "refreshOnChange": true, "confirm_label": "Selected Rx template key(s):", "calculateValue": "if (!data.epipen_quantity) { value=[]; } else { const qty=data.epipen_quantity.toString(); if (qty==='1'){ value=['epipen-single']; } else if (qty==='2' && data.epipen_dispense_preference==='two_now'){ value=['epipen-double']; } else if (qty==='2' && data.epipen_dispense_preference==='one_with_refill'){ value=['epipen-refill']; } else if (qty==='3'){ value=['epipen-triple']; } else { value=[]; } }"}]}