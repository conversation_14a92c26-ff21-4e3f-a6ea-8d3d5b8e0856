{"components": [{"key": "diagnosis_new_hsv", "type": "radio", "input": true, "label": "Are you experiencing the 1st outbreak in your lifetime?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "tableView": false, "validate": {"required": true}, "optionsLabelPosition": "right"}, {"key": "diagnosis_shingles", "type": "radio", "input": true, "label": "Are you concerned you may be experiencing a shingles outbreak?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "tableView": false, "validate": {"required": true}, "customConditional": "show = data.diagnosis_new_hsv == false;", "optionsLabelPosition": "right"}, {"key": "ocular_herpes", "type": "radio", "input": true, "label": "Are you concerned you may be experiencing a herpes outbreak in your eye?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "tableView": false, "validate": {"required": true}, "customConditional": "show = data.diagnosis_shingles == false;", "optionsLabelPosition": "right"}, {"key": "diagnosis_uncertain_hsv", "type": "radio", "input": true, "label": "Do you have a new rash that you're not sure is related to herpes?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "tableView": false, "validate": {"required": true}, "optionsLabelPosition": "right", "customConditional": "show = data.ocular_herpes == false;"}, {"key": "herpes_contraindications", "type": "selectboxes", "input": true, "label": "Have you had any of the following medical conditions:", "values": [{"label": "Bone marrow transplant", "value": "bone_marrow", "shortcut": ""}, {"label": "Kidney transplant", "value": "kidney_transplant", "shortcut": ""}, {"label": "Kidney disease", "value": "kidney_disease", "shortcut": ""}, {"label": "Advanced HIV or 'AIDS'", "value": "aids", "shortcut": ""}, {"label": "Currently breastfeeding", "value": "breastfeeding", "shortcut": ""}], "inputType": "checkbox", "tableView": false, "customConditional": "show = data.diagnosis_new_hsv == false && data.diagnosis_shingles == false && data.ocular_herpes == false && data.diagnosis_uncertain_hsv == false;", "optionsLabelPosition": "right"}, {"key": "no_hsv_contraindications", "customClass": "mt-n3", "defaultValue": false, "input": true, "label": "None of the above", "tableView": false, "validate": {"custom": "valid = _.some(_.values(data.herpes_contraindications)) || data.no_hsv_contraindications;"}, "type": "checkbox", "customConditional": "show = data.diagnosis_new_hsv == false && data.diagnosis_shingles == false && data.ocular_herpes == false && data.diagnosis_uncertain_hsv == false;", "errors": {"custom": "required, or select a symptom."}}]}