{"components": [{"key": "ella_instructions_header", "html": "<h2 class='text-center'>ELLA Emergency Contraception Intake Form</h2><p>Please complete the following questionnaire to help us better understand your needs and ensure the safe and effective use of ELLA.</p>", "type": "content", "input": false, "label": "Instructions", "tableView": false, "refreshOnChange": false}, {"key": "general_information_header", "html": "<h3>Intake History</h3>", "type": "content", "input": false, "label": "General Information", "tableView": false, "refreshOnChange": false}, {"key": "unprotected_intercourse_date_time", "type": "datetime", "input": true, "label": "What was the date and time you had unprotected intercourse? (ELLA remains effective up to 120 hours (5 days) after unprotected intercourse)", "format": "MMMM dd, yyyy hh:mm a", "widget": {"mode": "single", "type": "calendar", "format": "MMMM dd, yyyy hh:mm a", "allowInput": true, "enableTime": true, "noCalendar": false, "displayInTimezone": "viewer", "useLocaleSettings": true}, "validate": {"required": true}, "tableView": true, "enableDate": true, "enableTime": true, "confirmlabel": "Date and time of unprotected intercourse:"}, {"key": "time_since_intercourse", "type": "textfield", "input": true, "label": "Time since unprotected intercourse (in hours):", "disabled": true, "tableView": true, "confirmlabel": "Time since unprotected intercourse:", "calculateValue": "value = data.unprotected_intercourse_date_time ? Math.floor((new Date() - new Date(data.unprotected_intercourse_date_time)) / (1000 * 60 * 60)) : '';"}, {"key": "emergency_contraceptive_use_heading", "html": "<h3>Emergency Contraceptive Use</h3>", "type": "content", "input": false, "tableView": false}, {"key": "past_emergency_contraceptive_use", "type": "selectboxes", "input": true, "label": "Have you used any of the following medications in the past?", "values": [{"label": "Plan B", "value": "plan_b", "shortcut": ""}, {"label": "ELLA", "value": "ella", "shortcut": ""}], "tableView": true, "confirmlabel": "Past emergency contraceptive use:", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_emergency_contraceptives", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a medication."}, "validate": {"custom": "valid = !!data.none_of_the_above_emergency_contraceptives || _.some(_.values(data.past_emergency_contraceptive_use));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "last_dose_plan_b", "data": {"values": [{"label": "1-2 days ago", "value": "1 to 2_days"}, {"label": "3-5 days ago", "value": "3 to 5 days"}, {"label": "6 days - 2 weeks ago", "value": "6 days to 2 weeks"}, {"label": "2 - 4 weeks ago", "value": "2 to 4_weeks"}, {"label": "1 - 2 months ago", "value": "1 to 2_months"}, {"label": "2 - 6 months ago", "value": "2 to 6_months"}, {"label": "6 months - 1 year ago", "value": "6 months to 1 year"}, {"label": "1 - 2 years ago", "value": "1 to 2 years"}, {"label": "2 - 5 years ago", "value": "2 to 5 years"}, {"label": "> 5 years ago", "value": "more than 5 years ago"}]}, "type": "select", "input": true, "label": "When was your last dose of Plan B used?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirmlabel": "Last dose of Plan B:", "customConditional": "show = data.past_emergency_contraceptive_use && data.past_emergency_contraceptive_use.plan_b"}, {"key": "last_dose_ella", "data": {"values": [{"label": "< 3 days ago", "value": "less than 3 days"}, {"label": "4 days - 1 week ago", "value": "4 days to 1 week"}, {"label": "1 - 2 weeks ago", "value": "1 to 2 weeks"}, {"label": "2 - 4 weeks ago", "value": "2 to 4 weeks"}, {"label": "1 - 2 months ago", "value": "1 to 2_months"}, {"label": "2 - 6 months ago", "value": "2 to 6_months"}, {"label": "6 months - 1 year ago", "value": "6 months to 1 year"}, {"label": "1 - 2 years ago", "value": "1 to 2 years"}, {"label": "2 - 5 years ago", "value": "2 to 5 years"}, {"label": "> 5 years ago", "value": "more than 5 years ago"}]}, "type": "select", "input": true, "label": "When was your last dose of ELLA used?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirmlabel": "Last dose of ELLA:", "customConditional": "show = data.past_emergency_contraceptive_use && data.past_emergency_contraceptive_use.ella"}, {"key": "ella_tolerance", "type": "radio", "input": true, "label": "Did you tolerate the <PERSON> well without side effects?", "values": [{"label": "No side effects", "value": "none"}, {"label": "Mild side effects", "value": "mild"}, {"label": "Moderate but tolerable side effects", "value": "moderate_tolerable"}, {"label": "Severe side effects", "value": "severe", "adminFlag": true}], "validate": {"required": true}, "tableView": true, "confirmlabel": "Tolerance to ELLA:", "customConditional": "show = data.past_emergency_contraceptive_use && data.past_emergency_contraceptive_use.ella"}, {"key": "pregnancy_test_or_period", "type": "radio", "input": true, "label": "Since your last dose of ELLA, have you completed a pregnancy test or had a full period?", "values": [{"label": "Completed a pregnancy test", "value": "completed_pregnancy_test"}, {"label": "Had a full period", "value": "had_full_period"}, {"label": "Neither", "value": "neither", "adminFlag": true}], "validate": {"required": true}, "tableView": true, "confirmlabel": "Pregnancy test or period after ELLA:", "customConditional": "show = ['less than 3 days', '4 days to 1 week', '1 to 2 weeks', '2 to 4 weeks', '1 to 2 months'].includes(data.last_dose_ella)"}, {"key": "pregnancy_test_time", "data": {"values": [{"label": "Less than 12 hours ago", "value": "less than 12 hours"}, {"label": "1-3 days ago", "value": "1 to 3 days"}, {"label": "4-7 days ago", "value": "4 to 7 days"}, {"label": "1-2 weeks ago", "value": "1 to 2 weeks"}, {"label": "2-4 weeks ago", "value": "2 to 4 weeks"}, {"label": "1-3 months ago", "value": "1 to 3 months"}, {"label": "More than 3 months ago", "value": "more than 3 months"}]}, "type": "select", "input": true, "label": "When did you take a pregnancy test?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirmlabel": "Time of home pregnancy test:", "customConditional": "show = data.pregnancy_test_or_period === 'completed_pregnancy_test'"}, {"key": "typical_menstrual_flow", "type": "radio", "input": true, "label": "Was the quantity of menstrual flow during your last cycle typical for you?", "values": [{"label": "Yes, it was typical", "value": "typical"}, {"label": "No, it was lighter than usual", "value": "lighter", "adminFlag": true}, {"label": "No, it was heavier than usual", "value": "heavier", "adminFlag": true}], "validate": {"required": true}, "tableView": true, "confirmlabel": "Typical menstrual flow:", "customConditional": "show = data.pregnancy_test_or_period === 'had_full_period'"}, {"key": "current_symptoms_heading", "html": "<h3>Current Symptoms</h3>", "type": "content", "input": false, "tableView": false}, {"key": "current_symptoms", "type": "selectboxes", "input": true, "label": "Are you currently experiencing any of the following symptoms?", "values": [{"label": "Abdominal pain", "value": "abdominal_pain", "shortcut": ""}, {"label": "Pelvic pain", "value": "pelvic_pain", "shortcut": ""}, {"label": "Fevers", "value": "fevers", "shortcut": ""}, {"label": "<PERSON><PERSON>", "value": "chills", "shortcut": ""}, {"label": "<PERSON><PERSON><PERSON>", "value": "nausea", "shortcut": ""}, {"label": "Vomiting", "value": "vomiting", "shortcut": ""}], "adminFlag": true, "tableView": true, "optionsLabelPosition": "right"}, {"key": "none_of_the_above_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = !!data.none_of_the_above_symptoms || _.some(_.values(data.current_symptoms));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "current_symptoms_not_present", "type": "textfield", "input": true, "label": "You do DO NOT have the following symptoms:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "I DO NOT have the following symptoms:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.current_symptoms, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "pregnancy_status_heading", "html": "<h3>Pregnancy Status</h3>", "type": "content", "input": false, "tableView": false}, {"key": "currently_pregnant", "type": "radio", "input": true, "label": "Are you currently pregnant?", "values": [{"label": "Yes", "value": "yes", "adminFlag": true}, {"label": "No", "value": "no"}, {"label": "I'm unsure", "value": "unsure", "adminFlag": true}], "validate": {"required": true}, "tableView": true, "confirmlabel": "Currently pregnant:"}, {"key": "menstrual_cycle_regularity", "type": "radio", "input": true, "label": "Do you have regular or irregular cycles?", "values": [{"label": "Regular", "value": "regular"}, {"label": "Irregular", "value": "irregular"}, {"label": "I'm not sure", "value": "not_sure"}], "tooltip": "Regular cycles occur consistently within a range of 21-35 days, though the exact number of days can vary slightly from month to month. Irregular cycles may vary significantly in length, making it difficult to predict the timing of your next period. If you're unsure, consider whether your cycles typically fall within the 21-35 day range or if they are unpredictable.", "validate": {"required": true}, "tableView": true, "confirmlabel": "Menstrual cycle regularity:"}, {"key": "late_for_cycle", "type": "radio", "input": true, "label": "Are you currently late for your cycle?", "values": [{"label": "Yes", "value": "yes", "adminFlag": true}, {"label": "No", "value": "no"}, {"label": "I'm not sure", "value": "not_sure", "adminFlag": true}], "validate": {"required": true}, "tableView": true, "confirmlabel": "Currently late for cycle:"}, {"key": "pregnancy_test_time", "data": {"values": [{"label": "Less than 12 hours ago", "value": "less than 12 hours"}, {"label": "1-3 days ago", "value": "1 to 3 days"}, {"label": "4-7 days ago", "value": "4 to 7 days"}, {"label": "1-2 weeks ago", "value": "1 to 2 weeks"}, {"label": "2-4 weeks ago", "value": "2 to 4 weeks"}, {"label": "1-3 months ago", "value": "1 to 3 months"}, {"label": "More than 3 months ago", "value": "more than 3 months"}]}, "type": "select", "input": true, "label": "When did you last complete a pregnancy test?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirmlabel": "Time of home pregnancy test:", "customConditional": "show = data.home_pregnancy_test === 'yes'"}, {"key": "pregnancy_test_understanding", "type": "radio", "input": true, "label": "ELLA cannot be taken if you are currently pregnant. To keep you safe and ensure ELLA works as intended, it's important to confirm whether or not you are pregnant before taking it. If you haven't taken a home pregnancy test, please complete one and take a picture to show the pharmacist when picking up your prescription. Home pregnancy tests can be purchased at any pharmacy. Do you understand this step?", "values": [{"label": "I understand and will complete a test", "value": "understand"}, {"label": "I don't understand and need more help", "value": "do_not_understand"}], "validate": {"required": true}, "confirmlabel": "Understanding of pregnancy test requirement:", "customConditional": "show = data.home_pregnancy_test === 'no' || data.pregnancy_test_or_period === 'neither' || data.typical_menstrual_flow === 'lighter' || data.typical_menstrual_flow === 'heavier'"}, {"key": "current_contraceptives_heading", "html": "<h3>Current Contraceptives</h3>", "type": "content", "input": false, "tableView": false}, {"key": "current_contraceptive_methods_combined", "type": "selectboxes", "input": true, "label": "Do you currently use any form of contraception? If so, please select all that apply:", "values": [{"label": "I do not use contraception", "value": "none"}, {"label": "Birth control pills", "value": "birth_control_pills"}, {"label": "Condoms", "value": "condoms"}, {"label": "Intrauterine Device (IUD)", "value": "iud"}, {"label": "Implant", "value": "implant"}, {"label": "Injection (e.g., Depo-Provera)", "value": "injection"}, {"label": "Patch", "value": "patch"}, {"label": "Vaginal ring", "value": "vaginal_ring"}, {"label": "Natural family planning", "value": "natural_family_planning"}, {"label": "Other", "value": "other"}], "validate": {"required": true}, "tableView": true, "confirmlabel": "Current contraceptive methods:"}, {"key": "alternatives_to_ella_heading", "html": "<h3>Alternatives to ELLA</h3>", "type": "content", "input": false}, {"key": "preferred_method", "type": "radio", "input": true, "label": "A copper IUD is another highly effective method for emergency contraception. It can be inserted by a healthcare provider and is more than 99% effective at preventing pregnancy. It may also provide long-term contraception if desired.<br><ul><li>Effectiveness: Over 99% at preventing pregnancy.</li><li>Duration: Can be left in place for up to 10 years for ongoing contraception.</li><li>Additional benefits: Does not interfere with hormonal cycles.</li><li>Requires a healthcare provider for insertion.</li></ul><br>Which emergency contraception method do you prefer?", "values": [{"label": "Copper IUD", "value": "copper_iud"}, {"label": "ELLA", "value": "ella"}], "validate": {"required": true}, "tableView": true, "confirmlabel": "Preferred emergency contraception method:"}, {"key": "breastfeeding_header", "html": "<h3>Breastfeeding</h3>", "type": "content", "input": false, "label": "Breastfeeding", "tableView": false, "refreshOnChange": false}, {"key": "currently_breastfeeding", "type": "radio", "input": true, "label": "Are you currently breastfeeding?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "confirmlabel": "Currently breastfeeding:"}, {"key": "breastfeeding_advice", "type": "radio", "input": true, "label": "The manufacturer of ELLA reports that small amounts of the medication are excreted into breast milk, but it is generally considered acceptable to continue breastfeeding as the quantities are minimal. However, the CDC recommends discarding breast milk for 24 hours after taking ELLA. Please confirm your decision:", "values": [{"label": "I will continue to breastfeed", "value": "continue_breastfeeding"}, {"label": "I will discard breast milk for 24 hours after the dose", "value": "discard_breast_milk"}], "validate": {"required": true}, "confirmlabel": "Breastfeeding decision:", "customConditional": "show = data.currently_breastfeeding === 'yes'"}, {"key": "medication_interactions_heading", "html": "<h3>Medication Interactions</h3>", "type": "content", "input": false, "tableView": false}, {"key": "recent_plan_b_usage", "type": "radio", "input": true, "label": "Have you taken a dose of Plan B (Levonorgestrel) within the last 5 days?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "confirmlabel": "Recent Plan B usage:"}, {"key": "plan_b_advice", "html": "<div class='alert alert-warning'>Important Information: If you've taken Plan B (Levonorgestrel) in the last 5 days, ELLA may not work as effectively. This is because both medications act on the same progesterone receptors, and Plan B can interfere with how ELLA prevents ovulation.</div>", "type": "content", "input": false, "customConditional": "show = data.recent_plan_b_usage === 'yes'"}, {"key": "plan_b_confirmation", "type": "radio", "input": true, "label": "Do you still wish to pursue ELLA knowing it may be less effective?", "values": [{"label": "Yes, I understand and still wish to pursue ELLA.", "value": "yes"}, {"label": "No, I do not wish to pursue ELLA.", "value": "no"}], "validate": {"required": true}, "tableView": true, "confirmlabel": "Confirmation to pursue ELLA despite Plan B usage:", "customConditional": "show = data.recent_plan_b_usage === 'yes'"}, {"key": "medications_that_interfere", "type": "selectboxes", "input": true, "label": "Have you taken any of the following medications recently?", "values": [{"label": "Rifampin (antibiotic)", "value": "rifampin", "shortcut": ""}, {"label": "Phenytoin, Carbamazepine, or Phenobarbital (anticonvulsants)", "value": "anticonvulsants", "shortcut": ""}, {"label": "<PERSON>. John's Wort (herbal supplement)", "value": "st_johns_wort", "shortcut": ""}, {"label": "Ritonavir or Efavirenz (HIV medications)", "value": "hiv_medications", "shortcut": ""}, {"label": "Griseofulvin (antifungal)", "value": "griseofulvin", "shortcut": ""}], "tableView": true, "confirmlabel": "Medications that interfere with ELLA:", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_interfering_medications", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a medication."}, "validate": {"custom": "valid = !!data.none_of_the_above_interfering_medications || _.some(_.values(data.medications_that_interfere));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "heading_photo_id_selfie", "type": "content", "input": false, "label": "Content", "html": "<h3>Identity-Verification Photo</h3><p>We need a <strong>single clear photo</strong> of <em>you</em> holding a piece of photo ID next to your face. This is used only to confirm your identity for this request.</p>", "tableView": false, "refreshOnChange": false}, {"key": "uploadUrl", "type": "file", "input": true, "confirm_label": "Confirm ID selfie uploaded:", "label": "Upload Your ID Selfie", "description": "Tip: Stand in good light, hold your ID steady, and make sure both your face and the ID details are readable.", "image": true, "webcam": true, "capture": "user", "storage": "url", "url": "/app/q/{qpk}/formio-files/{pk}/", "multiple": false, "fileTypes": [{"label": "", "value": ""}], "tableView": true, "validate": {"required": true, "minFiles": 1, "maxFiles": 4}}, {"key": "photo_confirm_id_selfie", "type": "checkbox", "input": true, "confirm_label": "Confirm ID selfie acknowledgement:", "label": "I've uploaded a clear photo of myself holding my ID", "validate": {"required": true}, "tableView": false}, {"intake_template_key": "hx-any-questions"}, {"key": "intake_kind", "type": "textfield", "input": true, "label": "Intake Kind", "hidden": true, "disabled": true, "tableView": true, "clearOnHide": false, "calculateValue": "value = !data.intake_denial_reasons?.length ? 'mail' : 'chat';"}, {"key": "intake_mt_key", "type": "textfield", "input": true, "label": "Intake Mail Template Key", "hidden": true, "disabled": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.intake_kind=='mail'?'intake-ella':'intake-denial'"}, {"key": "allowed_rt_keys", "type": "textfield", "input": true, "label": "Allowed Rep<PERSON>", "hidden": true, "disabled": true, "multiple": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.intake_mt_key=='intake-denial'?[]:['get-rx','appointment-intake','edit-intake']"}]}