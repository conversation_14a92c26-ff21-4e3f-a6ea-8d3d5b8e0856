{% load template_extras %}
{% with rxts=questionnaire.rxts.all rxks=questionnaire.rxt_keys insured=questionnaire.insured_assays.all uninsured=questionnaire.uninsured_assays.all %}

<p>Hi {{ patient.name }},</p>
<p>
    This is {{ doctor.name }} (CPSO #{{ doctor.cpso_number }}) from our clinic. 
    If you have questions about your responses or feel your answers missed any symptoms or conditions, 
    we can arrange for secure real-time messaging. Otherwise, we can proceed with the plan below, but I need you to confirm the following from your medical history.
</p>


<!-- Plan Section -->
<h2>Plan</h2>

{# ----------  TESTS / INVESTIGATIONS ---------- #}
<h3>Lab Testing Requested</h3>

{% if insured or uninsured %}
  {% if insured %}
    <p><strong>Insured tests (OHIP-covered)</strong></p>
    <ul>
      {% for a in insured %}
        <li>{{ a.name }} – {{ a.test_type }}</li>
      {% endfor %}
    </ul>
  {% endif %}
  {% if uninsured %}
    <p><strong>Uninsured tests (private-pay)</strong></p>
    <ul>
      {% for a in uninsured %}
        <li>{{ a.name }} – {{ a.test_type }}</li>
      {% endfor %}
    </ul>
  {% endif %}
{% else %}
  <p>No diagnostic tests required at this stage.</p>
{% endif %}


<!-- =================  MEDICAL SUMMARY (Non-symptom history, NMR / Advanced Lipids)  ================= -->
{% with data=questionnaire.hpc.data summary=questionnaire.raw_formio_summary %}
<h3>Medical Summary</h3>
<ul>

  {# Reason(s) for NMR consultation #}
  {% with items=summary|confirm:"cholesterol_consultation_indication,cholesterol_consultation_other" %}
  {% if items %}
    <li><strong>Reason for consultation</strong>
      <ul>
        {% for qa in items %}
          {% if ":" in qa %}
            <li>{% autoescape off %}{{ qa|replace:": |: <strong>" }}{% endautoescape %}</strong></li>
          {% else %}<li>{{ qa|safe }}</li>{% endif %}
        {% endfor %}
      </ul>
    </li>
  {% endif %}
  {% endwith %}

  {# Prior cholesterol diagnosis & details #}
  {% with items=summary|confirm:"prior_cholesterol_diagnosis,cholesterol_diagnosis_age,cholesterol_issue_type" %}
  {% if items %}
    <li><strong>Prior cholesterol diagnosis</strong>
      <ul>
        {% for qa in items %}
          {% if ":" in qa %}
            <li>{% autoescape off %}{{ qa|replace:": |: <strong>" }}{% endautoescape %}</strong></li>
          {% else %}<li>{{ qa|safe }}</li>{% endif %}
        {% endfor %}
      </ul>
    </li>
  {% endif %}
  {% endwith %}

  {# Heart & blood vessel history - patient-friendly (no abbreviations) #}
<li><strong>Your heart &amp; blood vessel history</strong>
  <ul>
    {% if data.none_of_the_above_ascvd_history %}
      <li><strong>You reported having none of the following conditions:</strong>
        <ul class="mb-0">
          <li>Heart attack (myocardial infarction)</li>
          <li>Angina (chest pain)</li>
          <li>Stent procedure</li>
          <li>Heart bypass surgery</li>
          <li>Stroke</li>
          <li>Mini-stroke (TIA)</li>
          <li>Peripheral artery disease (poor blood flow to legs/feet)</li>
        </ul>
      </li>
    {% else %}
      {% with ah=data.ascvd_history %}
      {% if ah %}
        {% if ah.mi or ah.angina or ah.stent or ah.bypass_surgery or ah.stroke or ah.tia or ah.pad %}
          <li>Reported condition(s)
            <ul>
              {% if ah.mi %}<li><strong>Heart attack (myocardial infarction)</strong></li>{% endif %}
              {% if ah.angina %}<li><strong>Angina (chest pain)</strong></li>{% endif %}
              {% if ah.stent %}<li><strong>Stent procedure</strong></li>{% endif %}
              {% if ah.bypass_surgery %}<li><strong>Heart bypass surgery</strong></li>{% endif %}
              {% if ah.stroke %}<li><strong>Stroke</strong></li>{% endif %}
              {% if ah.tia %}<li><strong>Mini-stroke (TIA)</strong></li>{% endif %}
              {% if ah.pad %}<li><strong>Peripheral artery disease (poor blood flow to legs/feet)</strong></li>{% endif %}
            </ul>
          </li>
        {% endif %}
        <li>Conditions you do not have (as reported)
          <ul>
            {% if not ah.mi %}<li><strong>Heart attack (myocardial infarction)</strong></li>{% endif %}
            {% if not ah.angina %}<li><strong>Angina (chest pain)</strong></li>{% endif %}
            {% if not ah.stent %}<li><strong>Stent procedure</strong></li>{% endif %}
            {% if not ah.bypass_surgery %}<li><strong>Heart bypass surgery</strong></li>{% endif %}
            {% if not ah.stroke %}<li><strong>Stroke</li>{% endif %}
            {% if not ah.tia %}<li><strong>Mini-stroke (TIA)</strong></li>{% endif %}
            {% if not ah.pad %}<li><strong>Peripheral artery disease (poor blood flow to legs/feet)</strong></li>{% endif %}
          </ul>
        </li>
      {% endif %}
      {% endwith %}
    {% endif %}
  </ul>
</li>


  {# Specialist follow-up #}
  {% with items=summary|confirm:"ascvd_specialist_followup_status,ascvd_specialist_types,ascvd_next_followup" %}
  {% if items %}
    <li><strong>Specialist follow-up</strong>
      <ul>
        {% for qa in items %}
          {% if ":" in qa %}
            <li>{% autoescape off %}{{ qa|replace:": |: <strong>" }}{% endautoescape %}</strong></li>
          {% else %}<li>{{ qa|safe }}</li>{% endif %}
        {% endfor %}
      </ul>
    </li>
  {% endif %}
  {% endwith %}

  {# Family history (both early ASCVD and lipid markers) #}
  {% with items=summary|confirm:"family_history_premature_ascvd,family_members_with_early_ascvd,family_early_ascvd_details,family_history_lipid_markers,family_members_with_lipid_markers,family_lipid_marker_details" %}
  {% if items %}
    <li><strong>Family history</strong>
      <ul>
        {% for qa in items %}
          {% if ":" in qa %}
            <li>{% autoescape off %}{{ qa|replace:": |: <strong>" }}{% endautoescape %}</strong></li>
          {% else %}<li>{{ qa|safe }}</li>{% endif %}
        {% endfor %}
      </ul>
    </li>
  {% endif %}
  {% endwith %}

  {# Genetic testing #}
  {% with items=summary|confirm:"prior_genetic_testing,genetic_test_types,genetic_test_other_text" %}
  {% if items %}
    <li><strong>Genetic testing</strong>
      <ul>
        {% for qa in items %}
          {% if ":" in qa %}
            <li>{% autoescape off %}{{ qa|replace:": |: <strong>" }}{% endautoescape %}</strong></li>
          {% else %}<li>{{ qa|safe }}</li>{% endif %}
        {% endfor %}
      </ul>
    </li>
  {% endif %}
  {% endwith %}

</ul>

{# ───────── Symptoms Present (only renders if any detail answers exist) ───────── #}
{% with hqas=summary|confirm:"Chest Pain:chest_pain_onset,chest_pain_character,chest_pain_triggers,chest_pain_relievers,chest_pain_location,chest_pain_radiation,chest_pain_pattern;Palpitations:palpitations_onset,palpitations_rhythm,palpitations_triggers,palpitations_duration,palpitations_associated_symptoms;Swelling:swelling_location,swelling_sidedness,swelling_onset,swelling_timing,swelling_pitting;Dizziness:dizziness_onset,dizziness_frequency,dizziness_character,dizziness_timing,fainting_loss_consciousness;Cough:cough_type,cough_duration,cough_coughing_blood,cough_feeling_unwell,cough_with_cold,cough_progression;Shortness of breath:sob_triggers;Wheezing:wheezing_timing,wheezing_relief,wheezing_asthma_history" %}
{% if hqas %}
<h3>Symptoms Present</h3>
<ul>
  {% for heading,qas in hqas.items %}
    <li>{{heading}}</li>
    <ul>
      {% for qa in qas %}<li>{{qa|safe}}</li>{% endfor %}
    </ul>
  {% endfor %}
</ul>
{% endif %}
{% endwith %}

{# ───────── Symptoms NOT Present (uses computed "not present" fields) ───────── #}
<h3>Symptoms NOT Present</h3>
<ul>
  {% for qa in summary|confirm:"cardiac_symptoms_not_present,respiratory_symptoms_not_present" %}
    <li>{{qa|safe}}</li>
  {% endfor %}
</ul>

{% endwith %}


<!-- Health Condition Summary -->
<h3>Health Condition Summary</h3>
<ul>
  {% with summary=questionnaire.raw_formio_summary %}

  <li><strong>Current Medications:</strong> 
    {% if summary.medications_list and summary.medications_list.c_val %}
      {{ summary.medications_list.c_val }}
    {% else %}
      None
    {% endif %}
  </li>

<li><strong>Medication Allergies:</strong> 
  {% if summary.medication_allergies_list and summary.medication_allergies_list.c_val %}
    {{ summary.medication_allergies_list.c_val }}
  {% else %}
    None
  {% endif %}
</li>

  <li><strong>Past Surgeries:</strong> 
    {% if summary.past_surgeries_list and summary.past_surgeries_list.c_val %}
      {{ summary.past_surgeries_list.c_val }}
    {% else %}
      None
    {% endif %}
  </li>

  <li><strong>Health Conditions:</strong> 
    {% if summary.health_conditions_list and summary.health_conditions_list.c_val %}
      {{ summary.health_conditions_list.c_val }}
    {% else %}
      None
    {% endif %}
  </li>

  {% endwith %}
</ul>

<!-- =================  PRIOR LIPID / NMR TEST RESULTS  ================= -->
{% with data=questionnaire.hpc.data %}
{% if data.prior_lipids_ever == 'yes' and data.lipid_results_grid %}
<h3>Prior Test Results</h3>
<table class="table table-sm align-middle mb-3">
  <thead>
    <tr>
      <th style="width:14rem;">Date</th>
      <th>Standard Lipids</th>
      <th>ApoB / Lp(a)</th>
      <th>NMR / LipoProfile</th>
    </tr>
  </thead>
  <tbody>
    {% for row in data.lipid_results_grid %}
      <tr>
        {# ——— Date ——— #}
        <td>
          {% if row.lipid_month == 'unknown' %}?{% else %}{{ row.lipid_month|format_formio_str }}{% endif %}
          {% if row.lipid_year %} {{ row.lipid_year }}{% endif %}
        </td>

        {# ——— Standard Lipids ——— #}
        <td>
          {% if row.panel_units == 'mmol_l' %}
            {% with "mmol/L" as unit %}
              LDL: <strong>{% if row.ldl_value and row.ldl_value != 0 %}{{ row.ldl_value }}{% else %}Unknown{% endif %}</strong> {{ unit }}<br>
              HDL: <strong>{% if row.hdl_value and row.hdl_value != 0 %}{{ row.hdl_value }}{% else %}Unknown{% endif %}</strong> {{ unit }}<br>
              TG:  <strong>{% if row.tg_value  and row.tg_value  != 0 %}{{ row.tg_value  }}{% else %}Unknown{% endif %}</strong> {{ unit }}
            {% endwith %}
          {% elif row.panel_units == 'mg_dl' %}
            {% with "mg/dL" as unit %}
              LDL: <strong>{% if row.ldl_value and row.ldl_value != 0 %}{{ row.ldl_value }}{% else %}Unknown{% endif %}</strong> {{ unit }}<br>
              HDL: <strong>{% if row.hdl_value and row.hdl_value != 0 %}{{ row.hdl_value }}{% else %}Unknown{% endif %}</strong> {{ unit }}<br>
              TG:  <strong>{% if row.tg_value  and row.tg_value  != 0 %}{{ row.tg_value  }}{% else %}Unknown{% endif %}</strong> {{ unit }}
            {% endwith %}
          {% else %}
            {% with "?" as unit %}
              LDL: <strong>{% if row.ldl_value and row.ldl_value != 0 %}{{ row.ldl_value }}{% else %}Unknown{% endif %}</strong> {{ unit }}<br>
              HDL: <strong>{% if row.hdl_value and row.hdl_value != 0 %}{{ row.hdl_value }}{% else %}Unknown{% endif %}</strong> {{ unit }}<br>
              TG:  <strong>{% if row.tg_value  and row.tg_value  != 0 %}{{ row.tg_value  }}{% else %}Unknown{% endif %}</strong> {{ unit }}
            {% endwith %}
          {% endif %}
        </td>

        {# ——— ApoB / Lp(a) ——— #}
        <td>
          {% if row.apob_units == 'g_l' %}
            {% with "g/L" as au %}
              ApoB: <strong>{% if row.apob_value and row.apob_value != 0 %}{{ row.apob_value }}{% else %}Unknown{% endif %}</strong> {{ au }}
            {% endwith %}
          {% elif row.apob_units == 'mg_dl' %}
            {% with "mg/dL" as au %}
              ApoB: <strong>{% if row.apob_value and row.apob_value != 0 %}{{ row.apob_value }}{% else %}Unknown{% endif %}</strong> {{ au }}
            {% endwith %}
          {% else %}
            {% with "?" as au %}
              ApoB: <strong>{% if row.apob_value and row.apob_value != 0 %}{{ row.apob_value }}{% else %}Unknown{% endif %}</strong> {{ au }}
            {% endwith %}
          {% endif %}
          <br>
          {% if row.lpa_units == 'nmol_l' %}
            {% with "nmol/L" as lu %}
              Lp(a): <strong>{% if row.lpa_value and row.lpa_value != 0 %}{{ row.lpa_value }}{% else %}Unknown{% endif %}</strong> {{ lu }}
            {% endwith %}
          {% elif row.lpa_units == 'mg_dl' %}
            {% with "mg/dL" as lu %}
              Lp(a): <strong>{% if row.lpa_value and row.lpa_value != 0 %}{{ row.lpa_value }}{% else %}Unknown{% endif %}</strong> {{ lu }}
            {% endwith %}
          {% else %}
            {% with "?" as lu %}
              Lp(a): <strong>{% if row.lpa_value and row.lpa_value != 0 %}{{ row.lpa_value }}{% else %}Unknown{% endif %}</strong> {{ lu }}
            {% endwith %}
          {% endif %}
        </td>

        {# ——— NMR / LipoProfile ——— #}
        <td>
          {% if row.nmr_done == 'yes' %}
            LDL-P: <strong>{% if row.nmr_ldl_p and row.nmr_ldl_p != 0 %}{{ row.nmr_ldl_p }}{% else %}Unknown{% endif %}</strong> nmol/L<br>
            Small LDL-P: <strong>{% if row.nmr_small_ldl_p and row.nmr_small_ldl_p != 0 %}{{ row.nmr_small_ldl_p }}{% else %}Unknown{% endif %}</strong> nmol/L<br>
            LDL size: <strong>{% if row.nmr_ldl_size and row.nmr_ldl_size != 0 %}{{ row.nmr_ldl_size }}{% else %}Unknown{% endif %}</strong> nm<br>
            HDL-P (total): <strong>{% if row.nmr_hdl_p_total and row.nmr_hdl_p_total != 0 %}{{ row.nmr_hdl_p_total }}{% else %}Unknown{% endif %}</strong> µmol/L<br>
            LDL-C (NIH): <strong>{% if row.nmr_ldl_c_nih and row.nmr_ldl_c_nih != 0 %}{{ row.nmr_ldl_c_nih }}{% else %}Unknown{% endif %}</strong> mg/dL<br>
            LP-IR: <strong>{% if row.nmr_lp_ir_score and row.nmr_lp_ir_score != 0 %}{{ row.nmr_lp_ir_score }}{% else %}Unknown{% endif %}</strong>
          {% elif row.nmr_done == 'unsure' %}
            <em>Not sure</em>
          {% else %}
            <em>Not done</em>
          {% endif %}
        </td>
      </tr>
    {% endfor %}
  </tbody>
</table>
{% endif %}
{% endwith %}

<!-- **************  KEY ADVICE  ************** -->
<h3>Advice</h3>

{% with summary=questionnaire.raw_formio_summary %}
  {% with items=summary|confirm:"nmr_proceed_choice,nmr_fasting_ack" %}
    {% if items %}
      <ul class="mb-3">
        {% for qa in items %}
          <li>{{ qa|safe }}</li>
        {% endfor %}
      </ul>
    {% endif %}
  {% endwith %}
{% endwith %}

<h3>Next Steps</h3>

<ul class="mb-3">

  <li><strong>Before your blood draw:</strong>
    <ul>
      <li>Avoid alcohol in the prior 24 hours and heavy exercise the day before—both can transiently raise triglycerides.</li>
      <li>Take your usual medications unless you were told otherwise. If you recently changed cholesterol therapy, test after it has been stable for ~6-12 weeks.</li>
    </ul>
  </li>
</ul>


<p class="mt-3">
    <strong>Confirmation:</strong>
    By completing and submitting this intake, you confirm that the information provided is accurate to the best of your knowledge, <u>that you have read and understand all advice contained in the intake form</u>, and that you agree to seek emergency or in-person medical care if severe side-effects or new symptoms develop.
  </p>
<p>Best regards,<br>{{ doctor.name }}</p>
{% endwith %}