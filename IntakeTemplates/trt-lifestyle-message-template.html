{% load template_extras %}
{% with data=questionnaire.hpc.data %}
{% with summary=questionnaire.raw_formio_summary %}
{% with insured=questionnaire.insured_assays.all %}
{% with uninsured=questionnaire.uninsured_assays.all %}

<p>Hi {{patient.name}},</p>

<p>This is {{doctor.name}} (CPSO #{{doctor.cpso_number}}) &amp; clinic contact phone {{doctor.phone}}.</p>

<p>I've reviewed your intake related to low testosterone symptoms and hormone testing. If you had difficulty with any questions or feel your answers didn't reflect your current concerns, we can arrange a secure follow-up chat. Otherwise, we'll proceed with the plan below.</p>

<h4 style="text-align: left; font-weight: bold; margin-top: 30px;">Lab Testing Requested</h4>

{% if insured %}
<p><strong>Insured Tests:</strong></p>
<ul>
  {% for a in insured %}
  <li>{{a.name}} ({{a.test_type}} test)</li>
  {% endfor %}
</ul>
{% endif %}

{% if uninsured %}
<p><strong>Uninsured Tests:</strong></p>
<ul>
  {% for a in uninsured %}
  <li>{{a.name}} ({{a.test_type}} test)</li>
  {% endfor %}
</ul>
<p>Uninsured testing may include non-essential monitoring or private-pay options not covered by OHIP. These can be declined at the lab.</p>
{% endif %}

<h3 style="text-align: left; font-weight: bold; margin-top: 30px;">Plan</h3>

<p>Please review your intake summary to ensure accuracy. If everything looks correct, you may proceed to download your lab requisition.</p>

<p>Next steps:</p>
<ol>
  <li>Select your preferred laboratory and complete the required blood work.</li>
  <li>Your results will be uploaded to the portal once available.</li>
  <li>If any abnormalities are identified, we'll contact you to arrange a secure follow-up chat.</li>
</ol>

<p>If your results are normal but your symptoms persist or worsen, please seek same-day in-person medical care.</p>

<!-- Health Condition Summary -->
<h5>Health Condition Summary</h5>
<ul>
  {% with summary=questionnaire.raw_formio_summary %}

  <li><strong>Current Medications:</strong> 
    {% if summary.medications_list and summary.medications_list.c_val %}
      {{ summary.medications_list.c_val }}
    {% else %}
      None
    {% endif %}
  </li>

<li><strong>Medication Allergies:</strong> 
  {% if summary.medication_allergies_list and summary.medication_allergies_list.c_val %}
    {{ summary.medication_allergies_list.c_val }}
  {% else %}
    None
  {% endif %}
</li>

  <li><strong>Past Surgeries:</strong> 
    {% if summary.past_surgeries_list and summary.past_surgeries_list.c_val %}
      {{ summary.past_surgeries_list.c_val }}
    {% else %}
      None
    {% endif %}
  </li>

  <li><strong>Health Conditions:</strong> 
    {% if summary.health_conditions_list and summary.health_conditions_list.c_val %}
      {{ summary.health_conditions_list.c_val }}
    {% else %}
      None
    {% endif %}
  </li>

  {% endwith %}
</ul>

<p><strong>Reason for Testosterone Testing:</strong></p>
<ul>
  {% for s in summary|confirm:"reason_for_testosterone_testing" %}
    <li>{{ s|safe }}</li>
  {% endfor %}
</ul>


<p class="mb-0">By continuing with testing, you confirm the following from your medical history:</p>
<ul>
  <li>You are pursuing evaluation for low testosterone symptoms</li>
  <li>You will seek in-person emergency care for any of the following:
    <ul>
      <li>Chest pain, shortness of breath, palpitations</li>
      <li>Severe fatigue, weakness, dizziness, or fainting</li>
      <li>Abdominal pain, vomiting, or blood in stool or urine</li>
      <li>Thoughts of self-harm or harming others</li>
    </ul>
  </li>
  <li>You understood the advice provided in this assessment</li>
</ul>

<p><strong>Symptoms Present:</strong></p>
<ul>

  {% if data.low_testosterone_symptoms %}
  <li><strong>Testosterone-Related Symptoms:</strong>
    <ul>

      {% if data.low_testosterone_symptoms.low_energy %}
      <li>Fatigue or low energy
        <ul>
          {% if data.symptom_start_fatigue %}<li>Started: {{ data.symptom_start_fatigue.label }}</li>{% endif %}
          {% if data.fatigue_description %}
          <li>Details:
            <ul>
              {% if data.fatigue_description.physical %}<li>Physically tired</li>{% endif %}
              {% if data.fatigue_description.mental %}<li>Mental fatigue</li>{% endif %}
              {% if data.fatigue_description.motivation %}<li>Lack of motivation</li>{% endif %}
              {% if data.fatigue_description.sleepy %}<li>Daytime sleepiness</li>{% endif %}
              {% if data.fatigue_description.evening_worse %}<li>Worse in evening</li>{% endif %}
              {% if data.fatigue_description.despite_sleep %}<li>Fatigue despite good sleep</li>{% endif %}
            </ul>
          </li>
          {% endif %}
        </ul>
      </li>
      {% endif %}

      {% if data.low_testosterone_symptoms.ed %}
      <li>Erectile dysfunction
        <ul>
          {% if data.symptom_start_ed %}<li>Started: {{ data.symptom_start_ed.label }}</li>{% endif %}
          {% if data.ed_symptom_types %}
          <li>Details:
            <ul>
              {% if data.ed_symptom_types.cannot_get %}<li>Cannot get erection</li>{% endif %}
              {% if data.ed_symptom_types.cannot_maintain %}<li>Cannot maintain erection</li>{% endif %}
              {% if data.ed_symptom_types.weaker %}<li>Weaker erections</li>{% endif %}
              {% if data.ed_symptom_types.takes_longer %}<li>Takes longer to achieve</li>{% endif %}
              {% if data.ed_symptom_types.no_morning %}<li>Fewer/no morning erections</li>{% endif %}
              {% if data.ed_symptom_types.curvature %}<li>Curved erections</li>{% endif %}
            </ul>
          </li>
          {% endif %}
        </ul>
      </li>
      {% endif %}

      {% if data.low_testosterone_symptoms.brain_fog %}
      <li>Brain fog or difficulty concentrating
        <ul>
          {% if data.symptom_start_brain_fog %}<li>Started: {{ data.symptom_start_brain_fog.label }}</li>{% endif %}
          {% if data.brain_fog_description %}
          <li>Details:
            <ul>
              {% if data.brain_fog_description.concentration %}<li>Difficulty concentrating</li>{% endif %}
              {% if data.brain_fog_description.distracted %}<li>Easily distracted</li>{% endif %}
              {% if data.brain_fog_description.forgetfulness %}<li>Forgetfulness</li>{% endif %}
              {% if data.brain_fog_description.slowed_thinking %}<li>Slowed thinking</li>{% endif %}
              {% if data.brain_fog_description.fuzzy_feeling %}<li>Mental fog or fuzziness</li>{% endif %}
            </ul>
          </li>
          {% endif %}
        </ul>
      </li>
      {% endif %}

      {% if data.low_testosterone_symptoms.reduced_libido %}
      <li>Reduced libido
        <ul>
          {% if data.symptom_start_libido %}<li>Started: {{ data.symptom_start_libido.label }}</li>{% endif %}
          {% if data.libido_pattern %}<li>Pattern: {{ data.libido_pattern }}</li>{% endif %}
          {% if data.libido_distress_level %}<li>Impact: {{ data.libido_distress_level }}</li>{% endif %}
        </ul>
      </li>
      {% endif %}

      {% if data.low_testosterone_symptoms.muscle_loss %}
      <li>Loss of muscle mass or strength
        <ul>
          {% if data.symptom_start_muscle_loss %}<li>Started: {{ data.symptom_start_muscle_loss.label }}</li>{% endif %}
          {% if data.muscle_loss_chronicity %}<li>Description: {{ data.muscle_loss_chronicity }}</li>{% endif %}
          {% if data.muscle_loss_what_changed %}
          <li>Changes noticed:
            <ul>
              {% if data.muscle_loss_what_changed.size %}<li>Muscle size</li>{% endif %}
              {% if data.muscle_loss_what_changed.strength %}<li>Strength/endurance</li>{% endif %}
              {% if data.muscle_loss_what_changed.both %}<li>Both size and strength</li>{% endif %}
              {% if data.muscle_loss_what_changed.neither %}<li>Feels different, no clear loss</li>{% endif %}
            </ul>
          </li>
          {% endif %}
          {% if data.muscle_loss_training_consistency %}<li>Training consistency: {{ data.muscle_loss_training_consistency }}</li>{% endif %}
        </ul>
      </li>
      {% endif %}

    </ul>
  </li>
  {% endif %}

{% if data.mental_health_symptoms.low_mood or data.mental_health_symptoms.anger or data.mental_health_symptoms.anxiety or data.mental_health_symptoms.psychosis %}
<li><strong>Mental Health Symptoms:</strong>
  <ul>
    {% if data.mental_health_symptoms.low_mood or data.mental_health_symptoms.anger or data.mental_health_symptoms.anxiety or data.mental_health_symptoms.psychosis %}
    <li>Details:
      <ul>
        {% if data.mental_health_symptoms.low_mood %}<li>Low mood</li>{% endif %}
        {% if data.mental_health_symptoms.anger %}<li>Irritability or anger</li>{% endif %}
        {% if data.mental_health_symptoms.anxiety %}<li>Anxiety</li>{% endif %}
        {% if data.mental_health_symptoms.psychosis %}<li>Hearing or seeing things others don’t</li>{% endif %}
      </ul>
    </li>
    {% endif %}

    {% if data.mental_health_onset %}
    <li>
      Duration:
      {% if data.mental_health_onset == "under_1_week" %}Symptoms began within the last week
      {% elif data.mental_health_onset == "1_4_weeks" %}1–4 weeks ago
      {% elif data.mental_health_onset == "1_3_months" %}1–3 months ago
      {% elif data.mental_health_onset == "3_6_months" %}3–6 months ago
      {% elif data.mental_health_onset == "6_12_months" %}6–12 months ago
      {% elif data.mental_health_onset == "over_1_year_continuous" %}Ongoing for over a year
      {% elif data.mental_health_onset == "recurrent_1_month" %}Comes and goes over the past month
      {% elif data.mental_health_onset == "recurrent_1_year" %}Comes and goes over the past year
      {% elif data.mental_health_onset == "recurrent_many_years" %}Recurring for many years
      {% elif data.mental_health_onset == "unsure" %}Uncertain onset
      {% endif %}
    </li>
    {% endif %}
  </ul>
</li>
{% endif %}
</ul>


<p><strong>Symptoms NOT Present:</strong></p>
<ul>

  {% if summary|confirm:"cardiac_symptoms_not_present" %}
  <li><strong>Heart Symptoms:</strong>
    {% if not data.cardiovascular_symptoms.chest_pain %}Chest pain{% endif %}
    {% if not data.cardiovascular_symptoms.palpitations %}{% if data.cardiovascular_symptoms.chest_pain %}, Palpitations{% else %}Palpitations{% endif %}{% endif %}
    {% if not data.cardiovascular_symptoms.swelling %}{% if data.cardiovascular_symptoms.chest_pain or data.cardiovascular_symptoms.palpitations %}, Swelling{% else %}Swelling{% endif %}{% endif %}
    {% if not data.cardiovascular_symptoms.dizziness %}{% if data.cardiovascular_symptoms.chest_pain or data.cardiovascular_symptoms.palpitations or data.cardiovascular_symptoms.swelling %}, Dizziness or fainting{% else %}Dizziness or fainting{% endif %}{% endif %}
  </li>
  {% endif %}

  {% if summary|confirm:"respiratory_symptoms_not_present" %}
  <li><strong>Breathing Symptoms:</strong>
    {% if not data.respiratory_symptoms.cough %}Cough{% endif %}
    {% if not data.respiratory_symptoms.shortness_of_breath %}{% if data.respiratory_symptoms.cough %}, Shortness of breath{% else %}Shortness of breath{% endif %}{% endif %}
    {% if not data.respiratory_symptoms.wheezing %}{% if data.respiratory_symptoms.cough or data.respiratory_symptoms.shortness_of_breath %}, Wheezing{% else %}Wheezing{% endif %}{% endif %}
  </li>
  {% endif %}

  {% if summary|confirm:"gastrointestinal_symptoms_not_present" %}
  <li><strong>Digestive Symptoms:</strong>
    {% if not data.abdominal_gastrointestinal_symptoms.abdominal_pain %}Abdominal pain{% endif %}
    {% if not data.abdominal_gastrointestinal_symptoms.nausea %}{% if data.abdominal_gastrointestinal_symptoms.abdominal_pain %}, Nausea{% else %}Nausea{% endif %}{% endif %}
    {% if not data.abdominal_gastrointestinal_symptoms.vomiting %}{% if data.abdominal_gastrointestinal_symptoms.abdominal_pain or data.abdominal_gastrointestinal_symptoms.nausea %}, Vomiting{% else %}Vomiting{% endif %}{% endif %}
    {% if not data.abdominal_gastrointestinal_symptoms.diarrhea %}{% if data.abdominal_gastrointestinal_symptoms.abdominal_pain or data.abdominal_gastrointestinal_symptoms.nausea or data.abdominal_gastrointestinal_symptoms.vomiting %}, Diarrhea{% else %}Diarrhea{% endif %}{% endif %}
    {% if not data.abdominal_gastrointestinal_symptoms.constipation %}{% if data.abdominal_gastrointestinal_symptoms.abdominal_pain or data.abdominal_gastrointestinal_symptoms.nausea or data.abdominal_gastrointestinal_symptoms.vomiting or data.abdominal_gastrointestinal_symptoms.diarrhea %}, Constipation{% else %}Constipation{% endif %}{% endif %}
    {% if not data.abdominal_gastrointestinal_symptoms.bloating_gas %}{% if data.abdominal_gastrointestinal_symptoms.abdominal_pain or data.abdominal_gastrointestinal_symptoms.nausea or data.abdominal_gastrointestinal_symptoms.vomiting or data.abdominal_gastrointestinal_symptoms.diarrhea or data.abdominal_gastrointestinal_symptoms.constipation %}, Bloating or gas{% else %}Bloating or gas{% endif %}{% endif %}
    {% if not data.abdominal_gastrointestinal_symptoms.rectal_bleeding %}{% if data.abdominal_gastrointestinal_symptoms.abdominal_pain or data.abdominal_gastrointestinal_symptoms.nausea or data.abdominal_gastrointestinal_symptoms.vomiting or data.abdominal_gastrointestinal_symptoms.diarrhea or data.abdominal_gastrointestinal_symptoms.constipation or data.abdominal_gastrointestinal_symptoms.bloating_gas %}, Rectal bleeding{% else %}Rectal bleeding{% endif %}{% endif %}
  </li>
  {% endif %}

  {% if summary|confirm:"mental_health_symptoms_not_present" %}
  <li><strong>Mental Health Symptoms:</strong>
    {% if not data.mental_health_symptoms.anxiety %}Anxiety{% endif %}
    {% if not data.mental_health_symptoms.low_mood %}{% if data.mental_health_symptoms.anxiety %}Low mood{% else %}, Low mood{% endif %}{% endif %}
    {% if not data.mental_health_symptoms.anger %}{% if data.mental_health_symptoms.anxiety or data.mental_health_symptoms.low_mood %}, Irritability or anger{% else %}Irritability or anger{% endif %}{% endif %}
    {% if not data.mental_health_symptoms.psychosis %}{% if data.mental_health_symptoms.anxiety or data.mental_health_symptoms.low_mood or data.mental_health_symptoms.anger %}, Hearing voices or seeing things others don't{% else %}Hearing voices or seeing things others don't{% endif %}{% endif %}
  </li>
  {% endif %}

</ul>

<!-- =================  RECORDED LAB VALUES  ================= -->
{% comment %}Show a compact table only if any prior results exist{% endcomment %}
{% with summary|confirm:"prior_kidney_function_value,lipid_profile_abnormalities,recent_a1c_value,recent_fbg_value,prior_cbc_value,prior_b12_value,prior_tsh_value" as any_labs %}
{% if any_labs %}
<h4>Recorded&nbsp;Lab&nbsp;Values</h4>
<table class="table table-sm mb-4">
  <thead>
    <tr>
      <th>Test</th>
      <th>Most&nbsp;recent&nbsp;result</th>
      <th>When&nbsp;tested</th>
    </tr>
  </thead>
  <tbody>
    {# eGFR #}
    {% with summary|confirm:"prior_kidney_function_value" as v %}{% if v %}
      <tr>
        <td>eGFR</td>
        <td><strong>{{ v|first|replace:"eGFR result: |"|safe }}</strong></td>
        <td>{% with summary|confirm:"last_kidney_function_test" as w %}{% if w %}<strong>{{ w|first|replace:"Timing: |"|safe }}</strong>{% endif %}{% endwith %}</td>
      </tr>
    {% endif %}{% endwith %}

    {# Lipid profile #}
    {% with summary|confirm:"lipid_profile_abnormalities" as v %}{% if v %}
      <tr>
        <td>Lipid profile</td>
        <td><strong>{{ v|first|replace:"Findings: |"|safe }}</strong></td>
        <td>{% with summary|confirm:"last_lipid_profile_test" as w %}{% if w %}<strong>{{ w|first|replace:"Timing: |"|safe }}</strong>{% endif %}{% endwith %}</td>
      </tr>
    {% endif %}{% endwith %}

    {# HbA1c #}
    {% with summary|confirm:"recent_a1c_value" as v %}{% if v %}
      <tr>
        <td>HbA1c</td>
        <td><strong>{{ v|first|replace:"Result: |"|safe }}</strong></td>
        <td>{% with summary|confirm:"last_a1c_test" as w %}{% if w %}<strong>{{ w|first|replace:"Timing: |"|safe }}</strong>{% endif %}{% endwith %}</td>
      </tr>
    {% endif %}{% endwith %}

    {# Fasting glucose #}
    {% with summary|confirm:"recent_fbg_value" as v %}{% if v %}
      <tr>
        <td>Fasting glucose</td>
        <td><strong>{{ v|first|replace:"Result: |"|safe }}</strong></td>
        <td>{% with summary|confirm:"last_fasting_glucose_test" as w %}{% if w %}<strong>{{ w|first|replace:"Timing: |"|safe }}</strong>{% endif %}{% endwith %}</td>
      </tr>
    {% endif %}{% endwith %}

    {# CBC (Hemoglobin) #}
    {% with summary|confirm:"prior_cbc_value" as v %}{% if v %}
      <tr>
        <td>CBC (Hemoglobin)</td>
        <td><strong>{{ v|first|replace:"Hemoglobin: |"|safe }}</strong></td>
        <td>{% with summary|confirm:"last_cbc_test" as w %}{% if w %}<strong>{{ w|first|replace:"Timing: |"|safe }}</strong>{% endif %}{% endwith %}</td>
      </tr>
    {% endif %}{% endwith %}

    {# Vitamin B12 #}
    {% with summary|confirm:"prior_b12_value" as v %}{% if v %}
      <tr>
        <td>Vitamin B12</td>
        <td><strong>{{ v|first|replace:"Result: |"|safe }}</strong></td>
        <td>{% with summary|confirm:"last_b12_test" as w %}{% if w %}<strong>{{ w|first|replace:"Timing: |"|safe }}</strong>{% endif %}{% endwith %}</td>
      </tr>
    {% endif %}{% endwith %}

    {# TSH #}
    {% with summary|confirm:"prior_tsh_value" as v %}{% if v %}
      <tr>
        <td>TSH</td>
        <td><strong>{{ v|first|replace:"Result: |"|safe }}</strong></td>
        <td>{% with summary|confirm:"last_tsh_test" as w %}{% if w %}<strong>{{ w|first|replace:"Timing: |"|safe }}</strong>{% endif %}{% endwith %}</td>
      </tr>
    {% endif %}{% endwith %}
  </tbody>
</table>
{% endif %}
{% endwith %}

{% if data.recommendation_sleep_study or data.recommendation_penile_doppler_due_to_curvature or data.fertility_testing_recommendation_no_semen or data.fertility_testing_recommendation_normal_semen or data.mental_health_suicidal_check or data.rectal_bleeding_warning or data.cough_urgent_warning %}

<h3 style="font-weight: bold; margin-top: 30px;">Recommendations</h3>
<ul>

  {% if data.recommendation_sleep_study %}
    <li>
      Possible Sleep Apnea: Recommend sleep study
      {% if data.sleep_study_understanding == "understand" %}
        – <strong>Understands</strong>
      {% elif data.sleep_study_understanding == "do_not_understand" %}
        – <strong>Does not understand</strong>
      {% endif %}
    </li>
  {% endif %}

  {% if data.recommendation_penile_doppler_due_to_curvature %}
    <li>
      Will pursue penile ultrasound for curvature
      {% if data.penile_doppler_curvature_understanding == "understand" %}
        – <strong>Understands</strong>
      {% elif data.penile_doppler_curvature_understanding == "do_not_understand" %}
        – <strong>Does not understand</strong>
      {% endif %}
    </li>
  {% endif %}

  {% if data.fertility_testing_recommendation_no_semen %}
    <li>
      Fertility: Recommend semen analysis before testosterone testing
      {% if data.fertility_testing_understanding_no_semen == "understand" %}
        – <strong>Understands</strong>
      {% elif data.fertility_testing_understanding_no_semen == "do_not_understand" %}
        – <strong>Does not understand</strong>
      {% endif %}
    </li>
  {% endif %}

  {% if data.fertility_testing_recommendation_normal_semen %}
    <li>
      Fertility: Hormone testing not required with normal semen
      {% if data.fertility_testing_understanding_normal_semen == "understand" %}
        – <strong>Understands</strong>
      {% elif data.fertility_testing_understanding_normal_semen == "do_not_understand" %}
        – <strong>Does not understand</strong>
      {% endif %}
    </li>
  {% endif %}

  {% if data.mental_health_suicidal_check == "yes" %}
    {% if data.mental_health_si_hi_timing == "today" %}
      <li>Mental Health: Immediate emergency care recommended</li>
    {% elif data.mental_health_si_hi_timing == "frequent" %}
      <li>Mental Health: Frequent suicidal thoughts – recommend urgent care</li>
    {% endif %}
  {% elif data.mental_health_suicidal_check == "no" %}
    <li>
      Mental Health: Safety plan in place
      {% if data.mental_health_safety_understanding == "understand" %}
        – <strong>Understands</strong>
      {% elif data.mental_health_safety_understanding == "do_not_understand" %}
        – <strong>Does not understand</strong>
      {% endif %}
    </li>
  {% endif %}

  {% if data.rectal_bleeding_warning %}
    <li>
      Rectal bleeding: Follow-up advised to rule out serious GI causes
      {% if data.rectal_bleeding_warning_understanding == "understand" %}
        – <strong>Understands</strong>
      {% elif data.rectal_bleeding_warning_understanding == "do_not_understand" %}
        – <strong>Does not understand</strong>
      {% endif %}
    </li>
  {% endif %}

  {% if data.cough_urgent_warning %}
    <li>
      Cough: Emergency evaluation advised if symptoms worsen
      {% if data.cough_urgent_warning_understanding == "understand" %}
        – <strong>Understands</strong>
      {% elif data.cough_urgent_warning_understanding == "do_not_understand" %}
        – <strong>Does not understand</strong>
      {% endif %}
    </li>
  {% endif %}

</ul>

{% endif %}

<h3 style="text-align: left; font-weight: bold; margin-top: 30px;">Important Testing Reminders</h3>
<ul>
  <li><strong>Time of Testing:</strong> Testosterone should be tested before 10 AM for accuracy</li>
  <li><strong>Confirmation Testing:</strong> Low levels should be confirmed with a second early-morning test</li>
  <li><strong>Factors Affecting Results:</strong> Poor sleep, alcohol, stress, and illness can temporarily lower levels</li>
  <li><strong>Physical Exam:</strong> If you have normal results, it's important to have an exam in-person with a doctor</li>
</ul>

<p>Best regards,<br>{{doctor.name}}</p>

{% endwith %}
{% endwith %}
{% endwith %}
{% endwith %}