{"components": [{"key": "cgm_insurance", "type": "radio", "input": true, "label": "Do you have insurance coverage for Continuous Glucose Monitors (CGMs)?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Unsure", "value": "unsure"}], "validate": {"required": true}, "tableView": true}, {"key": "cgm_no_insurance_alert", "html": "<p class='text-red'>CGMs can be purchased over the counter. This panel is intended for individuals who require a prescription for insurance coverage.</p>", "type": "content", "input": true, "tableView": false, "clearOnHide": false, "customConditional": "show = data.cgm_insurance === 'no';"}, {"key": "cgm_reason", "type": "selectboxes", "input": true, "label": "Please identify your reason for requesting a continuous glucose monitor (CGM):", "values": [{"label": "Use a CGM to reinforce lifestyle improvements", "value": "lifestyle_improvements"}, {"label": "Have diabetes on insulin", "value": "diabetic_on_insulin"}, {"label": "Have diabetes and pregnant", "value": "diabetic_in_pregnancy"}, {"label": "Have diabetes and have low blood sugars", "value": "type_ii_tracking_low_blood_sugar"}], "inputType": "checkbox", "tableView": true, "optionsLabelPosition": "right"}, {"key": "cgm_reason_none", "type": "checkbox", "input": true, "label": "None of the above", "customClass": "mt-n3", "validate": {"custom": "valid = _.some(_.values(data.cgm_reason)) || data.cgm_reason_none;"}, "defaultValue": false}, {"key": "preferred_monitor", "type": "radio", "input": true, "label": "Please select your preferred continuous glucose monitor (CGM):", "values": [{"label": "Dexcom G7", "value": "dexcom"}, {"label": "FreeStyle Libre II", "value": "freestyle_libre"}], "tableView": true, "validate": {"required": true}, "defaultValue": ""}, {"key": "cgm_contraindications_symptoms", "type": "selectboxes", "input": true, "label": "Have you experienced any of the following symptoms:", "values": [{"label": "Severe or frequent headaches", "value": "headaches", "shortcut": ""}, {"label": "Palpitations or chest pain", "value": "palpitations", "shortcut": ""}, {"label": "Shortness of breath", "value": "dyspnea", "shortcut": ""}, {"label": "Dizziness or fainting spells", "value": "dizziness", "shortcut": ""}, {"label": "Changes in vision (e.g., blurry vision or loss of vision)", "value": "vision_changes", "shortcut": ""}, {"label": "Chest tightness, especially if occurring during or after physical activity (i.e. exercise)", "value": "chest_pain_with_activity", "shortcut": ""}], "inputType": "checkbox", "tableView": true, "optionsLabelPosition": "right"}, {"key": "no_cgm_contraindications_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "customClass": "mt-n3", "validate": {"custom": "valid = _.some(_.values(data.cgm_contraindications_symptoms)) || data.no_cgm_contraindications_symptoms;"}, "defaultValue": false}, {"key": "cgm_contraindications_symptoms_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following CGM contraindication symptoms:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.cgm_contraindications_symptoms, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "no_indications", "type": "textfield", "input": true, "label": "No CGM Indication:", "hidden": true, "disabled": true, "multiple": false, "tableView": true, "clearOnHide": false, "defaultValue": true, "calculateValue": "value = !_.some(_.values(data.cgm_reason));", "refreshOnChange": true}, {"key": "contraindication_keys", "type": "textfield", "input": true, "label": "Contraindication keys:", "hidden": true, "disabled": true, "multiple": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "value = (_.some(_.values(data.cgm_contraindications_symptoms)) && !data.no_cgm_contraindications_symptoms) || (_.some(_.values(data.cgm_contraindications_symptoms)) && data.no_cgm_contraindications_symptoms) ? ['contraindications'] : [];", "refreshOnChange": true}, {"key": "approved_html", "type": "content", "input": true, "tableView": false, "clearOnHide": false, "defaultValue": "<h3 class='text-green'>You're eligible to obtain a prescription for a Continuous Glucose Monitor.</h3></br><p>You can confirm if your insurance provider (if you have one) provides coverage for Continuous Glucose Monitors. TeleTest will provide a CGM prescription for 3 months sent to your local pharmacy.</p>"}]}