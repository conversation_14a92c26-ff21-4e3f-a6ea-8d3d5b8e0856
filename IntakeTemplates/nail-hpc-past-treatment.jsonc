{"components": [{"key": "heading_past_treatments_nail", "type": "content", "html": "<br><h4>Past Treatments for Nail Fungus</h4><p>Please tell us about any medicines or procedures you've already tried.</p>"}, {"key": "used_prescription_past_nail", "type": "radio", "label": "Have you ever used prescription treatments for nail fungus?", "confirm_label": "Tried prescription treatments:", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true}, {"key": "past_treatment_effectiveness_nail", "type": "radio", "label": "Overall, how well did prescription treatment(s) work?", "confirm_label": "Overall effectiveness:", "values": [{"label": "Completely cleared the nail(s)", "value": "cleared"}, {"label": "Greatly improved but not fully cleared", "value": "very_improved"}, {"label": "Helped somewhat", "value": "partial"}, {"label": "No noticeable improvement", "value": "ineffective"}, {"label": "Not sure / varied", "value": "unsure"}], "validate": {"required": true}, "customConditional": "show = data.used_prescription_past_nail === 'yes';", "tableView": true}, {"key": "past_rx_types_nail", "type": "selectboxes", "label": "Which of these have you tried? (Select all that apply)", "confirm_label": "Types tried:", "values": [{"label": "Topical lacquers / solutions", "value": "topical"}, {"label": "Oral antifungal pills", "value": "oral"}, {"label": "Laser or light therapy", "value": "laser"}, {"label": "Over-the-counter products", "value": "otc"}, {"label": "Home / natural remedies", "value": "home"}, {"label": "Other", "value": "other"}, {"label": "None of these", "value": "none"}], "inputType": "checkbox", "validate": {"required": true}, "customConditional": "show = data.used_prescription_past_nail === 'yes';", "tableView": true}, {"key": "heading_topical_selected_nail", "type": "content", "html": "<br><h4>Topical medicines you've used</h4>", "customConditional": "show = data.past_rx_types_nail && data.past_rx_types_nail.topical;"}, {"key": "nail_rx_topical_grid", "type": "<PERSON><PERSON><PERSON>", "label": "Add each topical product you've used:", "confirm_label": "Topical Rx list:", "addAnother": "+ Add Another", "tableView": false, "components": [{"key": "rx_name", "type": "select", "label": "Product", "widget": "html5", "data": {"values": [{"label": "Efinaconazole 10 % solution (Jublia)", "value": "efinaconazole"}, {"label": "Tavaborole 5 % solution (Kerydin)", "value": "tava<PERSON><PERSON>"}, {"label": "Ciclopirox 8 % lacquer (Penlac)", "value": "ciclopirox"}, {"label": "Amorolfine 5 % lacquer (Loceryl)", "value": "amorolfine"}, {"label": "Urea / keratolytic solution", "value": "urea"}, {"label": "Other topical Rx", "value": "other"}]}, "validate": {"required": true}, "tableView": true, "confirm_label": "Topical product:"}, {"key": "rx_other_name", "type": "textfield", "label": "Other product", "validate": {"required": true}, "customConditional": "show = row && row.rx_name === 'other';", "tableView": true, "confirm_label": "Other product name:"}, {"key": "rx_current", "type": "radio", "label": "Are you still using it?", "inline": true, "values": [{"label": "Currently", "value": "current"}, {"label": "Past only", "value": "past"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Currently using?"}, {"key": "rx_application_freq", "type": "select", "label": "How often did / do you apply it?", "widget": "html5", "data": {"values": [{"label": "Once daily", "value": "qd"}, {"label": "Twice daily", "value": "bid"}, {"label": "Three times weekly", "value": "tiw"}, {"label": "Once weekly", "value": "qw"}, {"label": "Other / unsure", "value": "unsure"}]}, "validate": {"required": true}, "tableView": true, "confirm_label": "Application frequency:"}, {"key": "rx_adherence", "type": "radio", "label": "How closely did you follow that schedule?", "inline": false, "values": [{"label": "Almost every dose", "value": "perfect"}, {"label": "Missed a few doses", "value": "most"}, {"label": "Applied < 50 % of the time", "value": "some"}, {"label": "Rarely / stopped early", "value": "poor"}, {"label": "Not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Adherence:"}, {"key": "rx_effect", "type": "select", "label": "Effect on nail(s)", "widget": "html5", "data": {"values": [{"label": "Completely cleared the nail(s)", "value": "cleared"}, {"label": "Greatly improved but not fully cleared", "value": "very_improved"}, {"label": "Helped a little", "value": "partial"}, {"label": "No change", "value": "none"}, {"label": "Made it worse", "value": "worse"}, {"label": "Not sure", "value": "unsure"}]}, "validate": {"required": true}, "tableView": true, "confirm_label": "Observed effect:"}, {"key": "rx_last_used", "type": "select", "label": "When did you last use it?", "widget": "html5", "data": {"values": [{"label": "≤ 1 week ago", "value": "le1w"}, {"label": "1–2 weeks ago", "value": "1_2w"}, {"label": "2–4 weeks ago", "value": "2_4w"}, {"label": "1–3 months ago", "value": "1_3m"}, {"label": "3–6 months ago", "value": "3_6m"}, {"label": "6–9 months ago", "value": "6_9m"}, {"label": "9–12 months ago", "value": "9_12m"}, {"label": "> 12 months ago", "value": "gt12m"}, {"label": "Not sure", "value": "unsure"}]}, "validate": {"required": true}, "customConditional": "show = row && row.rx_current === 'past';", "tableView": true, "confirm_label": "Last used:"}], "customConditional": "show = data.past_rx_types_nail && data.past_rx_types_nail.topical;"}, {"key": "heading_oral_selected_nail", "type": "content", "html": "<br><h4>Oral antifungal pills you've used</h4>", "customConditional": "show = data.past_rx_types_nail && data.past_rx_types_nail.oral;"}, {"key": "nail_rx_oral_grid", "type": "<PERSON><PERSON><PERSON>", "label": "Add each oral medicine you've used:", "confirm_label": "Oral Rx list:", "addAnother": "+ Add Another", "tableView": false, "components": [{"key": "rx_name", "type": "select", "label": "Medicine", "widget": "html5", "data": {"values": [{"label": "Terbinafine (Lamisil)", "value": "terbinafine"}, {"label": "Itraconazole (Sporanox)", "value": "itraconazole"}, {"label": "Fluconazole (Diflucan)", "value": "fluconazole"}, {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "griseofulvin"}, {"label": "Other oral antifungal", "value": "other"}]}, "validate": {"required": true}, "tableView": true, "confirm_label": "Oral medicine:"}, {"key": "rx_other_name", "type": "textfield", "label": "Other medicine", "validate": {"required": true}, "customConditional": "show = row && row.rx_name === 'other';", "tableView": true, "confirm_label": "Other medicine name:"}, {"key": "rx_current", "type": "radio", "label": "Are you still taking it?", "inline": true, "values": [{"label": "Currently", "value": "current"}, {"label": "Past only", "value": "past"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Currently taking?"}, {"key": "rx_dose_schedule", "type": "select", "label": "Typical dosing schedule", "widget": "html5", "data": {"values": [{"label": "Once daily (continuous)", "value": "qd"}, {"label": "Pulse: 1 week on / 3 weeks off", "value": "pulse"}, {"label": "Once weekly", "value": "qw"}, {"label": "Other / unsure", "value": "unsure"}]}, "validate": {"required": true}, "tableView": true, "confirm_label": "Dosing schedule:"}, {"key": "rx_adherence", "type": "radio", "label": "How often did you miss a dose?", "inline": false, "values": [{"label": "Rarely / never", "value": "perfect"}, {"label": "Missed a few doses", "value": "most"}, {"label": "Missed many doses", "value": "some"}, {"label": "Stopped early", "value": "poor"}, {"label": "Not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Adherence:"}, {"key": "rx_effect", "type": "select", "label": "Effect on nail(s)", "widget": "html5", "data": {"values": [{"label": "Cleared / greatly improved", "value": "cleared"}, {"label": "Helped a little", "value": "partial"}, {"label": "No change", "value": "none"}, {"label": "Made it worse", "value": "worse"}, {"label": "Not sure", "value": "unsure"}]}, "validate": {"required": true}, "tableView": true, "confirm_label": "Observed effect:"}, {"key": "rx_last_used", "type": "select", "label": "When did you last take it?", "widget": "html5", "data": {"values": [{"label": "≤ 1 week ago", "value": "le1w"}, {"label": "1–2 weeks ago", "value": "1_2w"}, {"label": "2–4 weeks ago", "value": "2_4w"}, {"label": "1–3 months ago", "value": "1_3m"}, {"label": "3–6 months ago", "value": "3_6m"}, {"label": "6–9 months ago", "value": "6_9m"}, {"label": "9–12 months ago", "value": "9_12m"}, {"label": "> 12 months ago", "value": "gt12m"}, {"label": "Not sure", "value": "unsure"}]}, "validate": {"required": true}, "customConditional": "show = row && row.rx_current === 'past';", "tableView": true, "confirm_label": "Last taken:"}, {"key": "rx_side_effects", "type": "selectboxes", "label": "Side-effects experienced", "values": [{"label": "Upset stomach", "value": "gi"}, {"label": "Headache", "value": "headache"}, {"label": "Taste changes", "value": "taste"}, {"label": "Liver-enzyme increase", "value": "liver"}, {"label": "Other / unsure", "value": "other"}], "inputType": "checkbox", "tableView": true, "confirm_label": "Side-effects:"}], "customConditional": "show = data.past_rx_types_nail && data.past_rx_types_nail.oral;"}]}