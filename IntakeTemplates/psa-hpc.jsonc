{"components": [{"key": "content", "html": "<h2 style='text-align:center;'>PSA Testing Instructions</h2><p>Please answer the following questions related to your request for PSA (Prostate-Specific Antigen) blood testing.</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "heading_psa_indication", "html": "<h4>Reason for PSA Testing</h4>", "type": "content", "input": false, "label": "Reason for Testing", "tableView": false, "refreshOnChange": false}, {"key": "screening_indication_psa", "type": "selectboxes", "input": true, "label": "Please clarify why you are requesting a PSA (Prostate-Specific Antigen) test:", "values": [{"label": "I have been diagnosed with prostate cancer in the past and require monitoring", "value": "history_prostate_cancer", "shortcut": ""}, {"label": "I have had a previous elevated PSA and need follow-up", "value": "prior_elevated_psa", "shortcut": ""}, {"label": "I would like to test for preventative health / routine screening", "value": "preventative_screening", "shortcut": ""}, {"label": "I am currently taking testosterone or anabolic steroids and require monitoring", "value": "on_testosterone_or_anabolic_steroids", "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "confirm_label": "Reason(s) for PSA testing:", "optionsLabelPosition": "right"}, {"key": "other_screening_indication_psa", "type": "textarea", "input": true, "label": "Please state your “other” reason(s) for seeking PSA testing:", "tableView": true, "autoExpand": false, "confirm_label": "Other reason(s) for PSA testing:", "customConditional": "show = !!(data.screening_indication_psa && data.screening_indication_psa.other);"}, {"key": "heading_history_prostate_cancer", "html": "<h4>History of Prostate Cancer</h4>", "type": "content", "input": false, "label": "Content", "customConditional": "show = !!(data.screening_indication_psa && data.screening_indication_psa.history_prostate_cancer);"}, {"key": "pc_dx_year", "type": "number", "input": true, "label": "What year were you diagnosed with prostate cancer?", "validate": {"max": 2100, "min": 1900}, "tableView": true, "confirm_label": "Diagnosis year:", "customConditional": "show = !!(data.screening_indication_psa && data.screening_indication_psa.history_prostate_cancer);"}, {"key": "pc_treatment_received", "type": "selectboxes", "input": true, "label": "Which treatments have you received?", "values": [{"label": "Surgery to remove the prostate", "value": "prostatectomy"}, {"label": "Radiation treatment", "value": "radiation"}, {"label": "Hormone shots or tablets to lower testosterone", "value": "hormone_therapy"}, {"label": "Chemotherapy or other medicines", "value": "systemic"}, {"label": "No treatment", "value": "monitoring"}, {"label": "Other / not sure", "value": "other_unsure"}], "tableView": true, "confirm_label": "Treatment(s) received:", "customConditional": "show = !!(data.screening_indication_psa && data.screening_indication_psa.history_prostate_cancer);"}, {"key": "pc_oncology_followup", "type": "radio", "input": true, "label": "Are you currently being followed by a urologist or cancer specialist?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "tableView": true, "confirm_label": "Specialist follow-up:", "customConditional": "show = !!(data.screening_indication_psa && data.screening_indication_psa.history_prostate_cancer);"}, {"key": "pc_followup_type", "type": "selectboxes", "input": true, "label": "Which type of specialist is following you?", "values": [{"label": "Urologist", "value": "urologist"}, {"label": "Radiation Oncologist", "value": "radiation_oncologist"}, {"label": "Medical Oncologist", "value": "medical_oncologist"}, {"label": "Family Doctor / PCP", "value": "family_doctor"}, {"label": "Other / not sure", "value": "other_unsure"}], "tableView": true, "confirm_label": "Specialist type:", "customConditional": "show = data.pc_oncology_followup === 'yes';"}, {"key": "pc_last_followup_range", "data": {"values": [{"label": "Within the past 3 months", "value": "within_3m"}, {"label": "3–6 months ago", "value": "3_6m"}, {"label": "6–12 months ago", "value": "6_12m"}, {"label": "1–2 years ago", "value": "1_2y"}, {"label": "More than 2 years ago", "value": "over_2y"}, {"label": "Not sure", "value": "unsure"}]}, "type": "select", "input": true, "label": "When was your last follow-up with this specialist?", "widget": "html5", "multiple": false, "tableView": true, "confirm_label": "Last follow-up:", "customConditional": "show = data.pc_oncology_followup === 'yes';"}, {"key": "pc_specialist_location", "type": "radio", "input": true, "label": "Is your doctor located in Canada or outside Canada?", "values": [{"label": "In Canada", "value": "canada"}, {"label": "Outside Canada", "value": "outside"}], "tableView": true, "confirm_label": "Doctor location:", "customConditional": "show = data.pc_oncology_followup === 'yes';"}, {"key": "heading_prior_elevated_psa", "html": "<h4>Previous Elevated PSA</h4><p>You mentioned you had a higher PSA result before. Please tell us more about what was done after that result.</p>", "type": "content", "input": false, "label": "Content", "customConditional": "show = !!(data.screening_indication_psa && data.screening_indication_psa.prior_elevated_psa);"}, {"key": "prior_psa_biopsy_done", "type": "radio", "input": true, "label": "Have you ever had a prostate biopsy (a needle test of the prostate)?", "values": [{"label": "No", "value": "no"}, {"label": "Yes — no cancer found", "value": "benign"}, {"label": "Yes — cancer found", "value": "cancer"}], "tableView": true, "confirm_label": "Biopsy history:", "customConditional": "show = !!(data.screening_indication_psa && data.screening_indication_psa.prior_elevated_psa);"}, {"key": "prior_psa_followup_specialist", "type": "radio", "input": true, "label": "Did you see a urologist or specialist after the higher PSA?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "tableView": true, "confirm_label": "Specialist seen:", "customConditional": "show = !!(data.screening_indication_psa && data.screening_indication_psa.prior_elevated_psa);"}, {"key": "heading_preventative_screening", "html": "<h4>Routine Screening</h4>", "type": "content", "input": false, "label": "Content", "customConditional": "show = !!(data.screening_indication_psa && data.screening_indication_psa.preventative_screening);"}, {"key": "screening_family_history", "type": "radio", "input": true, "label": "Do you have a father, brother, or son who has had prostate cancer?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "unsure"}], "tableView": true, "confirm_label": "Family history:", "customConditional": "show = !!(data.screening_indication_psa && data.screening_indication_psa.preventative_screening);"}, {"key": "fh_who_diagnosed", "type": "selectboxes", "input": true, "label": "Who in your family was diagnosed?", "values": [{"label": "Father", "value": "father"}, {"label": "Brother", "value": "brother"}, {"label": "Son", "value": "son"}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "confirm_label": "Relative(s) diagnosed:", "customConditional": "show = !!(data.screening_indication_psa && data.screening_indication_psa.preventative_screening) && data.screening_family_history === 'yes';"}, {"key": "fh_father_age_dx", "data": {"values": [{"label": "< 50 years", "value": "lt50"}, {"label": "50–59 years", "value": "50_59"}, {"label": "60–69 years", "value": "60_69"}, {"label": "70+ years", "value": "70_plus"}, {"label": "Not sure", "value": "unsure"}]}, "type": "select", "input": true, "label": "Father: age at diagnosis", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Father age at diagnosis:", "customConditional": "show = !!(data.screening_indication_psa && data.screening_indication_psa.preventative_screening) && data.screening_family_history === 'yes' && data.fh_who_diagnosed && data.fh_who_diagnosed.father === true;"}, {"key": "fh_brother_age_dx", "data": {"values": [{"label": "< 50 years", "value": "lt50"}, {"label": "50–59 years", "value": "50_59"}, {"label": "60–69 years", "value": "60_69"}, {"label": "70+ years", "value": "70_plus"}, {"label": "Not sure", "value": "unsure"}]}, "type": "select", "input": true, "label": "Brother: age at diagnosis", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Brother age at diagnosis:", "customConditional": "show = !!(data.screening_indication_psa && data.screening_indication_psa.preventative_screening) && data.screening_family_history === 'yes' && data.fh_who_diagnosed && data.fh_who_diagnosed.brother === true;"}, {"key": "fh_son_age_dx", "data": {"values": [{"label": "< 50 years", "value": "lt50"}, {"label": "50–59 years", "value": "50_59"}, {"label": "60–69 years", "value": "60_69"}, {"label": "70+ years", "value": "70_plus"}, {"label": "Not sure", "value": "unsure"}]}, "type": "select", "input": true, "label": "Son: age at diagnosis", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Son age at diagnosis:", "customConditional": "show = !!(data.screening_indication_psa && data.screening_indication_psa.preventative_screening) && data.screening_family_history === 'yes' && data.fh_who_diagnosed && data.fh_who_diagnosed.son === true;"}, {"key": "screening_black_heritage", "type": "radio", "input": true, "label": "Do you identify as Black or have Caribbean or African ancestry (higher risk group)?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Prefer not to say", "value": "prefer_not"}], "tableView": true, "confirm_label": "Higher risk ancestry:", "customConditional": "show = !!(data.screening_indication_psa && data.screening_indication_psa.preventative_screening);"}, {"key": "heading_on_androgens", "html": "<h4>Monitoring While on Testosterone or Steroid Use</h4>", "type": "content", "input": false, "label": "Content", "customConditional": "show = !!(data.screening_indication_psa && data.screening_indication_psa.on_testosterone_or_anabolic_steroids);"}, {"key": "androgen_therapy_type", "data": {"values": [{"label": "Prescription testosterone injection", "value": "injection"}, {"label": "Testosterone gel or patch", "value": "gel_patch"}, {"label": "Non-prescribed testosterone", "value": "other_rx"}, {"label": "Non-prescribed anabolic steroid(s)", "value": "non_rx"}]}, "type": "select", "input": true, "label": "Which of these best describes what you are taking?", "widget": "html5", "multiple": false, "tableView": true, "confirm_label": "Current therapy:", "customConditional": "show = !!(data.screening_indication_psa && data.screening_indication_psa.on_testosterone_or_anabolic_steroids);"}, {"key": "androgen_duration", "data": {"values": [{"label": "< 1 month", "value": "lt1m"}, {"label": "1 month", "value": "1m"}, {"label": "2 months", "value": "2m"}, {"label": "3 months", "value": "3m"}, {"label": "4 months", "value": "4m"}, {"label": "5 months", "value": "5m"}, {"label": "6 months", "value": "6m"}, {"label": "7 months", "value": "7m"}, {"label": "8 months", "value": "8m"}, {"label": "9 months", "value": "9m"}, {"label": "10 months", "value": "10m"}, {"label": "11 months", "value": "11m"}, {"label": "1 year", "value": "1y"}, {"label": "2 years", "value": "2y"}, {"label": "3 years", "value": "3y"}, {"label": "4 years", "value": "4y"}, {"label": "5 years", "value": "5y"}, {"label": "More than 5 years", "value": "gt5y"}, {"label": "Not sure", "value": "unsure"}]}, "type": "select", "input": true, "label": "How long have you been on this therapy?", "widget": "html5", "multiple": false, "tableView": true, "confirm_label": "Duration on therapy:", "refreshOnChange": false, "customConditional": "show = !!(data.screening_indication_psa && data.screening_indication_psa.on_testosterone_or_anabolic_steroids);"}, {"key": "heading_other_reason_psa", "html": "<h4>Other Reason for PSA Testing</h4><p>You selected 'Other'. Please provide more detail if you'd like.</p>", "type": "content", "input": false, "label": "Content", "customConditional": "show = !!(data.screening_indication_psa && data.screening_indication_psa.other);"}, {"key": "other_reason_clarify", "type": "textfield", "input": true, "label": "Your reason (optional)", "tableView": true, "confirm_label": "Other reason:", "customConditional": "show = !!(data.screening_indication_psa && data.screening_indication_psa.other);"}, {"key": "heading_prostate_exam", "html": "<h4>Prostate Exam (Digital Rectal Exam)</h4><p>A prostate exam, also called a <strong>digital rectal exam (DRE)</strong>, is a test where a doctor inserts a lubricated, gloved finger into the rectum to feel the prostate gland. This allows the doctor to check for changes in size, shape, or texture of the prostate that might suggest conditions such as enlargement, inflammation, or cancer. The exam is usually brief and may feel a bit uncomfortable, but it should not be painful.</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "prostate_exam_last_range", "data": {"values": [{"label": "Within the past 6 months", "value": "within_6m"}, {"label": "6–12 months ago", "value": "6_12m"}, {"label": "1–2 years ago", "value": "1_2y"}, {"label": "More than 2 years ago", "value": "over_2y"}, {"label": "Never had a prostate exam", "value": "never"}, {"label": "Not sure", "value": "unsure"}]}, "type": "select", "input": true, "label": "When was your last prostate (digital rectal) exam?", "widget": "html5", "multiple": false, "tableView": true, "confirm_label": "Last prostate exam:", "refreshOnChange": false}, {"key": "prostate_exam_performed_by", "data": {"values": [{"label": "Urologist", "value": "urologist"}, {"label": "Family doctor", "value": "family_doctor"}, {"label": "Nurse practitioner", "value": "nurse_practitioner"}, {"label": "Other / not sure", "value": "other_unsure"}]}, "type": "select", "input": true, "label": "Who performed your last prostate exam?", "widget": "html5", "multiple": false, "tableView": true, "confirm_label": "<PERSON><PERSON> performed by:", "refreshOnChange": false, "customConditional": "show = !!(data.prostate_exam_last_range && data.prostate_exam_last_range !== 'never' && data.prostate_exam_last_range !== 'unsure');"}, {"key": "prostate_exam_findings", "type": "selectboxes", "input": true, "label": "What were you told about the exam findings?", "values": [{"label": "Normal exam / nothing abnormal found", "value": "normal"}, {"label": "Doctor said the prostate was enlarged (common with age)", "value": "enlarged"}, {"label": "Doctor felt a lump, firm spot, or irregular area", "value": "nodule"}, {"label": "Doctor said the prostate was tender or painful", "value": "tenderness"}, {"label": "I don’t remember or wasn’t told", "value": "unsure"}], "tableView": true, "confirm_label": "Prostate exam findings:", "customConditional": "show = !!(data.prostate_exam_last_range && data.prostate_exam_last_range !== 'never' && data.prostate_exam_last_range !== 'unsure');"}, {"key": "prostate_exam_abnormal_details", "type": "textarea", "input": true, "label": "If anything abnormal was noted, please provide details (optional)", "tableView": true, "autoExpand": false, "confirm_label": "Abnormal exam details:", "refreshOnChange": false, "customConditional": "show = !!(data.prostate_exam_findings && (data.prostate_exam_findings.enlarged || data.prostate_exam_findings.nodule || data.prostate_exam_findings.tenderness || data.prostate_exam_findings.other_unsure));"}, {"key": "intake_denial_reasons", "type": "textfield", "input": true, "label": "Intake Denial Reasons:", "hidden": true, "disabled": true, "multiple": true, "adminFlag": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "value = [];", "refreshOnChange": true}, {"key": "intake_kind", "type": "textfield", "input": true, "label": "Intake Kind", "hidden": true, "disabled": true, "tableView": true, "clearOnHide": false, "calculateValue": "value = !data.intake_denial_reasons?.length ? 'mail' : 'chat';"}, {"key": "intake_mt_key", "type": "textfield", "input": true, "label": "Intake Mail Template Key", "hidden": true, "disabled": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.intake_kind=='mail'?'intake-psa':'intake-denial'"}, {"key": "allowed_rt_keys", "type": "textfield", "input": true, "label": "Allowed Rep<PERSON>", "hidden": true, "disabled": true, "multiple": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.intake_mt_key=='intake-denial'?[]:['get-req','appointment-intake','edit-intake']"}]}