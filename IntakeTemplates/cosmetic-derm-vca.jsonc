{"components": [{"key": "cosmetic_panel_interest", "type": "selectboxes", "input": true, "label": "Are you interested in a cosmetic consultation for any of the following reasons? (Select all that apply)", "values": [{"label": "Skin Rejuvenation", "value": "skin_rejuvenation"}, {"label": "<PERSON><PERSON><PERSON>", "value": "acne_scars"}, {"label": "Wrinkles and Fine Lines", "value": "wrinkles_fine_lines"}, {"label": "Hyperpigmentation or Dark Spots", "value": "hyperpigmentation"}, {"label": "<PERSON><PERSON><PERSON>", "value": "melasma"}, {"label": "Uneven Skin Tone", "value": "uneven_skin_tone"}, {"label": "General Skin Advice", "value": "general_advice"}], "inputType": "checkbox", "tableView": true, "optionsLabelPosition": "right"}, {"key": "no_cosmetic_panel_interest", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "Please select at least one option or 'None of the above.'"}, "validate": {"custom": "valid = _.some(_.values(data.cosmetic_panel_interest)) || data.no_cosmetic_panel_interest;"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "concerned_skin_cancer", "type": "radio", "input": true, "label": "Are you concerned you may have skin cancer?", "values": [{"label": "Yes", "value": "Yes"}, {"label": "No", "value": "No"}, {"label": "I don't know", "value": "I don't know"}], "validate": {"required": true}, "tableView": true, "defaultValue": "", "customConditional": "show = _.some(_.values(data.cosmetic_panel_interest)) && !data.no_cosmetic_panel_interest;"}, {"key": "previous_skin_cancer", "type": "selectboxes", "input": true, "label": "Have you been diagnosed with any of the following types of skin cancer?", "values": [{"label": "Melanoma", "value": "melanoma", "shortcut": ""}, {"label": "Squamous Cell Carcinoma", "value": "squamous_cell_carcinoma", "shortcut": ""}, {"label": "Basal Cell Carcinoma", "value": "basal_cell_carcinoma", "shortcut": ""}], "inputType": "checkbox", "tableView": true, "optionsLabelPosition": "right"}, {"key": "no_previous_skin_cancer", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "Please select at least one option or 'None of the above.'"}, "validate": {"custom": "valid = _.some(_.values(data.previous_skin_cancer)) || data.no_previous_skin_cancer;"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "new_symptoms", "type": "selectboxes", "input": true, "label": "Have you recently experienced any of the following symptoms? (Select all that apply)", "values": [{"label": "New bruising", "value": "new_bruising", "shortcut": ""}, {"label": "A new rash", "value": "new_rash", "shortcut": ""}, {"label": "Full-body rash", "value": "full_body_rash", "shortcut": ""}, {"label": "Open wounds or sores", "value": "open_wounds", "shortcut": ""}, {"label": "I've lost weight without trying", "value": "unintentional_weight_loss", "shortcut": ""}, {"label": "I'm experiencing fevers and chills", "value": "fevers_chills", "shortcut": ""}, {"label": "I have sores in my mouth/genitals", "value": "oral_genital_sores", "shortcut": ""}, {"label": "Recent hair loss", "value": "hair_loss", "shortcut": ""}], "inputType": "checkbox", "tableView": true, "optionsLabelPosition": "right"}, {"key": "no_new_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "Please select at least one option or 'None of the above.'"}, "validate": {"custom": "valid = _.some(_.values(data.new_symptoms)) || data.no_new_symptoms;"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "no_indications", "type": "textfield", "input": true, "label": "No Cosmetic Indication:", "hidden": true, "disabled": true, "multiple": false, "tableView": true, "clearOnHide": false, "defaultValue": false, "calculateValue": "value = data.no_cosmetic_panel_interest;", "refreshOnChange": true}, {"key": "contraindication_keys", "type": "textfield", "input": true, "label": "Contraindication keys:", "hidden": true, "disabled": true, "multiple": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "value = (_.some(_.values(data.previous_skin_cancer)) && !data.no_previous_skin_cancer) || (_.some(_.values(data.new_symptoms)) && !data.no_new_symptoms) || data.concerned_skin_cancer === 'Yes' || data.concerned_skin_cancer === \"I don't know\" ? ['contraindications'] : [];", "refreshOnChange": true}, {"key": "approved_html", "type": "content", "input": true, "tableView": false, "clearOnHide": false, "defaultValue": "<h3 class='text-green'> You're eligible to complete a Cosmetic Dermatology Consultation with one of our Skin Care Specialists.</h3></br><p>TeleTest physicians will provide:<ul><li>A professional assessment of your skin concerns</li><li>Provide evidence-based recommendations to manage and treat your concerns</li><li>Provide prescription therapy where appropriate</li></ul>Please note that prescriptions will be sent to your preferred compounding pharmacy for processing. Please read more about our Cosmetic Dermatology Panel <a href='https://docs.teletest.ca/cosmetic-dermatology/consultation-requests' target='_blank'>(HERE)</a></p>"}]}