{"components": [{"key": "heading_herpes_renewal", "html": "<h1><center><strong>Herpes Prescription Renewal</strong></h1><center><p>To expedite your care, please answer the following questions to the best of your abilities. &nbsp;We ask these questions to provide the best possible care.&nbsp;</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "heading_medical_indication", "html": "<h4>Type of Prescription&nbsp;</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "medication_indication", "type": "selectboxes", "input": true, "label": "Please select what you are interested in today:", "values": [{"label": "Outbreak Medication - Genital/Anal Herpes", "value": "rxn_episodic_genital", "shortcut": ""}, {"label": "Outbreak Medication - Cold Sores", "value": "rxn_episodic_oral", "shortcut": ""}, {"label": "Once Daily Medication", "value": "supress_outbreaks", "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "optionsLabelPosition": "right"}, {"key": "other_medical_indication", "type": "textarea", "input": true, "label": "Please state your “other” reason(s) for medication:", "tableView": true, "autoExpand": false, "customConditional": "show = data.medication_indication.other;"}, {"key": "heading_medical_indication", "html": "<h4>Medical Indication&nbsp;</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "medication_use_past", "type": "radio", "input": true, "label": "Have you been given prescription medication in the past for herpes?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "previous_medication", "type": "selectboxes", "input": true, "label": "Please select what medication/supplements you've been on in the past:", "values": [{"label": "[Pill] <PERSON><PERSON><PERSON><PERSON><PERSON> <strong>(Brand: Valtrex)</strong>", "value": "valacyclovir_pill", "shortcut": ""}, {"label": "[Pill] <PERSON><PERSON><PERSON><PERSON><PERSON> <strong>(Brand: Zovirax)</strong>", "value": "acyclovir_pill", "shortcut": ""}, {"label": "[Pill] <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <strong>(<PERSON>: <PERSON><PERSON><PERSON>)</strong>", "value": "famcyclovir_pill", "shortcut": ""}, {"label": "[Pill] L-Lysine (Supplement)", "value": "lysine", "shortcut": ""}, {"label": "[Cream] Acyclovir <strong>(Brand: Zovirax)</strong>", "value": "acyclovir_cream", "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "customConditional": "show = data.medication_use_past == true;", "optionsLabelPosition": "right"}, {"key": "other_medication", "type": "textarea", "input": true, "label": "Please state what other medication you have tried:", "tableView": true, "autoExpand": false, "conditional": {"eq": "other", "show": true, "when": "previous_medication"}}, {"key": "current_supressive_therapy", "type": "radio", "input": true, "label": "Are you currently on a once daily pill to suppress herpes outbreaks?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.medication_use_past == true;", "optionsLabelPosition": "right"}, {"key": "duration_supressive_therapy", "data": {"values": [{"label": "<1 year", "value": "less_1_year"}, {"label": "1-2 years", "value": "1-2_years"}, {"label": "2-3 years", "value": "2-3_years"}, {"label": "3-4 years", "value": "3-4_years"}, {"label": "4-5 years", "value": "4-5_years"}, {"label": "6 episodes", "value": "5+_years"}]}, "type": "select", "input": true, "label": "How long have you been on supressive therapy for?", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.current_supressive_therapy == true;", "optionsLabelPosition": "right"}, {"key": "improvement_outbreak_suppression", "data": {"values": [{"label": "1 less outbreak / 12 months", "value": "1_less"}, {"label": "1-2 fewer outbreaks / 12 months", "value": "1-2_outbreaks"}, {"label": "2-3 fewer outbreaks / 12 months", "value": "2-3_outbreaks"}, {"label": "3-5 fewer outbreaks / 12 months", "value": "3-5_outbreaks"}, {"label": "5-10 fewer outbreaks / 12 months", "value": "5-10_outbreaks"}, {"label": "10+ fewer outbreaks / 12 months", "value": "10+_outbreaks"}]}, "type": "select", "input": true, "label": "How many fewer outbreaks have you experienced on supressive therapy?", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.current_supressive_therapy == true;", "optionsLabelPosition": "right"}, {"key": "previous_supressive_therapy", "type": "radio", "input": true, "label": "Were you previously on a once daily pill to suppress herpes outbreaks?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.current_supressive_therapy == false;", "optionsLabelPosition": "right"}, {"key": "diagnosis_hsv", "type": "radio", "input": true, "label": "Has a doctor or nurse practitioner diagnosed you with herpes in the past?", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No/I don't know", "value": "no_don't_know", "shortcut": ""}], "tableView": true, "optionsLabelPosition": "right"}, {"key": "swab_confirmation", "type": "radio", "input": true, "label": "Have you ever had a herpes swab to confirm a diagnosis (these are different swabs than those that test for gonorrhea/chlamydia)?", "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No/I don't know", "value": "no_don't_know", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "herpes_infection_site", "type": "selectboxes", "input": true, "label": "Please select where you have a confirmed case of herpes:", "values": [{"label": "Genital/Anal <PERSON>", "value": "genital_exposure", "shortcut": ""}, {"label": "Cold Sores", "value": "oral_exposure", "shortcut": ""}, {"label": "Hand", "value": "whitlow_hand", "shortcut": ""}, {"label": "Eye", "value": "ocular", "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "optionsLabelPosition": "right"}, {"key": "other_hsv_site", "type": "textarea", "input": true, "label": "Please state what other location you have identified:", "tableView": true, "autoExpand": false, "conditional": {"eq": "other", "show": true, "when": "herpes_infection_site"}}, {"key": "genital_herpes_heading", "html": "<h5><strong><PERSON><PERSON></strong></h5>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.herpes_infection_site.genital_exposure;"}, {"key": "quantity_herpes_outbreaks_12_months", "data": {"values": [{"label": "1 episode", "value": "1_episode"}, {"label": "2 episodes", "value": "2_episodes"}, {"label": "3 episodes", "value": "3_episodes"}, {"label": "4 episodes", "value": "4_episodes"}, {"label": "5 episodes", "value": "5_episodes"}, {"label": "6 episodes", "value": "6_episodes"}, {"label": "7 episodes", "value": "7_episodes"}, {"label": "8 episodes", "value": "8_episodes"}, {"label": "9 episodes", "value": "9_episodes"}, {"label": "10+ episodes", "value": "10+"}]}, "type": "select", "input": true, "label": "How many outbreaks in the genital or anal region have you had in the last 12 months?", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.herpes_infection_site.genital_exposure;", "optionsLabelPosition": "right"}, {"key": "genital_herpes_serotype", "type": "selectboxes", "input": true, "label": "Do you know the type of herpes outbreaks you experience in the genital area?  [The type of herpes can only be determined by the results of a swab or blood test]", "values": [{"label": "HSV 1", "value": "hsv1", "shortcut": ""}, {"label": "HSV 2", "value": "hsv2", "shortcut": ""}, {"label": "I'm not sure", "value": "not_sure", "shortcut": ""}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "customConditional": "show = data.herpes_infection_site.genital_exposure;", "optionsLabelPosition": "right"}, {"key": "orolabial_herpes_heading", "html": "<h5><strong><PERSON> Sores</strong></h5>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.herpes_infection_site.oral_exposure;"}, {"key": "quantity_orolabial herpes_12_months", "data": {"values": [{"label": "1 episode", "value": "1_episode"}, {"label": "2 episodes", "value": "2_episodes"}, {"label": "3 episodes", "value": "3_episodes"}, {"label": "4 episodes", "value": "4_episodes"}, {"label": "5 episodes", "value": "5_episodes"}, {"label": "6 episodes", "value": "6_episodes"}, {"label": "7 episodes", "value": "7_episodes"}, {"label": "8 episodes", "value": "8_episodes"}, {"label": "9 episodes", "value": "9_episodes"}, {"label": "10+ episodes", "value": "10+"}]}, "type": "select", "input": true, "label": "How many cold sore outbreaks have you had in the last 12 months? [The type of herpes can only be determined by the results of a swab or blood test]", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.herpes_infection_site.oral_exposure;", "optionsLabelPosition": "right"}, {"key": "orolabial_herpes_serotype", "type": "selectboxes", "input": true, "label": "Do you know the type of herpes outbreaks you experience in the oral area?", "values": [{"label": "HSV 1", "value": "hsv1", "shortcut": ""}, {"label": "HSV 2", "value": "hsv2", "shortcut": ""}, {"label": "I'm not sure", "value": "not_sure", "shortcut": ""}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "customConditional": "show = data.herpes_infection_site.oral_exposure;", "optionsLabelPosition": "right"}, {"key": "herpes_other_medication", "type": "textarea", "input": true, "label": "You selected 'other', please list your medication/supplements below:", "validate": {"required": true}, "tableView": true, "autoExpand": false, "conditional": {"eq": "other", "show": true, "when": "herpes_infection_sites"}}, {"key": "heading_medication_intolerance", "html": "<h4>Medication Side Effects&nbsp;</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "avoid_previous_medication", "type": "selectboxes", "input": true, "label": "Please select what medications you wish to avoid taking due to side effects:", "values": [{"label": "[Pill] <PERSON><PERSON><PERSON><PERSON><PERSON> <strong>(Brand: Valtrex)</strong>", "value": "valacyclovir_pill", "shortcut": ""}, {"label": "[Pill] <PERSON><PERSON><PERSON><PERSON><PERSON> <strong>(Brand: Zovirax)</strong>", "value": "acyclovir_pill", "shortcut": ""}, {"label": "[Pill] <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <strong>(<PERSON>: <PERSON><PERSON><PERSON>)</strong>", "value": "famcyclovir_pill", "shortcut": ""}, {"label": "[Cream] Acyclovir <strong>(Brand: Zovirax)</strong>", "value": "acyclovir_cream", "shortcut": ""}, {"label": "None of the Above", "value": "no_medication_intolerance", "shortcut": ""}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "optionsLabelPosition": "right"}, {"key": "herpes_medication_safety", "html": "<h5><strong>Medication Safety</strong></h5>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "kidney_disease", "type": "radio", "input": true, "label": "Has a doctor ever advised you that you have kidney disease or poorly functioning kidneys", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No/Don't Know", "value": "no_dont_know", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "content2", "html": "<h2>Questions for the Doctor</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "additional_questions", "type": "textarea", "input": true, "label": "Please use this area to ask any questions that you might have for your health care provider:", "tableView": true, "autoExpand": false}]}