{"components": [{"key": "bv__yeast_heading", "html": "<h2 style=\"margin-left:0.0px;\"><span class=\"text-big\"><strong>Instructions</strong></span></h2><p style=\"margin-left:0.0px;\">To expedite your care, please answer the following questions to the best of your abilities. &nbsp;If you have any questions, please list them in the space provided below.</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "heading_yeast_history", "html": "<h5><strong>Previous Yeast Infections</strong></h5>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "yeast_past", "type": "radio", "input": true, "label": "Have you been diagnosed with or thought you had yeast infections in the past?", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "last_yeast_treated", "data": {"values": [{"label": "Less than 1 Week Ago", "value": "<1_week"}, {"label": "1-2 <PERSON> Ago", "value": "1-2_weeks"}, {"label": "2-4 <PERSON> Ago", "value": "2-4_weeks"}, {"label": "1-2 Months Ago", "value": "1-2_months"}, {"label": "2-4 Months Ago", "value": "2-4_months"}, {"label": "4-6 Months Ago", "value": "4-6_months"}, {"label": "6-12 Months Ago", "value": "6-12_months"}, {"label": "1-2 <PERSON> Ago", "value": "1-2_years"}, {"label": "2+ Years Ago", "value": "2+years"}]}, "type": "select", "input": true, "label": "When was your last (confirmed or suspected) yeast infection?", "widget": "html5", "tableView": true, "customConditional": "show = data.yeast_past == 'yes';", "optionsLabelPosition": "right"}, {"key": "medication_last_used_yeast", "type": "selectboxes", "input": true, "label": "What medication did you last use to treat your yeast infection?", "inline": false, "values": [{"label": "[<strong>Brand: Diflucan</strong>] Fluconazole 150mg Pill", "value": "fluconazole", "shortcut": ""}, {"label": "[<strong>Brand: <PERSON><PERSON>n</strong>] Clotrimazole Cream", "value": "canesten", "shortcut": ""}, {"label": "[<strong>Brand: Monistat</strong>] Miconazole Cream", "value": "miconazole", "shortcut": ""}, {"label": "I didn't use any medication and it went away on it's own.", "value": "self_resolution", "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.yeast_past == 'yes';", "optionsLabelPosition": "right"}, {"key": "other_yeast_medication_used", "type": "textarea", "input": true, "label": "Please state you've used before to treat yeast infections medication(s):", "tableView": true, "autoExpand": false, "customConditional": "show = data.medication_last_used_yeast == 'other';"}, {"key": "similar_to_past_yeast", "type": "radio", "input": true, "label": "Do your symptoms feel similar to previous yeast infections?", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No/I'm not sure", "value": "no_not_sure", "shortcut": ""}], "validate": {"required": true}, "tableView": false, "customConditional": "show = data.yeast_past == 'yes';", "optionsLabelPosition": "right"}, {"key": "number_of_yeast_in_last_12_months", "type": "radio", "input": true, "label": "How many yeast infections has a health care provider diagnosed you with in the last 12 months?", "inline": false, "values": [{"label": "0", "value": "0", "shortcut": ""}, {"label": "1", "value": "1-2", "shortcut": ""}, {"label": "2-3", "value": "2-3", "shortcut": ""}, {"label": "4 or more", "value": "4", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.yeast_past == 'yes';", "optionsLabelPosition": "right"}, {"key": "number_of_suspected_yeast_in_last_12_months", "type": "radio", "input": true, "label": "How many yeast infections have you suspected you have had in the last 12 months?", "inline": false, "values": [{"label": "0", "value": "0", "shortcut": ""}, {"label": "1", "value": "1", "shortcut": ""}, {"label": "2-3", "value": "24", "shortcut": ""}, {"label": "4 or more", "value": "4", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.yeast_past == 'yes';", "optionsLabelPosition": "right"}, {"key": "heading_bv_history", "html": "<h5><strong>Previous Bacterial Vaginosis (BV) </strong></h5>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "bv_past", "type": "radio", "input": true, "label": "Have you been diagnosed with or thought you had Bacterial Vaginosis (BV) in the past?", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No/Don't Know", "value": "no_dont_know", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "last_bv_treated", "data": {"values": [{"label": "Less than 1 Week Ago", "value": "<1_week"}, {"label": "1-2 <PERSON> Ago", "value": "1-2_weeks"}, {"label": "2-4 <PERSON> Ago", "value": "2-4_weeks"}, {"label": "1-2 Months Ago", "value": "1-2_months"}, {"label": "2-4 Months Ago", "value": "2-4_months"}, {"label": "4-6 Months Ago", "value": "4-6_months"}, {"label": "6-12 Months Ago", "value": "6-12_months"}, {"label": "1-2 <PERSON> Ago", "value": "1-2_years"}, {"label": "2+ Years Ago", "value": "2+years"}]}, "type": "select", "input": true, "label": "When was your last episode (confirmed or suspected) of BV?", "widget": "html5", "tableView": true, "customConditional": "show = data.bv_past == 'yes';", "optionsLabelPosition": "right"}, {"key": "medication_last_used_bv", "type": "selectboxes", "input": true, "label": "What medication did you last use to treat BV?", "inline": false, "values": [{"label": "<strong>Pill</strong>: [<strong>Brand: <PERSON><PERSON></strong>] Metronidazole 500mg - Twice Daily for 7 days", "value": "flagyl_7_days", "shortcut": ""}, {"label": "<strong>Pill</strong>: [<strong>Brand: <PERSON><PERSON></strong>] Metronidazole 500mg - 2g Single Dose", "value": "flagyl_single_dose", "shortcut": ""}, {"label": "<strong>Pill</strong>: [<strong>Brand: <PERSON><PERSON><PERSON></strong>] Clindamycin 300mg Twice Daily for 7 days", "value": "clindamycin_oral", "shortcut": ""}, {"label": "<strong>Gel</strong>: [<strong>Brand: Flagyl</strong>] Metronidazole 0.75% gel Vaginally for 5 days", "value": "flagyl_topical", "shortcut": ""}, {"label": "<strong>Cream</strong>: [<strong>Brand: Cleocin</strong>] Clindamycin 2% cream Vaginally for 7 days", "value": "clinda_topical", "shortcut": ""}, {"label": "I didn't use any medication and it went away on it's own.", "value": "self_resolution", "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.bv_past == 'yes';", "optionsLabelPosition": "right"}, {"key": "other_bv_medication_used", "type": "textarea", "input": true, "label": "Please state you've used before to treat Bacterial Vaginosis infections medication(s):", "tableView": true, "autoExpand": false, "customConditional": "show = data.medication_last_used_bv == 'other';"}, {"key": "similar_to_past_bv", "type": "radio", "input": true, "label": "Do your symptoms today feel similar to previous Bacterial Vaginosis infections?", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No/I'm not sure", "value": "no_not_sure", "shortcut": ""}], "validate": {"required": true}, "tableView": false, "customConditional": "show = data.bv_past == 'yes';", "optionsLabelPosition": "right"}, {"key": "number_of_bv_in_last_12_months", "type": "radio", "input": true, "label": "How many bacterial vaginosis infections has a health care provider diagnosed you with in the last 12 months?", "inline": false, "values": [{"label": "0", "value": "0", "shortcut": ""}, {"label": "1", "value": "1-2", "shortcut": ""}, {"label": "2-3", "value": "2-3", "shortcut": ""}, {"label": "4 or more", "value": "4", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.yeast_bv == 'yes';", "optionsLabelPosition": "right"}, {"key": "number_of_suspected_bv_in_last_12_months", "type": "radio", "input": true, "label": "How many bacterial vaginosis infections have you suspected you have had in the last 12 months?", "inline": false, "values": [{"label": "0", "value": "0", "shortcut": ""}, {"label": "1", "value": "1", "shortcut": ""}, {"label": "2-3", "value": "24", "shortcut": ""}, {"label": "4 or more", "value": "4", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.bv_past == 'yes';", "optionsLabelPosition": "right"}, {"key": "pap_history", "html": "<h5><strong>PAP Testing</strong></h5>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "pap_past", "type": "radio", "input": true, "label": "Have you ever had a PAP test in the past?", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "I don't know what a PAP test is", "value": "doesn't_know_about_paps", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "last_pap_date", "data": {"values": [{"label": "<1 year ago", "value": "<1_year"}, {"label": "1-2 years ago", "value": "1-2_years"}, {"label": "2-3 years ago", "value": "2-3_years"}, {"label": "3-4 years ago", "value": "3-4_years"}, {"label": "4-5 years ago", "value": "4-5_years"}, {"label": "5+ years ago", "value": "5+_years"}]}, "type": "select", "input": true, "label": "When was your last PAP test completed?", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.pap_past == 'yes';", "optionsLabelPosition": "right"}, {"key": "pap_past_normal", "type": "radio", "input": true, "label": "Was your last PAP test normal and have you ever had abnormal PAP testing?", "inline": false, "values": [{"label": "My last PAP test was normal, and I've never had an abnormal PAP before.", "value": "normal_pap_lifetime"}, {"label": "My last PAP test was normal, but I had an abnormal PAP in the past.", "value": "normal_pap_abnormal_past"}, {"label": "My last PAP test was abnormal and I <strong>have follow up scheduled</strong>.", "value": "abnormal_pap_has_fu"}, {"label": "My last PAP test was abnormal and I <strong>do not have follow up scheduled</strong>.", "value": "abnormal_pap_needs_fu"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.pap_past == 'yes';", "optionsLabelPosition": "right"}]}