{"components": [{"key": "heading_birth_control", "html": "<h1><strong>Birth Control Prescription</strong></h1><p>To expedite your care, please answer the following questions to the best of your abilities. &nbsp;We ask these questions to provide the best possible care.&nbsp;</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = !data.sku || data.sku == 'fem_pn_ocp';"}, {"key": "heading_suppress_menses", "html": "<h1><strong>Prescription to Delay Your Period</strong></h1><p>To expedite your care, please answer the following questions to the best of your abilities. &nbsp;We ask these questions to provide the best possible care.&nbsp;</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.sku == 'fem_pn_suppress_menses';"}, {"key": "delay_menses_for_less_than_14_days", "type": "radio", "input": true, "label": "Do you plan on delaying your period for a duration of less than 14 days?", "inline": false, "values": [{"label": "Yes", "value": true}, {"label": "No/Don't Know", "value": false}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.sku == 'fem_pn_suppress_menses';", "optionsLabelPosition": "right"}, {"key": "delay_menses_indication", "type": "selectboxes", "input": true, "label": "Please select your reason(s) for being on a medication to delay your period:", "values": [{"label": "Travel/vacation", "value": "travel"}, {"label": "Religious Purpose", "value": "religious_purpose"}, {"label": "Other", "value": "other"}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "customConditional": "show = data.sku == 'fem_pn_suppress_menses';", "optionsLabelPosition": "right"}, {"key": "delay_menses_indication_other_response", "type": "textarea", "input": true, "label": "You selected 'other', please list your reason below:", "validate": {"required": true}, "tableView": true, "autoExpand": false, "customConditional": "show = data.sku == 'fem_pn_suppress_menses' && data.delay_menses_indication && data.delay_menses_indication.other;"}, {"key": "heading_safety_assessment", "html": "<h1>Hormonal Medication Safety Assessment</h1><p>Please answer a few questions about other health conditions. This information is medically necessary to determine if menstrual suppression pills are appropriate for you.</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "pregnancy_within_year", "type": "radio", "input": true, "label": "Do you plan on becoming pregnant within the next year?", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No/Don't Know", "value": "no_dont_know", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.sku == 'fem_pn_ocp';", "optionsLabelPosition": "right"}, {"key": "currently_on_pill", "type": "radio", "input": true, "label": "Are you currently on a birth control pill?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "ocp_brands", "type": "textfield", "input": true, "label": "OCP Brands:", "hidden": true, "disabled": true, "multiple": true, "tableView": false, "clearOnHide": false, "calculateValue": [{"label": "<PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>) [20/100]", "value": "alesseAlysenaAvianeEsmeLutera"}, {"label": "<PERSON><PERSON><PERSON><PERSON>  [0.5/35 or 1/35]", "value": "brevicon0535Or135"}, {"label": "Diane-35 (<PERSON><PERSON><PERSON>35, <PERSON><PERSON><PERSON>-35) [35/2]", "value": "diane35Cleo35Cyestra35"}, {"label": "<PERSON><PERSON> (<PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>) [30/150]", "value": "marvelonApriFreya<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Ovima, Portia) [30/150]", "value": "minOvralOvimaPortia"}, {"label": "Linessa (25/100-150)", "value": "linessa"}, {"label": "<PERSON><PERSON><PERSON> (30/1.5)", "value": "<PERSON><PERSON><PERSON>"}, {"label": "<PERSON><PERSON> (10/1)", "value": "lolo"}, {"label": "Micronor (<PERSON><PERSON><PERSON><PERSON>, Mo<PERSON><PERSON>) [0.35]", "value": "micronorJencyclaMovisse"}, {"label": "<PERSON><PERSON><PERSON> (35/0.5-1)", "value": "ortho"}, {"label": "Seasonale (Indayo) [30/150]", "value": "seasonaleIndayo"}, {"label": "<PERSON>-<PERSON><PERSON> [25/0.18-0.25]", "value": "triCyclenLo"}, {"label": "Tri-Cyclen [35/0.18-0.25]", "value": "triCyclen"}, {"label": "<PERSON><PERSON> [20/3]", "value": "yazYazPlusMya"}, {"label": "<PERSON><PERSON><PERSON> [30/3]", "value": "yasmin"}, {"label": "Other", "value": "other"}]}, {"key": "current_ocp_brand", "data": {"custom": "values = data.ocp_brands;"}, "type": "select", "input": true, "label": "Please select your current birth control pill:", "widget": "html5", "dataSrc": "custom", "validate": {"required": true}, "tableView": true, "labelProperty": "label", "valueProperty": "value", "customConditional": "show = data.currently_on_pill == true;", "optionsLabelPosition": "right"}, {"key": "on_pill_past", "type": "radio", "input": true, "label": "Have you been on birth control pills in the past?", "inline": false, "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.currently_on_pill == false;", "optionsLabelPosition": "right"}, {"key": "past_ocp_brand", "data": {"custom": "values = data.ocp_brands;"}, "type": "select", "input": true, "label": "Please select your most recent birth control pill:", "widget": "html5", "dataSrc": "custom", "validate": {"required": true}, "tableView": true, "labelProperty": "label", "valueProperty": "value", "customConditional": "show = data.on_pill_past === true;", "optionsLabelPosition": "right"}, {"key": "other_ocp_brand_response", "type": "textarea", "input": true, "label": "You selected “other birth control”, please specify the brand:", "validate": {"required": true}, "tableView": true, "autoExpand": false, "customConditional": "show = data.past_ocp_brand == 'other' || data.current_ocp_brand == 'other';"}, {"key": "sexually_active_lifetime", "type": "radio", "input": true, "label": "Have you been sexually active within your lifetime?", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "Prefer Not To Disclose", "value": "prefer_not_to_disclose", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.sku == 'fem_pn_ocp'", "optionsLabelPosition": "right"}, {"key": "ocp_indication", "type": "selectboxes", "input": true, "label": "Please select your reason(s) for being on birth control:", "values": [{"label": "Family Planning (preventing pregnancy)", "value": "family_planning", "shortcut": ""}, {"label": "Painful Menstrual Cramps", "value": "painful_menses", "shortcut": ""}, {"label": "Control Acne", "value": "manage_acne", "shortcut": ""}, {"label": "Regulate my Menstrual Cycles", "value": "regulate_menses", "shortcut": ""}, {"label": "Manage Endometriosis", "value": "manage_endometriosis", "shortcut": ""}, {"label": "Manage Menstrual Headaches", "value": "manage_menstrual_headache", "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "customConditional": "show = data.sku == 'fem_pn_ocp';", "optionsLabelPosition": "right"}, {"key": "ocp_indication_other_response", "type": "textarea", "input": true, "label": "You selected 'other', please list your reason below:", "validate": {"required": true}, "tableView": true, "autoExpand": false, "customConditional": "show = data.sku == 'fem_pn_ocp' && data.ocp_indication && data.ocp_indication.other;"}, {"key": "daily_pill_non_compliant", "type": "radio", "input": true, "label": "Taking birth control pills requires you to take a pill every day. If you are someone who is prone to forgetting or missing pills, birth control pills are not the recommended form of contraception. Do you believe you may forget or miss pills?", "inline": false, "values": [{"label": "Yes, I'm prone to forgetting pills", "value": true}, {"label": "No, I can take pills daily", "value": false}], "tableView": true, "customConditional": "show = data.sku == 'fem_pn_ocp' && data.currently_on_pill === false;", "optionsLabelPosition": "right"}, {"key": "heading_birth_control_alternatives", "html": "<h3>Alternatives to Birth Control Pills </h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.sku == 'fem_pn_ocp' && data.currently_on_pill === false;", "optionsLabelPosition": "right"}, {"key": "ocp_alternatives_reviewed", "type": "radio", "input": true, "label": "If you are not currently on birth control pills, have you reviewed the alternatives that are available (i.e. IUD, hormone shot, estrogen patch, etc)?<br><a href='https://docs.teletest.ca/order-guide-birth-control' target='_blank'>You can read more about alternatives (here)</a>.", "inline": false, "values": [{"label": "Yes, I have reviewed the alternatives", "value": true}, {"label": "No, I have not reviewed the alternatives", "value": false}], "tableView": true, "customConditional": "show = data.sku == 'fem_pn_ocp' && data.currently_on_pill === false;", "optionsLabelPosition": "right"}, {"key": "ocp_alternatives_chat_requested", "type": "radio", "input": true, "label": "Do you wish to have a conversation about birth control pill alternatives?", "inline": false, "values": [{"label": "Yes, I would like to talk with a doctor about birth control pill alternatives", "value": true}, {"label": "No, I am only interested in pill options", "value": false}], "tableView": true, "customConditional": "show = data.sku == 'fem_pn_ocp' && data.currently_on_pill === false;", "optionsLabelPosition": "right"}, {"key": "happy_with_pill", "type": "radio", "input": true, "label": "Are you happy with your birth control pill, or would you like to change it to another birth control pill or another contraceptive method?", "inline": false, "values": [{"label": "I am happy with my current pill", "value": "happy_with_pill", "shortcut": ""}, {"label": "I would like to change to another pill", "value": "change_pill", "shortcut": ""}, {"label": "I would like to trial another method ", "value": "trial_non_pill_option", "shortcut": ""}], "tableView": true, "customConditional": "show = data.sku == 'fem_pn_ocp' && data.currently_on_pill === true;", "optionsLabelPosition": "right"}, {"key": "prior_ocp_brands", "type": "textarea", "input": true, "label": "Please list any previous birth control brands you've been on, and if you didn't like a particular one. If you don't remember, please leave this area blank.", "tableView": true, "autoExpand": false, "customConditional": "show = data.sku == 'fem_pn_ocp' && data.happy_with_pill == 'change_pill';"}, {"key": "current_ocp_side_effects", "type": "selectboxes", "input": true, "label": "Please select any side effects you are having with your current birth control pill:", "values": [{"label": "<PERSON><PERSON><PERSON>", "value": "nausea", "shortcut": ""}, {"label": "Breast Tenderness", "value": "breast_tenderness", "shortcut": ""}, {"label": "Bloating and Fluid Retention", "value": "bloating_fluid_retention", "shortcut": ""}, {"label": "Headaches", "value": "headaches", "shortcut": ""}, {"label": "Painful Periods", "value": "painful_menses", "shortcut": ""}, {"label": "Low Libido", "value": "low_libido", "shortcut": ""}, {"label": "Breakthrough Bleeding", "value": "breakthrough_bleeding", "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}], "inputType": "checkbox", "tableView": true, "customConditional": "show = data.sku == 'fem_pn_ocp' && data.happy_with_pill == 'change_pill';", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_current_ocp_side_effects", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a side effect."}, "validate": {"custom": "valid = !!data.none_of_the_above_current_ocp_side_effects || _.some(_.values(data.current_ocp_side_effects));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.sku == 'fem_pn_ocp' && data.happy_with_pill == 'change_pill';"}, {"key": "current_ocp_side_effects_other_response", "type": "textarea", "input": true, "label": "You selected other, please specify your reason for wanting to change birth control pills:", "validate": {"required": true}, "tableView": true, "autoExpand": false, "customConditional": "show = data.sku == 'fem_pn_ocp' && data.current_ocp_side_effects && data.current_ocp_side_effects.other;"}, {"key": "ocp_duration", "type": "radio", "input": true, "label": "How long have you been on the same birth control for?", "values": [{"label": "3 months or less", "value": "3months_or_less", "shortcut": ""}, {"label": "More than 3 months", "value": "3+months", "shortcut": ""}], "tableView": true, "customConditional": "show = data.sku == 'fem_pn_ocp' && data.current_ocp_side_effects == 'breakthrough';", "optionsLabelPosition": "right"}, {"key": "heading_safety_assessment", "html": "<h1>Hormonal Contraception Safety Assessment</h1><p>Please answer a few questions about health conditions. &nbsp;This information is medically necessary to determine if birth control pills are appropriate for you.</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "customClass": "mt-5", "refreshOnChange": false}, {"key": "heading_cardiac-_risk_factors", "html": "<h2>Cardiac and Vascular Risk Factors</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "vascular_rf", "type": "selectboxes", "input": true, "label": "Do you have any of the following medical conditions?", "values": [{"label": "Diabetes", "value": "diabetes", "shortcut": ""}, {"label": "High Blood Pressure", "value": "htn", "shortcut": ""}, {"label": "High Cholesterol", "value": "dyslipidemia", "shortcut": ""}], "adminFlag": true, "tableView": true, "optionsLabelPosition": "right"}, {"key": "none_of_the_above_vascular_rf", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a vascular risk factor."}, "validate": {"custom": "valid = !!data.none_of_the_above_vascular_rf || _.some(_.values(data.vascular_rf));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "cardiac_rf", "type": "selectboxes", "input": true, "label": "Have you had any of the following cardiac conditions diagnosed?", "values": [{"label": "Cardiomyopathy", "value": "cardiomyopathy", "shortcut": ""}, {"label": "Heart Valve Disease ", "value": "heart_valve_disease", "shortcut": ""}, {"label": "Cardiac Stents or Bypass ", "value": "cardiac_stenting", "shortcut": ""}, {"label": "Plaque buildup in your blood vessels (heart, brain, limbs)", "value": "cad_pvd", "shortcut": ""}], "inputType": "checkbox", "tableView": true, "optionsLabelPosition": "right"}, {"key": "none_of_the_above_cardiac_rf", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a cardiac risk factor."}, "validate": {"custom": "valid = !!data.none_of_the_above_cardiac_rf || _.some(_.values(data.cardiac_rf));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "vte_risk_factor", "type": "radio", "input": true, "label": "Have you ever had blood clots in your lungs, limbs or organs (i.e. pulmonary embolus or deep vein thrombosis)?  If you have, doctors usually start you on blood thinners for a minimum of 3 months.  ", "inline": false, "values": [{"label": "Yes", "value": true}, {"label": "No", "value": false}, {"label": "I don't understand this question", "value": "did_not_understand"}], "tableView": true, "optionsLabelPosition": "right"}, {"key": "heading_diabetes_hx", "html": "<h2>Diabetes History</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.vascular_rf.diabetes;"}, {"key": "dm_duration", "type": "selectboxes", "input": true, "label": "Do any of the following apply to you:", "values": [{"label": "I have had diabetes for more than 20 years", "value": "dm_greater_20_years", "shortcut": ""}, {"label": "I've had complications from my diabetes including diabetic eye disease, nerve damage (numbness), or kidney damage", "value": "dm_complications", "shortcut": ""}, {"label": "I don't understand this question", "value": "doesn't_understand", "shortcut": ""}], "inputType": "checkbox", "tableView": true, "customConditional": "show = data.vascular_rf.diabetes;", "optionsLabelPosition": "right"}, {"key": "heading_htn_hx", "html": "<h2>Hypertension History</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.vascular_rf.htn;"}, {"key": "currently_on_BP_meds", "type": "radio", "input": true, "label": "Are you on blood pressure pills?", "inline": false, "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}, {"label": "I don't know", "value": "doesn't_know", "shortcut": ""}], "tableView": true, "customConditional": "show = data.vascular_rf.htn;", "optionsLabelPosition": "right"}, {"key": "heading_other_rf", "html": "<h2>Other Risk Factors</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "other_rf", "type": "selectboxes", "input": true, "label": "Have you had any of the following now or in the past?", "values": [{"label": "Gallstones", "value": "gallstones", "shortcut": ""}, {"label": "<PERSON><PERSON>", "value": "lupus", "shortcut": ""}, {"label": "Liver Cirrhosis", "value": "cirrhosis", "shortcut": ""}, {"label": "Liver Cancer", "value": "liver_cancer", "shortcut": ""}, {"label": "Breast Cancer", "value": "breast_cancer", "shortcut": ""}, {"label": "Hepatitis B or C", "value": "hepatitis_b_c", "shortcut": ""}, {"label": "Stroke or Mini-Stroke (TIA)", "value": "mini_stroke_tia", "shortcut": ""}], "adminFlag": true, "inputType": "checkbox", "tableView": true, "optionsLabelPosition": "right"}, {"key": "none_of_the_above_other_rf", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a risk factor."}, "validate": {"custom": "valid = !!data.none_of_the_above_other_rf || _.some(_.values(data.other_rf));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "surgeries", "type": "radio", "input": true, "label": "Have you ever had 1) an organ transplant, bariatric surgery, or 2) recently had a surgery requiring you to be bed or chair bound?", "inline": false, "values": [{"label": "Yes", "value": true}, {"label": "No", "value": false}, {"label": "I don't understand this question", "value": "did_not_understand"}], "tableView": true, "optionsLabelPosition": "right"}, {"key": "headache_hx", "type": "radio", "input": true, "label": "Do you get headaches?", "inline": false, "values": [{"label": "Yes", "value": true}, {"label": "No", "value": false}, {"label": "I don't understand this question", "value": "did_not_understand"}], "tableView": true, "optionsLabelPosition": "right"}, {"key": "headache_features", "type": "selectboxes", "input": true, "label": "Please check any of the following characteristics that apply to your headaches:", "values": [{"label": "Band-like (i.e. someone is wrapping a band around my head)", "value": "band_like"}, {"label": "Usually one sided", "value": "one-sided"}, {"label": "Dull ache", "value": "dull"}, {"label": "Head is being squeezed", "value": "squeezing"}, {"label": "Feels like a hat on my head", "value": "tension"}, {"label": "Head feels full", "value": "fullness"}, {"label": "My headache is 'pulsatile' (i.e. it feels like a throbbing or beating sensation) ", "value": "pulsatile"}, {"label": "I get nauseous (feel like throwing up) or vomit (throw up)", "value": "nausea_or_vomiting"}, {"label": "I am bothered by light or sound ", "value": "phono_photo_sensitivity"}, {"label": "Head feels full of pressure", "value": "pressure"}, {"label": "Pain is mild", "value": "mild_pain"}, {"label": "Pain is moderate", "value": "moderate_pain"}, {"label": "Pain is severe", "value": "severe_pain"}], "inputType": "checkbox", "tableView": true, "customConditional": "show = data.headache_hx === true;", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_headache_features", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a headache characteristics."}, "validate": {"custom": "valid = !!data.none_of_the_above_headache_features || _.some(_.values(data.headache_features));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.headache_hx === true;"}, {"key": "neuro_features", "type": "selectboxes", "input": true, "label": "Do you get any of the following symptoms with a headache?", "values": [{"label": "<strong>Vision Changes</strong>: any changes in your vision, zigzagging lines, loss of sight, a 'shimmering' sensation in your field of vision", "value": "vision_change", "shortcut": ""}, {"label": "<strong>Sensory Changes</strong>: tingling or a pins-and-needle feeling on your body, altered sensation on your tongue", "value": "sensory_change", "shortcut": ""}, {"label": "<strong>Muscle Changes</strong>: weakness of your face, arms or legs ", "value": "motor_changes", "shortcut": ""}, {"label": "<strong>Language Difficulties</strong>: difficulties with word-finding, difficulties speaking, selecting incorrect words when speaking", "value": "language_weakness", "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}], "adminFlag": true, "inputType": "checkbox", "tableView": true, "customConditional": "show = data.headache_hx === true;", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_neuro_features", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom you get with headaches."}, "validate": {"custom": "valid = !!data.none_of_the_above_neuro_features || _.some(_.values(data.neuro_features));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.headache_hx === true;"}, {"key": "other_neuro_features", "type": "textarea", "input": true, "label": "Please describe the other symptoms you get with headaches:", "tableView": true, "autoExpand": false, "customConditional": "show = data.neuro_features && data.neuro_features.other;"}, {"key": "neuro_symptoms_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following neurological symptoms:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "I DO NOT have the following neurological symptoms:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.neuro_features, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "migraine_hx", "type": "radio", "input": true, "label": "Do you have a history of migraines?", "inline": false, "values": [{"label": "Yes", "value": true}, {"label": "No", "value": false}, {"label": "I don't understand this question / I don't know what a migraine is", "value": "did_not_understand"}], "tableView": true, "optionsLabelPosition": "right"}, {"key": "heading_hematologic_rf", "html": "<h2>Hematologic Risk Factors</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "hematologic_rf", "type": "selectboxes", "input": true, "label": "Do you have any of the following blood disorders?", "values": [{"label": "AT deficiency", "value": "at_deficiency", "shortcut": ""}, {"label": "Protein C deficiency", "value": "protein_c_deficiency", "shortcut": ""}, {"label": "Protein S deficiency", "value": "protein_s_deficiency", "shortcut": ""}, {"label": "Factor V Leiden", "value": "factor_v_leiden", "shortcut": ""}, {"label": "Prothrombin Mutation", "value": "prothrombin_mutation", "shortcut": ""}], "adminFlag": true, "tableView": true, "optionsLabelPosition": "right"}, {"key": "none_of_the_above_hematologic_rf", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a hematologic risk factor."}, "validate": {"custom": "valid = !!data.none_of_the_above_hematologic_rf || _.some(_.values(data.hematologic_rf));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "smoking_status", "type": "radio", "input": true, "label": "Do you currently smoke cigarettes?", "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}], "tableView": true, "defaultValue": {"no": false, "yes": false, "dontKnow": false}, "confirm_label": "Current smoker:", "optionsLabelPosition": "right"}, {"key": "smoking_quantify", "type": "radio", "input": true, "label": "How many cigarettes on average do you smoke in a day?", "inline": false, "values": [{"label": "< 15 cigarette", "value": "less_15", "shortcut": ""}, {"label": "15 or more", "value": "15_or_more", "shortcut": ""}, {"label": "She<PERSON>a/hooka", "value": "15_or_more", "shortcut": ""}], "adminFlag": true, "tableView": true, "conditional": {"eq": "yes", "show": true, "when": "smoking_status"}, "optionsLabelPosition": "right"}, {"key": "medication_contraindications", "type": "selectboxes", "input": true, "label": "Are you on any of the following medications?", "values": [{"label": "Rifampin", "value": "rifampin", "shortcut": ""}, {"label": "Seizure medication (i.e. Lamotrigine, phenytoin, carbamazepine, barbiturates, primidone, topiramate, oxcarbazepine)", "value": "seizure_meds", "shortcut": ""}, {"label": "Anti-Viral Medication for HIV", "value": "HIV_meds", "shortcut": ""}, {"label": "PrEP", "value": "prep", "shortcut": ""}], "adminFlag": true, "tableView": true, "optionsLabelPosition": "right"}, {"key": "none_of_the_above_medication_contraindications", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a medication."}, "validate": {"custom": "valid = !!data.none_of_the_above_medication_contraindications || _.some(_.values(data.medication_contraindications));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "blood_pressure_measurement_heading", "html": "<h2>Blood Pressure</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "blood_pressure_record", "type": "radio", "input": true, "label": "Do you have a blood pressure measurement recorded within the last 12 weeks?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "blood_pressure_instruction", "html": "<p>Prior to your consultation time, please ensure you obtain a blood pressure measurement using a home blood pressure cuff or from a local pharmacy. Blood pressures measurements must be taken on a device with an inflatable cuff, and cannot be measured on your sports watch or smartphone.</p>", "type": "content", "conditional": {"json": {"===": [{"var": "data.blood_pressure_record"}, "no"]}}}, {"key": "recent_blood_pressure", "type": "textfield", "input": true, "label": "Please enter your recent blood pressure:", "validate": {"required": true}, "tableView": true, "conditional": {"json": {"===": [{"var": "data.blood_pressure_record"}, "yes"]}}, "placeholder": "e.g., 120/80 mmHg", "confirm_label": "Recent blood pressure (from blood pressure cuff):"}, {"key": "content2", "html": "<h2>Questions for the Doctor</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "any_other_questions", "type": "radio", "input": true, "label": "Do you have any specific questions for the healthcare provider?", "inline": false, "values": [{"label": "Yes, I have additional questions I would like to discuss", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "validate": {"required": true}, "tableView": true, "defaultValue": false, "optionsLabelPosition": "right"}, {"key": "stated_other_questions", "type": "textarea", "input": true, "label": "Please use this area to ask any questions that you might have for your health care provider:", "adminFlag": true, "tableView": true, "autoExpand": false, "customConditional": "show = data.any_other_questions === true;"}, {"tab_name": "preferences", "intake_template_key": "hpc-ocp-preferences"}, {"key": "intake_denial_reasons", "type": "textfield", "input": true, "label": "Intake Denial Reasons:", "hidden": true, "disabled": true, "multiple": true, "adminFlag": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "value = [];", "refreshOnChange": true}, {"key": "intake_kind", "type": "textfield", "input": true, "label": "Intake Kind", "hidden": true, "disabled": true, "tableView": true, "clearOnHide": false, "calculateValue": "value = !data.intake_denial_reasons?.length ? 'mail' : 'chat';"}, {"key": "intake_mt_key", "type": "textfield", "input": true, "label": "Intake Mail Template Key", "hidden": true, "disabled": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.intake_kind === 'mail' ? (data.sku === 'fem_pn_suppress_menses' ? 'intake-delay' : 'intake-ocp') : 'intake-denial';"}, {"key": "allowed_rt_keys", "type": "textfield", "input": true, "label": "Allowed Rep<PERSON>", "hidden": true, "disabled": true, "multiple": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.intake_mt_key=='intake-denial'?[]:['appointment-intake','edit-intake','get-rx']"}]}