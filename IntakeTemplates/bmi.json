{"_vid": 0, "name": "bmi", "path": "bmi", "tags": [], "type": "form", "title": "BMI", "display": "form", "settings": {}, "revisions": "", "components": [{"key": "bmiColumns", "type": "columns", "input": false, "label": "Columns", "columns": [{"pull": 0, "push": 0, "size": "md", "width": 4, "offset": 0, "components": [{"key": "height", "data": {"values": [{"label": "", "value": ""}, {"label": "≤ 150cm / ≤ 4'10\"", "value": 1.48}, {"label": "  150cm / 4'11\"  ", "value": 1.5}, {"label": "  152cm / 5'      ", "value": 1.52}, {"label": "  155cm / 5'1\"   ", "value": 1.55}, {"label": "  157cm / 5'2\"   ", "value": 1.57}, {"label": "  160cm / 5'3\"   ", "value": 1.6}, {"label": "  163cm / 5'4\"   ", "value": 1.63}, {"label": "  165cm / 5'5\"   ", "value": 1.65}, {"label": "  168cm / 5'6\"   ", "value": 1.68}, {"label": "  170cm / 5'7\"   ", "value": 1.7}, {"label": "  173cm / 5'8\"   ", "value": 1.73}, {"label": "  175cm / 5'9\"   ", "value": 1.75}, {"label": "  178cm / 5'10\"  ", "value": 1.78}, {"label": "  180cm / 5'11\"  ", "value": 1.8}, {"label": "  183cm / 6'      ", "value": 1.83}, {"label": "  185cm / 6'1\"   ", "value": 1.85}, {"label": "  188cm / 6'2\"   ", "value": 1.88}, {"label": "  190cm / 6'3\"   ", "value": 1.9}, {"label": "  193cm / 6'4\"   ", "value": 1.93}, {"label": "  195cm / 6'5\"   ", "value": 1.95}, {"label": "  198cm / 6'6\"   ", "value": 1.98}, {"label": "  200cm / 6'7\"   ", "value": 2}, {"label": "  203cm / 6'8\"   ", "value": 2.03}, {"label": "  205cm / 6'9\"   ", "value": 2.05}, {"label": "  208cm / 6'10\"  ", "value": 2.08}, {"label": "≥ 210cm / ≥ 6'11\"", "value": 2.1}]}, "type": "select", "input": true, "label": "Height", "widget": "html5", "tableView": true, "searchEnabled": false}], "currentWidth": 4}, {"pull": 0, "push": 0, "size": "md", "width": 3, "offset": 0, "components": [{"key": "weight", "type": "number", "input": true, "label": "Weight", "validate": {"max": 500, "min": 35, "step": 5}, "inputType": "text", "tableView": true}], "currentWidth": 3}, {"pull": 0, "push": 0, "size": "md", "width": 3, "offset": 0, "components": [{"key": "units", "data": {"values": [{"label": "lbs", "value": "lbs"}, {"label": "kg", "value": "kg"}]}, "type": "select", "input": true, "label": "kg / lbs", "widget": "html5", "tableView": true, "defaultValue": "lbs", "searchEnabled": false}], "currentWidth": 3}, {"pull": 0, "push": 0, "size": "md", "width": 2, "offset": 0, "components": [{"key": "bmi", "type": "textfield", "input": true, "label": "BMI", "disabled": true, "tableView": true, "displayMask": "99.9", "calculateValue": {"/": [{"/": [{"var": "data.weight"}, {"if": [{"==": [{"var": "data.units"}, "lbs"]}, 2.20462, 1]}]}, {"*": [{"var": "data.height"}, {"var": "data.height"}]}]}}], "currentWidth": 2}], "tableView": false}], "controller": "", "properties": {}, "submissionAccess": []}