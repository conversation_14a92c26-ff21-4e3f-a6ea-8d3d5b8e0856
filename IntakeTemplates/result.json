{"name": "result", "type": "form", "title": "Result", "display": "form", "components": [{"key": "skus", "type": "textfield", "input": true, "label": "SKUs", "hidden": true, "visible": false, "disabled": true, "multiple": true, "hideLabel": true, "tableView": false, "spellcheck": false, "clearOnHide": false}, {"key": "originalAssays", "type": "textfield", "input": true, "label": "Original Assays", "hidden": true, "visible": false, "disabled": true, "multiple": true, "hideLabel": true, "tableView": false, "spellcheck": false, "clearOnHide": false}, {"key": "assay_choices", "type": "textfield", "input": true, "label": "Assay Choices", "hidden": true, "visible": false, "disabled": true, "multiple": true, "hideLabel": true, "tableView": false, "spellcheck": false, "clearOnHide": false}, {"key": "showSubmit", "type": "textfield", "input": true, "label": "", "hidden": true, "visible": false, "disabled": true, "hideLabel": true, "tableView": false, "spellcheck": false, "clearOnHide": false, "calculateValue": {"!!": true}}, {"key": "pleaseClickSubmit", "type": "htmlelement", "attrs": [{"attr": "", "value": ""}], "input": false, "label": "HTML", "content": "To continue please click “Submit Form”", "tableView": false, "refreshOnChange": false, "customConditional": "show = !!data.showSubmit;"}]}