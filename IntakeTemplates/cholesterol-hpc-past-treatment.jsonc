{"components": [{"key": "heading_past_treatments", "type": "content", "input": false, "label": "Content", "tableView": true, "refreshOnChange": false, "html": "<h3>Past Cholesterol Medicines</h3><p>Tell us about any cholesterol medicines you used in the past and then stopped. This helps us avoid repeats, understand side effects, and choose better options for you.</p>"}, {"key": "any_past_stopped_meds", "type": "radio", "input": true, "label": "Have you used any cholesterol medicine in the past that was stopped?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "unsure"}], "tableView": true, "refreshOnChange": false, "validate": {"required": true}, "confirm_label": "Past medicine stopped:"}, {"key": "past_treatments_grid", "type": "<PERSON><PERSON><PERSON>", "label": "Add each past (stopped) medication:", "addAnother": "+ Add Past Medication", "customConditional": "show = data.any_past_stopped_meds === 'yes';", "openWhenEmpty": true, "defaultOpen": true, "displayAsTable": false, "useRowTemplate": true, "tableView": false, "refreshOnChange": false, "removeRow": true, "customClass": "pasttx-nohdr", "templates": {"header": "", "row": "<div class='card shadow-sm w-100'><div class='card-body py-2 px-3'><div class='d-flex justify-content-between align-items-start small w-100'><div class='me-2'><div class='fw-bold mb-1'>{{ row.medication_label || 'Medication ?' }}{{ (row.past_dose && row.past_dose.label) ? (' — ' + row.past_dose.label) : (row.past_dose && row.past_dose.value) ? (' — ' + row.past_dose.value) : (row.past_dose ? (' — ' + row.past_dose) : '') }}{{ row.past_frequency ? (' (' + ({daily:'daily', q48h:'every other day', bid:'twice daily', weekly:'weekly', q2w:'every 2 weeks', q4w:'monthly', q6mo:'every 6 months', other:'other'}[row.past_frequency] || row.past_frequency) + ')') : '' }}</div><div>Took for: {{ ({w2_4:'2–4 weeks', m1_3:'1–3 months', m3_6:'3–6 months', m6_12:'6–12 months', y1_3:'1–3 years', y3p:'3+ years'}[row.past_duration] || 'Not specified') }} • Stopped: {{ (row.stop_month==='unknown' ? 'Month ?' : _.startCase(row.stop_month)) }} {{ row.stop_year || '' }}</div><div class='mt-1'>Reason(s) to stop: {{ (function(){ var r=_.keys(_.pickBy(row.stop_reason||{})); return r.length?_.startCase(r.join(', ').replace(/_/g,' ')):'Not specified'; })() }}</div><div class='mt-1'>Side effects at the time: {{ (function(){ if(row.none_of_the_above_stop_se) return 'None'; var list=[]; var g=row.stop_side_effects||{}; list=list.concat(_.keys(_.pickBy(g))); if(['evolocumab','alirocumab'].includes(row.med_key) && row.stop_injection_site_reaction){ list.push('injection_site'); } return list.length?_.startCase(list.join(', ').replace(/_/g,' ')):'Not specified'; })() }}</div></div><div><button class='btn btn-sm btn-outline-danger removeRow'>Delete</button></div></div></div></div>"}, "rowTemplate": "<div class='card shadow-sm w-100'><div class='card-body py-2 px-3'><div class='d-flex justify-content-between align-items-start small w-100'><div class='me-2'><div class='fw-bold mb-1'>{{ row.medication_label || 'Medication ?' }}{{ (row.past_dose && row.past_dose.label) ? (' — ' + row.past_dose.label) : (row.past_dose && row.past_dose.value) ? (' — ' + row.past_dose.value) : (row.past_dose ? (' — ' + row.past_dose) : '') }}{{ row.past_frequency ? (' (' + ({daily:'daily', q48h:'every other day', bid:'twice daily', weekly:'weekly', q2w:'every 2 weeks', q4w:'monthly', q6mo:'every 6 months', other:'other'}[row.past_frequency] || row.past_frequency) + ')') : '' }}</div><div>Took for: {{ ({w2_4:'2–4 weeks', m1_3:'1–3 months', m3_6:'3–6 months', m6_12:'6–12 months', y1_3:'1–3 years', y3p:'3+ years'}[row.past_duration] || 'Not specified') }} • Stopped: {{ (row.stop_month==='unknown' ? 'Month ?' : _.startCase(row.stop_month)) }} {{ row.stop_year || '' }}</div><div class='mt-1'>Reason(s) to stop: {{ (function(){ var r=_.keys(_.pickBy(row.stop_reason||{})); return r.length?_.startCase(r.join(', ').replace(/_/g,' ')):'Not specified'; })() }}</div><div class='mt-1'>Side effects at the time: {{ (function(){ if(row.none_of_the_above_stop_se) return 'None'; var list=[]; var g=row.stop_side_effects||{}; list=list.concat(_.keys(_.pickBy(g))); if(['evolocumab','alirocumab'].includes(row.med_key) && row.stop_injection_site_reaction){ list.push('injection_site'); } return list.length?_.startCase(list.join(', ').replace(/_/g,' ')):'Not specified'; })() }}</div></div><div><button class='btn btn-sm btn-outline-danger removeRow'>Delete</button></div></div></div></div>", "components": [{"type": "columns", "key": "med_and_dose_row", "tableView": false, "columns": [{"width": 7, "components": [{"key": "medication", "type": "radio", "input": true, "label": "Select the medication (stopped)", "tableView": true, "values": [{"label": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Lipitor)", "value": "atorvastatin"}, {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Crestor)", "value": "ros<PERSON><PERSON><PERSON><PERSON>"}, {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Zocor)", "value": "simvas<PERSON>in"}, {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Pravachol)", "value": "pravastatin"}, {"label": "Ezetimibe (Ezetrol)", "value": "ezetimibe"}, {"label": "Evolocumab (Repatha)", "value": "evolocumab"}, {"label": "Alirocumab (Praluent)", "value": "aliro<PERSON><PERSON>"}, {"label": "Other (specify)", "value": "other"}], "validate": {"required": true}}, {"key": "med_key", "type": "textfield", "input": true, "label": "Selected medication key", "hidden": true, "clearOnHide": false, "tableView": true, "calculateValue": "value = row.medication || '';"}, {"key": "medication_label", "type": "textfield", "input": true, "label": "Selected medication label", "hidden": true, "clearOnHide": false, "tableView": true, "calculateValue": "var map={atorvastatin:'<PERSON><PERSON><PERSON><PERSON><PERSON> (Lipitor)', rosuvastatin:'<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Crestor)', simvastatin:'<PERSON><PERSON><PERSON><PERSON><PERSON> (Zocor)', pravastatin:'<PERSON><PERSON><PERSON><PERSON><PERSON> (Pravachol)', ezetimibe:'<PERSON><PERSON><PERSON><PERSON><PERSON> (Ezetrol)', evolocumab:'<PERSON><PERSON><PERSON><PERSON><PERSON> (Repatha)', alirocumab:'<PERSON><PERSON><PERSON><PERSON> (Praluent)', other:(row.other_medication_name||'Other')}; value = map[row.med_key] || '';"}, {"key": "other_medication_name", "type": "textfield", "input": true, "label": "Other medication name", "placeholder": "e.g., Fluvastatin (Lescol)", "tableView": true, "customConditional": "show = row.med_key === 'other';"}]}, {"width": 5, "components": [{"key": "past_dose", "type": "select", "input": true, "label": "Typical dose you were on", "widget": "html5", "tableView": true, "valueProperty": "value", "dataSrc": "custom", "data": {"custom": "var map={atorvastatin:['5 mg','10 mg','20 mg','40 mg','80 mg'],rosuvastatin:['5 mg','10 mg','20 mg','40 mg'],simvastatin:['10 mg','20 mg','40 mg'],pravastatin:['10 mg','20 mg','40 mg','80 mg'],ezetimibe:['10 mg']}; var k=row.med_key||''; var arr=(map[k]||[]).map(function(d){return {label:d, value:d};}); if(k==='other'){arr=[{label:'1 mg',value:'1 mg'},{label:'2 mg',value:'2 mg'},{label:'5 mg',value:'5 mg'},{label:'10 mg',value:'10 mg'},{label:'20 mg',value:'20 mg'},{label:'40 mg',value:'40 mg'}];} values = arr;"}, "calculateValue": "if (value && typeof value === 'object') { value = value.value || value.label || ''; }", "customConditional": "show = ['atorvastatin','rosuvastatin','simvastatin','pravastatin','ezetimibe','other'].includes(row.med_key);"}]}]}, {"key": "past_frequency", "type": "select", "input": true, "label": "How often did you take it?", "widget": "html5", "tableView": true, "data": {"values": [{"label": "Daily", "value": "daily"}, {"label": "Every other day", "value": "q48h"}, {"label": "Twice daily", "value": "bid"}, {"label": "Weekly", "value": "weekly"}, {"label": "Every 2 weeks", "value": "q2w"}, {"label": "Monthly", "value": "q4w"}, {"label": "Every 6 months", "value": "q6mo"}, {"label": "Other", "value": "other"}]}, "calculateValue": "if(!value){ var k=row.med_key||''; if(['atorvastatin','rosuvastatin','simvastatin','pravastatin','ezetimibe','other'].includes(k)) value='daily'; else if(['evolocumab','alirocumab'].includes(k)) value='q2w'; }"}, {"key": "past_duration", "type": "select", "input": true, "label": "How long did you take it before stopping?", "widget": "html5", "tableView": true, "data": {"values": [{"label": "2–4 weeks", "value": "w2_4"}, {"label": "1–3 months", "value": "m1_3"}, {"label": "3–6 months", "value": "m3_6"}, {"label": "6–12 months", "value": "m6_12"}, {"label": "1–3 years", "value": "y1_3"}, {"label": "3+ years", "value": "y3p"}]}}, {"type": "columns", "key": "stop_date_row", "columns": [{"width": 6, "components": [{"key": "stop_month", "type": "select", "input": true, "label": "Stop month", "widget": "html5", "tableView": true, "data": {"values": [{"label": "Unknown", "value": "unknown"}, {"label": "January", "value": "jan"}, {"label": "February", "value": "feb"}, {"label": "March", "value": "mar"}, {"label": "April", "value": "apr"}, {"label": "May", "value": "may"}, {"label": "June", "value": "jun"}, {"label": "July", "value": "jul"}, {"label": "August", "value": "aug"}, {"label": "September", "value": "sep"}, {"label": "October", "value": "oct"}, {"label": "November", "value": "nov"}, {"label": "December", "value": "dec"}]}}]}, {"width": 6, "components": [{"key": "stop_year", "type": "number", "input": true, "label": "Stop year (YYYY)", "placeholder": "e.g., 2023", "tableView": true, "validate": {"min": 1900, "max": 2100}}]}]}, {"key": "stop_reason", "type": "selectboxes", "input": true, "label": "Why was it stopped?", "tableView": true, "optionsLabelPosition": "right", "values": [{"label": "Side effects", "value": "side_effects"}, {"label": "Not enough improvement", "value": "limited_benefit"}, {"label": "Doctor/clinic recommendation", "value": "provider_recommendation"}, {"label": "Cost or insurance issues", "value": "cost_access"}, {"label": "Hard to take regularly", "value": "adherence"}, {"label": "Drug interaction / new medicines", "value": "interaction"}, {"label": "Pregnancy/planning pregnancy", "value": "pregnancy"}, {"label": "Lifestyle changes controlled cholesterol", "value": "lifestyle_control"}, {"label": "Switched to another medicine", "value": "switched"}, {"label": "I preferred to stop", "value": "patient_preference"}, {"label": "Other", "value": "other"}]}, {"key": "stop_side_effects", "type": "selectboxes", "input": true, "label": "Side effects at the time (if any)", "tableView": true, "optionsLabelPosition": "right", "values": [{"label": "Muscle pain/aches (myalgia)", "value": "muscle_pain"}, {"label": "Weakness or cramps", "value": "weakness_cramps"}, {"label": "Dark urine or severe muscle pain", "value": "dark_urine"}, {"label": "Abdominal pain or nausea", "value": "abdominal_pain"}, {"label": "Elevated liver enzymes noted", "value": "elevated_lfts"}, {"label": "Memory or concentration changes", "value": "cognitive"}, {"label": "Rash or allergy", "value": "rash"}, {"label": "Other side effect", "value": "other_effect"}]}, {"key": "stop_injection_site_reaction", "type": "checkbox", "input": true, "label": "Injection site reaction", "tableView": true, "customConditional": "show = ['evolocumab','alirocumab'].includes(row.med_key);"}, {"key": "none_of_the_above_stop_se", "type": "checkbox", "input": true, "label": "None of the above", "tableView": true, "customClass": "mt-n3", "defaultValue": false, "validate": {"custom": "var anyGen=_.some(_.values(row.stop_side_effects)); var anyInj=(['evolocumab','alirocumab'].includes(row.med_key)) ? !!row.stop_injection_site_reaction : false; valid = !!row.none_of_the_above_stop_se || anyGen || anyInj;"}}, {"key": "stop_notes", "type": "textfield", "input": true, "label": "Notes (optional)", "tableView": true}]}, {"key": "past_treatments_summary", "type": "textarea", "input": true, "label": "Past treatments (summary)", "autoExpand": true, "hidden": true, "disabled": true, "clearOnHide": false, "tableView": true, "confirm_label": "Past Treatments (Stopped):", "calculateValue": "var rows = data.past_treatments_grid || []; var freqMap={daily:'daily', q48h:'every other day', bid:'twice daily', weekly:'weekly', q2w:'every 2 weeks', q4w:'monthly', q6mo:'every 6 months', other:'other'}; var durMap={w2_4:'2–4 weeks', m1_3:'1–3 months', m3_6:'3–6 months', m6_12:'6–12 months', y1_3:'1–3 years', y3p:'3+ years'}; var mon={jan:'Jan', feb:'Feb', mar:'Mar', apr:'Apr', may:'May', jun:'Jun', jul:'Jul', aug:'Aug', sep:'Sep', oct:'Oct', nov:'Nov', dec:'Dec', unknown:'?'}; value = rows.map(function(r){ var dose = r.past_dose && r.past_dose.label ? r.past_dose.label : (r.past_dose && r.past_dose.value ? r.past_dose.value : (r.past_dose || '')); var stopd = (mon[r.stop_month]||'') + (r.stop_year?(' '+r.stop_year):''); var seList=[]; if(r.none_of_the_above_stop_se){seList=['None'];} else { var g=r.stop_side_effects||{}; seList=_.keys(_.pickBy(g)); if(['evolocumab','alirocumab'].includes(r.med_key) && r.stop_injection_site_reaction){ seList.push('injection_site'); } if(!seList.length){ seList=['Not specified']; } } var seText=_.startCase(seList.join(', ').replace(/_/g,' ')); var reasons = _.keys(_.pickBy(r.stop_reason||{})); var reasonsText = reasons.length?_.startCase(reasons.join(', ').replace(/_/g,' ')):'Not specified'; return (r.medication_label||'Medication ?') + (dose?(' — '+dose):'') + (r.past_frequency?(' ('+(freqMap[r.past_frequency]||r.past_frequency)+')'):'') + ' | Took for: ' + (durMap[r.past_duration]||'—') + ' | Stopped: ' + (stopd||'—') + ' | Reason(s): ' + reasonsText + ' | SE: ' + seText; }).join('\\n');"}]}