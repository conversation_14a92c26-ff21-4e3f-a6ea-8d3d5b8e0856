{"components": [{"key": "heading_past_treatments_smoking", "html": "</br><h4>Past Smoking-Cessation Treatments</h4>", "type": "content"}, {"key": "used_prescription_past_smoking", "type": "radio", "label": "Have you used prescription or nicotine-replacement medicines to quit in the past?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Used quit-smoking medicines before:"}, {"key": "prior_quit_attempts", "type": "number", "label": "How many serious quit attempts have you made (any method)?", "validate": {"required": true, "min": 0}, "tableView": true, "customConditional": "show = data.used_prescription_past_smoking === 'yes';", "confirm_label": "Number of previous quit attempts:"}, {"key": "smoking_rx_grid", "type": "<PERSON><PERSON><PERSON>", "label": "Add each medication or nicotine product you tried:", "addAnother": "+ Add Another", "customConditional": "show = data.used_prescription_past_smoking === 'yes';", "components": [{"key": "med_name", "type": "select", "tooltip": "Select the quit-smoking medicine or nicotine product you tried.", "label": "Medicine", "data": {"values": [{"label": "Champix® (varenicline)", "value": "champix"}, {"label": "Zyban® SR (bupropion)", "value": "zyban"}, {"label": "Nicotine pouches (Zonnic®)", "value": "zonnic"}, {"label": "Nicotine patch / gum / lozenge", "value": "nrt_other"}, {"label": "Other quit medication", "value": "other"}]}, "widget": "html5", "validate": {"required": true}}, {"key": "med_other_name", "type": "textfield", "tooltip": "Type the name of the medication if it isn’t in the list.", "label": "Other medicine name", "validate": {"required": true}, "customConditional": "show = row && row.med_name === 'other';"}, {"key": "highest_dose", "type": "select", "label": "Highest dose used:", "tooltip": "Choose the highest dose you reached for Champix or Zyban.", "data": {"values": [{"label": "Champix 0.5 mg twice daily (starting dose)", "value": "cx_05_bid"}, {"label": "Champix 1 mg twice daily (maximum, regular)", "value": "cx_1_bid"}, {"label": "Zyban 150 mg once daily (starting dose)", "value": "zb_150_qd"}, {"label": "Zyban 150 mg twice daily (maximum, regular)", "value": "zb_150_bid"}, {"label": "Not sure / other", "value": "other"}]}, "widget": "html5", "validate": {"required": true}, "customConditional": "show = row && ['champix','zyban'].includes(row.med_name);"}, {"key": "duration_used", "type": "select", "tooltip": "How long you kept taking this medicine.", "label": "Duration used:", "data": {"values": [{"label": "≤ 2 weeks", "value": "le2w"}, {"label": "2-4 weeks", "value": "2_4w"}, {"label": "1-2 months", "value": "1_2m"}, {"label": "2-3 months", "value": "2_3m"}, {"label": "3-6 months", "value": "3_6m"}, {"label": "> 6 months", "value": "gt6m"}, {"label": "Not sure", "value": "unsure"}]}, "widget": "html5", "validate": {"required": true}}, {"key": "smokefree_duration", "type": "select", "tooltip": "Longest stretch you stayed smoke-free while on this medicine.", "label": "Time stayed smoke free:", "data": {"values": [{"label": "None (never quit)", "value": "none"}, {"label": "< 1 month", "value": "lt1m"}, {"label": "1-3 months", "value": "1_3m"}, {"label": "3-6 months", "value": "3_6m"}, {"label": "> 6 months", "value": "gt6m"}, {"label": "Still smoke-free", "value": "current"}, {"label": "Not sure", "value": "unsure"}]}, "widget": "html5", "validate": {"required": true}}, {"key": "intolerance", "type": "radio", "tooltip": "Answer Yes if side-effects forced you to quit the medicine.", "inline": true, "label": "Did side effects force you to stop:", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}}, {"key": "last_used", "type": "select", "tooltip": "When you last took this medicine or product.", "label": "Last used:", "data": {"values": [{"label": "< 1 week", "value": "lt1w"}, {"label": "1-4 weeks", "value": "1_4w"}, {"label": "4 weeks - 3 months", "value": "4w_3m"}, {"label": "3-6 months", "value": "3_6m"}, {"label": "6-12 months", "value": "6_12m"}, {"label": "1+ year ago", "value": "gte1y"}, {"label": "Not sure", "value": "unsure"}]}, "widget": "html5", "validate": {"required": true}}, {"key": "sh_thoughts", "type": "radio", "inline": true, "label": "Thoughts of self-harm/suicide/harming others on meds:", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}}], "rowTemplate": "<div class='card shadow-sm w-100'><div class='card-body py-2 px-3'><div class='row small'><div class='col-sm-3'><strong>{{ row.med_name==='other' ? row.med_other_name : _.startCase(row.med_name) }}</strong></div><div class='col-sm-2'>{{ ({cx_05_bid:'Cx 0.5 bid',cx_1_bid:'Cx 1 bid',zb_150_qd:'Zb 150 qd',zb_150_bid:'Zb 150 bid',other:'—'}[row.highest_dose] || '') }}</div><div class='col-sm-2'>{{ ({le2w:'≤2 w','2_4w':'2-4 w','1_2m':'1-2 m','2_3m':'2-3 m','3_6m':'3-6 m','gt6m':'>6 m',unsure:'N/S'}[row.duration_used] || '') }}</div><div class='col-sm-2'>{{ ({lt1w:'<1 w','1_4w':'1-4 w','4w_3m':'4w-3m','3_6m':'3-6 m','6_12m':'6-12 m','gte1y':'1+ y',unsure:'N/S'}[row.last_used] || '') }}</div><div class='col-sm-2'>{{ ({none:'0',lt1m:'<1 m','1_3m':'1-3 m','3_6m':'3-6 m',gt6m:'>6 m',current:'Still',unsure:'N/S'}[row.smokefree_duration] || '') }}</div><div class='col-sm-1'>{{ row.intolerance==='yes' ? 'Yes' : 'No' }}</div><div class='col-sm-2'>{{ row.sh_thoughts==='yes' ? 'Yes' : 'No' }}</div></div></div></div>", "tableView": true}]}