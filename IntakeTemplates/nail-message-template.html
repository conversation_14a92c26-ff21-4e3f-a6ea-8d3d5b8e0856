{% load template_extras %}
{% with data=questionnaire.hpc.data %}
{% with summary=questionnaire.raw_formio_summary %}
{% with insured=questionnaire.insured_assays.all %}
{% with uninsured=questionnaire.uninsured_assays.all %}
{% with rxts=questionnaire.rxts.all %}

<!-- Introduction -->
<p>Hi&nbsp;{{ patient.name }},</p>
<p>
  This is {{ doctor.name }} (CPSO&nbsp;#{{ doctor.cpso_number }}) from our clinic.<br>
  If you have questions about your responses or feel your answers missed any symptoms or conditions,
  we can arrange secure real-time messaging. Otherwise, please confirm the information below so I can
  finalize any prescription.
</p>

<!-- Confirmation Section -->
<ul>
  <li><strong>You have confirmed all of the following:</strong>
    <ul>
      {% if data.sex == "female" %}
        <li>You are <em>not</em> currently pregnant or breastfeeding.</li>
      {% endif %}
      <li>You have <em>no known allergy</em> to efinaconazole, Jublia®, or related azole antifungals.</li>
      <li>You have <em>no open wounds</em> or ulcers around the affected nail(s).</li>
    </ul>
  </li>
</ul>

<!-- Intake History Summary -->
<h5>Intake History Summary</h5>
<ul class="ms-1">

  <li><strong>Treatment Plan Chosen</strong>
    <ul>
      {% for s in summary|confirm:"antifungal_plan_choice" %}
        <li>{{ s|safe }}</li>
      {% empty %}
        <li>Not specified.</li>
      {% endfor %}
    </ul>
  </li>

  <li><strong>Infection Timing&nbsp;&amp;&nbsp;Course</strong>
    <ul>
      {% for s in summary|confirm:"nail_start_time,nail_first_or_repeat,nails_onset_pattern,nail_course_since_onset" %}
        <li>{{ s|safe }}</li>
      {% empty %}
        <li>Not specified.</li>
      {% endfor %}
    </ul>
  </li>

  <li><strong>Current Infection Details</strong>
    <ul>
      {% for s in summary|confirm:"nail_count_current,affected_nail_sites,nail_symptoms_pain" %}
        <li>{{ s|safe }}</li>
      {% empty %}
        <li>Not specified.</li>
      {% endfor %}
    </ul>
  </li>

  <li><strong>Testing History</strong>
    <ul>
      {% for s in summary|confirm:"previous_nail_testing,nail_test_result,nail_test_date" %}
        <li>{{ s|safe }}</li>
      {% empty %}
        <li>Not specified.</li>
      {% endfor %}
    </ul>
  </li>

</ul>

<!-- Health Condition Summary -->
<h5>Health Condition Summary</h5>
<ul>
  {% with summary=questionnaire.raw_formio_summary %}

  <li><strong>Current Medications:</strong> 
    {% if summary.medications_list and summary.medications_list.c_val %}
      {{ summary.medications_list.c_val }}
    {% else %}
      None
    {% endif %}
  </li>

<li><strong>Medication Allergies:</strong> 
  {% if summary.medication_allergies_list and summary.medication_allergies_list.c_val %}
    {{ summary.medication_allergies_list.c_val }}
  {% else %}
    None
  {% endif %}
</li>

  <li><strong>Past Surgeries:</strong> 
    {% if summary.past_surgeries_list and summary.past_surgeries_list.c_val %}
      {{ summary.past_surgeries_list.c_val }}
    {% else %}
      None
    {% endif %}
  </li>

  <li><strong>Health Conditions:</strong> 
    {% if summary.health_conditions_list and summary.health_conditions_list.c_val %}
      {{ summary.health_conditions_list.c_val }}
    {% else %}
      None
    {% endif %}
  </li>

  {% endwith %}
</ul>

<!-- Advice Section -->
<h5>Advice</h5>
<p>
  You confirm that you have reviewed and understood the advice provided in the intake form about
  Jublia® use, potential side-effects, and the importance of nail testing when recommended.
</p>

<!-- Plan Section -->
<h5>Plan</h5>
{% if rxts %}
  <p><strong>Prescriptions:</strong></p>
  <ul>{% for rx in rxts %}<li>{{ rx.display_name }}</li>{% endfor %}</ul>
{% else %}
  <p>No prescriptions have been issued at this time.</p>
{% endif %}

<!-- Instructions Section -->
<h5>Instructions</h5>
<ol>

  {% if data.antifungal_plan_choice == "testing_only" or data.antifungal_plan_choice == "testing_plus_treatment" %}
    <li><strong>Collect a nail sample (if testing ordered):</strong>
      <ul>
        <li>Pick up the free kit at any <strong>LifeLabs</strong> location.</li>
        <li>Clip 8–10 small pieces of the affected nail (include debris under the nail).</li>
        <li>Return the kit; results are ready in about&nbsp;4&nbsp;weeks.</li>
      </ul>
    </li>
  {% endif %}

  {% if data.antifungal_plan_choice != "testing_only" %}
    <li><strong>Using Jublia® (efinaconazole&nbsp;10&nbsp;% solution):</strong>
      <ul>
        <li>Apply <em>once daily</em> to the entire nail surface, surrounding skin, and under the free edge.</li>
        <li>Make sure the nail is clean &amp; dry; let it dry fully before socks or shoes.</li>
        <li>Continue daily for up to <strong>48&nbsp;weeks</strong>—healthy nail must grow in.</li>
        <li>Trim and file the nail weekly to improve medication penetration.</li>
      </ul>
    </li>
  {% endif %}

  <li><strong>Follow-up &amp; precautions:</strong>
    <ul>
      <li>Upload two clear photos of the affected nail(s) if you have not already done so.</li>
      <li>Stop Jublia and seek care if you develop severe pain, blistering, or spreading redness.</li>
      <li>Monitor for any dark brown/black streak that widens or extends onto surrounding skin—this is very rare (&lt; 1 case / million / year) but could signal subungual melanoma.</li>
    </ul>
  </li>

</ol>

<!-- Footer -->
<p>Best regards,<br>{{ doctor.name }}</p>

{% endwith %}
{% endwith %}
{% endwith %}
{% endwith %}
{% endwith %}