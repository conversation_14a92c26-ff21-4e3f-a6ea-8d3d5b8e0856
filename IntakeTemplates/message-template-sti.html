{% load template_extras %}
{% with data=questionnaire.hpc.data %}
{% with summary=questionnaire.raw_formio_summary %}
{% with insured=questionnaire.insured_assays.all %}
{% with uninsured=questionnaire.uninsured_assays.all %}
{% with rxts=questionnaire.rxts.all %}

<p>Hi {{ patient.name }},</p>

<p>
  This is {{ doctor.name }} (CPSO #{{ doctor.cpso_number }}) &amp; clinic contact
  phone {{ doctor.phone }}.
</p>

<p>
  I've reviewed your history. If you felt that you did not understand any
  questions, or felt your answers did not accurately describe or excluded
  symptoms, we can set up an appointment for real-time secure messaging;
  otherwise we can proceed with the plan below.
</p>

<!--  Site-specific swab shipping notice: prints once if any INSURED assay
      has test_type “swab” (covers oral & rectal swabs). -->
{% for a in insured %}
  {% if a.test_type|lower == "swab" %}
    {% ifchanged %}
      <p><strong><em>
        You had selected site-specific swab testing (for oral and anal sites).
        If you proceed, please ensure the mailing address on file is correct —
        swabs will be sent to your home in discreet Amazon-style packaging and
        typically arrive within 1-2 business days.
      </em></strong></p>
    {% endifchanged %}
  {% endif %}
{% endfor %}

<h5>Plan</h5>

{% if insured or uninsured %}
  <p class="mb-0">Lab testing for the following:</p>

  {% if insured and uninsured %}
    <strong class="mb-0">Insured</strong>
  {% endif %}
  <ul>
    {% for a in insured %}
      <li>{{ a.name }} ({{ a.test_type }} test)</li>
    {% endfor %}
  </ul>

  {% if uninsured %}
    <strong class="mb-0">Uninsured</strong>
  {% endif %}
  <ul>
    {% for a in uninsured %}
      <li>{{ a.name }} ({{ a.test_type }} test)</li>
    {% endfor %}
  </ul>

  {% if data.recommend_any %}
    <p class="mb-0">The following tests were recommended to be added to your requisition:</p>
    <ul>
      {% if data.recommend_vswb %}
        <li>Vaginal self-swab
          <ul>
            <li>Bacterial vaginosis (BV)</li>
            <li>Yeast infection</li>
          </ul>
        </li>
      {% endif %}
      {% if data.recommend_uti %}
        <li>Urine culture
          <ul><li>Urinary tract infection (UTI)</li></ul>
        </li>
      {% endif %}
    </ul>
  {% endif %}
{% endif %}

{% if rxts %}
  <p>Prescription for the following:</p>
  <ul>
    {% for rx in rxts %}
      <li>{{ rx.display_name }}</li>
    {% endfor %}
  </ul>
{% endif %}

{% if data.opt_out_names %}
  <p>It was noted that you opted-out of the following assays: {{ data.opt_out_names }}</p>
{% endif %}

<p>
  Once you attend the lab to provide a sample and we receive the results, you'll
  be able to view/download them via the portal. We will contact you if anything
  requires your attention.
</p>

{% if data.concerned_about_known_exposure %}
  You mentioned that you were concerned about exposure to
  {{ data.exposure_keys|join:", " }}.
  Based on your preferences I have included prescription treatments in your plan.
{% endif %}

<p class="mb-0">By continuing with the plan you confirm:</p>
<ul>
  <li>
    You don't have any abdominal/pelvic pain or fevers/chills
    <ul><li>You'll seek same-day in-person care if you develop any</li></ul>
  </li>
  <li>Your medical history is correct (summary below)</li>
  <li>
    You understand:
    <ul>
      <li>All advice that was provided in the intake assessment</li>
      <li>Accurate testing requires waiting a certain time after the exposure date (window period)</li>
    </ul>
  </li>
</ul>

<p>Best regards,<br>{{ doctor.name }}</p>

<hr>

<p class="mb-0">Medical History Summary:</p>
<ul>
  {% for s in summary|confirm:"sti_testing_requested,testing_indication,stated_reason_for_testing,last_sti_test,past_std,previous_syphilis_positive,previous_syphilis_treatment,syphilis_last_treatment,syphilis_treatment_location,last_rpr_titre_level,last_rpr_test,previous_hiv_positive,unprotected_sex,number_of_recent_sexual_partners,partner_sex,type_of_sex,opt_out_only_oral_anal,concerned_about_known_exposure,type_of_exposure,list_of_confirmed_exposures,other_sti_exposure_reason,confirm_all_three_stis,last_exposure_time,confirm_hiv_positive,hiv_last_exposure,pep_taken,confirm_syphilis_positive,syphilis_symptoms,no_syphilis_symptoms,currently_pregnant,vaginal_sex_without_contraception" %}
    <li>{{ s|safe }}</li>
  {% endfor %}
</ul>

<!-- hepatitis screening -->
{% with hqas=summary|confirm:"Hepatitis Risk & Screening:interested_in_hep_screening,no_hep_b_c_birth_country_risk,country_of_birth;Previous Testing & Vaccination:previous_hep_b_test,previous_hep_c_test,hep_b_vaccinated,hep_b_vaccine_advice;Hepatitis C Risk Factors:hep_c_positive,hep_c_risk_factors_since_last_test,please_explain_how_you_were_exposed_through_equipment;Screening Recommendations:recommend_hepb,recommend_hepc" %}
{% if hqas %} {% for heading,qas in hqas.items %}
<h6>{{heading}}</h6>
<ul>
  {% for qa in qas %}<li>{{qa|safe}}</li>{% endfor %}
</ul>
{% endfor %}{% endif %}{% endwith %}

<!-- Urogenital Symptom Summary -->
<h5>Urinary / Genitourinary Symptoms</h5>

{% with hqas=summary|confirm:"Urinary Symptoms:urinary_symptoms_present;Symptom Triggers:urinary_symptom_triggers;Onset Pattern:urinary_symptom_onset_pattern,all_urinary_symptoms_start;Dysuria Details:urinary_dysuria_quality;Urgency/Frequency Pattern:urinary_urgency_pattern;Night-time Waking:urinary_nighttime_waking;Hematuria Timing:hematuria_timing_pattern;Hematuria Appearance:hematuria_appearance_description;Fever Details:fever_measured_or_felt,fever_temp_over_38;Side / Back Pain:side_pain_character;Penile Discharge:penile_discharge_description,penile_discharge_frequency;Testicular Pain:testicular_side_affected" %}
{% if hqas %}
<h6>Symptoms Present</h6>
<ul>
  {% for heading,qas in hqas.items %}
  <li>{{ heading }}</li>
  <ul>
    {% for qa in qas %}<li>{{ qa|safe }}</li>{% endfor %}
  </ul>
  {% endfor %}
</ul>
{% endif %}{% endwith %}

<!-- Vaginal / Vulvovaginal Symptom Summary -->
<h5>Vaginal / Vulvovaginal Symptoms</h5>

{% with hqas=summary|confirm:"Vaginal Symptoms:vaginal_symptoms_present;Symptom Triggers:vaginal_symptom_triggers;Onset Pattern:vaginal_symptom_onset_pattern,all_vaginal_symptoms_start;Itchiness Location:vaginal_itchiness_location;Discharge Details:vaginal_discharge_color,vaginal_discharge_consistency;Odour Description:vaginal_odour_description;Dryness Relation:dryness_relationship_intercourse;Bleeding Pattern:bleeding_correlation;Lump Characteristics:vaginal_lump_characteristics;Skin Rash:vaginal_skin_rash_characteristics" %}
{% if hqas %}
<h6>Symptoms Present</h6>
<ul>
  {% for heading,qas in hqas.items %}
  <li>{{ heading }}</li>
  <ul>
    {% for qa in qas %}<li>{{ qa|safe }}</li>{% endfor %}
  </ul>
  {% endfor %}
</ul>
{% endif %}{% endwith %}

<!-- Vaginal / Vulvovaginal Symptom Summary -->
<h5>Vaginal / Vulvovaginal Symptoms</h5>

{% with hqas=summary|confirm:"Vaginal Symptoms:vaginal_symptoms_present;Symptom Triggers:vaginal_symptom_triggers;Onset Pattern:vaginal_symptom_onset_pattern,all_vaginal_symptoms_start;Itchiness Location:vaginal_itchiness_location;Discharge Details:vaginal_discharge_color,vaginal_discharge_consistency;Odour Description:vaginal_odour_description;Dryness Relation:dryness_relationship_intercourse;Bleeding Pattern:bleeding_correlation;Lump Characteristics:vaginal_lump_characteristics;Skin Rash:vaginal_skin_rash_characteristics" %}
{% if hqas %}
<h6>Symptoms Present</h6>
<ul>
  {% for heading,qas in hqas.items %}
  <li>{{ heading }}</li>
  <ul>
    {% for qa in qas %}<li>{{ qa|safe }}</li>{% endfor %}
  </ul>
  {% endfor %}
</ul>
{% endif %}{% endwith %}

<!-- Cold / Flu-like Symptom Summary -->
<h5>Cold / Flu-like Symptoms</h5>

{% with hqas=summary|confirm:"Cold Symptoms:cold_symptoms_present;Onset Pattern:cold_symptom_onset_pattern,all_cold_symptoms_start;Runny/Stuffy Nose:cold_rhinorrhea_quality;Sore Throat / Hoarseness:cold_sore_throat_quality;Cough Details:cold_cough_quality;Mouth Sores:mouth_sores_description" %}
{% if hqas %}
<h6>Symptoms Present</h6>
<ul>
  {% for heading,qas in hqas.items %}
  <li>{{ heading }}</li>
  <ul>
    {% for qa in qas %}<li>{{ qa|safe }}</li>{% endfor %}
  </ul>
  {% endfor %}
</ul>
{% endif %}{% endwith %}

<h6>Symptoms NOT Present</h6>
<ul>
  {% for qa in summary|confirm:"cold_symptoms_not_present,none_of_the_above_cold_symptom_overview,none_of_the_above_cough_red_flag_symptoms" %}
  <li>{{ qa|safe }}</li>
  {% endfor %}
</ul>


<h6>Symptoms NOT Present</h6>
<ul>
  {% for qa in summary|confirm:"vaginal_symptoms_not_present,vaginal_symptom_triggers_not_present,vaginal_discharge_consistency_not_present,vaginal_odour_description_not_present,vaginal_skin_rash_characteristics_not_present,bleeding_correlation_not_present,vaginal_itchiness_location_not_present,vaginal_lump_characteristics_not_present" %}
  <li>{{ qa|safe }}</li>
  {% endfor %}
</ul>

{# ──────────────────────────────────────────────────────────────────────────────
   WINDOW-PERIOD SECTION
   • prints only while at least one assay is still in a window period
   • one row per assay
   • “cleared” replaces the date once that window has passed
   ─────────────────────────────────────────────────────────────────────────── #}

{% with summary|confirm:"not_in_early_window_period,not_in_late_window_period" as outstanding %}
{% with data.in_windows as wp %}

  {# render nothing if every assay has fully cleared #}
  {% if outstanding and wp %}

    <p class="mb-0">Exposure&nbsp;date:&nbsp;{{ data.exposure_date }}</p>

    <p class="mb-0">Window&nbsp;Periods:</p>

    <table class="table table-sm">
      <thead>
        <tr>
          <th>Assay</th>
          <th>Early&nbsp;window</th>
          <th>Late&nbsp;window</th>
        </tr>
      </thead>
      <tbody>
        {# show only assays that still need watching #}
        {% for code, v in wp.items %}
          {% if not v.in_early or not v.in_late %}
            <tr>
              <td>{{ v.name }}</td>
              <td>
                {% if v.in_early %}
                  <span class="text-success">cleared</span>
                {% else %}
                  {{ v.early }}
                {% endif %}
              </td>
              <td>
                {% if v.in_late %}
                  <span class="text-success">cleared</span>
                {% else %}
                  {{ v.late }}
                {% endif %}
              </td>
            </tr>
          {% endif %}
        {% endfor %}
      </tbody>
    </table>

  {% endif %}

{% endwith %}
{% endwith %}


{% endwith %}
{% endwith %}
{% endwith %}
{% endwith %}
{% endwith %}