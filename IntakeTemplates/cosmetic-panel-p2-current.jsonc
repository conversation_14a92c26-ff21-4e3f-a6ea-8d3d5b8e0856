{"components": [{"key": "current_heading_current_treatments", "html": "<h1><strong>Current Treatments</strong></h1>", "type": "content", "input": false, "label": "Content"}, {"key": "current_heading_current_treatments_intro", "html": "<p>This section is for recording any treatments you are <strong>currently using or actively undergoing</strong>. If you are not on any prescription medications or receiving any procedures at this time, you'll have the chance to detail any past treatments in the following section.</p>", "type": "content", "input": false, "label": "Content", "tableView": false}, {"key": "current_heading_medications", "html": "<h2><strong>Medications</strong></h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "current_doctor_prescribed_treatments", "type": "radio", "input": true, "label": "Are you currently on any medications prescribed by a doctor for your skin?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "medication_classes", "type": "selectboxes", "input": true, "label": "Which classes of skin medications are you currently using? (Select all that apply)", "values": [{"label": "Topical Retinoids (i.e. <PERSON><PERSON><PERSON>in, Adapalene, Tazarotene)", "value": "current_topical_retinoids"}, {"label": "Oral Retinoids (i.e. Accutane)", "value": "current_oral_retinoids"}, {"label": "Benzoyl Peroxide (BPO)", "value": "bpo"}, {"label": "Topical Antibiotics (i.e. Clindamycin)", "value": "current_topical_antibiotics"}, {"label": "Oral Antibiotics (i.e. Doxycycline)", "value": "oral_antibiotics"}, {"label": "Skin Lightening Agents (i.e. Hydroquinone, Triple Cream)", "value": "current_skin_lightening_agents"}], "tableView": true, "customConditional": "show = data.current_doctor_prescribed_treatments === 'yes';", "optionsLabelPosition": "right"}, {"key": "current_heading_current_topical_retinoids", "html": "<h3><strong>Topical Retinoids</strong></h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.medication_classes && data.medication_classes.current_topical_retinoids;"}, {"key": "current_topical_retinoids", "type": "selectboxes", "input": true, "label": "Which topical retinoids are you currently using? (Select all that apply)", "values": [{"label": "Tretinoin", "value": "tretinoin"}, {"label": "Adapal<PERSON>", "value": "adapalene"}, {"label": "Tazarotene", "value": "tazarotene"}, {"label": "None of the above", "value": "none_of_the_above"}, {"label": "Other", "value": "other"}], "tableView": true, "customConditional": "show = data.medication_classes && data.medication_classes.current_topical_retinoids;", "optionsLabelPosition": "right"}, {"key": "current_heading_tretinoin", "html": "<h4><strong>Tretinoin</strong></h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.current_topical_retinoids && data.current_topical_retinoids.tretinoin;"}, {"key": "current_tretinoin_strength", "type": "radio", "input": true, "label": "What strength of Tretinoin are you using?", "values": [{"label": "0.025%", "value": "0.025"}, {"label": "0.05%", "value": "0.05"}, {"label": "0.1%", "value": "0.1"}], "tableView": true, "customConditional": "show = data.current_topical_retinoids && data.current_topical_retinoids.tretinoin;"}, {"key": "current_tretinoin_duration", "data": {"values": [{"label": "Less than 1 month", "value": "less_than_1_month"}, {"label": "1-3 months", "value": "1_3_months"}, {"label": "3-6 months", "value": "3_6_months"}, {"label": "6-12 months", "value": "6_12_months"}, {"label": "1-2 years", "value": "1_2_years"}, {"label": "More than 2 years", "value": "more_than_2_years"}]}, "type": "select", "input": true, "label": "How long have you been using Tretinoin?", "widget": "html5", "tableView": true, "customConditional": "show = data.current_topical_retinoids && data.current_topical_retinoids.tretinoin;"}, {"key": "current_tretinoin_frequency", "data": {"values": [{"label": "Daily", "value": "daily"}, {"label": "Every other day", "value": "every_other_day"}, {"label": "Twice a week", "value": "twice_weekly"}, {"label": "Weekly", "value": "weekly"}]}, "type": "select", "input": true, "label": "How often do you apply Tretinoin?", "widget": "html5", "tableView": true, "customConditional": "show = data.current_topical_retinoids && data.current_topical_retinoids.tretinoin;"}, {"key": "current_tretinoin_tolerability", "type": "radio", "input": true, "label": "How well do you tolerate Tretinoin?", "values": [{"label": "Very well (no irritation)", "value": "very_well"}, {"label": "Moderate (some irritation)", "value": "moderate"}, {"label": "Poorly (significant irritation)", "value": "poorly"}], "tableView": true, "customConditional": "show = data.current_topical_retinoids && data.current_topical_retinoids.tretinoin;"}, {"key": "current_tretinoin_improvement", "type": "radio", "input": true, "label": "Have you noticed an improvement in your skin since using Tretinoin?", "values": [{"label": "Significant improvement", "value": "significant_improvement"}, {"label": "Moderate improvement", "value": "moderate_improvement"}, {"label": "Slight improvement", "value": "slight_improvement"}, {"label": "No improvement", "value": "no_improvement"}, {"label": "Worsening", "value": "worsening"}, {"label": "Unsure", "value": "unsure"}], "tableView": true, "customConditional": "show = data.current_topical_retinoids && data.current_topical_retinoids.tretinoin;"}, {"key": "current_heading_adapalene", "html": "<h4><strong><PERSON><PERSON><PERSON></strong></h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.current_topical_retinoids && data.current_topical_retinoids.adapalene;"}, {"key": "current_adapalene_strength", "type": "radio", "input": true, "label": "What strength of Adapalene are you using?", "values": [{"label": "0.1%", "value": "0.1"}, {"label": "0.3%", "value": "0.3"}], "tableView": true, "customConditional": "show = data.current_topical_retinoids && data.current_topical_retinoids.adapalene;"}, {"key": "current_adapalene_duration", "data": {"values": [{"label": "Less than 1 month", "value": "less_than_1_month"}, {"label": "1-3 months", "value": "1_3_months"}, {"label": "3-6 months", "value": "3_6_months"}, {"label": "6-12 months", "value": "6_12_months"}, {"label": "1-2 years", "value": "1_2_years"}, {"label": "More than 2 years", "value": "more_than_2_years"}]}, "type": "select", "input": true, "label": "How long have you been using Adapalene?", "widget": "html5", "tableView": true, "customConditional": "show = data.current_topical_retinoids && data.current_topical_retinoids.adapalene;"}, {"key": "current_adapalene_frequency", "data": {"values": [{"label": "Daily", "value": "daily"}, {"label": "Every other day", "value": "every_other_day"}, {"label": "Twice a week", "value": "twice_weekly"}, {"label": "Weekly", "value": "weekly"}]}, "type": "select", "input": true, "label": "How often do you apply Adapalene?", "widget": "html5", "tableView": true, "customConditional": "show = data.current_topical_retinoids && data.current_topical_retinoids.adapalene;"}, {"key": "current_adapalene_tolerability", "type": "radio", "input": true, "label": "How well do you tolerate <PERSON><PERSON><PERSON>?", "values": [{"label": "Very well (no irritation)", "value": "very_well"}, {"label": "Moderate (some irritation)", "value": "moderate"}, {"label": "Poorly (significant irritation)", "value": "poorly"}], "tableView": true, "customConditional": "show = data.current_topical_retinoids && data.current_topical_retinoids.adapalene;"}, {"key": "current_adapalene_improvement", "type": "radio", "input": true, "label": "Have you noticed an improvement in your skin since using Adapalene?", "values": [{"label": "Significant improvement", "value": "significant_improvement"}, {"label": "Moderate improvement", "value": "moderate_improvement"}, {"label": "Slight improvement", "value": "slight_improvement"}, {"label": "No improvement", "value": "no_improvement"}, {"label": "Worsening", "value": "worsening"}, {"label": "Unsure", "value": "unsure"}], "tableView": true, "customConditional": "show = data.current_topical_retinoids && data.current_topical_retinoids.adapalene;"}, {"key": "current_heading_tazarotene", "html": "<h4><strong><PERSON><PERSON><PERSON><PERSON></strong></h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.current_topical_retinoids && data.current_topical_retinoids.tazarotene;"}, {"key": "current_tazarotene_strength", "type": "radio", "input": true, "label": "What strength of Tazarotene are you using?", "values": [{"label": "0.05%", "value": "0.05"}, {"label": "0.1%", "value": "0.1"}], "tableView": true, "customConditional": "show = data.current_topical_retinoids && data.current_topical_retinoids.tazarotene;"}, {"key": "current_tazarotene_duration", "data": {"values": [{"label": "Less than 1 month", "value": "less_than_1_month"}, {"label": "1-3 months", "value": "1_3_months"}, {"label": "3-6 months", "value": "3_6_months"}, {"label": "6-12 months", "value": "6_12_months"}, {"label": "1-2 years", "value": "1_2_years"}, {"label": "More than 2 years", "value": "more_than_2_years"}]}, "type": "select", "input": true, "label": "How long have you been using Tazarotene?", "widget": "html5", "tableView": true, "customConditional": "show = data.current_topical_retinoids && data.current_topical_retinoids.tazarotene;"}, {"key": "current_tazarotene_frequency", "data": {"values": [{"label": "Daily", "value": "daily"}, {"label": "Every other day", "value": "every_other_day"}, {"label": "Twice a week", "value": "twice_weekly"}, {"label": "Weekly", "value": "weekly"}]}, "type": "select", "input": true, "label": "How often do you apply Tazarotene?", "tableView": true, "customConditional": "show = data.current_topical_retinoids && data.current_topical_retinoids.tazarotene;"}, {"key": "current_tazarotene_tolerability", "type": "radio", "input": true, "label": "How well do you tolerate <PERSON><PERSON><PERSON><PERSON>?", "values": [{"label": "Very well (no irritation)", "value": "very_well"}, {"label": "Moderate (some irritation)", "value": "moderate"}, {"label": "Poorly (significant irritation)", "value": "poorly"}], "tableView": true, "customConditional": "show = data.current_topical_retinoids && data.current_topical_retinoids.tazarotene;"}, {"key": "current_tazarotene_improvement", "type": "radio", "input": true, "label": "Have you noticed an improvement in your skin since using Tazarotene?", "values": [{"label": "Significant improvement", "value": "significant_improvement"}, {"label": "Moderate improvement", "value": "moderate_improvement"}, {"label": "Slight improvement", "value": "slight_improvement"}, {"label": "No improvement", "value": "no_improvement"}, {"label": "Worsening", "value": "worsening"}, {"label": "Unsure", "value": "unsure"}], "tableView": true, "customConditional": "show = data.current_topical_retinoids && data.current_topical_retinoids.tazarotene;"}, {"key": "current_heading_current_oral_retinoids", "html": "<h3><strong>Oral Retinoids</strong></h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.medication_classes && data.medication_classes.current_oral_retinoids;"}, {"key": "current_oral_retinoids", "type": "selectboxes", "input": true, "label": "Which oral retinoids are you currently using? (Select all that apply)", "values": [{"label": "Accutane", "value": "accutane"}, {"label": "<PERSON><PERSON><PERSON>", "value": "clarus"}, {"label": "Epuris", "value": "epuris"}, {"label": "Acitretin", "value": "acitretin"}, {"label": "None of the above", "value": "none_of_the_above"}, {"label": "Other", "value": "other"}], "tableView": true, "customConditional": "show = data.medication_classes && data.medication_classes.current_oral_retinoids;", "optionsLabelPosition": "right"}, {"key": "current_oral_retinoid_duration", "data": {"values": [{"label": "Less than 1 month", "value": "less_than_1_month"}, {"label": "1-3 months", "value": "1_3_months"}, {"label": "3-6 months", "value": "3_6_months"}, {"label": "6-12 months", "value": "6_12_months"}, {"label": "1-2 years", "value": "1_2_years"}, {"label": "More than 2 years", "value": "more_than_2_years"}]}, "type": "select", "input": true, "label": "How long have you been taking oral retinoids?", "widget": "html5", "tableView": true, "customConditional": "show = data.current_oral_retinoids && (_.some(_.values(data.current_oral_retinoids)));"}, {"key": "current_oral_retinoid_improvement", "type": "radio", "input": true, "label": "Have you noticed an improvement in your skin since taking oral retinoids?", "values": [{"label": "Significant improvement", "value": "significant_improvement"}, {"label": "Moderate improvement", "value": "moderate_improvement"}, {"label": "Slight improvement", "value": "slight_improvement"}, {"label": "No improvement", "value": "no_improvement"}, {"label": "Worsening", "value": "worsening"}, {"label": "Unsure", "value": "unsure"}], "tableView": true, "customConditional": "show = data.current_oral_retinoids && (_.some(_.values(data.current_oral_retinoids)));"}, {"key": "current_oral_retinoid_tolerability", "type": "radio", "input": true, "label": "How well are you tolerating the oral retinoid?", "values": [{"label": "Very well (no side effects)", "value": "very_well"}, {"label": "Moderately well (mild side effects)", "value": "moderately_well"}, {"label": "Poorly (moderate side effects)", "value": "poorly"}, {"label": "Very poorly (severe side effects)", "value": "very_poorly"}], "tableView": true, "customConditional": "show = data.current_oral_retinoids && (_.some(_.values(data.current_oral_retinoids)));"}, {"key": "current_heading_bpo", "html": "<h3><strong>Benzoyl Peroxide (BPO)</strong></h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.medication_classes && data.medication_classes.bpo;"}, {"key": "current_bpo_products", "type": "selectboxes", "input": true, "label": "What types of Benzoyl Peroxide (BPO) products are you using? (Select all that apply)", "values": [{"label": "<PERSON>el", "value": "gel"}, {"label": "Cream", "value": "cream"}, {"label": "Foam", "value": "foam"}, {"label": "Wash", "value": "wash"}], "tableView": true, "customConditional": "show = data.medication_classes && data.medication_classes.bpo;", "optionsLabelPosition": "right"}, {"key": "current_bpo_strength", "type": "selectboxes", "input": true, "label": "What strength of Benzoyl Peroxide (BPO) are you using? (Select all that apply)", "values": [{"label": "2.5%", "value": "2_5_percent"}, {"label": "5%", "value": "5_percent"}, {"label": "10%", "value": "10_percent"}, {"label": "Other", "value": "other"}], "tableView": true, "customConditional": "show = data.current_bpo_products && _.some(_.values(data.current_bpo_products));"}, {"key": "current_bpo_application_area", "type": "selectboxes", "input": true, "label": "Where do you apply Benzoyl Peroxide?", "values": [{"label": "Face", "value": "face"}, {"label": "Chest", "value": "chest"}, {"label": "Back", "value": "back"}, {"label": "Other", "value": "other"}], "tableView": true, "customConditional": "show = data.current_bpo_strength && _.some(_.values(data.current_bpo_strength));"}, {"key": "current_bpo_frequency", "data": {"values": [{"label": "Once daily", "value": "once_daily"}, {"label": "Twice daily", "value": "twice_daily"}, {"label": "A few times a week", "value": "few_times_week"}, {"label": "Occasionally", "value": "occasionally"}]}, "type": "select", "input": true, "label": "How often do you apply Benzoyl Peroxide?", "widget": "html5", "tableView": true, "customConditional": "show = data.current_bpo_application_area && _.some(_.values(data.current_bpo_application_area));"}, {"key": "current_bpo_tolerability", "type": "radio", "input": true, "label": "How well do you tolerate Benzoyl Peroxide?", "values": [{"label": "Very well (no irritation)", "value": "very_well"}, {"label": "Moderately well (mild irritation)", "value": "moderately_well"}, {"label": "Poorly (moderate irritation)", "value": "poorly"}, {"label": "Very poorly (severe irritation)", "value": "very_poorly"}], "tableView": true, "customConditional": "show = data.current_bpo_frequency && data.current_bpo_frequency !== 'none';"}, {"key": "current_heading_current_topical_antibiotics", "html": "<h3><strong>Topical Antibiotics</strong></h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.medication_classes && data.medication_classes.current_topical_antibiotics;"}, {"key": "current_topical_antibiotics", "type": "selectboxes", "input": true, "label": "Which topical antibiotics are you currently using? (Select all that apply)", "values": [{"label": "Clindamycin", "value": "clindamycin"}, {"label": "Erythromycin", "value": "erythromycin"}, {"label": "Metronidazole", "value": "metronidazole"}], "tableView": true, "customConditional": "show = data.medication_classes && data.medication_classes.current_topical_antibiotics;", "optionsLabelPosition": "right"}, {"key": "current_heading_clindamycin", "html": "<h4><strong>Clindamy<PERSON></strong></h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.current_topical_antibiotics && data.current_topical_antibiotics.clindamycin;"}, {"key": "current_clindamycin_strength", "type": "radio", "input": true, "label": "What strength of Clindamycin are you using?", "values": [{"label": "1%", "value": "1_percent"}, {"label": "2%", "value": "2_percent"}, {"label": "Other", "value": "other"}], "tableView": true, "customConditional": "show = data.current_topical_antibiotics && data.current_topical_antibiotics.clindamycin;"}, {"key": "current_clindamycin_application_area", "type": "selectboxes", "input": true, "label": "Where do you apply Clindamycin?", "values": [{"label": "Face", "value": "face"}, {"label": "Chest", "value": "chest"}, {"label": "Back", "value": "back"}, {"label": "Other", "value": "other"}], "tableView": true, "customConditional": "show = data.current_clindamycin_strength && _.some(_.values(data.current_clindamycin_strength));"}, {"key": "current_clindamycin_frequency", "data": {"values": [{"label": "Once daily", "value": "once_daily"}, {"label": "Twice daily", "value": "twice_daily"}, {"label": "A few times a week", "value": "few_times_week"}, {"label": "Occasionally", "value": "occasionally"}]}, "type": "select", "input": true, "label": "How often do you apply Clindamycin?", "widget": "html5", "tableView": true, "customConditional": "show = data.current_clindamycin_application_area && _.some(_.values(data.current_clindamycin_application_area));"}, {"key": "current_clindamycin_tolerability", "type": "radio", "input": true, "label": "How well do you tolerate Clindamycin?", "values": [{"label": "Very well (no irritation)", "value": "very_well"}, {"label": "Moderately well (mild irritation)", "value": "moderately_well"}, {"label": "Poorly (moderate irritation)", "value": "poorly"}, {"label": "Very poorly (severe irritation)", "value": "very_poorly"}], "tableView": true, "customConditional": "show = data.current_clindamycin_frequency && data.current_clindamycin_frequency !== 'none';"}, {"key": "current_heading_erythromycin", "html": "<h4><strong>Erythromycin</strong></h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.current_topical_antibiotics && data.current_topical_antibiotics.erythromycin;"}, {"key": "current_erythromycin_strength", "type": "radio", "input": true, "label": "What strength of Erythromycin are you using?", "values": [{"label": "1%", "value": "1_percent"}, {"label": "2%", "value": "2_percent"}, {"label": "Other", "value": "other"}], "tableView": true, "customConditional": "show = data.current_topical_antibiotics && data.current_topical_antibiotics.erythromycin;"}, {"key": "current_erythromycin_application_area", "type": "selectboxes", "input": true, "label": "Where do you apply Erythromycin?", "values": [{"label": "Face", "value": "face"}, {"label": "Chest", "value": "chest"}, {"label": "Back", "value": "back"}, {"label": "Other", "value": "other"}], "tableView": true, "customConditional": "show = data.current_erythromycin_strength && _.some(_.values(data.current_erythromycin_strength));"}, {"key": "current_erythromycin_frequency", "data": {"values": [{"label": "Once daily", "value": "once_daily"}, {"label": "Twice daily", "value": "twice_daily"}, {"label": "A few times a week", "value": "few_times_week"}, {"label": "Occasionally", "value": "occasionally"}]}, "type": "select", "input": true, "label": "How often do you apply Erythromycin?", "widget": "html5", "tableView": true, "customConditional": "show = data.current_erythromycin_application_area && _.some(_.values(data.current_erythromycin_application_area));"}, {"key": "current_erythromycin_tolerability", "type": "radio", "input": true, "label": "How well do you tolerate Erythromycin?", "values": [{"label": "Very well (no irritation)", "value": "very_well"}, {"label": "Moderately well (mild irritation)", "value": "moderately_well"}, {"label": "Poorly (moderate irritation)", "value": "poorly"}, {"label": "Very poorly (severe irritation)", "value": "very_poorly"}], "tableView": true, "customConditional": "show = data.current_erythromycin_frequency && data.current_erythromycin_frequency !== 'none';"}, {"key": "current_heading_metronida<PERSON>le", "html": "<h4><strong>Metronidazole</strong></h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.current_topical_antibiotics && data.current_topical_antibiotics.metronidazole;"}, {"key": "current_metronidazole_strength", "type": "radio", "input": true, "label": "What strength of Metronidazole are you using?", "values": [{"label": "0.75%", "value": "0_75_percent"}, {"label": "1%", "value": "1_percent"}, {"label": "Other", "value": "other"}], "tableView": true, "customConditional": "show = data.current_topical_antibiotics && data.current_topical_antibiotics.metronidazole;"}, {"key": "current_metronidazole_application_area", "type": "selectboxes", "input": true, "label": "Where do you apply Metronidazole?", "values": [{"label": "Face", "value": "face"}, {"label": "Chest", "value": "chest"}, {"label": "Back", "value": "back"}, {"label": "Other", "value": "other"}], "tableView": true, "customConditional": "show = data.current_metronidazole_strength && _.some(_.values(data.current_metronidazole_strength));"}, {"key": "current_metronidazole_frequency", "data": {"values": [{"label": "Once daily", "value": "once_daily"}, {"label": "Twice daily", "value": "twice_daily"}, {"label": "A few times a week", "value": "few_times_week"}, {"label": "Occasionally", "value": "occasionally"}]}, "type": "select", "input": true, "label": "How often do you apply Metronidazole?", "widget": "html5", "tableView": true, "customConditional": "show = data.current_metronidazole_application_area && _.some(_.values(data.current_metronidazole_application_area));"}, {"key": "current_metronidazole_tolerability", "type": "radio", "input": true, "label": "How well do you tolerate Metronidazole?", "values": [{"label": "Very well (no irritation)", "value": "very_well"}, {"label": "Moderately well (mild irritation)", "value": "moderately_well"}, {"label": "Poorly (moderate irritation)", "value": "poorly"}, {"label": "Very poorly (severe irritation)", "value": "very_poorly"}], "tableView": true, "customConditional": "show = data.current_metronidazole_frequency && data.current_metronidazole_frequency !== 'none';"}, {"key": "current_heading_oral_antibiotics_details", "html": "<h3><strong>Oral Antibiotics Details</strong></h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.medication_classes && data.medication_classes.oral_antibiotics;"}, {"key": "current_oral_antibiotic_types", "type": "selectboxes", "input": true, "label": "Which oral antibiotics are you currently on? (Select all that apply)", "values": [{"label": "Doxycycline", "value": "doxycycline"}, {"label": "Minocycline", "value": "minocycline"}, {"label": "Tetracycline", "value": "tetracycline"}, {"label": "Other", "value": "other"}, {"label": "None of the above", "value": "none_of_the_above"}], "tableView": true, "customConditional": "show = data.medication_classes && data.medication_classes.oral_antibiotics;", "optionsLabelPosition": "right"}, {"key": "current_oral_antibiotics_other_details", "type": "textfield", "input": true, "label": "If other, please specify the name(s):", "tableView": true, "customConditional": "show = data.current_oral_antibiotic_types && data.current_oral_antibiotic_types.other;"}, {"key": "current_heading_doxycycline", "html": "<h4><strong>Doxycycline</strong></h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.current_oral_antibiotic_types && data.current_oral_antibiotic_types.doxycycline;"}, {"key": "current_doxycycline_duration", "data": {"values": [{"label": "Less than 1 month", "value": "less_than_1_month"}, {"label": "1-3 months", "value": "1_3_months"}, {"label": "3-6 months", "value": "3_6_months"}, {"label": "6-12 months", "value": "6_12_months"}, {"label": "1-2 years", "value": "1_2_years"}, {"label": "More than 2 years", "value": "more_than_2_years"}]}, "type": "select", "input": true, "label": "How long have you been on Doxycycline?", "widget": "html5", "tableView": true, "customConditional": "show = data.current_oral_antibiotic_types && data.current_oral_antibiotic_types.doxycycline;"}, {"key": "current_doxycycline_dosage", "data": {"values": [{"label": "40 mg once daily", "value": "40mg_once_daily"}, {"label": "100 mg once daily", "value": "100mg_once_daily"}, {"label": "100 mg twice daily", "value": "100mg_twice_daily"}, {"label": "Other", "value": "other"}]}, "type": "select", "input": true, "label": "What dosage of Doxycycline were you prescribed?", "widget": "html5", "tableView": true, "customConditional": "show = data.current_oral_antibiotic_types && data.current_oral_antibiotic_types.doxycycline;"}, {"key": "current_doxycycline_dosage_other", "type": "textfield", "input": true, "label": "Please specify your dosage:", "tableView": true, "customConditional": "show = data.current_doxycycline_dosage && data.current_doxycycline_dosage === 'other';"}, {"key": "current_doxycycline_tolerability", "type": "radio", "input": true, "label": "How well have you tolerated Doxycycline?", "values": [{"label": "Very well (no side effects)", "value": "very_well"}, {"label": "Moderately well (mild side effects)", "value": "moderately_well"}, {"label": "Poorly (moderate side effects)", "value": "poorly"}, {"label": "Very poorly (severe side effects)", "value": "very_poorly"}], "tableView": true, "customConditional": "show = data.current_oral_antibiotic_types && data.current_oral_antibiotic_types.doxycycline;"}, {"key": "current_doxycycline_improvement", "type": "radio", "input": true, "label": "Did you notice an improvement in your skin while taking Doxycycline?", "values": [{"label": "Significant improvement", "value": "significant_improvement"}, {"label": "Moderate improvement", "value": "moderate_improvement"}, {"label": "Slight improvement", "value": "slight_improvement"}, {"label": "No improvement", "value": "no_improvement"}, {"label": "Worsening", "value": "worsening"}, {"label": "Unsure", "value": "unsure"}], "tableView": true, "customConditional": "show = data.current_oral_antibiotic_types && data.current_oral_antibiotic_types.doxycycline;"}, {"key": "current_heading_minocycline", "html": "<h4><strong>Minocycline</strong></h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.current_oral_antibiotic_types && data.current_oral_antibiotic_types.minocycline;"}, {"key": "current_minocycline_duration", "data": {"values": [{"label": "Less than 1 month", "value": "less_than_1_month"}, {"label": "1-3 months", "value": "1_3_months"}, {"label": "3-6 months", "value": "3_6_months"}, {"label": "6-12 months", "value": "6_12_months"}, {"label": "1-2 years", "value": "1_2_years"}, {"label": "More than 2 years", "value": "more_than_2_years"}]}, "type": "select", "input": true, "label": "How long have you been on Minocycline?", "widget": "html5", "tableView": true, "customConditional": "show = data.current_oral_antibiotic_types && data.current_oral_antibiotic_types.minocycline;"}, {"key": "current_minocycline_dosage", "data": {"values": [{"label": "50 mg once daily", "value": "50mg_once_daily"}, {"label": "50 mg twice daily", "value": "50mg_twice_daily"}, {"label": "100 mg once daily", "value": "100mg_once_daily"}, {"label": "100 mg twice daily", "value": "100mg_twice_daily"}, {"label": "Other", "value": "other"}]}, "type": "select", "input": true, "label": "What dosage of Minocycline were you prescribed?", "widget": "html5", "tableView": true, "customConditional": "show = data.current_oral_antibiotic_types && data.current_oral_antibiotic_types.minocycline;"}, {"key": "current_minocycline_dosage_other", "type": "textfield", "input": true, "label": "Please specify your dosage:", "tableView": true, "customConditional": "show = data.current_minocycline_dosage && data.current_minocycline_dosage === 'other';"}, {"key": "current_minocycline_tolerability", "type": "radio", "input": true, "label": "How well did you tolerate Minocycline?", "values": [{"label": "Very well (no side effects)", "value": "very_well"}, {"label": "Moderately well (mild side effects)", "value": "moderately_well"}, {"label": "Poorly (moderate side effects)", "value": "poorly"}, {"label": "Very poorly (severe side effects)", "value": "very_poorly"}], "tableView": true, "customConditional": "show = data.current_oral_antibiotic_types && data.current_oral_antibiotic_types.minocycline;"}, {"key": "current_minocycline_improvement", "type": "radio", "input": true, "label": "Did you notice an improvement in your skin while taking Minocycline?", "values": [{"label": "Significant improvement", "value": "significant_improvement"}, {"label": "Moderate improvement", "value": "moderate_improvement"}, {"label": "Slight improvement", "value": "slight_improvement"}, {"label": "No improvement", "value": "no_improvement"}, {"label": "Worsening", "value": "worsening"}, {"label": "Unsure", "value": "unsure"}], "tableView": true, "customConditional": "show = data.current_oral_antibiotic_types && data.current_oral_antibiotic_types.minocycline;"}, {"key": "current_heading_tetracycline", "html": "<h4><strong>Tetracycline</strong></h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.current_oral_antibiotic_types && data.current_oral_antibiotic_types.tetracycline;"}, {"key": "current_tetracycline_dosage", "data": {"values": [{"label": "250 mg once daily", "value": "250mg_once_daily"}, {"label": "250 mg twice daily", "value": "250mg_twice_daily"}, {"label": "500 mg once daily", "value": "500mg_once_daily"}, {"label": "500 mg twice daily", "value": "500mg_twice_daily"}, {"label": "Other", "value": "other"}]}, "type": "select", "input": true, "label": "What dosage of Tetracycline were you prescribed?", "widget": "html5", "tableView": true, "customConditional": "show = data.current_oral_antibiotic_types && data.current_oral_antibiotic_types.tetracycline;"}, {"key": "current_tetracycline_dosage_other", "type": "textfield", "input": true, "label": "Please specify your dosage:", "tableView": true, "customConditional": "show = data.current_tetracycline_dosage && data.current_tetracycline_dosage === 'other';"}, {"key": "current_tetracycline_duration", "data": {"values": [{"label": "Less than 1 month", "value": "less_than_1_month"}, {"label": "1-3 months", "value": "1_3_months"}, {"label": "3-6 months", "value": "3_6_months"}, {"label": "6-12 months", "value": "6_12_months"}, {"label": "1-2 years", "value": "1_2_years"}, {"label": "More than 2 years", "value": "more_than_2_years"}]}, "type": "select", "input": true, "label": "How long did you take Tetracycline?", "widget": "html5", "tableView": true, "customConditional": "show = data.current_oral_antibiotic_types && data.current_oral_antibiotic_types.tetracycline;"}, {"key": "current_heading_current_skin_lightening_agents", "html": "<h3><strong>Skin Lightening Agents</strong></h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.medication_classes && data.medication_classes.current_skin_lightening_agents;"}, {"key": "current_skin_lightening_agents", "type": "selectboxes", "input": true, "label": "Which skin lightening agents are you currently using? (Select all that apply)", "values": [{"label": "Hydroquinone", "value": "hydroquinone"}, {"label": "Triple Cream", "value": "triple_cream"}, {"label": "Azelaic Acid", "value": "azelaic_acid"}, {"label": "Kojic Acid", "value": "kojic_acid"}, {"label": "Niacinamide", "value": "niacinamide"}, {"label": "Vitamin C", "value": "vitamin_c"}, {"label": "Other", "value": "other"}, {"label": "None of the above", "value": "none_of_the_above"}], "tableView": true, "customConditional": "show = data.medication_classes && data.medication_classes.current_skin_lightening_agents;", "optionsLabelPosition": "right"}, {"key": "current_heading_hydroquinone", "html": "<h4>Hydroquinone</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.current_skin_lightening_agents && data.current_skin_lightening_agents.hydroquinone;"}, {"key": "current_hydroquinone_strength", "type": "radio", "input": true, "label": "What strength of Hydroquinone are you using?", "values": [{"label": "2%", "value": "2_percent"}, {"label": "4%", "value": "4_percent"}, {"label": "Other", "value": "other"}], "tableView": true, "customConditional": "show = data.current_skin_lightening_agents && data.current_skin_lightening_agents.hydroquinone;"}, {"key": "current_hydroquinone_frequency", "data": {"values": [{"label": "Once daily", "value": "once_daily"}, {"label": "Twice daily", "value": "twice_daily"}, {"label": "A few times a week", "value": "few_times_week"}, {"label": "Other", "value": "other"}]}, "type": "select", "input": true, "label": "How often do you apply Hydroquinone?", "widget": "html5", "tableView": true, "customConditional": "show = data.current_hydroquinone_strength;"}, {"key": "current_hydroquinone_duration", "data": {"values": [{"label": "Less than 1 month", "value": "less_than_1_month"}, {"label": "1-3 months", "value": "1_3_months"}, {"label": "3-6 months", "value": "3_6_months"}, {"label": "6-12 months", "value": "6_12_months"}, {"label": "More than a year", "value": "more_than_a_year"}]}, "type": "select", "input": true, "label": "How long have you been using Hydroquinone?", "widget": "html5", "tableView": true, "customConditional": "show = data.current_hydroquinone_frequency;"}, {"key": "current_hydroquinone_application_area", "type": "textfield", "input": true, "label": "Which areas of your skin are you applying Hydroquinone?", "tableView": true, "placeholder": "E.g., face, neck, hands", "customConditional": "show = data.current_hydroquinone_duration;"}, {"key": "current_hydroquinone_tolerability", "type": "radio", "input": true, "label": "How well are you tolerating Hydroquinone?", "values": [{"label": "Very well (no side effects)", "value": "very_well"}, {"label": "Mild side effects", "value": "mild_side_effects"}, {"label": "Moderate side effects", "value": "moderate_side_effects"}, {"label": "Severe side effects", "value": "severe_side_effects"}], "tableView": true, "customConditional": "show = data.current_hydroquinone_application_area;"}, {"key": "current_hydroquinone_improvement", "type": "radio", "input": true, "label": "Have you noticed an improvement in your skin since using Hydroquinone?", "values": [{"label": "Significant improvement", "value": "significant_improvement"}, {"label": "Moderate improvement", "value": "moderate_improvement"}, {"label": "Slight improvement", "value": "slight_improvement"}, {"label": "No improvement", "value": "no_improvement"}, {"label": "Condition worsened", "value": "condition_worsened"}], "tableView": true, "customConditional": "show = data.current_hydroquinone_tolerability;"}, {"key": "current_heading_triple_cream", "html": "<h4>Triple Cream</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.current_skin_lightening_agents && data.current_skin_lightening_agents.triple_cream;"}, {"key": "current_triple_cream_strength", "type": "radio", "input": true, "label": "Which Triple Cream formulation are you using?", "values": [{"label": "Kligman Formula (2% Hydroquinone, 0.025% Tretinoin, 0.01% Dexamethasone)", "value": "kligman_formula"}, {"label": "Tri-Luma (4% Hydroquinone, 0.05% Tretinoin, 0.01% Fluocinolone)", "value": "tri_luma"}, {"label": "Custom formulation", "value": "custom_strength"}], "tableView": true, "customConditional": "show = data.current_skin_lightening_agents && data.current_skin_lightening_agents.triple_cream;"}, {"key": "current_triple_cream_custom_strength_details", "type": "textfield", "input": true, "label": "Please specify the custom formulation details:", "tableView": true, "customConditional": "show = data.current_skin_lightening_agents && data.current_skin_lightening_agents.triple_cream && data.current_triple_cream_strength === 'custom_strength';"}, {"key": "current_triple_cream_frequency", "type": "radio", "input": true, "label": "How often do you apply Triple Cream?", "values": [{"label": "Once daily", "value": "once_daily"}, {"label": "Twice daily", "value": "twice_daily"}, {"label": "A few times a week", "value": "few_times_week"}, {"label": "Other", "value": "other"}], "tableView": true, "customConditional": "show = data.current_skin_lightening_agents && data.current_skin_lightening_agents.triple_cream && data.current_triple_cream_strength;"}, {"key": "current_triple_cream_frequency_other_details", "type": "textfield", "input": true, "label": "Please specify the frequency details:", "tableView": true, "customConditional": "show = data.current_skin_lightening_agents && data.current_skin_lightening_agents.triple_cream && data.current_triple_cream_frequency === 'other';"}, {"key": "current_triple_cream_total_use_duration", "data": {"values": [{"label": "Less than 4 weeks", "value": "less_than_4_weeks"}, {"label": "4-8 weeks", "value": "4_8_weeks"}, {"label": "8-12 weeks", "value": "8_12_weeks"}, {"label": "12-16 weeks", "value": "12_16_weeks"}, {"label": "16-20 weeks", "value": "16_20_weeks"}, {"label": "20-24 weeks", "value": "20_24_weeks"}, {"label": "6-9 months", "value": "6_9_months"}, {"label": "9-12 months", "value": "9_12_months"}, {"label": "1-1.5 years", "value": "1_1.5_years"}, {"label": "1.5-2 years", "value": "1.5_2_years"}, {"label": "More than 2 years", "value": "more_than_2_years"}]}, "type": "select", "input": true, "label": "How long have you used Triple Cream in total over your lifetime?<br><br>• <strong>For example,</strong> if you used the cream for 3 months two years ago, and another 3 months this year, this would add up to a total of 6 months of use.<br>• Please select the option that best represents this total time.</br>", "widget": "html5", "tableView": true, "customConditional": "show = data.current_skin_lightening_agents && data.current_skin_lightening_agents.triple_cream && data.current_triple_cream_strength && data.current_triple_cream_frequency;"}, {"key": "current_triple_cream_improvement", "type": "radio", "input": true, "label": "Have you noticed an improvement in your skin since using Triple Cream?", "values": [{"label": "Significant improvement", "value": "significant_improvement"}, {"label": "Moderate improvement", "value": "moderate_improvement"}, {"label": "Slight improvement", "value": "slight_improvement"}, {"label": "No change", "value": "no_change"}, {"label": "Worsened condition", "value": "worsened_condition"}, {"label": "Not applicable", "value": "not_applicable"}], "tableView": true, "customConditional": "show = data.current_skin_lightening_agents && data.current_skin_lightening_agents.triple_cream && data.current_triple_cream_strength && data.current_triple_cream_frequency && data.current_triple_cream_total_use_duration;"}, {"key": "current_heading_azelaic_acid", "html": "<h4>Azelaic Acid</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.current_skin_lightening_agents && data.current_skin_lightening_agents.azelaic_acid;"}, {"key": "current_azelaic_acid_strength", "type": "radio", "input": true, "label": "What strength of Azelaic Acid are you using?", "values": [{"label": "10%", "value": "10_percent"}, {"label": "15%", "value": "15_percent"}, {"label": "20%", "value": "20_percent"}, {"label": "Other", "value": "other"}], "tableView": true, "customConditional": "show = data.current_skin_lightening_agents && data.current_skin_lightening_agents.azelaic_acid;"}, {"key": "azelaic_acid_custom_strength", "type": "textfield", "input": true, "label": "Please specify the custom strength of Azelaic Acid", "tableView": true, "customConditional": "show = data.current_azelaic_acid_strength === 'other';"}, {"key": "current_azelaic_acid_frequency", "type": "radio", "input": true, "label": "How often do you apply Azelaic Acid?", "values": [{"label": "Once daily", "value": "once_daily"}, {"label": "Twice daily", "value": "twice_daily"}, {"label": "A few times a week", "value": "few_times_week"}, {"label": "Other", "value": "other"}], "tableView": true, "customConditional": "show = data.current_azelaic_acid_strength && data.current_azelaic_acid_strength !== 'other';"}, {"key": "current_azelaic_acid_custom_frequency", "type": "textfield", "input": true, "label": "Please specify your custom application frequency", "tableView": true, "customConditional": "show = data.current_azelaic_acid_frequency === 'other';"}, {"key": "current_azelaic_acid_duration", "data": {"values": [{"label": "Less than 4 weeks", "value": "less_than_4_weeks"}, {"label": "4-8 weeks", "value": "4_8_weeks"}, {"label": "8-12 weeks", "value": "8_12_weeks"}, {"label": "12-16 weeks", "value": "12_16_weeks"}, {"label": "16-20 weeks", "value": "16_20_weeks"}, {"label": "20-24 weeks", "value": "20_24_weeks"}, {"label": "6-9 months", "value": "6_9_months"}, {"label": "9-12 months", "value": "9_12_months"}, {"label": "1-1.5 years", "value": "1_1.5_years"}, {"label": "1.5-2 years", "value": "1.5_2_years"}, {"label": "More than 2 years", "value": "more_than_2_years"}]}, "type": "select", "input": true, "label": "How long have you used Azelaic Acid in total over your lifetime? <br>• Example: If you used it for 3 months two years ago, and another 3 months this year, this would total 6 months of use. <br>• Select the option that best represents this total time.", "widget": "html5", "tableView": true, "customConditional": "show = data.current_azelaic_acid_frequency && data.current_azelaic_acid_frequency !== 'other';"}, {"key": "current_azelaic_acid_improvement", "type": "radio", "input": true, "label": "Have you noticed an improvement in your skin since using Azelaic Acid?", "values": [{"label": "Significant improvement", "value": "significant_improvement"}, {"label": "Moderate improvement", "value": "moderate_improvement"}, {"label": "Slight improvement", "value": "slight_improvement"}, {"label": "No change", "value": "no_change"}, {"label": "Worsened condition", "value": "worsened_condition"}, {"label": "Not applicable", "value": "not_applicable"}], "tableView": true, "customConditional": "show = data.current_azelaic_acid_duration && data.current_azelaic_acid_duration !== 'never';"}, {"key": "current_heading_kojic_acid", "html": "<h4>Kojic Acid</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.current_skin_lightening_agents && data.current_skin_lightening_agents.kojic_acid;"}, {"key": "current_kojic_acid_frequency", "data": {"values": [{"label": "Once daily", "value": "once_daily"}, {"label": "Twice daily", "value": "twice_daily"}]}, "type": "select", "input": true, "label": "How often do you apply Kojic Acid?", "widget": "html5", "tableView": true, "customConditional": "show = data.current_skin_lightening_agents && data.current_skin_lightening_agents.kojic_acid;"}, {"key": "current_heading_niacinamide", "html": "<h4>Niacinamide</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.current_skin_lightening_agents && data.current_skin_lightening_agents.niacinamide;"}, {"key": "current_niacinamide_frequency", "data": {"values": [{"label": "Once daily", "value": "once_daily"}, {"label": "Twice daily", "value": "twice_daily"}]}, "type": "select", "input": true, "label": "How often do you apply Niacinamide?", "widget": "html5", "tableView": true, "customConditional": "show = data.current_skin_lightening_agents && data.current_skin_lightening_agents.niacinamide;"}, {"key": "current_heading_vitamin_c", "html": "<h4>Vitamin C</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.current_skin_lightening_agents && data.current_skin_lightening_agents.vitamin_c;"}, {"key": "current_vitamin_c_frequency", "data": {"values": [{"label": "Once daily", "value": "once_daily"}, {"label": "Twice daily", "value": "twice_daily"}]}, "type": "select", "input": true, "label": "How often do you apply Vitamin C?", "widget": "html5", "tableView": true, "customConditional": "show = data.current_skin_lightening_agents && data.current_skin_lightening_agents.vitamin_c;"}, {"key": "current_heading_procedures", "html": "<h2><strong>Procedures</strong></h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.current_doctor_prescribed_treatments === 'yes' || data.current_doctor_prescribed_treatments === 'no';"}, {"key": "current_recent_procedures", "type": "radio", "input": true, "label": "Are you currently or have you recently completed cosmetic procedures (including chemical peels, botox, fillers, microneedling, lasers)?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.current_doctor_prescribed_treatments === 'yes' || data.current_doctor_prescribed_treatments === 'no';", "optionsLabelPosition": "right"}, {"key": "current_details_recent_procedures", "type": "selectboxes", "input": true, "label": "Please specify the cosmetic procedures you are currently or have recently completed (Select all that apply):", "values": [{"label": "Chemical Peels", "value": "current_chemical_peels"}, {"label": "Botox", "value": "botox"}, {"label": "Fillers", "value": "fillers"}, {"label": "Microneedling", "value": "microneedling"}, {"label": "Lasers/IPL", "value": "lasers"}], "tableView": true, "customConditional": "show = data.current_recent_procedures === 'yes';", "optionsLabelPosition": "right"}, {"key": "current_heading_current_chemical_peels", "html": "<h3><strong>Chemical Peels</strong></h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.current_details_recent_procedures && data.current_details_recent_procedures.current_chemical_peels;"}, {"key": "current_peel_type", "type": "selectboxes", "input": true, "label": "Which type of peels do you use?", "values": [{"label": "At-home peels", "value": "at_home"}, {"label": "Professionally applied peels", "value": "professional"}, {"label": "None of the above", "value": "none"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.current_details_recent_procedures && data.current_details_recent_procedures.current_chemical_peels;"}, {"key": "current_heading_at_home_peels", "html": "<h4><strong>At-Home Peels</strong></h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.current_details_recent_procedures && data.current_details_recent_procedures.current_chemical_peels && data.current_peel_type && data.current_peel_type.at_home && !data.current_peel_type.none;"}, {"key": "current_peel_frequency_at_home", "data": {"values": [{"label": "Nightly", "value": "nightly"}, {"label": "Every few days", "value": "few_days"}, {"label": "Once a week", "value": "weekly"}, {"label": "Every 2-3 weeks", "value": "bi_to_triweekly"}, {"label": "Once a month", "value": "monthly"}, {"label": "Occasionally", "value": "occasionally"}, {"label": "Never", "value": "never"}]}, "type": "select", "input": true, "label": "How often do you use <strong>at-home</strong> chemical peels?", "widget": "html5", "tableView": true, "customConditional": "show = data.current_details_recent_procedures && data.current_details_recent_procedures.current_chemical_peels && data.current_peel_type.at_home && !data.current_peel_type.none;"}, {"key": "current_at_home_peel_duration", "data": {"values": [{"label": "Less than 1 month", "value": "less_than_1_month"}, {"label": "1-3 months", "value": "1_to_3_months"}, {"label": "3-6 months", "value": "3_to_6_months"}, {"label": "6-12 months", "value": "6_to_12_months"}, {"label": "1-2 years", "value": "1_to_2_years"}, {"label": "More than 2 years", "value": "more_than_2_years"}]}, "type": "select", "input": true, "label": "How long have you incorporated at-home chemical peels into your routine?", "widget": "html5", "tableView": true, "customConditional": "show = data.current_details_recent_procedures && data.current_details_recent_procedures.current_chemical_peels && data.current_peel_type && data.current_peel_type.at_home && data.current_peel_frequency_at_home !== 'never';"}, {"key": "current_at_home_current_peel_types", "type": "selectboxes", "input": true, "label": "What types of at-home peels do you use? (Select all that apply)", "values": [{"label": "Glycolic Acid", "value": "glycolic_acid"}, {"label": "Salicylic Acid", "value": "salicylic_acid"}, {"label": "Lactic Acid", "value": "lactic_acid"}, {"label": "Man<PERSON>ic <PERSON>", "value": "mandelic_acid"}, {"label": "TCA (Trichloroacetic Acid)", "value": "tca"}, {"label": "Enzyme Peels", "value": "enzyme_peels"}, {"label": "Retinol Peels", "value": "retinol_peels"}, {"label": "None of the above", "value": "none_of_the_above"}], "tableView": true, "customConditional": "show = data.current_details_recent_procedures && data.current_details_recent_procedures.current_chemical_peels && data.current_peel_type.at_home && !data.current_peel_type.none && data.current_peel_frequency_at_home !== 'never';"}, {"key": "current_strength_glycolic_acid", "type": "radio", "input": true, "label": "What strength of Glycolic Acid do you use?", "values": [{"label": "Less than 10%", "value": "less_than_10"}, {"label": "10-20%", "value": "10_to_20"}, {"label": "20-30%", "value": "20_to_30"}, {"label": "Above 30%", "value": "above_30"}, {"label": "Not sure", "value": "not_sure"}], "tableView": true, "customConditional": "show = data.current_at_home_current_peel_types && data.current_at_home_current_peel_types.glycolic_acid;"}, {"key": "current_strength_salicylic_acid", "type": "radio", "input": true, "label": "What strength of Salicylic Acid do you use?", "values": [{"label": "Less than 1%", "value": "less_than_1"}, {"label": "1-2%", "value": "1_to_2"}, {"label": "Above 2%", "value": "above_2"}], "tableView": true, "customConditional": "show = data.current_at_home_current_peel_types && data.current_at_home_current_peel_types.salicylic_acid;"}, {"key": "current_strength_lactic_acid", "type": "radio", "input": true, "label": "What strength of Lactic Acid do you use?", "values": [{"label": "Less than 5%", "value": "less_than_5"}, {"label": "5-10%", "value": "5_to_10"}, {"label": "10-20%", "value": "10_to_20"}, {"label": "Above 20%", "value": "above_20"}, {"label": "Not sure", "value": "not_sure"}], "tableView": true, "customConditional": "show = data.current_at_home_current_peel_types && data.current_at_home_current_peel_types.lactic_acid;"}, {"key": "current_strength_mandelic_acid", "type": "radio", "input": true, "label": "What strength of Mandelic Acid do you use?", "values": [{"label": "Less than 5%", "value": "less_than_5"}, {"label": "5-10%", "value": "5_to_10"}, {"label": "10-20%", "value": "10_to_20"}, {"label": "Above 20%", "value": "above_20"}, {"label": "Not sure", "value": "not_sure"}], "tableView": true, "customConditional": "show = data.current_at_home_current_peel_types && data.current_at_home_current_peel_types.mandelic_acid;"}, {"key": "current_strength_tca", "type": "radio", "input": true, "label": "What strength of TCA (Trichloroacetic Acid) do you use?", "values": [{"label": "Less than 10%", "value": "less_than_10"}, {"label": "10-20%", "value": "10_to_20"}, {"label": "20-30%", "value": "20_to_30"}, {"label": "Above 30%", "value": "above_30"}, {"label": "Not sure", "value": "not_sure"}], "tableView": true, "customConditional": "show = data.current_at_home_current_peel_types && data.current_at_home_current_peel_types.tca;"}, {"key": "current_strength_enzyme_peels", "type": "radio", "input": true, "label": "What type of Enzyme Peels do you use?", "values": [{"label": "<PERSON><PERSON>", "value": "papaya"}, {"label": "Pumpkin Enzyme", "value": "pumpkin"}, {"label": "Pineapple Enzyme", "value": "pineapple"}, {"label": "Other Enzyme", "value": "other"}], "tableView": true, "customConditional": "show = data.current_at_home_current_peel_types && data.current_at_home_current_peel_types.enzyme_peels;"}, {"key": "current_strength_retinol_peels", "type": "radio", "input": true, "label": "What strength of Retinol Peel do you use?", "values": [{"label": "Less than 0.5%", "value": "less_than_0_5"}, {"label": "0.5-1%", "value": "0_5_to_1"}, {"label": "Above 1%", "value": "above_1"}, {"label": "Not sure", "value": "not_sure"}], "tableView": true, "customConditional": "show = data.current_at_home_current_peel_types && data.current_at_home_current_peel_types.retinol_peels;"}, {"key": "current_improvement_at_home_peels", "type": "radio", "input": true, "label": "Have you noticed an improvement in your skin since incorporating at-home chemical peels into your routine?", "values": [{"label": "Significant improvement", "value": "significant_improvement"}, {"label": "Moderate improvement", "value": "moderate_improvement"}, {"label": "Slight improvement", "value": "slight_improvement"}, {"label": "No change", "value": "no_change"}, {"label": "It's worse than before", "value": "worsening"}, {"label": "Not sure", "value": "not_sure"}], "tableView": true, "customConditional": "show = data.current_details_recent_procedures && data.current_details_recent_procedures.current_chemical_peels && data.current_peel_type && data.current_peel_type.at_home && data.current_peel_frequency_at_home !== 'never';"}, {"key": "current_heading_professional_peels", "html": "<h4><strong>Professional Peels</strong></h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.current_details_recent_procedures && data.current_details_recent_procedures.current_chemical_peels && data.current_peel_type && data.current_peel_type.professional && !data.current_peel_type.none;"}, {"key": "current_peel_frequency_professional", "data": {"values": [{"label": "Once a week", "value": "weekly"}, {"label": "Every 2-3 weeks", "value": "bi_to_triweekly"}, {"label": "Once a month", "value": "monthly"}, {"label": "Every 1-2 months", "value": "one_to_two_months"}, {"label": "Every 3-4 months", "value": "three_to_four_months"}, {"label": "Every 5-6 months", "value": "five_to_six_months"}, {"label": "Every 6-12 months", "value": "six_to_twelve_months"}, {"label": "Once a year", "value": "yearly"}, {"label": "Occasionally (less than once a year)", "value": "occasionally"}, {"label": "Never", "value": "never"}]}, "type": "select", "input": true, "label": "How often do you receive <strong>professionally</strong> applied chemical peels?", "widget": "html5", "tableView": true, "customConditional": "show = data.current_details_recent_procedures && data.current_details_recent_procedures.current_chemical_peels && data.current_peel_type.professional && !data.current_peel_type.none;"}, {"key": "current_professional_peel_duration", "data": {"values": [{"label": "Less than 1 month", "value": "less_than_1_month"}, {"label": "1-3 months", "value": "1_to_3_months"}, {"label": "3-6 months", "value": "3_to_6_months"}, {"label": "6-12 months", "value": "6_to_12_months"}, {"label": "1-2 years", "value": "1_to_2_years"}, {"label": "More than 2 years", "value": "more_than_2_years"}]}, "type": "select", "input": true, "label": "How long have you incorporated professionally applied chemical peels into your routine?", "widget": "html5", "tableView": true, "customConditional": "show = data.current_details_recent_procedures && data.current_details_recent_procedures.current_chemical_peels && data.current_peel_type && data.current_peel_type.professional && data.current_peel_frequency_professional !== 'never';"}, {"key": "current_professional_current_peel_types", "type": "selectboxes", "input": true, "label": "What types of professionally applied peels do you receive? (Select all that apply)", "values": [{"label": "Glycolic Acid Peel", "value": "glycolic_acid_peel"}, {"label": "Salicylic Acid Peel", "value": "salicylic_acid_peel"}, {"label": "<PERSON>tic <PERSON>", "value": "lactic_acid_peel"}, {"label": "TCA (Trichloroacetic Acid) Peel", "value": "tca_peel"}, {"label": "<PERSON><PERSON>'s Peel", "value": "jess<PERSON>_peel"}, {"label": "None of the above", "value": "none_of_the_above"}], "tableView": true, "customConditional": "show = data.current_details_recent_procedures && data.current_details_recent_procedures.current_chemical_peels && data.current_peel_type.professional && !data.current_peel_type.none && data.current_peel_frequency_professional !== 'never';"}, {"key": "current_strength_glycolic_acid_peel", "type": "radio", "input": true, "label": "What strength of Glycolic Acid Peel do you receive?", "values": [{"label": "Less than 20%", "value": "less_than_20"}, {"label": "20-30%", "value": "20_to_30"}, {"label": "30-50%", "value": "30_to_50"}, {"label": "Above 50%", "value": "above_50"}, {"label": "Not sure", "value": "not_sure"}], "tableView": true, "customConditional": "show = data.current_professional_current_peel_types && data.current_professional_current_peel_types.glycolic_acid_peel;"}, {"key": "current_strength_salicylic_acid_peel", "type": "radio", "input": true, "label": "What strength of Salicylic Acid Peel do you receive?", "values": [{"label": "Less than 10%", "value": "less_than_10"}, {"label": "10-20%", "value": "10_to_20"}, {"label": "Above 20%", "value": "above_20"}, {"label": "Not sure", "value": "not_sure"}], "tableView": true, "customConditional": "show = data.current_professional_current_peel_types && data.current_professional_current_peel_types.salicylic_acid_peel;"}, {"key": "current_strength_lactic_acid_peel", "type": "radio", "input": true, "label": "What strength of Lactic Acid Peel do you receive?", "values": [{"label": "Less than 20%", "value": "less_than_20"}, {"label": "20-40%", "value": "20_to_40"}, {"label": "Above 40%", "value": "above_40"}, {"label": "Not sure", "value": "not_sure"}], "tableView": true, "customConditional": "show = data.current_professional_current_peel_types && data.current_professional_current_peel_types.lactic_acid_peel;"}, {"key": "current_strength_tca_peel", "type": "radio", "input": true, "label": "What strength of TCA (Trichloroacetic Acid) Peel do you receive?", "values": [{"label": "Less than 10%", "value": "less_than_10"}, {"label": "10-20%", "value": "10_to_20"}, {"label": "20-30%", "value": "20_to_30"}, {"label": "Above 30%", "value": "above_30"}, {"label": "Not sure", "value": "not_sure"}], "tableView": true, "customConditional": "show = data.current_professional_current_peel_types && data.current_professional_current_peel_types.tca_peel;"}, {"key": "current_strength_jessners_peel", "type": "radio", "input": true, "label": "What strength of <PERSON><PERSON>'s <PERSON> do you receive?", "values": [{"label": "Standard Strength", "value": "standard_strength"}, {"label": "Modified (with lower concentration acids)", "value": "modified_strength"}, {"label": "Not sure", "value": "not_sure"}], "tableView": true, "customConditional": "show = data.current_professional_current_peel_types && data.current_professional_current_peel_types.jessners_peel;"}, {"key": "current_improvement_professional_peels", "type": "radio", "input": true, "label": "Have you noticed an improvement in your skin since incorporating professionally applied chemical peels into your routine?", "values": [{"label": "Significant improvement", "value": "significant_improvement"}, {"label": "Moderate improvement", "value": "moderate_improvement"}, {"label": "Slight improvement", "value": "slight_improvement"}, {"label": "No change", "value": "no_change"}, {"label": "It's worse than before", "value": "worsening"}, {"label": "Not sure", "value": "not_sure"}], "tableView": true, "customConditional": "show = data.current_details_recent_procedures && data.current_details_recent_procedures.current_chemical_peels && data.current_peel_type && data.current_peel_type.professional && data.current_peel_frequency_professional !== 'never';"}, {"key": "current_heading_botox", "html": "<h3><strong>Botox</strong></h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.current_details_recent_procedures && data.current_details_recent_procedures.botox;"}, {"key": "current_botox_frequency", "data": {"values": [{"label": "Every 3 months", "value": "every_3_months"}, {"label": "Every 4-6 months", "value": "every_4_6_months"}, {"label": "Every 6-12 months", "value": "every_6_12_months"}, {"label": "Once a year", "value": "yearly"}, {"label": "Occasionally (less than once a year)", "value": "occasionally"}, {"label": "Never", "value": "never"}]}, "type": "select", "input": true, "label": "How often do you receive Botox injections?", "widget": "html5", "tableView": true, "customConditional": "show = data.current_details_recent_procedures && data.current_details_recent_procedures.botox;"}, {"key": "current_botox_duration", "data": {"values": [{"label": "Less than 6 months", "value": "less_than_6_months"}, {"label": "6-12 months", "value": "6_12_months"}, {"label": "1-2 years", "value": "1_2_years"}, {"label": "2-5 years", "value": "2_5_years"}, {"label": "More than 5 years", "value": "more_than_5_years"}]}, "type": "select", "input": true, "label": "How long have you been receiving Botox injections?", "widget": "html5", "tableView": true, "customConditional": "show = data.current_botox_frequency"}, {"key": "current_botox_areas_treated", "type": "selectboxes", "input": true, "label": "Which areas do you typically treat with Botox? (Select all that apply)", "values": [{"label": "Forehead", "value": "forehead"}, {"label": "Glabella (between eyebrows)", "value": "glabella"}, {"label": "Crow's feet (around eyes)", "value": "crows_feet"}, {"label": "Jawline", "value": "jawline"}, {"label": "Neck", "value": "neck"}, {"label": "Other areas", "value": "other_areas"}], "tableView": true, "customConditional": "show = data.current_botox_duration"}, {"key": "current_botox_areas_other", "type": "textfield", "input": true, "label": "If other areas, please specify:", "tableView": true, "customConditional": "show = data.current_botox_areas_treated && data.current_botox_areas_treated.other_areas;"}, {"key": "current_botox_side_effects", "type": "selectboxes", "input": true, "label": "Have you experienced any side effects from Botox? (Select all that apply)", "values": [{"label": "Bruising", "value": "bruising"}, {"label": "Headaches", "value": "headaches"}, {"label": "Drooping eyelids", "value": "drooping_eyelids"}, {"label": "Muscle weakness", "value": "muscle_weakness"}, {"label": "None", "value": "none"}], "tableView": true, "customConditional": "show = data.current_botox_areas_treated"}, {"key": "current_botox_improvement", "type": "radio", "input": true, "label": "Have you noticed an improvement in your appearance since starting Botox treatments?", "values": [{"label": "Yes, significant improvement", "value": "significant_improvement"}, {"label": "Yes, some improvement", "value": "some_improvement"}, {"label": "No noticeable improvement", "value": "no_improvement"}, {"label": "Yes, but with some areas I would like improved further", "value": "partial_improvement"}, {"label": "No, I'm dissatisfied", "value": "dissatisfied"}, {"label": "Not applicable", "value": "not_applicable"}], "tableView": true, "customConditional": "show = data.current_botox_side_effects"}, {"key": "current_heading_fillers", "html": "<h3><strong>Fillers</strong></h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.current_details_recent_procedures && data.current_details_recent_procedures.fillers;"}, {"key": "current_filler_frequency", "data": {"values": [{"label": "Every 3 months", "value": "every_3_months"}, {"label": "Every 4-6 months", "value": "every_4_6_months"}, {"label": "Every 6-12 months", "value": "every_6_12_months"}, {"label": "Once a year", "value": "yearly"}, {"label": "Occasionally (less than once a year)", "value": "occasionally"}, {"label": "Never", "value": "never"}]}, "type": "select", "input": true, "label": "How often do you receive filler injections?", "widget": "html5", "tableView": true, "customConditional": "show = data.current_details_recent_procedures && data.current_details_recent_procedures.fillers;"}, {"key": "current_filler_duration", "data": {"values": [{"label": "Less than 6 months", "value": "less_than_6_months"}, {"label": "6-12 months", "value": "6_12_months"}, {"label": "1-2 years", "value": "1_2_years"}, {"label": "2-5 years", "value": "2_5_years"}, {"label": "More than 5 years", "value": "more_than_5_years"}]}, "type": "select", "input": true, "label": "How long have you been receiving filler injections?", "widget": "html5", "tableView": true, "customConditional": "show = data.current_filler_frequency"}, {"key": "current_filler_areas_treated", "type": "selectboxes", "input": true, "label": "Which areas do you typically treat with fillers? (Select all that apply)", "values": [{"label": "Cheeks", "value": "cheeks"}, {"label": "Lips", "value": "lips"}, {"label": "Nasolabial folds", "value": "nasolabial_folds"}, {"label": "Under-eye area", "value": "under_eye"}, {"label": "Jawline", "value": "jawline"}, {"label": "<PERSON>", "value": "chin"}, {"label": "Other areas", "value": "other_areas"}], "tableView": true, "customConditional": "show = data.current_filler_duration"}, {"key": "current_filler_areas_other", "type": "textfield", "input": true, "label": "If other areas, please specify:", "tableView": true, "customConditional": "show = data.current_filler_areas_treated && data.current_filler_areas_treated.other_areas;"}, {"key": "current_filler_types", "type": "selectboxes", "input": true, "label": "Which types of fillers have you used? (Select all that apply)", "values": [{"label": "Hyaluronic Acid (e.g., Juvederm, Restylane)", "value": "hyaluronic_acid"}, {"label": "Calcium Hydroxylapatite (e.g., Radiesse)", "value": "calcium_hydroxylapatite"}, {"label": "Poly-L-lactic Acid (e.g., Sculptra)", "value": "poly_l_lactic_acid"}, {"label": "Polymethylmethacrylate (PMMA)", "value": "pmma"}, {"label": "Fat Grafting", "value": "fat_grafting"}, {"label": "Other", "value": "other"}], "tableView": true, "customConditional": "show = data.current_filler_areas_treated"}, {"key": "current_filler_types_other", "type": "textfield", "input": true, "label": "If other, please specify:", "tableView": true, "customConditional": "show = data.current_filler_types && data.current_filler_types.other;"}, {"key": "current_filler_side_effects", "type": "selectboxes", "input": true, "label": "Have you experienced any side effects from fillers? (Select all that apply)", "values": [{"label": "Bruising", "value": "bruising"}, {"label": "Swelling", "value": "swelling"}, {"label": "Lumps or bumps", "value": "lumps_bumps"}, {"label": "Discoloration", "value": "discoloration"}, {"label": "Pain or tenderness", "value": "pain_tenderness"}, {"label": "None", "value": "none"}], "tableView": true, "customConditional": "show = data.current_filler_types"}, {"key": "current_filler_improvement", "type": "radio", "input": true, "label": "Have you noticed an improvement in your appearance since starting filler treatments?", "values": [{"label": "Yes, significant improvement", "value": "significant_improvement"}, {"label": "Yes, some improvement", "value": "some_improvement"}, {"label": "No noticeable improvement", "value": "no_improvement"}, {"label": "Yes, but with some areas I would like improved further", "value": "partial_improvement"}, {"label": "No, I'm dissatisfied", "value": "dissatisfied"}, {"label": "Not applicable", "value": "not_applicable"}], "tableView": true, "customConditional": "show = data.current_filler_side_effects"}, {"key": "current_heading_microneedling", "html": "<h3><strong>Microneedling</strong></h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.current_details_recent_procedures && data.current_details_recent_procedures.microneedling;"}, {"key": "current_microneedling_frequency", "data": {"values": [{"label": "Once a month", "value": "monthly"}, {"label": "Every 2-3 months", "value": "every_2_3_months"}, {"label": "Every 4-6 months", "value": "every_4_6_months"}, {"label": "Once a year", "value": "yearly"}, {"label": "Occasionally", "value": "occasionally"}, {"label": "Never", "value": "never"}]}, "type": "select", "input": true, "label": "How often do you receive microneedling treatments?", "widget": "html5", "tableView": true, "customConditional": "show = data.current_details_recent_procedures && data.current_details_recent_procedures.microneedling;"}, {"key": "current_microneedling_duration", "data": {"values": [{"label": "Less than 6 months", "value": "less_than_6_months"}, {"label": "6-12 months", "value": "6_12_months"}, {"label": "1-2 years", "value": "1_2_years"}, {"label": "2-5 years", "value": "2_5_years"}, {"label": "More than 5 years", "value": "more_than_5_years"}]}, "type": "select", "input": true, "label": "How long have you been receiving microneedling treatments?", "widget": "html5", "tableView": true, "customConditional": "show = data.current_microneedling_frequency"}, {"key": "current_microneedling_type", "type": "selectboxes", "input": true, "label": "What type of microneedling treatments do you receive? (Select all that apply)", "values": [{"label": "Standard Microneedling", "value": "standard_microneedling"}, {"label": "Microneedling with PRP (Platelet-Rich Plasma)", "value": "microneedling_prp"}, {"label": "Radiofrequency Microneedling", "value": "radiofrequency_microneedling"}, {"label": "Other", "value": "other"}], "tableView": true, "customConditional": "show = data.current_microneedling_duration"}, {"key": "current_microneedling_type_other", "type": "textfield", "input": true, "label": "If other, please specify:", "tableView": true, "customConditional": "show = data.current_microneedling_type && data.current_microneedling_type.other;"}, {"key": "current_microneedling_side_effects", "type": "selectboxes", "input": true, "label": "Have you experienced any side effects from microneedling? (Select all that apply)", "values": [{"label": "Redness", "value": "redness"}, {"label": "Swelling", "value": "swelling"}, {"label": "Bruising", "value": "bruising"}, {"label": "Infection", "value": "infection"}, {"label": "Skin irritation", "value": "skin_irritation"}, {"label": "None", "value": "none"}], "tableView": true, "customConditional": "show = data.current_microneedling_type"}, {"key": "current_microneedling_improvement", "type": "radio", "input": true, "label": "Have you noticed an improvement in your skin since starting microneedling treatments?", "values": [{"label": "Yes, significant improvement", "value": "significant_improvement"}, {"label": "Yes, some improvement", "value": "some_improvement"}, {"label": "No noticeable improvement", "value": "no_improvement"}, {"label": "Yes, but with some areas needing further improvement", "value": "partial_improvement"}, {"label": "No, I'm dissatisfied", "value": "dissatisfied"}, {"label": "Not applicable", "value": "not_applicable"}], "tableView": true, "customConditional": "show = data.current_microneedling_side_effects"}, {"key": "current_heading_laser_treatments", "html": "<h3><strong>Laser/IPL Treatments</strong></h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.current_details_recent_procedures && data.current_details_recent_procedures.lasers;"}, {"key": "current_laser_frequency", "data": {"values": [{"label": "Every month", "value": "monthly"}, {"label": "Every 2-3 months", "value": "bi_to_tri_monthly"}, {"label": "Every 4-6 months", "value": "four_to_six_months"}, {"label": "Once a year", "value": "yearly"}, {"label": "Occasionally", "value": "occasionally"}, {"label": "Never", "value": "never"}]}, "type": "select", "input": true, "label": "How often do you receive laser treatments?", "widget": "html5", "tableView": true, "customConditional": "show = data.current_details_recent_procedures && data.current_details_recent_procedures.lasers;"}, {"key": "current_laser_duration", "data": {"values": [{"label": "Less than 6 months", "value": "less_than_6_months"}, {"label": "6-12 months", "value": "6_12_months"}, {"label": "1-2 years", "value": "1_2_years"}, {"label": "2-5 years", "value": "2_5_years"}, {"label": "More than 5 years", "value": "more_than_5_years"}]}, "type": "select", "input": true, "label": "How long have you been receiving laser treatments?", "widget": "html5", "tableView": true, "customConditional": "show = data.current_laser_frequency"}, {"key": "current_laser_types", "type": "selectboxes", "input": true, "label": "What types of laser treatments do you receive? (Select all that apply)", "values": [{"label": "Ablative Laser (e.g., CO2, Erbium)", "value": "ablative"}, {"label": "Non-Ablative Laser (e.g., Nd:YAG, Diode, Pico)", "value": "non_ablative"}, {"label": "IPL (Intense Pulsed Light)", "value": "ipl"}, {"label": "Fractional Laser", "value": "fractional"}, {"label": "<PERSON><PERSON><PERSON> (PDL)", "value": "pdl"}, {"label": "Q-Switched Laser", "value": "q_switched"}, {"label": "Other", "value": "other"}], "tableView": true, "customConditional": "show = data.current_laser_duration"}, {"key": "current_laser_type_other", "type": "textfield", "input": true, "label": "If other, please specify the type:", "tableView": true, "customConditional": "show = data.current_laser_types && data.current_laser_types.other;"}, {"key": "current_laser_side_effects", "type": "selectboxes", "input": true, "label": "Have you experienced any side effects from laser treatments? (Select all that apply)", "values": [{"label": "Redness", "value": "redness"}, {"label": "Swelling", "value": "swelling"}, {"label": "Blisters", "value": "blisters"}, {"label": "Hyperpigmentation", "value": "hyperpigmentation"}, {"label": "Hypopigmentation", "value": "hypopigmentation"}, {"label": "<PERSON>arring", "value": "scarring"}, {"label": "Skin irritation", "value": "skin_irritation"}, {"label": "None", "value": "none"}], "tableView": true, "customConditional": "show = data.current_laser_types"}, {"key": "current_laser_improvement", "type": "radio", "input": true, "label": "Have you noticed an improvement in your skin since starting laser treatments?", "values": [{"label": "Yes, significant improvement", "value": "significant_improvement"}, {"label": "Yes, some improvement", "value": "some_improvement"}, {"label": "No noticeable improvement", "value": "no_improvement"}, {"label": "Yes, but with some areas needing further improvement", "value": "partial_improvement"}, {"label": "No, I'm dissatisfied", "value": "dissatisfied"}, {"label": "Not applicable", "value": "not_applicable"}], "tableView": true, "customConditional": "show = data.current_laser_side_effects"}, {"key": "current_prescription_retinoids", "type": "selectboxes", "input": true, "label": "Which retinoids are you currently using? (Select all that apply)", "values": [{"label": "Tretinoin", "value": "tretinoin"}, {"label": "Adapal<PERSON>", "value": "adapalene"}, {"label": "Tazarotene", "value": "tazarotene"}], "tableView": true, "customConditional": "show = data.general_prescription_treatments && data.general_prescription_treatments.retinoids;", "optionsLabelPosition": "right"}, {"key": "current_antibiotic_creams", "type": "selectboxes", "input": true, "label": "Which antibiotic creams are you currently using? (Select all that apply)", "values": [{"label": "Clindamycin", "value": "clindamycin"}, {"label": "Erythromycin", "value": "erythromycin"}, {"label": "Metronidazole", "value": "metronidazole"}], "tableView": true, "customConditional": "show = data.general_prescription_treatments && data.general_prescription_treatments.current_antibiotic_creams;", "optionsLabelPosition": "right"}, {"key": "current_skin_lightening_creams", "type": "selectboxes", "input": true, "label": "Which skin lightening creams are you currently using? (Select all that apply)", "values": [{"label": "Hydroquinone", "value": "hydroquinone"}, {"label": "Azelaic Acid", "value": "azelaic_acid"}, {"label": "Kojic Acid", "value": "kojic_acid"}, {"label": "Niacinamide", "value": "niacinamide"}], "tableView": true, "customConditional": "show = data.general_prescription_treatments && data.general_prescription_treatments.current_skin_lightening_creams;", "optionsLabelPosition": "right"}, {"key": "current_chemical_peels", "type": "selectboxes", "input": true, "label": "Which chemical peels are you currently undergoing? (Select all that apply)", "values": [{"label": "Glycolic Acid Peel", "value": "glycolic_acid_peel"}, {"label": "Salicylic Acid Peel", "value": "salicylic_acid_peel"}, {"label": "Other Peels", "value": "other_peels"}], "tableView": true, "customConditional": "show = data.general_procedures && data.general_procedures.peels;", "optionsLabelPosition": "right"}, {"key": "heading_sunscreen_use", "type": "content", "html": "<h2><strong>Sunscreen</strong></h2>", "input": false, "tableView": false}, {"key": "sunscreen_use_general", "type": "radio", "input": true, "label": "Do you currently use sunscreen?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "sunscreen_product_spf", "type": "textfield", "input": true, "label": "What SPF is your usual sunscreen?", "customConditional": "show = data.sunscreen_use_general === 'yes';"}, {"key": "sunscreen_product_type", "type": "selectboxes", "input": true, "label": "What type of sunscreen do you typically use? (Select all that apply)", "values": [{"label": "Cream/lotion/Gel", "value": "cream_lotion_gel"}, {"label": "Spray", "value": "spray"}, {"label": "Stick", "value": "stick"}], "customConditional": "show = data.sunscreen_use_general === 'yes';"}, {"key": "sunscreen_tint", "type": "radio", "input": true, "label": "Is it tinted or non-tinted?", "values": [{"label": "Tinted", "value": "tinted"}, {"label": "Non-tinted", "value": "non_tinted"}, {"label": "Not sure", "value": "not_sure"}], "customConditional": "show = data.sunscreen_use_general === 'yes';"}, {"key": "sunscreen_ingredients", "type": "selectboxes", "input": true, "label": "What type of sunscreen ingredients do you use? (Select all that apply)", "values": [{"label": "Mineral-based (zinc oxide, titanium dioxide)", "value": "mineral"}, {"label": "Chemical-based (avobenzone, oxybenzone, etc.)", "value": "chemical"}, {"label": "Mixed", "value": "mixed"}, {"label": "Not sure", "value": "not_sure"}], "customConditional": "show = data.sunscreen_use_general === 'yes';"}, {"key": "sunscreen_broad_spectrum", "type": "radio", "input": true, "label": "Is it labelled as broad spectrum (UVA & UVB)?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "not_sure"}], "customConditional": "show = data.sunscreen_use_general === 'yes';"}, {"key": "sunscreen_application_frequency", "type": "radio", "input": true, "label": "How often do you apply sunscreen?", "values": [{"label": "Daily, year-round", "value": "daily_year_round"}, {"label": "Only in summer or sunny weather", "value": "summer_only"}, {"label": "Only when outdoors for long periods", "value": "outdoor_long_periods"}, {"label": "Rarely or never", "value": "rarely"}], "customConditional": "show = data.sunscreen_use_general === 'yes';"}, {"key": "sunscreen_reapplication", "type": "radio", "input": true, "label": "How often do you reapply sunscreen?", "values": [{"label": "Every 2 hours when outdoors", "value": "2_hours"}, {"label": "After swimming/sweating", "value": "post_activity"}, {"label": "I don't usually reapply", "value": "dont_reapply"}, {"label": "Not applicable", "value": "not_applicable"}], "customConditional": "show = data.sunscreen_use_general === 'yes';"}, {"key": "sunscreen_application_areas", "type": "selectboxes", "input": true, "label": "Where do you apply sunscreen? (Select all that apply)", "values": [{"label": "Face", "value": "face"}, {"label": "Neck", "value": "neck"}, {"label": "Ears", "value": "ears"}, {"label": "Arms", "value": "arms"}, {"label": "Hands", "value": "hands"}, {"label": "Legs", "value": "legs"}, {"label": "<PERSON><PERSON><PERSON>", "value": "scalp"}, {"label": "Other", "value": "other"}], "customConditional": "show = data.sunscreen_use_general === 'yes';"}, {"key": "sunscreen_barriers", "type": "selectboxes", "input": true, "label": "What prevents you from using sunscreen regularly? (Select all that apply)", "values": [{"label": "I forget", "value": "forget"}, {"label": "I don't think I need it", "value": "dont_need"}, {"label": "It feels greasy or irritating", "value": "greasy"}, {"label": "It causes acne or other skin reactions", "value": "reactions"}, {"label": "Too expensive", "value": "expensive"}, {"label": "Hard to find one that suits my skin tone", "value": "skin_tone"}, {"label": "Not sure which product to choose", "value": "unsure_what_to_choose"}, {"label": "Other", "value": "other"}]}, {"key": "sunscreen_history", "type": "selectboxes", "input": true, "label": "Have you ever had any of the following? (Select all that apply)", "values": [{"label": "Sunburn in the last year", "value": "sunburn"}, {"label": "A history of skin cancer", "value": "skin_cancer"}, {"label": "Hyperpigmentation worsened by sun", "value": "hyperpigmentation"}, {"label": "Photosensitive reactions or drug-induced sun sensitivity", "value": "photosensitivity"}]}]}