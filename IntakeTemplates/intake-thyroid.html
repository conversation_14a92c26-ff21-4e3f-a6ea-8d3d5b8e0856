{% load template_extras %}
{% with data=questionnaire.hpc.data %}
{% with summary=questionnaire.raw_formio_summary %}
{% with insured=questionnaire.insured_assays.all %}
{% with uninsured=questionnaire.uninsured_assays.all %}

<p>Hi {{patient.name}},</p>

<p>This is {{doctor.name}} (CPSO #{{doctor.cpso_number}}) &amp; clinic contact phone {{doctor.phone}}.</p>

<p>I've reviewed your intake regarding possible hypothyroidism. If any questions were unclear or if your answers didn’t reflect your current concerns, we can arrange a secure follow-up chat. Otherwise, here is the plan:</p>

<h4 style="text-align: left; font-weight: bold; margin-top: 30px;">Lab Testing Requested</h4>

{% if insured %}
<p><strong>Insured Tests:</strong></p>
<ul>
  {% for a in insured %}
    <li>{{ a.name }} ({{ a.test_type }} test)</li>
  {% endfor %}
</ul>
{% endif %}

{% if uninsured %}
<p><strong>Uninsured Tests:</strong></p>
<ul>
  {% for a in uninsured %}
    <li>{{ a.name }} ({{ a.test_type }} test)</li>
  {% endfor %}
</ul>
<p>Uninsured testing may include additional thyroid markers not covered by OHIP. These are optional and can be declined at the lab.</p>
{% endif %}

<h3 style="text-align: left; font-weight: bold; margin-top: 30px;">Plan</h3>
<ol>
  <li>Visit your preferred lab to complete the blood work.</li>
  <li>Your results will be uploaded to your portal once available.</li>
  <li>If anything abnormal is found, we will contact you to discuss next steps.</li>
</ol>

<h4 style="text-align: left; font-weight: bold; margin-top: 30px;">Health Summary</h4>
<li><strong>Current Medications:</strong> 
    {% if summary.medications_list and summary.medications_list.c_val %}
      {{ summary.medications_list.c_val }}
    {% else %}
      None
    {% endif %}
  </li>

<li><strong>Medication Allergies:</strong> 
  {% if summary.medication_allergies_list and summary.medication_allergies_list.c_val %}
    {{ summary.medication_allergies_list.c_val }}
  {% else %}
    None
  {% endif %}
</li>

  <li><strong>Past Surgeries:</strong> 
    {% if summary.past_surgeries_list and summary.past_surgeries_list.c_val %}
      {{ summary.past_surgeries_list.c_val }}
    {% else %}
      None
    {% endif %}
  </li>


<h2 style="text-align: center; font-weight: bold; margin-top: 40px; margin-bottom: 20px;">
    Please Confirm Your Medical and Symptom History
    </h2>

    {% with reasons=summary|confirm:"reason_thyroid_testing" %}
    {% if reasons %}
      <h4 style="text-align: left; font-weight: bold; margin-top: 30px;">Reason for Thyroid Testing</h4>
      <p>
        {% for r in reasons %}
          {{ r|safe }}{% if not forloop.last %}, {% endif %}
        {% endfor %}
      </p>
    {% endif %}
  {% endwith %}

{% if data.autoimmune_condition_type and data.autoimmune_condition_type != "none" %}
<p><strong>Associated Autoimmune Condition:</strong> {{ data.autoimmune_condition_type }}</p>
{% endif %}

<h4 style="text-align: left; font-weight: bold; margin-top: 30px;">Symptom Summary</h4>

{% if data.symptom_onset_pattern == "same_day" and data.all_thyroid_symptoms_start.label %}
  <p>All symptoms began around the same time: <strong>{{ data.all_thyroid_symptoms_start.label }}</strong>.</p>
{% endif %}

<ul>

<h5>Thyroid-Related Symptoms</h5>

<li>Fatigue or low energy
  <ul>
    {% if data.symptom_onset_pattern != "same_day" and data.symptom_start_fatigue.label %}<li>Started: {{ data.symptom_start_fatigue.label }}</li>{% endif %}
    {% if data.fatigue_time_of_day %}<li>Most noticeable: {{ data.fatigue_time_of_day }}</li>{% endif %}
    {% if data.fatigue_frequency %}<li>Frequency: {{ data.fatigue_frequency }}</li>{% endif %}
    {% if data.fatigue_affects_function %}<li>Affects daily life: {{ data.fatigue_affects_function|title }}</li>{% endif %}
    {% if data.fatigue_daytime_sleepiness %}<li>Daytime sleepiness: {{ data.fatigue_daytime_sleepiness }}</li>{% endif %}
    {% if data.fatigue_osa_risk %}<li>OSA Risk Factors: {{ data.fatigue_osa_risk }}</li>{% endif %}
    {% if data.fatigue_osa_evaluation %}<li>OSA Diagnosis: {{ data.fatigue_osa_evaluation }}</li>{% endif %}
  </ul>
</li>

<li>Cold intolerance
  <ul>
    {% if data.symptom_onset_pattern != "same_day" and data.symptom_start_cold_intolerance.label %}<li>Started: {{ data.symptom_start_cold_intolerance.label }}</li>{% endif %}
    {% if data.cold_intolerance_description %}
      {% for k, v in data.cold_intolerance_description.items %}
        {% if v %}<li>Description: {{ k }}</li>{% endif %}
      {% endfor %}
    {% endif %}
  </ul>
</li>

<li>Weight gain
  <ul>
    {% if data.symptom_onset_pattern != "same_day" and data.symptom_start_weight_gain.label %}<li>Started: {{ data.symptom_start_weight_gain.label }}</li>{% endif %}
    {% if data.weight_gain_description %}
      {% for k, v in data.weight_gain_description.items %}
        {% if v %}<li>Type: {{ k }}</li>{% endif %}
      {% endfor %}
    {% endif %}
    {% if data.weight_gain_amount and data.weight_gain_units %}<li>Amount: {{ data.weight_gain_amount }} {{ data.weight_gain_units }}</li>{% endif %}
  </ul>
</li>

<li>Constipation
  <ul>
    {% if data.symptom_onset_pattern != "same_day" and data.symptom_start_constipation.label %}<li>Started: {{ data.symptom_start_constipation.label }}</li>{% endif %}
    {% if data.constipation_frequency %}<li>Frequency: {{ data.constipation_frequency }}</li>{% endif %}
    {% if data.constipation_stool_type %}
      {% for k, v in data.constipation_stool_type.items %}
        {% if v %}<li>Stool type: {{ k }}</li>{% endif %}
      {% endfor %}
    {% endif %}
    {% if data.constipation_straining %}<li>Straining: {{ data.constipation_straining }}</li>{% endif %}
    {% if data.constipation_incomplete %}<li>Incomplete emptying: {{ data.constipation_incomplete }}</li>{% endif %}
    {% if data.constipation_laxatives %}<li>Laxative use: {{ data.constipation_laxatives }}</li>{% endif %}
    {% if data.constipation_impact_life %}<li>Impact on life: {{ data.constipation_impact_life }}</li>{% endif %}
  </ul>
</li>

<li>Dry skin
  <ul>
    {% if data.symptom_onset_pattern != "same_day" and data.symptom_start_dry_skin.label %}<li>Started: {{ data.symptom_start_dry_skin.label }}</li>{% endif %}
    {% if data.dry_skin_description %}
      {% for k, v in data.dry_skin_description.items %}
        {% if v %}<li>Location: {{ k }}</li>{% endif %}
      {% endfor %}
    {% endif %}
  </ul>
</li>

<li>Heavy or irregular periods
  <ul>
    {% if data.symptom_onset_pattern != "same_day" and data.symptom_start_menstrual_irregularity.label %}<li>Started: {{ data.symptom_start_menstrual_irregularity.label }}</li>{% endif %}
    {% if data.menstrual_irregularity_description %}
      {% for k, v in data.menstrual_irregularity_description.items %}
        {% if v %}<li>Description: {{ k }}</li>{% endif %}
      {% endfor %}
    {% endif %}
  </ul>
</li>

<li>Hair thinning or hair loss
  <ul>
    {% if data.symptom_onset_pattern != "same_day" and data.symptom_start_hair_loss.label %}<li>Started: {{ data.symptom_start_hair_loss.label }}</li>{% endif %}
    {% if data.hair_loss_description %}
      {% for k, v in data.hair_loss_description.items %}
        {% if v %}<li>Description: {{ k }}</li>{% endif %}
      {% endfor %}
    {% endif %}
  </ul>
</li>

</ul>

<h5>Mental Health Symptoms</h5>

{% if data.mental_health_symptoms.anger or data.mental_health_symptoms.low_mood or data.mental_health_symptoms.anxiety or data.mental_health_symptoms.psychosis %}
<li>Mental health symptoms
  <ul>
    {% if data.mental_health_symptoms.low_mood %}<li>Low mood or depression</li>{% endif %}
    {% if data.mental_health_symptoms.anxiety %}<li>Anxiety or nervousness</li>{% endif %}
    {% if data.mental_health_symptoms.anger %}<li>Irritability or anger</li>{% endif %}
    {% if data.mental_health_symptoms.psychosis %}<li>Hallucinations or seeing/hearing things that aren't there</li>{% endif %}

    {% if data.mental_health_onset %}
    <li>Onset:
      {% if data.mental_health_onset == "under_1_week" %}Within the last week
      {% elif data.mental_health_onset == "1_4_weeks" %}1–4 weeks ago
      {% elif data.mental_health_onset == "1_3_months" %}1–3 months ago
      {% elif data.mental_health_onset == "3_6_months" %}3–6 months ago
      {% elif data.mental_health_onset == "6_12_months" %}6–12 months ago
      {% elif data.mental_health_onset == "over_1_year_continuous" %}Ongoing for over a year
      {% elif data.mental_health_onset == "recurrent_1_month" %}Comes and goes over the past month
      {% elif data.mental_health_onset == "recurrent_1_year" %}Recurring over the past year
      {% elif data.mental_health_onset == "recurrent_many_years" %}Recurring for many years
      {% elif data.mental_health_onset == "unsure" %}Uncertain timing
      {% else %}{{ data.mental_health_onset }}
      {% endif %}
    </li>
    {% endif %}

    {% if data.mental_health_prior_treatment %}
      {% if data.mental_health_prior_treatment.therapy %}<li>Past treatment: Therapy</li>{% endif %}
      {% if data.mental_health_prior_treatment.medication %}<li>Past treatment: Medication</li>{% endif %}
      {% if data.mental_health_prior_treatment.hospitalization %}<li>Past treatment: Hospitalization</li>{% endif %}
      {% if data.mental_health_prior_treatment.none %}<li>No prior treatment</li>{% endif %}
    {% endif %}

    {% if data.mental_health_therapy_discussion %}
      <li>Has discussed therapy: {{ data.mental_health_therapy_discussion|yesno:"Yes,No" }}</li>
    {% endif %}

    {% if data.mental_health_suicidal_check == "no" %}
      <li>No current suicidal thoughts</li>
    {% endif %}

    {% if data.mental_health_safety_understanding %}
      <li>Understands safety plan: {{ data.mental_health_safety_understanding|title }}</li>
    {% endif %}
  </ul>
</li>
{% endif %}

<h5>Cardiovascular Symptoms</h5>

{% if data.cardiovascular_symptoms.chest_pain or data.cardiovascular_symptoms.palpitations or data.cardiovascular_symptoms.swelling or data.cardiovascular_symptoms.dizziness %}
<li>Cardiac symptoms
  <ul>
    {% if data.cardiovascular_symptoms.chest_pain %}
    <li>Chest pain
      <ul>
        {% if data.chest_pain_onset %}<li>Onset: {{ data.chest_pain_onset }}</li>{% endif %}

        {% if data.chest_pain_triggers %}
          {% if data.chest_pain_triggers.meal %}<li>Triggered by meals</li>{% endif %}
          {% if data.chest_pain_triggers.lying %}<li>Triggered while lying down</li>{% endif %}
          {% if data.chest_pain_triggers.stress %}<li>Triggered by emotional stress</li>{% endif %}
          {% if data.chest_pain_triggers.unknown %}<li>No identifiable trigger</li>{% endif %}
          {% if data.chest_pain_triggers.exertion %}<li>Triggered by physical exertion</li>{% endif %}
          {% if data.chest_pain_triggers.breathing %}<li>Triggered by deep breathing</li>{% endif %}
        {% endif %}

        {% if data.chest_pain_relievers %}
          {% if data.chest_pain_relievers.rest %}<li>Relieved by rest</li>{% endif %}
          {% if data.chest_pain_relievers.lying %}<li>Relieved by lying down</li>{% endif %}
          {% if data.chest_pain_relievers.unsure %}<li>Not sure what relieves it</li>{% endif %}
          {% if data.chest_pain_relievers.nothing %}<li>Not relieved by anything</li>{% endif %}
          {% if data.chest_pain_relievers.standing %}<li>Relieved by standing</li>{% endif %}
          {% if data.chest_pain_relievers.medication %}<li>Relieved by medication</li>{% endif %}
        {% endif %}

        {% if data.chest_pain_location %}
          {% if data.chest_pain_location.left_chest %}<li>Located in left chest</li>{% endif %}
          {% if data.chest_pain_location.right_chest %}<li>Located in right chest</li>{% endif %}
          {% if data.chest_pain_location.upper_chest %}<li>Located in upper chest</li>{% endif %}
          {% if data.chest_pain_location.centre_chest %}<li>Located in centre of chest</li>{% endif %}
          {% if data.chest_pain_location.other %}<li>Location: Other</li>{% endif %}
        {% endif %}

        {% if data.chest_pain_radiation %}
        <li>Radiation:
          <ul>
            {% if data.chest_pain_radiation.jaw %}<li>Jaw</li>{% endif %}
            {% if data.chest_pain_radiation.neck %}<li>Neck</li>{% endif %}
            {% if data.chest_pain_radiation.back %}<li>Back</li>{% endif %}
            {% if data.chest_pain_radiation.left_arm %}<li>Left arm</li>{% endif %}
            {% if data.chest_pain_radiation.right_arm %}<li>Right arm</li>{% endif %}
            {% if data.chest_pain_radiation.none %}<li>No radiation</li>{% endif %}
          </ul>
        </li>
      {% endif %}

        {% if data.chest_pain_pattern %}<li>Pattern: {{ data.chest_pain_pattern }}</li>{% endif %}
      </ul>
    </li>
    {% endif %}

    {% if data.cardiovascular_symptoms.palpitations %}<li>Palpitations</li>{% endif %}
    {% if data.cardiovascular_symptoms.swelling %}<li>Swelling in legs or ankles</li>{% endif %}
    {% if data.cardiovascular_symptoms.dizziness %}<li>Dizziness or fainting</li>{% endif %}
  </ul>
</li>
{% endif %}


<h5>Respiratory Symptoms</h5>

{% if data.respiratory_symptoms.cough or data.respiratory_symptoms.shortness_of_breath or data.respiratory_symptoms.wheezing %}
<li>Respiratory symptoms
  <ul>
    {% if data.respiratory_symptoms.cough %}
      <li>Cough</li>
    {% endif %}

    {% if data.respiratory_symptoms.shortness_of_breath %}
      <li>Shortness of breath</li>
    {% endif %}

    {% if data.respiratory_symptoms.wheezing %}
    <li>Wheezing
      <ul>
        {% if data.wheezing_timing.rest %}<li>Occurs at rest</li>{% endif %}
        {% if data.wheezing_timing.night %}<li>Occurs at night</li>{% endif %}
        {% if data.wheezing_timing.exercise %}<li>Triggered by exercise</li>{% endif %}
        {% if data.wheezing_timing.irritants %}<li>Triggered by environmental irritants</li>{% endif %}
        {% if data.wheezing_timing.lying_down %}<li>Worsens when lying down</li>{% endif %}
        {% if data.wheezing_timing.cold_weather %}<li>Triggered by cold weather</li>{% endif %}

        {% if data.wheezing_relief %}
          <li>Relieved by inhaler/puffer: {{ data.wheezing_relief|yesno:"Yes,No" }}</li>
        {% endif %}

        {% if data.wheezing_asthma_history %}
          <li>Asthma history: {{ data.wheezing_asthma_history|title }}</li>
        {% endif %}
      </ul>
    </li>
    {% endif %}
  </ul>
</li>
{% endif %}

</ul>

<h4 style="text-align: left; font-weight: bold; margin-top: 30px;">Symptoms Not Present</h4>

<p>
  <strong>Thyroid:</strong>
  {% if data.no_thyroid_symptoms %}
    None reported.
  {% else %}
    {% if not data.thyroid_symptoms.fatigue %}Fatigue or low energy,{% endif %}
    {% if not data.thyroid_symptoms.dry_skin %} Dry skin,{% endif %}
    {% if not data.thyroid_symptoms.constipation %} Constipation,{% endif %}
    {% if not data.thyroid_symptoms.cold_intolerance %} Cold intolerance,{% endif %}
    {% if not data.thyroid_symptoms.weight_gain %} Weight gain,{% endif %}
    {% if not data.thyroid_symptoms.hair_loss %} Hair thinning or loss,{% endif %}
    {% if not data.thyroid_symptoms.menstrual_irregularity %} Heavy or irregular periods,{% endif %}
  {% endif %}
</p>

<p>
  <strong>Mental Health:</strong>
  {% if not data.mental_health_symptoms.low_mood and not data.mental_health_symptoms.anxiety and not data.mental_health_symptoms.anger and not data.mental_health_symptoms.psychosis %}
    None reported.
  {% else %}
    {% if not data.mental_health_symptoms.low_mood %}Low mood or depression,{% endif %}
    {% if not data.mental_health_symptoms.anxiety %} Anxiety,{% endif %}
    {% if not data.mental_health_symptoms.anger %} Irritability or anger,{% endif %}
    {% if not data.mental_health_symptoms.psychosis %} Hallucinations or psychosis,{% endif %}
  {% endif %}
</p>

<p>
  <strong>Cardiovascular:</strong>
  {% if not data.cardiovascular_symptoms.chest_pain and not data.cardiovascular_symptoms.palpitations and not data.cardiovascular_symptoms.swelling and not data.cardiovascular_symptoms.dizziness %}
    None reported.
  {% else %}
    {% if not data.cardiovascular_symptoms.chest_pain %}Chest pain,{% endif %}
    {% if not data.cardiovascular_symptoms.palpitations %} Palpitations,{% endif %}
    {% if not data.cardiovascular_symptoms.swelling %} Swelling in legs or ankles,{% endif %}
    {% if not data.cardiovascular_symptoms.dizziness %} Dizziness or fainting,{% endif %}
  {% endif %}
</p>

<p>
  <strong>Respiratory:</strong>
  {% if not data.respiratory_symptoms.cough and not data.respiratory_symptoms.shortness_of_breath and not data.respiratory_symptoms.wheezing %}
    None reported.
  {% else %}
    {% if not data.respiratory_symptoms.cough %}Cough,{% endif %}
    {% if not data.respiratory_symptoms.shortness_of_breath %} Shortness of breath,{% endif %}
    {% if not data.respiratory_symptoms.wheezing %} Wheezing,{% endif %}
  {% endif %}
</p>


<h4 style="text-align: left; font-weight: bold; margin-top: 30px;">Thyroid Testing Notes</h4>
<ul>
  <li><strong>TSH Testing:</strong> Can be done at any time of day</li>
  <li><strong>Repeat Testing:</strong> If treatment is started, bloodwork is repeated after 6–8 weeks</li>
  <li><strong>Physical Exam:</strong> Always recommended for complete thyroid evaluation</li>
</ul>

<p>Best regards,<br>{{doctor.name}}</p>

{% endwith %}
{% endwith %}
{% endwith %}
{% endwith %}