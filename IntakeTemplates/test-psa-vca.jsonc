{"components": [{"key": "indications", "type": "selectboxes", "input": true, "label": "Please select your reason(s) for testing:", "values": [{"label": "Past prostate cancer (monitoring)", "value": "history_prostate_cancer", "shortcut": ""}, {"label": "Previously high PSA (follow-up)", "value": "prior_elevated_psa", "shortcut": ""}, {"label": "On testosterone / anabolic steroids (monitoring)", "value": "on_testosterone_or_anabolic_steroids", "shortcut": ""}, {"label": "Routine check / preventive screening", "value": "preventative_screening", "shortcut": ""}], "inputType": "checkbox", "tableView": true, "confirm_label": "Reason(s) for PSA testing:", "optionsLabelPosition": "right"}, {"key": "no_indications", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a reason."}, "validate": {"custom": "valid = _.some(_.values(data.indications)) || data.no_indications;"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "psa_red_flag", "type": "selectboxes", "input": true, "label": "Please select if any of the following apply to you:", "values": [{"label": "Fevers or Chills", "value": "fevers_chills", "shortcut": ""}, {"label": "Bloody Urine", "value": "hematuria", "shortcut": ""}, {"label": "Abdominal and/or Pelvic Pain", "value": "abdo_pain", "shortcut": ""}, {"label": "Unable to Urinate", "value": "unable_to_urinate", "shortcut": ""}, {"label": "Penile Discharge", "value": "penile_discharge", "shortcut": ""}, {"label": "<PERSON>l Discharge", "value": "anal_discharge", "shortcut": ""}, {"label": "<PERSON>well", "value": "malaise", "shortcut": ""}], "inputType": "checkbox", "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = _.some(_.values(data.indications));"}, {"key": "no_psa_red_flag", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = _.some(_.values(data.psa_red_flag)) || data.no_psa_red_flag;"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = _.some(_.values(data.indications));"}, {"key": "rejected_html", "type": "content", "input": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = (function(){ return _.some(_.values(data.psa_red_flag)) ? \"<h3 class='text-red'>Please seek urgent medical attention!</h3><p class='text-red'>The symptoms you selected can indicate a serious condition. PSA testing is not appropriate until these have been evaluated in-person. Please attend an urgent care clinic or emergency department.</p>\" : null; })();"}, {"key": "contraindication_keys", "type": "textfield", "input": true, "label": "Contraindication keys:", "hidden": true, "disabled": true, "multiple": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "value = _.keys(_.pickBy(data.psa_red_flag));", "refreshOnChange": true}, {"key": "approved_html", "type": "content", "input": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = (function(){ var hasRedFlags = _.some(_.values(data.psa_red_flag)); if (hasRedFlags) { return null; } var anySel = _.some(_.values(data.indications)); var none = !!data.no_indications; if (!anySel && !none) { return null; } var hx = !!(data.indications && (data.indications.history_prostate_cancer || data.indications.prior_elevated_psa)); var androgens = !!(data.indications && data.indications.on_testosterone_or_anabolic_steroids); var prev = !!(data.indications && data.indications.preventative_screening); var html = \"<h3 class='text-green'>PSA Testing</h3>\"; if (hx) { html += \"<p>TeleTest physicians can arrange PSA testing and provide follow-up based on your history.</p>\"; } if (androgens) { html += \"<p>TeleTest physicians can arrange PSA testing. Typical monitoring includes a PSA at <strong>baseline</strong>, again at <strong>3-12 months</strong> after starting or a dose change, and then <strong>annually</strong> thereafter.</p>\"; } if (prev && !hx && !androgens) { html += \"<p>Please note that routine PSA testing is <em>not recommended for everyone</em> according to current guidelines, but you can still choose to have testing.</p><p><u>Factors that may raise your risk profile for prostate cancer and PSA testing relevance include:</u></p><ul><li>Age over 50</li><li>Black ancestry</li><li>Family history (father, brother, or close relative with prostate cancer)</li><li>Certain inherited mutations (e.g., BRCA1/2)</li></ul>\"; } if (none) { html += \"<p>Based on your answers, PSA testing is not required right now. If your situation changes, we can review and arrange testing.</p>\"; } html += \"<p><strong>Cost &amp; timing:</strong> In most cases, PSA testing for screening is not covered by OHIP. Labs charge about $40. Results are usually available within 24-48 hours and will be uploaded to your profile.</p>\"; return html; })();"}]}