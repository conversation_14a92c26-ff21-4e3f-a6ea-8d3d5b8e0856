{"components": [{"key": "heading_photo_prescription", "html": "<h1><strong>Photograph of Current Prescription</strong></h1><p>To help expedite your care, please upload a clear photo that confirms your current prescription. Acceptable forms include pharmacy receipts, prescription bottles, or a screenshot from an online pharmacy. The image must clearly display your full name, the medication name, and the dosage.</p><p><strong>If you have previously obtained this medication through TeleTest, or do not currently have access to your prescription, you may skip the upload by checking the box below.</strong></p>", "type": "content", "input": false, "label": "Content"}, {"key": "skip_prescription_photo", "type": "checkbox", "label": "I have already obtained this medication from TeleTest OR I do not have access to my prior prescription", "input": true, "defaultValue": false, "tableView": true}, {"key": "photo_upload_header", "html": "<h2>Photo Upload</h2>", "type": "content", "input": false, "label": "Content", "customConditional": "show = !data.skip_prescription_photo;"}, {"key": "uploadUrl", "url": "/app/q/{qpk}/formio-files/{pk}/", "type": "file", "image": true, "input": true, "label": "Upload: URL", "webcam": true, "capture": "user", "storage": "url", "multiple": true, "fileTypes": [{"label": "", "value": ""}], "tableView": true, "validateWhenHidden": false, "customConditional": "show = !data.skip_prescription_photo;"}]}