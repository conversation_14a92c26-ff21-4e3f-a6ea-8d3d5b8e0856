{"components": [{"key": "reason_thyroid_testing", "type": "selectboxes", "label": "What is your reason for thyroid testing or treatment?", "values": [{"label": "Family member (blood-relative) has hypothyroidism", "value": "family_history"}, {"label": "I am currently taking thyroid medication or stopped thyroid medication", "value": "on_medication"}, {"label": "I have symptoms of hypothyroidism (i.e. weight gain, fatigue)", "value": "have_symptoms"}, {"label": "I am on a medication that affects thyroid function (e.g. lithium, amiodarone)", "value": "medication_thyroid_risk"}, {"label": "I have an autoimmune condition (e.g. celiac disease, type 1 diabetes, lupus, rheumatoid arthritis)", "value": "autoimmune_comorbidity"}], "input": true, "optionsLabelPosition": "right"}, {"key": "no_reason_thyroid_testing", "type": "checkbox", "input": true, "label": "None of the above", "customClass": "mt-n3", "defaultValue": false, "validate": {"custom": "valid = !!data.no_reason_thyroid_testing || !!_.some(_.values(data.reason_thyroid_testing));"}, "errors": {"custom": "Please select at least one reason or 'None of the above'."}}, {"key": "thyroid_symptoms", "type": "selectboxes", "input": true, "label": "Which of the following symptoms do you have", "values": [{"label": "Fatigue or low energy", "value": "fatigue", "shortcut": ""}, {"label": "Dry skin", "value": "dry_skin", "shortcut": ""}, {"label": "Constipation", "value": "constipation", "shortcut": ""}, {"label": "Feeling cold when others are comfortable", "value": "cold_intolerance", "shortcut": ""}, {"label": "Weight gain without changes in diet or exercise", "value": "weight_gain", "shortcut": ""}, {"label": "Thinning hair or hair loss", "value": "hair_loss", "shortcut": ""}, {"label": "Heavy or irregular periods", "value": "menstrual_irregularity", "shortcut": ""}], "inputType": "checkbox", "tableView": true, "customConditional": "show = !data.no_reason_thyroid_testing && data.reason_thyroid_testing && data.reason_thyroid_testing.have_symptoms", "optionsLabelPosition": "right"}, {"key": "no_thyroid_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "Please select at least one symptom or 'None of the above'."}, "validate": {"custom": "valid = !!data.no_thyroid_symptoms || !!_.some(_.values(data.thyroid_symptoms));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = !data.no_reason_thyroid_testing && data.reason_thyroid_testing && data.reason_thyroid_testing.have_symptoms"}, {"key": "thyroid_symptom_duration", "type": "select", "label": "How long have you had these symptoms?", "widget": "html5", "data": {"values": [{"label": "Less than 12 weeks", "value": "< 12 weeks"}, {"label": "More than 12 weeks", "value": "> 12 weeks"}]}, "input": true, "validate": {"required": true}, "customConditional": "show = data.reason_thyroid_testing && data.reason_thyroid_testing.have_symptoms && _.some(data.thyroid_symptoms, Boolean) && !data.no_thyroid_symptoms;"}, {"key": "autoimmune_condition_type", "type": "select", "input": true, "label": "Which autoimmune condition do you have?", "widget": "html5", "data": {"values": [{"label": "Celiac disease", "value": "celiac"}, {"label": "Type 1 diabetes", "value": "type1_diabetes"}, {"label": "<PERSON><PERSON> (SLE)", "value": "lupus"}, {"label": "Rheumatoid arthritis", "value": "rheumatoid_arthritis"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "psoriasis"}, {"label": "Vitiligo", "value": "vitiligo"}, {"label": "Multiple sclerosis", "value": "ms"}, {"label": "Inflammatory bowel disease (<PERSON><PERSON><PERSON>'s, ulcerative colitis)", "value": "ibd"}, {"label": "<PERSON>'s disease", "value": "addisons"}, {"label": "I don't have any of these", "value": "none"}]}, "validate": {"required": true}, "customConditional": "show = data.reason_thyroid_testing && data.reason_thyroid_testing.autoimmune_comorbidity === true"}, {"key": "contraindications_thyroid", "type": "selectboxes", "input": true, "label": "Are you currently experiencing any of the following symptoms:", "values": [{"label": "Heart palpitations (fast or irregular heartbeat)", "value": "palpitations", "shortcut": ""}, {"label": "Chest Pain or Tightness", "value": "chest_pain", "shortcut": ""}, {"label": "Unexpected weight loss (without trying)", "value": "weight_loss", "shortcut": ""}, {"label": "Night sweats or fevers (i.e. high temperature)", "value": "night_sweats", "shortcut": ""}, {"label": "Shortness of breath", "value": "shortness_breath", "shortcut": ""}, {"label": "A new rash", "value": "new_rash", "shortcut": ""}, {"label": "Nausea or vomiting", "value": "nausea_vomiting", "shortcut": ""}, {"label": "Abdominal or pelvic pain/cramping", "value": "abdominal_pain", "shortcut": ""}, {"label": "Headaches (new or worsening)", "value": "headache", "shortcut": ""}, {"label": "Feeling depressed or new changes in mood", "value": "low_mood", "shortcut": ""}], "inputType": "checkbox", "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = _.some(data.reason_thyroid_testing, Boolean) && !data.no_reason_thyroid_testing"}, {"key": "no_contraindications_thyroid", "type": "checkbox", "input": true, "label": "None of the above", "customClass": "mt-n3", "defaultValue": false, "validate": {"custom": "valid = !!data.no_contraindications_thyroid || !!_.some(_.values(data.contraindications_thyroid));"}, "errors": {"custom": "Please select at least one symptom or 'None of the above'."}, "customConditional": "show = _.some(data.reason_thyroid_testing, Boolean) && !data.no_reason_thyroid_testing"}, {"key": "no_indications", "type": "textfield", "input": true, "label": "No Indication", "hidden": true, "disabled": true, "multiple": false, "clearOnHide": false, "defaultValue": true, "calculateValue": "const s = data.reason_thyroid_testing || {}; const onlyAuto = Object.keys(s).length === 1 && s.autoimmune_comorbidity === true; const autoimmuneInvalid = data.autoimmune_condition_type === 'none'; const noOtherReason = !_.some(s, (v,k) => k !== 'autoimmune_comorbidity' && v); const symptomOnly = Object.keys(s).length === 1 && s.have_symptoms === true; const symptomNone = data.no_thyroid_symptoms === true; value = data.no_reason_thyroid_testing === true || (onlyAuto && autoimmuneInvalid) || (s.autoimmune_comorbidity && autoimmuneInvalid && noOtherReason) || (symptomOnly && symptomNone);"}, {"key": "contraindication_keys", "type": "textfield", "input": true, "label": "Contraindication keys", "hidden": true, "disabled": true, "multiple": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "const redFlags = (_.get(data, 'contraindications_thyroid.none_of_the_above') === true) ? [] : (_.some(data.contraindications_thyroid, Boolean) ? ['red_flag_symptoms'] : []); const shortDuration = data.reason_thyroid_testing && data.reason_thyroid_testing.have_symptoms && data.thyroid_symptom_duration === '< 12 weeks' ? ['recent_symptoms'] : []; const symptomMismatch = data.reason_thyroid_testing && data.reason_thyroid_testing.have_symptoms && data.no_thyroid_symptoms === true ? ['symptom_selected_but_none_chosen'] : []; value = redFlags.concat(shortDuration, symptomMismatch);"}, {"key": "rejected_html", "type": "content", "input": true, "tableView": false, "clearOnHide": false, "defaultValue": "<h3 class='text-red'>You're not currently eligible for thyroid testing through our service.</h3></br><p>Based on your responses, one of the following may apply:</p><ul><li>You do not have a clear medical reason for thyroid testing at this time</li><li>You don't have any specific signs of hypothyroidism</li><li>You may have symptoms that require in-person evaluation</li></ul><p>If you're experiencing symptoms such as heart palpitations, unexplained weight loss, or persistent low mood, please consult your family physician, a walk-in clinic, or an emergency department for further assessment.</p>"}, {"key": "approved_html", "type": "content", "input": true, "tableView": false, "clearOnHide": false, "defaultValue": "<h3 class='text-green'>You're eligible to discuss hypothyroidism testing and management.</h3></br><p>Hypothyroidism is a common condition that can cause symptoms like:</p><ul><li>Fatigue or low energy</li><li>Weight gain without changes in diet</li><li>Dry skin, constipation, or feeling cold easily</li><li>Depression or low mood</li><li>Heavy or irregular periods</li></ul><p>It's usually caused by an underactive thyroid gland and is easily diagnosed with a simple blood test (TSH). Treatment typically involves a daily medication (levothyroxine) to restore your thyroid hormone levels.</p><p>Our team will review your symptoms and determine if a TSH or full thyroid panel is appropriate. We're here to help you feel your best with safe, evidence-based care.</p>"}]}