{% load template_extras %}
{% with rxts=questionnaire.rxts.all rxks=questionnaire.rxt_keys %}
<p>Hi {{ patient.name }},</p>
<p>
    This is {{ doctor.name }} (CPSO #{{ doctor.cpso_number }}) from our clinic. 
    If you have questions about your responses or feel your answers missed any symptoms or conditions, 
    we can arrange for secure real-time messaging. Otherwise, we can proceed with the plan below, but I need you to confirm the following from your medical history.
</p>

<!-- =================  MEDICAL SUMMARY  ================= -->
{% with data=questionnaire.hpc.data summary=questionnaire.raw_formio_summary %}
<h5>Medical History Summary</h5>
<ul>
  {# Indication for treatment #}
  {% with ind_list=summary|confirm:"treatment_indication,other_treatment_indication" %}
  {% if ind_list %}
    <li><strong>Reason for treatment</strong>
      <ul>
        {% for qa in ind_list %}
          <li>{{ qa|replace:": |: <strong>"|safe }}</strong></li>
        {% endfor %}
      </ul>
    </li>
  {% endif %}
  {% endwith %}

  {# Cold sores (oral HSV) #}
  {% if data.treatment_indication and data.treatment_indication.cold_sores_treatment %}
    {% with oral_list=summary|confirm:"sores_present,skin_change_locations,sensations_before,sores_appearance,sores_appearance_other,cold_sores_physician_confirmation,cold_sores_age_diagnosis,cold_sores_past_medication,cold_sores_outbreak_frequency,cold_sores_swab_confirmation,cold_sores_serotype_confirmation,does_rash_feel_typical,symptoms_start_timeframe" %}
    {% if oral_list %}
      <li><strong>Cold sores (oral HSV)</strong>
        <ul>
          {% for qa in oral_list %}
            <li>{{ qa|replace:": |: <strong>"|safe }}</strong></li>
          {% endfor %}
        </ul>
      </li>
    {% endif %}
    {% endwith %}
  {% endif %}

  {# Genital herpes #}
  {% if data.treatment_indication and data.treatment_indication.genital_herpes_treatment %}
    {% with gen_list=summary|confirm:"genital_sores_present,genital_sores_sensations,genital_sores_locations,genital_sores_appearance,genital_sores_appearance_other,genital_sores_duration,genital_sores_triggers,genital_herpes_physician_confirmation,genital_herpes_outbreak_frequency,genital_sores_swab_confirmation,does_rash_feel_typical,symptoms_start_timeframe" %}
    {% if gen_list %}
      <li><strong>Genital herpes</strong>
        <ul>
          {% for qa in gen_list %}
            <li>{{ qa|replace:": |: <strong>"|safe }}</strong></li>
          {% endfor %}
        </ul>
      </li>
    {% endif %}
    {% endwith %}
  {% endif %}

{# Suspected herpes rash #}
{% if data.treatment_indication and data.treatment_indication.suspects_herpes %}
  {% with rash_list=summary|confirm:"rash_present,rash_locations,rash_description,rash_description_other,rash_duration,rash_triggers,rash_physician_confirmation,rash_age_symptom_onset,rash_outbreak_frequency,rash_swab_confirmation,rash_serotype_confirmation" %}
  {% if rash_list %}
    <li><strong>Suspected herpes rash</strong>
      <ul>
        {% for qa in rash_list %}
          <li>{{ qa|safe }}</li>
        {% endfor %}
      </ul>
    </li>
  {% endif %}
  {% endwith %}
{% endif %}

{# Meds & side effects #}
{% with meds_list=summary|confirm:"past_medications_used,medication_side_effects" %}
{% if meds_list %}
  <li><strong>Past medications & side effects</strong>
    <ul>
      {% for qa in meds_list %}
        <li>{{ qa|safe }}</li>
      {% endfor %}
    </ul>
  </li>
{% endif %}
{% endwith %}
</ul>
{% endwith %}

<!-- ================  HSV MEDICATION PREFERENCES SUMMARY  ================ -->
{% with summary=questionnaire.raw_formio_summary %}
<h5>Medication Preferences Summary</h5>
<ul>
 
    {# Site for treatment #}
{% with site_list=summary|confirm:"medication_site_confirmation" %}
{% if site_list %}
  <li><strong>Site for treatment</strong>
    <ul>
      {% for qa in site_list %}
        {% if ":" in qa %}
          <li>{% autoescape off %}{{ qa|replace:": |: <strong>" }}{% endautoescape %}</strong></li>
        {% else %}
          <li>{{ qa|safe }}</li>
        {% endif %}
      {% endfor %}
    </ul>
  </li>
{% endif %}
{% endwith %}

{# Therapy type #}
{% with tt_list=summary|confirm:"therapy_type_intro" %}
{% if tt_list %}
  <li><strong>Therapy type</strong>
    <ul>
      {% for qa in tt_list %}
        {% if ":" in qa %}
          <li>{% autoescape off %}{{ qa|replace:": |: <strong>" }}{% endautoescape %}</strong></li>
        {% else %}
          <li>{{ qa|safe }}</li>
        {% endif %}
      {% endfor %}
    </ul>
  </li>
{% endif %}
{% endwith %}

{# Daily suppressive therapy confirmations/reasons #}
{% with sup_daily_list=summary|confirm:"confirm_daily_dose,daily_dose_reason,daily_dose_reason_other" %}
{% if sup_daily_list %}
  <li><strong>Daily therapy confirmation & reason(s)</strong>
    <ul>
      {% for qa in sup_daily_list %}
        {% if ":" in qa %}
          <li>{% autoescape off %}{{ qa|replace:": |: <strong>" }}{% endautoescape %}</strong></li>
        {% else %}
          <li>{{ qa|safe }}</li>
        {% endif %}
      {% endfor %}
    </ul>
  </li>
{% endif %}
{% endwith %}

{# Suppressive therapy details #}
{% with sup_details=summary|confirm:"suppressive_therapy_confirmation,suppressive_therapy_past,suppressive_dosing" %}
{% if sup_details %}
  <li><strong>Suppressive therapy details</strong>
    <ul>
      {% for qa in sup_details %}
        {% if "Selected dosing for suppressive therapy:" in qa %}
          <li>
            Selected dosing for suppressive therapy:
            <strong>{{ qa|cut:"Selected dosing for suppressive therapy: " }}</strong>
          </li>
        {% elif ":" in qa %}
          <li>{% autoescape off %}{{ qa|replace:": |: <strong>" }}{% endautoescape %}</strong></li>
        {% else %}
          <li>{{ qa|safe }}</li>
        {% endif %}
      {% endfor %}
    </ul>
  </li>
{% endif %}
{% endwith %}

{# On-demand regimens: Genital/Body #}
{% with od_genital=summary|confirm:"on_demand_dosing_genital" %}
{% if od_genital %}
  <li><strong>On-demand regimen (genital/body)</strong>
    <ul>
      {% for qa in od_genital %}
        {% if "Select the medication and dosing for genital herpes treatment:" in qa %}
          <li>
            Select the medication and dosing for genital herpes treatment:
            <strong>{{ qa|cut:"Select the medication and dosing for genital herpes treatment: " }}</strong>
          </li>
        {% elif ":" in qa %}
          <li>{% autoescape off %}{{ qa|replace:": |: <strong>" }}{% endautoescape %}</strong></li>
        {% else %}
          <li>{{ qa|safe }}</li>
        {% endif %}
      {% endfor %}
    </ul>
  </li>
{% endif %}
{% endwith %}

{# On-demand regimens: Cold sores #}
{% with od_oral=summary|confirm:"on_demand_dosing_cold_sores" %}
{% if od_oral %}
  <li><strong>On-demand regimen (cold sores)</strong>
    <ul>
      {% for qa in od_oral %}
        {% if "Select the medication and dosing for cold sore treatment:" in qa %}
          <li>
            Select the medication and dosing for cold sore treatment:
            <strong>{{ qa|cut:"Select the medication and dosing for cold sore treatment: " }}</strong>
          </li>
        {% elif ":" in qa %}
          <li>{% autoescape off %}{{ qa|replace:": |: <strong>" }}{% endautoescape %}</strong></li>
        {% else %}
          <li>{{ qa|safe }}</li>
        {% endif %}
      {% endfor %}
    </ul>
  </li>
{% endif %}
{% endwith %}

{# Reduced kidney function #}
{% with renal_list=summary|confirm:"reduced_kidney_function" %}
{% if renal_list %}
  <li><strong>Kidney Issues</strong>
    <ul>
      {% for qa in renal_list %}
        {% if ":" in qa %}
          <li>{% autoescape off %}{{ qa|replace:": |: <strong>" }}{% endautoescape %}</strong></li>
        {% else %}
          <li>{{ qa|safe }}</li>
        {% endif %}
      {% endfor %}
    </ul>
  </li>
{% endif %}
{% endwith %}

{# Computed selection set (Rx template keys) #}
{% with rx_set=summary|confirm:"rxts" %}
{% if rx_set %}
  <li><strong>Selected regimen key(s)</strong>
    <ul>
      {% for qa in rx_set %}
        {% if ":" in qa %}
          <li>{% autoescape off %}{{ qa|replace:": |: <strong>" }}{% endautoescape %}</strong></li>
        {% else %}
          <li>{{ qa|safe }}</li>
        {% endif %}
      {% endfor %}
    </ul>
  </li>
{% endif %}
{% endwith %}

</ul>
{% endwith %}

<!-- Plan Section -->
<h5>Plan</h5>

<!-- rxts: prescription -->
{% if rxts %}
<p>Based on your provided medical history you qualify for anti-viral medication with:</p>
<ul>
    {% for rx in rxts %}
    <li>{{rx.display_name}}</li>
    {% endfor %}
</ul>
{% else %}
<p>No prescriptions have been issued at this time.</p>
{% endif %}

<!-- Information Section -->
<ol>
    <li><strong>Medication Information:</strong>
        <ul>
            <li><strong>Duration of Use:</strong>
                <ul>
                    <li>You can choose to stop your medication at any time.</li>
                </ul>
            </li>
            <li><strong>Administration Tips:</strong>
                <ul>
                    <li>Your medication is most effective in treating an outbreak if it is taken at the onset of your 'prodrome'.</li>
                    <li>A prodrome is the set of symptoms that show up before a blister forms - sometimes a burning, tingling, or itchiness sensation.</li>
                </ul>
            </li>
        </ul>
    </li>
   <li><strong>Prescription Process:</strong>
  <ul>
    {% if rxts %}
      {% for rx in rxts %}
        {% if "Suppressive" in rx.display_name %}
          <li>For daily suppressive therapy, your prescription will include enough medication for 6 months (e.g., 90 days with 1 refill).</li>
        {% else %}
          <li>For episodic treatment, your prescription will include enough doses for multiple episodes (e.g., 2 episodes per fill, with 5 repeats for a total of 12 episodes).</li>
        {% endif %}
      {% endfor %}
    {% endif %}
    <li>The prescription will be faxed to your chosen pharmacy. Most pharmacies process faxes within 30 minutes. You may call ahead to confirm.</li>
  </ul>
</li>
    <li><strong>Monitoring:</strong>
        <ul>
            <li>Monitor the frequency of outbreaks to assess the effectiveness of treatment. Consider switching between episodic and suppressive therapy based on symptoms and lifestyle needs.</li>
        </ul>
    </li>
</ol>
<p class="mt-3">
    <strong>Confirmation:</strong>
    By completing and submitting this intake, you confirm that the information provided is accurate to the best of your knowledge, <u>that you have read and understand all advice contained in the intake form</u>, and that you agree to seek emergency or in-person medical care if severe side-effects or new symptoms develop.
  </p>
<p>Best regards,<br>{{ doctor.name }}</p>
{% endwith %}