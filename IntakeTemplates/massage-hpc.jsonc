{"components": [{"key": "intro", "type": "content", "input": false, "html": "<h2>Massage Prescription Intake Questionnaire</h2><p>Please complete the following questions.</p>", "label": "Introduction", "tableView": false}, {"key": "section1_header", "type": "content", "input": false, "html": "<h3>Reason for Consultation</h3>", "label": "Section 1 Header", "tableView": false}, {"key": "reason_for_consultation", "type": "selectboxes", "input": true, "label": "Why are you seeking a massage prescription?", "values": [{"label": "General muscle tension/stiffness", "value": "muscle_tension", "shortcut": ""}, {"label": "Chronic pain management", "value": "chronic_pain", "shortcut": ""}, {"label": "Recovery from injury", "value": "recovery_injury", "shortcut": ""}, {"label": "Stress relief", "value": "stress_relief", "shortcut": ""}, {"label": "Other", "value": "other", "shortcut": ""}], "tableView": true, "validate": {"required": true}, "optionsLabelPosition": "right"}, {"key": "reason_for_consultation_other_text", "type": "textfield", "input": true, "label": "Please specify your reason:", "placeholder": "Enter your reason", "tableView": true, "customConditional": "show = data.reason_for_consultation && data.reason_for_consultation.other;", "validate": {"required": true}}, {"key": "current_concern_history_header", "type": "content", "input": false, "html": "<h3>Your Current Health Concern</h3>", "label": "Current Health Concern", "tableView": false}, {"key": "primary_discomfort_area", "type": "selectboxes", "input": true, "label": "Where is your primary area of discomfort?", "values": [{"label": "Neck", "value": "neck", "shortcut": ""}, {"label": "Shoulder", "value": "shoulder", "shortcut": ""}, {"label": "Upper back", "value": "upper_back", "shortcut": ""}, {"label": "Lower back", "value": "lower_back", "shortcut": ""}, {"label": "Hip", "value": "hip", "shortcut": ""}, {"label": "Knee", "value": "knee", "shortcut": ""}, {"label": "Leg", "value": "leg", "shortcut": ""}, {"label": "Arm", "value": "arm", "shortcut": ""}, {"label": "Hand/wrist", "value": "hand_wrist", "shortcut": ""}, {"label": "Other", "value": "other_area", "shortcut": ""}], "tableView": true, "validate": {"required": true}, "optionsLabelPosition": "right"}, {"key": "primary_discomfort_area_other_text", "type": "textfield", "input": true, "label": "Please describe the specific area:", "placeholder": "Enter the specific area", "tableView": true, "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.other_area;", "validate": {"required": true}}, {"key": "neck_details_heading", "type": "content", "input": false, "html": "<h3>Neck Discomfort Details</h3>", "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.neck;", "tableView": false}, {"key": "neck_duration", "type": "select", "input": true, "label": "How long have you experienced neck discomfort?", "placeholder": "Select duration", "data": {"values": [{"label": "< 1 day", "value": "lt1_day"}, {"label": "1-3 days", "value": "1_3_days"}, {"label": "4-7 days", "value": "4_7_days"}, {"label": "8-14 days", "value": "8_14_days"}, {"label": "15-40 days", "value": "15_40_days"}, {"label": "1-3 months", "value": "1_3_months"}, {"label": "3-6 months", "value": "3_6_months"}, {"label": "6-12 months", "value": "6_12_months"}, {"label": "1-3 years", "value": "1_3_years"}, {"label": "3+ years", "value": "3plus_years"}]}, "widget": "html5", "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.neck;", "tableView": true, "validate": {"required": true}}, {"key": "neck_pattern", "type": "radio", "input": true, "label": "Is your neck discomfort constant or does it come and go?", "values": [{"label": "Constant", "value": "constant"}, {"label": "Intermittent", "value": "intermittent"}, {"label": "Not sure", "value": "not_sure"}], "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.neck;", "tableView": true, "validate": {"required": true}}, {"key": "neck_radiation", "type": "radio", "input": true, "label": "Does your discomfort spread from your neck to your shoulders or arms?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "not_sure"}], "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.neck;", "tableView": true, "validate": {"required": true}}, {"key": "neck_trigger_activities", "type": "selectboxes", "input": true, "label": "Which activities or movements tend to trigger your neck discomfort?", "values": [{"label": "Prolonged sitting", "value": "prolonged_sitting", "shortcut": ""}, {"label": "Poor posture", "value": "poor_posture", "shortcut": ""}, {"label": "Stress or tension", "value": "stress", "shortcut": ""}, {"label": "Overhead activities", "value": "overhead", "shortcut": ""}, {"label": "Repetitive motions", "value": "repetitive", "shortcut": ""}, {"label": "Other", "value": "other_trigger", "shortcut": ""}], "tableView": true, "validate": {"required": true}, "optionsLabelPosition": "right", "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.neck;"}, {"key": "neck_relieving_activities", "type": "selectboxes", "input": true, "label": "Which actions or positions help relieve your neck discomfort?", "values": [{"label": "Resting", "value": "resting", "shortcut": ""}, {"label": "Changing posture", "value": "changing_posture", "shortcut": ""}, {"label": "Applying heat or cold", "value": "heat_cold", "shortcut": ""}, {"label": "Gentle stretching", "value": "stretching", "shortcut": ""}, {"label": "Other", "value": "other_relieving", "shortcut": ""}], "tableView": true, "validate": {"required": true}, "optionsLabelPosition": "right", "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.neck;"}, {"key": "neck_numbness", "type": "radio", "input": true, "label": "Do you experience any numbness, tingling, or weakness in your neck, shoulders, or arms?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "not_sure"}], "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.neck;", "tableView": true, "validate": {"required": true}}, {"key": "neck_changes", "type": "radio", "input": true, "label": "Have you noticed any swelling or changes in the appearance of your neck?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "not_sure"}], "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.neck;", "tableView": true, "validate": {"required": true}}, {"key": "neck_side", "type": "radio", "input": true, "label": "Is the discomfort affecting one side of your neck or both?", "values": [{"label": "One side", "value": "one_side"}, {"label": "Both sides", "value": "both_sides"}, {"label": "Not sure", "value": "not_sure"}], "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.neck;", "tableView": true, "validate": {"required": true}}, {"key": "neck_injury_history", "type": "selectboxes", "input": true, "label": "Have you had any recent injuries or incidents that might be related to your neck discomfort?", "values": [{"label": "No, I have not had any injuries", "value": "no_injuries", "shortcut": ""}, {"label": "Minor strain or sprain", "value": "minor_strain_sprain", "shortcut": ""}, {"label": "Whiplash from a fall or accident", "value": "whiplash", "shortcut": ""}, {"label": "Sports-related injury", "value": "sports_injury", "shortcut": ""}, {"label": "Accident (e.g., car accident)", "value": "accident", "shortcut": ""}, {"label": "Other injury or incident", "value": "other_injury", "shortcut": ""}], "tableView": true, "validate": {"required": true}, "optionsLabelPosition": "right", "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.neck;"}, {"key": "neck_activity_effects", "type": "select", "input": true, "label": "How does your neck discomfort change with activity?", "placeholder": "Select an option", "data": {"values": [{"label": "Worsens with prolonged sitting", "value": "worsens_sitting"}, {"label": "Worsens with repetitive movements", "value": "worsens_repetitive"}, {"label": "Worsens with stress or tension", "value": "worsens_stress"}, {"label": "No significant change", "value": "no_change"}]}, "widget": "html5", "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.neck;", "tableView": true, "validate": {"required": true}}, {"key": "shoulder_details_heading", "type": "content", "input": false, "html": "<h3>Shoulder Discomfort Details</h3>", "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.shoulder;", "tableView": false}, {"key": "shoulder_duration", "type": "select", "input": true, "label": "How long have you experienced shoulder discomfort?", "placeholder": "Select duration", "data": {"values": [{"label": "< 1 day", "value": "lt1_day"}, {"label": "1-3 days", "value": "1_3_days"}, {"label": "4-7 days", "value": "4_7_days"}, {"label": "8-14 days", "value": "8_14_days"}, {"label": "15-40 days", "value": "15_40_days"}, {"label": "1-3 months", "value": "1_3_months"}, {"label": "3-6 months", "value": "3_6_months"}, {"label": "6-12 months", "value": "6_12_months"}, {"label": "1-3 years", "value": "1_3_years"}, {"label": "3+ years", "value": "3plus_years"}]}, "widget": "html5", "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.shoulder;", "tableView": true, "validate": {"required": true}}, {"key": "shoulder_pattern", "type": "radio", "input": true, "label": "Is your shoulder discomfort constant or does it come and go?", "values": [{"label": "Constant", "value": "constant"}, {"label": "Intermittent", "value": "intermittent"}, {"label": "Not sure", "value": "not_sure"}], "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.shoulder;", "tableView": true, "validate": {"required": true}}, {"key": "shoulder_radiation", "type": "radio", "input": true, "label": "Does your discomfort spread from your shoulder to your upper arm or neck?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "not_sure"}], "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.shoulder;", "tableView": true, "validate": {"required": true}}, {"key": "shoulder_trigger_activities", "type": "selectboxes", "input": true, "label": "Which activities or movements tend to trigger your shoulder discomfort?", "values": [{"label": "Reaching overhead", "value": "overhead", "shortcut": ""}, {"label": "Lifting objects", "value": "lifting", "shortcut": ""}, {"label": "Carrying heavy items", "value": "carrying", "shortcut": ""}, {"label": "Repetitive motions", "value": "repetitive", "shortcut": ""}, {"label": "Prolonged sitting", "value": "prolonged_sitting", "shortcut": ""}, {"label": "Stress or tension", "value": "stress", "shortcut": ""}, {"label": "Other", "value": "other_trigger", "shortcut": ""}], "tableView": true, "validate": {"required": true}, "optionsLabelPosition": "right", "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.shoulder;"}, {"key": "shoulder_relieving_activities", "type": "selectboxes", "input": true, "label": "Which actions or positions help relieve your shoulder discomfort?", "values": [{"label": "Resting", "value": "resting", "shortcut": ""}, {"label": "Changing posture", "value": "changing_posture", "shortcut": ""}, {"label": "Applying heat or cold", "value": "heat_cold", "shortcut": ""}, {"label": "Gentle stretching", "value": "stretching", "shortcut": ""}, {"label": "Over-the-counter pain relief", "value": "otc_pain", "shortcut": ""}, {"label": "Other", "value": "other_relieving", "shortcut": ""}], "tableView": true, "validate": {"required": true}, "optionsLabelPosition": "right", "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.shoulder;"}, {"key": "shoulder_numbness", "type": "radio", "input": true, "label": "Do you experience any numbness, tingling, or weakness in your shoulder or arm?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "not_sure"}], "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.shoulder;", "tableView": true, "validate": {"required": true}}, {"key": "shoulder_changes", "type": "radio", "input": true, "label": "Have you noticed any swelling or changes in the appearance of your shoulder?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "not_sure"}], "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.shoulder;", "tableView": true, "validate": {"required": true}}, {"key": "shoulder_side", "type": "radio", "input": true, "label": "Is the discomfort affecting one shoulder or both?", "values": [{"label": "One shoulder", "value": "one_shoulder"}, {"label": "Both shoulders", "value": "both_shoulders"}, {"label": "Not sure", "value": "not_sure"}], "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.shoulder;", "tableView": true, "validate": {"required": true}}, {"key": "shoulder_injury_history", "type": "selectboxes", "input": true, "label": "Have you had any recent injuries or incidents that might be related to your shoulder discomfort?", "values": [{"label": "No, I have not had any injuries", "value": "no_injuries", "shortcut": ""}, {"label": "Minor strain or sprain", "value": "minor_strain_sprain", "shortcut": ""}, {"label": "Dislocation", "value": "dislocation", "shortcut": ""}, {"label": "Sports-related injury", "value": "sports_injury", "shortcut": ""}, {"label": "Accident (e.g., fall or collision)", "value": "accident", "shortcut": ""}, {"label": "Other injury or incident", "value": "other_injury", "shortcut": ""}], "tableView": true, "validate": {"required": true}, "optionsLabelPosition": "right", "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.shoulder;"}, {"key": "shoulder_activity_effects", "type": "select", "input": true, "label": "How does your shoulder discomfort change with activity?", "placeholder": "Select an option", "data": {"values": [{"label": "Worsens with overhead activities", "value": "worsens_overhead"}, {"label": "Worsens with lifting", "value": "worsens_lifting"}, {"label": "Worsens with reaching", "value": "worsens_reaching"}, {"label": "No significant change", "value": "no_change"}]}, "widget": "html5", "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.shoulder;", "tableView": true, "validate": {"required": true}}, {"key": "leg_details_heading", "type": "content", "input": false, "html": "<h3>Leg Discomfort Details</h3>", "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.leg;", "tableView": false}, {"key": "leg_duration", "type": "select", "input": true, "label": "How long have you experienced leg discomfort?", "placeholder": "Select duration", "data": {"values": [{"label": "< 1 day", "value": "lt1_day"}, {"label": "1-3 days", "value": "1_3_days"}, {"label": "4-7 days", "value": "4_7_days"}, {"label": "8-14 days", "value": "8_14_days"}, {"label": "15-40 days", "value": "15_40_days"}, {"label": "1-3 months", "value": "1_3_months"}, {"label": "3-6 months", "value": "3_6_months"}, {"label": "6-12 months", "value": "6_12_months"}, {"label": "1-3 years", "value": "1_3_years"}, {"label": "3+ years", "value": "3plus_years"}]}, "widget": "html5", "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.leg;", "tableView": true, "validate": {"required": true}}, {"key": "leg_pattern", "type": "radio", "input": true, "label": "Is your leg discomfort constant or does it come and go?", "values": [{"label": "Constant", "value": "constant"}, {"label": "Intermittent", "value": "intermittent"}, {"label": "Not sure", "value": "not_sure"}], "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.leg;", "tableView": true, "validate": {"required": true}}, {"key": "leg_radiation", "type": "radio", "input": true, "label": "Does your discomfort travel from your lower back down your leg or into your foot?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "not_sure"}], "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.leg;", "tableView": true, "validate": {"required": true}}, {"key": "leg_trigger_activities", "type": "selectboxes", "input": true, "label": "Which activities or movements tend to trigger your leg discomfort?", "values": [{"label": "Walking", "value": "walking", "shortcut": ""}, {"label": "Running", "value": "running", "shortcut": ""}, {"label": "Standing for long periods", "value": "standing", "shortcut": ""}, {"label": "Sitting for long periods", "value": "sitting", "shortcut": ""}, {"label": "Bending or twisting", "value": "bending", "shortcut": ""}, {"label": "Climbing stairs", "value": "stair_climbing", "shortcut": ""}, {"label": "Lifting heavy objects", "value": "lifting", "shortcut": ""}, {"label": "Other", "value": "other_trigger", "shortcut": ""}], "tableView": true, "validate": {"required": true}, "optionsLabelPosition": "right", "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.leg;"}, {"key": "leg_relieving_activities", "type": "selectboxes", "input": true, "label": "Which actions or positions help relieve your leg discomfort?", "values": [{"label": "Resting", "value": "resting", "shortcut": ""}, {"label": "Changing posture", "value": "changing_posture", "shortcut": ""}, {"label": "Gentle stretching", "value": "stretching", "shortcut": ""}, {"label": "Applying heat", "value": "heat", "shortcut": ""}, {"label": "Applying cold", "value": "cold", "shortcut": ""}, {"label": "Elevating the leg", "value": "elevating", "shortcut": ""}, {"label": "Other", "value": "other_relieving", "shortcut": ""}], "tableView": true, "validate": {"required": true}, "optionsLabelPosition": "right", "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.leg;"}, {"key": "leg_numbness", "type": "radio", "input": true, "label": "Do you experience any numbness, tingling, or weakness in your leg or foot?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "not_sure"}], "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.leg;", "tableView": true, "validate": {"required": true}}, {"key": "leg_changes", "type": "radio", "input": true, "label": "Have you noticed any swelling or changes in the color of your leg?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "not_sure"}], "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.leg;", "tableView": true, "validate": {"required": true}}, {"key": "leg_side", "type": "radio", "input": true, "label": "Does the discomfort affect one leg or both?", "values": [{"label": "One leg", "value": "one_leg"}, {"label": "Both legs", "value": "both_legs"}, {"label": "Not sure", "value": "not_sure"}], "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.leg;", "tableView": true, "validate": {"required": true}}, {"key": "leg_injury_history", "type": "selectboxes", "input": true, "label": "Have you had any recent injuries or incidents that might be related to your leg discomfort?", "values": [{"label": "No, I have not had any injuries", "value": "no_injuries", "shortcut": ""}, {"label": "Minor sprain or strain", "value": "minor_sprain_strain", "shortcut": ""}, {"label": "Slip or fall", "value": "slip_fall", "shortcut": ""}, {"label": "Sports-related injury", "value": "sports_injury", "shortcut": ""}, {"label": "Accident (e.g., car accident)", "value": "accident", "shortcut": ""}, {"label": "Other injury or incident", "value": "other_injury", "shortcut": ""}], "tableView": true, "validate": {"required": true}, "optionsLabelPosition": "right", "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.leg;"}, {"key": "leg_activity_effects", "type": "select", "input": true, "label": "How does your leg discomfort change with activity?", "placeholder": "Select an option", "data": {"values": [{"label": "Worsens with walking", "value": "worsens_walking"}, {"label": "Worsens with sitting", "value": "worsens_sitting"}, {"label": "Worsens with standing", "value": "worsens_standing"}, {"label": "No significant change", "value": "no_change"}]}, "widget": "html5", "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.leg;", "tableView": true, "validate": {"required": true}}, {"key": "upper_back_details_heading", "type": "content", "input": false, "html": "<h3>Upper Back Discomfort Details</h3>", "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.upper_back;", "tableView": false}, {"key": "upper_back_duration", "type": "select", "input": true, "label": "How long have you experienced upper back discomfort?", "placeholder": "Select duration", "data": {"values": [{"label": "< 1 day", "value": "lt1_day"}, {"label": "1-3 days", "value": "1_3_days"}, {"label": "4-7 days", "value": "4_7_days"}, {"label": "8-14 days", "value": "8_14_days"}, {"label": "15-40 days", "value": "15_40_days"}, {"label": "1-3 months", "value": "1_3_months"}, {"label": "3-6 months", "value": "3_6_months"}, {"label": "6-12 months", "value": "6_12_months"}, {"label": "1-3 years", "value": "1_3_years"}, {"label": "3+ years", "value": "3plus_years"}]}, "widget": "html5", "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.upper_back;", "tableView": true, "validate": {"required": true}}, {"key": "upper_back_pattern", "type": "radio", "input": true, "label": "Is your upper back discomfort constant or does it come and go?", "values": [{"label": "Constant", "value": "constant"}, {"label": "Intermittent", "value": "intermittent"}, {"label": "Not sure", "value": "not_sure"}], "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.upper_back;", "tableView": true, "validate": {"required": true}}, {"key": "upper_back_radiation", "type": "radio", "input": true, "label": "Does your discomfort spread from your upper back to your neck or shoulders?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "not_sure"}], "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.upper_back;", "tableView": true, "validate": {"required": true}}, {"key": "upper_back_trigger_activities", "type": "selectboxes", "input": true, "label": "Which activities or movements tend to trigger your upper back discomfort?", "values": [{"label": "Prolonged sitting", "value": "prolonged_sitting", "shortcut": ""}, {"label": "Poor posture", "value": "poor_posture", "shortcut": ""}, {"label": "Carrying heavy loads", "value": "heavy_loading", "shortcut": ""}, {"label": "Repetitive movements", "value": "repetitive", "shortcut": ""}, {"label": "Stress or tension", "value": "stress", "shortcut": ""}, {"label": "Other", "value": "other_trigger", "shortcut": ""}], "tableView": true, "validate": {"required": true}, "optionsLabelPosition": "right", "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.upper_back;"}, {"key": "upper_back_relieving_activities", "type": "selectboxes", "input": true, "label": "Which actions or positions help relieve your upper back discomfort?", "values": [{"label": "Resting", "value": "resting", "shortcut": ""}, {"label": "Changing posture", "value": "changing_posture", "shortcut": ""}, {"label": "Gentle stretching", "value": "stretching", "shortcut": ""}, {"label": "Applying heat or cold", "value": "heat_cold", "shortcut": ""}, {"label": "Other", "value": "other_relieving", "shortcut": ""}], "tableView": true, "validate": {"required": true}, "optionsLabelPosition": "right", "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.upper_back;"}, {"key": "upper_back_numbness", "type": "radio", "input": true, "label": "Do you experience any numbness, tingling, or weakness in your upper back, neck, or shoulders?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "not_sure"}], "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.upper_back;", "tableView": true, "validate": {"required": true}}, {"key": "upper_back_changes", "type": "radio", "input": true, "label": "Have you noticed any changes in the appearance or feeling of your upper back?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "not_sure"}], "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.upper_back;", "tableView": true, "validate": {"required": true}}, {"key": "upper_back_injury_history", "type": "selectboxes", "input": true, "label": "Have you had any recent injuries or incidents that might be related to your upper back discomfort?", "values": [{"label": "No, I have not had any injuries", "value": "no_injuries", "shortcut": ""}, {"label": "Minor strain or sprain", "value": "minor_strain_sprain", "shortcut": ""}, {"label": "Whiplash or similar injury", "value": "whiplash", "shortcut": ""}, {"label": "Sports-related injury", "value": "sports_injury", "shortcut": ""}, {"label": "Accident (e.g., fall or collision)", "value": "accident", "shortcut": ""}, {"label": "Other injury or incident", "value": "other_injury", "shortcut": ""}], "tableView": true, "validate": {"required": true}, "optionsLabelPosition": "right", "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.upper_back;"}, {"key": "upper_back_activity_effects", "type": "select", "input": true, "label": "How does your upper back discomfort change with activity?", "placeholder": "Select an option", "data": {"values": [{"label": "Worsens with prolonged sitting", "value": "worsens_sitting"}, {"label": "Worsens with lifting", "value": "worsens_lifting"}, {"label": "Worsens with repetitive movements", "value": "worsens_repetitive"}, {"label": "No significant change", "value": "no_change"}]}, "widget": "html5", "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.upper_back;", "tableView": true, "validate": {"required": true}}, {"key": "lower_back_details_heading", "type": "content", "input": false, "html": "<h3>Lower Back Discomfort Details</h3>", "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.lower_back;", "tableView": false}, {"key": "lower_back_duration", "type": "select", "input": true, "label": "How long have you experienced lower back discomfort?", "placeholder": "Select duration", "data": {"values": [{"label": "< 1 day", "value": "lt1_day"}, {"label": "1-3 days", "value": "1_3_days"}, {"label": "4-7 days", "value": "4_7_days"}, {"label": "8-14 days", "value": "8_14_days"}, {"label": "15-40 days", "value": "15_40_days"}, {"label": "1-3 months", "value": "1_3_months"}, {"label": "3-6 months", "value": "3_6_months"}, {"label": "6-12 months", "value": "6_12_months"}, {"label": "1-3 years", "value": "1_3_years"}, {"label": "3+ years", "value": "3plus_years"}]}, "widget": "html5", "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.lower_back;", "tableView": true, "validate": {"required": true}}, {"key": "lower_back_pattern", "type": "radio", "input": true, "label": "Is your lower back discomfort constant or does it come and go?", "values": [{"label": "Constant", "value": "constant"}, {"label": "Intermittent", "value": "intermittent"}, {"label": "Not sure", "value": "not_sure"}], "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.lower_back;", "tableView": true, "validate": {"required": true}}, {"key": "lower_back_radiation", "type": "radio", "input": true, "label": "Does your discomfort spread from your lower back to your buttocks or legs?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "not_sure"}], "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.lower_back;", "tableView": true, "validate": {"required": true}}, {"key": "lower_back_trigger_activities", "type": "selectboxes", "input": true, "label": "Which activities or movements tend to trigger your lower back discomfort?", "values": [{"label": "Prolonged sitting", "value": "prolonged_sitting", "shortcut": ""}, {"label": "Heavy lifting", "value": "heavy_lifting", "shortcut": ""}, {"label": "Bending or twisting", "value": "bending_twisting", "shortcut": ""}, {"label": "Standing for long periods", "value": "prolonged_standing", "shortcut": ""}, {"label": "Repetitive movements", "value": "repetitive", "shortcut": ""}, {"label": "Other", "value": "other_trigger", "shortcut": ""}], "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.lower_back;", "tableView": true, "validate": {"required": true}, "optionsLabelPosition": "right"}, {"key": "lower_back_relieving_activities", "type": "selectboxes", "input": true, "label": "Which actions or positions help relieve your lower back discomfort?", "values": [{"label": "Resting", "value": "resting", "shortcut": ""}, {"label": "Changing posture", "value": "changing_posture", "shortcut": ""}, {"label": "Gentle stretching", "value": "stretching", "shortcut": ""}, {"label": "Applying heat or cold", "value": "heat_cold", "shortcut": ""}, {"label": "Using a supportive device (e.g., brace)", "value": "supportive_device", "shortcut": ""}, {"label": "Other", "value": "other_relieving", "shortcut": ""}], "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.lower_back;", "tableView": true, "validate": {"required": true}, "optionsLabelPosition": "right"}, {"key": "lower_back_numbness", "type": "radio", "input": true, "label": "Do you experience any numbness, tingling, or weakness in your lower back or legs?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "not_sure"}], "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.lower_back;", "tableView": true, "validate": {"required": true}}, {"key": "lower_back_changes", "type": "radio", "input": true, "label": "Have you noticed any swelling or changes in the appearance of your lower back?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "not_sure"}], "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.lower_back;", "tableView": true, "validate": {"required": true}}, {"key": "lower_back_injury_history", "type": "selectboxes", "input": true, "label": "Have you had any recent injuries or incidents that might be related to your lower back discomfort?", "values": [{"label": "No, I have not had any injuries", "value": "no_injuries", "shortcut": ""}, {"label": "Minor strain or sprain", "value": "minor_strain_sprain", "shortcut": ""}, {"label": "Slip or fall", "value": "slip_fall", "shortcut": ""}, {"label": "Sports-related injury", "value": "sports_injury", "shortcut": ""}, {"label": "Accident (e.g., car accident)", "value": "accident", "shortcut": ""}, {"label": "Other injury or incident", "value": "other_injury", "shortcut": ""}], "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.lower_back;", "tableView": true, "validate": {"required": true}, "optionsLabelPosition": "right"}, {"key": "lower_back_activity_effects", "type": "select", "input": true, "label": "How does your lower back discomfort change with activity?", "placeholder": "Select an option", "data": {"values": [{"label": "Worsens with sitting", "value": "worsens_sitting"}, {"label": "Worsens with standing", "value": "worsens_standing"}, {"label": "Worsens with bending or twisting", "value": "worsens_bending"}, {"label": "No significant change", "value": "no_change"}]}, "widget": "html5", "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.lower_back;", "tableView": true, "validate": {"required": true}}, {"key": "hip_details_heading", "type": "content", "input": false, "html": "<h3>Hip Discomfort Details</h3>", "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.hip;", "tableView": false}, {"key": "hip_duration", "type": "select", "input": true, "label": "How long have you experienced hip discomfort?", "placeholder": "Select duration", "data": {"values": [{"label": "< 1 day", "value": "lt1_day"}, {"label": "1-3 days", "value": "1_3_days"}, {"label": "4-7 days", "value": "4_7_days"}, {"label": "8-14 days", "value": "8_14_days"}, {"label": "15-40 days", "value": "15_40_days"}, {"label": "1-3 months", "value": "1_3_months"}, {"label": "3-6 months", "value": "3_6_months"}, {"label": "6-12 months", "value": "6_12_months"}, {"label": "1-3 years", "value": "1_3_years"}, {"label": "3+ years", "value": "3plus_years"}]}, "widget": "html5", "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.hip;", "tableView": true, "validate": {"required": true}}, {"key": "hip_pattern", "type": "radio", "input": true, "label": "Is your hip discomfort constant or does it come and go?", "values": [{"label": "Constant", "value": "constant"}, {"label": "Intermittent", "value": "intermittent"}, {"label": "Not sure", "value": "not_sure"}], "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.hip;", "tableView": true, "validate": {"required": true}}, {"key": "hip_radiation", "type": "radio", "input": true, "label": "Does your hip discomfort radiate to your groin or thigh?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "not_sure"}], "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.hip;", "tableView": true, "validate": {"required": true}}, {"key": "hip_trigger_activities", "type": "selectboxes", "input": true, "label": "Which activities or movements tend to trigger your hip discomfort?", "values": [{"label": "Walking", "value": "walking", "shortcut": ""}, {"label": "Climbing stairs", "value": "climbing_stairs", "shortcut": ""}, {"label": "Standing for long periods", "value": "standing_long", "shortcut": ""}, {"label": "Lifting or bending", "value": "lifting_bending", "shortcut": ""}, {"label": "Sitting for prolonged periods", "value": "prolonged_sitting", "shortcut": ""}, {"label": "Other", "value": "other_trigger", "shortcut": ""}], "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.hip;", "tableView": true, "validate": {"required": true}, "optionsLabelPosition": "right"}, {"key": "hip_relieving_activities", "type": "selectboxes", "input": true, "label": "Which actions or positions help relieve your hip discomfort?", "values": [{"label": "Resting", "value": "resting", "shortcut": ""}, {"label": "Changing posture", "value": "changing_posture", "shortcut": ""}, {"label": "Gentle stretching", "value": "stretching", "shortcut": ""}, {"label": "Applying heat", "value": "heat", "shortcut": ""}, {"label": "Using a cushion or support", "value": "support", "shortcut": ""}, {"label": "Other", "value": "other_relieving", "shortcut": ""}], "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.hip;", "tableView": true, "validate": {"required": true}, "optionsLabelPosition": "right"}, {"key": "hip_numbness", "type": "radio", "input": true, "label": "Do you experience any numbness, tingling, or weakness in your hip or surrounding areas?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "not_sure"}], "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.hip;", "tableView": true, "validate": {"required": true}}, {"key": "hip_changes", "type": "radio", "input": true, "label": "Have you noticed any swelling or changes in the appearance of your hip?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "not_sure"}], "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.hip;", "tableView": true, "validate": {"required": true}}, {"key": "hip_injury_history", "type": "selectboxes", "input": true, "label": "Have you had any recent injuries or incidents that might be related to your hip discomfort?", "values": [{"label": "No, I have not had any injuries", "value": "no_injuries", "shortcut": ""}, {"label": "Minor strain or sprain", "value": "minor_strain_sprain", "shortcut": ""}, {"label": "Fall or slip", "value": "fall_slip", "shortcut": ""}, {"label": "Sports-related injury", "value": "sports_injury", "shortcut": ""}, {"label": "Accident (e.g., car accident)", "value": "accident", "shortcut": ""}, {"label": "Other injury or incident", "value": "other_injury", "shortcut": ""}], "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.hip;", "tableView": true, "validate": {"required": true}, "optionsLabelPosition": "right"}, {"key": "hip_activity_effects", "type": "select", "input": true, "label": "How does your hip discomfort change with activity?", "placeholder": "Select an option", "data": {"values": [{"label": "Worsens with walking", "value": "worsens_walking"}, {"label": "Worsens with prolonged sitting", "value": "worsens_sitting"}, {"label": "Worsens with bending or twisting", "value": "worsens_bending"}, {"label": "No significant change", "value": "no_change"}]}, "widget": "html5", "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.hip;", "tableView": true, "validate": {"required": true}}, {"key": "knee_details_heading", "type": "content", "input": false, "html": "<h3><PERSON><PERSON> Details</h3>", "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.knee;", "tableView": false}, {"key": "knee_duration", "type": "select", "input": true, "label": "How long have you experienced knee discomfort?", "placeholder": "Select duration", "data": {"values": [{"label": "< 1 day", "value": "lt1_day"}, {"label": "1-3 days", "value": "1_3_days"}, {"label": "4-7 days", "value": "4_7_days"}, {"label": "8-14 days", "value": "8_14_days"}, {"label": "15-40 days", "value": "15_40_days"}, {"label": "1-3 months", "value": "1_3_months"}, {"label": "3-6 months", "value": "3_6_months"}, {"label": "6-12 months", "value": "6_12_months"}, {"label": "1-3 years", "value": "1_3_years"}, {"label": "3+ years", "value": "3plus_years"}]}, "widget": "html5", "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.knee;", "tableView": true, "validate": {"required": true}}, {"key": "knee_pattern", "type": "radio", "input": true, "label": "Is your knee discomfort constant or does it come and go?", "values": [{"label": "Constant", "value": "constant"}, {"label": "Intermittent", "value": "intermittent"}, {"label": "Not sure", "value": "not_sure"}], "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.knee;", "tableView": true, "validate": {"required": true}}, {"key": "knee_radiation", "type": "radio", "input": true, "label": "Does your knee discomfort extend to your lower leg or foot?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "not_sure"}], "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.knee;", "tableView": true, "validate": {"required": true}}, {"key": "knee_trigger_activities", "type": "selectboxes", "input": true, "label": "Which activities or movements tend to trigger your knee discomfort?", "values": [{"label": "Walking", "value": "walking", "shortcut": ""}, {"label": "Running", "value": "running", "shortcut": ""}, {"label": "Stair climbing", "value": "stair_climbing", "shortcut": ""}, {"label": "Squatting", "value": "squatting", "shortcut": ""}, {"label": "Bending", "value": "bending", "shortcut": ""}, {"label": "Other", "value": "other_trigger", "shortcut": ""}], "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.knee;", "tableView": true, "validate": {"required": true}, "optionsLabelPosition": "right"}, {"key": "knee_relieving_activities", "type": "selectboxes", "input": true, "label": "Which actions or positions help relieve your knee discomfort?", "values": [{"label": "Resting", "value": "resting", "shortcut": ""}, {"label": "Changing posture", "value": "changing_posture", "shortcut": ""}, {"label": "Gentle stretching", "value": "stretching", "shortcut": ""}, {"label": "Applying heat or cold", "value": "heat_cold", "shortcut": ""}, {"label": "Other", "value": "other_relieving", "shortcut": ""}], "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.knee;", "tableView": true, "validate": {"required": true}, "optionsLabelPosition": "right"}, {"key": "knee_numbness", "type": "radio", "input": true, "label": "Do you experience any numbness, tingling, or weakness in your knee or lower leg?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "not_sure"}], "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.knee;", "tableView": true, "validate": {"required": true}}, {"key": "knee_changes", "type": "radio", "input": true, "label": "Have you noticed any swelling or changes in the appearance of your knee?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "not_sure"}], "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.knee;", "tableView": true, "validate": {"required": true}}, {"key": "knee_injury_history", "type": "selectboxes", "input": true, "label": "Have you had any recent injuries or incidents that might be related to your knee discomfort?", "values": [{"label": "No, I have not had any injuries", "value": "no_injuries", "shortcut": ""}, {"label": "Minor sprain or strain", "value": "minor_sprain_strain", "shortcut": ""}, {"label": "Fall or twist", "value": "fall_twist", "shortcut": ""}, {"label": "Sports-related injury", "value": "sports_injury", "shortcut": ""}, {"label": "Accident (e.g., car accident)", "value": "accident", "shortcut": ""}, {"label": "Other injury or incident", "value": "other_injury", "shortcut": ""}], "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.knee;", "tableView": true, "validate": {"required": true}, "optionsLabelPosition": "right"}, {"key": "knee_activity_effects", "type": "select", "input": true, "label": "How does your knee discomfort change with activity?", "placeholder": "Select an option", "data": {"values": [{"label": "Worsens with running", "value": "worsens_running"}, {"label": "Worsens with prolonged sitting", "value": "worsens_sitting"}, {"label": "Worsens with bending", "value": "worsens_bending"}, {"label": "No significant change", "value": "no_change"}]}, "widget": "html5", "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.knee;", "tableView": true, "validate": {"required": true}}, {"key": "arm_details_heading", "type": "content", "input": false, "html": "<h3>Arm Discomfort Details</h3>", "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.arm;", "tableView": false}, {"key": "arm_duration", "type": "select", "input": true, "label": "How long have you experienced arm discomfort?", "placeholder": "Select duration", "data": {"values": [{"label": "< 1 day", "value": "lt1_day"}, {"label": "1-3 days", "value": "1_3_days"}, {"label": "4-7 days", "value": "4_7_days"}, {"label": "8-14 days", "value": "8_14_days"}, {"label": "15-40 days", "value": "15_40_days"}, {"label": "1-3 months", "value": "1_3_months"}, {"label": "3-6 months", "value": "3_6_months"}, {"label": "6-12 months", "value": "6_12_months"}, {"label": "1-3 years", "value": "1_3_years"}, {"label": "3+ years", "value": "3plus_years"}]}, "widget": "html5", "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.arm;", "tableView": true, "validate": {"required": true}}, {"key": "arm_pattern", "type": "radio", "input": true, "label": "Is your arm discomfort constant or does it come and go?", "values": [{"label": "Constant", "value": "constant"}, {"label": "Intermittent", "value": "intermittent"}, {"label": "Not sure", "value": "not_sure"}], "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.arm;", "tableView": true, "validate": {"required": true}}, {"key": "arm_radiation", "type": "radio", "input": true, "label": "Does your arm discomfort extend to your hand?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "not_sure"}], "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.arm;", "tableView": true, "validate": {"required": true}}, {"key": "arm_trigger_activities", "type": "selectboxes", "input": true, "label": "Which activities or movements tend to trigger your arm discomfort?", "values": [{"label": "Repetitive movements", "value": "repetitive", "shortcut": ""}, {"label": "Lifting objects", "value": "lifting", "shortcut": ""}, {"label": "Overhead activities", "value": "overhead", "shortcut": ""}, {"label": "Sitting for long periods", "value": "sitting", "shortcut": ""}, {"label": "Other", "value": "other_trigger", "shortcut": ""}], "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.arm;", "tableView": true, "validate": {"required": true}, "optionsLabelPosition": "right"}, {"key": "arm_relieving_activities", "type": "selectboxes", "input": true, "label": "Which actions or positions help relieve your arm discomfort?", "values": [{"label": "Resting", "value": "resting", "shortcut": ""}, {"label": "Changing posture", "value": "changing_posture", "shortcut": ""}, {"label": "Gentle stretching", "value": "stretching", "shortcut": ""}, {"label": "Applying heat or cold", "value": "heat_cold", "shortcut": ""}, {"label": "Other", "value": "other_relieving", "shortcut": ""}], "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.arm;", "tableView": true, "validate": {"required": true}, "optionsLabelPosition": "right"}, {"key": "arm_numbness", "type": "radio", "input": true, "label": "Do you experience any numbness, tingling, or weakness in your arm or hand?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "not_sure"}], "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.arm;", "tableView": true, "validate": {"required": true}}, {"key": "arm_changes", "type": "radio", "input": true, "label": "Have you noticed any swelling or changes in the appearance of your arm?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "not_sure"}], "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.arm;", "tableView": true, "validate": {"required": true}}, {"key": "arm_injury_history", "type": "selectboxes", "input": true, "label": "Have you had any recent injuries or incidents that might be related to your arm discomfort?", "values": [{"label": "No, I have not had any injuries", "value": "no_injuries", "shortcut": ""}, {"label": "Minor strain or sprain", "value": "minor_strain_sprain", "shortcut": ""}, {"label": "Fall or impact injury", "value": "fall_impact", "shortcut": ""}, {"label": "Sports-related injury", "value": "sports_injury", "shortcut": ""}, {"label": "Accident (e.g., car accident)", "value": "accident", "shortcut": ""}, {"label": "Other injury or incident", "value": "other_injury", "shortcut": ""}], "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.arm;", "tableView": true, "validate": {"required": true}, "optionsLabelPosition": "right"}, {"key": "arm_activity_effects", "type": "select", "input": true, "label": "How does your arm discomfort change with activity?", "placeholder": "Select an option", "data": {"values": [{"label": "Worsens with repetitive movements", "value": "worsens_repetitive"}, {"label": "Worsens with lifting", "value": "worsens_lifting"}, {"label": "Worsens with overhead activities", "value": "worsens_overhead"}, {"label": "No significant change", "value": "no_change"}]}, "widget": "html5", "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.arm;", "tableView": true, "validate": {"required": true}}, {"key": "hand_wrist_details_heading", "type": "content", "input": false, "html": "<h3>Hand/Wrist Discomfort Details</h3>", "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.hand_wrist;", "tableView": false}, {"key": "hand_wrist_duration", "type": "select", "input": true, "label": "How long have you experienced hand/wrist discomfort?", "placeholder": "Select duration", "data": {"values": [{"label": "< 1 day", "value": "lt1_day"}, {"label": "1-3 days", "value": "1_3_days"}, {"label": "4-7 days", "value": "4_7_days"}, {"label": "8-14 days", "value": "8_14_days"}, {"label": "15-40 days", "value": "15_40_days"}, {"label": "1-3 months", "value": "1_3_months"}, {"label": "3-6 months", "value": "3_6_months"}, {"label": "6-12 months", "value": "6_12_months"}, {"label": "1-3 years", "value": "1_3_years"}, {"label": "3+ years", "value": "3plus_years"}]}, "widget": "html5", "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.hand_wrist;", "tableView": true, "validate": {"required": true}}, {"key": "hand_wrist_pattern", "type": "radio", "input": true, "label": "Is your hand/wrist discomfort constant or does it come and go?", "values": [{"label": "Constant", "value": "constant"}, {"label": "Intermittent", "value": "intermittent"}, {"label": "Not sure", "value": "not_sure"}], "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.hand_wrist;", "tableView": true, "validate": {"required": true}}, {"key": "hand_wrist_radiation", "type": "radio", "input": true, "label": "Does your hand/wrist discomfort extend to your fingers?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "not_sure"}], "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.hand_wrist;", "tableView": true, "validate": {"required": true}}, {"key": "hand_wrist_trigger_activities", "type": "selectboxes", "input": true, "label": "Which activities or movements tend to trigger your hand/wrist discomfort?", "values": [{"label": "Repetitive tasks (e.g., typing)", "value": "repetitive_tasks", "shortcut": ""}, {"label": "Using a mouse", "value": "using_mouse", "shortcut": ""}, {"label": "Lifting objects", "value": "lifting", "shortcut": ""}, {"label": "Prolonged writing", "value": "prolonged_writing", "shortcut": ""}, {"label": "Other", "value": "other_trigger", "shortcut": ""}], "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.hand_wrist;", "tableView": true, "validate": {"required": true}, "optionsLabelPosition": "right"}, {"key": "hand_wrist_relieving_activities", "type": "selectboxes", "input": true, "label": "Which actions or positions help relieve your hand/wrist discomfort?", "values": [{"label": "Resting", "value": "resting", "shortcut": ""}, {"label": "Adjusting workstation ergonomics", "value": "ergonomics", "shortcut": ""}, {"label": "Taking breaks", "value": "taking_breaks", "shortcut": ""}, {"label": "Gentle stretching", "value": "stretching", "shortcut": ""}, {"label": "Other", "value": "other_relieving", "shortcut": ""}], "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.hand_wrist;", "tableView": true, "validate": {"required": true}, "optionsLabelPosition": "right"}, {"key": "hand_wrist_numbness", "type": "radio", "input": true, "label": "Do you experience any numbness, tingling, or weakness in your hand or wrist?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "not_sure"}], "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.hand_wrist;", "tableView": true, "validate": {"required": true}}, {"key": "hand_wrist_changes", "type": "radio", "input": true, "label": "Have you noticed any swelling or changes in the appearance of your hand or wrist?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "not_sure"}], "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.hand_wrist;", "tableView": true, "validate": {"required": true}}, {"key": "hand_wrist_injury_history", "type": "selectboxes", "input": true, "label": "Have you had any recent injuries or incidents that might be related to your hand/wrist discomfort?", "values": [{"label": "No, I have not had any injuries", "value": "no_injuries", "shortcut": ""}, {"label": "Minor strain or sprain", "value": "minor_strain_sprain", "shortcut": ""}, {"label": "Fall or impact injury", "value": "fall_impact", "shortcut": ""}, {"label": "Sports-related injury", "value": "sports_injury", "shortcut": ""}, {"label": "Accident (e.g., car accident)", "value": "accident", "shortcut": ""}, {"label": "Other injury or incident", "value": "other_injury", "shortcut": ""}], "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.hand_wrist;", "tableView": true, "validate": {"required": true}, "optionsLabelPosition": "right"}, {"key": "hand_wrist_activity_effects", "type": "select", "input": true, "label": "How does your hand/wrist discomfort change with activity?", "placeholder": "Select an option", "data": {"values": [{"label": "Worsens with repetitive tasks", "value": "worsens_repetitive"}, {"label": "Worsens with prolonged typing", "value": "worsens_typing"}, {"label": "Worsens with writing", "value": "worsens_writing"}, {"label": "No significant change", "value": "no_change"}]}, "widget": "html5", "customConditional": "show = data.primary_discomfort_area && data.primary_discomfort_area.hand_wrist;", "tableView": true, "validate": {"required": true}}, {"key": "symptom_understanding_header", "type": "content", "input": false, "html": "<h3>Understanding Your Symptoms</h3>", "label": "Symptom Understanding", "tableView": false}, {"key": "discomfort_pattern", "type": "selectboxes", "input": true, "label": "How would you describe the pattern of your discomfort?", "values": [{"label": "Comes and goes (intermittent)", "value": "intermittent", "shortcut": ""}, {"label": "Constant but varies in intensity", "value": "varies_intensity", "shortcut": ""}, {"label": "Constant without any relief", "value": "constant_no_relief", "shortcut": ""}, {"label": "Progressive worsening over time", "value": "progressive", "shortcut": ""}, {"label": "Flares up occasionally with symptom-free periods", "value": "flares_occasional", "shortcut": ""}, {"label": "Triggered by specific activities", "value": "triggered", "shortcut": ""}, {"label": "No clear pattern", "value": "no_clear_pattern", "shortcut": ""}], "tableView": true, "validate": {"required": true}, "optionsLabelPosition": "right"}, {"key": "flare_frequency", "type": "select", "input": true, "label": "How often do the flares occur?", "placeholder": "Select frequency", "data": {"values": [{"label": "Daily", "value": "daily"}, {"label": "Several times a week", "value": "several_week"}, {"label": "Weekly", "value": "weekly"}, {"label": "Monthly", "value": "monthly"}, {"label": "Less than once a month", "value": "less_than_monthly"}]}, "tableView": true, "customConditional": "show = data.discomfort_pattern === 'flares_occasional';", "validate": {"required": true}}, {"key": "flare_duration", "type": "select", "input": true, "label": "When the discomfort flares, how long does it typically last?", "placeholder": "Select duration", "widget": "html5", "data": {"values": [{"label": "Less than 30 minutes", "value": "lt_30"}, {"label": "30 minutes to 2 hours", "value": "30_to_120"}, {"label": "2-4 hours", "value": "2_to_4"}, {"label": "More than 4 hours", "value": "gt_4"}, {"label": "All day", "value": "all_day"}]}, "tableView": true, "customConditional": "show = data.flare_frequency !== undefined;", "validate": {"required": true}}, {"key": "flare_trigger", "type": "select", "input": true, "label": "Is there a noticeable trigger for the symptom flares?", "placeholder": "Select a trigger", "data": {"values": [{"label": "Physical activity or exertion", "value": "physical_activity"}, {"label": "Prolonged sitting or standing", "value": "prolonged_posture"}, {"label": "Stress or emotional triggers", "value": "stress"}, {"label": "Weather changes", "value": "weather"}, {"label": "No specific trigger", "value": "none"}]}, "tableView": true, "customConditional": "show = data.flare_duration !== undefined;", "validate": {"required": true}}, {"key": "physical_activity_trigger", "type": "select", "input": true, "label": "Which type of activity triggers the discomfort?", "placeholder": "Select an activity", "data": {"values": [{"label": "Lifting heavy objects", "value": "lifting"}, {"label": "Running or high‑impact exercise", "value": "running"}, {"label": "Bending or twisting", "value": "bending"}, {"label": "Climbing stairs", "value": "stairs"}, {"label": "Other physical activity", "value": "other_physical"}]}, "tableView": true, "customConditional": "show = data.flare_trigger === 'physical_activity';", "validate": {"required": true}}, {"key": "physical_activity_trigger_other_text", "type": "textfield", "input": true, "label": "Please describe the triggering activity:", "placeholder": "Enter details", "tableView": true, "customConditional": "show = data.physical_activity_trigger === 'other_physical';", "validate": {"required": true}}, {"key": "symptom_progression", "type": "radio", "input": true, "label": "Since your symptoms began, have they been:", "values": [{"label": "Stable (no significant change)", "value": "stable"}, {"label": "Gradually worsening", "value": "gradual_worsening"}, {"label": "Rapidly worsening", "value": "rapid_worsening"}, {"label": "Improving", "value": "improving"}, {"label": "Fluctuating without clear progression", "value": "fluctuating"}], "tableView": true, "customConditional": "show = data.flare_trigger !== undefined;", "validate": {"required": true}}, {"key": "diagnosis_review_header", "type": "content", "input": false, "html": "<h3>Your Diagnosis</h3>", "label": "Diagnosis Review", "tableView": false}, {"key": "diagnosis_received", "type": "radio", "input": true, "label": "Have you received a diagnosis for this condition?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "tableView": true, "validate": {"required": true}}, {"key": "diagnosis_details", "type": "selectboxes", "input": true, "label": "What was the diagnosis provided?", "values": [{"label": "Muscle strain", "value": "muscle_strain", "shortcut": ""}, {"label": "<PERSON><PERSON><PERSON>", "value": "tendinitis", "shortcut": ""}, {"label": "Nerve impingement", "value": "nerve_impingement", "shortcut": ""}, {"label": "Oste<PERSON>th<PERSON>is", "value": "osteoarthritis", "shortcut": ""}, {"label": "Fibromyalgia", "value": "fibromyalgia", "shortcut": ""}, {"label": "Chronic pain syndrome", "value": "chronic_pain_syndrome", "shortcut": ""}, {"label": "Other", "value": "other_diagnosis", "shortcut": ""}], "tableView": true, "customConditional": "show = data.diagnosis_received === 'yes';", "validate": {"required": true}, "optionsLabelPosition": "right"}, {"key": "diagnosis_other_text", "type": "textfield", "input": true, "label": "Please provide the diagnosis:", "placeholder": "Enter diagnosis details", "tableView": true, "customConditional": "show = data.diagnosis_received === 'yes' && data.diagnosis_details && data.diagnosis_details.other_diagnosis;", "validate": {"required": true}}, {"key": "diagnosis_provider", "type": "radio", "input": true, "label": "Who provided this diagnosis?", "values": [{"label": "Medical doctor (MD)", "value": "md"}, {"label": "Nurse practitioner (NP)", "value": "np"}, {"label": "Physiotherapist", "value": "physiotherapist"}, {"label": "Chiropractor", "value": "chiropractor"}, {"label": "Other healthcare provider", "value": "other_provider"}], "tableView": true, "customConditional": "show = data.diagnosis_received === 'yes';", "validate": {"required": true}}, {"key": "provider_other_text", "type": "textfield", "input": true, "label": "Please specify the provider:", "placeholder": "Enter provider details", "tableView": true, "customConditional": "show = data.diagnosis_received === 'yes' && data.diagnosis_provider === 'other_provider';", "validate": {"required": true}}, {"key": "treatment_response_header", "type": "content", "input": false, "html": "<h3>How You Are Responding to Treatment</h3>", "label": "Treatment Response", "tableView": false}, {"key": "treatment_tried", "type": "radio", "input": true, "label": "Have you tried any treatments for this concern?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "tableView": true, "validate": {"required": true}}, {"key": "treatment_types", "type": "selectboxes", "input": true, "label": "What treatments have you tried? (Select all that apply)", "values": [{"label": "Physiotherapy", "value": "physiotherapy"}, {"label": "Chiropractic care", "value": "chiropractic"}, {"label": "Massage therapy", "value": "massage_therapy"}, {"label": "Pain medication", "value": "pain_medication"}, {"label": "Acupuncture", "value": "acupuncture"}, {"label": "Exercise/stretching", "value": "exercise"}], "inputType": "checkbox", "tableView": true, "customConditional": "show = data.treatment_tried === 'yes';"}, {"key": "treatment_relief", "type": "radio", "input": true, "label": "Did these treatments provide relief?", "values": [{"label": "Yes, significant relief", "value": "significant_relief"}, {"label": "Some relief", "value": "some_relief"}, {"label": "No relief", "value": "no_relief"}], "tableView": true, "customConditional": "show = data.treatment_tried === 'yes';", "validate": {"required": true}}, {"key": "investigation_history_header", "type": "content", "input": false, "html": "<h3>Previous Tests and Investigations</h3>", "label": "Investigation History", "tableView": false}, {"key": "investigations_done", "type": "selectboxes", "input": true, "label": "Have you had any investigations related to this concern? (Select all that apply)", "values": [{"label": "X‑ray", "value": "xray"}, {"label": "MRI", "value": "mri"}, {"label": "CT scan", "value": "ct_scan"}, {"label": "Ultrasound", "value": "ultrasound"}, {"label": "Nerve conduction studies", "value": "nerve_conduction"}, {"label": "None", "value": "none"}], "inputType": "checkbox", "tableView": true, "optionsLabelPosition": "right"}, {"key": "investigation_results", "type": "selectboxes", "input": true, "label": "What were the investigation results?", "values": [{"label": "<PERSON><PERSON><PERSON>", "value": "tendinitis", "shortcut": ""}, {"label": "Torn Ligament", "value": "torn_ligament", "shortcut": ""}, {"label": "Muscle Strain", "value": "muscle_strain", "shortcut": ""}, {"label": "Stress Fracture", "value": "stress_fracture", "shortcut": ""}, {"label": "Oste<PERSON>th<PERSON>is", "value": "osteoarthritis", "shortcut": ""}, {"label": "No abnormal findings", "value": "normal", "shortcut": ""}, {"label": "Awaiting further testing", "value": "awaiting", "shortcut": ""}, {"label": "Don't know", "value": "dont_know", "shortcut": ""}], "tableView": true, "customConditional": "show = data.investigations_done && data.investigations_done.indexOf('none') === -1;", "validate": {"required": true}, "optionsLabelPosition": "right"}, {"key": "health_background_header", "type": "content", "input": false, "html": "<h3>Your Health Background</h3>", "label": "Health Background", "tableView": false}, {"key": "medical_conditions", "type": "selectboxes", "input": true, "label": "Do you have any of the following medical conditions? (Select all that apply)", "values": [{"label": "Osteoporosis", "value": "osteoporosis"}, {"label": "Recent fractures", "value": "fractures"}, {"label": "Cancer", "value": "cancer"}, {"label": "Blood clotting disorder", "value": "blood_clot"}, {"label": "Heart disease", "value": "heart_disease"}, {"label": "Uncontrolled high blood pressure", "value": "hypertension"}, {"label": "Skin infections/open wounds", "value": "skin_infections"}, {"label": "None of the above", "value": "none"}], "inputType": "checkbox", "tableView": true, "optionsLabelPosition": "right", "validate": {"custom": "valid = _.some(_.values(data.medical_conditions)) || data.medical_none === true;", "customMessage": "Please select at least one option or indicate 'None of the above'."}}, {"key": "medical_none", "type": "checkbox", "input": true, "label": "None of the above", "defaultValue": false, "customClass": "mt-n3", "tableView": true, "validate": {"custom": "valid = _.some(_.values(data.medical_conditions)) || data.medical_none === true;"}}, {"key": "blood_thinners", "type": "radio", "input": true, "label": "Are you currently on any blood thinners?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "tableView": true, "validate": {"required": true}}, {"key": "section9_header", "type": "content", "input": false, "html": "<h3>9. Additional Information</h3>", "label": "Section 9 Header", "tableView": false}, {"key": "other_symptoms_header", "type": "content", "input": false, "html": "<h3>Other Symptoms</h3><p>Please indicate any additional symptoms you may be experiencing.</p>", "label": "Other Symptoms Header", "tableView": false}, {"key": "red_flag_symptoms", "type": "selectboxes", "input": true, "label": "Do you have any of the following symptoms? (Select all that apply)", "values": [{"label": "Unexpected weight loss", "value": "weight_loss"}, {"label": "Fever, chills, or night sweats", "value": "fever_chills"}, {"label": "Numbness, tingling, or weakness in limbs", "value": "neurological_symptoms"}, {"label": "Difficulty controlling bowel or bladder", "value": "loss_control"}, {"label": "Ongoing pain that does not improve with rest", "value": "severe_pain"}, {"label": "Pain that increases at night or during rest", "value": "night_pain"}, {"label": "Recent injury (e.g., fall, accident)", "value": "recent_trauma"}, {"label": "History of cancer with new or changing pain", "value": "cancer_history"}, {"label": "None of the above", "value": "none"}], "inputType": "checkbox", "tableView": true, "adminflag": true, "optionsLabelPosition": "right", "validate": {"custom": "valid = _.some(_.values(data.red_flag_symptoms)) || data.red_flag_none === true;", "customMessage": "Please select at least one option or indicate 'None of the above'."}}, {"key": "red_flag_none", "type": "checkbox", "input": true, "label": "None of the above", "defaultValue": false, "customClass": "mt-n3", "tableView": true, "validate": {"custom": "valid = _.some(_.values(data.red_flag_symptoms)) || data.red_flag_none === true;"}}, {"key": "additional_information", "type": "select", "input": true, "label": "Is there anything else you'd like to share about your condition?", "placeholder": "Select an option", "data": {"values": [{"label": "No additional information", "value": "none"}, {"label": "I've experienced similar symptoms before", "value": "similar_symptoms"}, {"label": "Symptoms are interfering with daily activities", "value": "interfering"}, {"label": "I am concerned about worsening symptoms", "value": "worsening"}]}, "tableView": true, "validate": {"required": true}}]}