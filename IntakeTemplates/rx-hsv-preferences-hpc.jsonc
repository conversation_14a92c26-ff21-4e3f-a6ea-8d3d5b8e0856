{"components": [{"key": "medication_preferences_header", "html": "<h2>Medication Preferences</h2><p>Please confirm your preferred HSV treatment options below.</p>", "type": "content", "input": false, "label": "Content"}, {"key": "select_site_heading", "html": "<h3>Select Your Site</h3>", "type": "content", "input": false, "label": "Content"}, {"key": "medication_site_confirmation", "type": "selectboxes", "input": true, "label": "Please confirm the site you are requesting medication for:", "values": [{"label": "Cold sores (around the mouth or nose)", "value": "cold_sores"}, {"label": "Genital area", "value": "genital"}, {"label": "Non-oral, non-genital areas (e.g., body rash)", "value": "non_oral_non_genital"}, {"label": "I'm not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Site for treatment:"}, {"key": "therapy_type_heading", "html": "<h3>Therapy Type</h3>", "type": "content", "input": false, "label": "Content", "customConditional": "show = data.medication_site_confirmation && Object.values(data.medication_site_confirmation).some(value => value);"}, {"key": "therapy_type_intro", "type": "radio", "input": true, "label": "There are two ways to use antiviral medication: treating outbreaks as they happen or taking a daily dose to prevent outbreaks. <strong>Most patients treat outbreaks as needed</strong>, but a daily dose might be recommended in these situations: </br></br><ul><li>You have frequent outbreaks (6 or more per year).</li><li>Your outbreaks are long-lasting or very painful.</li><li>You want to reduce the risk of passing herpes to a partner who has tested negative on a herpes blood test.</li><li>You want to lower the risk of passing herpes during planned future exposures.</li></ul>Which therapy type do you prefer?</br>", "values": [{"label": "On-demand treatment (medication taken during outbreaks)", "value": "on_demand"}, {"label": "Daily suppressive therapy (prevents outbreaks and reduces transmission risk)", "value": "suppressive"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Preferred therapy type:", "customConditional": "show = data.medication_site_confirmation && Object.values(data.medication_site_confirmation).some(value => value);"}, {"key": "confirm_daily_dose_heading", "html": "<h3>Daily Dose Therapy</h3>", "type": "content", "input": false, "label": "Content", "customConditional": "show = data.therapy_type_intro === 'suppressive';"}, {"key": "confirm_daily_dose", "type": "radio", "input": true, "label": "Are you sure you wish to take a daily dose of antiviral medication? <br><br> <p>Daily suppressive therapy has potential downsides compared to episodic treatment:</p> <ul> <li>Taking medication every day, resulting in a much higher total pill count (365 pills/year versus a few per outbreak).</li> <li>Possible side effects, such as headaches, nausea, or gastrointestinal upset.</li><li>Higher cost due to the continuous need for medication.</li></ul>", "values": [{"label": "Yes, I wish to take a daily dose", "value": "yes"}, {"label": "If no, please revise your response above", "value": "no"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Confirmation for daily suppressive therapy:", "customConditional": "show = data.therapy_type_intro === 'suppressive';"}, {"key": "daily_dose_reason", "type": "selectboxes", "input": true, "label": "Which reason best describes why you wish to pursue a daily dose of antiviral medication? (Select all that apply)", "values": [{"label": "Frequent outbreaks (6 or more per year)", "value": "frequent_outbreaks"}, {"label": "Prolonged or painful outbreaks", "value": "prolonged_outbreaks"}, {"label": "Reduce risk of passing herpes to a partner who has tested negative", "value": "reduce_risk_to_partner"}, {"label": "Reduce risk during planned future exposures", "value": "reduce_risk_future_exposures"}, {"label": "Other (please specify below)", "value": "other"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.confirm_daily_dose === 'yes';"}, {"key": "daily_dose_reason_other", "type": "textfield", "input": true, "label": "Please specify your reason for pursuing a daily dose:", "validate": {"required": false}, "tableView": true, "placeholder": "Enter your reason", "customConditional": "show = data.daily_dose_reason && data.daily_dose_reason.other;"}, {"key": "suppressive_therapy_confirmation", "type": "radio", "input": true, "label": "Do you confirm that you want suppressive therapy to help prevent herpes outbreaks and understand the increased number of doses required with this approach?", "values": [{"label": "Yes, I want suppressive therapy", "value": "yes"}, {"label": "No, I want episodic treatment instead", "value": "no"}], "tooltip": "Suppressive therapy means taking daily antiviral medication, which significantly increases the number of pills taken compared to episodic treatment (365 pills/year vs. pills only during outbreaks).", "validate": {"required": true}, "tableView": true, "confirm_label": "Suppressive therapy preference:", "customConditional": "show = data.treatment_type === 'suppressive';"}, {"key": "suppressive_therapy_past", "type": "radio", "input": true, "label": "Have you used daily suppressive therapy in the past?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Previous suppressive therapy usage:", "customConditional": "show = data.treatment_type === 'suppressive';"}, {"key": "on_demand_heading_genital", "html": "<h3>On-Demand Treatment for Genital/Body Herpes</h3>", "type": "content", "input": false, "label": "Content", "customConditional": "show = data.therapy_type_intro === 'on_demand' && data.medication_site_confirmation && (data.medication_site_confirmation.genital || data.medication_site_confirmation.non_oral_non_genital);"}, {"key": "on_demand_dosing_genital", "type": "selectboxes", "input": true, "label": "Select the medication and dosing for genital herpes treatment:", "values": [{"label": "Valacyclovir: 500mg twice daily for 3 days", "value": "valacyclovir-500mg-bid"}, {"label": "Acyclovir: 800 mg twice times daily for 5 days", "value": "acyclovir-800mg-bid"}, {"label": "Famciclovir: 1000mg twice daily for 1 day", "value": "famcyclovir-1000mg-bid"}, {"label": "Acyclovir (Ointment): Apply six times daily for 7 days", "value": "zovirax-genital"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.therapy_type_intro === 'on_demand' && data.medication_site_confirmation && (data.medication_site_confirmation.genital || data.medication_site_confirmation.non_oral_non_genital);"}, {"key": "on_demand_heading_cold_sores", "html": "<h3>On-Demand Treatment for Cold Sores</h3><p>Choose the treatment options and dosage for outbreaks in the oral area.</p>", "type": "content", "input": false, "label": "Content", "customConditional": "show = data.therapy_type_intro === 'on_demand' && data.medication_site_confirmation && data.medication_site_confirmation.cold_sores;"}, {"key": "on_demand_dosing_cold_sores", "type": "selectboxes", "input": true, "label": "Select the medication and dosing for cold sore treatment:", "values": [{"label": "Valacyclovir: 2000 mg (4 tablets) twice daily for 1 day (recommended)", "value": "valacyclovir-2g-bid", "default": true}, {"label": "Acyclovir: 400 mg three times daily for 5 days", "value": "acyclovir-400mg-tid"}, {"label": "Acyclovir (Ointment): Apply five times daily for 4 days", "value": "zovirax-cold-sore"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.therapy_type_intro === 'on_demand' && data.medication_site_confirmation && data.medication_site_confirmation.cold_sores;"}, {"key": "suppressive_heading", "html": "<h3>Select Your Suppressive Medication</h3><p>Choose the treatment options and daily dosing for preventing outbreaks.</p>", "type": "content", "input": false, "label": "Content", "customConditional": "show = data.therapy_type_intro === 'suppressive' && data.medication_site_confirmation && Object.values(data.medication_site_confirmation).some(value => value);"}, {"key": "suppressive_dosing", "type": "selectboxes", "input": true, "label": "Select the medication and daily dosing for suppressive therapy:", "values": [{"label": "Valacyclovir: 500 mg once daily (low frequency of outbreaks) (most common)", "value": "valacyclovir-500mg-od", "shortcut": ""}, {"label": "Valacyclovir: 1 gram once daily (high frequency of outbreaks or reduce transmission to a partner)", "value": "valacyclovir-1000mg-od", "shortcut": ""}, {"label": "Acyclovir: 400 mg twice daily", "value": "acyclovir-400mg-bid", "shortcut": ""}, {"label": "Famciclovir: 250 mg twice daily", "value": "famciclovir_250_twice", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "confirm_label": "Selected dosing for suppressive therapy:", "customConditional": "show = data.therapy_type_intro === 'suppressive' && data.medication_site_confirmation && Object.values(data.medication_site_confirmation).some(value => value);"}, {"key": "reduced_kidney_function", "type": "radio", "input": true, "label": "Do you have reduced kidney function?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Do you have reduced kidney function:"}, {"key": "rxts", "type": "textfield", "input": true, "label": "HSV Rx Templates:", "hidden": true, "disabled": true, "multiple": true, "adminFlag": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "let output = []; if (Array.isArray(data.on_demand_dosing_cold_sores)) { output = output.concat(data.on_demand_dosing_cold_sores); } if (Array.isArray(data.on_demand_dosing_genital)) { output = output.concat(data.on_demand_dosing_genital); } if (Array.isArray(data.suppressive_dosing)) { output = output.concat(data.suppressive_dosing); } value = output.length ? output : [];", "refreshOnChange": true}]}