{"components": [{"key": "content", "html": "<h2>Instructions</h2><p>Please complete the following questionnaire regarding your menstrual and fertility history to assist with your AMH testing. This information helps provide a better understanding of your reproductive health.</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "header_menstrual_history", "html": "<h2>Menstrual History</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "age_menstruation_started", "type": "number", "input": true, "label": "At approximately what age did you start menstruating?", "validate": {"required": true}, "tableView": true}, {"key": "menstrual_cycle_regular", "type": "radio", "input": true, "label": "How would you describe your menstrual cycles?</br></br><strong>• Regular menstrual cycles:</strong> Your periods generally arrive every 21-35 days, even if there's a slight variation.</br>&emsp;<strong>Example:</strong> You might go 25 days between periods one month, 27 days the next, and 30 days after that—this is still considered regular.</br></br><strong>• Irregular menstrual cycles:</strong> This involves wide swings in timing or skipping cycles.</br>&emsp;<strong>Example:</strong> You might have 25 days between one cycle and the next, then 55 days until the following period, then 28 days, and then 42 days—this pattern is considered irregular.</br>", "inline": false, "values": [{"label": "Yes, my cycles are regular", "value": "regular"}, {"label": "No, they are irregular", "value": "irregular"}, {"label": "I'm not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true}, {"key": "cycle_length", "data": {"values": [{"label": "Doesn't apply", "value": "does_not_apply"}, {"label": "I don't know", "value": "do_not_know"}, {"label": "21", "value": "21"}, {"label": "22", "value": "22"}, {"label": "23", "value": "23"}, {"label": "24", "value": "24"}, {"label": "25", "value": "25"}, {"label": "26", "value": "26"}, {"label": "27", "value": "27"}, {"label": "28", "value": "28"}, {"label": "29", "value": "29"}, {"label": "30", "value": "30"}, {"label": "31", "value": "31"}, {"label": "32", "value": "32"}, {"label": "33", "value": "33"}, {"label": "34", "value": "34"}, {"label": "35", "value": "35"}, {"label": "36", "value": "36"}, {"label": "37", "value": "37"}, {"label": "38", "value": "38"}, {"label": "39", "value": "39"}, {"label": "40", "value": "40"}]}, "type": "select", "input": true, "label": "What is the average length of your cycle (in days)? (Count from the first day of full flow of one cycle to the first day of full flow of the next.)", "widget": "html5", "tableView": true, "conditional": {"eq": "regular", "show": true, "when": "menstrual_cycle_regular"}}, {"key": "header_fertility_history", "html": "<h2>Fertility History</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "trying_to_conceive", "type": "radio", "input": true, "label": "Are you currently trying to conceive?", "inline": false, "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "tableView": true}, {"key": "trying_to_conceive_duration", "data": {"values": [{"label": "Less than 1 month", "value": "<1_month"}, {"label": "1-2 months", "value": "1-2_months"}, {"label": "2-3 months", "value": "2-3_months"}, {"label": "3-4 months", "value": "3-4_months"}, {"label": "4-5 months", "value": "4-5_months"}, {"label": "5-6 months", "value": "5-6_months"}, {"label": "6-7 months", "value": "6-7_months"}, {"label": "7-8 months", "value": "7-8_months"}, {"label": "8-9 months", "value": "8-9_months"}, {"label": "9-10 months", "value": "9-10_months"}, {"label": "10-11 months", "value": "10-11_months"}, {"label": "11-12 months", "value": "11-12_months"}, {"label": "1-2 years", "value": "1-2_years"}, {"label": "2-3 years", "value": "2-3_years"}, {"label": "3+ years", "value": ">3_years"}]}, "type": "select", "input": true, "label": "For how long have you been trying to conceive?", "widget": "html5", "tableView": true, "customConditional": "show = data.trying_to_conceive == 'yes';"}, {"key": "previous_pregnancies", "type": "radio", "input": true, "label": "Have you had any previous pregnancies?", "inline": false, "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "tableView": true}, {"key": "pregnancy_outcomes", "type": "selectboxes", "input": true, "label": "What were the outcomes of your previous pregnancies? (Select all that apply)", "values": [{"label": "Live birth", "value": "live_birth"}, {"label": "Miscarriage", "value": "miscarriage"}, {"label": "Stillbirth", "value": "stillbirth"}, {"label": "Termination", "value": "termination"}], "inputType": "checkbox", "tableView": true, "customConditional": "show = data.previous_pregnancies == 'yes';"}, {"key": "fertility_treatment_history", "type": "radio", "input": true, "label": "Have you ever undergone fertility treatments (e.g., IVF, IUI)?", "inline": false, "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "tableView": true}, {"key": "type_of_fertility_treatment", "type": "selectboxes", "input": true, "label": "Which fertility treatments have you undergone? (Select all that apply)", "values": [{"label": "IVF (In Vitro Fertilization)", "value": "ivf"}, {"label": "IUI (Intrauterine Insemination)", "value": "iui"}, {"label": "Ovulation Induction", "value": "ovulation_induction"}, {"label": "Other", "value": "other"}], "inputType": "checkbox", "tableView": true, "customConditional": "show = data.fertility_treatment_history == 'yes';"}, {"key": "family_history_infertility", "type": "radio", "input": true, "label": "Do you have a family history of infertility?", "inline": false, "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "I Don't Know", "value": "dont_know"}], "tableView": true}, {"key": "header_amh_values", "html": "<h2>AMH Values</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "previous_amh_testing", "type": "radio", "input": true, "label": "Have you had AMH testing in the past?", "inline": false, "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "I Don't Know", "value": "unknown"}], "tableView": true}, {"key": "last_amh_test", "data": {"values": [{"label": "< 3 months ago", "value": "<3months"}, {"label": "3-6 months ago", "value": "3-6_months"}, {"label": "6-12 months ago", "value": "6-12_months"}, {"label": "1 - 2 years ago", "value": "1-2_years"}, {"label": "2 - 3 years ago", "value": "2-3_years"}, {"label": "3-5 years ago", "value": "3-5_years"}, {"label": "More than 5 years ago", "value": "5+_years"}]}, "type": "select", "input": true, "label": "When did you have your last AMH test (outside of TeleTest)?", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.previous_amh_testing == 'yes';"}, {"key": "amh_value_known", "type": "radio", "input": true, "label": "Do you know the approximate value of your last AMH test?", "inline": false, "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "tableView": true, "customConditional": "show = data.previous_amh_testing == 'yes';"}, {"key": "amh_value_range", "data": {"values": [{"label": "Unknown", "value": "unknown"}, {"label": "< 1 pmol/L (< 0.14 ng/mL)", "value": "<1"}, {"label": "1-5 pmol/L (0.14-0.7 ng/mL)", "value": "1-5"}, {"label": "5-15 pmol/L (0.7-2.1 ng/mL)", "value": "5-15"}, {"label": "15-25 pmol/L (2.1-3.5 ng/mL)", "value": "15-25"}, {"label": "25-35 pmol/L (3.5-4.9 ng/mL)", "value": "25-35"}, {"label": "35-48 pmol/L (4.9-6.7 ng/mL)", "value": "35-48"}, {"label": "> 48 pmol/L (> 6.7 ng/mL)", "value": ">48"}]}, "type": "select", "input": true, "label": "Please select the range of your last AMH level (pmol/L and ng/mL):", "widget": "html5", "tableView": true, "customConditional": "show = data.amh_value_known == 'yes';"}, {"key": "header_partners_fertility", "html": "<h2>Partner's Fertility Investigations</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "has_partner", "type": "radio", "input": true, "label": "Do you currently have a partner for fertility considerations?", "inline": false, "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "tableView": true}, {"key": "partner_fertility_testing", "type": "radio", "input": true, "label": "Has your partner undergone any fertility testing (e.g., semen analysis)?", "inline": false, "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "I don't know", "value": "unknown"}], "tableView": true, "customConditional": "show = data.has_partner == 'yes';"}, {"key": "partner_semen_analysis_result", "type": "radio", "input": true, "label": "Were your partner's semen analysis parameters normal or abnormal?", "inline": false, "values": [{"label": "Normal", "value": "normal"}, {"label": "Abnormal", "value": "abnormal"}, {"label": "Hasn't been done", "value": "not_completed_yet"}, {"label": "I don't know", "value": "unknown"}], "tableView": true, "customConditional": "show = data.partner_fertility_testing == 'yes';"}, {"key": "header_menstrual_tracking", "html": "<h2>Menstrual Tracking</h2><p>Tracking your menstrual cycles helps assess cycle regularity and predict ovulation. Please provide details about your last few periods.</p>", "type": "content", "input": false, "label": "Menstrual Tracking", "tableView": false, "refreshOnChange": false}, {"key": "do_you_track_cycles", "type": "radio", "input": true, "label": "Do you keep track of your menstrual cycles using a calendar or an app?", "inline": false, "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "tableView": true}, {"key": "header_most_recent_period", "html": "<h3>Most Recent Period</h3><p>Please record the first day of full bleeding and how long it lasted.</p>", "type": "content", "input": false, "label": "Most Recent Period", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.do_you_track_cycles == 'yes';"}, {"key": "tracking_last_cycle_date", "type": "datetime", "input": true, "label": "Date when your most recent period started (first day of full bleeding):", "widget": "calendar", "tableView": true, "customConditional": "show = data.do_you_track_cycles == 'yes';"}, {"key": "tracking_last_cycle_duration", "type": "number", "input": true, "label": "How many days did your most recent period last? (Count from first spotting to the end of bleeding.)", "tableView": true, "customConditional": "show = data.do_you_track_cycles == 'yes';"}, {"key": "header_period_before_that", "html": "<h3>Period Before That</h3><p>Now, record details about the period you had before your most recent one.</p>", "type": "content", "input": false, "label": "Period Before That", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.do_you_track_cycles == 'yes';"}, {"key": "tracking_prior_cycle1_date", "type": "datetime", "input": true, "label": "Date when the period before your most recent one started (first day of full bleeding):", "widget": "calendar", "tableView": true, "customConditional": "show = data.do_you_track_cycles == 'yes';"}, {"key": "tracking_prior_cycle1_duration", "type": "number", "input": true, "label": "How many days did that period last? (Count from first spotting to the end of bleeding.)", "tableView": true, "customConditional": "show = data.do_you_track_cycles == 'yes';"}, {"key": "calculated_cycle_length", "type": "textfield", "input": true, "label": "Calculated Length of Time Between Your Last Two Periods (in days)", "disabled": true, "tableView": true, "confirmLabel": "Last Cycle Length:", "calculateValue": "if (data.tracking_last_cycle_date && data.tracking_prior_cycle1_date) { var lastCycleStart = new Date(data.tracking_last_cycle_date); var priorCycle1Start = new Date(data.tracking_prior_cycle1_date); value = (lastCycleStart - priorCycle1Start) / (1000 * 60 * 60 * 24); }", "customConditional": "show = data.do_you_track_cycles == 'yes';"}, {"key": "header_another_period_before_that", "html": "<h3>Another Period Before That</h3><p>If you remember, please record details about the period before the last two.</p>", "type": "content", "input": false, "label": "Another Period Before That", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.do_you_track_cycles == 'yes';"}, {"key": "tracking_prior_cycle2_date", "type": "datetime", "input": true, "label": "Date when the period before the last two started (first day of full bleeding):", "widget": "calendar", "tableView": true, "customConditional": "show = data.do_you_track_cycles == 'yes';"}, {"key": "tracking_prior_cycle2_duration", "type": "number", "input": true, "label": "How many days did that period last? (Count from first spotting to the end of bleeding.)", "tableView": true, "customConditional": "show = data.do_you_track_cycles == 'yes';"}, {"key": "calculated_prior_cycle_length", "type": "textfield", "input": true, "label": "Calculated Length of Time Between These Periods (in days)", "disabled": true, "tableView": true, "confirmLabel": "Previous Cycle Length:", "calculateValue": "if (data.tracking_prior_cycle1_date && data.tracking_prior_cycle2_date) { var priorCycle1Start = new Date(data.tracking_prior_cycle1_date); var priorCycle2Start = new Date(data.tracking_prior_cycle2_date); value = (priorCycle1Start - priorCycle2Start) / (1000 * 60 * 60 * 24); }", "customConditional": "show = data.do_you_track_cycles == 'yes';"}, {"key": "header_previous_investigations", "html": "<h2>Prior Investigations</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "prior_investigations_imaging", "type": "selectboxes", "input": true, "label": "Which imaging investigations have you had as part of your fertility workup? (Select all that apply)", "values": [{"label": "Transvaginal Ultrasound", "value": "transvaginal_ultrasound"}, {"label": "Sonohysterogram", "value": "sonohysterogram"}, {"label": "HSG (Hysterosalpingogram)", "value": "hsg"}, {"label": "None of the Above", "value": "none_of_the_above"}], "inputType": "checkbox", "tableView": true}, {"key": "prior_investigations_labwork", "type": "selectboxes", "input": true, "label": "Which lab tests have you had as part of your fertility investigations? (Select all that apply)", "values": [{"label": "FSH", "value": "fsh"}, {"label": "LH", "value": "lh"}, {"label": "TSH", "value": "tsh"}, {"label": "Prolactin", "value": "prolactin"}, {"label": "Testosterone", "value": "testosterone"}, {"label": "None of the Above", "value": "none_of_the_above"}], "inputType": "checkbox", "tableView": true}, {"key": "header_day3_21bloodwork", "html": "<h2>Day 3 & Day 21 Bloodwork</h2><p>Blood tests on specific days of your menstrual cycle help assess your hormonal health and fertility status. These tests provide important information about your ovarian reserve and whether you are ovulating regularly.</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "day3_bloodwork", "type": "radio", "input": true, "label": "<strong>Day 3 Bloodwork (Baseline Hormone Testing)</strong><br>Typically done on the 2nd or 3rd day of your menstrual cycle, this test measures key hormones such as:<ul><li><strong>FSH (Follicle-Stimulating Hormone):</strong> Assesses ovarian reserve and how hard your body is working to stimulate the ovaries.</li><li><strong>LH (Luteinizing Hormone):</strong> Regulates ovulation and egg release.</li><li><strong>Estradiol (E2):</strong> Reflects estrogen levels, important for the menstrual cycle and ovulation.</li></ul>Have you had Day 3 bloodwork as part of your fertility workup?", "inline": false, "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "I'm not sure", "value": "unknown"}], "tableView": true}, {"key": "day21_bloodwork", "type": "radio", "input": true, "label": "<strong>Day 21 Bloodwork (Luteal Phase Progesterone Test)</strong><br>Usually performed around day 21 of a 28-day cycle (or about 7 days after ovulation), this test measures:<ul><li><strong>Progesterone:</strong> Confirms if ovulation has occurred by evaluating progesterone levels, which rise after ovulation.</li></ul>This test helps determine if you are ovulating regularly. Have you had Day 21 bloodwork as part of your fertility workup?", "inline": false, "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "I'm not sure", "value": "unknown"}], "tableView": true}, {"key": "header_conditions_affect_fertility", "html": "<h2>Conditions That May Affect Fertility</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "conditions_affect_fertility", "type": "selectboxes", "input": true, "label": "Have you been diagnosed with any of the following conditions that may affect fertility? (Select all that apply)", "values": [{"label": "PCOS (Polycystic Ovarian Syndrome)", "value": "pcos", "shortcut": ""}, {"label": "Endometriosis", "value": "endometriosis", "shortcut": ""}, {"label": "Uterine Fibroids", "value": "fibroids", "shortcut": ""}, {"label": "Pelvic Inflammatory Disease (PID)", "value": "pid", "shortcut": ""}, {"label": "Other (Specify Below)", "value": "other", "shortcut": ""}], "adminFlag": true, "tableView": true, "optionsLabelPosition": "right"}, {"key": "none_of_the_above_conditions_affecting_fertility", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a condition."}, "validate": {"custom": "valid = !!data.none_of_the_above_conditions_affecting_fertility || _.some(_.values(data.conditions_affect_fertility));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "specified_other_condition", "type": "textfield", "input": true, "label": "If you selected 'Other,' please specify:", "hidden": true, "tableView": true, "clearOnHide": false, "customConditional": "show = !!data.conditions_affect_fertility.other;"}, {"key": "conditions_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following fertility-related conditions:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "I DO NOT have the following fertility-related conditions:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.conditions_affect_fertility, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "header_prior_surgeries_fertility", "html": "<h2>Prior Surgeries</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "prior_surgeries_fertility", "type": "selectboxes", "input": true, "label": "Have you undergone any surgeries that may affect fertility? (Select all that apply)", "values": [{"label": "Tubal Ligation", "value": "tubal_ligation", "shortcut": ""}, {"label": "Tubal Ligation Reversal", "value": "tubal_ligation_reversal", "shortcut": ""}, {"label": "Myomectomy (Fibroid Removal)", "value": "myomectomy", "shortcut": ""}, {"label": "Ovarian <PERSON>", "value": "ovarian_cyst_removal", "shortcut": ""}, {"label": "Other (Specify Below)", "value": "other", "shortcut": ""}], "adminFlag": true, "tableView": true, "optionsLabelPosition": "right"}, {"key": "none_of_the_above_prior_surgeries", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a surgery."}, "validate": {"custom": "valid = !!data.none_of_the_above_prior_surgeries || _.some(_.values(data.prior_surgeries_fertility));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "specified_other_surgery", "type": "textfield", "input": true, "label": "If you selected 'Other,' please specify:", "hidden": true, "tableView": true, "clearOnHide": false, "customConditional": "show = !!data.prior_surgeries_fertility.other;"}, {"key": "surgeries_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following fertility-related surgeries:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "I DO NOT have the following fertility-related surgeries:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.prior_surgeries_fertility, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "header_symptoms", "html": "<h2>Symptoms</h2><p>Please indicate if you have experienced any of the following symptoms related to your menstrual or reproductive health.</p>", "type": "content", "input": false, "label": "Symptoms", "tableView": false, "refreshOnChange": false}, {"key": "fertility_symptoms", "type": "selectboxes", "input": true, "label": "Have you experienced any of the following symptoms? (Select all that apply)", "values": [{"label": "Spotting between menstrual cycles", "value": "spotting_between_cycles", "shortcut": ""}, {"label": "Increasing pain with my menstrual cycles", "value": "increasing_pain_with_cycles", "shortcut": ""}, {"label": "Very heavy menstrual bleeding", "value": "heavy_menstrual_bleeding", "shortcut": ""}, {"label": "Bleeding after intercourse", "value": "bleeding_after_sex", "shortcut": ""}, {"label": "Chronic pelvic pain", "value": "chronic_pelvic_pain", "shortcut": ""}, {"label": "Other (Specify Below)", "value": "other", "shortcut": ""}], "adminFlag": true, "tableView": true, "optionsLabelPosition": "right"}, {"key": "none_of_the_above_fertility_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = !!data.none_of_the_above_fertility_symptoms || _.some(_.values(data.fertility_symptoms));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "specified_other_symptom", "type": "textfield", "input": true, "label": "If you selected 'Other,' please specify:", "hidden": true, "tableView": true, "clearOnHide": false, "customConditional": "show = !!data.fertility_symptoms.other;"}, {"key": "symptoms_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following symptoms:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "I DO NOT have the following symptoms:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.fertility_symptoms, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "header_pap_testing", "html": "<h2>Pap Testing</h2><p>Regular Pap testing helps detect cervical changes early. Please answer the following questions about your screening history.</p><ul><li>A Pap test is <strong>not the same as a vaginal swab</strong>. It is a special screening test that checks for changes in cervical cells that could lead to cervical cancer.</li><li>The test involves a <strong>speculum exam</strong>. A speculum is a small, duck-bill-shaped device that is gently inserted into the vagina. It helps to hold the vaginal walls open so that the healthcare provider can clearly see the cervix and collect a sample of cells.</li><li><strong>Typically, the government sends reminder letters</strong> when you are due for a Pap test as part of routine cervical cancer screening programs.</li></ul>", "type": "content", "input": false, "label": "Pap Testing", "tableView": false, "refreshOnChange": false}, {"key": "had_pap_test", "type": "radio", "input": true, "label": "Have you ever had a Pap test?", "inline": false, "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "tableView": true}, {"key": "sexually_active", "type": "radio", "input": true, "label": "Have you ever been sexually active? <br><br><strong>Note:</strong> This includes any form of sexual activity such as vaginal, oral, or even casual genital contact.", "inline": false, "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Prefer not to disclose", "value": "prefer_not_disclose"}], "tableView": true, "customConditional": "show = data.had_pap_test == 'no';"}, {"key": "age_for_pap_test", "type": "number", "input": true, "label": "Please enter your age:", "tableView": true, "customConditional": "show = data.sexually_active == 'yes' || data.sexually_active == 'prefer_not_disclose';"}, {"key": "pap_test_age_warning", "type": "radio", "input": true, "label": "<p><strong>Important:</strong> If you are over the age of 25 and have ever been sexually active, you should have a Pap test to screen for cervical cancer. Regular screening helps detect changes early, which can prevent cervical cancer.<br><br>Do you understand the importance of Pap testing?</p>", "inline": false, "values": [{"label": "I understand", "value": "understand"}, {"label": "I do not understand", "value": "do_not_understand"}], "tableView": true, "customConditional": "show = (data.sexually_active == 'yes' || data.sexually_active == 'prefer_not_disclose') && data.age_for_pap_test >= 25;"}, {"key": "last_pap_normal", "type": "radio", "input": true, "label": "Was your last Pap test result normal?", "inline": false, "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "tableView": true, "customConditional": "show = data.had_pap_test == 'yes';"}, {"key": "last_pap_more_than_3_years", "type": "radio", "input": true, "label": "Has it been <strong>more than 3 years</strong> since your last normal Pap test?", "inline": false, "values": [{"label": "No, it has been 3 years or less", "value": "no"}, {"label": "Yes, it has been more than 3 years", "value": "yes"}], "tableView": true, "customConditional": "show = data.last_pap_normal == 'yes';"}, {"key": "pap_due_acknowledgment", "type": "radio", "input": true, "label": "<p><strong>Important:</strong> If it has been more than 3 years since your last normal Pap test, you should schedule a screening through your local public health unit, family doctor, or a walk-in clinic. Pap tests help detect early changes that can prevent cervical cancer. <br><br>Do you understand the importance of Pap testing?</p>", "inline": false, "values": [{"label": "I understand", "value": "understand"}, {"label": "I do not understand", "value": "do_not_understand"}], "tableView": true, "customConditional": "show = data.last_pap_more_than_3_years == 'yes';"}, {"key": "colposcopy_assessment", "type": "radio", "input": true, "label": "Did you receive a colposcopy assessment after an abnormal Pap test?", "inline": false, "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "tableView": true, "customConditional": "show = data.last_pap_normal == 'no';"}, {"key": "colposcopy_warning", "html": "<p><strong>Warning:</strong> If you had an abnormal Pap test and have not had a colposcopy assessment, you should consult a physician.</p>", "type": "content", "input": false, "tableView": false, "customConditional": "show = data.colposcopy_assessment == 'no';"}, {"key": "colposcopy_follow_up", "type": "radio", "input": true, "label": "Did your colposcopy follow-up recommend routine Pap testing or more frequent testing every 1-2 years?", "inline": false, "values": [{"label": "Routine testing at normal frequency", "value": "routine"}, {"label": "More frequent testing every 1-2 years", "value": "frequent"}], "tableView": true, "customConditional": "show = data.colposcopy_assessment == 'yes';"}, {"key": "followed_up_pap_recommendation", "type": "radio", "input": true, "label": "Have you followed up with your recommended Pap testing schedule?", "inline": false, "values": [{"label": "Yes", "value": "yes"}, {"label": "No, I am due for a Pap test", "value": "no_due"}], "tableView": true, "customConditional": "show = data.colposcopy_follow_up == 'frequent';"}, {"key": "pap_due_warning", "html": "<p><strong>Reminder:</strong> You are due for a Pap test based on your last colposcopy recommendation. Please schedule your screening.</p>", "type": "content", "input": false, "tableView": false, "customConditional": "show = data.followed_up_pap_recommendation == 'no_due';"}]}