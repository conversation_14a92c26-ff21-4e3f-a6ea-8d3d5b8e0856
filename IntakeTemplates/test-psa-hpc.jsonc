{
    "components": [
        {
            "key": "heading_psa_bloodwork",
            "html": "<h1><center><strong>PSA Testing</strong></h1><center><p>To expedite your care, please answer the following questions to the best of your abilities. &nbsp;We ask these questions to provide the best possible care.&nbsp;</p>",
            "type": "content",
            "input": false,
            "label": "Content",
            "tableView": false,
            "refreshOnChange": false
        },
        {
            "key": "heading_medical_indication",
            "html": "<h4>Reason for Testing&nbsp;</h4>",
            "type": "content",
            "input": false,
            "label": "Content",
            "tableView": false,
            "refreshOnChange": false
        },
        {
            "key": "testing_indication",
            "type": "selectboxes",
            "input": true,
            "label": "Please select your reason(s) for testing:",
            "values": [
                {
                    "label": "Family History of Prostate Cancer",
                    "value": "fm_hx_prostate_cancer",
                    "shortcut": ""
                },
                {
                    "label": "Routine Monitoring",
                    "value": "routine_testing",
                    "shortcut": ""
                },
                {
                    "label": "Prostate Symptoms",
                    "value": "prostate_symptoms",
                    "shortcut": ""
                },
                {
                    "label": "Monitoring PSA following Prostate Cancer Treatment",
                    "value": "notified_of_a_positive_result",
                    "shortcut": ""
                },
                {
                    "label": "Other",
                    "value": "other",
                    "shortcut": ""
                }
            ],
            "validate": {
                "required": true
            },
            "inputType": "checkbox",
            "tableView": true,
            "confirm_label": "Reason(s) for testing:",
            "optionsLabelPosition": "right"
        },
        {
            "key": "family_history",
            "type": "radio",
            "input": true,
            "label": "Do you have a family history (i.e. brother, father, uncle) of prostate cancer?",
            "inline": false,
            "values": [
                {
                    "label": "No",
                    "value": false,
                    "shortcut": ""
                },
                {
                    "label": "Yes",
                    "value": true,
                    "shortcut": ""
                }
            ],
            "validate": {
                "required": true
            },
            "tableView": true,
            "optionsLabelPosition": "right"
        },
        {
            "key": "age_at_diagnosis",
            "data": {
                "values": [
                    {
                        "label": "I don't know",
                        "value": "doesn't_know"
                    }, 
                    {
                        "label": "Age 20-25",
                        "value": "age_20_25"
                    },
                    {
                        "label": "Age 25-30",
                        "value": "age_25_30"
                    },
                    {
                        "label": "Age 30-35",
                        "value": "age_30_35"
                    },
                    {
                        "label": "Age 35-40",
                        "value": "age_35_40"
                    },
                    {
                        "label": "Age 40-45",
                        "value": "age_40_45"
                    },
                    {
                        "label": "Age 45-50",
                        "value": "age_45_50"
                    },
                    {
                        "label": "Age 50-55",
                        "value": "age_50_55"
                    },
                    {
                        "label": "Age 55-60",
                        "value": "age_55_60"
                    },
                    {
                        "label": "Age 60-65",
                        "value": "age_60_65"
                    },
                    {
                        "label": "Age 65-70",
                        "value": "age_65_70"
                    },
                    {
                        "label": "Age 70-75",
                        "value": "age_70_75"
                    },
                    {
                        "label": "Age 75-80",
                        "value": "age_75_80"
                    },
                    {
                        "label": "80+",
                        "value": "80+"
                    }
                ]
            },
            "type": "select",
            "input": true,
            "label": "Please specify the earliest age at which a family member rieved a diagnosis of prostate cancer:",
            "widget": "html5",
            "validate": {
                "required": true
            },
            "tableView": true,
            "customConditional": "show = data.family_history == true;",
            "optionsLabelPosition": "right"
        },
        {
            "key": "previous_psa_test",
            "type": "radio",
            "input": true,
            "label": "Have you had a PSA test before?",
            "inline": false,
            "values": [
                {
                    "label": "Yes",
                    "value": true,
                    "shortcut": ""
                },
                {
                    "label": "No",
                    "value": false,
                    "shortcut": ""
                }
                
            ],
            "validate": {
                "required": true
            },
            "tableView": true,
            "optionsLabelPosition": "right"
        },
        {
            "key": "last_psa_test",
            "type": "datetime",
            "input": true,
            "label": "When was your last PSA test?",
            "tableView": true,
            "validate": {
                "required": false
            },
            "customConditional": "show = data.previous_psa_test == true",
            "optionsLabelPosition": "right"
        },
        {
            "key": "last_psa_level",
            "type": "number",
            "input": true,
            "label": "What was your last PSA level (ng/mL)?",
            "tableView": true,
            "validate": {
                "required": false,
                "min": 0
            },
            "customConditional": "show = data.previous_psa_test == true",
            "optionsLabelPosition": "right"
        },
        {
            "key": "recent_infections",
            "type": "radio",
            "input": true,
            "label": "Have you had a recent urinary tract infection, prostatitis, or other prostate-related issues?",
            "inline": false,
            "values": [
                {
                    "label": "No",
                    "value": false,
                    "shortcut": ""
                },
                {
                    "label": "Yes",
                    "value": true,
                    "shortcut": ""
                }
            ],
            "validate": {
                "required": true
            },
            "tableView": true,
            "optionsLabelPosition": "right"
        }
            
            {
            "key": "recent_medical_procedures",
            "type": "radio",
            "input": true,
            "label": "Have you had any recent medical procedures involving the prostate or urinary system?",
            "inline": false,
            "values": [
                {
                    "label": "No",
                    "value": false,
                    "shortcut": ""
                },
                {
                    "label": "Yes",
                    "value": true,
                    "shortcut": ""
                }
            ],
            "validate": {
                "required": true
            },
            "tableView": true,
            "optionsLabelPosition": "right"
        }
        {
            "key": "content2",
            "html": "<h2>Questions for the Doctor</h2>",
            "type": "content",
            "input": false,
            "label": "Content",
            "tableView": false,
            "refreshOnChange": false
        },
        {
            "key": "additional_questions",
            "type": "textarea",
            "input": true,
            "label": "Please use this area to ask any questions that you might have for your health care provider:",
            "tableView": true,
            "autoExpand": false
        }
    ]
}