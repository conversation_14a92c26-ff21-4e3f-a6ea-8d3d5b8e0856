{"components": [{"key": "heading_hypothyroidism", "html": "<h1><center><strong>Hypothyroidism Assessment</strong></center></h1><p>To provide the best care, please answer the following questions about your symptoms, history, and possible causes of thyroid dysfunction.</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "reason_for_thyroid_testing", "type": "selectboxes", "input": true, "label": "What is your reason for requesting thyroid testing or treatment?", "values": [{"label": "I have symptoms", "value": "symptoms"}, {"label": "I've had abnormal thyroid levels in the past", "value": "prior_abnormal"}, {"label": "I am currently taking or recently stopped thyroid medication", "value": "on_medication"}, {"label": "I have a family history of thyroid disease", "value": "family_history"}, {"label": "I have another autoimmune condition (e.g. type 1 diabetes, lupus)", "value": "autoimmune"}, {"label": "I'm on a medication that affects thyroid function (e.g. lithium, amiodarone)", "value": "medication_thyroid_risk"}, {"label": "Renew thyroid medication prescription", "value": "renew_prescription"}, {"label": "Other", "value": "other"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Reason for Thyroid Testing:"}, {"key": "reason_for_thyroid_testing_other", "type": "textfield", "input": true, "label": "Please describe your reason for thyroid testing or treatment:", "tableView": true, "confirm_label": "Other reason for thyroid testing:", "customConditional": "show = data.reason_for_thyroid_testing?.other === true;"}, {"key": "heading_autoimmune", "html": "</br><h4>Autoimmune History</h4>", "type": "content", "input": false, "tableView": false, "customConditional": "show = data.reason_for_thyroid_testing?.autoimmune === true"}, {"key": "autoimmune_condition_type_v2", "type": "selectboxes", "input": true, "label": "Select all autoimmune conditions that apply:", "values": [{"label": "Celiac disease", "value": "celiac"}, {"label": "Type 1 diabetes", "value": "type1_diabetes"}, {"label": "<PERSON><PERSON> (SLE)", "value": "lupus"}, {"label": "Rheumatoid arthritis", "value": "rheumatoid_arthritis"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "psoriasis"}, {"label": "Vitiligo", "value": "vitiligo"}, {"label": "Multiple sclerosis", "value": "ms"}, {"label": "Inflammatory bowel disease (<PERSON><PERSON><PERSON>'s or ulcerative colitis)", "value": "ibd"}, {"label": "<PERSON>'s disease", "value": "addisons"}], "tableView": true, "confirm_label": "Autoimmune conditions selected:", "customConditional": "show = data.reason_for_thyroid_testing?.autoimmune === true", "optionsLabelPosition": "right"}, {"key": "autoimmune_diagnosis_timing", "data": {"values": [{"label": "Less than 1 year ago", "value": "lt_1y"}, {"label": "1-5 years ago", "value": "1_5y"}, {"label": "More than 5 years ago", "value": "gt_5y"}, {"label": "Not sure", "value": "unsure"}, {"label": "I have not been diagnosed with an autoimmune condition", "value": "none"}]}, "type": "select", "input": true, "label": "When were you first diagnosed with this?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Timing of autoimmune diagnosis:", "customConditional": "show = data.autoimmune_condition_type_v2 && Object.values(data.autoimmune_condition_type_v2).some(v => v)"}, {"key": "autoimmune_conflict_warning", "html": "<div style='border-left: 4px solid #ffc107; background-color: #fff3cd; padding: 10px; margin-bottom: 10px;'><strong>Warning:</strong> You selected that you have not been diagnosed with an autoimmune condition, but earlier you indicated one or more autoimmune conditions. Please go back and review your selection to ensure it accurately reflects your history.</div>", "type": "content", "input": false, "customConditional": "show = data.autoimmune_diagnosis_timing === 'none' && data.autoimmune_condition_type_v2 && Object.values(data.autoimmune_condition_type_v2).some(v => v)"}, {"key": "heading_thyroid_levels_history", "html": "</br><h4>Prior Thyroid Levels</h4>", "type": "content", "input": false, "tableView": false, "customConditional": "show = data.reason_for_thyroid_testing?.prior_abnormal === true"}, {"key": "prior_thyroid_result", "type": "radio", "input": true, "label": "What were your thyroid levels the last time you were tested?", "values": [{"label": "My TSH was high (suggesting hypothyroidism)", "value": "high_tsh"}, {"label": "My TSH was normal", "value": "normal_tsh"}, {"label": "I don't remember", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Previous TSH result:", "customConditional": "show = data.reason_for_thyroid_testing?.prior_abnormal === true"}, {"key": "thyroid_last_test_timing", "data": {"values": [{"label": "Within the last month", "value": "Within the last month"}, {"label": "1-2 months ago", "value": "1-2 months ago"}, {"label": "2-3 months ago", "value": "2-3 months ago"}, {"label": "3-4 months ago", "value": "3-4 months ago"}, {"label": "4-5 months ago", "value": "4-5 months ago"}, {"label": "5-6 months ago", "value": "5-6 months ago"}, {"label": "6-9 months ago", "value": "6-9 months ago"}, {"label": "9-12 months ago", "value": "9-12 months ago"}, {"label": "1-3 years ago", "value": "1-3 years ago"}, {"label": "More than 3 years ago", "value": "More than 3 years ago"}, {"label": "I don't remember", "value": "I don't remember"}]}, "type": "select", "input": true, "label": "When was your last thyroid blood test completed?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Last thyroid test completed:", "customConditional": "show = data.reason_for_thyroid_testing?.prior_abnormal === true"}, {"key": "heading_thyroid_medication", "html": "</br><h4>Thyroid Medication History</h4><p>If you are currently taking or recently stopped thyroid medication, we'd like to know a bit more about your treatment.</p>", "type": "content", "input": false, "tableView": false, "customConditional": "show = data.reason_for_thyroid_testing?.on_medication === true"}, {"key": "thyroid_medication_type", "type": "selectboxes", "input": true, "label": "Which thyroid medications are you currently taking or have recently stopped?", "values": [{"label": "Levothyroxine (Synthroid, Eltroxin, Euthyrox)", "value": "levothyroxine"}, {"label": "Desiccated thyroid (e.g. <PERSON><PERSON>, <PERSON>our)", "value": "desiccated"}, {"label": "Liothyronine (Cytomel)", "value": "liothyronine"}, {"label": "Other", "value": "other"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Thyroid medications selected:", "customConditional": "show = data.reason_for_thyroid_testing?.on_medication === true", "optionsLabelPosition": "right"}, {"key": "thyroid_medication_start_time", "data": {"values": [{"label": "Within the past 1 week", "value": "within_1_week"}, {"label": "1-2 weeks ago", "value": "1-2 weeks"}, {"label": "2-3 weeks ago", "value": "2_3_weeks"}, {"label": "3-4 weeks ago", "value": "3_4_weeks"}, {"label": "4-6 weeks ago", "value": "4_6_weeks"}, {"label": "6 weeks-3 months ago", "value": "6w_3mo"}, {"label": "3-12 months ago", "value": "3_12mo"}, {"label": "1-3 years ago", "value": "1_3y"}, {"label": "More than 3 years ago", "value": "gt_3y"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "When did you start taking (or last stop taking) thyroid medication?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Start or stop time of thyroid medication:", "customConditional": "show = data.reason_for_thyroid_testing?.on_medication === true"}, {"key": "thyroid_medication_dose_change", "type": "radio", "input": true, "label": "Has your dose changed recently?", "values": [{"label": "Yes — dose was increased", "value": "increased"}, {"label": "Yes — dose was decreased", "value": "decreased"}, {"label": "No — stable dose", "value": "stable"}, {"label": "Not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Change in thyroid medication dose:", "customConditional": "show = data.reason_for_thyroid_testing?.on_medication === true"}, {"key": "heading_family_thyroid_history", "html": "</br><h4>Family History of Thyroid Disease</h4><p>Thyroid issues often run in families. Please let us know who in your family has been affected and what condition they were diagnosed with.</p>", "type": "content", "input": false, "tableView": false, "customConditional": "show = data.reason_for_thyroid_testing?.family_history === true"}, {"key": "family_thyroid_diagnosed_members", "type": "selectboxes", "input": true, "label": "Which family members have been diagnosed with thyroid disease?", "values": [{"label": "Mother", "value": "mother"}, {"label": "Father", "value": "father"}, {"label": "Sister", "value": "sister"}, {"label": "Brother", "value": "brother"}, {"label": "Daughter", "value": "daughter"}, {"label": "Son", "value": "son"}, {"label": "Aunt or Uncle", "value": "aunt_uncle"}, {"label": "Grandparent", "value": "grandparent"}, {"label": "Other", "value": "other"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Family members with thyroid disease:", "customConditional": "show = data.reason_for_thyroid_testing?.family_history === true"}, {"key": "family_thyroid_condition_type", "type": "selectboxes", "input": true, "label": "What type of thyroid disease were they diagnosed with? (Select all that apply)", "values": [{"label": "Hypothyroidism (underactive thyroid)", "value": "hypothyroidism"}, {"label": "Hyperthyroidism (overactive thyroid)", "value": "hyperthyroidism"}, {"label": "<PERSON>' disease", "value": "graves"}, {"label": "<PERSON><PERSON><PERSON>'s thyroiditis", "value": "<PERSON><PERSON><PERSON>"}, {"label": "Thyroid nodules or goitre", "value": "nodules_goitre"}, {"label": "Thyroid cancer", "value": "cancer"}, {"label": "I'm not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Types of thyroid disease in family:", "customConditional": "show = data.reason_for_thyroid_testing?.family_history === true"}, {"key": "family_thyroid_cancer_type", "data": {"values": [{"label": "Papillary thyroid cancer", "value": "papillary"}, {"label": "Follicular thyroid cancer", "value": "follicular"}, {"label": "Medullary thyroid cancer", "value": "medullary"}, {"label": "Anaplastic thyroid cancer", "value": "anaplastic"}, {"label": "I don't know", "value": "unknown"}]}, "type": "select", "input": true, "label": "Do you know what type of thyroid cancer your family member had?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Type of thyroid cancer (if known):", "customConditional": "show = data.family_thyroid_condition_type?.cancer === true"}, {"key": "header_symptoms", "html": "<h2>Symptoms of Hypothyroidism</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "hypothyroid_symptoms", "type": "selectboxes", "input": true, "label": "Are you currently experiencing any of the following symptoms?", "values": [{"label": "Fatigue or low energy", "value": "fatigue"}, {"label": "Cold intolerance (feeling cold when others are comfortable)", "value": "cold_intolerance"}, {"label": "Weight gain without changes in diet or activity", "value": "weight_gain"}, {"label": "Constipation", "value": "constipation"}, {"label": "Dry skin", "value": "dry_skin"}, {"label": "Thinning hair or hair loss", "value": "hair_loss"}, {"label": "Heavy or irregular menstrual periods", "value": "menstrual_irregularity"}, {"label": "Brain fog or difficulty concentrating", "value": "brain_fog"}], "adminFlag": true, "tableView": true, "confirm_label": "I have the following hypothyroid-related symptoms:", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_hypothyroid_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "Please select at least one symptom or 'None of the above'."}, "validate": {"custom": "valid = !!data.none_of_the_above_hypothyroid_symptoms || _.some(_.values(data.hypothyroid_symptoms));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "symptom_clarification_if_none_selected", "rows": 4, "type": "textarea", "input": true, "label": "You selected that you have symptoms, but didn't select any specific ones. Can you describe your symptoms in your own words?", "tableView": true, "autoExpand": true, "placeholder": "e.g., changes in mood, energy, sleep, digestion, etc.", "customConditional": "show = data.reason_for_thyroid_testing?.symptoms === true && data.none_of_the_above_hypothyroid_symptoms === true"}, {"key": "hypothyroid_symptoms_present", "type": "textfield", "input": true, "label": "Patient indicated they have the following hypothyroid-related symptoms:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "I have the following hypothyroid-related symptoms:", "calculateValue": "value = _.join(_.map(_.filter([ { label: 'Fatigue or low energy', value: 'fatigue' }, { label: 'Cold intolerance (feeling cold when others are comfortable)', value: 'cold_intolerance' }, { label: 'Weight gain without changes in diet or activity', value: 'weight_gain' }, { label: 'Constipation', value: 'constipation' }, { label: 'Dry skin', value: 'dry_skin' }, { label: 'Thinning hair or hair loss', value: 'hair_loss' }, { label: 'Heavy or irregular menstrual periods', value: 'menstrual_irregularity' }, { label: 'Brain fog or difficulty concentrating', value: 'brain_fog' }, { label: 'Low mood or depression', value: 'low_mood' } ], function(option) { return data.hypothyroid_symptoms && data.hypothyroid_symptoms[option.value]; }), 'label'), ', ');"}, {"key": "hypothyroid_symptoms_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following hypothyroid-related symptoms:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "I DO NOT have the following hypothyroid-related symptoms:", "calculateValue": "value = _.join(_.map(_.filter([ { label: 'Fatigue or low energy', value: 'fatigue' }, { label: 'Cold intolerance (feeling cold when others are comfortable)', value: 'cold_intolerance' }, { label: 'Weight gain without changes in diet or activity', value: 'weight_gain' }, { label: 'Constipation', value: 'constipation' }, { label: 'Dry skin', value: 'dry_skin' }, { label: 'Thinning hair or hair loss', value: 'hair_loss' }, { label: 'Heavy or irregular menstrual periods', value: 'menstrual_irregularity' }, { label: 'Brain fog or difficulty concentrating', value: 'brain_fog' }, { label: 'Low mood or depression', value: 'low_mood' } ], function(option) { return !(data.hypothyroid_symptoms && data.hypothyroid_symptoms[option.value]); }), 'label'), ', ');"}, {"key": "heading_hypothyroid_symptom_onset", "html": "</br><h2 class='text-center'>Symptom Onset</h2>", "type": "content", "input": false, "tableView": false, "customConditional": "show = data.hypothyroid_symptoms && Object.values(data.hypothyroid_symptoms).includes(true);"}, {"key": "hypothyroid_symptom_onset_pattern", "type": "radio", "input": true, "label": "Did your symptoms all start around the same time, or did they develop on separate days?", "values": [{"label": "All symptoms started around the same time", "value": "same_time"}, {"label": "Symptoms started on separate days", "value": "separate_days"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Onset pattern of symptoms:", "customConditional": "show = data.hypothyroid_symptoms && Object.values(data.hypothyroid_symptoms).filter(Boolean).length > 1"}, {"key": "durations_hypothyroid", "type": "textfield", "input": true, "label": "Durations:", "hidden": true, "disabled": true, "multiple": true, "tableView": false, "clearOnHide": false, "calculateValue": [{"label": "Less than 2 weeks ago", "value": "lt_2_weeks"}, {"label": "2-4 weeks ago", "value": "2-4 weeks"}, {"label": "1 month ago", "value": "1_month"}, {"label": "2 months ago", "value": "2_months"}, {"label": "3 months ago", "value": "3_months"}, {"label": "4 months ago", "value": "4_months"}, {"label": "5 months ago", "value": "5_months"}, {"label": "6 months ago", "value": "6_months"}, {"label": "7-12 months ago", "value": "7_12_months"}, {"label": "1-2 years ago", "value": "1_2_years"}, {"label": "2-3 years ago", "value": "2_3_years"}, {"label": "More than 3 years ago", "value": "gt_3_years"}]}, {"key": "all_hypothyroid_symptoms_start", "data": {"custom": "values = data.durations_hypothyroid"}, "type": "select", "input": true, "label": "When did your symptoms start?", "widget": "html5", "dataSrc": "custom", "validate": {"required": true}, "tableView": true, "confirm_label": "All symptoms started:", "customConditional": "show = data.hypothyroid_symptoms && Object.values(data.hypothyroid_symptoms).filter(Boolean).length >= 2 && data.hypothyroid_symptom_onset_pattern === 'same_time';", "optionsLabelPosition": "right"}, {"key": "heading_fatigue", "html": "<h4 class='mb-n2'>Fatigue</h4>", "type": "content", "input": false, "label": "Fatigue Timing", "tableView": false, "refreshOnChange": true, "customConditional": "show = data.hypothyroid_symptoms?.fatigue === true"}, {"key": "symptom_start_fatigue", "data": {"custom": "values = [{label: `I've always had fatigue and this isn't new for me`, value:`I've always had fatigue and this isn't new for me`}].concat(data.durations_hypothyroid)"}, "type": "select", "input": true, "label": "When did your fatigue start?", "widget": "html5", "dataSrc": "custom", "validate": {"required": true}, "tableView": true, "confirm_label": "Fatigue started:", "customConditional": "show = data.hypothyroid_symptoms?.fatigue === true && (!data.hypothyroid_symptom_onset_pattern || data.hypothyroid_symptom_onset_pattern === 'separate_days')", "optionsLabelPosition": "right"}, {"key": "fatigue_time_of_day", "type": "selectboxes", "input": true, "label": "When is your fatigue usually the worst?", "values": [{"label": "In the morning (even after sleeping)", "value": "In the morning (even after sleeping)"}, {"label": "Mid-day", "value": "Mid-day"}, {"label": "Evening", "value": "Evening"}, {"label": "All day long", "value": "All day long"}], "tableView": true, "confirm_label": "Fatigue is worst:", "customConditional": "show = data.hypothyroid_symptoms?.fatigue === true && (!data.hypothyroid_symptom_onset_pattern || data.hypothyroid_symptom_onset_pattern === 'same_time' || data.hypothyroid_symptom_onset_pattern === 'separate_days')", "optionsLabelPosition": "right"}, {"key": "fatigue_frequency", "type": "radio", "input": true, "label": "How often do you feel unusually tired or low in energy?", "values": [{"label": "Every day", "value": "Every day"}, {"label": "Most days", "value": "Most days"}, {"label": "A few times per week", "value": "A few times per week"}, {"label": "Occasionally", "value": "Occasionally"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Fatigue frequency:", "customConditional": "show = data.fatigue_time_of_day && Object.keys(data.fatigue_time_of_day).length > 0"}, {"key": "fatigue_affects_function", "type": "radio", "input": true, "label": "Does your fatigue interfere with daily tasks, concentration, or activities?", "values": [{"label": "Yes", "value": "Yes"}, {"label": "Somewhat", "value": "Somewhat"}, {"label": "No", "value": "No"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Fatigue impacts daily function:", "customConditional": "show = data.fatigue_frequency"}, {"key": "fatigue_daytime_sleepiness", "type": "radio", "input": true, "label": "Do you often feel sleepy or drowsy during the day?", "values": [{"label": "Yes, almost every day", "value": "Yes, almost every day"}, {"label": "Sometimes", "value": "Sometimes"}, {"label": "No", "value": "No"}], "tableView": true, "confirm_label": "Daytime sleepiness:", "customConditional": "show = data.fatigue_affects_function"}, {"key": "fatigue_osa_risk_factors", "type": "selectboxes", "input": true, "label": "Have you been told or noticed any of the following? (Check all that apply)", "values": [{"label": "Loud snoring", "value": "Loud snoring"}, {"label": "Pauses in breathing during sleep", "value": "Pauses in breathing during sleep"}, {"label": "Waking up gasping or choking", "value": "Waking up gasping or choking"}, {"label": "Morning headaches", "value": "Morning headaches"}, {"label": "Dry mouth on waking", "value": "Dry mouth on waking"}], "validate": {"custom": "valid = !!data.none_of_the_above_fatigue_osa_risk_factors || _.some(_.values(data.fatigue_osa_risk_factors));"}, "tableView": true, "confirm_label": "Sleep-related symptoms:", "customConditional": "show = data.fatigue_affects_function && data.fatigue_daytime_sleepiness && (data.fatigue_daytime_sleepiness === 'Yes, almost every day' || data.fatigue_daytime_sleepiness === 'Sometimes')", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_fatigue_osa_risk_factors", "type": "checkbox", "input": true, "label": "None of the above", "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.fatigue_affects_function && data.fatigue_daytime_sleepiness && (data.fatigue_daytime_sleepiness === 'Yes, almost every day' || data.fatigue_daytime_sleepiness === 'Sometimes')"}, {"key": "fatigue_osa_risk_factors_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following sleep-related symptoms:", "hidden": true, "disabled": true, "multiple": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "I DO NOT have the following sleep-related symptoms:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.fatigue_osa_risk_factors, _.negate(_.identity))), _.startCase), ', '), /_/g, ' ');"}, {"key": "fatigue_osa_evaluation", "type": "radio", "input": true, "label": "Have you ever had a sleep study or been tested for sleep apnea (OSA)?", "values": [{"label": "Yes - and diagnosed with sleep apnea", "value": "diagnosed"}, {"label": "Yes - but results were normal", "value": "normal"}, {"label": "No - never had a sleep study", "value": "never_tested"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Sleep apnea testing history:", "customConditional": "show = data.fatigue_affects_function && data.fatigue_osa_risk_factors && Object.keys(data.fatigue_osa_risk_factors).length > 0"}, {"key": "recommendation_sleep_study_osa", "html": "<div style='border-left: 4px solid #198754; background-color: #d1e7dd; padding: 10px; margin-bottom: 10px;'><strong>Recommendation:</strong> The symptoms you've selected may suggest a condition called <strong>obstructive sleep apnea (OSA)</strong>. We recommend speaking with <strong>your doctor or a local walk-in clinic</strong> about a <strong>sleep study</strong>, which is a test that checks your breathing, oxygen levels, and sleep quality overnight.<br><br>Sleep apnea can cause fatigue, poor concentration, headaches, and long-term health risks if left untreated. A sleep study can help determine if treatment or other options are needed to improve your sleep and energy levels.</div>", "type": "content", "input": false, "customConditional": "show = data.fatigue_affects_function && data.fatigue_osa_evaluation === 'never_tested'"}, {"key": "recommendation_sleep_study_osa_acknowledgement", "type": "radio", "input": true, "label": "Do you understand this recommendation to speak with your doctor or local walk-in clinic about a sleep study?", "values": [{"label": "I understand", "value": "understand"}, {"label": "I do not understand", "value": "do_not_understand"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Understands sleep study recommendation:", "customConditional": "show = data.fatigue_affects_function && data.fatigue_osa_evaluation === 'never_tested'"}, {"key": "heading_cold_intolerance_details", "html": "</br><h4 class='mb-n2'>Cold Intolerance</h4>", "type": "content", "input": false, "label": "Cold Sensitivity Details", "tableView": false, "refreshOnChange": true, "customConditional": "show = data.hypothyroid_symptoms?.cold_intolerance === true"}, {"key": "symptom_start_cold_intolerance", "data": {"custom": "values = [{label: `I've always been sensitive to cold`, value: `I've always been sensitive to cold`}].concat(data.durations_hypothyroid)"}, "type": "select", "input": true, "label": "When did the cold intolerance begin?", "widget": "html5", "dataSrc": "custom", "validate": {"required": true}, "tableView": true, "confirm_label": "Cold sensitivity started:", "customConditional": "show = data.hypothyroid_symptoms?.cold_intolerance === true && (!data.hypothyroid_symptom_onset_pattern || data.hypothyroid_symptom_onset_pattern === 'separate_days')"}, {"key": "cold_intolerance_description", "type": "selectboxes", "input": true, "label": "How does the cold sensitivity affect you?", "values": [{"label": "Always feel colder than others", "value": "Always feel colder than others"}, {"label": "Need more layers or heat than usual", "value": "Need extra layers or heat"}, {"label": "Cold hands and feet most of the time", "value": "Cold extremities"}, {"label": "Other", "value": "Other"}], "tableView": true, "confirm_label": "Cold intolerance description:", "customConditional": "show = data.hypothyroid_symptoms?.cold_intolerance === true"}, {"key": "heading_weight_gain_details", "html": "</br><h4 class='mb-n2'>Weight Gain</h4></br><p><em>Some people confuse being overweight or struggling to lose weight with actual weight gain. Please answer the questions below based on whether you believe your body weight has increased over time — not just difficulty losing it.</em></p>", "type": "content", "input": false, "tableView": false, "refreshOnChange": true, "customConditional": "show = data.hypothyroid_symptoms?.weight_gain === true"}, {"key": "weight_gain_vs_difficulty_losing", "type": "radio", "input": true, "label": "Are you referring to actual weight gain (higher number on the scale), or difficulty losing weight despite effort?", "values": [{"label": "I've gained weight (scale has gone up)", "value": "actual_gain"}, {"label": "I haven't gained weight, but I can't lose it", "value": "difficulty_losing"}, {"label": "Both", "value": "both"}, {"label": "Not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Nature of weight concern:", "customConditional": "show = data.hypothyroid_symptoms?.weight_gain === true"}, {"key": "symptom_start_weight_gain", "data": {"custom": "values = [{label: `I've always struggled with weight gain`, value: `I've always struggled with weight gain`}].concat(data.durations_hypothyroid)"}, "type": "select", "input": true, "label": "When did the weight gain begin?", "widget": "html5", "dataSrc": "custom", "validate": {"required": true}, "tableView": true, "confirm_label": "Weight gain started:", "customConditional": "show = data.hypothyroid_symptoms?.weight_gain === true && (!data.hypothyroid_symptom_onset_pattern || data.hypothyroid_symptom_onset_pattern === 'separate_days')"}, {"key": "weight_gain_description", "type": "selectboxes", "input": true, "label": "How would you describe your weight gain?", "values": [{"label": "Gradual over time", "value": "Gradual over time"}, {"label": "Sudden or unexplained", "value": "Sudden or unexplained"}, {"label": "Weight gain despite healthy diet", "value": "Weight gain despite healthy diet"}, {"label": "Weight gain despite regular exercise", "value": "Weight gain despite regular exercise"}, {"label": "Other", "value": "Other"}], "tableView": true, "confirm_label": "Weight gain description:", "customConditional": "show = data.hypothyroid_symptoms?.weight_gain === true"}, {"key": "weight_gain_amount_and_units", "type": "columns", "columns": [{"pull": 0, "push": 0, "width": 6, "offset": 0, "components": [{"key": "weight_gain_amount", "type": "number", "input": true, "label": "How much weight have you gained?", "validate": {"min": 0, "required": true}, "tableView": true, "confirm_label": "Weight gained:", "customConditional": "show = data.hypothyroid_symptoms?.weight_gain === true && (data.weight_gain_vs_difficulty_losing === 'actual_gain' || data.weight_gain_vs_difficulty_losing === 'both')"}]}, {"pull": 0, "push": 0, "width": 6, "offset": 0, "components": [{"key": "weight_gain_units", "data": {"values": [{"label": "lbs", "value": "lbs"}, {"label": "kg", "value": "kg"}]}, "type": "select", "input": true, "label": "Unit", "widget": "html5", "validate": {"required": true}, "tableView": true, "defaultValue": "lbs", "confirm_label": "Weight gain unit:", "customConditional": "show = data.hypothyroid_symptoms?.weight_gain === true && (data.weight_gain_vs_difficulty_losing === 'actual_gain' || data.weight_gain_vs_difficulty_losing === 'both')"}]}], "customConditional": "show = data.hypothyroid_symptoms?.weight_gain === true && (data.weight_gain_vs_difficulty_losing === 'actual_gain' || data.weight_gain_vs_difficulty_losing === 'both')"}, {"key": "heading_constipation", "html": "</br><h4>Constipation</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.abdominal_gastrointestinal_symptoms && data.abdominal_gastrointestinal_symptoms.constipation === true;"}, {"key": "constipation_frequency", "type": "radio", "input": true, "label": "How often are your bowel movements?", "values": [{"label": "Every 1-2 days", "value": "daily"}, {"label": "Every 3-4 days", "value": "3_4 days"}, {"label": "Less than 2 times per week", "value": "lt_2_week"}, {"label": "Unsure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Bowel movement frequency:", "customConditional": "show = data.abdominal_gastrointestinal_symptoms && data.abdominal_gastrointestinal_symptoms.constipation === true;"}, {"key": "onset_constipation", "data": {"values": [{"label": "Today", "value": "today"}, {"label": "1-3 days ago", "value": "1_3 days"}, {"label": "4-7 days ago", "value": "4-7 days"}, {"label": "1-2 weeks ago", "value": "1-2 weeks"}, {"label": "2-4 weeks ago", "value": "2-4 weeks"}, {"label": "More than 1 month ago (continuous)", "value": "over_1_month"}, {"label": "Comes and goes over the past month", "value": "recurrent_1_month"}, {"label": "Comes and goes over the past year", "value": "recurrent_1_year"}, {"label": "Comes and goes for many years", "value": "recurrent_many_years"}]}, "type": "select", "input": true, "label": "When did the constipation begin?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Constipation started:", "customConditional": "show = data.abdominal_gastrointestinal_symptoms && data.abdominal_gastrointestinal_symptoms.constipation === true;"}, {"key": "constipation_typical_vs_new", "type": "radio", "input": true, "label": "Is this episode of constipation similar to your past experiences, or is it different?", "values": [{"label": "Typical - feels the same as prior episodes", "value": "typical"}, {"label": "Different - new or unusual compared to prior", "value": "different"}, {"label": "Not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.onset_constipation && (data.onset_constipation === 'recurrent_1_month' || data.onset_constipation === 'recurrent_1_year' || data.onset_constipation === 'recurrent_many_years');"}, {"key": "heading_constipation", "html": "</br><h4 class='mb-n2'>Constipation</h4>", "type": "content", "input": false, "label": "Constipation Details", "tableView": false, "refreshOnChange": true, "customConditional": "show = data.hypothyroid_symptoms?.constipation === true"}, {"key": "symptom_start_constipation", "data": {"custom": "values = [{label: `I've always had constipation and this isn't new for me`, value:`I've always had constipation and this isn't new for me`}].concat(data.durations_hypothyroid)"}, "type": "select", "input": true, "label": "When did your constipation start?", "widget": "html5", "dataSrc": "custom", "validate": {"required": true}, "tableView": true, "confirm_label": "Constipation started:", "customConditional": "show = data.hypothyroid_symptoms?.constipation === true && (!data.hypothyroid_symptom_onset_pattern || data.hypothyroid_symptom_onset_pattern === 'separate_days')", "optionsLabelPosition": "right"}, {"key": "constipation_frequency", "type": "radio", "input": true, "label": "How often do you have a bowel movement (poop)?", "values": [{"label": "Every day", "value": "Every day"}, {"label": "Every 2-3 days", "value": "Every 2-3 days"}, {"label": "Once or twice per week", "value": "Once or twice per week"}, {"label": "Less than once per week", "value": "Less than once per week"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Bowel movement frequency:", "customConditional": "show = data.hypothyroid_symptoms?.constipation === true && (!data.hypothyroid_symptom_onset_pattern || data.hypothyroid_symptom_onset_pattern === 'separate_days' || data.hypothyroid_symptom_onset_pattern === 'same_time')"}, {"key": "constipation_stool_type", "type": "selectboxes", "input": true, "label": "What describes your usual stool (poop) when you're constipated?", "values": [{"label": "Very hard or pellet-like", "value": "Very hard or pellet-like"}, {"label": "Dry or cracked", "value": "Dry or cracked"}, {"label": "Small and hard to pass", "value": "Small and hard to pass"}, {"label": "Thin or stringy", "value": "Thin or stringy"}], "tableView": true, "confirm_label": "Constipation stool type:", "customConditional": "show = data.constipation_frequency", "optionsLabelPosition": "right"}, {"key": "constipation_straining", "type": "radio", "input": true, "label": "Do you usually have to push or strain to pass stool (poop)?", "values": [{"label": "Yes, most of the time", "value": "Yes, most of the time"}, {"label": "Sometimes", "value": "Sometimes"}, {"label": "No", "value": "No"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Straining with bowel movements:", "customConditional": "show = data.constipation_stool_type && Object.keys(data.constipation_stool_type).length > 0"}, {"key": "constipation_incomplete", "type": "radio", "input": true, "label": "Do you often feel like you haven't fully emptied your bowels after going?", "values": [{"label": "Yes", "value": "Yes"}, {"label": "Sometimes", "value": "Sometimes"}, {"label": "No", "value": "No"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Incomplete emptying feeling:", "customConditional": "show = data.constipation_straining"}, {"key": "constipation_impact_on_life", "type": "radio", "input": true, "label": "How much does constipation affect your daily life?", "values": [{"label": "Not at all", "value": "Not at all"}, {"label": "Mildly bothersome", "value": "Mildly bothersome"}, {"label": "Moderate impact (affects mood or focus)", "value": "Moderate impact"}, {"label": "Severe (affects sleep, work, or quality of life)", "value": "Severe impact"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Impact of constipation:", "customConditional": "show = data.constipation_incomplete"}, {"key": "constipation_laxative_use", "type": "radio", "input": true, "label": "Do you use anything to help with constipation (including over-the-counter products)?", "values": [{"label": "Yes", "value": "Yes"}, {"label": "No", "value": "No"}, {"label": "Sometimes", "value": "Sometimes"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Laxative or treatment use for constipation:", "customConditional": "show = data.constipation_impact_on_life"}, {"key": "recommendation_constipation_peg_fibre", "html": "<div style='border-left: 4px solid #198754; background-color: #d1e7dd; padding: 10px; margin-bottom: 10px;'><strong>Recommendation:</strong> Increasing dietary fibre — including vegetables, fruit skins, whole grains, chia, flax, or oats — can help manage constipation. You can also safely use a powder called <strong>polyethylene glycol</strong> (brand names include <strong>Restoralax</strong>) to soften stool and improve regularity. It draws water into the bowel and is not habit-forming. This product is available without a prescription at any pharmacy (e.g. Shoppers, Rexall, Costco, Walmart, or your local store), and it is <strong>safe to use long-term</strong> if needed. Most people start with <strong>1 scoop (17g)</strong> in a full glass of water daily. It may take 1-3 days to work. If needed, you can increase to 2-3 scoops per day unless you develop diarrhea.</div>", "type": "content", "input": false, "customConditional": "show = !!data.constipation_laxative_use"}, {"key": "recommendation_constipation_peg_fibre_acknowledgement", "type": "radio", "input": true, "label": "Do you understand this recommendation about fibre and Restoralax use for constipation?", "values": [{"label": "I understand", "value": "I understand"}, {"label": "I do not understand", "value": "I do not understand"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Understands PEG and fibre recommendation:", "customConditional": "show = !!data.constipation_laxative_use"}, {"key": "heading_colon_cancer_and_bleeding", "html": "</br><h4>Colon Cancer Risk and Rectal Bleeding</h4>", "type": "content", "input": false, "tableView": false, "customConditional": "show = data.hypothyroid_symptoms?.constipation === true"}, {"key": "family_history_colon_cancer", "type": "selectboxes", "input": true, "label": "Do you have a family history of colon cancer?", "values": [{"label": "Parent", "value": "Parent"}, {"label": "Sibling", "value": "Sibling"}, {"label": "Grandparent", "value": "Grandparent"}, {"label": "Aunt or uncle", "value": "Aunt or uncle"}, {"label": "Cousin", "value": "Cousin"}], "validate": {"custom": "valid = !!data.none_of_the_above_family_history_colon_cancer || _.some(_.values(data.family_history_colon_cancer));"}, "tableView": true, "confirm_label": "Family history of colon cancer:", "customConditional": "show = data.hypothyroid_symptoms?.constipation === true", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_family_history_colon_cancer", "type": "checkbox", "input": true, "label": "I don't have any family members that I know of with colon cancer", "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.hypothyroid_symptoms?.constipation === true"}, {"key": "age_youngest_colon_cancer_diagnosis", "data": {"values": [{"label": "Under 40", "value": "Under 40"}, {"label": "40-49", "value": "40-49"}, {"label": "50-59", "value": "50-59"}, {"label": "60 or older", "value": "60 or older"}, {"label": "I don't know", "value": "I don't know"}]}, "type": "select", "input": true, "label": "How old was the youngest family member when they were diagnosed with colon cancer?", "widget": "html5", "validate": {"required": true}, "confirm_label": "Youngest age at family diagnosis:", "customConditional": "show = _.some(data.family_history_colon_cancer, Boolean) && data.hypothyroid_symptoms?.constipation === true"}, {"key": "recommendation_colonoscopy_early_due_to_family_history", "html": "<div style='border-left: 4px solid #198754; background-color: #d1e7dd; padding: 10px; margin-bottom: 10px;'><strong>Recommendation:</strong> If you have a <strong>first-degree relative</strong> (parent or sibling) diagnosed with colon cancer, screening guidelines recommend you begin colonoscopy <strong>10 years before their age at diagnosis</strong>. For example, if your mother was diagnosed at age 52, you should aim to have your first colonoscopy by age 42. This helps catch changes earlier and may significantly reduce risk. If you're not sure when your relative was diagnosed, speak with your doctor to discuss the best time to begin screening.</div>", "type": "content", "input": false, "customConditional": "show = (data.family_history_colon_cancer?.Parent === true || data.family_history_colon_cancer?.Sibling === true) && data.hypothyroid_symptoms?.constipation === true"}, {"key": "recommendation_colonoscopy_early_acknowledgement", "type": "radio", "input": true, "label": "Do you understand this recommendation about starting colonoscopy earlier due to family history?", "values": [{"label": "I understand", "value": "I understand"}, {"label": "I do not understand", "value": "I do not understand"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Understands early colonoscopy recommendation:", "customConditional": "show = (data.family_history_colon_cancer?.Parent === true || data.family_history_colon_cancer?.Sibling === true) && data.hypothyroid_symptoms?.constipation === true"}, {"key": "rectal_bleeding_symptoms", "type": "selectboxes", "input": true, "label": "Have you noticed any of the following rectal bleeding symptoms?", "values": [{"label": "Bright red blood on toilet paper", "value": "Bright red blood on toilet paper"}, {"label": "Blood mixed with stool", "value": "Blood mixed with stool"}, {"label": "Dark or tarry stool", "value": "Dark or tarry stool"}, {"label": "Blood dripping into the toilet", "value": "Blood dripping into the toilet"}], "validate": {"custom": "valid = !!data.none_of_the_above_rectal_bleeding_symptoms || _.some(data.rectal_bleeding_symptoms, <PERSON><PERSON><PERSON>);"}, "tableView": true, "confirm_label": "Rectal bleeding symptoms:", "customConditional": "show = data.hypothyroid_symptoms?.constipation === true", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_rectal_bleeding_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.hypothyroid_symptoms?.constipation === true"}, {"key": "rectal_bleeding_symptoms_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following rectal bleeding symptoms:", "hidden": true, "disabled": true, "multiple": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "I DO NOT have the following rectal bleeding symptoms:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.rectal_bleeding_symptoms, _.negate(_.identity))), _.startCase), ', '), /_/g, ' ');"}, {"key": "recommendation_constipation_exam_colonoscopy", "html": "<div style='border-left: 4px solid #198754; background-color: #d1e7dd; padding: 10px; margin-bottom: 10px;'><strong>Recommendation:</strong> Any <strong>new or persistent constipation</strong> should be assessed in-person by a healthcare provider. While it is often due to diet or dehydration, constipation that does not improve with <strong>increased fibre and fluid intake</strong> may require further testing, including a <strong>colonoscopy</strong>. If your constipation improves with dietary changes, further investigations may not be necessary. We recommend a physical exam to help determine the best next steps.</div>", "type": "content", "input": false, "customConditional": "show = data.hypothyroid_symptoms?.constipation === true && data.none_of_the_above_rectal_bleeding_symptoms === true"}, {"key": "recommendation_constipation_exam_colonoscopy_acknowledgement", "type": "radio", "input": true, "label": "Do you understand this recommendation about seeing a provider and when colonoscopy may be required for constipation?", "values": [{"label": "I understand", "value": "I understand"}, {"label": "I do not understand", "value": "I do not understand"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Understands colonoscopy recommendation for constipation:", "customConditional": "show = data.hypothyroid_symptoms?.constipation === true && data.none_of_the_above_rectal_bleeding_symptoms === true"}, {"key": "rectal_bleeding_prior_diagnosis", "type": "selectboxes", "input": true, "label": "Have you ever been given a diagnosis to explain your rectal bleeding?", "values": [{"label": "Hemorrhoids", "value": "Hemorrhoids"}, {"label": "Anal fissure", "value": "Anal fissure"}, {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"label": "Inflammatory bowel disease (e.g. <PERSON><PERSON>'s, ulcerative colitis)", "value": "IBD"}, {"label": "Other", "value": "Other"}, {"label": "No diagnosis was made", "value": "None"}], "tableView": true, "confirm_label": "Prior diagnosis for bleeding:", "customConditional": "show = data.rectal_bleeding_symptoms && _.some(data.rectal_bleeding_symptoms, Boolean) && data.hypothyroid_symptoms?.constipation === true", "optionsLabelPosition": "right"}, {"key": "rectal_bleeding_first_onset", "data": {"values": [{"label": "Less than 1 week ago", "value": "Less than 1 week ago"}, {"label": "1-4 weeks ago", "value": "1-4 weeks ago"}, {"label": "1-3 months ago", "value": "1-3 months ago"}, {"label": "4-12 months ago", "value": "4-12 months ago"}, {"label": "Over 1 year ago (comes and goes)", "value": "Over 1 year ago (intermittent)"}, {"label": "Over 1 year ago (continuous)", "value": "Over 1 year ago (continuous)"}, {"label": "I don't remember", "value": "I don't remember"}]}, "type": "select", "input": true, "label": "When did you first notice rectal bleeding?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Bleeding started:", "customConditional": "show = data.rectal_bleeding_symptoms && _.some(data.rectal_bleeding_symptoms, Boolean) && data.hypothyroid_symptoms?.constipation === true"}, {"key": "rectal_bleeding_frequency", "type": "radio", "input": true, "label": "How often do you experience rectal bleeding?", "values": [{"label": "Only once", "value": "Only once"}, {"label": "Occasionally (every few weeks)", "value": "Occasionally"}, {"label": "Frequently (weekly or more)", "value": "Frequently"}, {"label": "Constant or near daily", "value": "Constant"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Bleeding frequency:", "customConditional": "show = data.rectal_bleeding_symptoms && _.some(data.rectal_bleeding_symptoms, Boolean) && data.hypothyroid_symptoms?.constipation === true"}, {"key": "recommendation_rectal_bleeding_urgent", "html": "<div style='border-left: 4px solid #dc3545; background-color: #f8d7da; padding: 10px; margin-bottom: 10px;'><strong>Urgent Recommendation:</strong> Because you are experiencing rectal bleeding that is new or frequent, we recommend you speak with a healthcare provider in-person. Bright red blood, especially when combined with constipation or changes in bowel habits, should be assessed with a physical exam and may require further testing such as a colonoscopy. Please seek care through your family doctor, walk-in clinic, or emergency department if symptoms worsen.</div>", "type": "content", "input": false, "customConditional": "show = data.hypothyroid_symptoms?.constipation === true && data.rectal_bleeding_symptoms && _.some(data.rectal_bleeding_symptoms, Boolean) && (data.rectal_bleeding_frequency === 'Frequently' || data.rectal_bleeding_frequency === 'Constant' || ['Less than 1 week ago', '1-4 weeks ago'].includes(data.rectal_bleeding_first_onset))"}, {"key": "recommendation_rectal_bleeding_urgent_acknowledgement", "type": "radio", "input": true, "label": "Do you understand this recommendation about seeking in-person care for rectal bleeding?", "values": [{"label": "I understand", "value": "I understand"}, {"label": "I do not understand", "value": "I do not understand"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Understands urgency of rectal bleeding:", "customConditional": "show = data.hypothyroid_symptoms?.constipation === true && data.rectal_bleeding_symptoms && _.some(data.rectal_bleeding_symptoms, Boolean) && (data.rectal_bleeding_frequency === 'Frequently' || data.rectal_bleeding_frequency === 'Constant' || ['Less than 1 week ago', '1-4 weeks ago'].includes(data.rectal_bleeding_first_onset))"}, {"key": "heading_dry_skin_details", "html": "</br><h4 class='mb-n2'>Dry Skin</h4>", "type": "content", "input": false, "tableView": false, "refreshOnChange": true, "customConditional": "show = data.hypothyroid_symptoms?.dry_skin === true"}, {"key": "symptom_start_dry_skin", "data": {"custom": "values = [{label: `My skin has always been dry`, value: `My skin has always been dry`}].concat(data.durations_hypothyroid)"}, "type": "select", "input": true, "label": "When did the dry skin begin or worsen?", "widget": "html5", "dataSrc": "custom", "validate": {"required": true}, "tableView": true, "confirm_label": "Dry skin started:", "customConditional": "show = data.hypothyroid_symptoms?.dry_skin === true && (!data.hypothyroid_symptom_onset_pattern || data.hypothyroid_symptom_onset_pattern === 'separate_days')"}, {"key": "dry_skin_description", "type": "selectboxes", "input": true, "label": "Where do you notice dry skin the most?", "values": [{"label": "Hands or feet", "value": "Hands or feet"}, {"label": "Legs or arms", "value": "<PERSON>bs"}, {"label": "Face or scalp", "value": "Face or scalp"}, {"label": "Widespread", "value": "Widespread"}, {"label": "Other", "value": "Other"}], "tableView": true, "confirm_label": "Dry skin location:", "customConditional": "show = data.hypothyroid_symptoms?.dry_skin === true"}, {"key": "heading_hair_loss_details", "html": "</br><h4 class='mb-n2'>Hair Thinning or Hair Loss</h4>", "type": "content", "input": false, "tableView": false, "refreshOnChange": true, "customConditional": "show = data.hypothyroid_symptoms?.hair_loss === true"}, {"key": "symptom_start_hair_loss", "data": {"custom": "values = [{label: `Hair has always been thin or fine`, value: `Hair has always been thin or fine`}].concat(data.durations_hypothyroid)"}, "type": "select", "input": true, "label": "When did you first notice hair thinning or loss?", "widget": "html5", "dataSrc": "custom", "validate": {"required": true}, "tableView": true, "confirm_label": "Hair thinning started:", "customConditional": "show = data.hypothyroid_symptoms?.hair_loss === true && (!data.hypothyroid_symptom_onset_pattern || data.hypothyroid_symptom_onset_pattern === 'separate_days')"}, {"key": "hair_loss_description", "type": "selectboxes", "input": true, "label": "What type of hair loss have you noticed?", "values": [{"label": "Overall hair thinning", "value": "Diffuse thinning"}, {"label": "Bald spots or patches", "value": "<PERSON><PERSON>"}, {"label": "Increased hair shedding", "value": "Shedding"}, {"label": "Thinning at the crown or temples", "value": "Crown/temples"}, {"label": "Eyebrow thinning", "value": "Eyebrows"}, {"label": "Other", "value": "Other"}], "tableView": true, "confirm_label": "Hair loss description:", "customConditional": "show = data.hypothyroid_symptoms?.hair_loss === true"}, {"key": "hair_loss_location", "type": "selectboxes", "input": true, "label": "Where is the hair loss located? (Select all that apply)", "values": [{"label": "Scalp - diffuse or generalized", "value": "Scalp - diffuse"}, {"label": "Scalp - specific area or patch", "value": "Scalp - localized"}, {"label": "Eyebrows", "value": "Eyebrows"}, {"label": "Body (arms, legs, chest, etc.)", "value": "Body"}, {"label": "Eyelashes", "value": "Eyelashes"}, {"label": "Other", "value": "Other"}], "tableView": true, "confirm_label": "Hair loss location:", "customConditional": "show = data.hair_loss_description && Object.keys(data.hair_loss_description).length > 0"}, {"key": "hair_loss_acceleration", "type": "radio", "input": true, "label": "Has the hair loss been accelerating or worsening recently?", "values": [{"label": "Yes", "value": "Yes"}, {"label": "No", "value": "No"}, {"label": "Not sure", "value": "Not sure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Hair loss accelerating:", "customConditional": "show = data.hair_loss_description && Object.keys(data.hair_loss_description).length > 0"}, {"key": "hair_loss_circular_appearance", "type": "radio", "input": true, "label": "Are any areas of hair loss circular or patch-like in appearance?", "values": [{"label": "Yes", "value": "Yes"}, {"label": "No", "value": "No"}, {"label": "Not sure", "value": "Not sure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Circular or patchy areas:", "customConditional": "show = data.hair_loss_description && Object.keys(data.hair_loss_description).length > 0"}, {"key": "heading_menstrual_irregularity_details", "html": "</br><h4 class='mb-n2'>Menstrual Irregularities</h4>", "type": "content", "input": false, "tableView": false, "refreshOnChange": true, "customConditional": "show = data.hypothyroid_symptoms?.menstrual_irregularity === true"}, {"key": "symptom_start_menstrual_irregularity", "data": {"custom": "values = [{label: `My cycle has always been irregular`, value: `My cycle has always been irregular`}].concat(data.durations_hypothyroid)"}, "type": "select", "input": true, "label": "When did your menstrual changes begin?", "widget": "html5", "dataSrc": "custom", "validate": {"required": true}, "tableView": true, "confirm_label": "Menstrual irregularity started:", "customConditional": "show = data.hypothyroid_symptoms?.menstrual_irregularity === true && (!data.hypothyroid_symptom_onset_pattern || data.hypothyroid_symptom_onset_pattern === 'separate_days')"}, {"key": "menstrual_irregularity_pattern", "type": "selectboxes", "input": true, "label": "What menstrual changes have you experienced?", "values": [{"label": "Heavier periods", "value": "Heavier periods"}, {"label": "Longer periods", "value": "Longer periods"}, {"label": "Shorter cycles (more frequent periods)", "value": "Shorter cycles"}, {"label": "Missed periods", "value": "Missed periods"}, {"label": "Unpredictable cycle", "value": "Unpredictable cycle"}, {"label": "Bleeding between periods", "value": "Intermenstrual bleeding"}, {"label": "Other", "value": "Other"}], "tableView": true, "confirm_label": "Menstrual changes:", "customConditional": "show = data.hypothyroid_symptoms?.menstrual_irregularity === true"}, {"key": "menstrual_ultrasound_done", "type": "radio", "input": true, "label": "Have you ever had a pelvic ultrasound to investigate your menstrual irregularities?", "values": [{"label": "Yes", "value": "Yes"}, {"label": "No", "value": "No"}, {"label": "Not sure", "value": "Not sure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Pelvic ultrasound completed:", "customConditional": "show = data.menstrual_irregularity_pattern && Object.keys(data.menstrual_irregularity_pattern).length > 0"}, {"key": "seen_gynecologist", "type": "radio", "input": true, "label": "Have you ever been evaluated by a gynecologist for these menstrual symptoms?", "values": [{"label": "Yes", "value": "Yes"}, {"label": "No", "value": "No"}, {"label": "Not sure", "value": "Not sure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Seen gynecologist for symptoms:", "customConditional": "show = data.menstrual_irregularity_pattern && Object.keys(data.menstrual_irregularity_pattern).length > 0"}, {"key": "bleeding_related_to_intercourse", "type": "radio", "input": true, "label": "Have you noticed bleeding that happens during or after intercourse?", "values": [{"label": "Yes", "value": "Yes"}, {"label": "No", "value": "No"}, {"label": "Not sure", "value": "Not sure"}, {"label": "Prefer not to disclose", "value": "Prefer not to disclose"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Bleeding related to intercourse:", "customConditional": "show = data.menstrual_irregularity_pattern && Object.keys(data.menstrual_irregularity_pattern).length > 0"}, {"key": "last_pap_test_timing", "data": {"values": [{"label": "Within the last year", "value": "Within the last year"}, {"label": "1-3 years ago", "value": "1-3 years ago"}, {"label": "More than 3 years ago", "value": "More than 3 years ago"}, {"label": "Never", "value": "Never"}, {"label": "Not sure", "value": "Not sure"}]}, "type": "select", "input": true, "label": "When was your last Pap test (cervical cancer screening)?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Timing of last Pap test:", "customConditional": "show = data.menstrual_irregularity_pattern && Object.keys(data.menstrual_irregularity_pattern).length > 0"}, {"key": "last_pap_test_result", "type": "radio", "input": true, "label": "Do you remember the result of your last Pap test?", "values": [{"label": "Normal", "value": "Normal"}, {"label": "Abnormal", "value": "Abnormal"}, {"label": "Not sure", "value": "Not sure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Last Pap test result:", "customConditional": "show = data.last_pap_test_timing && data.last_pap_test_timing !== 'Never'"}, {"key": "recommendation_pap_due_to_no_history", "html": "<div style='border-left: 4px solid #0d6efd; background-color: #e7f1ff; padding: 10px; margin-bottom: 10px;'><strong>Recommendation:</strong> Since you have <strong>not had a Pap test</strong> and have been <strong>sexually active at any point in your life</strong> — including vaginal, oral, or anal sex — we recommend having a <strong>Pap test and in-person cervical exam</strong> performed. Pap tests help screen for changes in the cervix that could lead to cervical cancer, and are important even if you're not currently sexually active or have only had non-penetrative contact.<br><br>An in-person cervical exam allows a healthcare provider to visually assess the cervix for signs of inflammation, polyps, or other abnormalities that may explain symptoms such as irregular bleeding. These exams are part of routine preventative care and can detect problems early, when they're easier to treat.</div>", "type": "content", "input": false, "customConditional": "show = data.last_pap_test_timing === 'Never'"}, {"key": "recommendation_pap_due_to_no_history_acknowledgement", "type": "radio", "input": true, "label": "Do you understand this recommendation to have a Pap test and cervical exam?", "values": [{"label": "I understand", "value": "I understand"}, {"label": "I do not understand", "value": "I do not understand"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Understands Pap recommendation:", "customConditional": "show = data.last_pap_test_timing === 'Never'"}, {"key": "heading_brain_fog_details", "html": "</br><h4 class='mb-n2'>Brain Fog or Difficulty Concentrating</h4>", "type": "content", "input": false, "tableView": false, "refreshOnChange": true, "customConditional": "show = data.hypothyroid_symptoms?.brain_fog === true"}, {"key": "symptom_start_brain_fog", "data": {"custom": "values = [{label: `I've always had trouble with focus or attention`, value: `I've always had trouble with focus or attention`}].concat(data.durations_hypothyroid)"}, "type": "select", "input": true, "label": "When did your brain fog or concentration issues begin?", "widget": "html5", "dataSrc": "custom", "validate": {"required": true}, "tableView": true, "confirm_label": "Brain fog started:", "customConditional": "show = data.hypothyroid_symptoms?.brain_fog === true && (!data.hypothyroid_symptom_onset_pattern || data.hypothyroid_symptom_onset_pattern === 'separate_days')"}, {"key": "brain_fog_description", "type": "selectboxes", "input": true, "label": "How would you describe your brain fog or mental slowing?", "values": [{"label": "Trouble concentrating", "value": "Trouble concentrating"}, {"label": "Forgetfulness or memory issues", "value": "Forgetfulness"}, {"label": "Mentally 'foggy' or unclear", "value": "Foggy thinking"}, {"label": "Slower thinking or response time", "value": "Slow cognition"}, {"label": "Other", "value": "Other"}], "tableView": true, "confirm_label": "Brain fog description:", "customConditional": "show = data.hypothyroid_symptoms?.brain_fog === true"}, {"key": "heading_hypothyroid_triggers_life", "html": "</br><h4>Work, School, and Personal Life Events</h4><p>Stressful life changes can sometimes affect thyroid health. Please let us know if any of the following occurred before or around the time your symptoms began.</p>", "type": "content", "input": false, "tableView": false, "customConditional": "show = data.hypothyroid_symptoms && Object.values(data.hypothyroid_symptoms).includes(true) && (!data.hypothyroid_symptom_onset_pattern || data.hypothyroid_symptom_onset_pattern === 'same_time');"}, {"key": "hypothyroid_triggers_life", "type": "selectboxes", "input": true, "label": "Life Stressors (select all that apply)", "values": [{"label": "Increased stress at work or school", "value": "work_stress"}, {"label": "Major life transition (e.g. job loss, new job, divorce)", "value": "transition"}, {"label": "Financial stress", "value": "financial"}, {"label": "Grief or illness in the family", "value": "grief"}, {"label": "Other significant personal stress", "value": "other"}], "tableView": true, "confirm_label": "Work/Personal Stress Triggers:", "customConditional": "show = data.hypothyroid_symptoms && Object.values(data.hypothyroid_symptoms).includes(true) && (!data.hypothyroid_symptom_onset_pattern || data.hypothyroid_symptom_onset_pattern === 'same_time');"}, {"key": "none_of_the_above_hypothyroid_triggers_life", "type": "checkbox", "input": true, "label": "None of the above", "validate": {"custom": "valid = !!data.none_of_the_above_hypothyroid_triggers_life || _.some(_.values(data.hypothyroid_triggers_life));"}, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.hypothyroid_symptoms && Object.values(data.hypothyroid_symptoms).includes(true) && (!data.hypothyroid_symptom_onset_pattern || data.hypothyroid_symptom_onset_pattern === 'same_time');"}, {"key": "hypothyroid_triggers_life_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following life-related triggers:", "hidden": true, "disabled": true, "tableView": true, "confirm_label": "I DO NOT have the following life-related triggers:", "calculateValue": "value = _.replace(_.join(_.map(_.difference(_.keys(_.pickBy(data.hypothyroid_triggers_life, _.negate(_.identity))), ['other']), _.startCase), ', '), /_/g, ' ');"}, {"key": "heading_hypothyroid_triggers_health", "html": "</br><h4>Health Events and Medical History</h4><p>Certain illnesses, medications, or chronic conditions may affect thyroid function. Please let us know if any of the following apply to you.</p>", "type": "content", "input": false, "tableView": false, "customConditional": "show = data.hypothyroid_symptoms && Object.values(data.hypothyroid_symptoms).includes(true) && (!data.hypothyroid_symptom_onset_pattern || data.hypothyroid_symptom_onset_pattern === 'same_time');"}, {"key": "hypothyroid_triggers_health", "type": "selectboxes", "input": true, "label": "Health Triggers (select all that apply)", "values": [{"label": "Recent illness or viral infection", "value": "recent_illness"}, {"label": "Chronic pain or inflammatory condition", "value": "chronic_pain"}, {"label": "Mental health changes (e.g. depression, anxiety)", "value": "mental_health"}, {"label": "New or recent medication started", "value": "new_medication"}, {"label": "Hospitalization or surgery", "value": "hospitalization"}], "tableView": true, "confirm_label": "Health Triggers:", "customConditional": "show = data.hypothyroid_symptoms && Object.values(data.hypothyroid_symptoms).includes(true) && (!data.hypothyroid_symptom_onset_pattern || data.hypothyroid_symptom_onset_pattern === 'same_time');"}, {"key": "none_of_the_above_hypothyroid_triggers_health", "type": "checkbox", "input": true, "label": "None of the above", "validate": {"custom": "valid = !!data.none_of_the_above_hypothyroid_triggers_health || _.some(_.values(data.hypothyroid_triggers_health));"}, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.hypothyroid_symptoms && Object.values(data.hypothyroid_symptoms).includes(true) && (!data.hypothyroid_symptom_onset_pattern || data.hypothyroid_symptom_onset_pattern === 'same_time');"}, {"key": "hypothyroid_triggers_health_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following health-related triggers:", "hidden": true, "disabled": true, "tableView": true, "confirm_label": "I DO NOT have the following health-related triggers:", "calculateValue": "value = _.replace(_.join(_.map(_.difference(_.keys(_.pickBy(data.hypothyroid_triggers_health, _.negate(_.identity))), ['other']), _.startCase), ', '), /_/g, ' ');"}, {"key": "heading_hypothyroid_triggers_lifestyle", "html": "</br><h4>Lifestyle and Environmental Factors</h4><p>Changes in your lifestyle, diet, or sleep may influence how you feel. Please let us know if any of these apply around the time your symptoms began.</p>", "type": "content", "input": false, "tableView": false, "customConditional": "show = data.hypothyroid_symptoms && Object.values(data.hypothyroid_symptoms).includes(true) && (!data.hypothyroid_symptom_onset_pattern || data.hypothyroid_symptom_onset_pattern === 'same_time');"}, {"key": "hypothyroid_triggers_lifestyle", "type": "selectboxes", "input": true, "label": "Lifestyle or Environmental Triggers (select all that apply)", "values": [{"label": "Change in sleep schedule or chronic sleep disruption", "value": "sleep_disruption"}, {"label": "Recent weight gain", "value": "weight_gain"}, {"label": "Dietary changes (e.g. low-carb, keto, or vegan)", "value": "dietary_change"}, {"label": "Poor or unbalanced nutrition", "value": "poor_diet"}, {"label": "Stopped exercising", "value": "stopped_exercise"}, {"label": "Other major lifestyle change", "value": "other"}], "tableView": true, "confirm_label": "Lifestyle/Environmental Triggers:", "customConditional": "show = data.hypothyroid_symptoms && Object.values(data.hypothyroid_symptoms).includes(true) && (!data.hypothyroid_symptom_onset_pattern || data.hypothyroid_symptom_onset_pattern === 'same_time');", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_hypothyroid_triggers_lifestyle", "type": "checkbox", "input": true, "label": "None of the above", "validate": {"custom": "valid = !!data.none_of_the_above_hypothyroid_triggers_lifestyle || _.some(_.values(data.hypothyroid_triggers_lifestyle));"}, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.hypothyroid_symptoms && Object.values(data.hypothyroid_symptoms).includes(true) && (!data.hypothyroid_symptom_onset_pattern || data.hypothyroid_symptom_onset_pattern === 'same_time');"}, {"key": "heading_symptoms", "html": "</br><h2>General Symptoms</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "heading_heart_health", "html": "</br><h4>Heart Health</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "cardiovascular_symptoms", "type": "selectboxes", "input": true, "label": "Do you have any of the following cardiovascular symptoms?", "values": [{"label": "Chest pain, tightness or discomfort", "value": "chest_pain", "shortcut": ""}, {"label": "Palpitations", "value": "palpitations", "shortcut": ""}, {"label": "Swelling in the legs, ankles, or feet", "value": "swelling", "shortcut": ""}, {"label": "Dizziness or fainting", "value": "dizziness", "shortcut": ""}], "adminFlag": true, "tableView": true, "confirm_label": "Cardiovascular symptoms:", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_cardiovascular_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = !!data.none_of_the_above_cardiovascular_symptoms || _.some(_.values(data.cardiovascular_symptoms));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "cardiac_symptoms_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following cardiac symptoms:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "I DO NOT have the following heart related symptoms:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.cardiovascular_symptoms, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "heading_chest_pain", "html": "</br><h4>Chest Pain</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.chest_pain === true;"}, {"key": "chest_pain_onset", "data": {"values": [{"label": "Today", "value": "today"}, {"label": "1 day ago", "value": "1 day"}, {"label": "2 days ago", "value": "2 days"}, {"label": "3 days ago", "value": "3 days"}, {"label": "4 days ago", "value": "4 days"}, {"label": "5 days ago", "value": "5 days"}, {"label": "6 days ago", "value": "6 days"}, {"label": "7 days ago", "value": "7 days"}, {"label": "1-2 weeks ago", "value": "1-2 weeks"}, {"label": "2-4 weeks ago", "value": "2-4 weeks"}, {"label": "1-3 months ago", "value": "1-2 months"}, {"label": "More than 3 months ago", "value": "3+ months"}]}, "type": "select", "input": true, "label": "When did the chest pain or discomfort start?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Chest pain started:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.chest_pain === true;"}, {"key": "chest_pain_triggers", "type": "selectboxes", "input": true, "label": "What brings the chest pain on or makes it worse?", "values": [{"label": "Physical activity or exertion", "value": "exertion"}, {"label": "Stress or anxiety", "value": "stress"}, {"label": "Eating a heavy meal", "value": "meal"}, {"label": "Lying down", "value": "lying"}, {"label": "Breathing deeply or coughing", "value": "breathing"}, {"label": "Unknown or unpredictable", "value": "unknown"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Chest pain triggers:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.chest_pain === true;"}, {"key": "chest_pain_relievers", "type": "selectboxes", "input": true, "label": "What makes the chest pain better?", "values": [{"label": "Rest", "value": "rest"}, {"label": "Lying down", "value": "lying"}, {"label": "Standing upright", "value": "standing"}, {"label": "Medication (e.g., nitroglycerin, pain relievers)", "value": "medication"}, {"label": "Nothing helps", "value": "nothing"}, {"label": "Unsure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Chest pain relief:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.chest_pain === true;"}, {"key": "chest_pain_character", "type": "radio", "input": true, "label": "How would you describe the pain?", "values": [{"label": "<PERSON>", "value": "sharp"}, {"label": "Dull/aching", "value": "dull"}, {"label": "Tight/pressure-like", "value": "pressure"}, {"label": "Burning", "value": "burning"}, {"label": "Stabbing", "value": "stabbing"}, {"label": "Other", "value": "other"}], "confirm_label": "Chest pain character:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.chest_pain === true;"}, {"key": "chest_pain_location", "type": "selectboxes", "input": true, "label": "Where is the pain located?", "values": [{"label": "Centre of chest", "value": "centre_chest"}, {"label": "Left side of chest", "value": "left_chest"}, {"label": "Right side of chest", "value": "right_chest"}, {"label": "Upper chest or sternum", "value": "upper_chest"}, {"label": "Other", "value": "other"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Chest pain location:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.chest_pain === true;"}, {"key": "chest_pain_radiation", "type": "selectboxes", "input": true, "label": "Does the pain radiate to any of the following areas?", "values": [{"label": "Left arm", "value": "left_arm"}, {"label": "Right arm", "value": "right_arm"}, {"label": "<PERSON><PERSON>", "value": "jaw"}, {"label": "Neck", "value": "neck"}, {"label": "Back", "value": "back"}, {"label": "No radiation", "value": "none"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Chest pain radiation:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.chest_pain === true;"}, {"key": "chest_pain_pattern", "type": "radio", "input": true, "label": "Is the chest pain constant or does it come and go?", "values": [{"label": "Constant", "value": "constant"}, {"label": "Comes and goes (intermittent)", "value": "intermittent"}, {"label": "Unsure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Chest pain pattern:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.chest_pain === true;"}, {"key": "heading_palpitations", "html": "</br><h4>Palpitations</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.palpitations === true;"}, {"key": "palpitations_onset", "data": {"values": [{"label": "Today", "value": "today"}, {"label": "1 day ago", "value": "1 day"}, {"label": "2-3 days ago", "value": "2-3 days"}, {"label": "4-7 days ago", "value": "4-7 days"}, {"label": "1-2 weeks ago", "value": "1-2 weeks"}, {"label": "2-4 weeks ago", "value": "2-4 weeks"}, {"label": "More than a month ago", "value": "1 month +"}]}, "type": "select", "input": true, "label": "When did your palpitations begin?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Palpitations started:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.palpitations === true;"}, {"key": "palpitations_rhythm", "type": "radio", "input": true, "label": "How would you describe the rhythm of your palpitations?", "values": [{"label": "Regular and fast", "value": "regular and fast"}, {"label": "Irregular and fast", "value": "irregular and fast"}, {"label": "Skipped beats", "value": "skipped beats"}, {"label": "Flutters", "value": "flutters"}, {"label": "Other", "value": "other"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Palpitations rhythm:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.palpitations === true;"}, {"key": "palpitations_triggers", "type": "selectboxes", "input": true, "label": "What triggers the palpitations?", "values": [{"label": "Exercise or physical activity", "value": "exercise"}, {"label": "Stress or anxiety", "value": "stress"}, {"label": "Caffeine or stimulants", "value": "caffeine"}, {"label": "Alcohol", "value": "alcohol"}, {"label": "Occur at rest", "value": "rest"}, {"label": "Occur at night", "value": "night"}, {"label": "No clear trigger", "value": "unknown"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Palpitations triggers:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.palpitations === true;"}, {"key": "palpitations_duration", "data": {"values": [{"label": "A few seconds", "value": "seconds"}, {"label": "Less than 5 minutes", "value": "less than 5 minutes"}, {"label": "5-30 minutes", "value": "5-30 minutes"}, {"label": "30 minutes to a few hours", "value": "30 minutes to a few hours"}, {"label": "More than a few hours", "value": "More than a few hours"}, {"label": "Constant", "value": "constant"}, {"label": "Varies", "value": "varies"}]}, "type": "select", "input": true, "label": "How long do the palpitations usually last?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Palpitations duration:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.palpitations === true;"}, {"key": "palpitations_associated_symptoms", "type": "selectboxes", "input": true, "label": "Do you experience any of the following during palpitations?", "values": [{"label": "Dizziness or lightheadedness", "value": "dizziness"}, {"label": "Shortness of breath", "value": "sob"}, {"label": "Chest pain", "value": "chest_pain"}, {"label": "Sweating", "value": "sweating"}, {"label": "Fainting or near-fainting", "value": "fainting"}, {"label": "No other symptoms", "value": "none"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Palpitations associated symptoms:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.palpitations === true;"}, {"key": "heading_swelling", "html": "</br><h4>Swelling</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.swelling === true;"}, {"key": "swelling_location", "type": "selectboxes", "input": true, "label": "Where is the swelling located?", "values": [{"label": "Feet", "value": "feet"}, {"label": "<PERSON><PERSON>", "value": "ankles"}, {"label": "Lower legs", "value": "lower_legs"}, {"label": "Thighs", "value": "thighs"}, {"label": "Abdomen", "value": "abdomen"}, {"label": "Other", "value": "other"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Swelling location:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.swelling === true;"}, {"key": "swelling_sidedness", "type": "radio", "input": true, "label": "Is the swelling on one side or both sides?", "values": [{"label": "Both sides", "value": "Both sides"}, {"label": "One side only", "value": "One side only"}, {"label": "Varies", "value": "varies"}, {"label": "Unsure", "value": "unsure"}], "tableView": true, "confirm_label": "Swelling sidedness:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.swelling === true;"}, {"key": "swelling_onset", "data": {"values": [{"label": "Today", "value": "today"}, {"label": "1 day ago", "value": "1 day"}, {"label": "2-3 days ago", "value": "2-3 days"}, {"label": "4-7 days ago", "value": "4-7 days"}, {"label": "1-2 weeks ago", "value": "1-2 weeks"}, {"label": "More than 2 weeks ago", "value": "2_plus_weeks"}]}, "type": "select", "input": true, "label": "When did the swelling begin?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Swelling started:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.swelling === true;"}, {"key": "swelling_timing", "type": "radio", "input": true, "label": "When is the swelling most noticeable?", "values": [{"label": "By the end of the day", "value": "evening"}, {"label": "In the morning", "value": "morning"}, {"label": "Constant throughout the day", "value": "constant"}, {"label": "Varies day to day", "value": "varies"}, {"label": "Unsure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Swelling timing:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.swelling === true;"}, {"key": "swelling_pitting", "type": "radio", "input": true, "label": "When you press on the swollen area, does it leave a dent (pitting)?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "unsure"}], "tableView": true, "confirm_label": "Swelling pitting:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.swelling === true;"}, {"key": "heading_dizziness", "html": "</br><h4>Dizziness or Fainting</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.dizziness === true;"}, {"key": "dizziness_onset", "data": {"values": [{"label": "Today", "value": "today"}, {"label": "1 day ago", "value": "1_day"}, {"label": "2-3 days ago", "value": "2_3_days"}, {"label": "4-7 days ago", "value": "4_7_days"}, {"label": "1-2 weeks ago", "value": "1_2_weeks"}, {"label": "2-4 weeks ago", "value": "2_4_weeks"}, {"label": "1-3 months ago", "value": "1_3_months"}, {"label": "3-6 months ago", "value": "3_6_months"}, {"label": "6-12 months ago", "value": "6_12_months"}, {"label": "1-3 years ago", "value": "1_3_years"}, {"label": "More than 3 years ago", "value": "3_plus_years"}]}, "type": "select", "input": true, "label": "When did the dizziness or fainting episodes begin?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Dizziness started:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.dizziness === true;"}, {"key": "dizziness_frequency", "type": "radio", "input": true, "label": "How often do you experience dizziness or fainting?", "values": [{"label": "Once", "value": "once"}, {"label": "Occasionally (less than once a week)", "value": "occasional"}, {"label": "Frequently (once or more per week)", "value": "frequent"}, {"label": "Daily or almost daily", "value": "daily"}, {"label": "Unsure", "value": "unsure"}], "tableView": true, "confirm_label": "Dizziness frequency:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.dizziness === true;"}, {"key": "dizziness_character", "type": "radio", "input": true, "label": "How would you describe the dizziness?", "values": [{"label": "Lightheadedness or feeling faint", "value": "lightheaded"}, {"label": "Spinning or vertigo", "value": "spinning"}, {"label": "Unsteady or off balance", "value": "unsteady"}, {"label": "Hard to describe", "value": "hard_to_describe"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Dizziness character:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.dizziness === true;"}, {"key": "dizziness_timing", "type": "selectboxes", "input": true, "label": "When does the dizziness or fainting usually happen?", "values": [{"label": "After standing up", "value": "standing_up"}, {"label": "After exertion or exercise", "value": "exertion"}, {"label": "At rest", "value": "rest"}, {"label": "With dehydration or hunger", "value": "dehydration"}, {"label": "Without a clear trigger", "value": "unknown"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Dizziness timing:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.dizziness === true;"}, {"key": "fainting_loss_consciousness", "type": "radio", "input": true, "label": "Have you ever fully lost consciousness during one of these episodes?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Unsure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Fainting/loss of consciousness:", "customConditional": "show = data.cardiovascular_symptoms && data.cardiovascular_symptoms.dizziness === true;"}, {"key": "heading_lung_health", "html": "</br><h4>Lung Health</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "respiratory_symptoms", "type": "selectboxes", "input": true, "label": "Do you have any of the following respiratory symptoms?", "values": [{"label": "<PERSON><PERSON>", "value": "cough", "shortcut": ""}, {"label": "Shortness of breath", "value": "shortness_of_breath", "shortcut": ""}, {"label": "Wheezing", "value": "wheezing", "shortcut": ""}], "adminFlag": true, "tableView": true, "confirm_label": "Respiratory symptoms:", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_respiratory_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = !!data.none_of_the_above_respiratory_symptoms || _.some(_.values(data.respiratory_symptoms));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "respiratory_symptoms_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following respiratory symptoms:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "I DO NOT have the following breathing related symptoms:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.respiratory_symptoms, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "heading_cough", "html": "</br><h4>Cough</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.respiratory_symptoms && data.respiratory_symptoms.cough === true;"}, {"key": "cough_type", "type": "radio", "input": true, "label": "Is your cough dry or productive?", "values": [{"label": "Dry (no phlegm)", "value": "dry"}, {"label": "Productive (with phlegm)", "value": "productive"}, {"label": "Varies", "value": "varies"}, {"label": "Unsure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Cough type:", "customConditional": "show = data.respiratory_symptoms && data.respiratory_symptoms.cough === true;"}, {"key": "cough_duration", "data": {"values": [{"label": "Less than 1 week", "value": "lt_1wk"}, {"label": "1-2 weeks", "value": "1_2wk"}, {"label": "2-4 weeks", "value": "2_4wk"}, {"label": "More than 4 weeks", "value": "gt_4wk"}]}, "type": "select", "input": true, "label": "How long have you had the cough?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Cough duration:", "customConditional": "show = data.respiratory_symptoms && data.respiratory_symptoms.cough === true;"}, {"key": "cough_coughing_blood", "type": "radio", "input": true, "label": "Have you noticed any blood when coughing?", "values": [{"label": "Yes - bright red blood", "value": "bright_red"}, {"label": "Yes - dark or coffee ground appearance", "value": "coffee_ground"}, {"label": "I think so - unsure of colour or source", "value": "unsure_appearance"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "description": "This includes coughing up blood that appears bright red or looks like coffee grounds (which may suggest bleeding in the lungs or stomach).", "confirm_label": "Coughing up blood:", "customConditional": "show = data.respiratory_symptoms && data.respiratory_symptoms.cough === true;"}, {"key": "cough_feeling_unwell", "type": "radio", "input": true, "label": "Do you feel generally unwell with your cough (e.g. fatigue, fever, weakness)?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Feeling unwell with cough:", "customConditional": "show = data.respiratory_symptoms && data.respiratory_symptoms.cough === true;"}, {"key": "cough_with_cold", "type": "radio", "input": true, "label": "Did your cough begin at the same time as a cold or viral illness (e.g. sore throat, congestion, fever)?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Cough with cold:", "customConditional": "show = data.respiratory_symptoms && data.respiratory_symptoms.cough === true;"}, {"key": "cough_progression", "type": "radio", "input": true, "label": "Is your cough improving, getting worse, or staying the same?", "values": [{"label": "Improving", "value": "improving"}, {"label": "Getting worse", "value": "worsening"}, {"label": "No change", "value": "no_change"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Cough progression:", "customConditional": "show = data.cough_with_cold === 'yes';"}, {"key": "cough_urgent_warning", "html": "<div style='border-left: 4px solid #ffc107; background-color: #fff3cd; padding: 10px; margin-bottom: 10px;'><strong>Warning:</strong> If you are experiencing any of the following — worsening cough, shortness of breath, coughing up blood, feeling generally unwell, or if your cough has lasted more than 4 weeks — we advise seeking same-day care in an emergency department. These may be signs of a more serious condition that should not be delayed.</div>", "type": "content", "input": false, "label": "Content", "customConditional": "show = (data.cough_progression === 'worsening') || (data.respiratory_symptoms && data.respiratory_symptoms.shortness_of_breath === true) || (data.cough_coughing_blood === true) || (data.cough_feeling_unwell === true) || (data.cough_duration === 'gt_4wk');"}, {"key": "cough_urgent_warning_understanding", "type": "radio", "input": true, "label": "Do you understand this warning about when to seek emergency care?", "values": [{"label": "I understand", "value": "understand"}, {"label": "I do not understand", "value": "do_not_understand"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Understands Emergency Care Warning:", "customConditional": "show = (data.cough_progression === 'worsening') || (data.respiratory_symptoms && data.respiratory_symptoms.shortness_of_breath === true) || (data.cough_coughing_blood === true) || (data.cough_feeling_unwell === true) || (data.cough_duration === 'gt_4wk');"}, {"key": "heading_shortness_of_breath", "html": "</br><h4>Shortness of Breath</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.respiratory_symptoms && data.respiratory_symptoms.shortness_of_breath === true;"}, {"key": "sob_triggers", "type": "selectboxes", "input": true, "label": "When do you typically feel short of breath?", "values": [{"label": "At rest", "value": "rest"}, {"label": "With mild activity (e.g. walking)", "value": "mild_activity"}, {"label": "With moderate or strenuous activity", "value": "exercise"}, {"label": "While lying flat", "value": "lying_flat"}, {"label": "At night (waking from sleep)", "value": "nocturnal"}, {"label": "Unsure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Shortness of breath triggers:", "customConditional": "show = data.respiratory_symptoms && data.respiratory_symptoms.shortness_of_breath === true;"}, {"key": "heading_wheezing", "html": "</br><h4>Wheezing</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.respiratory_symptoms && data.respiratory_symptoms.wheezing === true;"}, {"key": "wheezing_timing", "type": "selectboxes", "input": true, "label": "When is the wheezing most noticeable?", "values": [{"label": "During exercise", "value": "exercise"}, {"label": "At rest", "value": "rest"}, {"label": "At night", "value": "night"}, {"label": "In cold weather", "value": "cold_weather"}, {"label": "When lying down", "value": "lying_down"}, {"label": "When exposed to irritants (e.g. smoke, dust)", "value": "irritants"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Wheezing timing:", "customConditional": "show = data.respiratory_symptoms && data.respiratory_symptoms.wheezing === true;"}, {"key": "wheezing_relief", "type": "radio", "input": true, "label": "Do you use any medications to relieve wheezing?", "values": [{"label": "Yes - inhaler or nebulizer", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Occasionally", "value": "occasional"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Wheezing relief:", "customConditional": "show = data.respiratory_symptoms && data.respiratory_symptoms.wheezing === true;"}, {"key": "wheezing_asthma_history", "type": "radio", "input": true, "label": "Have you ever been diagnosed with asthma (currently or in the past)?", "values": [{"label": "Yes - currently diagnosed", "value": "current"}, {"label": "Yes - past diagnosis only", "value": "past"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Wheezing asthma history:", "customConditional": "show = data.respiratory_symptoms && data.respiratory_symptoms.wheezing === true;"}, {"key": "heading_gastro_health", "html": "</br><h4>Gastrointestinal Health</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "abdominal_gastrointestinal_symptoms", "type": "selectboxes", "input": true, "label": "Do you have any of the following abdominal or gastrointestinal symptoms?", "values": [{"label": "Abdominal pain", "value": "abdominal_pain", "shortcut": ""}, {"label": "<PERSON><PERSON><PERSON>", "value": "nausea", "shortcut": ""}, {"label": "Vomiting", "value": "vomiting", "shortcut": ""}, {"label": "Diarrhea", "value": "diarrhea", "shortcut": ""}, {"label": "Constipation", "value": "constipation", "shortcut": ""}, {"label": "Bloating or gas", "value": "bloating_gas", "shortcut": ""}, {"label": "Rectal bleeding", "value": "rectal_bleeding", "shortcut": ""}], "adminFlag": true, "tableView": true, "confirm_label": "Gastrointestinal symptoms:", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_abdominal_gastrointestinal_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = !!data.none_of_the_above_abdominal_gastrointestinal_symptoms || _.some(_.values(data.abdominal_gastrointestinal_symptoms));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "gastrointestinal_symptoms_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following gastrointestinal symptoms:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "I DO NOT have the following gastrointestinal symptoms:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.abdominal_gastrointestinal_symptoms, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "heading_rectal_bleeding", "html": "</br><h4>Rectal Bleeding</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.abdominal_gastrointestinal_symptoms && data.abdominal_gastrointestinal_symptoms.rectal_bleeding === true;"}, {"key": "onset_rectal_bleeding", "data": {"values": [{"label": "Today", "value": "today"}, {"label": "1-3 days ago", "value": "1_3 days"}, {"label": "4-7 days ago", "value": "4-7 days"}, {"label": "1-2 weeks ago", "value": "1-2 weeks"}, {"label": "2-4 weeks ago", "value": "2-4 weeks"}, {"label": "More than 1 month ago (continuous)", "value": "over_1_month"}, {"label": "Comes and goes over the past month", "value": "recurrent_1_month"}, {"label": "Comes and goes over the past year", "value": "recurrent_1_year"}, {"label": "Comes and goes for many years", "value": "recurrent_many_years"}]}, "type": "select", "input": true, "label": "When did the rectal bleeding begin?", "widget": "html5", "tableView": true, "confirm_label": "Rectal bleeding started:", "customConditional": "show = data.abdominal_gastrointestinal_symptoms && data.abdominal_gastrointestinal_symptoms.rectal_bleeding === true;"}, {"key": "rectal_bleeding_appearance", "type": "radio", "input": true, "label": "What type of blood have you noticed?", "values": [{"label": "Bright red blood (e.g., on toilet paper or in toilet)", "value": "bright_red"}, {"label": "Dark or tarry stool (melena)", "value": "tarry"}, {"label": "Blood mixed with stool", "value": "mixed"}, {"label": "I think so - unsure of colour or source", "value": "unsure"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "description": "This may include bright red blood, dark or tarry stools, or blood mixed with stool. Please select the best match.", "confirm_label": "Rectal bleeding appearance:", "customConditional": "show = data.abdominal_gastrointestinal_symptoms && data.abdominal_gastrointestinal_symptoms.rectal_bleeding === true;"}, {"key": "rectal_bleeding_typical_vs_new", "type": "radio", "input": true, "label": "Is this episode of rectal bleeding similar to past episodes, or is it different?", "values": [{"label": "Typical - feels the same as prior episodes", "value": "typical"}, {"label": "Different - new or unusual compared to prior", "value": "different"}, {"label": "Not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Rectal bleeding typical vs new:", "customConditional": "show = data.onset_rectal_bleeding && (data.onset_rectal_bleeding === 'recurrent_1_month' || data.onset_rectal_bleeding === 'recurrent_1_year' || data.onset_rectal_bleeding === 'recurrent_many_years');"}, {"key": "rectal_bleeding_warning", "html": "<div style='border-left: 4px solid #f5c518; background-color: #fff3cd; padding: 10px; margin-bottom: 10px;'><strong>Recommendation:</strong> Rectal bleeding can sometimes be caused by small tears in the skin (anal fissures) or hemorrhoids, especially if related to constipation or straining. However, it can also be a sign of more serious gastrointestinal conditions such as inflammatory bowel disease or colorectal cancer. If you are experiencing rectal bleeding, a physical exam and further evaluation — including possible lab testing or colonoscopy — may be warranted to determine the cause and ensure appropriate treatment.</div>", "type": "content", "input": false, "customConditional": "show = data.abdominal_gastrointestinal_symptoms && data.abdominal_gastrointestinal_symptoms.rectal_bleeding === true;"}, {"key": "rectal_bleeding_warning_understanding", "type": "radio", "input": true, "label": "Do you understand the importance of having rectal bleeding evaluated by a healthcare provider?", "values": [{"label": "I understand", "value": "understand"}, {"label": "I do not understand", "value": "do_not_understand"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Understands Rectal Bleeding Recommendation:", "customConditional": "show = data.abdominal_gastrointestinal_symptoms && data.abdominal_gastrointestinal_symptoms.rectal_bleeding === true;"}, {"key": "heading_abdominal_pain", "html": "</br><h4>Abdominal Pain</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.abdominal_gastrointestinal_symptoms && data.abdominal_gastrointestinal_symptoms.abdominal_pain === true;"}, {"key": "abdominal_pain_location", "type": "selectboxes", "input": true, "label": "Where is your abdominal pain located?", "values": [{"label": "Upper abdomen", "value": "upper"}, {"label": "Lower abdomen", "value": "lower"}, {"label": "Right side", "value": "right"}, {"label": "Left side", "value": "left"}, {"label": "Around the belly button", "value": "periumbilical"}, {"label": "Pain moves around", "value": "diffuse"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Abdominal pain location:", "customConditional": "show = data.abdominal_gastrointestinal_symptoms && data.abdominal_gastrointestinal_symptoms.abdominal_pain === true;"}, {"key": "onset_abdominal_pain", "data": {"values": [{"label": "Today", "value": "today"}, {"label": "1-3 days ago", "value": "1_3 days"}, {"label": "4-7 days ago", "value": "4-7 days"}, {"label": "1-2 weeks ago", "value": "1-2 weeks"}, {"label": "2-4 weeks ago", "value": "2-4 weeks"}, {"label": "More than 1 month ago (continuous)", "value": "over_1_month"}, {"label": "Comes and goes over the past month", "value": "recurrent_1_month"}, {"label": "Comes and goes over the past year", "value": "recurrent_1_year"}, {"label": "Comes and goes for many years", "value": "recurrent_many_years"}]}, "type": "select", "input": true, "label": "When did the abdominal pain begin?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Abdominal pain started:", "customConditional": "show = data.abdominal_gastrointestinal_symptoms && data.abdominal_gastrointestinal_symptoms.abdominal_pain === true;"}, {"key": "abdominal_pain_typical_vs_new", "type": "radio", "input": true, "label": "Is this episode of abdominal pain similar to your past episodes, or is it different?", "values": [{"label": "Typical - feels the same as prior episodes", "value": "typical"}, {"label": "Different - new or unusual compared to prior", "value": "different"}, {"label": "Not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Abdominal pain typical vs new:", "customConditional": "show = data.onset_abdominal_pain && (data.onset_abdominal_pain === 'recurrent_1_month' || data.onset_abdominal_pain === 'recurrent_1_year' || data.onset_abdominal_pain === 'recurrent_many_years');"}, {"key": "heading_nausea", "html": "</br><h4>Nausea</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.abdominal_gastrointestinal_symptoms && data.abdominal_gastrointestinal_symptoms.nausea === true;"}, {"key": "nausea_timing", "type": "radio", "input": true, "label": "When do you usually feel nauseated?", "values": [{"label": "In the morning", "value": "morning"}, {"label": "After eating", "value": "post_meal"}, {"label": "Randomly throughout the day", "value": "random"}, {"label": "Constant", "value": "constant"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Nausea timing:", "customConditional": "show = data.abdominal_gastrointestinal_symptoms && data.abdominal_gastrointestinal_symptoms.nausea === true;"}, {"key": "onset_nausea", "data": {"values": [{"label": "Today", "value": "today"}, {"label": "1-3 days ago", "value": "1_3 days"}, {"label": "4-7 days ago", "value": "4-7 days"}, {"label": "1-2 weeks ago", "value": "1-2 weeks"}, {"label": "2-4 weeks ago", "value": "2-4 weeks"}, {"label": "More than 1 month ago (continuous)", "value": "over_1_month"}, {"label": "Comes and goes over the past month", "value": "recurrent_1_month"}, {"label": "Comes and goes over the past year", "value": "recurrent_1_year"}, {"label": "Comes and goes for many years", "value": "recurrent_many_years"}]}, "type": "select", "input": true, "label": "When did the nausea begin?", "widget": "html5", "tableView": true, "confirm_label": "Na<PERSON><PERSON> started:", "customConditional": "show = data.abdominal_gastrointestinal_symptoms && data.abdominal_gastrointestinal_symptoms.nausea === true;"}, {"key": "nausea_typical_vs_new", "type": "radio", "input": true, "label": "Is this episode of nausea similar to past episodes, or is it different?", "values": [{"label": "Typical - feels the same as prior episodes", "value": "typical"}, {"label": "Different - new or unusual compared to prior", "value": "different"}, {"label": "Not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Nausea typical vs new:", "customConditional": "show = data.onset_nausea && (data.onset_nausea === 'recurrent_1_month' || data.onset_nausea === 'recurrent_1_year' || data.onset_nausea === 'recurrent_many_years');"}, {"key": "heading_vomiting", "html": "</br><h4>Vomiting</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.abdominal_gastrointestinal_symptoms && data.abdominal_gastrointestinal_symptoms.vomiting === true;"}, {"key": "vomit_appearance", "type": "radio", "input": true, "label": "What does the vomit look like?", "values": [{"label": "Undigested food or fluid", "value": "food"}, {"label": "Yellow or green (bile)", "value": "bile"}, {"label": "Dark or coffee-ground appearance", "value": "coffee_ground"}, {"label": "Bright red blood", "value": "bright_red"}, {"label": "Not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "description": "This includes food, yellow/green bile, or signs of bleeding such as coffee ground appearance.", "confirm_label": "Vomit appearance:", "customConditional": "show = data.abdominal_gastrointestinal_symptoms && data.abdominal_gastrointestinal_symptoms.vomiting === true;"}, {"key": "onset_vomiting", "data": {"values": [{"label": "Today", "value": "today"}, {"label": "1-3 days ago", "value": "1_3 days"}, {"label": "4-7 days ago", "value": "4-7 days"}, {"label": "1-2 weeks ago", "value": "1-2 weeks"}, {"label": "2-4 weeks ago", "value": "2-4 weeks"}, {"label": "More than 1 month ago (continuous)", "value": "over_1_month"}, {"label": "Comes and goes over the past month", "value": "recurrent_1_month"}, {"label": "Comes and goes over the past year", "value": "recurrent_1_year"}, {"label": "Comes and goes for many years", "value": "recurrent_many_years"}]}, "type": "select", "input": true, "label": "When did the vomiting begin?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Vomiting started:", "customConditional": "show = data.abdominal_gastrointestinal_symptoms && data.abdominal_gastrointestinal_symptoms.vomiting === true;"}, {"key": "vomiting_typical_vs_new", "type": "radio", "input": true, "label": "Is this episode of vomiting similar to past episodes, or is it different?", "values": [{"label": "Typical - feels the same as prior episodes", "value": "typical"}, {"label": "Different - new or unusual compared to prior", "value": "different"}, {"label": "Not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Vomiting typical vs new:", "customConditional": "show = data.onset_vomiting && (data.onset_vomiting === 'recurrent_1_month' || data.onset_vomiting === 'recurrent_1_year' || data.onset_vomiting === 'recurrent_many_years');"}, {"key": "heading_diarrhea", "html": "</br><h4>Diarrhea</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.abdominal_gastrointestinal_symptoms && data.abdominal_gastrointestinal_symptoms.diarrhea === true;"}, {"key": "diarrhea_features", "type": "selectboxes", "input": true, "label": "What features describe your diarrhea?", "values": [{"label": "Watery", "value": "watery"}, {"label": "Contains mucus", "value": "mucus"}, {"label": "Contains blood", "value": "bloody"}, {"label": "<PERSON><PERSON> need to go", "value": "urgent"}, {"label": "Associated with cramping", "value": "cramping"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Diarrhea features:", "customConditional": "show = data.abdominal_gastrointestinal_symptoms && data.abdominal_gastrointestinal_symptoms.diarrhea === true;"}, {"key": "onset_diarrhea", "data": {"values": [{"label": "Today", "value": "today"}, {"label": "1-3 days ago", "value": "1_3 days"}, {"label": "4-7 days ago", "value": "4-7 days"}, {"label": "1-2 weeks ago", "value": "1-2 weeks"}, {"label": "2-4 weeks ago", "value": "2-4 weeks"}, {"label": "More than 1 month ago (continuous)", "value": "over_1_month"}, {"label": "Comes and goes over the past month", "value": "recurrent_1_month"}, {"label": "Comes and goes over the past year", "value": "recurrent_1_year"}, {"label": "Comes and goes for many years", "value": "recurrent_many_years"}]}, "type": "select", "input": true, "label": "When did the diarrhea begin?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Diarrhea started:", "customConditional": "show = data.abdominal_gastrointestinal_symptoms && data.abdominal_gastrointestinal_symptoms.diarrhea === true;"}, {"key": "diarrhea_typical_vs_new", "type": "radio", "input": true, "label": "Is this episode of diarrhea similar to your previous episodes, or is it different?", "values": [{"label": "Typical - feels the same as prior episodes", "value": "typical"}, {"label": "Different - new or unusual compared to prior", "value": "different"}, {"label": "Not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Diarrhea typical vs new:", "customConditional": "show = data.onset_diarrhea && (data.onset_diarrhea === 'recurrent_1_month' || data.onset_diarrhea === 'recurrent_1_year' || data.onset_diarrhea === 'recurrent_many_years');"}, {"key": "heading_mental_health", "html": "</br><h4>Mental Health</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "mental_health_symptoms", "type": "selectboxes", "input": true, "label": "Have you experienced any of the following symptoms?", "values": [{"label": "Feeling down, sad, or depressed most days", "value": "low_mood", "shortcut": ""}, {"label": "Easily irritated, angry, or short-tempered", "value": "anger", "shortcut": ""}, {"label": "Feeling nervous, anxious, or unable to relax", "value": "anxiety", "shortcut": ""}, {"label": "Seeing or hearing things others do not (hallucinations or unusual sensations)", "value": "psychosis", "shortcut": ""}], "adminFlag": true, "tableView": true, "confirm_label": "Mental health symptoms:", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_mental_health_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "You must select at least one symptom or 'None of the above'."}, "validate": {"custom": "valid = !!data.none_of_the_above_mental_health_symptoms || _.some(_.values(data.mental_health_symptoms));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "mental_health_symptoms_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following mental health symptoms:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "I DO NOT have the following mental health symptoms:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.mental_health_symptoms, _.negate(_.identity))), _.capitalize), ', '), /_/g, ' ');"}, {"key": "heading_mental_health_followup", "html": "</br><h4>Mental Health Symptoms</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.mental_health_symptoms && Object.values(data.mental_health_symptoms).includes(true);"}, {"key": "mental_health_prior_diagnosis", "type": "radio", "input": true, "label": "Have you ever been diagnosed with or suspected of having depression or anxiety?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "unsure"}], "tableView": true, "confirm_label": "Prior diagnosis of depression or anxiety:", "customConditional": "show = data.mental_health_symptoms && Object.values(data.mental_health_symptoms).includes(true);"}, {"key": "mental_health_diagnosis_source", "type": "radio", "input": true, "label": "Who made the diagnosis?", "values": [{"label": "Family doctor or primary care provider", "value": "family_doctor"}, {"label": "Psychiatrist", "value": "psychiatrist"}, {"label": "Therapist or counselor", "value": "therapist"}, {"label": "Self-diagnosed or suspected", "value": "self_diagnosed"}, {"label": "Not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Diagnosis source:", "customConditional": "show = data.mental_health_prior_diagnosis === 'yes';"}, {"key": "mental_health_onset", "data": {"values": [{"label": "Within the last week", "value": "under_1_week"}, {"label": "1-4 weeks ago", "value": "1_4_weeks"}, {"label": "1-3 months ago", "value": "1-2 months"}, {"label": "3-6 months ago", "value": "3_6_months"}, {"label": "6-12 months ago", "value": "6_12_months"}, {"label": "More than a year ago (continuous symptoms)", "value": "over_1_year_continuous"}, {"label": "Symptoms come and go over the past month", "value": "recurrent_1_month"}, {"label": "Symptoms come and go over the past year", "value": "recurrent_1_year"}, {"label": "Symptoms come and go for many years", "value": "recurrent_many_years"}, {"label": "Unsure", "value": "unsure"}]}, "type": "select", "input": true, "label": "When did these symptoms first begin?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Symptoms started:", "customConditional": "show = data.mental_health_symptoms && Object.values(data.mental_health_symptoms).includes(true);"}, {"key": "mental_health_prior_treatment", "type": "selectboxes", "input": true, "label": "Have you ever been treated for these symptoms?", "values": [{"label": "Medication (e.g. antidepressants, antianxiety, antipsychotics)", "value": "medication"}, {"label": "Counseling or therapy", "value": "therapy"}, {"label": "Hospitalization or emergency care", "value": "hospitalization"}, {"label": "No prior treatment", "value": "none"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Prior treatment for mental health symptoms:", "customConditional": "show = data.mental_health_symptoms && Object.values(data.mental_health_symptoms).includes(true);"}, {"key": "mental_health_therapy_discussion", "type": "radio", "input": true, "label": "Have you previously discussed these symptoms with a therapist or mental health professional?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Prior discussion with therapist or mental health professional:", "customConditional": "show = data.mental_health_symptoms && Object.values(data.mental_health_symptoms).includes(true);"}, {"key": "mental_health_suicidal_check", "type": "radio", "input": true, "label": "Have you had any thoughts of harming yourself or others?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Prefer not to answer", "value": "prefer_not_say"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Suicidal thoughts check:", "customConditional": "show = data.mental_health_symptoms && Object.values(data.mental_health_symptoms).includes(true);"}, {"key": "mental_health_si_hi_timing", "type": "radio", "input": true, "label": "Are you currently experiencing thoughts of harming yourself or others?", "values": [{"label": "No - I've had them in the past but not today or recently", "value": "past_only"}, {"label": "I had them recently, but not today", "value": "recent_but_not_today"}, {"label": "Yes - I have them today", "value": "today"}, {"label": "Yes - I have them frequently", "value": "frequent"}, {"label": "Unsure how to describe it", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Current thoughts of harm:", "customConditional": "show = data.mental_health_suicidal_check === 'yes';"}, {"key": "mental_health_emergency_warning", "html": "<div style='border-left: 4px solid #dc3545; background-color: #f8d7da; padding: 10px; margin-bottom: 10px;'><strong>Emergency Support:</strong> You indicated that you're currently experiencing thoughts of harming yourself or others. Please call <strong>911</strong> or go to the <strong>nearest emergency department</strong> immediately. Help is available right now and your safety matters.</div>", "type": "content", "input": false, "label": "Content", "customConditional": "show = data.mental_health_si_hi_timing === 'today' || data.mental_health_si_hi_timing === 'frequent';"}, {"key": "mental_health_safety_warning", "html": "<div style='border-left: 4px solid #ffc107; background-color: #fff3cd; padding: 10px; margin-bottom: 10px;'><strong>Warning:</strong> Even if you are not currently experiencing suicidal or harmful thoughts, please seek same-day in-person care through an emergency department if this changes. Immediate help is available and critical when these symptoms occur.</div>", "type": "content", "input": false, "customConditional": "show = data.mental_health_suicidal_check === 'no';"}, {"key": "mental_health_safety_understanding", "type": "radio", "input": true, "label": "Do you understand when to seek emergency care if thoughts of harm arise?", "values": [{"label": "I understand", "value": "understand"}, {"label": "I do not understand", "value": "do_not_understand"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Understands Mental Health Safety Advice:", "customConditional": "show = data.mental_health_suicidal_check === 'no';"}, {"key": "heading_thyroid_medication", "html": "<h2></br>Thyroid Medication History</h2>", "type": "content", "input": false, "tableView": false}, {"key": "thyroid_medication_status", "type": "radio", "input": true, "label": "Are you currently taking thyroid medication?", "values": [{"label": "Yes — I am currently taking thyroid medication", "value": "currently_on"}, {"label": "No — I was on thyroid medication in the past", "value": "previously_on"}, {"label": "No — I have never taken thyroid medication", "value": "never_on"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Thyroid medication status:"}, {"key": "thyroid_medication_last_use", "data": {"values": [{"label": "Within the past 1 week", "value": "within_1_week"}, {"label": "1-2 weeks ago", "value": "1-2 weeks"}, {"label": "2-3 weeks ago", "value": "2_3_weeks"}, {"label": "3-4 weeks ago", "value": "3_4_weeks"}, {"label": "4-6 weeks ago", "value": "4_6_weeks"}, {"label": "6 weeks-3 months ago", "value": "6w_3mo"}, {"label": "3-12 months ago", "value": "3_12mo"}, {"label": "1-3 years ago", "value": "1_3y"}, {"label": "More than 3 years ago", "value": "gt_3y"}, {"label": "Not sure", "value": "not_sure"}]}, "type": "select", "input": true, "label": "When did you stop taking thyroid medication?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Time since thyroid medication stopped:", "customConditional": "show = data.thyroid_medication_status === 'previously_on'"}, {"key": "thyroid_medication_stopped_reason", "type": "selectboxes", "input": true, "label": "Why did you stop taking thyroid medication?", "values": [{"label": "It was only needed during pregnancy", "value": "pregnancy_only"}, {"label": "My symptoms improved", "value": "symptoms_improved"}, {"label": "My doctor stopped it based on test results", "value": "doctor_stopped"}, {"label": "I chose to stop taking it", "value": "self_stopped"}, {"label": "Other reason", "value": "other"}], "tableView": true, "confirm_label": "Reason for stopping medication:", "customConditional": "show = data.thyroid_medication_status === 'previously_on'"}, {"key": "thyroid_medication_followup_testing", "type": "radio", "input": true, "label": "Did you have follow-up thyroid testing after stopping the medication?", "values": [{"label": "Yes — and the results were normal", "value": "normal"}, {"label": "Yes — but the results were abnormal", "value": "abnormal"}, {"label": "No follow-up testing was done", "value": "no_testing"}, {"label": "Not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Follow-up testing after stopping:", "customConditional": "show = data.thyroid_medication_status === 'previously_on'"}, {"key": "header_prior_tests", "html": "<h2>Prior Lab Testing</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "prior_tests_completed", "type": "selectboxes", "input": true, "label": "Have you had any of the following tests completed <strong>after</strong> your symptoms started?", "values": [{"label": "Kidney function (eGFR)", "value": "egfr"}, {"label": "Lipid Profile (Cholesterol levels)", "value": "lipid_profile"}, {"label": "Diabetes Testing (HbA1c)", "value": "a1c"}, {"label": "Fasting Blood Glucose (FBG)", "value": "fasting_glucose"}, {"label": "CBC (Complete Blood Count)", "value": "cbc"}, {"label": "Vitamin B12", "value": "b12"}, {"label": "TSH (Thyroid Stimulating Hormone)", "value": "tsh"}, {"label": "I have not had these tests completed", "value": "no_prior_tests"}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "confirm_label": "Prior Tests Completed:", "optionsLabelPosition": "right"}, {"key": "last_known_lab_timing", "data": {"values": [{"label": "<1 month ago", "value": "Less than 1 month ago"}, {"label": "1-2 months ago", "value": "1-2 months ago"}, {"label": "2-3 months ago", "value": "2-3 months ago"}, {"label": "3-4 months ago", "value": "3-4 months ago"}, {"label": "4-5 months ago", "value": "4-5 months ago"}, {"label": "5-6 months ago", "value": "5-6 months ago"}, {"label": "6-12 months ago", "value": "6-12 months ago"}, {"label": "12-24 months ago", "value": "12-24 months ago"}, {"label": "24+ months ago", "value": "More than 24 months ago"}, {"label": "I haven't had lab testing", "value": "Haven't had lab testing"}]}, "type": "select", "input": true, "label": "Do you recall when any set of lab tests (bloodwork) was completed?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Last Known Lab Timing:", "customConditional": "show = data.prior_tests_completed?.no_prior_tests === true && Object.values(data.prior_tests_completed).filter(v => v === true).length === 1"}, {"key": "heading_kidney_function", "html": "<h3>Kidney Function (eGFR)</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.prior_tests_completed?.egfr;"}, {"key": "last_kidney_function_test", "data": {"values": [{"label": "<1 month ago", "value": "Less than 1 month ago"}, {"label": "1-2 months ago", "value": "1-2 months ago"}, {"label": "2-3 months ago", "value": "2-3 months ago"}, {"label": "3-4 months ago", "value": "3-4 months ago"}, {"label": "4-5 months ago", "value": "4-5 months ago"}, {"label": "5-6 months ago", "value": "5-6 months ago"}, {"label": "6-12 months ago", "value": "6-12 months ago"}, {"label": "12-24 months ago", "value": "12-24 months ago"}, {"label": "24+ months ago", "value": "More than 24 months ago"}, {"label": "Never had one", "value": "Never had one"}]}, "type": "select", "input": true, "label": "When was your last kidney function test completed?", "widget": "html5", "validate": {"required": true}, "tableView": true, "placeholder": "Select the time range", "confirm_label": "Last Kidney Function Test:", "customConditional": "show = data.prior_tests_completed?.egfr;"}, {"key": "prior_kidney_function_value", "data": {"values": [{"label": "I don't remember my result", "value": "I don't remember my result"}, {"label": "eGFR > 90", "value": "eGFR > 90"}, {"label": "eGFR 85-89", "value": "eGFR 85-89"}, {"label": "eGFR 80-84", "value": "eGFR 80-84"}, {"label": "eGFR 75-79", "value": "eGFR 75-79"}, {"label": "eGFR 70-74", "value": "eGFR 70-74"}, {"label": "eGFR 65-69", "value": "eGFR 65-69"}, {"label": "eGFR 60-64", "value": "eGFR 60-64"}, {"label": "eGFR 55-59", "value": "eGFR 55-59"}, {"label": "eGFR 50-54", "value": "eGFR 50-54"}, {"label": "eGFR 45-49", "value": "eGFR 45-49"}, {"label": "eGFR < 45", "value": "eGFR < 45"}]}, "type": "select", "input": true, "label": "What was your most recent eGFR measurement?", "widget": "html5", "validate": {"required": true}, "tableView": true, "placeholder": "Select the value range", "confirm_label": "eGFR Value Range:", "customConditional": "show = data.prior_tests_completed?.egfr;"}, {"key": "heading_urine_acr", "html": "</br><h3>Urine Albumin-to-Creatinine Ratio (ACR)</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.prior_tests_completed?.urine_acr;"}, {"key": "last_urine_acr_test", "data": {"values": [{"label": "<1 month ago", "value": "Less than 1 month ago"}, {"label": "1-2 months ago", "value": "1-2 months ago"}, {"label": "2-3 months ago", "value": "2-3 months ago"}, {"label": "3-4 months ago", "value": "3-4 months ago"}, {"label": "4-5 months ago", "value": "4-5 months ago"}, {"label": "5-6 months ago", "value": "5-6 months ago"}, {"label": "6-12 months ago", "value": "6-12 months ago"}, {"label": "12-24 months ago", "value": "12-24 months ago"}, {"label": "24+ months ago", "value": "More than 24 months ago"}, {"label": "Never had one", "value": "Never had one"}]}, "type": "select", "input": true, "label": "When was your last urine ACR test completed?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Last Urine ACR Test:", "customConditional": "show = data.prior_tests_completed?.urine_acr;"}, {"key": "heading_lipid_profile", "html": "</br><h3>Lipid Profile (Cholesterol)</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.prior_tests_completed?.lipid_profile;"}, {"key": "last_lipid_profile_test", "data": {"values": [{"label": "<1 month ago", "value": "Less than 1 month ago"}, {"label": "1-2 months ago", "value": "1-2 months ago"}, {"label": "2-3 months ago", "value": "2-3 months ago"}, {"label": "3-4 months ago", "value": "3-4 months ago"}, {"label": "4-5 months ago", "value": "4-5 months ago"}, {"label": "5-6 months ago", "value": "5-6 months ago"}, {"label": "6-12 months ago", "value": "6-12 months ago"}, {"label": "12-24 months ago", "value": "12-24 months ago"}, {"label": "24+ months ago", "value": "More than 24 months ago"}, {"label": "Never had one", "value": "Never had one"}]}, "type": "select", "input": true, "label": "When was your last lipid profile test completed?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Last Lipid Profile Test:", "customConditional": "show = data.prior_tests_completed?.lipid_profile;"}, {"key": "heading_lipid_profile_results", "html": "</br><h3>Lipid Profile Results</h3><p>Select any abnormalities found in your test results.</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.last_lipid_profile_test !== undefined;"}, {"key": "lipid_profile_abnormalities", "type": "selectboxes", "input": true, "label": "Were any of the following findings reported in your lipid profile?", "values": [{"label": "High LDL", "value": "High LDL"}, {"label": "High Triglycerides", "value": "High Triglycerides"}, {"label": "High HDL", "value": "High HDL"}, {"label": "Normal HDL", "value": "Normal HDL"}, {"label": "Normal lipid profile", "value": "Normal lipid profile"}, {"label": "I don't remember my values", "value": "I don't remember my values"}, {"label": "I don't remember them but was told they were normal", "value": "I don't remember them but was told they were normal"}], "tooltip": "Select all that apply based on your most recent test results.", "validate": {"required": true}, "tableView": true, "customClass": "mt-n3", "confirm_label": "Lipid Profile Abnormalities:", "customConditional": "show = data.last_lipid_profile_test !== undefined;"}, {"key": "heading_diabetes_tests", "html": "</br><h3>Diabetes Testing (HbA1c or Fasting Blood Glucose)</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.prior_tests_completed?.a1c === true || data.prior_tests_completed?.fasting_glucose === true;"}, {"key": "last_a1c_test", "data": {"values": [{"label": "<1 month ago", "value": "Less than 1 month ago"}, {"label": "1-2 months ago", "value": "1-2 months ago"}, {"label": "2-3 months ago", "value": "2-3 months ago"}, {"label": "3-4 months ago", "value": "3-4 months ago"}, {"label": "4-5 months ago", "value": "4-5 months ago"}, {"label": "5-6 months ago", "value": "5-6 months ago"}, {"label": "6-12 months ago", "value": "6-12 months ago"}, {"label": "12-24 months ago", "value": "12-24 months ago"}, {"label": "24+ months ago", "value": "More than 24 months ago"}, {"label": "Never had one", "value": "Never had one"}]}, "type": "select", "input": true, "label": "When was your last HbA1c test completed?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Last A1c Test:", "customConditional": "show = data.prior_tests_completed?.a1c === true;"}, {"key": "recent_a1c_value", "data": {"values": [{"label": "I don't remember my result", "value": "I don't remember my result"}, {"label": "Below 5.7%", "value": "Below 5.7%"}, {"label": "5.7% - 6.4%", "value": "5.7% - 6.4%"}, {"label": "6.5% - 6.9%", "value": "6.5% - 6.9%"}, {"label": "7.0% - 7.9%", "value": "7.0% - 7.9%"}, {"label": "8.0% - 8.9%", "value": "8.0% - 8.9%"}, {"label": "9.0% - 9.9%", "value": "9.0% - 9.9%"}, {"label": "10.0% or higher", "value": "10.0% or higher"}]}, "type": "select", "input": true, "label": "What was your most recent HbA1c result?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Recent A1c Value:", "customConditional": "show = data.prior_tests_completed?.a1c === true;"}, {"key": "last_fasting_glucose_test", "data": {"values": [{"label": "<1 month ago", "value": "Less than 1 month ago"}, {"label": "1-2 months ago", "value": "1-2 months ago"}, {"label": "2-3 months ago", "value": "2-3 months ago"}, {"label": "3-4 months ago", "value": "3-4 months ago"}, {"label": "4-5 months ago", "value": "4-5 months ago"}, {"label": "5-6 months ago", "value": "5-6 months ago"}, {"label": "6-12 months ago", "value": "6-12 months ago"}, {"label": "12-24 months ago", "value": "12-24 months ago"}, {"label": "24+ months ago", "value": "More than 24 months ago"}, {"label": "Never had one", "value": "Never had one"}]}, "type": "select", "input": true, "label": "When was your last fasting blood glucose test completed?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Last Fasting Glucose Test:", "customConditional": "show = data.prior_tests_completed?.fasting_glucose === true;"}, {"key": "recent_fbg_value", "data": {"values": [{"label": "I don't remember my result", "value": "I don't remember my result"}, {"label": "Below 5.6 mmol/L [<100 mg/dL]", "value": "Below 5.6 mmol/L [<100 mg/dL]"}, {"label": "5.6 - 6.9 mmol/L [100-125 mg/dL]", "value": "5.6 - 6.9 mmol/L [100-125 mg/dL]"}, {"label": "7.0 - 7.9 mmol/L [126-142 mg/dL]", "value": "7.0 - 7.9 mmol/L [126-142 mg/dL]"}, {"label": "8.0 - 8.9 mmol/L [143-160 mg/dL]", "value": "8.0 - 8.9 mmol/L [143-160 mg/dL]"}, {"label": "9.0 - 9.9 mmol/L [161-178 mg/dL]", "value": "9.0 - 9.9 mmol/L [161-178 mg/dL]"}, {"label": "10.0 - 11.9 mmol/L [179-214 mg/dL]", "value": "10.0 - 11.9 mmol/L [179-214 mg/dL]"}, {"label": "12.0+ mmol/L [215+ mg/dL]", "value": "12.0+ mmol/L [215+ mg/dL]"}]}, "type": "select", "input": true, "label": "What was your most recent <strong>fasting blood glucose</strong> (FBG) result?", "widget": "html5", "validate": {"required": true}, "tableView": true, "description": "Select the range that matches your lab result. Canadian units shown (mmol/L), with U.S. units in [mg/dL].", "confirm_label": "Recent FBG Value:", "customConditional": "show = data.prior_tests_completed?.fasting_glucose === true && data.last_fasting_glucose_test !== 'never_had';"}, {"key": "heading_cbc", "html": "</br><h3>CBC (Complete Blood Count)</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.prior_tests_completed?.cbc;"}, {"key": "last_cbc_test", "data": {"values": [{"label": "<1 month ago", "value": "Less than 1 month ago"}, {"label": "1-2 months ago", "value": "1-2 months ago"}, {"label": "2-3 months ago", "value": "2-3 months ago"}, {"label": "3-4 months ago", "value": "3-4 months ago"}, {"label": "4-5 months ago", "value": "4-5 months ago"}, {"label": "5-6 months ago", "value": "5-6 months ago"}, {"label": "6-12 months ago", "value": "6-12 months ago"}, {"label": "12-24 months ago", "value": "12-24 months ago"}, {"label": "24+ months ago", "value": "More than 24 months ago"}, {"label": "Never had one", "value": "Never had one"}]}, "type": "select", "input": true, "label": "When was your last CBC test completed?", "widget": "html5", "validate": {"required": true}, "tableView": true, "placeholder": "Select the time range", "confirm_label": "Last CBC Test:", "customConditional": "show = data.prior_tests_completed?.cbc;"}, {"key": "prior_cbc_value", "data": {"values": [{"label": "I don't remember my result", "value": "I don't remember my result"}, {"label": "< 70 g/L", "value": "Less than 70 g/L"}, {"label": "70-100 g/L", "value": "70-100 g/L"}, {"label": "101-120 g/L", "value": "101-120 g/L"}, {"label": "121-150 g/L", "value": "121-150 g/L"}, {"label": "> 150 g/L", "value": "Greater than 150 g/L"}]}, "type": "select", "input": true, "label": "What was your most recent hemoglobin level (CBC result)?", "widget": "html5", "validate": {"required": true}, "tableView": true, "placeholder": "Select your hemoglobin level", "confirm_label": "Hemoglobin Level:", "customConditional": "show = data.prior_tests_completed?.cbc;"}, {"key": "heading_b12", "html": "</br><h3>Vitamin B12</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.prior_tests_completed?.b12;"}, {"key": "last_b12_test", "data": {"values": [{"label": "<1 month ago", "value": "Less than 1 month ago"}, {"label": "1-2 months ago", "value": "1-2 months ago"}, {"label": "2-3 months ago", "value": "2-3 months ago"}, {"label": "3-4 months ago", "value": "3-4 months ago"}, {"label": "4-5 months ago", "value": "4-5 months ago"}, {"label": "5-6 months ago", "value": "5-6 months ago"}, {"label": "6-12 months ago", "value": "6-12 months ago"}, {"label": "12-24 months ago", "value": "12-24 months ago"}, {"label": "24+ months ago", "value": "More than 24 months ago"}, {"label": "Never had one", "value": "Never had one"}]}, "type": "select", "input": true, "label": "When was your last B12 test completed?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Last B12 Test:", "customConditional": "show = data.prior_tests_completed?.b12;"}, {"key": "prior_b12_value", "data": {"values": [{"label": "I don't remember my result", "value": "I don't remember my result"}, {"label": "< 150 pmol/L", "value": "< 150 pmol/L"}, {"label": "150-200 pmol/L", "value": "150-200 pmol/L"}, {"label": "200-300 pmol/L", "value": "200-300 pmol/L"}, {"label": "> 300 pmol/L", "value": "> 300 pmol/L"}]}, "type": "select", "input": true, "label": "What was your most recent B12 level?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "B12 Level:", "customConditional": "show = data.prior_tests_completed?.b12;"}, {"key": "heading_tsh", "html": "</br><h3>TSH (Thyroid Stimulating Hormone)</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.prior_tests_completed?.tsh;"}, {"key": "last_tsh_test", "data": {"values": [{"label": "<1 month ago", "value": "Less than 1 month ago"}, {"label": "1-2 months ago", "value": "1-2 months ago"}, {"label": "2-3 months ago", "value": "2-3 months ago"}, {"label": "3-4 months ago", "value": "3-4 months ago"}, {"label": "4-5 months ago", "value": "4-5 months ago"}, {"label": "5-6 months ago", "value": "5-6 months ago"}, {"label": "6-12 months ago", "value": "6-12 months ago"}, {"label": "12-24 months ago", "value": "12-24 months ago"}, {"label": "24+ months ago", "value": "More than 24 months ago"}, {"label": "Never had one", "value": "Never had one"}]}, "type": "select", "input": true, "label": "When was your last TSH test completed?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Last TSH Test:", "customConditional": "show = data.prior_tests_completed?.tsh;"}, {"key": "prior_tsh_value", "data": {"values": [{"label": "I don't remember my result", "value": "I don't remember my result"}, {"label": "Below 0.1 mIU/L (very low)", "value": "Below 0.1 mIU/L (very low)"}, {"label": "0.1 - 0.3 mIU/L (low)", "value": "0.1 - 0.3 mIU/L (low)"}, {"label": "0.4 - 4.0 mIU/L (normal range)", "value": "0.4 - 4.0 mIU/L (normal range)"}, {"label": "4.1 - 5.0 mIU/L (mildly elevated)", "value": "4.1 - 5.0 mIU/L (mildly elevated)"}, {"label": "5.1 - 7.5 mIU/L (borderline high)", "value": "5.1 - 7.5 mIU/L (borderline high)"}, {"label": "7.6 - 10 mIU/L (moderately high)", "value": "7.6 - 10 mIU/L (moderately high)"}, {"label": "Above 10 mIU/L (high, likely hypothyroidism)", "value": "Above 10 mIU/L (high, likely hypothyroidism)"}]}, "type": "select", "input": true, "label": "What was your most recent TSH level?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "TSH Level:", "customConditional": "show = data.prior_tests_completed?.tsh;"}, {"key": "additional_information", "type": "textarea", "input": true, "label": "Please provide any additional details about your symptoms, medical history, or medications that you think are relevant.", "tableView": true, "autoExpand": false}, {"key": "intake_denial_reasons", "type": "textfield", "input": true, "label": "Intake Denial Reasons:", "hidden": true, "disabled": true, "multiple": true, "adminFlag": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "value = [];", "refreshOnChange": true}, {"key": "intake_kind", "type": "textfield", "input": true, "label": "Intake Kind", "hidden": true, "disabled": true, "tableView": true, "clearOnHide": false, "calculateValue": "value = !data.intake_denial_reasons?.length ? 'mail' : 'chat';"}, {"key": "intake_mt_key", "type": "textfield", "input": true, "label": "Intake Mail Template Key", "hidden": true, "disabled": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.intake_kind=='mail'?'intake-thyroid':'intake-denial'"}, {"key": "allowed_rt_keys", "type": "textfield", "input": true, "label": "Allowed Rep<PERSON>", "hidden": true, "disabled": true, "multiple": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.intake_mt_key=='intake-denial'?[]:['get-req','appointment-intake','edit-intake']"}]}