{"components": [{"key": "thyroid_medication_preferences_header", "type": "content", "input": false, "label": "Content", "html": "<h2>Thyroid Medication Details</h2><p>Please tell us whether you are currently taking thyroid medication, have taken it in the past, or have never taken it. If you are on medication now—or plan to restart—confirm your current or prior dosing schedule below.</p>"}, {"key": "thyroid_medication_history", "type": "radio", "input": true, "label": "Have you <strong>ever</strong> taken thyroid medication in the past, or are you <strong>currently</strong> taking it?", "labelPosition": "top", "values": [{"label": "Yes, currently or in the past", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "thyroid_medication_renewal_choice", "type": "radio", "input": true, "label": "Which option best describes your current thyroid medication status?", "confirm_label": "Thyroid Medication Renewal Preference:", "values": [{"label": "Not on thyroid medication, unsure if I need to restart", "value": "not_on_medication"}, {"label": "Stopped but need to restart", "value": "off_medication_needs_restart"}, {"label": "On medication — I have enough", "value": "have_enough_medication"}, {"label": "Need a refill today", "value": "renewal_now"}, {"label": "I'll wait on results to decide", "value": "renewal_wait_for_results"}, {"label": "Other (please specify)", "value": "other"}], "customConditional": "show = !!data.thyroid_medication_history && data.thyroid_medication_history === 'yes';", "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "thyroid_medication_format", "type": "radio", "input": true, "label": "<div>Which type of thyroid medication do you currently take or were on in the past?<br><ul style='margin:0;padding-left:18px;'><li><b>Pill format</b> &nbsp;(e.g. Synthroid, Eltroxin, Levothyroxine)</li><li><b>Desiccated thyroid extract</b> &nbsp;(e.g. Armour, ERFA)</li></ul><br></div>", "values": [{"label": "Standard thyroid hormone (levothyroxine, Synthroid, Eltroxin)", "value": "pill_format"}, {"label": "Desiccated thyroid (e.g. <PERSON><PERSON> Thyroid, ERFA, NP Thyroid, Nature-Throid)", "value": "desiccated_format"}, {"label": "Other (specify)", "value": "other_format"}], "customConditional": "show = data.thyroid_medication_history === 'yes' && data.thyroid_medication_renewal_choice && data.thyroid_medication_renewal_choice !== 'not_on_medication';", "tableView": true}, {"key": "thyroid_medication_format_other", "type": "textfield", "input": true, "label": "Please specify the thyroid medication format:", "tableView": true, "customConditional": "show = data.thyroid_medication_format === 'other_format';"}, {"key": "thyroid_brand_synthetic", "type": "radio", "input": true, "label": "Which brand of synthetic thyroid medication do/did you take?", "values": [{"label": "Synth<PERSON>", "value": "synthroid"}, {"label": "Eltroxin", "value": "eltroxin"}, {"label": "Euthyrox", "value": "euthyrox"}, {"label": "Levothyroxine (generic)", "value": "levothyroxine_generic"}, {"label": "Other", "value": "other_synthetic"}, {"label": "None of the above", "value": "none"}], "validate": {"required": true}, "customConditional": "show = data.thyroid_medication_format === 'pill_format';", "tableView": true, "optionsLabelPosition": "right"}, {"key": "thyroid_brand_desiccated", "type": "radio", "input": true, "label": "Which brand of desiccated thyroid extract do/did you take?", "values": [{"label": "<PERSON><PERSON>", "value": "armour"}, {"label": "ERFA Thyroid", "value": "erfa"}, {"label": "NP Thyroid", "value": "np_thyroid"}, {"label": "Nature-Throid", "value": "nature_throid"}, {"label": "Other", "value": "other_desiccated"}, {"label": "None of the above", "value": "none"}], "validate": {"required": true}, "customConditional": "show = data.thyroid_medication_format === 'desiccated_format';", "tableView": true, "optionsLabelPosition": "right"}, {"key": "same_dose_every_day", "type": "radio", "input": true, "label": "Do/did you take the same dose every day or does it vary through the week?", "values": [{"label": "Same dose every day", "value": "yes"}, {"label": "Varies during the week", "value": "no"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = (data.thyroid_medication_format === 'pill_format' && !!data.thyroid_brand_synthetic) || (data.thyroid_medication_format === 'desiccated_format' && !!data.thyroid_brand_desiccated);"}, {"key": "thyroid_brand_other_text", "type": "textfield", "input": true, "label": "Please specify the brand:", "tableView": true, "customConditional": "show = data.thyroid_brand_synthetic === 'other_synthetic' || data.thyroid_brand_desiccated === 'other_desiccated';"}, {"key": "daily_thyroid_dose_pill", "type": "select", "input": true, "label": "Select the dose you take/took daily (pill format):", "widget": "html5", "data": {"values": [{"label": "25 mcg", "value": "25mcg"}, {"label": "37.5 mcg", "value": "37.5mcg"}, {"label": "50 mcg", "value": "50mcg"}, {"label": "62.5 mcg", "value": "62.5mcg"}, {"label": "75 mcg", "value": "75mcg"}, {"label": "88 mcg", "value": "88mcg"}, {"label": "100 mcg", "value": "100mcg"}, {"label": "112 mcg", "value": "112mcg"}, {"label": "125 mcg", "value": "125mcg"}, {"label": "137 mcg", "value": "137mcg"}, {"label": "150 mcg", "value": "150mcg"}, {"label": "175 mcg", "value": "175mcg"}, {"label": "200 mcg", "value": "200mcg"}, {"label": "225 mcg", "value": "225mcg"}, {"label": "250 mcg", "value": "250mcg"}, {"label": "300 mcg", "value": "300mcg"}, {"label": "No Dose", "value": "no_dose"}, {"label": "Other", "value": "other"}]}, "validate": {"required": true}, "customConditional": "show = data.same_dose_every_day === 'yes' && data.thyroid_medication_format === 'pill_format' && !!data.thyroid_brand_synthetic && data.thyroid_brand_synthetic !== 'none';", "tableView": true}, {"key": "daily_thyroid_dose_desiccated", "type": "select", "input": true, "label": "Select the dose you take/took daily:", "widget": "html5", "data": {"values": [{"label": "15 mg (¼ grain)", "value": "15mg"}, {"label": "30 mg (½ grain)", "value": "30mg"}, {"label": "60 mg (1 grain)", "value": "60mg"}, {"label": "90 mg (1.5 grains)", "value": "90mg"}, {"label": "120 mg (2 grains)", "value": "120mg"}, {"label": "150 mg (2.5 grains)", "value": "150mg"}, {"label": "180 mg (3 grains)", "value": "180mg"}, {"label": "More than 180 mg", "value": "more_than_180mg"}, {"label": "No Dose", "value": "no_dose"}, {"label": "Other", "value": "other"}]}, "validate": {"required": true}, "tableView": true, "customConditional": "show = data.same_dose_every_day === 'yes' && data.thyroid_medication_format === 'desiccated_format' && !!data.thyroid_brand_desiccated && data.thyroid_brand_desiccated !== 'none' && ['have_enough_medication','renewal_now','renewal_wait_for_results','off_medication_needs_restart'].includes(data.thyroid_medication_renewal_choice);"}, {"key": "weekly_thyroid_dosing_grid", "type": "datagrid", "tableView": true, "input": true, "label": "Weekly Thyroid Dosing Schedule (Pill Format)", "reorder": false, "addAnother": false, "removeRow": false, "rowClass": "condensed-grid", "defaultOpen": true, "customConditional": "show = data.same_dose_every_day === 'no' && data.thyroid_medication_format === 'pill_format' && !!data.thyroid_brand_synthetic && data.thyroid_brand_synthetic !== 'none';", "components": [{"key": "day", "type": "textfield", "input": true, "label": "Day", "disabled": true, "defaultValue": "", "tableView": true}, {"key": "dose", "type": "select", "input": true, "label": "<PERSON><PERSON>", "widget": "html5", "data": {"values": [{"label": "25 mcg", "value": "25mcg"}, {"label": "37.5 mcg", "value": "37.5mcg"}, {"label": "50 mcg", "value": "50mcg"}, {"label": "62.5 mcg", "value": "62.5mcg"}, {"label": "75 mcg", "value": "75mcg"}, {"label": "88 mcg", "value": "88mcg"}, {"label": "100 mcg", "value": "100mcg"}, {"label": "112 mcg", "value": "112mcg"}, {"label": "125 mcg", "value": "125mcg"}, {"label": "137 mcg", "value": "137mcg"}, {"label": "150 mcg", "value": "150mcg"}, {"label": "175 mcg", "value": "175mcg"}, {"label": "200 mcg", "value": "200mcg"}, {"label": "225 mcg", "value": "225mcg"}, {"label": "250 mcg", "value": "250mcg"}, {"label": "300 mcg", "value": "300mcg"}, {"label": "No Dose", "value": "no_dose"}, {"label": "Other", "value": "other"}]}, "dataSrc": "values", "tableView": true, "validate": {"required": true}, "calculateValue": "if (!instance.parent?.root?.data?.weekly_thyroid_dosing_grid?.[instance.rowIndex]?.dose && data.weekly_thyroid_dosing_grid?.[0]?.dose) { value = data.weekly_thyroid_dosing_grid[0].dose; }"}], "defaultValue": [{"day": "Monday"}, {"day": "Tuesday"}, {"day": "Wednesday"}, {"day": "Thursday"}, {"day": "Friday"}, {"day": "Saturday"}, {"day": "Sunday"}]}, {"key": "weekly_desiccated_dosing_grid", "type": "datagrid", "tableView": true, "input": true, "label": "Weekly Thyroid Dosing Schedule (Desiccated Format)", "reorder": false, "addAnother": false, "removeRow": false, "rowClass": "condensed-grid", "defaultOpen": true, "customConditional": "show = data.same_dose_every_day === 'no' && data.thyroid_medication_format === 'desiccated_format' && !!data.thyroid_brand_desiccated && data.thyroid_brand_desiccated !== 'none' && ['have_enough_medication','renewal_now','renewal_wait_for_results','off_medication_needs_restart'].includes(data.thyroid_medication_renewal_choice);", "components": [{"key": "day", "type": "textfield", "input": true, "label": "Day", "disabled": true, "defaultValue": "", "tableView": true}, {"key": "dose", "type": "select", "input": true, "label": "Dose (mg / grains)", "widget": "html5", "data": {"values": [{"label": "15 mg (¼ grain)", "value": "15mg"}, {"label": "30 mg (½ grain)", "value": "30mg"}, {"label": "60 mg (1 grain)", "value": "60mg"}, {"label": "90 mg (1.5 grains)", "value": "90mg"}, {"label": "120 mg (2 grains)", "value": "120mg"}, {"label": "150 mg (2.5 grains)", "value": "150mg"}, {"label": "180 mg (3 grains)", "value": "180mg"}, {"label": "More than 180 mg", "value": "more_than_180mg"}, {"label": "No Dose", "value": "no_dose"}, {"label": "Other", "value": "other"}]}, "dataSrc": "values", "validate": {"required": true}, "tableView": true, "calculateValue": "if (!instance.parent?.root?.data?.weekly_desiccated_dosing_grid?.[instance.rowIndex]?.dose) { if (data.weekly_desiccated_dosing_grid?.[0]?.dose) { value = data.weekly_desiccated_dosing_grid[0].dose; } else if (data.daily_thyroid_dose_desiccated) { value = data.daily_thyroid_dose_desiccated; } }"}], "defaultValue": [{"day": "Monday"}, {"day": "Tuesday"}, {"day": "Wednesday"}, {"day": "Thursday"}, {"day": "Friday"}, {"day": "Saturday"}, {"day": "Sunday"}]}, {"key": "heading_thyroid_dosing_summary", "type": "content", "input": false, "label": "Heading", "html": "<h4>Review Your Weekly Thyroid Dosing</h4><p>Please confirm that your weekly dosing schedule below is accurate.</p>", "customConditional": "show = (data.daily_thyroid_dose || (data.weekly_thyroid_dosing_grid?.length > 0) || (data.weekly_desiccated_dosing_grid?.length > 0));"}, {"key": "thyroid_dosing_summary_text", "type": "content", "input": false, "label": "Weekly Thyroid Dosing Summary:", "html": "<div>{{ (data.same_dose_every_day === 'yes' && (data.daily_thyroid_dose?.label || data.daily_thyroid_dose)) ? ('<b>Daily Dose:</b> ' + (data.daily_thyroid_dose.label || data.daily_thyroid_dose)) : (data.weekly_thyroid_dosing_grid ? data.weekly_thyroid_dosing_grid.map(x => (x.day + ': ' + (x.dose?.label || x.dose || 'Not specified'))).join('<br>') : (data.weekly_desiccated_dosing_grid ? data.weekly_desiccated_dosing_grid.map(x => (x.day + ': ' + (x.dose?.label || x.dose || 'Not specified'))).join('<br>') : '')) }}</div>", "refreshOn": "data", "customConditional": "show = (data.daily_thyroid_dose || (data.weekly_thyroid_dosing_grid?.length > 0) || (data.weekly_desiccated_dosing_grid?.length > 0));"}, {"key": "confirm_weekly_dosing", "type": "radio", "input": true, "label": "Is this thyroid dosing schedule accurate?", "values": [{"label": "Yes, that's accurate", "value": "yes"}, {"label": "No, I need to change it", "value": "no"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = (data.same_dose_every_day === 'yes' && data.daily_thyroid_dose) || (data.weekly_thyroid_dosing_grid?.length > 0) || (data.weekly_desiccated_dosing_grid?.length > 0);"}, {"key": "confirm_weekly_dosing_explanation", "type": "textarea", "input": true, "label": "Please explain your correct thyroid dosing schedule:", "validate": {"required": true}, "autoExpand": true, "tableView": true, "customConditional": "show = data.confirm_weekly_dosing === 'no';"}, {"key": "daily_thyroid_dose_summary", "type": "content", "input": false, "label": "Daily Thyroid Dose Summary", "html": "<div>{{ (() => { const synthMap = { synthroid:'Synthroid', eltroxin:'Eltroxin', euthyrox:'Euthyrox', levothyroxine_generic:'Levothyroxine (generic)', other_synthetic:'Other' }; const dessMap = { armour:'Armour Thyroid', erfa:'ERFA Thyroid', np_thyroid:'NP Thyroid', nature_throid:'Nature-Throid', other_desiccated:'Other' }; const brand = (data.thyroid_medication_format==='pill_format') ? synthMap[data.thyroid_brand_synthetic] : dessMap[data.thyroid_brand_desiccated]; const dose  = (data.thyroid_medication_format==='pill_format') ? (data.daily_thyroid_dose_pill?.label||data.daily_thyroid_dose_pill||'Not specified') : (data.daily_thyroid_dose_desiccated?.label||data.daily_thyroid_dose_desiccated||'Not specified'); return 'Daily Dose: ' + (brand||'') + ' – ' + dose; })() }}</div>", "refreshOn": "data", "customConditional": "show = data.same_dose_every_day === 'yes' && ((data.thyroid_medication_format === 'pill_format' && data.daily_thyroid_dose_pill) || (data.thyroid_medication_format === 'desiccated_format' && data.daily_thyroid_dose_desiccated));"}, {"key": "confirm_daily_dose_summary", "type": "radio", "input": true, "label": "Is this daily thyroid dose correct?", "values": [{"label": "Yes, that's correct", "value": "yes"}, {"label": "No, I need to change it", "value": "no"}], "validate": {"required": true}, "optionsLabelPosition": "right", "customConditional": "show = data.same_dose_every_day === 'yes' && ((data.thyroid_medication_format === 'pill_format' && data.daily_thyroid_dose_pill) || (data.thyroid_medication_format === 'desiccated_format' && data.daily_thyroid_dose_desiccated));"}]}