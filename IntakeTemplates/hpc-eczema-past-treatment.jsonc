{"components": [{"key": "heading_past_treatments", "html": "</br><h4>Past Treatments</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "used_prescription_past", "type": "radio", "input": true, "label": "Have you used prescription skin treatments for this rash in the past?", "inline": false, "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Prescription treatment used before:", "optionsLabelPosition": "right"}, {"key": "past_treatment_effectiveness", "type": "radio", "input": true, "label": "How well did past prescription treatments work for your rash overall?", "inline": false, "values": [{"label": "Very effective - rash cleared completely", "value": "very_effective"}, {"label": "Somewhat effective - helped but didn't fully clear", "value": "partial"}, {"label": "Not effective - didn't improve my rash", "value": "ineffective"}, {"label": "Not sure / varied by product", "value": "unsure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Effectiveness of past treatment:", "customConditional": "show = data.used_prescription_past === 'yes';"}, {"key": "rx_product_grid", "type": "<PERSON><PERSON><PERSON>", "label": "Add each prescription product you've used and where you applied it:", "rowClass": "mb-2", "tableView": true, "templates": {"edit": "<button type='button' class='btn btn-sm btn-light'><i class='fa fa-edit'></i></button>", "remove": "<button type='button' class='btn btn-sm btn-danger'><i class='fa fa-trash'></i></button>"}, "addAnother": "+ Add Another", "components": [{"key": "rx_name", "data": {"values": [{"label": "Betamethasone dipropionate 0.05 % (Diprosone®)", "value": "betamethasone_dipropionate"}, {"label": "Betamethasone valerate 0.05 % (Betaderm® 0.05 %)", "value": "betamethasone_valerate_0_05"}, {"label": "Betamethasone valerate 0.1 % (Betaderm® 0.1 %)", "value": "betamethasone_valerate_0_1"}, {"label": "Clobetasol propionate 0.05 % (Dermovate®)", "value": "clobetasol"}, {"label": "Crisaborole 2 % ointment (Eucrisa®)", "value": "crisaborole"}, {"label": "Dermotic® ear oil", "value": "dermotic"}, {"label": "Desonide 0.05 %", "value": "desonide"}, {"label": "Desoximetasone 0.25 %", "value": "desoximetasone_0_25"}, {"label": "Fluocinonide 0.05 % (Lyderm®)", "value": "fluocinonide"}, {"label": "Halobetasol propionate 0.05 % (Ultravate®)", "value": "halobetasol"}, {"label": "Hydrocortisone 0.5 %", "value": "hydrocortisone_0_5"}, {"label": "Hydrocortisone 1 %", "value": "hydrocortisone_1"}, {"label": "Hydrocortisone 2.5 %", "value": "hydrocortisone_2_5"}, {"label": "Mometasone furoate 0.1 % (Elocom®)", "value": "mometasone"}, {"label": "Pimecrolimus 1 % cream (Elidel®)", "value": "pimecrolimus"}, {"label": "Roflumilast 0.15 % cream (Zoryve®)", "value": "roflumilast_0_15"}, {"label": "Roflumilast 0.3 % cream (Zoryve®)", "value": "roflumilast_0_3"}, {"label": "Tacrolimus 0.1 % (Protopic®)", "value": "protopic"}, {"label": "Triamcinolone acetonide 0.1 % (Kenalog®)", "value": "triamcinolone"}, {"label": "Other prescription", "value": "other"}]}, "type": "select", "tableView": true, "label": "Product", "style": {"wordBreak": "break-word", "whiteSpace": "normal"}, "widget": "html5", "validate": {"required": true}, "labelWidth": 25, "labelMargin": 1}, {"key": "rx_other_name", "tableView": true, "type": "textfield", "label": "Other product", "validate": {"required": true}, "customConditional": "show = data.parent && data.parent.rx_name === 'other';"}, {"key": "rx_where", "type": "selectboxes", "tableView": true, "label": "Where", "values": [{"label": "Face", "value": "face"}, {"label": "Eyelids", "value": "eyelids"}, {"label": "Neck", "value": "neck"}, {"label": "<PERSON><PERSON><PERSON>", "value": "scalp"}, {"label": "Chest", "value": "chest"}, {"label": "Back", "value": "back"}, {"label": "Abdomen", "value": "abdomen"}, {"label": "Arms", "value": "arms"}, {"label": "Elbows", "value": "elbows"}, {"label": "Hands", "value": "hands"}, {"label": "Fingers", "value": "fingers"}, {"label": "Palms / Soles", "value": "palms_soles"}, {"label": "Back of hands / feet", "value": "dorsal"}, {"label": "Legs", "value": "legs"}, {"label": "Knees", "value": "knees"}, {"label": "<PERSON><PERSON><PERSON>", "value": "groin"}, {"label": "Folds / Creases", "value": "folds"}, {"label": "Other", "value": "other"}, {"label": "None of the above", "value": "none", "customClass": "mt-n3"}], "validate": {"required": true}, "inputType": "checkbox", "labelWidth": 25, "labelMargin": 1}, {"key": "rx_where_other", "tableView": true, "type": "textfield", "label": "Other area", "validate": {"required": true}, "customConditional": "show = data.parent && data.parent.rx_where && data.parent.rx_where.other;"}, {"key": "rx_current", "type": "radio", "label": "Using", "tableView": true, "inline": true, "values": [{"label": "Currently", "value": "current"}, {"label": "Past", "value": "past"}], "validate": {"required": true}, "labelWidth": 25, "labelMargin": 1}, {"key": "rx_effect", "tableView": true, "data": {"values": [{"label": "Cleared the rash", "value": "cleared"}, {"label": "Helped somewhat", "value": "partial"}, {"label": "No change", "value": "none"}, {"label": "Made it worse", "value": "worse"}, {"label": "Not sure", "value": "unsure"}]}, "type": "select", "label": "Effect", "widget": "html5", "validate": {"required": true}, "labelWidth": 25, "labelMargin": 1}, {"key": "rx_freq", "tableView": true, "data": {"values": [{"label": "Once daily", "value": "qd"}, {"label": "Twice daily", "value": "bid"}, {"label": "3 × +/day", "value": "tid_plus"}, {"label": "Only as needed", "value": "prn"}, {"label": "Not sure", "value": "unsure"}]}, "type": "select", "label": "Frequency", "widget": "html5", "validate": {"required": true}, "labelWidth": 25, "labelMargin": 1}, {"key": "rx_longest", "tableView": true, "data": {"values": [{"label": "≤ 7 days", "value": "le7d"}, {"label": "8–14 days", "value": "8_14d"}, {"label": "15–30 days", "value": "15_30d"}, {"label": "1–3 months", "value": "1_3m"}, {"label": "3–6 months", "value": "3_6m"}, {"label": "6–12 months", "value": "6_12m"}, {"label": "> 12 months", "value": "gt12m"}, {"label": "Not sure", "value": "unsure"}]}, "type": "select", "label": "Duration", "widget": "html5", "validate": {"required": true}, "labelWidth": 25, "labelMargin": 1}, {"key": "rx_form", "tableView": true, "data": {"values": [{"label": "Cream", "value": "cream"}, {"label": "Ointment (greasy)", "value": "ointment"}, {"label": "Lotion (runny)", "value": "lotion"}, {"label": "Foam / spray", "value": "foam"}, {"label": "Not sure", "value": "unsure"}]}, "type": "select", "label": "Form", "widget": "html5", "validate": {"required": true}, "labelWidth": 25, "labelMargin": 1}], "rowTemplate": "<div class='card shadow-sm w-100'> \n          <div class='card-body py-2 px-3'> \n            <div class='row small'>\n              <div class='col-sm-2'><strong>{{ data.rx_name === 'other' ? data.rx_other_name : _.startCase(data.rx_name.replace(/_/g,' ')) }}</strong></div>\n              <div class='col-sm-2'>{{ _.join(_.map(_.keys(_.pickBy(data.rx_where)), _.startCase), ', ') }}</div>\n              <div class='col-sm-1'>{{ data.rx_current === 'current' ? 'Yes' : 'Past' }}</div>\n              <div class='col-sm-2'>{{ (data.rx_effect || '').replace('_',' ') | title }}</div>\n              <div class='col-sm-1'>{{ ({qd:'1×/day',bid:'2×/day',tid_plus:'3×+',prn:'PRN',unsure:'N/S'}[data.rx_freq] || '') }}</div>\n              <div class='col-sm-2'>{{ ({le7d:'≤ 7 d','8_14d':'8–14 d','15_30d':'15–30 d','1_3m':'1–3 m','3_6m':'3–6 m','6_12m':'6–12 m','gt12m':'12 m+'}[data.rx_longest] || '') }}</div>\n              <div class='col-sm-2'>{{ _.startCase(data.rx_form || '') }}</div>\n            </div>\n          </div> \n        </div>", "confirm_label": "Prescription product details:", "displayAsTable": false, "headerTemplate": "<div class='row'>\n  <div class='col-sm-2'>Prod</div>\n  <div class='col-sm-2'>Where</div>\n  <div class='col-sm-1'>Using</div>\n  <div class='col-sm-2'>Effect</div>\n  <div class='col-sm-1'>Freq</div>\n  <div class='col-sm-2'>Duration</div>\n  <div class='col-sm-2'>Form</div>\n</div>", "customConditional": "show = data.used_prescription_past === 'yes' && !!data.past_treatment_effectiveness;"}]}