{"name": "result", "type": "form", "title": "Result", "display": "form", "components": [{"key": "skus", "type": "textfield", "input": true, "label": "SKUs", "hidden": true, "visible": false, "disabled": true, "multiple": true, "hideLabel": true, "tableView": false, "spellcheck": false, "clearOnHide": false}, {"key": "originalAssays", "type": "textfield", "input": true, "label": "Original Assays", "hidden": true, "visible": false, "disabled": true, "multiple": true, "hideLabel": true, "tableView": false, "spellcheck": false, "clearOnHide": false}, {"key": "in_person_care_required", "type": "textfield", "input": true, "label": "In person care required", "hidden": true, "disabled": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = !!data.symptoms_greater_than_7_days || !!data.pregnant || !!data.immunosuppression;"}, {"key": "showSubmit", "type": "textfield", "input": true, "label": "", "hidden": true, "visible": false, "disabled": true, "hideLabel": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = !data.needle_stick && !data.in_person_care_required && !_.some(_.values(data.std_red_flag_symptoms)) && !data.no_herpes_medication && !data.no_birth_control;"}, {"key": "userBulletKeys", "type": "textfield", "input": true, "label": "User Bullets:", "hidden": true, "disabled": true, "multiple": true, "hideLabel": true, "tableView": false, "spellcheck": false, "clearOnHide": false, "calculateValue": "value = _.union(_.keys(_.pickBy(data.selected_bullets)), (!!_.some(_.values(data.uti_symptoms)) ? ['UTI'] : []))", "optionsLabelPosition": "right"}, {"key": "assay_choices", "type": "textfield", "input": true, "label": "Selected assays values:", "hidden": true, "disabled": true, "multiple": true, "hideLabel": true, "tableView": false, "spellcheck": false, "clearOnHide": false, "calculateValue": "value = _.intersection(data.originalAssays, data.userBulletKeys);", "optionsLabelPosition": "right"}, {"key": "show_birth_control", "type": "htmlelement", "attrs": [{"attr": "", "value": ""}], "input": false, "label": "HTML", "content": "You're eligible to discuss prescription birth control medication.", "tableView": false, "customConditional": "show = data.assay_choices.indexOf('RX-OCP') > -1 && !data.no_birth_control;"}, {"key": "insuredDisplay", "type": "mylist", "input": true, "label": "", "multiple": true, "iconClass": "fa-regular fa-circle-check", "iconColor": "green", "tableView": false, "defaultValue": [], "calculateValue": {"var": "data.userBulletKeys"}, "clearOnRefresh": true, "customConditional": "show = !!data.showSubmit && !!data.validInsurance && !!_.some(data.userBulletKeys);"}, {"key": "uninsuredSummary", "type": "uninsuredsummary", "input": true, "label": "", "multiple": true, "tableView": false, "defaultValue": [], "calculateValue": {"_difference": [{"var": "data.uninsured"}, {"if": [{"!=": [{"var": "data.sex"}, "female"]}, ["TRICH"], []]}]}, "customConditional": "show = !!data.showSubmit && !!_.some(data.uninsured);"}, {"key": "vswbRecommendedNote", "type": "htmlelement", "attrs": [{"attr": "", "value": ""}], "input": false, "label": "HTML", "content": "Based on your symptoms we recommend <button type='submit' class='btn btn-link-primary' name='add-bv-test'>adding a BV test (click to add)</button><br/><br/>If you have already had a negative vaginal swab result in the past 7 days we recommend a speculum examination at a walk-in-clinic for further assessment", "tableView": false, "customConditional": "show = !!data.showSubmit && data.assay_choices && data.assay_choices.indexOf('VSWB') == -1 && data.sex == 'female' && !!_.some(_.values(data.other_std_symptoms));"}, {"key": "stdRecommendedNote", "type": "htmlelement", "attrs": [{"attr": "", "value": ""}], "input": false, "label": "HTML", "content": "Based on your symptoms we recommend <button type='submit' class='btn btn-link-primary' name='add-std-test'>adding a STD test (click to add)</button>", "tableView": false, "customConditional": "show = !!data.showSubmit && data.assay_choices && data.assay_choices.indexOf('CT') < 0 && !!_.some(_.values(data.other_std_symptoms));"}, {"key": "redFlagsNote", "type": "htmlelement", "attrs": [{"attr": "", "value": ""}], "input": false, "label": "HTML", "content": "<h3 class='text-red'>Please seek immediate attention at an emergency clinic!</h3><p class='text-red'>We are not able to offer you screening. You have symptoms which may be indicative of a serious condition.</p>", "tableView": false, "customConditional": "show = !data.needle_stick && !!_.some(_.values(data.std_red_flag_symptoms));"}, {"key": "noTestNote", "type": "htmlelement", "attrs": [{"attr": "", "value": ""}], "input": false, "label": "HTML", "content": "<h3>We cannot currently offer you screening</h3><p>We are not able to offer you screening as your condition requires in-person care.</p>", "tableView": false, "customConditional": "show = !data.needle_stick && !_.some(_.values(data.std_red_flag_symptoms)) && !!data.in_person_care_required;"}, {"key": "needle_stickNote", "type": "htmlelement", "attrs": [{"attr": "", "value": ""}], "input": false, "label": "HTML", "content": "<h3 class='text-red'>Please seek immediate attention at an emergency clinic!</h3><p class='text-red'>Exposure to a needle stick or contaminated blood/blood products may require post-exposure prophylaxis urgently.</p>", "tableView": false, "customConditional": "show = !!data.needle_stick;"}, {"key": "no_herpes_medication", "type": "htmlelement", "attrs": [{"attr": "", "value": ""}], "input": true, "label": "HTML", "content": "<h3 class='text-red'>We cannot currently offer prescription medication for herpes. We advise in person assessment with a physician</h3>", "hideLabel": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = (data.assay_choices.indexOf('RX-HSVS') > -1 || data.assay_choices.indexOf('RX-HSVO') > -1) && (_.some(_.values(data.herpes_contraindications)) || data.diagnosis_new_hsv == true || data.diagnosis_shingles == true || data.ocular_herpes || data.diagnosis_uncertain_hsv == true);", "customConditional": "show = !!data.no_herpes_medication"}, {"key": "no_birth_control", "type": "htmlelement", "attrs": [{"attr": "", "value": ""}], "input": true, "content": "<h3 class='text-red'>We cannot currently offer you birth control medication</h3>", "hideLabel": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.assay_choices.indexOf('RX-OCP') > -1 && (_.some(_.values(data.birth_control_contraindication)) || data.blood_pressure_reading != 'normotensive' || data.smoking_status == true);", "customConditional": "show = !!data.no_birth_control"}, {"key": "pleaseClickSubmit", "type": "htmlelement", "attrs": [{"attr": "", "value": ""}], "input": false, "label": "HTML", "content": "To continue please click “Submit Form”", "tableView": false, "refreshOnChange": false, "customConditional": "show = !!data.showSubmit;"}]}