{"components": [{"key": "heading_prior_testing", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "html": "<h3>Prior Test Results</h3>"}, {"key": "prior_psa_ever", "type": "radio", "input": true, "label": "Have you completed PSA testing in the past?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "unsure"}], "tableView": true, "refreshOnChange": false, "validate": {"required": true}, "confirm_label": "Prior PSA testing:"}, {"key": "heading_prior_psa_grid", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "html": "<h3>PSA Details</h3>", "customConditional": "show = data.prior_psa_ever === 'yes';"}, {"key": "psa_results_grid", "type": "<PERSON><PERSON><PERSON>", "label": "Add each prior PSA test:", "addAnother": "+ Add Another Test", "tableView": true, "refreshOnChange": false, "customConditional": "show = data.prior_psa_ever === 'yes';", "components": [{"key": "psa_month", "type": "select", "input": true, "label": "Month", "widget": "html5", "tableView": true, "refreshOnChange": false, "data": {"values": [{"label": "Unknown", "value": "unknown"}, {"label": "January", "value": "jan"}, {"label": "February", "value": "feb"}, {"label": "March", "value": "mar"}, {"label": "April", "value": "apr"}, {"label": "May", "value": "may"}, {"label": "June", "value": "jun"}, {"label": "July", "value": "jul"}, {"label": "August", "value": "aug"}, {"label": "September", "value": "sep"}, {"label": "October", "value": "oct"}, {"label": "November", "value": "nov"}, {"label": "December", "value": "dec"}]}, "validate": {"required": true}}, {"key": "psa_year", "type": "number", "input": true, "label": "Year (YYYY)", "placeholder": "e.g., 2023", "tableView": true, "refreshOnChange": false, "validate": {"min": 1900, "max": 2100}}, {"key": "psa_type", "type": "select", "input": true, "label": "Assay type", "widget": "html5", "tableView": true, "refreshOnChange": false, "data": {"values": [{"label": "Total PSA", "value": "total"}, {"label": "Free PSA", "value": "free"}, {"label": "Not sure", "value": "unsure"}]}, "validate": {"required": true}}, {"key": "psa_value", "type": "number", "input": true, "label": "PSA value", "placeholder": "e.g., 3.20 (enter 0.00 if unknown)", "description": "If you don’t remember the value, enter 0.00.", "tableView": true, "refreshOnChange": false, "validate": {"min": 0, "step": 0.01}}, {"key": "psa_units", "type": "select", "input": true, "label": "Units", "widget": "html5", "tableView": true, "refreshOnChange": false, "defaultValue": "ng_ml", "data": {"values": [{"label": "ng/mL (&micro;g/L)", "value": "ng_ml"}, {"label": "&micro;g/L", "value": "ug_l"}, {"label": "mcg/L", "value": "mcg_l"}]}, "validate": {"required": true}}], "rowTemplate": "<div class='card shadow-sm w-100'><div class='card-body py-2 px-3'><div class='row small'><div class='col-sm-4'><strong>{{ (data.psa_month==='unknown' ? 'Month ?' : _.startCase(data.psa_month)) }} {{ data.psa_year || '' }}</strong></div><div class='col-sm-4'>{{ ({total:'Total PSA',free:'Free PSA',unsure:'Not sure'}[data.psa_type] || '') }}</div><div class='col-sm-4'>{{ (data.psa_value===0) ? 'Unknown' : (data.psa_value ? (data.psa_value + ' ' + ({ng_ml:'ng/mL (\\u00B5g/L)',ug_l:'\\u00B5g/L',mcg_l:'mcg/L'}[data.psa_units] || '')) : '') }}</div></div></div></div>"}]}