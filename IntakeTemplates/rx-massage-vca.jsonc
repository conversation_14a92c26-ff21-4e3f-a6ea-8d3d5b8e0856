{"components": [{"key": "seeking_massage_prescription", "type": "radio", "input": true, "label": "Are you seeking a massage therapy prescription for insurance coverage purposes?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "confirmLabel": "Seeking massage therapy prescription:", "optionsLabelPosition": "right"}, {"key": "reason_massage_prescription", "type": "selectboxes", "input": true, "label": "Please select your reason for seeking a massage therapy prescription:", "values": [{"label": "Muscle tightness or stiffness", "value": "muscle_tightness", "shortcut": ""}, {"label": "Pain that has already been checked by a healthcare provider", "value": "checked_pain", "shortcut": ""}, {"label": "Recovery from an injury that was previously assessed", "value": "injury_recovery", "shortcut": ""}, {"label": "General stress management and relaxation", "value": "stress_relaxation", "shortcut": ""}], "inputType": "checkbox", "tableView": true, "confirmLabel": "Reason for massage therapy prescription:", "customConditional": "show = !!data.seeking_massage_prescription && data.seeking_massage_prescription === 'yes';", "optionsLabelPosition": "right"}, {"key": "no_reason_massage", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "Please select at least one reason or confirm none apply."}, "validate": {"custom": "valid = !!data.no_reason_massage || !!_.some(_.values(data.reason_massage_prescription));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = !!data.seeking_massage_prescription && data.seeking_massage_prescription === 'yes';"}, {"key": "concerns_massage", "type": "selectboxes", "input": true, "label": "Are you concerned about any of the following?", "values": [{"label": "A new pain/injury that has not been checked by a healthcare provider", "value": "new_pain", "shortcut": ""}, {"label": "Concerned about a broken bone", "value": "broken_bone", "shortcut": ""}, {"label": "Concern about weakness, coordination, or changes in movement that haven't been assessed", "value": "movement_changes", "shortcut": ""}], "inputType": "checkbox", "tableView": true, "confirmLabel": "Concerns:", "customConditional": "show = !!data.reason_massage_prescription && (data.reason_massage_prescription.muscle_tightness || data.reason_massage_prescription.checked_pain || data.reason_massage_prescription.injury_recovery || data.reason_massage_prescription.stress_relaxation);", "optionsLabelPosition": "right"}, {"key": "no_concerns_massage", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "Please select at least one concern or confirm none apply."}, "validate": {"custom": "valid = !!data.no_concerns_massage || !!_.some(_.values(data.concerns_massage));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = !!data.reason_massage_prescription && (data.reason_massage_prescription.muscle_tightness || data.reason_massage_prescription.checked_pain || data.reason_massage_prescription.injury_recovery || data.reason_massage_prescription.stress_relaxation);"}, {"key": "duration_pain", "type": "radio", "input": true, "label": "How long have you been experiencing this issue?", "values": [{"label": "Less than 4 weeks", "value": "less_than_4_weeks"}, {"label": "More than 4 weeks", "value": "more_than_4_weeks"}], "validate": {"required": true}, "tableView": true, "confirmLabel": "Duration of issue:", "customConditional": "show = !!data.no_concerns_massage;", "optionsLabelPosition": "right"}, {"key": "pain_changes", "type": "radio", "input": true, "label": "Have you noticed any changes in your pain, such as spreading to new areas or becoming constant when it was previously occasional?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "confirmLabel": "Pain changes:", "customConditional": "show = !!data.duration_pain && data.duration_pain === 'more_than_4_weeks';", "optionsLabelPosition": "right"}, {"key": "symptoms_contra", "type": "selectboxes", "input": true, "label": "Are you currently experiencing any of the following symptoms?", "values": [{"label": "Pain that wakes you up at night that was not present before", "value": "night_pain"}, {"label": "Pain that does not change with movement or position", "value": "constant_pain"}, {"label": "Tingling, numbness, or weakness in your arms or legs", "value": "numbness_weakness"}, {"label": "Difficulty with bladder or bowel control", "value": "bladder_bowel_issues"}, {"label": "Unexplained swelling in the painful area", "value": "swelling"}, {"label": "Chest pain or pressure", "value": "chest_pain"}, {"label": "Feeling short of breath", "value": "short_breath"}, {"label": "Feeling lightheaded or faint", "value": "fainting"}, {"label": "Rapid or irregular heartbeat", "value": "irregular_heartbeat"}, {"label": "Feeling overwhelmed, anxious, or low in mood", "value": "mental_health"}], "inputType": "checkbox", "tableView": true, "confirmLabel": "Symptoms present:", "customConditional": "show = !!data.pain_changes && data.pain_changes === 'no';", "optionsLabelPosition": "right"}, {"key": "no_symptoms_contra", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "Please select at least one symptom or confirm none apply."}, "validate": {"custom": "valid = !!data.no_symptoms_contra || !!_.some(_.values(data.symptoms_contra));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = !!data.pain_changes && data.pain_changes === 'no';"}, {"key": "no_indications", "type": "textfield", "input": true, "label": "No Indication:", "hidden": true, "disabled": true, "multiple": false, "tableView": true, "clearOnHide": false, "defaultValue": true, "calculateValue": "value = (data.seeking_massage_prescription === 'yes' && !!_.some(_.values(data.reason_massage_prescription)) && data.duration_pain === 'more_than_4_weeks') ? false : true;", "refreshOnChange": true}, {"key": "contraindication_keys", "type": "textfield", "input": true, "label": "Contraindication keys:", "hidden": true, "disabled": true, "multiple": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "value = [];\n\n// If any concerning symptoms\nif (_.some(_.values(data.symptoms_contra))) {\n  value.push('red_flag_symptoms');\n}\n\n// If concerns selected\nif (_.some(_.values(data.concerns_massage))) {\n  value.push('massage_concerns');\n}\n\n// If pain is worsening\nif (data.pain_changes === 'yes') {\n  value.push('worsening_pain');\n}\n\n// Check each individual symptom and add specific keys\nif (data.symptoms_contra) {\n  if (data.symptoms_contra.night_pain) value.push('night_pain');\n  if (data.symptoms_contra.constant_pain) value.push('constant_pain');\n  if (data.symptoms_contra.numbness_weakness) value.push('numbness_weakness');\n  if (data.symptoms_contra.bladder_bowel_issues) value.push('bladder_bowel_issues');\n  if (data.symptoms_contra.swelling) value.push('swelling');\n  if (data.symptoms_contra.chest_pain) value.push('chest_pain');\n  if (data.symptoms_contra.short_breath) value.push('short_breath');\n  if (data.symptoms_contra.fainting) value.push('fainting');\n  if (data.symptoms_contra.irregular_heartbeat) value.push('irregular_heartbeat');\n  if (data.symptoms_contra.mental_health) value.push('mental_health');\n}\n", "refreshOnChange": true}, {"key": "rejected_html", "type": "content", "input": true, "tableView": false, "clearOnHide": false, "defaultValue": "<h3 class='text-red'>You're ineligible for a massage therapy prescription at this time.</h3></br><p>Please consult a physician in person to discuss your symptoms, as further assessment may be needed.</p>", "refreshOnChange": true}, {"key": "approved_html", "type": "content", "input": true, "tableView": false, "clearOnHide": false, "defaultValue": "<h3 class='text-green'>You are eligible for a massage therapy prescription.</h3></br><p>A prescription can be provided based on your symptoms and duration of pain.</p>", "refreshOnChange": true}]}