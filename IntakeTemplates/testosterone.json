{"name": "testosterone", "type": "form", "title": "Testosterone", "display": "form", "components": [{"key": "testosteroneTherapy", "type": "radio", "input": true, "label": "Are you currently on testosterone therapy or using any testosterone derivatives (e.g. anabolic steroids)?", "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "conditional": {"json": {"!!": {"var": "data.showSubmit"}}}}, {"key": "therapyMonths_TT", "type": "radio", "input": true, "label": "How many months have you been on testosterone therapy or using testosterone derivatives?", "inline": false, "values": [{"label": "Less than 3 months", "value": 2, "shortcut": ""}, {"label": "Between 3 to 6 months", "value": 5, "shortcut": ""}, {"label": "Between 6 to 12 months", "value": 10, "shortcut": ""}, {"label": "Greater than 12 months", "value": 13, "shortcut": ""}], "tableView": false, "conditional": {"json": {"and": [{"!!": {"var": "data.showSubmit"}}, {"var": "data.testosteroneTherapy"}]}}, "optionsLabelPosition": "right"}, {"key": "decreaseInLibido", "type": "radio", "input": true, "label": "Do you have a decrease in libido (sex drive)?", "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "conditional": {"json": {"and": [{"!!": {"var": "data.showSubmit"}}, {"!": [{"var": "data.testosteroneTherapy"}]}, {"!=": ["female", {"var": "data.sex"}]}]}}}, {"key": "erectionsLessStrong", "type": "radio", "input": true, "label": "Are your erections less strong?", "values": [{"label": "Yes", "value": true, "shortcut": ""}, {"label": "No", "value": false, "shortcut": ""}], "conditional": {"json": {"and": [{"!!": {"var": "data.showSubmit"}}, {"!": {"var": "data.testosteroneTherapy"}}, {"!=": ["female", {"var": "data.sex"}]}]}}}, {"key": "testosteroneSymptoms", "type": "selectboxes", "input": true, "label": "Do you have any of the following symptoms?", "values": [{"label": "I have a lack of energy", "value": "lackOfEnergy", "shortcut": ""}, {"label": "I have decreased strength and endurance", "value": "decreasedStrength", "shortcut": ""}, {"label": "I have lost height", "value": "lostHeight", "shortcut": ""}, {"label": "I have noticed a decrease 'enjoyment of life'", "value": "decreasedEnjoymentOfLife", "shortcut": ""}, {"label": "I am sad and/or grumpy", "value": "sadOrGrumpy", "shortcut": ""}, {"label": "I am falling asleep after dinner?", "value": "fallingAsleepAfter<PERSON><PERSON><PERSON>", "shortcut": ""}, {"label": "I have noticed a recent deterioration in my ability to play sports", "value": "recentSportsProblem", "shortcut": ""}, {"label": "There has been a recent deterioration in my work performance", "value": "recentWorkProblem", "shortcut": ""}], "inputType": "checkbox", "conditional": {"json": {"and": [{"!!": {"var": "data.showSubmit"}}, {"!": {"var": "data.testosteroneTherapy"}}, {"!=": ["female", {"var": "data.sex"}]}]}}}, {"key": "noTestosteroneSympoms", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = !data.showSubmit || data.noTestosteroneSympoms || _.some(_.values(data.testosteroneSymptoms));"}, "tableView": false, "conditional": {"json": {"and": [{"!!": {"var": "data.showSubmit"}}, {"!": {"var": "data.testosteroneTherapy"}}, {"!=": ["female", {"var": "data.sex"}]}]}}, "customClass": "mt-n3", "defaultValue": false}, {"key": "recTestFreqMonths_TT", "type": "textfield", "input": true, "label": "", "hidden": true, "disabled": true, "tableView": true, "spellcheck": false, "clearOnHide": false, "calculateValue": {"if": [{"var": "data.testosteroneTherapy"}, {"if": [{"<": [12, {"var": "data.therapyMonths_TT"}]}, 12, {"if": [{"<": [6, {"var": "data.therapyMonths_TT"}]}, 6, 3]}]}, {"if": [{"var": "data.decreaseInLibido"}, 6, {"if": [{"var": "data.erectionsLessStrong"}, 6, {"if": [{"<": [3, {"_sum": {"_values": {"var": "data.testosteroneSymptoms"}}}]}, 18, 101]}]}]}]}}]}