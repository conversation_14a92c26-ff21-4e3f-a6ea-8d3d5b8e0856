{"_vid": 0, "name": "window-period", "type": "form", "title": "Window Periods", "display": "form", "settings": {}, "components": [{"key": "windows", "type": "textfield", "input": true, "label": "Window periods (days):", "hidden": true, "disabled": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = {'CT':{'early':7,'late':14},'GC':{'early':7,'late':14},'TRICH':{'early':7,'late':30},'HIV':{'early':44,'late':90},'VDRL':{'early':44,'late':90},'HEPB-SAG':{'early':21,'late':42},'HCV-AB':{'early':60,'late':180},'HSV':{'early':30,'late':120}};"}, {"key": "window_keys", "type": "textfield", "input": true, "label": "Assay keys for window periods:", "hidden": true, "disabled": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = _.pick(data.windows, data.userBulletKeys)"}, {"key": "window_period_heading", "html": "<h2 style='text-align:center;' id='window-periods'><strong>Window Periods</strong></h2>", "type": "content", "input": false, "label": "Window Periods", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.show_all && !_.isEmpty(data.window_keys)"}, {"key": "window_period_explanation", "html": "<div class='small'><p>Lab tests have detection times known as “window periods”. To ensure your tests are as accurate as possible, the following section calculates detection dates.</p><a href='https://docs.teletest.ca/all-about-sti-testing' target='_blank'>You can read more about window periods here.</a></div>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.show_all && !_.isEmpty(data.window_keys)"}, {"key": "last_sex", "type": "radio", "input": true, "label": "When was your last partner exposure (i.e. protected or unprotected sex)?", "inline": false, "values": [{"label": "≤1 week (0-7 days) ago", "value": "0-7_days", "shortcut": ""}, {"label": "1-2 weeks (8-14 days) ago", "value": "8-14_days", "shortcut": ""}, {"label": "2-4 weeks (15-28 days) ago", "value": "15-28_days", "shortcut": ""}, {"label": "1-3 months (29-90 days) ago", "value": "29-90_days", "shortcut": ""}, {"label": ">3 months (90+ days) ago", "value": "90+_days", "shortcut": ""}], "tableView": true, "confirm_label": "Last partner exposure:", "customConditional": "show = data.show_all && !_.isEmpty(data.window_keys)", "optionsLabelPosition": "right"}, {"key": "last_sex_range", "type": "textfield", "input": true, "label": "Exposure range:", "hidden": true, "disabled": true, "multiple": true, "redrawOn": "last_sex", "tableView": false, "clearOnHide": false, "calculateValue": "value = !data.last_sex ? null : {'0-7_days':[0,7],'8-14_days':[7,14],'15-28_days':[14,29],'29-90_days':[29,90],'90+_days':[90,10000]}[data.last_sex];"}, {"key": "exposure_date", "type": "datetime", "input": true, "label": "Please input the approximate date of your last partner exposure (i.e. protected or unprotected sex):", "errors": {"custom": "inconsistent data."}, "format": "yyyy-MM-dd", "hidden": false, "widget": {"mode": "single", "type": "calendar", "format": "yyyy-MM-dd", "locale": "en", "maxDate": "moment()", "minDate": "moment().subtract(1, 'years')", "time_24hr": false, "allowInput": true, "enableTime": false, "noCalendar": false, "hourIncrement": 1, "disableWeekdays": false, "disableWeekends": false, "minuteIncrement": 1, "displayInTimezone": "viewer", "useLocaleSettings": false}, "validate": {"custom": "valid = data.exposure_date_valid;", "required": true}, "tableView": true, "datePicker": {"maxDate": "moment()", "minDate": "moment().subtract(1, 'years')", "disableWeekdays": false, "disableWeekends": false}, "enableTime": false, "confirm_label": "Exposure date (YYYY-MM-DD):", "calculateValue": "value = moment().startOf('day').subtract((data.last_sex_range[0]),'days').format('YYYY-MM-DD');", "customConditional": "show = data.show_all && !_.isEmpty(data.window_keys)", "enableMaxDateInput": true, "enableMinDateInput": true, "allowCalculateOverride": true}, {"key": "exposure_date_valid", "html": "<div class='text-danger'>Exposure date <strong>{{data.exposure_date ? data.exposure_date.slice(0,10) : ''}}</strong> is {{data.exposure_date ? moment().diff(moment(data.exposure_date),'days') : ''}} days ago & outside the approximate exposure range <strong>{{data.last_sex ? data.last_sex.replace('_',' ') : ''}}</strong> you selected above.</div>", "type": "content", "input": true, "tableView": true, "clearOnHide": false, "calculateValue": "var edays = moment().diff(moment(data.exposure_date),'days'); value = data.last_sex_range[0] <= edays && edays <= data.last_sex_range[1];", "refreshOnChange": true, "customConditional": "show = data.show_all && !_.isEmpty(data.window_keys) && !data.exposure_date_valid"}, {"key": "days_since_exposure", "type": "textfield", "input": true, "label": "Days since exposure:", "hidden": true, "disabled": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = moment().diff(moment(data.exposure_date),'days');", "refreshOnChange": true}, {"key": "filtered_window_dates", "type": "textfield", "input": true, "label": "Filtered window periods (days):", "hidden": true, "disabled": true, "multiple": false, "tableView": false, "clearOnHide": false, "calculateValue": "value = !data.exposure_date ? data.windows : _.mapValues(data.window_keys, (v)=>{return _.mapValues(v, (w)=>{return moment(data.exposure_date).add(w,'days')})});"}, {"key": "in_windows", "type": "textfield", "input": true, "label": "Window periods & status:", "hidden": true, "disabled": true, "multiple": false, "tableView": false, "clearOnHide": false, "calculateValue": "let bd = {'CT':{'name':'Chlamydia','test_type':'urine'},'GC':{'name':'Gonorrhea','test_type':'urine'},'TRICH':{'name':'Trichomoniasis','test_type':'urine'},'HIV':{'name':'HIV','test_type':'blood'},'VDRL':{'name':'Syphilis','test_type':'blood'},'HEPB-SAG':{'name':'Hepatitis B','test_type':'blood'},'HCV-AB':{'name':'Hepatitis C','test_type':'blood'},'HSV':{'name':'Herpes Simplex Virus 1 & 2','test_type':'blood'}}; value = _.mapValues(_.mapValues(data.filtered_window_dates, (v)=>{return _.reduce(v, (r,d,w)=>{return _.assign(r, {[w]:d,[`in_${w}`]:d<moment()}); }, {})}), (v,k)=>{return _.assign(v, bd[k]);})"}, {"key": "all_in_early_windows", "html": "<div class='text-success'>All of your lab tests are in the window period. You can get tested ASAP.</div>", "type": "content", "input": true, "tableView": true, "clearOnHide": false, "calculateValue": "value = _.every(data.in_windows, (v,k)=>{return v.in_early});", "customConditional": "show = data.show_all && !_.isEmpty(data.window_keys) && !!data.exposure_date_valid && !!data.all_in_early_windows"}, {"key": "urine_all_in_early_windows", "html": "<div class='text-success'>All of your <strong>urine tests</strong> are in the window period. You can immediately attend the lab to provide a urine sample.</div><div class='small mt-3 mb-n3'>However:</div>", "type": "content", "input": true, "tableView": true, "clearOnHide": false, "calculateValue": "value = _.every(_.pickBy(data.in_windows, (v,k)=>{return v.test_type=='urine';}), (v,k)=>{return v.in_early});", "customConditional": "show = data.show_all && !_.isEmpty(data.window_keys) && !!data.exposure_date_valid && _.some(data.in_windows, {'test_type':'urine'}) && !data.all_in_early_windows && data.urine_all_in_early_windows"}, {"key": "not_in_early_window_period", "type": "textfield", "input": true, "label": "Some of your lab tests are NOT in the window period. We recommend waiting until the following dates for accurate detection:", "disabled": true, "multiple": true, "tableView": true, "defaultValue": [], "confirm_label": "Accurate testing requires waiting until:", "calculateValue": "value = _.map(_.pickBy(data.in_windows, (v,k)=>{return !v.in_early}), (v,k)=>{return `${v.name}: ${v.early&&v.early.calendar(calConf)}`});", "clearOnRefresh": true, "customConditional": "show = data.show_all && !_.isEmpty(data.window_keys) && !!data.exposure_date_valid && !!data.exposure_date && !data.all_in_early_windows"}, {"key": "not_in_late_window_period", "type": "textfield", "input": true, "label": "Test accuracy may be increased if you wait until the following dates:", "disabled": true, "multiple": true, "tableView": true, "defaultValue": [], "confirm_label": "Test accuracy may be increased by waiting until:", "calculateValue": "value = _.map(_.pickBy(data.in_windows, (v,k)=>{return !v.in_late}), (v,k)=>{return `${v.name}: ${v.late&&v.late.calendar(calConf)}`});", "clearOnRefresh": true, "customConditional": "show = data.show_all && !_.isEmpty(data.window_keys) && !!data.exposure_date_valid && !!data.exposure_date && !_.every(data.in_windows, (v,k)=>{return v.in_late})"}, {"key": "window_period_clarification", "html": "<p class='small'>You can do urine testing first & return to the lab for bloodwork. Tell the lab technician you'll return at a later date for the blood testing. The bloodwork part of your requisition is unused until you complete it, so a separate requisition is not required.</p>", "type": "content", "input": true, "tableView": true, "clearOnHide": false, "customConditional": "show = data.show_all && !_.isEmpty(data.window_keys) && !!data.exposure_date_valid && !!data.exposure_date && _.some(data.in_windows, {'test_type':'urine'}) && !_.every(data.in_windows, (v,k)=>{return v.in_late})"}, {"key": "window_periods_info", "html": "<div class='small'>If you <strong>have symptoms we recommend testing immediately</strong>. If you have negative results, we recommend a repeat urine test at 14 days and repeat bloodwork at 12 weeks.</div>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.show_all && !_.isEmpty(data.window_keys)"}, {"key": "window_periods_understood", "type": "radio", "input": true, "label": "Please confirm:", "inline": false, "values": [{"label": "I understand window periods", "value": true, "shortcut": ""}, {"label": "I do not understand window periods and need more counselling", "value": false, "shortcut": ""}], "validate": {"required": true}, "hideLabel": false, "tableView": true, "confirm_label": "Window periods:", "customConditional": "show = data.show_all && !_.isEmpty(data.window_keys)", "optionsLabelPosition": "right"}], "controller": "", "properties": {}, "submissionAccess": []}