{"components": [{"key": "dx_osa", "type": "radio", "input": true, "label": "Have you been diagnosed with obstructive sleep apnea (OSA)?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "I'm not sure", "value": "unknown"}], "validate": {"required": true}, "tableView": true, "confirmLabel": "Diagnosed with OSA:", "optionsLabelPosition": "right"}, {"key": "clinic_requires_rereferral", "type": "radio", "input": true, "label": "Does the sleep clinic (or equipment provider) require a new referral to prescribe or adjust CPAP/equipment?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "I'm not sure", "value": "unknown"}], "validate": {"required": true}, "tableView": true, "confirmLabel": "Clinic requires a new referral:", "customConditional": "show = data.dx_osa === 'yes';", "optionsLabelPosition": "right"}, {"key": "osa_symptoms", "type": "selectboxes", "input": true, "label": "Do you have any of the following?", "values": [{"label": "Loud snoring most nights", "value": "snoring_loud"}, {"label": "Someone has noticed I stop breathing or gasp during sleep", "value": "observed_apnea"}, {"label": "I wake up choking or gasping", "value": "nocturnal_gasp"}, {"label": "I feel very sleepy in the daytime", "value": "daytime_sleepiness"}, {"label": "Morning headaches", "value": "morning_headache"}], "widget": "html5", "inputType": "checkbox", "tableView": true, "confirmLabel": "Symptoms present:", "optionsLabelPosition": "right", "customConditional": "show = (data.dx_osa === 'no') || (data.dx_osa === 'unknown');"}, {"key": "osa_symptoms_none", "type": "checkbox", "input": true, "label": "None of the above", "defaultValue": false, "tableView": true, "customClass": "mt-n3", "errors": {"custom": "Please select at least one symptom or confirm none apply."}, "validate": {"custom": "valid = !!data.osa_symptoms_none || !!_.some(_.values(data.osa_symptoms));"}, "customConditional": "show = (data.dx_osa === 'no') || (data.dx_osa === 'unknown');"}, {"key": "headache_start_timing", "type": "select", "input": true, "label": "When did the morning headaches start?", "widget": "html5", "data": {"values": [{"label": "Within the last 6 months (new)", "value": "lt6mo"}, {"label": "More than 6 months ago", "value": "ge6mo"}, {"label": "I'm not sure", "value": "unknown"}]}, "validate": {"required": true}, "tableView": true, "customConditional": "show = (data.dx_osa === 'no' || data.dx_osa === 'unknown') && _.get(data, 'osa_symptoms.morning_headache') === true;"}, {"key": "headache_nausea", "type": "radio", "input": true, "label": "Do you experience nausea or vomiting in the morning with the headaches?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "confirmLabel": "Morning nausea/vomiting with headaches:", "optionsLabelPosition": "right", "customConditional": "show = (data.dx_osa === 'no' || data.dx_osa === 'unknown') && _.get(data, 'osa_symptoms.morning_headache') === true;"}, {"key": "cardiac_redflags", "type": "selectboxes", "input": true, "label": "Any of these symptoms?", "values": [{"label": "Chest pain or pressure", "value": "chest_pain"}, {"label": "Shortness of breath at rest or with minimal exertion", "value": "sob"}, {"label": "Heart palpitations with lightheadedness", "value": "palpitations"}, {"label": "Fainting or near-fainting", "value": "syncope"}, {"label": "New swelling in legs/ankles/feet", "value": "edema"}], "widget": "html5", "inputType": "checkbox", "tableView": true, "confirmLabel": "Cardiac red flags:", "optionsLabelPosition": "right", "customConditional": "show = (data.dx_osa === 'no' || data.dx_osa === 'unknown') && _.get(data, 'osa_symptoms.nocturnal_gasp') === true;"}, {"key": "cardiac_redflags_none", "type": "checkbox", "input": true, "label": "None of the above", "defaultValue": false, "tableView": true, "customClass": "mt-n3", "errors": {"custom": "Please select at least one symptom or confirm none apply."}, "validate": {"custom": "var anyRF = !!_.some(_.values(data.cardiac_redflags)); valid = !!data.cardiac_redflags_none || anyRF;"}, "customConditional": "show = (data.dx_osa === 'no' || data.dx_osa === 'unknown') && _.get(data, 'osa_symptoms.nocturnal_gasp') === true;"}, {"key": "osa_exclusions", "type": "radio", "input": true, "label": "Do you experience difficulty staying awake while driving or operating heavy machinery?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "confirmLabel": "Safety exclusion present:", "optionsLabelPosition": "right", "customConditional": "var symptomPathReady = (data.dx_osa === 'no' || data.dx_osa === 'unknown') && (!!_.some(_.values(data.osa_symptoms)) && !data.osa_symptoms_none); show = (data.dx_osa === 'yes' && data.clinic_requires_rereferral === 'yes') || symptomPathReady;"}, {"key": "no_indications", "type": "textfield", "input": true, "label": "No Indication:", "hidden": true, "disabled": true, "multiple": false, "tableView": true, "clearOnHide": false, "defaultValue": true, "calculateValue": "var hasDxProceed = (data.dx_osa === 'yes' && data.clinic_requires_rereferral === 'yes'); var hasSymptoms = (!!_.some(_.values(data.osa_symptoms)) && !data.osa_symptoms_none); value = !(hasDxProceed || hasSymptoms);", "refreshOnChange": true}, {"key": "contraindication_keys", "type": "textfield", "input": true, "label": "Contraindication keys:", "hidden": true, "disabled": true, "multiple": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "value = []; if (data.osa_exclusions === 'yes') { value.push('sleepy_while_driving'); } if (_.get(data, 'osa_symptoms.morning_headache') && data.headache_start_timing === 'lt6mo') { value.push('headache_new_under_6mo'); } if (_.get(data, 'osa_symptoms.morning_headache') && data.headache_nausea === 'yes') { value.push('headache_with_nausea'); } var anyRF = !!_.some(_.values(data.cardiac_redflags)); if (_.get(data, 'osa_symptoms.nocturnal_gasp') && anyRF && !data.cardiac_redflags_none) { value.push('cardiac_red_flags'); }", "refreshOnChange": true}, {"key": "approved_html", "type": "content", "input": true, "tableView": false, "clearOnHide": false, "defaultValue": "<div style='border-left:4px solid #198754;background:#E8F5E9;padding:10px;margin:10px 0'><h3 class='text-green' style='margin:0 0 8px 0'>You're eligible for a sleep clinic referral.</h3><p>Based on your answers, a referral for assessment and/or CPAP equipment is appropriate. A clinician will review your request and, if suitable, arrange the referral to an accredited sleep clinic.</p><p><em>Note:</em> If you develop severe symptoms at any time (such as chest pain, severe shortness of breath, fainting, or confusion), seek urgent in-person care.</p></div>", "refreshOnChange": true}, {"key": "rejected_html", "type": "content", "input": true, "tableView": false, "clearOnHide": false, "defaultValue": "<h3 class='text-red'>We cannot arrange an online referral right now.</h3></br><p>Your request was declined for one or more of the following reasons:</p><ul class='text-red'><li>No clear reason for a referral was identified</li><li>Some of your answers suggest issues that should be checked in person first</li></ul><p>Please book an appointment with a local healthcare provider to discuss your situation and next steps.</p><p class='text-red'><strong>Seek urgent care immediately</strong> if you develop severe or worsening symptoms such as chest pain, trouble breathing, fainting, or sudden new headaches.</p>", "refreshOnChange": true}]}