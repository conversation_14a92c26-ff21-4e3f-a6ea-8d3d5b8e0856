{"components": [{"key": "contraindications", "type": "selectboxes", "input": true, "label": "Please select if any of the following apply to you:", "values": [{"label": "Experiencing the 1st outbreak in your lifetime", "value": "diagnosis_new_hsv", "shortcut": ""}, {"label": "Concern you may be experiencing a shingles outbreak", "value": "diagnosis_shingles", "shortcut": ""}, {"label": "Concern you may be experiencing a herpes outbreak in your eye", "value": "ocular_herpes", "shortcut": ""}, {"label": "A new rash or other symptoms that you're not sure is related to herpes", "value": "diagnosis_uncertain_hsv", "shortcut": ""}], "inputType": "checkbox", "tableView": true, "optionsLabelPosition": "right"}, {"key": "no_contraindications", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = _.some(_.values(data.contraindications)) || data.no_contraindications;"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "hsv_test_note_swab", "type": "htmlelement", "input": false, "label": "HTML", "content": "<h3 class='text-red'>Please seek immediate attention at an urgent care clinic!</h3><p class='text-red'>Diagnosis of a herpes outbreak requires a time-sensitive PCR swab.</p>", "tableView": false, "customConditional": "show = _.some(['diagnosis_new_hsv', 'diagnosis_uncertain_hsv'], (k)=>{return _.includes(_.keys(_.pickBy(data.contraindications)), k)});"}, {"key": "hsv_test_note_treatment", "type": "htmlelement", "input": false, "label": "HTML", "content": "<h3 class='text-red'>Please seek immediate attention at an urgent care clinic!</h3><p class='text-red'>Treatment of a herpes or shingles outbreak requires immediate assessment</p>", "tableView": false, "customConditional": "show = _.some(['diagnosis_shingles', 'ocular_herpes'], (k)=>{return _.includes(_.keys(_.pickBy(data.contraindications)), k)});"}, {"key": "indications", "type": "selectboxes", "input": true, "label": "Do any of the following apply to you:", "values": [{"label": "I have recurrent cold sores but have never had a swab to confirm the serotype (HSV 1 or HSV2)", "value": "cold_sores", "shortcut": ""}, {"label": "I have a recurrent blistering genital rash but have never had a swab to confirm the serotype (HSV 1 or HSV2)", "value": "genital_blistering_rash", "shortcut": ""}, {"label": "I have a partner with a physician-confirmed case of genital herpes. I would like to know if I have the same strain because my partner is considering going on a daily herpes pill.", "value": "partner_confirmed_genital_herpes_serotype_matching", "shortcut": ""}, {"label": "I have a partner with a physician-confirmed case of cold sores. I would like to know if I have the same strain because my partner is considering going on a daily herpes pill.", "value": "partner_cold_sores_serotype_matching", "shortcut": ""}, {"label": "I had a partner with cold sores, never had symptoms, and would like to know if I have the virus.", "value": "partner_cold_sores_asymptomatic", "shortcut": ""}, {"label": "I had a partner with genital herpes, never had symptoms, and would like to know if I have the virus.", "value": "partner_genital_herpes_asymptomatic", "shortcut": ""}], "inputType": "checkbox", "tableView": true, "customConditional": "show = !_.some(_.values(data.contraindications)) && data.no_contraindications && data.sku == 'std_pn_hsv_test';", "optionsLabelPosition": "right"}, {"key": "no_indications", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = _.some(_.values(data.indications)) || data.no_indications;"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = !_.some(_.values(data.contraindications)) && data.no_contraindications && data.sku == 'std_pn_hsv_test';"}, {"key": "contraindication_keys", "type": "textfield", "input": true, "label": "Contraindication keys:", "hidden": true, "disabled": true, "multiple": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "value = _.concat(_.keys(_.pickBy(data.contraindications)), _.keys(_.pickBy(data.rx_hsv_contraindications)));", "refreshOnChange": true}, {"key": "approved_html", "type": "content", "input": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.sku != 'std_pn_hsv_test' ? null : `You're eligible to discuss herpes simplex blood test. Please read our <a href=\"https://docs.teletest.ca/about-the-herpes-virus\" target='_blank'>FAQ Guide</a> about commonly asked questions related to herpes testing. HSV testing is not right for everyone.<li>In Ontario, over 70% of adults test positive for HSV by age 40.</li><li>Testing and getting a positive result can lead to unnecessary anxiety and make conversations with sexual partners difficult.</li>`;"}]}