{"components": [{"key": "heading_gout_medication", "html": "<h1><center><strong>Gout Medication</strong></center></h1><p>To help us provide the best care, please answer the following questions about your health and medical history related to gout and its treatment.</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "gout_history", "html": "<h3>Your Gout History</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "prior_diagnosis", "type": "radio", "input": true, "label": "Have you been previously diagnosed with gout?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "tableView": true, "validate": {"required": true}}, {"key": "age_at_diagnosis", "type": "number", "input": true, "label": "At what age were you diagnosed with gout?", "tableView": true, "validate": {"required": true, "customMessage": "Please enter your age at diagnosis."}, "conditional": {"show": true, "when": "prior_diagnosis", "eq": "yes"}}, {"key": "gout_attacks_frequency", "type": "radio", "input": true, "label": "How often do you feel you experience gout attacks?", "values": [{"label": "Less than once a year", "value": "less_than_once_a_year"}, {"label": "1-2 times a year", "value": "1_2_times_a_year"}, {"label": "Every few months", "value": "every_few_months"}, {"label": "Monthly", "value": "monthly"}, {"label": "Weekly", "value": "weekly"}], "tableView": true, "validate": {"required": true}}, {"key": "kidney_stones_history", "type": "radio", "input": true, "label": "Have you ever had or been suspected of having kidney stones?", "values": [{"label": "Yes, I have had kidney stones", "value": "yes_had"}, {"label": "I suspect I might have had kidney stones", "value": "yes_suspected"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "not_sure"}], "tableView": true, "validate": {"required": true}}, {"key": "prior_lab_testing", "type": "radio", "input": true, "label": "Have you had any laboratory tests for gout?", "values": [{"label": "Yes", "value": true}, {"label": "No", "value": false}], "tableView": true, "validate": {"required": true}}, {"key": "last_uric_acid_test", "type": "select", "input": true, "label": "When was your last <strong> Uric Acid Level</strong>?", "data": {"values": [{"label": "<1 week ago", "value": "less_1_week"}, {"label": "2-4 weeks ago", "value": "2-4_weeks_ago"}, {"label": "4 weeks - 6 weeks ago", "value": "4-6_weeks"}, {"label": "6 weeks - 3 months ago", "value": "6_weeks-3_months"}, {"label": "3-6 months", "value": "3-6_months"}, {"label": "6-12 months", "value": "6-12_months"}, {"label": "12-24 months", "value": "12-24_months"}, {"label": "24+ months", "value": "24+_months"}, {"label": "Never had one", "value": "never_had"}]}, "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.prior_lab_testing == true;", "optionsLabelPosition": "right"}, {"key": "uric_acid_level_result", "type": "radio", "input": true, "label": "What was the result of your Uric Acid Level test?", "values": [{"label": "Normal", "value": "normal"}, {"label": "High", "value": "high"}, {"label": "Low", "value": "low"}, {"label": "Don't know", "value": "dont_know"}], "tableView": true, "validate": {"required": true}, "conditional": {"show": true, "when": "prior_lab_testing", "eq": "yes"}}, {"key": "ethnicity_consideration_for_allopurinol", "type": "radio", "input": true, "label": "Are you of Korean, Han Chinese, or Thai descent?", "tooltip": "In some individuals who might be candidates for allopurinol to prevent gout flares, genetic screening is required prior to starting therapy.", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "not_sure"}], "tableView": true, "validate": {"required": true}}, {"key": "allopurinol_contraindication_ckd", "type": "radio", "input": true, "label": "Have you been diagnosed with chronic kidney disease (CKD)?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "not_sure"}], "tableView": true, "validate": {"required": true}}, {"key": "current_flare_heading", "html": "<h3>Current Gout Flare</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "currently_having_flare", "type": "radio", "input": true, "label": "Are you currently experiencing a gout flare?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "not_sure"}], "tableView": true, "validate": {"required": true}}, {"key": "current_flare_start", "type": "select", "input": true, "label": "When did your current gout flare start?", "data": {"values": [{"label": "<24 hours ago", "value": "less_24_hours"}, {"label": "1-2 days ago", "value": "1-2_days"}, {"label": "3-4 days ago", "value": "3-4_days"}, {"label": "5-7 days ago", "value": "5-7_days"}, {"label": "More than a week ago", "value": "more_than_week"}]}, "widget": "html5", "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = data.currently_having_flare === 'yes';"}, {"key": "affected_joints", "type": "selectboxes", "input": true, "label": "Which joint(s) are affected by your current flare?", "values": [{"label": "Big toe", "value": "big_toe"}, {"label": "<PERSON><PERSON>", "value": "ankle"}, {"label": "Knee", "value": "knee"}, {"label": "Fingers", "value": "fingers"}, {"label": "<PERSON>rist", "value": "wrist"}, {"label": "Elbows", "value": "elbows"}, {"label": "Other", "value": "other"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.currently_having_flare === 'yes';", "inputType": "checkbox"}, {"key": "current_flare_intensity", "type": "radio", "input": true, "label": "How would you rate the intensity of your current gout flare?", "values": [{"label": "Mild", "value": "mild"}, {"label": "Moderate", "value": "moderate"}, {"label": "Severe", "value": "severe"}, {"label": "Very Severe", "value": "very_severe"}], "tableView": true, "validate": {"required": true}, "customConditional": "show = data.currently_having_flare === 'yes';"}, {"key": "current_flare_treatment", "type": "selectboxes", "input": true, "label": "Which of the following treatments have you taken for your current gout flare? Select all that apply.", "values": [{"label": "Advil (ibuprofen)", "value": "ibuprofen"}, {"label": "Aleve (naproxen)", "value": "naproxen"}, {"label": "Indomethacin", "value": "indomethacin"}, {"label": "<PERSON><PERSON><PERSON>", "value": "aspirin"}, {"label": "Tylenol", "value": "tylenol"}, {"label": "Colchicine", "value": "colchicine"}, {"label": "Prednisone", "value": "prednisone"}, {"label": "None", "value": "none"}, {"label": "Other", "value": "other"}], "validate": {"required": true}, "tableView": true, "inputType": "checkbox", "customConditional": "show = data.currently_having_flare === 'yes';"}, {"key": "current_flare_effectiveness", "type": "radio", "input": true, "label": "How effective has your current treatment been in managing your gout flare?", "values": [{"label": "Very effective", "value": "very_effective"}, {"label": "Somewhat effective", "value": "somewhat_effective"}, {"label": "Not effective", "value": "not_effective"}, {"label": "Made symptoms worse", "value": "worse"}, {"label": "Not sure", "value": "not_sure"}], "tableView": true, "validate": {"required": true}, "customConditional": "show = data.currently_having_flare === 'yes';"}, {"key": "acute_gout_medication_heading", "html": "<h3>Medications</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "acute_gout_medication", "type": "selectboxes", "input": true, "label": "Please select if you have taken any of the following medications in the past:", "values": [{"label": "Colchicine", "value": "colchicine"}, {"label": "Ibuprofen <strong>(Brand: <PERSON><PERSON>/<PERSON><PERSON><PERSON>)</strong>", "value": "ibuprofen"}, {"label": "Naproxen <strong>(Brand: <PERSON><PERSON>)</strong>", "value": "naproxen"}, {"label": "Prednisone", "value": "prednisone"}, {"label": "None of the above", "value": "none"}], "tableView": true, "validate": {"required": true}}, {"key": "heading_colchicine", "html": "<h3><strong>Colchicine</strong></h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.acute_gout_medication.colchicine;"}, {"key": "previous_colchicine_use", "type": "selectboxes", "input": true, "label": "Please select if you have taken <PERSON><PERSON><PERSON>:", "inline": false, "values": [{"label": "During an attack", "value": "acute_attack"}, {"label": "On a daily basis for prevention", "value": "daily_basis_prevention"}, {"label": "None of the above", "value": "none_of_the_above"}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "customConditional": "show = data.acute_gout_medication.colchicine;", "optionsLabelPosition": "right"}, {"key": "colchicine_effectiveness", "type": "radio", "input": true, "label": "Did you find Colchicine effective in managing your gout symptoms?", "values": [{"label": "Very effective", "value": "very_effective"}, {"label": "Somewhat effective", "value": "somewhat_effective"}, {"label": "Not effective", "value": "not_effective"}, {"label": "Made symptoms worse", "value": "worse"}, {"label": "Not sure", "value": "not_sure"}], "customConditional": "show = data.acute_gout_medication.colchicine;", "validate": {"required": true}, "tableView": true}, {"key": "colchicine_side_effects", "type": "selectboxes", "input": true, "label": "Have you experienced any of the following side effects from taking Colchicine? Select all that apply.", "values": [{"label": "Diarrhea", "value": "diarrhea"}, {"label": "<PERSON><PERSON><PERSON>", "value": "nausea"}, {"label": "Vomiting", "value": "vomiting"}, {"label": "Abdominal pain", "value": "abdominal_pain"}, {"label": "Muscle weakness", "value": "muscle_weakness"}, {"label": "None", "value": "none"}, {"label": "Other (please specify in comments)", "value": "other"}], "customConditional": "show = data.acute_gout_medication.colchicine;", "validate": {"required": true}, "tableView": true, "inputType": "checkbox"}, {"key": "colchicine_treatment_duration", "type": "select", "input": true, "label": "For how many days do you typically take Colchicine during a gout flare?", "data": {"values": [{"label": "1 day", "value": "1_day"}, {"label": "2 days", "value": "2_days"}, {"label": "3 days", "value": "3_days"}, {"label": "4 days", "value": "4_days"}, {"label": "5 days", "value": "5_days"}, {"label": "6 to 7 days", "value": "6-7_days"}, {"label": "More than 7 days", "value": "more_7_days"}, {"label": "Varies", "value": "varies"}, {"label": "Not applicable (I do not take Colchicine for flares)", "value": "not_applicable"}]}, "widget": "html5", "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = data.acute_gout_medication.colchicine;"}, {"key": "heading_ibuprofen", "html": "<h3><strong>Ibuprofen (Brand: Advil/Motrin)</strong></h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.acute_gout_medication.ibuprofen;"}, {"key": "previous_ibuprofen_use", "type": "selectboxes", "input": true, "label": "Please select if you have taken Ibuprofen:", "values": [{"label": "During an attack", "value": "acute_attack"}, {"label": "On a daily basis for prevention", "value": "daily_basis_prevention"}, {"label": "None of the above", "value": "none_of_the_above"}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "customConditional": "show = data.acute_gout_medication.ibuprofen;", "optionsLabelPosition": "right"}, {"key": "ibuprofen_effectiveness", "type": "radio", "input": true, "label": "Did you find Ibuprofen effective in managing your gout symptoms?", "values": [{"label": "Very effective", "value": "very_effective"}, {"label": "Somewhat effective", "value": "somewhat_effective"}, {"label": "Not effective", "value": "not_effective"}, {"label": "Made symptoms worse", "value": "worse"}, {"label": "Not sure", "value": "not_sure"}], "customConditional": "show = data.acute_gout_medication.ibuprofen;", "validate": {"required": true}, "tableView": true}, {"key": "ibuprofen_side_effects", "type": "selectboxes", "input": true, "label": "Have you experienced any of the following side effects from taking Ibuprofen? Select all that apply.", "values": [{"label": "Gastrointestinal issues", "value": "gi_issues"}, {"label": "Dizziness", "value": "dizziness"}, {"label": "Headache", "value": "headache"}, {"label": "None", "value": "none"}], "customConditional": "show = data.acute_gout_medication.ibuprofen;", "validate": {"required": true}, "tableView": true, "inputType": "checkbox"}, {"key": "ibuprofen_dosing", "type": "selectboxes", "input": true, "label": "Please select your typical dosing of Ibuprofen for a gout attack:", "values": [{"label": "200 mg every 4-6 hours", "value": "200mg_4-6h"}, {"label": "400 mg every 4-6 hours", "value": "400mg_4-6h"}, {"label": "600 mg every 6-8 hours", "value": "600mg_6-8h"}, {"label": "800 mg every 6-8 hours", "value": "800mg_6-8h"}, {"label": "200 mg every 8 hours", "value": "200mg_8h_low"}, {"label": "400 mg every 8 hours", "value": "400mg_8h_standard"}, {"label": "800 mg three times a day", "value": "800mg_3d_high"}, {"label": "Not applicable (I do not use Ibuprofen for flares)", "value": "not_applicable"}], "validate": {"required": true}, "tableView": true, "inputType": "checkbox", "customConditional": "show = data.acute_gout_medication.ibuprofen;"}, {"key": "ibuprofen_treatment_duration", "type": "select", "input": true, "label": "For how many days do you typically take Ibuprofen (<PERSON><PERSON>, Mo<PERSON>n) during a gout flare?", "data": {"values": [{"label": "1 day", "value": "1_day"}, {"label": "2 days", "value": "2_days"}, {"label": "3 days", "value": "3_days"}, {"label": "4 days", "value": "4_days"}, {"label": "5 days", "value": "5_days"}, {"label": "6 to 7 days", "value": "6-7_days"}, {"label": "More than 7 days", "value": "more_7_days"}, {"label": "Varies", "value": "varies"}, {"label": "Not applicable (I do not take Ibuprofen for flares)", "value": "not_applicable"}]}, "widget": "html5", "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = data.acute_gout_medication.ibuprofen;"}, {"key": "heading_indomethacin", "html": "<h3><strong>Indo<PERSON><PERSON><PERSON> (Brand: Indo<PERSON>)</strong></h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.acute_gout_medication.indomethacin;"}, {"key": "previous_indomethacin_use", "type": "selectboxes", "input": true, "label": "Please select if you have taken Indomethacin:", "values": [{"label": "During an attack", "value": "acute_attack"}, {"label": "On a daily basis for prevention", "value": "daily_basis_prevention"}, {"label": "None of the above", "value": "none_of_the_above"}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "customConditional": "show = data.acute_gout_medication.indomethacin;", "optionsLabelPosition": "right"}, {"key": "indomethacin_effectiveness", "type": "radio", "input": true, "label": "Did you find Indomethacin effective in managing your gout symptoms?", "values": [{"label": "Very effective", "value": "very_effective"}, {"label": "Somewhat effective", "value": "somewhat_effective"}, {"label": "Not effective", "value": "not_effective"}, {"label": "Made symptoms worse", "value": "worse"}, {"label": "Not sure", "value": "not_sure"}], "customConditional": "show = data.acute_gout_medication.indomethacin;", "validate": {"required": true}, "tableView": true}, {"key": "indomethacin_side_effects", "type": "selectboxes", "input": true, "label": "Have you experienced any of the following side effects from taking Indomethacin? Select all that apply.", "values": [{"label": "Gastrointestinal issues", "value": "gi_issues"}, {"label": "Dizziness", "value": "dizziness"}, {"label": "Headache", "value": "headache"}, {"label": "<PERSON><PERSON>", "value": "rash"}, {"label": "None", "value": "none"}], "customConditional": "show = data.acute_gout_medication.indomethacin;", "validate": {"required": true}, "tableView": true, "inputType": "checkbox"}, {"key": "indomethacin_dosing", "type": "selectboxes", "input": true, "label": "Please select your typical dosing of Indomethacin for a gout attack:", "values": [{"label": "25 mg two to three times a day", "value": "25mg_2-3day"}, {"label": "50 mg two to three times a day", "value": "50mg_2-3day"}, {"label": "75 mg once a day", "value": "75mg_1day_er"}, {"label": "50 mg three times a day", "value": "50mg_3day"}, {"label": "25 mg three times a day", "value": "25mg_3day"}, {"label": "100 mg once a day", "value": "100mg_1day_er_severe"}, {"label": "Not applicable (I do not use Indomethacin for flares)", "value": "not_applicable"}, {"label": "Other", "value": "other"}], "validate": {"required": true}, "tableView": true, "inputType": "checkbox", "customConditional": "show = data.acute_gout_medication.indomethacin;"}, {"key": "indomethacin_treatment_duration", "type": "select", "input": true, "label": "For how many days do you typically take Indomethacin during a gout flare?", "data": {"values": [{"label": "1 day", "value": "1_day"}, {"label": "2 days", "value": "2_days"}, {"label": "3 days", "value": "3_days"}, {"label": "4 days", "value": "4_days"}, {"label": "5 days", "value": "5_days"}, {"label": "6 to 7 days", "value": "6-7_days"}, {"label": "More than 7 days", "value": "more_7_days"}, {"label": "Varies", "value": "varies"}, {"label": "Not applicable (I do not take Indomethacin for flares)", "value": "not_applicable"}]}, "widget": "html5", "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = data.acute_gout_medication.indomethacin;"}, {"key": "heading_prednisone", "html": "<h3><strong>Prednisone</strong></h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false, "customConditional": "show = data.acute_gout_medication.prednisone;"}, {"key": "prednisone_effectiveness", "type": "radio", "input": true, "label": "Did you find Prednisone effective in managing your gout symptoms?", "values": [{"label": "Very effective", "value": "very_effective"}, {"label": "Somewhat effective", "value": "somewhat_effective"}, {"label": "Not effective", "value": "not_effective"}, {"label": "Made symptoms worse", "value": "worse"}, {"label": "Not sure", "value": "not_sure"}], "customConditional": "show = data.acute_gout_medication.prednisone;", "validate": {"required": true}, "tableView": true}, {"key": "prednisone_side_effects", "type": "selectboxes", "input": true, "label": "Have you experienced any of the following side effects from taking Prednisone? Select all that apply.", "values": [{"label": "Increased appetite", "value": "increased_appetite"}, {"label": "Weight gain", "value": "weight_gain"}, {"label": "Mood changes", "value": "mood_changes"}, {"label": "Insomnia", "value": "insomnia"}, {"label": "High blood pressure", "value": "high_blood_pressure"}, {"label": "None", "value": "none"}], "customConditional": "show = data.acute_gout_medication.prednisone;", "validate": {"required": true}, "tableView": true, "inputType": "checkbox"}, {"key": "prednisone_dosing", "type": "selectboxes", "input": true, "label": "Please select your typical dosing of Prednisone for a gout attack:", "values": [{"label": "5 mg per day", "value": "5mg_day"}, {"label": "10 mg per day", "value": "10mg_day"}, {"label": "20 mg per day", "value": "20mg_day"}, {"label": "40 mg per day", "value": "40mg_day"}, {"label": "Other", "value": "tapering_high_dose"}, {"label": "Not applicable (I do not use Prednisone for flares)", "value": "not_applicable"}], "validate": {"required": true}, "tableView": true, "inputType": "checkbox", "customConditional": "show = data.acute_gout_medication.prednisone;"}, {"key": "prednisone_treatment_duration", "type": "select", "input": true, "label": "For how many days do you typically take Prednisone during a gout flare?", "data": {"values": [{"label": "1 day", "value": "1_day"}, {"label": "2 days", "value": "2_days"}, {"label": "3 days", "value": "3_days"}, {"label": "4 days", "value": "4_days"}, {"label": "5 days", "value": "5_days"}, {"label": "6 to 7 days", "value": "6-7_days"}, {"label": "More than 7 days", "value": "more_7_days"}, {"label": "Varies", "value": "varies"}, {"label": "Not applicable (I do not take Prednisone for flares)", "value": "not_applicable"}]}, "widget": "html5", "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = data.acute_gout_medication.prednisone;"}, {"key": "preventative_gout_medication", "type": "selectboxes", "input": true, "label": "Please select if you take any of the following medications on a daily basis:", "values": [{"label": "Allopurinol", "value": "allopurinol"}, {"label": "Febuxostat", "value": "febuxostat"}, {"label": "None of the above", "value": "no_allopurinol_febuxostat_colchicine"}], "tableView": true, "validate": {"required": true}}, {"key": "allopurinol_status_query", "html": "<h3><strong>Allopurinol</strong></h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.preventative_gout_medication.allopurinol;", "refreshOnChange": false}, {"key": "allopurinol_status", "type": "radio", "input": true, "customConditional": "show = data.preventative_gout_medication.allopurinol;", "label": "Have you been on Allopurinol in the past or are you currently taking it?", "values": [{"label": "Currently on Allopurinol", "value": "current"}, {"label": "Taken Allopurinol in the past", "value": "past"}, {"label": "Never taken Allopurinol", "value": "never"}], "tableView": true}, {"key": "current_allopurinol_dose", "type": "select", "input": true, "widget": "html5", "label": "What is your current dose of Allopurinol?", "conditional": {"show": true, "when": "allopurinol_status", "eq": "current"}, "data": {"values": [{"label": "100 mg/day", "value": "100mg"}, {"label": "200 mg/day", "value": "200mg"}, {"label": "300 mg/day", "value": "300mg"}, {"label": "400 mg/day", "value": "400mg"}, {"label": "Other", "value": "other"}]}, "tableView": true}, {"key": "duration_current_allopurinol", "type": "select", "input": true, "widget": "html5", "label": "How long have you been taking Allopurinol?", "conditional": {"show": true, "when": "allopurinol_status", "eq": "current"}, "data": {"values": [{"label": "Less than 6 months", "value": "<6_months"}, {"label": "6 months to 1 year", "value": "6_months_to_1_year"}, {"label": "1-2 years", "value": "1-2_years"}, {"label": "More than 2 years", "value": ">2_years"}]}, "tableView": true}, {"key": "maximum_past_allopurinol_dose", "type": "select", "input": true, "widget": "html5", "label": "What was your maximum dose of Allopurinol in the past?", "conditional": {"show": true, "when": "allopurinol_status", "eq": "past"}, "data": {"values": [{"label": "100 mg/day", "value": "100mg_past"}, {"label": "200 mg/day", "value": "200mg_past"}, {"label": "300 mg/day", "value": "300mg_past"}, {"label": "400 mg/day", "value": "400mg_past"}, {"label": "Other", "value": "other_past"}]}, "tableView": true}, {"key": "duration_past_allopurinol", "type": "select", "input": true, "widget": "html5", "label": "How long were you on Allopurinol in the past?", "conditional": {"show": true, "when": "allopurinol_status", "eq": "past"}, "data": {"values": [{"label": "Less than 6 months", "value": "<6_months_past"}, {"label": "6 months to 1 year", "value": "6_months_to_1_year_past"}, {"label": "1-2 years", "value": "1-2_years_past"}, {"label": "More than 2 years", "value": ">2_years_past"}]}, "tableView": true}, {"key": "reason_for_stopping_allopurinol", "type": "select", "input": true, "widget": "html5", "label": "Why did you stop taking Allopurinol?", "conditional": {"show": true, "when": "allopurinol_status", "eq": "past"}, "data": {"values": [{"label": "Side effects", "value": "side_effects"}, {"label": "Not effective", "value": "not_effective"}, {"label": "Managed with lifestyle/diet", "value": "lifestyle_diet"}, {"label": "Other", "value": "other_reason"}]}, "tableView": true}, {"key": "allopurinol_side_effects", "type": "selectboxes", "input": true, "label": "Did you experience any side effects from Allopurinol?", "customConditional": "show = data.preventative_gout_medication.allopurinol && (data.allopurinol_status.current || data.allopurinol_status.past);", "values": [{"label": "Skin rash or hypersensitivity", "value": "skin_rash"}, {"label": "Nausea or vomiting", "value": "nausea_vomiting"}, {"label": "Elevated liver enzymes", "value": "elevated_liver_enzymes"}, {"label": "Kidney impairment", "value": "kidney_impairment"}, {"label": "None", "value": "none"}], "tableView": true}, {"key": "allopurinol_effectiveness", "type": "radio", "input": true, "customConditional": "show = data.preventative_gout_medication.allopurinol && (data.allopurinol_status.current || data.allopurinol_status.past);", "label": "Did you find the medication effective in reducing the frequency of gout attacks?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "not_sure"}], "tableView": true}, {"key": "gout_attacks_on_allopurinol", "type": "select", "widget": "html5", "input": true, "label": "How many gout attacks did you have while on your highest dose of Allopurinol?", "conditional": {"show": true, "when": "allopurinol_status", "eq": "current"}, "data": {"values": [{"label": "None", "value": "none"}, {"label": "1-2 attacks", "value": "1-2"}, {"label": "3-5 attacks", "value": "3-5"}, {"label": "6-10 attacks", "value": "6-10"}, {"label": "More than 10 attacks", "value": "more_than_10"}]}, "tableView": true}, {"key": "febuxostat_status_query", "html": "<h3><strong>Febuxostat</strong></h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.preventative_gout_medication.febuxostat;", "refreshOnChange": false}, {"key": "febuxostat_status", "type": "radio", "input": true, "customConditional": "show = data.preventative_gout_medication.febuxostat;", "label": "Have you been on Febuxostat in the past or are you currently taking it?", "values": [{"label": "Currently on Febuxostat", "value": "current"}, {"label": "Taken Febuxostat in the past", "value": "past"}, {"label": "Never taken Febuxostat", "value": "never"}], "tableView": true}, {"key": "current_febuxostat_dose", "type": "select", "input": true, "widget": "html5", "label": "What is your current dose of Febuxostat?", "conditional": {"show": true, "when": "febuxostat_status", "eq": "current"}, "data": {"values": [{"label": "40 mg/day", "value": "40mg"}, {"label": "80 mg/day", "value": "80mg"}, {"label": "Other", "value": "other"}]}, "tableView": true}, {"key": "duration_current_febuxostat", "type": "select", "input": true, "widget": "html5", "label": "How long have you been taking Febuxostat?", "conditional": {"show": true, "when": "febuxostat_status", "eq": "current"}, "data": {"values": [{"label": "Less than 6 months", "value": "<6_months"}, {"label": "6 months to 1 year", "value": "6_months_to_1_year"}, {"label": "1-2 years", "value": "1-2_years"}, {"label": "More than 2 years", "value": ">2_years"}]}, "tableView": true}, {"key": "maximum_past_febuxostat_dose", "type": "select", "input": true, "widget": "html5", "label": "What was your maximum dose of Febuxostat in the past?", "conditional": {"show": true, "when": "febuxostat_status", "eq": "past"}, "data": {"values": [{"label": "40 mg/day", "value": "40mg_past"}, {"label": "80 mg/day", "value": "80mg_past"}, {"label": "Other", "value": "other_past"}]}, "tableView": true}, {"key": "duration_past_febuxostat", "type": "select", "input": true, "widget": "html5", "label": "How long were you on Febuxostat in the past?", "conditional": {"show": true, "when": "febuxostat_status", "eq": "past"}, "data": {"values": [{"label": "Less than 6 months", "value": "<6_months_past"}, {"label": "6 months to 1 year", "value": "6_months_to_1_year_past"}, {"label": "1-2 years", "value": "1-2_years_past"}, {"label": "More than 2 years", "value": ">2_years_past"}]}, "tableView": true}, {"key": "reason_for_stopping_febuxostat", "type": "select", "input": true, "widget": "html5", "label": "Why did you stop taking Febuxostat?", "conditional": {"show": true, "when": "febuxostat_status", "eq": "past"}, "data": {"values": [{"label": "Side effects", "value": "side_effects"}, {"label": "Not effective", "value": "not_effective"}, {"label": "Managed with lifestyle/diet", "value": "lifestyle_diet"}, {"label": "Other", "value": "other_reason"}]}, "tableView": true}, {"key": "febuxostat_side_effects", "type": "selectboxes", "input": true, "label": "Did you experience any side effects from Febuxostat?", "customConditional": "show = data.preventative_gout_medication.febuxostat && (data.febuxostat_status.current || data.febuxostat_status.past);", "values": [{"label": "Liver function abnormalities", "value": "liver_function_abnormalities"}, {"label": "<PERSON><PERSON><PERSON>", "value": "nausea"}, {"label": "<PERSON><PERSON>", "value": "rash"}, {"label": "Increased gout flares", "value": "increased_gout_flares"}, {"label": "None", "value": "none"}], "tableView": true}, {"key": "febuxostat_effectiveness", "type": "radio", "input": true, "customConditional": "show = data.preventative_gout_medication.febuxostat && (data.febuxostat_status.current || data.febuxostat_status.past);", "label": "Did you find the medication effective in reducing the frequency of gout attacks?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "not_sure"}], "tableView": true}, {"key": "gout_attacks_on_febuxostat", "type": "select", "widget": "html5", "input": true, "label": "How many gout attacks did you have while on your highest dose of Febuxostat?", "conditional": {"show": true, "when": "febuxostat_status", "eq": "current"}, "data": {"values": [{"label": "None", "value": "none"}, {"label": "1-2 attacks", "value": "1-2"}, {"label": "3-5 attacks", "value": "3-5"}, {"label": "6-10 attacks", "value": "6-10"}, {"label": "More than 10 attacks", "value": "more_than_10"}]}, "tableView": true}, {"key": "header_health_conditions", "html": "<h2>Other Health Conditions</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "kidney_function", "type": "radio", "input": true, "label": "Has a healthcare provider ever told you that you have reduced kidney function?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "tableView": true, "validate": {"required": true}}, {"key": "liver_function", "type": "radio", "input": true, "label": "Has a healthcare provider ever told you that you have liver problems?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "tableView": true, "validate": {"required": true}}, {"key": "header_lifestyle_factors", "html": "<h2>Lifestyle Factors</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "drink_alcohol", "type": "radio", "input": true, "label": "Do you drink alcohol?", "values": [{"label": "Yes", "value": "yes", "shortcut": ""}, {"label": "No", "value": "no", "shortcut": ""}], "validate": {"required": true}, "tableView": true}, {"key": "type_of_alcohol", "type": "selectboxes", "validate": {"required": true}, "input": true, "label": "What type of alcohol do you usually drink? (Select all that apply)", "values": [{"label": "Beer", "value": "beer"}, {"label": "Wine", "value": "wine"}, {"label": "Spirits", "value": "spirits"}, {"label": "Other", "value": "other"}], "tableView": true, "customConditional": "show = data.drink_alcohol === 'yes';", "optionsLabelPosition": "right"}, {"key": "header_beer_details", "html": "<h4>Beer Details</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.drink_alcohol === 'yes' && data.type_of_alcohol.beer;", "refreshOnChange": false}, {"key": "beer_format", "type": "radio", "input": true, "validate": {"required": true}, "label": "What is the typical size of the bottle/can you drink?", "values": [{"label": "473ml", "value": "473ml"}, {"label": "355ml", "value": "355ml"}, {"label": "500ml", "value": "500ml"}, {"label": "650ml", "value": "650ml"}], "tableView": true, "customConditional": "show = data.drink_alcohol === 'yes' && data.type_of_alcohol.beer;"}, {"key": "beer_abv_content", "type": "radio", "input": true, "validate": {"required": true}, "label": "What is the typical Alcohol By Volume (ABV) content of the beer you drink?", "values": [{"label": "Non-alcoholic (<0.5%)", "value": "non_alcoholic"}, {"label": "Low (0.5% - 3.5%)", "value": "low"}, {"label": "Standard (3.5% - 5.5%)", "value": "standard"}, {"label": "High (>5.5%)", "value": "high"}], "tableView": true, "customConditional": "show = data.drink_alcohol === 'yes' && data.type_of_alcohol.beer;"}, {"key": "number_of_beers_per_day", "type": "radio", "input": true, "validate": {"required": true}, "label": "How many beers do you typically drink in a day?", "tooltip": "Please calculate the average number of beers you drink per day. For example, if you drink 24 beers a week, this would average out to approximately 3 beers per day.", "values": [{"label": "None", "value": "none"}, {"label": "Less than 1 beer", "value": "less_than_1_beer"}, {"label": "1 beer", "value": "1_beer"}, {"label": "2 beers", "value": "2_beers"}, {"label": "3-4 beers", "value": "3_4_beers"}, {"label": "5 or more beers", "value": "5_more_beers"}], "tableView": true, "customConditional": "show = data.drink_alcohol === 'yes' && data.type_of_alcohol.beer;"}, {"key": "header_wine_details", "html": "<h4>Wine Details</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.drink_alcohol === 'yes' && data.type_of_alcohol.wine;", "refreshOnChange": false}, {"key": "wine_bottle_size", "type": "radio", "input": true, "label": "What is the typical size of the wine bottle you drink?", "validate": {"required": true}, "values": [{"label": "375ml (Half bottle)", "value": "375ml"}, {"label": "750ml (Standard bottle)", "value": "750ml"}, {"label": "1 Liter", "value": "1liter"}, {"label": "1.5 Liters (Magnum)", "value": "1.5liters"}], "tableView": true, "customConditional": "show = data.drink_alcohol === 'yes' && data.type_of_alcohol.wine;"}, {"key": "wine_abv_content", "type": "radio", "validate": {"required": true}, "input": true, "label": "What is the typical Alcohol By Volume (ABV) content of the wine you drink?", "values": [{"label": "Low (<8%)", "value": "low"}, {"label": "Medium (8% - 14%)", "value": "medium"}, {"label": "High (>14%)", "value": "high"}], "tableView": true, "customConditional": "show = data.drink_alcohol === 'yes' && data.type_of_alcohol.wine;"}, {"key": "wine_bottle_duration", "type": "radio", "input": true, "validate": {"required": true}, "label": "If you're drinking wine by yourself, how long does a bottle typically last?", "values": [{"label": "Less than a day", "value": "less_than_a_day"}, {"label": "1 day", "value": "1_day"}, {"label": "2 days", "value": "2_days"}, {"label": "3 days", "value": "3_days"}, {"label": "4 days", "value": "4_days"}, {"label": "5 days", "value": "5_days"}, {"label": "More than 5 days", "value": "more_than_5_days"}, {"label": "I rarely finish a bottle", "value": "rarely_finish"}], "tableView": true, "customConditional": "show = data.drink_alcohol === 'yes' && data.type_of_alcohol.wine;"}, {"key": "header_spirits_details", "html": "<h4>Spirits Details</h4>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.drink_alcohol === 'yes' && data.type_of_alcohol.spirits;", "refreshOnChange": false}, {"key": "spirits_bottle_size", "type": "radio", "input": true, "validate": {"required": true}, "label": "What is the typical size of the spirits bottle you drink?", "values": [{"label": "0.375 Liters (12.7 oz)", "value": "375ml_12.7oz"}, {"label": "0.75 Liters (25.4 oz)", "value": "750ml_25.4oz"}, {"label": "1 Liter (33.8 oz)", "value": "1liter_33.8oz"}, {"label": "1.14 Liters (38.5 oz)", "value": "1.14liter_38.5oz"}, {"label": "1.75 Liters (59.2 oz)", "value": "1.75liters_59.2oz"}], "tableView": true, "customConditional": "show = data.drink_alcohol === 'yes' && data.type_of_alcohol.spirits;"}, {"key": "spirits_abv_content", "type": "radio", "input": true, "validate": {"required": true}, "label": "What is the typical Alcohol By Volume (ABV) content of the spirits you drink?", "values": [{"label": "Low (<20%)", "value": "low"}, {"label": "Medium (20% - 40%)", "value": "medium"}, {"label": "High (>40%)", "value": "high"}], "tableView": true, "customConditional": "show = data.drink_alcohol === 'yes' && data.type_of_alcohol.spirits;"}, {"key": "spirits_bottle_duration", "type": "radio", "input": true, "validate": {"required": true}, "label": "If you're drinking spirits by yourself, how long does a bottle typically last?", "values": [{"label": "Less than a day", "value": "less_than_a_day"}, {"label": "1-2 days", "value": "1_2_days"}, {"label": "3-4 days", "value": "3_4_days"}, {"label": "5-9 days", "value": "5_9_days"}, {"label": "10-14 days", "value": "10_14_days"}, {"label": "15-19 days", "value": "15_19_days"}, {"label": "20-24 days", "value": "20_24_days"}, {"label": "25-29 days", "value": "25_29_days"}, {"label": "More than 30 days", "value": "more_than_30_days"}, {"label": "I rarely finish a bottle", "value": "rarely_finish"}], "tableView": true, "customConditional": "show = data.drink_alcohol === 'yes' && data.type_of_alcohol.spirits;"}]}