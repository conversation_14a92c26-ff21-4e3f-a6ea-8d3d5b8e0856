{"components": [{"key": "heading_past_treatments_acne", "html": "</br><h4>Past Acne Treatments</h4>", "type": "content"}, {"key": "used_prescription_past_acne", "type": "radio", "label": "Have you used prescription acne medications (topical or oral) in the past?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "unsure"}], "validate": {"required": true}}, {"key": "past_treatment_effectiveness_acne", "type": "radio", "label": "How well did past prescription treatments work overall?", "values": [{"label": "Very effective - skin cleared", "value": "very_effective"}, {"label": "Helped but didn’t fully clear", "value": "partial"}, {"label": "Not effective", "value": "ineffective"}, {"label": "Not sure / varied", "value": "unsure"}], "validate": {"required": true}, "customConditional": "show = data.used_prescription_past_acne === 'yes';"}, {"key": "past_rx_types", "type": "selectboxes", "label": "Which prescription types have you used? (Select all that apply)", "values": [{"label": "Topical creams / gels / washes", "value": "topical"}, {"label": "Oral medicines (antibiotics, isotretinoin, spironolactone)", "value": "oral"}, {"label": "Hormonal pills (e.g., birth-control)", "value": "hormonal"}], "validate": {"required": true}, "inputType": "checkbox", "customConditional": "show = data.used_prescription_past_acne === 'yes' && !!data.past_treatment_effectiveness_acne;"}, {"key": "heading_topical_selected", "html": "<br><h4>Topical products you’ve used</h4>", "type": "content", "label": "Content", "customConditional": "show = data.past_rx_types && data.past_rx_types.topical === true;"}, {"key": "acne_rx_topical_grid", "type": "<PERSON><PERSON><PERSON>", "label": "Add each topical product you’ve used:", "addAnother": "+ Add Another", "components": [{"key": "rx_name", "data": {"values": [{"label": "Adapalene 0.1 % gel - Differin 0.1 %", "value": "adapalene_01"}, {"label": "Adapalene 0.3 % gel - Differin XP 0.3 %", "value": "adapalene_03"}, {"label": "Adapalene/BPO 0.1 %/2.5 % - TactuPump / Epiduo", "value": "adapalene_bpo_std"}, {"label": "Adapalene/BPO 0.3 %/2.5 % - TactuPump Forte", "value": "adapalene_bpo_forte"}, {"label": "Tazarotene 0.045 % lotion - Arazlo", "value": "tazarotene_0045_lot"}, {"label": "Tazarotene 0.05 % cream - Tazorac", "value": "tazarotene_005_cr"}, {"label": "Tazarotene 0.05 % gel - Tazorac", "value": "tazarotene_005_gel"}, {"label": "Tazarotene 0.1 % cream - Tazorac", "value": "tazarotene_01_cr"}, {"label": "Tazarotene 0.1 % gel - Tazorac", "value": "tazarotene_01_gel"}, {"label": "Tretinoin 0.025 % cream/gel - Stieva-A", "value": "tretinoin_0025"}, {"label": "Tretinoin 0.05 % cream/gel - Stieva-A", "value": "tretinoin_005"}, {"label": "Tretinoin 0.1 % cream/gel - Stieva-A", "value": "tretinoin_01"}, {"label": "Clindamycin 1.2 % + Tretinoin 0.025 % - Biacna", "value": "biacna"}, {"label": "Clindamycin 1 % + BPO 5 % - Clindoxyl / BenzaClin", "value": "clinda_bpo_5"}, {"label": "Erythromycin 3 % + BPO 5 % - Benzamycin", "value": "ery_bpo"}, {"label": "Clindamycin 1 % solution/gel/foam", "value": "clinda_1"}, {"label": "Dapsone 5 % gel - Aczone 5 %", "value": "dapsone_5"}, {"label": "Dapsone 7.5 % gel - Aczone 7.5 %", "value": "dapsone_75"}, {"label": "Clascoterone 1 % cream - Winlevi", "value": "<PERSON><PERSON><PERSON>"}, {"label": "BPO 4 % wash - PanOxyl 4 %", "value": "bpo_wash_4"}, {"label": "BPO 5 % wash - Benzagel 5 % / BenzaWash 5 %", "value": "bpo_wash_5"}, {"label": "BPO 10 % wash - PanOxyl 10 % Foaming Wash", "value": "bpo_wash_10"}, {"label": "Other topical Rx", "value": "other"}]}, "type": "select", "label": "Topical product", "widget": "html5", "validate": {"required": true}}, {"key": "rx_other_name", "type": "textfield", "label": "Other product", "validate": {"required": true}, "customConditional": "show = row && row.rx_name === 'other';"}, {"key": "rx_where", "type": "selectboxes", "label": "Main area applied", "values": [{"label": "Forehead / T-zone", "value": "forehead"}, {"label": "Cheeks", "value": "cheeks"}, {"label": "Jawline / Chin", "value": "jaw_chin"}, {"label": "Neck", "value": "neck"}, {"label": "Chest / Shoulders", "value": "chest"}, {"label": "Back", "value": "back"}, {"label": "Other", "value": "other"}, {"label": "None of the above", "value": "none", "customClass": "mt-n3"}], "validate": {"required": true}, "inputType": "checkbox"}, {"key": "rx_where_other", "type": "textfield", "label": "Other area", "validate": {"required": true}, "customConditional": "show = row && row.rx_where && row.rx_where.other;"}, {"key": "rx_current", "type": "radio", "label": "Using", "inline": true, "values": [{"label": "Currently", "value": "current"}, {"label": "Past", "value": "past"}], "validate": {"required": true}}, {"key": "stop_reason", "data": {"values": [{"label": "Skin cleared / no longer needed", "value": "cleared"}, {"label": "Side-effects", "value": "sidefx"}, {"label": "Didn’t help enough", "value": "ineff"}, {"label": "Cost / insurance coverage", "value": "cost"}, {"label": "Pregnancy / breastfeeding", "value": "preg"}, {"label": "Other reason", "value": "other"}]}, "type": "select", "label": "Why did you stop this medicine?", "widget": "html5", "validate": {"required": true}, "customConditional": "show = row && row.rx_current === 'past';"}, {"key": "stop_sidefx_list", "type": "selectboxes", "label": "Which side-effects did you notice?", "values": [{"label": "Dryness / peeling", "value": "dry"}, {"label": "Redness / irritation", "value": "red"}, {"label": "Burning / stinging", "value": "burn"}, {"label": "Increased sun-sensitivity", "value": "sun"}, {"label": "Headache / dizziness", "value": "headache"}, {"label": "Upset stomach", "value": "gi"}, {"label": "Other", "value": "other"}], "validate": {"required": true}, "inputType": "checkbox", "customConditional": "show = row && row.stop_reason === 'sidefx';"}, {"key": "stop_sidefx_other_detail", "type": "textfield", "label": "Other side-effect:", "validate": {"required": true}, "customConditional": "show = row && row.stop_sidefx_list && row.stop_sidefx_list.other;"}, {"key": "stop_other_detail", "type": "textfield", "label": "Please specify other reason:", "validate": {"required": true}, "customConditional": "show = row && row.stop_reason === 'other';"}, {"key": "rx_last_used", "data": {"values": [{"label": "≤ 1 week ago", "value": "le1w"}, {"label": "1-4 weeks ago", "value": "1_4w"}, {"label": "1-3 months ago", "value": "1_3m"}, {"label": "3-6 months ago", "value": "3_6m"}, {"label": "> 6 months ago", "value": "gt6m"}, {"label": "Not sure", "value": "unsure"}]}, "type": "select", "label": "When did you last use it?", "widget": "html5", "validate": {"required": true}, "customConditional": "show = row && row.rx_current === 'past';"}, {"key": "rx_effect", "data": {"values": [{"label": "Cleared acne", "value": "cleared"}, {"label": "Helped somewhat", "value": "partial"}, {"label": "No change", "value": "none"}, {"label": "Made it worse", "value": "worse"}, {"label": "Not sure", "value": "unsure"}]}, "type": "select", "label": "Effect", "widget": "html5", "validate": {"required": true}}, {"key": "rx_freq", "data": {"values": [{"label": "Once daily", "value": "qd"}, {"label": "Twice daily", "value": "bid"}, {"label": "Every other day", "value": "qod"}, {"label": "PRN / occasionally", "value": "prn"}, {"label": "Not sure", "value": "unsure"}]}, "type": "select", "label": "Frequency", "widget": "html5", "validate": {"required": true}}, {"key": "rx_longest", "data": {"values": [{"label": "≤ 4 weeks", "value": "le4w"}, {"label": "1 month", "value": "1m"}, {"label": "2 months", "value": "2m"}, {"label": "3 months", "value": "3m"}, {"label": "4 months", "value": "4m"}, {"label": "5 months", "value": "5m"}, {"label": "6 months", "value": "6m"}, {"label": "7 months", "value": "7m"}, {"label": "8 months", "value": "8m"}, {"label": "9 months", "value": "9m"}, {"label": "10 months", "value": "10m"}, {"label": "11 months", "value": "11m"}, {"label": "12 months", "value": "12m"}, {"label": "1-2 years", "value": "1_2y"}, {"label": "2-3 years", "value": "2_3y"}, {"label": "3-5 years", "value": "3_5y"}, {"label": "> 5 years", "value": "gt5y"}, {"label": "Not sure", "value": "unsure"}]}, "type": "select", "label": "Longest single stretch you used it <em>without breaks</em>:", "widget": "html5", "validate": {"required": true}}, {"key": "rx_form", "data": {"values": [{"label": "<PERSON>el", "value": "gel"}, {"label": "Cream", "value": "cream"}, {"label": "Lotion / Foam", "value": "lotion"}, {"label": "Wash", "value": "wash"}, {"label": "Other / unsure", "value": "unsure"}]}, "type": "select", "label": "Form", "widget": "html5", "validate": {"required": true}}], "rowTemplate": "<div class='card shadow-sm w-100'><div class='card-body py-2 px-3'><div class='row small'><div class='col-sm-3'><strong>{{ data.rx_name === 'other' ? data.rx_other_name : _.startCase(data.rx_name.replace(/_/g,' ')) }}</strong></div><div class='col-sm-3'>{{ _.join(_.map(_.keys(_.pickBy(data.rx_where,Boolean)), _.startCase), ', ') }}</div><div class='col-sm-1'>{{ data.rx_current==='current' ? 'Yes' : 'Past' }}</div><div class='col-sm-2'>{{ (data.rx_effect || '').replace('_',' ') | title }}</div><div class='col-sm-1'>{{ ({qd:'1×',bid:'2×',qod:'QOD',prn:'PRN',unsure:'N/S'}[data.rx_freq] || '') }}</div><div class='col-sm-2'>{{ ({le4w:'≤4 w','1_3m':'1-3 m','3_6m':'3-6 m','6_12m':'6-12 m','gt12m':'12 m+'}[data.rx_longest] || '') }}</div></div></div></div>", "customConditional": "show = data.past_rx_types && data.past_rx_types.topical;"}, {"key": "heading_oral_selected", "html": "<br><h4>Oral medicines you’ve used</h4>", "type": "content", "label": "Content", "customConditional": "show = data.past_rx_types && data.past_rx_types.oral === true;"}, {"key": "acne_rx_oral_grid", "type": "<PERSON><PERSON><PERSON>", "label": "Add each oral / hormonal product you’ve used:", "addAnother": "+ Add Another", "components": [{"key": "rx_name", "data": {"values": [{"label": "Aldactone 25 mg (spironolactone)", "value": "spiro25"}, {"label": "Aldactone 50 mg (spironolactone)", "value": "spiro50"}, {"label": "Aldactone 100 mg (spironolactone)", "value": "spiro100"}, {"label": "Doxycycline", "value": "doxy"}, {"label": "Minocycline", "value": "mino"}, {"label": "Tetracycline", "value": "tetra"}, {"label": "Trimethoprim-sulfamethoxazole", "value": "tmp_smx"}, {"label": "Isotretinoin (Accutane / Epuris / Clarus)", "value": "iso"}, {"label": "Combined OCP - Diane-35 / Yaz / <PERSON><PERSON><PERSON>", "value": "coc"}, {"label": "Other oral / hormonal Rx", "value": "other"}]}, "type": "select", "label": "Pills", "widget": "html5", "validate": {"required": true}}, {"key": "rx_other_name", "type": "textfield", "label": "Other product", "validate": {"required": true}, "customConditional": "show = row && row.rx_name === 'other';"}, {"key": "rx_where", "type": "selectboxes", "label": "Main area improved", "values": [{"label": "Face", "value": "face"}, {"label": "Neck", "value": "neck"}, {"label": "Chest", "value": "chest"}, {"label": "Back", "value": "back"}, {"label": "Other", "value": "other"}, {"label": "None / Not sure", "value": "none", "customClass": "mt-n3"}], "validate": {"required": true}, "inputType": "checkbox"}, {"key": "rx_where_other", "type": "textfield", "label": "Other area", "validate": {"required": true}, "customConditional": "show = row && row.rx_where && row.rx_where.other;"}, {"key": "rx_current", "type": "radio", "label": "Using", "inline": true, "values": [{"label": "Currently", "value": "current"}, {"label": "Past", "value": "past"}], "validate": {"required": true}}, {"key": "stop_reason", "data": {"values": [{"label": "Skin cleared / no longer needed", "value": "cleared"}, {"label": "Side-effects", "value": "sidefx"}, {"label": "Didn’t help enough", "value": "ineff"}, {"label": "Cost / insurance coverage", "value": "cost"}, {"label": "Pregnancy / breastfeeding", "value": "preg"}, {"label": "Other reason", "value": "other"}]}, "type": "select", "label": "Why did you stop this medicine?", "widget": "html5", "validate": {"required": true}, "customConditional": "show = row && row.rx_current === 'past';"}, {"key": "stop_sidefx_list", "type": "selectboxes", "label": "Which side-effects did you notice?", "values": [{"label": "Upset stomach / nausea", "value": "gi"}, {"label": "Diarrhea", "value": "diarrhea"}, {"label": "Yeast infection", "value": "yeast"}, {"label": "Sun-sensitivity", "value": "sun"}, {"label": "Headache / dizziness", "value": "headache"}, {"label": "Rash / allergy", "value": "rash"}, {"label": "Other", "value": "other"}], "validate": {"required": true}, "inputType": "checkbox", "customConditional": "show = row && row.stop_reason === 'sidefx';"}, {"key": "stop_sidefx_other_detail", "type": "textfield", "label": "Other side-effect:", "validate": {"required": true}, "customConditional": "show = row && row.stop_sidefx_list && row.stop_sidefx_list.other;"}, {"key": "stop_other_detail", "type": "textfield", "label": "Please specify other reason:", "validate": {"required": true}, "customConditional": "show = row && row.stop_reason === 'other';"}, {"key": "rx_effect", "data": {"values": [{"label": "Cleared acne", "value": "cleared"}, {"label": "Helped somewhat", "value": "partial"}, {"label": "No change", "value": "none"}, {"label": "Made it worse", "value": "worse"}, {"label": "Not sure", "value": "unsure"}]}, "type": "select", "label": "Effect", "widget": "html5", "validate": {"required": true}}, {"key": "rx_freq", "data": {"values": [{"label": "Once daily", "value": "qd"}, {"label": "Twice daily", "value": "bid"}, {"label": "Other / unsure", "value": "unsure"}]}, "type": "select", "label": "Usual dose", "widget": "html5", "validate": {"required": true}}, {"key": "rx_last_used", "data": {"values": [{"label": "≤ 1 week ago", "value": "le1w"}, {"label": "1-4 weeks ago", "value": "1_4w"}, {"label": "1-3 months ago", "value": "1_3m"}, {"label": "3-6 months ago", "value": "3_6m"}, {"label": "> 6 months ago", "value": "gt6m"}, {"label": "Not sure", "value": "unsure"}]}, "type": "select", "label": "When did you last use it?", "widget": "html5", "validate": {"required": true}, "customConditional": "show = row && row.rx_current === 'past';"}, {"key": "rx_longest", "data": {"values": [{"label": "≤ 4 weeks", "value": "le4w"}, {"label": "1 month", "value": "1m"}, {"label": "2 months", "value": "2m"}, {"label": "3 months", "value": "3m"}, {"label": "4 months", "value": "4m"}, {"label": "5 months", "value": "5m"}, {"label": "6 months", "value": "6m"}, {"label": "7 months", "value": "7m"}, {"label": "8 months", "value": "8m"}, {"label": "9 months", "value": "9m"}, {"label": "10 months", "value": "10m"}, {"label": "11 months", "value": "11m"}, {"label": "12 months", "value": "12m"}, {"label": "1-2 years", "value": "1_2y"}, {"label": "2-3 years", "value": "2_3y"}, {"label": "3-5 years", "value": "3_5y"}, {"label": "> 5 years", "value": "gt5y"}, {"label": "Not sure", "value": "unsure"}]}, "type": "select", "label": "Longest single stretch you used it <em>without breaks</em>:", "widget": "html5", "validate": {"required": true}}, {"key": "rx_form", "data": {"values": [{"label": "Capsule", "value": "capsule"}, {"label": "Tablet", "value": "tablet"}, {"label": "Other / unsure", "value": "unsure"}]}, "type": "select", "label": "Form", "widget": "html5", "validate": {"required": true}}], "rowTemplate": "<div class='card shadow-sm w-100'><div class='card-body py-2 px-3'><div class='row small'><div class='col-sm-3'><strong>{{ data.rx_name === 'other' ? data.rx_other_name : _.startCase(data.rx_name.replace(/_/g,' ')) }}</strong></div><div class='col-sm-3'>{{ _.join(_.map(_.keys(_.pickBy(data.rx_where,Boolean)), _.startCase), ', ') }}</div><div class='col-sm-1'>{{ data.rx_current==='current' ? 'Yes' : 'Past' }}</div><div class='col-sm-2'>{{ (data.rx_effect || '').replace('_',' ') | title }}</div><div class='col-sm-1'>{{ ({qd:'1×',bid:'2×',qod:'QOD',prn:'PRN',unsure:'N/S'}[data.rx_freq] || '') }}</div><div class='col-sm-2'>{{ ({le4w:'≤4 w',1m:'1 m',2m:'2 m',3m:'3 m',4m:'4 m',5m:'5 m',6m:'6 m',7m:'7 m',8m:'8 m',9m:'9 m',10m:'10 m',11m:'11 m',12m:'12 m',1_2y:'1-2 y',2_3y:'2-3 y',3_5y:'3-5 y',gt5y:'5 y+'}[data.rx_longest] || '') }}</div></div></div></div>", "customConditional": "show = data.past_rx_types && data.past_rx_types.oral;"}, {"key": "heading_hormonal_selected", "html": "<br><h4>Hormonal / anti-androgen treatments you’ve used</h4>", "type": "content", "label": "Content", "customConditional": "show = data.past_rx_types && data.past_rx_types.hormonal === true;"}, {"key": "hormonal_rx_grid", "type": "<PERSON><PERSON><PERSON>", "label": "Hormonal or anti-androgen medicines you’ve used:", "addAnother": "+ Add Another", "components": [{"key": "hormonal_name", "data": {"values": [{"label": "Alesse 20 µg EE / 100 µg levonorgestrel", "value": "alesse"}, {"label": "Brevicon 0.5/35 µg EE / 0.5 mg norethindrone", "value": "brevicon05"}, {"label": "Brevicon 1/35 µg EE / 1 mg norethindrone", "value": "brevicon1"}, {"label": "Cyclen 35 µg EE / 250 µg norgestimate", "value": "cyclen"}, {"label": "Demulen 30 µg EE / 2 mg ethynodiol diacetate", "value": "demulen30"}, {"label": "Diane-35 35 µg EE / 2 mg cyproterone", "value": "diane35"}, {"label": "Evra transdermal patch (norelgestromin/EE)", "value": "evra_patch"}, {"label": "Linessa triphasic EE 25 µg / desogestrel 100-150 µg", "value": "linessa"}, {"label": "Loestrin 1.5/30 µg EE / 1.5 mg norethindrone", "value": "<PERSON><PERSON><PERSON>"}, {"label": "Lolo 10 µg EE / 1 mg norethindrone", "value": "lolo"}, {"label": "Marvelon 30 µg EE / 150 µg desogestrel", "value": "marvelon"}, {"label": "Micronor 0.35 mg norethindrone (POP)", "value": "micronor"}, {"label": "Minestrin 1/20 20 µg EE / 1 mg norethindrone", "value": "minestrin"}, {"label": "Min-Ovral 30 µg EE / 150 µg levonorgestrel", "value": "min<PERSON><PERSON>"}, {"label": "NuvaRing 15 µg EE / 120 µg etonogestrel", "value": "nuvaring"}, {"label": "Ortho 7/7/7 triphasic EE 35 µg / norethindrone 0.5-1 mg", "value": "ortho777"}, {"label": "Seasonale 30 µg EE / 150 µg levonorgestrel, 84-day", "value": "seasonale"}, {"label": "Seasonique 30 µg EE / 150 µg levonorgestrel, 84-day", "value": "seasonique"}, {"label": "Slynd 4 mg drospirenone (POP)", "value": "slynd"}, {"label": "Synphasic biphasic EE 35 µg / norethindrone 0.5-1 mg", "value": "synphasic"}, {"label": "Tri-Cyclen triphasic EE 35 µg / norgestimate 0.18-0.25 mg", "value": "tricyclen"}, {"label": "Tri-Cyclen Lo triphasic EE 25 µg / norgestimate 0.18-0.25 mg", "value": "tricyclen_lo"}, {"label": "Yasmin 30 µg EE / 3 mg drospirenone", "value": "yasmin"}, {"label": "Yaz 20 µg EE / 3 mg drospirenone", "value": "yaz"}, {"label": "Other hormonal / anti-androgen", "value": "other"}]}, "type": "select", "label": "Product", "widget": "html5", "validate": {"required": true}}, {"key": "hormonal_other_name", "type": "textfield", "label": "Other product", "validate": {"required": true}, "customConditional": "show = row && row.hormonal_name === 'other';"}, {"key": "hormonal_current", "type": "radio", "label": "Using", "inline": true, "values": [{"label": "Currently", "value": "current"}, {"label": "Past", "value": "past"}], "validate": {"required": true}}, {"key": "stop_reason", "data": {"values": [{"label": "Skin cleared / no longer needed", "value": "cleared"}, {"label": "Side-effects", "value": "sidefx"}, {"label": "Didn’t help enough", "value": "ineff"}, {"label": "Cost / insurance coverage", "value": "cost"}, {"label": "Pregnancy / breastfeeding", "value": "preg"}, {"label": "Other reason", "value": "other"}]}, "type": "select", "label": "Why did you stop this medicine?", "widget": "html5", "validate": {"required": true}, "customConditional": "show = row && row.hormonal_current === 'past';"}, {"key": "stop_sidefx_list", "type": "selectboxes", "label": "Which side-effects did you notice?", "values": [{"label": "Breast tenderness", "value": "breast"}, {"label": "<PERSON><PERSON><PERSON>", "value": "nausea"}, {"label": "Mood changes", "value": "mood"}, {"label": "Weight or appetite change", "value": "weight"}, {"label": "Irregular bleeding / spotting", "value": "bleed"}, {"label": "Headache / migraine", "value": "headache"}, {"label": "Other", "value": "other"}], "validate": {"required": true}, "inputType": "checkbox", "customConditional": "show = row && row.stop_reason === 'sidefx';"}, {"key": "stop_sidefx_other_detail", "type": "textfield", "label": "Other side-effect:", "validate": {"required": true}, "customConditional": "show = row && row.stop_sidefx_list && row.stop_sidefx_list.other;"}, {"key": "stop_other_detail", "type": "textfield", "label": "Please specify other reason:", "validate": {"required": true}, "customConditional": "show = row && row.stop_reason === 'other';"}, {"key": "hormonal_effect", "data": {"values": [{"label": "Cleared acne", "value": "cleared"}, {"label": "Helped somewhat", "value": "partial"}, {"label": "No change", "value": "none"}, {"label": "Made it worse", "value": "worse"}, {"label": "Not sure", "value": "unsure"}]}, "type": "select", "label": "Effect on acne", "widget": "html5", "validate": {"required": true}}, {"key": "hormonal_last_used", "data": {"values": [{"label": "≤ 1 week ago", "value": "le1w"}, {"label": "1-4 weeks ago", "value": "1_4w"}, {"label": "1-3 months ago", "value": "1_3m"}, {"label": "3-6 months ago", "value": "3_6m"}, {"label": "> 6 months ago", "value": "gt6m"}, {"label": "Not sure", "value": "unsure"}]}, "type": "select", "label": "When did you last use it?", "widget": "html5", "validate": {"required": true}, "customConditional": "show = row && row.hormonal_current === 'past';"}, {"key": "hormonal_duration", "data": {"values": [{"label": "≤ 4 weeks", "value": "le4w"}, {"label": "1 month", "value": "1m"}, {"label": "2 months", "value": "2m"}, {"label": "3 months", "value": "3m"}, {"label": "4 months", "value": "4m"}, {"label": "5 months", "value": "5m"}, {"label": "6 months", "value": "6m"}, {"label": "7 months", "value": "7m"}, {"label": "8 months", "value": "8m"}, {"label": "9 months", "value": "9m"}, {"label": "10 months", "value": "10m"}, {"label": "11 months", "value": "11m"}, {"label": "12 months", "value": "12m"}, {"label": "1-2 years", "value": "1_2y"}, {"label": "2-3 years", "value": "2_3y"}, {"label": "3-5 years", "value": "3_5y"}, {"label": "> 5 years", "value": "gt5y"}, {"label": "Not sure", "value": "unsure"}]}, "type": "select", "label": "How long used", "widget": "html5", "validate": {"required": true}}], "rowTemplate": "<div class='card shadow-sm w-100'><div class='card-body py-2 px-3'><div class='row small'><div class='col-sm-4'><strong>{{ data.hormonal_name === 'other' ? data.hormonal_other_name : _.startCase(data.hormonal_name.replace(/_/g,' ')) }}</strong></div><div class='col-sm-2'>{{ data.hormonal_current==='current' ? 'Yes' : 'Past' }}</div><div class='col-sm-3'>{{ (data.hormonal_effect || '').replace('_',' ') | title }}</div><div class='col-sm-3'>{{ ({le4w:'≤4 w',1m:'1 m',2m:'2 m',3m:'3 m',4m:'4 m',5m:'5 m',6m:'6 m',7m:'7 m',8m:'8 m',9m:'9 m',10m:'10 m',11m:'11 m',12m:'12 m',1_2y:'1-2 y',2_3y:'2-3 y',3_5y:'3-5 y',gt5y:'5 y+'}[data.hormonal_duration] || '') }}</div></div></div></div>", "customConditional": "show = data.past_rx_types && data.past_rx_types.hormonal;"}]}