{"components": [{"key": "heading_reason_screening", "html": "<h4>Reason for Screening</h4>", "type": "content", "input": false, "tableView": false}, {"key": "reason_for_screening", "type": "selectboxes", "input": true, "label": "Why are you requesting celiac screening today?", "values": [{"label": "I have symptoms", "value": "digestive_symptoms"}, {"label": "I have been told I have nutrient deficiencies", "value": "nutrient_deficiencies"}, {"label": "A doctor recommended testing", "value": "doctor_recommended"}, {"label": "I have a family history of celiac disease", "value": "family_history"}, {"label": "Other", "value": "other"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Reason for celiac screening:"}, {"key": "reason_for_screening_other", "type": "textfield", "input": true, "label": "Please specify:", "validate": {"required": true}, "tableView": true, "confirm_label": "Additional reason for celiac screening:", "customConditional": "show = data.reason_for_screening && data.reason_for_screening.other;"}, {"key": "heading_nutrient_deficiencies", "html": "<h4>Nutrient Deficiencies</h4>", "type": "content", "input": false, "tableView": false, "customConditional": "show = data.reason_for_screening?.nutrient_deficiencies === true"}, {"key": "nutrient_deficiency_diagnosis_type", "type": "radio", "input": true, "label": "Was the nutrient deficiency confirmed by blood test, or is it suspected based on symptoms?", "values": [{"label": "Yes - confirmed by a lab test", "value": "confirmed"}, {"label": "No - only suspected, not tested", "value": "suspected"}, {"label": "I'm not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Was your nutrient deficiency lab-confirmed?", "customConditional": "show = data.reason_for_screening?.nutrient_deficiencies === true"}, {"key": "nutrients_confirmed_deficient", "type": "selectboxes", "input": true, "label": "Which nutrients were low on your blood tests?", "values": [{"label": "Vitamin B12", "value": "b12"}, {"label": "Iron / Ferritin", "value": "iron"}, {"label": "<PERSON><PERSON>", "value": "vitamin_d"}, {"label": "Folate", "value": "folate"}, {"label": "Magnesium", "value": "magnesium"}, {"label": "None of the above", "value": "none"}], "tableView": true, "confirm_label": "Nutrients that were low:", "customConditional": "show = data.nutrient_deficiency_diagnosis_type === 'confirmed'", "optionsLabelPosition": "right"}, {"key": "b12_low_range", "data": {"values": [{"label": "Below 100 pmol/L", "value": "below_100"}, {"label": "100-149 pmol/L", "value": "100_149"}, {"label": "150-199 pmol/L", "value": "150_199"}, {"label": "200-250 pmol/L", "value": "200_250"}, {"label": "I don't remember", "value": "dont_remember"}]}, "type": "select", "input": true, "label": "How low was your B12 level?", "widget": "html5", "confirm_label": "Lowest B12 range:", "customConditional": "show = data.nutrients_confirmed_deficient?.b12 === true"}, {"key": "iron_low_range", "data": {"values": [{"label": "Below 10 µg/L", "value": "below_10"}, {"label": "10-20 µg/L", "value": "10_20"}, {"label": "21-30 µg/L", "value": "21_30"}, {"label": "31-50 µg/L", "value": "31_50"}, {"label": "I don't remember", "value": "dont_remember"}]}, "type": "select", "input": true, "label": "How low was your ferritin (iron storage) level?", "widget": "html5", "confirm_label": "Lowest iron/ferritin range:", "customConditional": "show = data.nutrients_confirmed_deficient?.iron === true"}, {"key": "vitamin_d_low_range", "data": {"values": [{"label": "Below 25 nmol/L", "value": "below_25"}, {"label": "25-49 nmol/L", "value": "25_49"}, {"label": "50-74 nmol/L", "value": "50_74"}, {"label": "75-90 nmol/L", "value": "75_90"}, {"label": "I don't remember", "value": "dont_remember"}]}, "type": "select", "input": true, "label": "How low was your vitamin D level?", "widget": "html5", "confirm_label": "Lowest vitamin D range:", "customConditional": "show = data.nutrients_confirmed_deficient?.vitamin_d === true"}, {"key": "nutrient_supplemented", "type": "radio", "input": true, "label": "Did you take supplements to correct the deficiency?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Supplementation used?", "customConditional": "show = data.nutrient_deficiency_diagnosis_type === 'confirmed'"}, {"key": "nutrient_repeat_test", "type": "radio", "input": true, "label": "Did you ever have a repeat blood test to check if the deficiency was corrected?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Repeat test completed:", "customConditional": "show = data.nutrient_supplemented === 'yes'"}, {"key": "nutrient_corrected", "type": "radio", "input": true, "label": "Did your blood levels return to normal after treatment?", "values": [{"label": "Yes, the deficiency was corrected", "value": "corrected"}, {"label": "No, still below normal", "value": "still_low"}, {"label": "I'm not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Did levels normalize?", "customConditional": "show = data.nutrient_repeat_test === 'yes'"}, {"key": "low_nutrient_vegetarian_vegan", "type": "radio", "input": true, "label": "Do you follow a vegetarian or vegan diet?", "values": [{"label": "Yes - vegetarian", "value": "vegetarian"}, {"label": "Yes - vegan", "value": "vegan"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Vegetarian or vegan diet:", "customConditional": "show = data.nutrients_confirmed_deficient?.b12 === true || data.nutrients_confirmed_deficient?.iron === true"}, {"key": "recommendation_b12_iron_vegetarian", "html": "<div style='border-left: 4px solid #198754; background-color: #d1e7dd; padding: 10px; margin-bottom: 10px;'><strong>Note:</strong> If you're vegetarian or vegan, low vitamin B12 and iron levels are often not caused by celiac disease. These nutrients are naturally found in animal-based foods and may be low in plant-based diets unless carefully managed. We recommend working with a dietitian to ensure your diet includes all essential micronutrients, and to consider a multivitamin. A well-planned plant-based diet can be healthy and complete — but regular supplementation may be needed.</div>", "type": "content", "input": false, "customConditional": "show = data.low_nutrient_vegetarian_vegan === 'vegetarian' || data.low_nutrient_vegetarian_vegan === 'vegan'"}, {"key": "recommendation_b12_iron_vegetarian_acknowledgement", "type": "radio", "input": true, "label": "Do you understand this recommendation about the importance of planning your diet to prevent B12 or iron deficiency?", "values": [{"label": "I understand", "value": "understand"}, {"label": "I do not understand", "value": "do_not_understand"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Understands plant-based diet and deficiency recommendation:", "customConditional": "show = data.low_nutrient_vegetarian_vegan === 'vegetarian' || data.low_nutrient_vegetarian_vegan === 'vegan'"}, {"key": "heading_current_symptoms", "html": "<h4>Current Symptoms</h4>", "type": "content", "input": false, "tableView": false}, {"key": "celiac_symptoms", "type": "selectboxes", "input": true, "label": "Are you currently experiencing any of the following symptoms?", "values": [{"label": "Bloating or gas", "value": "bloating"}, {"label": "Diarrhea", "value": "diarrhea"}, {"label": "Constipation", "value": "constipation"}, {"label": "Abdominal discomfort or pain", "value": "abdominal_pain"}, {"label": "Fatigue", "value": "fatigue"}, {"label": "Unintentional weight loss", "value": "weight_loss"}, {"label": "Skin changes (itchy rash or blisters)", "value": "skin_changes"}, {"label": "Other symptoms", "value": "other"}], "adminFlag": true, "inputType": "checkbox", "tableView": true, "confirm_label": "I have the following celiac-related symptoms:", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_celiac_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "Please select at least one symptom or check 'None of the above.'"}, "validate": {"custom": "valid = !!data.none_of_the_above_celiac_symptoms || _.some(_.values(data.celiac_symptoms));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "celiac_symptoms_present", "type": "textfield", "input": true, "label": "Patient indicated they have the following celiac symptoms:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "You have the following celiac symptoms:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.celiac_symptoms)), _.capitalize), ', '), /_/g, ' ');"}, {"key": "celiac_symptoms_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following celiac symptoms:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "You DO NOT have the following celiac symptoms:", "calculateValue": "value = _.replace(_.join(_.map(_.difference(_.keys(_.pickBy(data.celiac_symptoms, _.negate(_.identity))),['other']), _.capitalize), ', '), /_/g, ' ');"}, {"key": "stated_other_celiac_symptoms", "type": "textarea", "input": true, "label": "Please describe any other symptoms you think may be related to gluten or digestion. Leave blank if not applicable.", "adminFlag": true, "tableView": true, "autoExpand": false, "placeholder": "e.g. headaches, joint pain, mood changes", "customConditional": "show = data.celiac_symptoms?.other === true"}, {"key": "durations", "type": "textfield", "input": true, "label": "Durations:", "hidden": true, "disabled": true, "multiple": true, "tableView": false, "clearOnHide": false, "calculateValue": [{"label": "Doesn't apply", "value": "doesn't_apply"}, {"label": "Last night", "value": "last_night"}, {"label": "Today", "value": "today"}, {"label": "1 day ago", "value": "1_day_ago"}, {"label": "2 days ago", "value": "2_days_ago"}, {"label": "3 days ago", "value": "3_days_ago"}, {"label": "4 days ago", "value": "4_days_ago"}, {"label": "5 days ago", "value": "5_days_ago"}, {"label": "6 days ago", "value": "6_days_ago"}, {"label": "7 days ago", "value": "7_days_ago"}, {"label": "8 days ago", "value": "8_days_ago"}, {"label": "9 days ago", "value": "9_days_ago"}, {"label": "10 days ago", "value": "10_days_ago"}, {"label": "11 days ago", "value": "11_days_ago"}, {"label": "12 days ago", "value": "12_days_ago"}, {"label": "13 days ago", "value": "13_days_ago"}, {"label": "14 days ago", "value": "14_days_ago"}, {"label": "14-21 days ago", "value": "14-21_days"}, {"label": "21-28 days ago", "value": "21-28_days"}, {"label": "28-60 days ago", "value": "28-60_days"}, {"label": "60-90 days ago", "value": "60-90_days"}, {"label": "90 days - 1 year ago", "value": "90_days-1_year"}, {"label": "1+ year ago", "value": "1+_year"}]}, {"key": "celiac_symptom_onset_pattern", "type": "radio", "input": true, "label": "Did your symptoms start around the same time or on separate days?", "values": [{"label": "All symptoms started around the same time", "value": "same_time"}, {"label": "Symptoms started on separate days", "value": "separate_days"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Symptom onset pattern:", "customConditional": "show = _.sum(_.values(data.celiac_symptoms).map(Number)) >= 2 && !data.none_of_the_above_celiac_symptoms", "optionsLabelPosition": "right"}, {"key": "all_celiac_symptoms_start", "data": {"custom": "values = data.durations"}, "type": "select", "input": true, "label": "When did your symptoms start?", "widget": "html5", "dataSrc": "custom", "validate": {"required": true}, "tableView": true, "confirm_label": "All symptoms started:", "customConditional": "show = data.celiac_symptom_onset_pattern === 'same_time'", "optionsLabelPosition": "right"}, {"key": "constipation_diarrhea_pattern", "type": "radio", "input": true, "label": "You've indicated that you experience both constipation and diarrhea. Which of the following best describes your pattern?", "values": [{"label": "I alternate between diarrhea and constipation", "value": "alternating"}, {"label": "I'm mostly constipated but occasionally have diarrhea", "value": "mostly_constipated"}, {"label": "I'm mostly dealing with diarrhea, but sometimes constipated", "value": "mostly_diarrhea"}, {"label": "Both are ongoing and unpredictable", "value": "mixed_unpredictable"}, {"label": "I'm not sure how to describe it", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Bowel symptom pattern:", "customConditional": "show = data.celiac_symptoms?.diarrhea && data.celiac_symptoms?.constipation", "optionsLabelPosition": "right"}, {"key": "heading_bloating", "html": "<h4 class='mb-n2'>Bloating or Gas</h4>", "type": "content", "input": false, "label": "Bloating Details", "tableView": false, "refreshOnChange": true, "customConditional": "show = data.celiac_symptoms?.bloating === true"}, {"key": "symptom_start_bloating", "data": {"custom": "values = [{label: `I've always had bloating and this isn't new for me`, value: `always`}].concat(data.durations)"}, "type": "select", "input": true, "label": "When did your bloating or gas start?", "widget": "html5", "dataSrc": "custom", "validate": {"required": true}, "tableView": true, "confirm_label": "Bloating started:", "customConditional": "show = data.celiac_symptoms?.bloating === true && (_.sum(_.values(data.celiac_symptoms).map(Number)) < 2 || data.celiac_symptom_onset_pattern == 'separate_days')", "optionsLabelPosition": "right"}, {"key": "bloating_timing", "type": "selectboxes", "input": true, "label": "When is your bloating typically the worst?", "values": [{"label": "After meals", "value": "after_meals"}, {"label": "In the evening", "value": "evening"}, {"label": "In the morning", "value": "morning"}, {"label": "All day long", "value": "all_day"}, {"label": "Only sometimes, no clear pattern", "value": "unpredictable"}], "tableView": true, "confirm_label": "Bloating is usually worst:", "customConditional": "show = data.celiac_symptoms?.bloating === true", "optionsLabelPosition": "right"}, {"key": "bloating_visible_swelling", "type": "radio", "input": true, "label": "Do you physically notice your abdomen looks more swollen or distended when you are bloated?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "I'm not sure", "value": "not_sure"}], "tableView": true, "confirm_label": "Visible abdominal swelling when bloated:", "customConditional": "show = data.bloating_timing && Object.keys(data.bloating_timing).length > 0"}, {"key": "bloating_relief", "type": "selectboxes", "input": true, "label": "What helps relieve the bloating when it occurs?", "values": [{"label": "Passing gas (farting)", "value": "passing_gas"}, {"label": "Having a bowel movement (pooping)", "value": "bowel_movement"}, {"label": "Lying down", "value": "lying_down"}, {"label": "Avoiding certain foods", "value": "dietary_change"}, {"label": "Nothing helps", "value": "nothing"}], "tableView": true, "confirm_label": "Things that relieve bloating:", "customConditional": "show = data.bloating_visible_swelling", "optionsLabelPosition": "right"}, {"key": "bloating_frequency", "type": "radio", "input": true, "label": "How often do you experience bloating?", "values": [{"label": "Every day", "value": "daily"}, {"label": "Most days of the week", "value": "most_days"}, {"label": "A few times per week", "value": "few_per_week"}, {"label": "Rarely", "value": "rarely"}], "tableView": true, "confirm_label": "Bloating frequency:", "customConditional": "show = data.bloating_relief && Object.keys(data.bloating_relief).length > 0"}, {"key": "heading_diarrhea", "html": "<h4 class='mb-n2'>Diarrhea</h4>", "type": "content", "input": false, "label": "Diarrhea <PERSON>", "tableView": false, "refreshOnChange": true, "customConditional": "show = data.celiac_symptoms?.diarrhea === true"}, {"key": "symptom_start_diarrhea", "data": {"custom": "values = [{label: `I've always had diarrhea and this isn't new for me`, value:`always`}].concat(data.durations)"}, "type": "select", "input": true, "label": "When did your diarrhea start?", "widget": "html5", "dataSrc": "custom", "validate": {"required": true}, "tableView": true, "confirm_label": "Diarrhea started:", "customConditional": "show = data.celiac_symptoms?.diarrhea === true && (_.sum(_.values(data.celiac_symptoms).map(Number)) < 2 || data.celiac_symptom_onset_pattern == 'separate_days')", "optionsLabelPosition": "right"}, {"key": "diarrhea_frequency", "type": "radio", "input": true, "label": "How often do you have loose or watery bowel movements?", "values": [{"label": "Multiple times a day", "value": "many_per_day"}, {"label": "Once per day", "value": "daily"}, {"label": "A few times per week", "value": "few_per_week"}, {"label": "Rarely", "value": "rarely"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Diarrhea frequency:", "customConditional": "show = data.celiac_symptoms?.diarrhea === true && (_.sum(_.values(data.celiac_symptoms).map(Number)) < 2 || data.celiac_symptom_onset_pattern === 'separate_days' || data.celiac_symptom_onset_pattern === 'same_time')"}, {"key": "diarrhea_consistency", "type": "selectboxes", "input": true, "label": "How would you describe the consistency of your stool when you have diarrhea?", "values": [{"label": "Watery", "value": "watery"}, {"label": "<PERSON><PERSON><PERSON>", "value": "mushy"}, {"label": "Soft but formed", "value": "soft"}, {"label": "Explosive or urgent", "value": "urgent"}], "tableView": true, "confirm_label": "Stool consistency during diarrhea:", "customConditional": "show = data.diarrhea_frequency", "optionsLabelPosition": "right"}, {"key": "diarrhea_urgency", "type": "radio", "input": true, "label": "Do you usually have to rush to the bathroom when you feel the need to go?", "values": [{"label": "Yes, it's very urgent", "value": "yes"}, {"label": "No, I can hold it", "value": "no"}, {"label": "Sometimes", "value": "sometimes"}], "tableView": true, "confirm_label": "Urgency with bowel movements:", "customConditional": "show = data.diarrhea_consistency && Object.keys(data.diarrhea_consistency).length > 0"}, {"key": "diarrhea_relief", "type": "selectboxes", "input": true, "label": "Does your diarrhea improve with any of the following?", "values": [{"label": "Avoiding certain foods", "value": "diet"}, {"label": "Reducing stress", "value": "stress"}, {"label": "Taking anti-diarrheal medication", "value": "medications"}, {"label": "No consistent relief", "value": "no_relief"}], "tableView": true, "confirm_label": "Things that help relieve diarrhea:", "customConditional": "show = data.diarrhea_urgency", "optionsLabelPosition": "right"}, {"key": "diarrhea_blood_mucus", "type": "selectboxes", "input": true, "label": "Have you noticed any of the following in your stool (poop)?", "values": [{"label": "Bright red blood", "value": "red_blood"}, {"label": "Dark or black stool", "value": "black_stool"}, {"label": "Mucus (clear or white stringy material)", "value": "mucus"}, {"label": "None of the above", "value": "none"}], "tableView": true, "confirm_label": "Blood or mucus in stool:", "customConditional": "show = data.diarrhea_relief && Object.keys(data.diarrhea_relief).length > 0", "optionsLabelPosition": "right"}, {"key": "diarrhea_additional_symptoms", "type": "selectboxes", "input": true, "label": "Do you also experience any of the following with your diarrhea?", "values": [{"label": "<PERSON><PERSON><PERSON>", "value": "nausea"}, {"label": "Vomiting", "value": "vomiting"}, {"label": "Low appetite", "value": "low_appetite"}, {"label": "Weight loss", "value": "weight_loss"}], "validate": {"custom": "valid = !!data.none_of_the_above_diarrhea_additional_symptoms || _.some(_.values(data.diarrhea_additional_symptoms));"}, "tableView": true, "confirm_label": "Other symptoms with diarrhea:", "customConditional": "show = data.diarrhea_blood_mucus", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_diarrhea_additional_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.diarrhea_blood_mucus"}, {"key": "heading_constipation", "html": "</br><h4 class='mb-n2'>Constipation</h4>", "type": "content", "input": false, "label": "Constipation Details", "tableView": false, "refreshOnChange": true, "customConditional": "show = data.celiac_symptoms?.constipation === true"}, {"key": "symptom_start_constipation", "data": {"custom": "values = [{label: `I've always had constipation and this isn't new for me`, value:`always`}].concat(data.durations)"}, "type": "select", "input": true, "label": "When did your constipation start?", "widget": "html5", "dataSrc": "custom", "validate": {"required": true}, "tableView": true, "confirm_label": "Constipation started:", "customConditional": "show = data.celiac_symptoms?.constipation === true && (_.sum(_.values(data.celiac_symptoms).map(Number)) < 2 || data.celiac_symptom_onset_pattern === 'separate_days')", "optionsLabelPosition": "right"}, {"key": "constipation_frequency", "type": "radio", "input": true, "label": "How often do you have a bowel movement (poop)?", "values": [{"label": "Every day", "value": "daily"}, {"label": "Every 2-3 days", "value": "every_2_3_days"}, {"label": "Once or twice per week", "value": "1_2_week"}, {"label": "Less than once per week", "value": "less_than_week"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Bowel movement frequency:", "customConditional": "show = data.celiac_symptoms?.constipation === true && (_.sum(_.values(data.celiac_symptoms).map(Number)) < 2 || data.celiac_symptom_onset_pattern === 'separate_days' || data.celiac_symptom_onset_pattern === 'same_time')"}, {"key": "constipation_stool_type", "type": "selectboxes", "input": true, "label": "What describes your usual stool (poop) when you're constipated?", "values": [{"label": "Very hard or pellet-like", "value": "hard"}, {"label": "Dry or cracked", "value": "dry"}, {"label": "Small and hard to pass", "value": "small"}, {"label": "Thin or stringy", "value": "thin"}], "tableView": true, "confirm_label": "Constipation stool type:", "customConditional": "show = data.constipation_frequency", "optionsLabelPosition": "right"}, {"key": "constipation_straining", "type": "radio", "input": true, "label": "Do you usually have to push or strain to pass stool (poop)?", "values": [{"label": "Yes, most of the time", "value": "yes_most"}, {"label": "Sometimes", "value": "sometimes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Straining with bowel movements:", "customConditional": "show = data.constipation_stool_type && Object.keys(data.constipation_stool_type).length > 0"}, {"key": "constipation_incomplete", "type": "radio", "input": true, "label": "Do you often feel like you haven't fully emptied your bowels after going?", "values": [{"label": "Yes", "value": "yes"}, {"label": "Sometimes", "value": "sometimes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Incomplete emptying feeling:", "customConditional": "show = data.constipation_straining"}, {"key": "constipation_impact_on_life", "type": "radio", "input": true, "label": "How much does constipation affect your daily life?", "values": [{"label": "Not at all", "value": "not_at_all"}, {"label": "Mildly bothersome", "value": "mild"}, {"label": "Moderate impact (affects mood or focus)", "value": "moderate"}, {"label": "Severe (affects sleep, work, or quality of life)", "value": "severe"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Impact of constipation:", "customConditional": "show = data.constipation_incomplete"}, {"key": "constipation_laxative_use", "type": "radio", "input": true, "label": "Do you use anything to help with constipation (including over-the-counter products)?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "Sometimes", "value": "sometimes"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Laxative or treatment use for constipation:", "customConditional": "show = data.constipation_impact_on_life"}, {"key": "constipation_laxative_effectiveness", "type": "radio", "input": true, "label": "Did you find that your constipation symptoms fully resolved after using these treatments?", "values": [{"label": "Yes, they fully resolved", "value": "yes"}, {"label": "No, the symptoms improved but did not fully go away", "value": "partial"}, {"label": "No, they did not improve at all", "value": "no"}, {"label": "I'm not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Effectiveness of constipation treatments:", "customConditional": "show = data.constipation_laxative_types && !data.constipation_laxative_types.none && _.some(_.omit(data.constipation_laxative_types, ['none', 'other']))"}, {"key": "recommendation_constipation_peg_fibre", "html": "<div style='border-left: 4px solid #198754; background-color: #d1e7dd; padding: 10px; margin-bottom: 10px;'><strong>Recommendation:</strong> Increasing dietary fibre — including vegetables, fruit skins, whole grains, chia, flax, or oats — can help manage constipation. You can also safely use a powder called <strong>polyethylene glycol</strong> (brand names include <strong>Restoralax</strong>) to soften stool and improve regularity. It draws water into the bowel and is not habit-forming. This product is available without a prescription at any pharmacy (e.g. Shoppers, Rexall, Costco, Walmart, or your local store), and it is <strong>safe to use long-term</strong> if needed. Most people start with <strong>1 scoop (17g)</strong> in a full glass of water daily. It may take 1-3 days to work. If needed, you can increase to 2-3 scoops per day unless you develop diarrhea.</div>", "type": "content", "input": false, "customConditional": "show = !!data.constipation_laxative_use"}, {"key": "recommendation_constipation_peg_fibre_acknowledgement", "type": "radio", "input": true, "label": "Do you understand this recommendation about fibre and Restoralax use for constipation?", "values": [{"label": "I understand", "value": "understand"}, {"label": "I do not understand", "value": "do_not_understand"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Understands PEG and fibre recommendation:", "customConditional": "show = !!data.constipation_laxative_use"}, {"key": "heading_abdominal_pain", "html": "<h4 class='mb-n2'>Abdominal Discomfort or Pain</h4>", "type": "content", "input": false, "label": "Pain Timing", "tableView": false, "refreshOnChange": true, "customConditional": "show = data.celiac_symptoms?.abdominal_pain === true"}, {"key": "symptom_start_abdominal_pain", "data": {"custom": "values = [{label: `I've always had abdominal pain and this isn't new for me`, value:`always`}].concat(data.durations)"}, "type": "select", "input": true, "label": "When did your abdominal pain start?", "widget": "html5", "dataSrc": "custom", "validate": {"required": true}, "tableView": true, "confirm_label": "Abdominal pain started:", "customConditional": "show = data.celiac_symptoms?.abdominal_pain === true && (_.sum(_.values(data.celiac_symptoms).map(Number)) < 2 || data.celiac_symptom_onset_pattern == 'separate_days')", "optionsLabelPosition": "right"}, {"key": "abdominal_pain_location", "type": "selectboxes", "input": true, "label": "Where do you feel the discomfort or pain? (Select all that apply)", "values": [{"label": "Upper abdomen (above belly button)", "value": "upper"}, {"label": "Lower abdomen (below belly button)", "value": "lower"}, {"label": "Left side", "value": "left"}, {"label": "Right side", "value": "right"}, {"label": "Around the belly button", "value": "periumbilical"}, {"label": "Everywhere / general abdominal discomfort", "value": "diffuse"}], "tableView": true, "confirm_label": "Pain location:", "customConditional": "show = data.celiac_symptoms?.abdominal_pain === true && (_.sum(_.values(data.celiac_symptoms).map(Number)) < 2 || data.celiac_symptom_onset_pattern === 'separate_days' || data.celiac_symptom_onset_pattern === 'same_time')", "optionsLabelPosition": "right"}, {"key": "abdominal_pain_quality", "type": "selectboxes", "input": true, "label": "What does the pain feel like? (Select all that apply)", "values": [{"label": "Cramping", "value": "cramping"}, {"label": "Sharp or stabbing", "value": "sharp"}, {"label": "A<PERSON>", "value": "aching"}, {"label": "Burning", "value": "burning"}, {"label": "Pressure or fullness", "value": "pressure"}, {"label": "Other", "value": "other"}], "tableView": true, "confirm_label": "Pain description:", "customConditional": "show = data.abdominal_pain_location && Object.keys(data.abdominal_pain_location).length > 0", "optionsLabelPosition": "right"}, {"key": "abdominal_pain_severity", "type": "radio", "input": true, "label": "How would you rate the severity of the pain?", "values": [{"label": "Mild (noticeable but not bothersome)", "value": "mild"}, {"label": "Moderate (bothersome or distracting)", "value": "moderate"}, {"label": "Severe (hard to concentrate, affects daily life)", "value": "severe"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Pain severity:", "customConditional": "show = data.abdominal_pain_quality && Object.keys(data.abdominal_pain_quality).length > 0"}, {"key": "abdominal_pain_pattern", "type": "radio", "input": true, "label": "Is the pain constant, or does it come and go?", "values": [{"label": "Comes and goes", "value": "intermittent"}, {"label": "Constant (always present)", "value": "constant"}, {"label": "Only after eating", "value": "after_eating"}, {"label": "Mostly in the morning or at night", "value": "time_of_day"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Pain pattern:", "customConditional": "show = data.abdominal_pain_severity"}, {"key": "abdominal_pain_relief", "type": "selectboxes", "input": true, "label": "What seems to help the pain go away or get better?", "values": [{"label": "Having a bowel movement (pooping)", "value": "bowel_movement"}, {"label": "Avoiding certain foods", "value": "avoiding_foods"}, {"label": "Resting or lying down", "value": "resting"}, {"label": "Taking pain or gas medications", "value": "medication"}, {"label": "Nothing really helps", "value": "nothing"}], "tableView": true, "confirm_label": "Things that relieve pain:", "customConditional": "show = data.abdominal_pain_pattern", "optionsLabelPosition": "right"}, {"key": "heading_fatigue", "html": "<h4 class='mb-n2'>Fatigue</h4>", "type": "content", "input": false, "label": "Fatigue Timing", "tableView": false, "refreshOnChange": true, "customConditional": "show = data.celiac_symptoms?.fatigue === true"}, {"key": "symptom_start_fatigue", "data": {"custom": "values = [{label: `I've always had fatigue and this isn't new for me`, value:`always`}].concat(data.durations)"}, "type": "select", "input": true, "label": "When did your fatigue start?", "widget": "html5", "dataSrc": "custom", "validate": {"required": true}, "tableView": true, "confirm_label": "Fatigue started:", "customConditional": "show = data.celiac_symptoms?.fatigue === true && (_.sum(_.values(data.celiac_symptoms).map(Number)) < 2 || data.celiac_symptom_onset_pattern == 'separate_days')", "optionsLabelPosition": "right"}, {"key": "fatigue_time_of_day", "type": "selectboxes", "input": true, "label": "When is your fatigue usually the worst?", "values": [{"label": "In the morning (even after sleeping)", "value": "morning"}, {"label": "Mid-day", "value": "midday"}, {"label": "Evening", "value": "evening"}, {"label": "All day long", "value": "all_day"}], "tableView": true, "confirm_label": "Fatigue is worst:", "customConditional": "show = data.celiac_symptoms?.fatigue === true && (_.sum(_.values(data.celiac_symptoms).map(Number)) < 2 || data.celiac_symptom_onset_pattern === 'same_time' || data.celiac_symptom_onset_pattern === 'separate_days')", "optionsLabelPosition": "right"}, {"key": "fatigue_frequency", "type": "radio", "input": true, "label": "How often do you feel unusually tired or low in energy?", "values": [{"label": "Every day", "value": "daily"}, {"label": "Most days", "value": "most_days"}, {"label": "A few times per week", "value": "few_per_week"}, {"label": "Occasionally", "value": "occasionally"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Fatigue frequency:", "customConditional": "show = data.fatigue_time_of_day && Object.keys(data.fatigue_time_of_day).length > 0"}, {"key": "fatigue_affects_function", "type": "radio", "input": true, "label": "Does your fatigue interfere with daily tasks, concentration, or activities?", "values": [{"label": "Yes", "value": "yes"}, {"label": "Somewhat", "value": "somewhat"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Fatigue impacts daily function:", "customConditional": "show = data.fatigue_frequency"}, {"key": "fatigue_daytime_sleepiness", "type": "radio", "input": true, "label": "Do you often feel sleepy or drowsy during the day?", "values": [{"label": "Yes, almost every day", "value": "yes"}, {"label": "Sometimes", "value": "sometimes"}, {"label": "No", "value": "no"}], "tableView": true, "confirm_label": "Daytime sleepiness:", "customConditional": "show = data.fatigue_affects_function && (data.fatigue_time_of_day?.morning || data.fatigue_time_of_day?.all_day)"}, {"key": "fatigue_osa_risk_factors", "type": "selectboxes", "input": true, "label": "Have you been told or noticed any of the following? (Check all that apply)", "values": [{"label": "Loud snoring", "value": "snoring"}, {"label": "Pauses in breathing during sleep", "value": "apnea"}, {"label": "Waking up gasping or choking", "value": "gasping"}, {"label": "Morning headaches", "value": "headaches"}, {"label": "Dry mouth on waking", "value": "dry_mouth"}], "validate": {"custom": "valid = !!data.none_of_the_above_fatigue_osa_risk_factors || _.some(_.values(data.fatigue_osa_risk_factors));"}, "tableView": true, "confirm_label": "Sleep-related symptoms:", "customConditional": "show = data.fatigue_daytime_sleepiness === 'yes' || data.fatigue_daytime_sleepiness === 'sometimes'", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_fatigue_osa_risk_factors", "type": "checkbox", "input": true, "label": "None of the above", "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.fatigue_daytime_sleepiness === 'yes' || data.fatigue_daytime_sleepiness === 'sometimes'"}, {"key": "fatigue_osa_evaluation", "type": "radio", "input": true, "label": "Have you ever had a sleep study or been tested for sleep apnea (OSA)?", "values": [{"label": "Yes - and diagnosed with sleep apnea", "value": "diagnosed"}, {"label": "Yes - but results were normal", "value": "normal"}, {"label": "No - never had a sleep study", "value": "never_tested"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Sleep apnea testing history:", "customConditional": "show = data.fatigue_osa_risk_factors && Object.keys(data.fatigue_osa_risk_factors).length > 0"}, {"key": "recommendation_sleep_study_osa", "html": "<div style='border-left: 4px solid #198754; background-color: #d1e7dd; padding: 10px; margin-bottom: 10px;'><strong>Recommendation:</strong> The symptoms you've selected may suggest a condition called <strong>obstructive sleep apnea (OSA)</strong>. We recommend speaking with <strong>your doctor or a local walk-in clinic</strong> about a <strong>sleep study</strong>, which is a test that checks your breathing, oxygen levels, and sleep quality overnight.<br><br>Sleep apnea can cause fatigue, poor concentration, headaches, and long-term health risks if left untreated. A sleep study can help determine if treatment or other options are needed to improve your sleep and energy levels.</div>", "type": "content", "input": false, "customConditional": "show = data.fatigue_osa_evaluation === 'never_tested'"}, {"key": "recommendation_sleep_study_osa_acknowledgement", "type": "radio", "input": true, "label": "Do you understand this recommendation to speak with your doctor or local walk-in clinic about a sleep study?", "values": [{"label": "I understand", "value": "understand"}, {"label": "I do not understand", "value": "do_not_understand"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Understands sleep study recommendation:", "customConditional": "show = data.fatigue_osa_evaluation === 'never_tested'"}, {"key": "heading_weight_loss", "html": "</br><h4 class='mb-n2'>Unintentional Weight Loss</h4>", "type": "content", "input": false, "label": "Weight Loss Timing", "tableView": false, "refreshOnChange": true, "customConditional": "show = data.celiac_symptoms?.weight_loss === true"}, {"key": "symptom_start_weight_loss", "data": {"custom": "values = [{label: `I've always had low weight and this isn't new for me`, value:`always`}].concat(data.durations)"}, "type": "select", "input": true, "label": "When did your unintentional weight loss start?", "widget": "html5", "dataSrc": "custom", "validate": {"required": true}, "tableView": true, "confirm_label": "Weight loss started:", "customConditional": "show = data.celiac_symptoms?.weight_loss === true && (_.sum(_.values(data.celiac_symptoms).map(Number)) < 2 || data.celiac_symptom_onset_pattern == 'separate_days')", "optionsLabelPosition": "right"}, {"key": "weight_loss_amount", "type": "radio", "input": true, "label": "Roughly how much weight have you lost unintentionally?", "values": [{"label": "Less than 5 pounds", "value": "under_5"}, {"label": "5-10 pounds", "value": "5_10"}, {"label": "10-20 pounds", "value": "10_20"}, {"label": "More than 20 pounds", "value": "over_20"}, {"label": "I'm not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Approximate weight lost:", "customConditional": "show = data.celiac_symptoms?.weight_loss === true && (_.sum(_.values(data.celiac_symptoms).map(Number)) < 2 || data.celiac_symptom_onset_pattern === 'separate_days' || data.celiac_symptom_onset_pattern === 'same_time')"}, {"key": "weight_loss_intentional", "type": "radio", "input": true, "label": "Were you trying to lose weight during this time?", "values": [{"label": "Yes, I was actively trying to lose weight", "value": "intentional"}, {"label": "No, it was unintentional", "value": "unintentional"}, {"label": "It started unintentionally but I continued with it", "value": "mixed"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Intentional vs unintentional weight loss:", "customConditional": "show = data.weight_loss_amount"}, {"key": "weight_loss_appetite_change", "type": "radio", "input": true, "label": "Has your appetite or eating habits changed during this time?", "values": [{"label": "Yes, I have less appetite", "value": "less_appetite"}, {"label": "Yes, I eat less but not sure why", "value": "eating_less"}, {"label": "No change in appetite", "value": "no_change"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Change in appetite:", "customConditional": "show = data.weight_loss_intentional"}, {"key": "weight_loss_other_symptoms", "type": "selectboxes", "input": true, "label": "Have you had any of the following along with weight loss?", "values": [{"label": "Fatigue or low energy", "value": "fatigue"}, {"label": "Frequent diarrhea or loose stool", "value": "diarrhea"}, {"label": "Ongoing nausea", "value": "nausea"}, {"label": "Night sweats or fevers", "value": "systemic"}, {"label": "No other symptoms", "value": "none"}], "tableView": true, "confirm_label": "Other symptoms present with weight loss:", "customConditional": "show = data.weight_loss_appetite_change", "optionsLabelPosition": "right"}, {"key": "weight_loss_urgent_assessment_warning", "html": "<div style='border-left: 4px solid #dc3545; background-color: #f8d7da; padding: 10px; margin-bottom: 10px;'><strong>Important:</strong> Losing weight without trying can be a sign of a serious underlying condition. This symptom requires timely medical evaluation. We recommend booking an <strong>in-person medical assessment as soon as possible</strong>. A doctor can examine you and may order blood work, imaging, or other tests to investigate further. Please do not delay seeking care.</div>", "type": "content", "input": false, "customConditional": "show = data.weight_loss_other_symptoms && _.some(data.weight_loss_other_symptoms, Boolean)"}, {"key": "weight_loss_red_flag_acknowledgement", "type": "radio", "input": true, "label": "Do you understand this recommendation about the importance of in-person assessment for unexplained weight loss?", "values": [{"label": "I understand", "value": "understand"}, {"label": "I do not understand", "value": "do_not_understand"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Understands red flag weight loss recommendation:", "customConditional": "show = data.weight_loss_other_symptoms && _.some(data.weight_loss_other_symptoms, Boolean)"}, {"key": "heading_skin_changes", "html": "</br><h4 class='mb-n2'>Skin Changes</h4>", "type": "content", "input": false, "tableView": false, "customConditional": "show = data.celiac_symptoms?.skin_changes === true"}, {"key": "skin_changes_location", "type": "selectboxes", "input": true, "label": "Where have you noticed itchy skin changes or a rash? (Select all that apply)", "values": [{"label": "Back of elbows", "value": "back_elbows"}, {"label": "Back of knees", "value": "back_knees"}, {"label": "Buttocks", "value": "buttocks"}, {"label": "Scalp or hairline", "value": "scalp"}, {"label": "Shoulders or upper back", "value": "shoulders"}, {"label": "Other areas", "value": "other"}], "tableView": true, "confirm_label": "Locations of skin changes:", "customConditional": "show = data.celiac_symptoms?.skin_changes === true"}, {"key": "none_of_the_above_skin_changes_location", "type": "checkbox", "input": true, "label": "None of the above", "validate": {"custom": "valid = !!data.none_of_the_above_skin_changes_location || _.some(_.values(data.skin_changes_location));"}, "tableView": true, "customClass": "mt-n3", "customConditional": "show = data.celiac_symptoms?.skin_changes === true"}, {"key": "skin_changes_characteristics", "type": "selectboxes", "input": true, "label": "How would you describe the rash or skin change? (Select all that apply)", "values": [{"label": "Intensely itchy", "value": "itchy"}, {"label": "Blistering or fluid-filled bumps", "value": "blisters"}, {"label": "Red or raised patches", "value": "raised"}, {"label": "Scabs or broken skin from scratching", "value": "scabs"}, {"label": "Other", "value": "other"}], "tableView": true, "confirm_label": "How the rash looks or feels:", "customConditional": "show = data.celiac_symptoms?.skin_changes === true"}, {"key": "skin_changes_duration", "data": {"values": [{"label": "Less than 1 week", "value": "Less than 1 week"}, {"label": "1-4 weeks", "value": "1-4 weeks"}, {"label": "1-3 months", "value": "1-3 months"}, {"label": "4-6 months", "value": "4-6 months"}, {"label": "6-12 months", "value": "6-12 months"}, {"label": "Over 1 year", "value": "Over 1 year"}, {"label": "Over 2 years", "value": "Over 2 years"}, {"label": "Comes and goes every few months", "value": "Comes and goes every few months"}, {"label": "Comes and goes every year", "value": "Comes and goes every year"}, {"label": "I've had this off and on for most of my life", "value": "I've had this off and on for most of my life"}, {"label": "I don't remember", "value": "I don't remember"}]}, "type": "select", "input": true, "label": "How long have you been experiencing these skin symptoms?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Duration of skin symptoms:", "customConditional": "show = data.celiac_symptoms?.skin_changes === true"}, {"key": "dh_symmetry_pattern", "type": "radio", "input": true, "label": "Is the rash or itchiness on both sides of your body in the same places (symmetrical)?", "values": [{"label": "Yes - it affects both sides equally", "value": "symmetrical"}, {"label": "No - it's only on one side or scattered", "value": "asymmetrical"}, {"label": "Not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Rash symmetry:", "customConditional": "show = data.celiac_symptoms?.skin_changes === true"}, {"key": "dh_rash_behavior", "type": "radio", "input": true, "label": "What happens to the rash over time?", "values": [{"label": "It starts with intense itching before any rash appears", "value": "itch_precedes_rash"}, {"label": "It blisters and then scabs over", "value": "blister_scab"}, {"label": "It stays red and itchy without blisters", "value": "red_itchy"}, {"label": "I'm not sure how it changes over time", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Rash behaviour over time:", "customConditional": "show = data.celiac_symptoms?.skin_changes === true"}, {"key": "dh_rash_chronicity", "type": "radio", "input": true, "label": "Does the rash go away completely and then return in the same spots?", "values": [{"label": "Yes - it comes and goes in the same areas", "value": "recurrent"}, {"label": "No - it stays in the same area without clearing", "value": "persistent"}, {"label": "No - it clears and doesn't return", "value": "resolved"}, {"label": "I'm not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Chronicity of rash:", "customConditional": "show = data.celiac_symptoms?.skin_changes === true"}, {"key": "dh_gluten_rash_timing", "type": "radio", "input": true, "label": "Have you noticed this rash appear after eating gluten-containing foods (e.g. bread, pasta)?", "values": [{"label": "Yes - it seems worse after eating gluten", "value": "gluten_triggered"}, {"label": "No connection to food", "value": "not_food_related"}, {"label": "I've never noticed a pattern", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Rash triggered by gluten?", "customConditional": "show = data.celiac_symptoms?.skin_changes === true"}, {"key": "dh_skin_biopsy", "type": "radio", "input": true, "label": "Have you ever had a skin biopsy (small sample taken) to check what caused the rash?", "values": [{"label": "Yes - and it confirmed dermatitis herpetiformis", "value": "confirmed"}, {"label": "Yes - but I don't remember the result", "value": "unknown_result"}, {"label": "No, I've never had a skin biopsy", "value": "never"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Skin biopsy done:", "customConditional": "show = data.celiac_symptoms?.skin_changes === true"}, {"key": "dh_skin_diagnosis_differentials", "type": "selectboxes", "input": true, "label": "Have you ever been told you have any of the following skin conditions?", "values": [{"label": "Eczema (atopic dermatitis)", "value": "eczema"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "value": "psoriasis"}, {"label": "Seborrheic dermatitis (flaky or greasy scalp/skin)", "value": "seb_derm"}, {"label": "A different skin diagnosis was made", "value": "other"}], "validate": {"custom": "valid = !!data.none_of_the_above_dh_skin_diagnosis_differentials || _.some(_.values(data.dh_skin_diagnosis_differentials));"}, "tableView": true, "confirm_label": "Prior skin conditions you've been told you have:", "customConditional": "show = data.celiac_symptoms?.skin_changes === true", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_dh_skin_diagnosis_differentials", "type": "checkbox", "input": true, "label": "None of the above", "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.celiac_symptoms?.skin_changes === true"}, {"key": "dh_skin_diagnosis_differentials_other", "type": "textfield", "input": true, "label": "Please specify the other skin condition you were diagnosed with:", "tableView": true, "customConditional": "show = data.dh_skin_diagnosis_differentials?.other === true"}, {"key": "recommendation_nonceliac_skin_diagnosis", "html": "<div style='border-left: 4px solid #198754; background-color: #d1e7dd; padding: 10px; margin-bottom: 10px;'><strong>Note:</strong> If celiac testing is negative, it's still very important to have a doctor examine your skin in person. <br><br>There are many skin conditions that can cause itching, rashes, or blisters that are not caused by gluten. These include <strong>eczema</strong>, <strong>psoriasis</strong>, <strong>seborrheic dermatitis</strong>, and others. <br><br>Each of these has its own treatment plan, and some require prescription creams or other medications. An in-person skin exam or referral to a dermatologist is the best way to find the right treatment for you.</div>", "type": "content", "input": false, "customConditional": "show = data.dh_skin_diagnosis_differentials || data.none_of_the_above_dh_skin_diagnosis_differentials === true"}, {"key": "recommendation_nonceliac_skin_diagnosis_understanding", "type": "radio", "input": true, "label": "Do you understand this recommendation about the importance of an in-person skin evaluation and individualized treatment?", "values": [{"label": "I understand", "value": "understand"}, {"label": "I do not understand", "value": "do_not_understand"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Understands non-celiac skin diagnosis recommendation:", "customConditional": "show = data.dh_skin_diagnosis_differentials || data.none_of_the_above_dh_skin_diagnosis_differentials === true"}, {"key": "photo_upload_header", "html": "<h4>Optional Photo Upload</h4><p>If you feel comfortable, you may upload a photo of your skin rash to help us better understand your symptoms. This is optional.</p>", "type": "content", "input": false, "label": "Content", "customConditional": "show = data.celiac_symptoms?.skin_changes === true"}, {"key": "upload_skin_photo", "url": "/app/q/{qpk}/formio-files/{pk}/", "type": "file", "image": true, "input": true, "label": "Upload a photo of your skin symptoms (optional)", "webcam": true, "capture": "user", "storage": "url", "multiple": true, "fileTypes": [{"label": "", "value": ""}], "tableView": true, "customConditional": "show = data.celiac_symptoms?.skin_changes === true", "validateWhenHidden": false}, {"key": "heading_other_general_symptoms", "html": "</br><h4>Other Symptoms</h4>", "type": "content", "input": false, "tableView": false}, {"key": "general_red_flag_like_symptoms", "type": "selectboxes", "input": true, "label": "Have you experienced any of the following symptoms recently?", "values": [{"label": "Vomiting", "value": "vomiting"}, {"label": "<PERSON><PERSON><PERSON>", "value": "nausea"}, {"label": "Chills or shaking", "value": "chills"}, {"label": "Fever (temperature above 38°C)", "value": "fever"}, {"label": "Dizziness or lightheadedness", "value": "dizziness"}], "validate": {"custom": "valid = !!data.none_of_the_above_general_red_flag_like_symptoms || _.some(_.values(data.general_red_flag_like_symptoms));"}, "tableView": true, "confirm_label": "Other symptoms you've experienced:", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_general_red_flag_like_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "general_red_flag_like_symptoms_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following symptoms:", "hidden": true, "disabled": true, "tableView": true, "clearOnHide": false, "confirm_label": "You do NOT have the following symptoms:", "calculateValue": "value = _.replace(_.join(_.map(_.keys(_.pickBy(data.general_red_flag_like_symptoms, _.negate(_.identity))), _.startCase), ', '), /_/g, ' ');"}, {"key": "heading_symptom_triggers", "html": "</br><h4>Possible Triggers</h4>", "type": "content", "input": false, "tableView": false, "customConditional": "show = data.celiac_symptoms && _.some(_.values(data.celiac_symptoms)) && !data.none_of_the_above_celiac_symptoms"}, {"key": "dairy_trigger_reaction", "type": "radio", "input": true, "label": "Have you noticed that your symptoms get worse after eating or drinking dairy (milk, cheese, yogurt)?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "I'm not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Dairy worsens symptoms:", "customConditional": "show = data.celiac_symptoms && _.some(_.values(data.celiac_symptoms)) && !data.none_of_the_above_celiac_symptoms", "optionsLabelPosition": "right"}, {"key": "recommendation_lactase_enzyme", "html": "<div style='border-left: 4px solid #198754; background-color: #d1e7dd; padding: 10px; margin-bottom: 10px;'><strong>Recommendation:</strong> If you find that dairy worsens your symptoms, you may wish to try an over-the-counter enzyme called <strong>lactase</strong> (e.g. Lac<PERSON>d). This can be taken with meals that contain dairy to help digest lactose. If you notice your symptoms improve when using lactase, this may suggest you are lactose intolerant. Many people use lactase enzymes when they plan to eat dairy, or in situations where accidental exposure might occur. This approach may allow you to enjoy dairy with fewer symptoms.</div>", "type": "content", "input": false, "customConditional": "show = data.dairy_trigger_reaction === 'yes'"}, {"key": "recommendation_lactase_enzyme_acknowledgement", "type": "radio", "input": true, "label": "Do you understand this recommendation about trying lactase enzymes with dairy-containing meals?", "values": [{"label": "I understand", "value": "understand"}, {"label": "I do not understand", "value": "do_not_understand"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Understands lactase enzyme recommendation:", "customConditional": "show = data.dairy_trigger_reaction === 'yes'"}, {"key": "gluten_trigger_reaction", "type": "radio", "input": true, "label": "Have you noticed that your symptoms get worse after eating foods with gluten (bread, pasta, crackers, baked goods)?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}, {"label": "I'm not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Symptoms after eating gluten:", "customConditional": "show = data.celiac_symptoms && _.some(_.values(data.celiac_symptoms)) && !data.none_of_the_above_celiac_symptoms", "optionsLabelPosition": "right"}, {"key": "other_common_triggers", "type": "selectboxes", "input": true, "label": "Do you notice any of these factors worsen your symptoms?", "values": [{"label": "Stress or anxiety", "value": "stress"}, {"label": "Large meals", "value": "large_meals"}, {"label": "Fatty or fried foods", "value": "fatty_foods"}, {"label": "Caffeine or alcohol", "value": "caffeine_alcohol"}, {"label": "Carbonated drinks (pop, sparkling water)", "value": "carbonation"}, {"label": "None of the above", "value": "none"}], "tableView": true, "confirm_label": "Other things that worsen symptoms:", "customConditional": "show = data.celiac_symptoms && _.some(_.values(data.celiac_symptoms)) && !data.none_of_the_above_celiac_symptoms", "optionsLabelPosition": "right"}, {"key": "heading_family_history_details", "html": "</br><h4>Family History of Celiac Disease</h4>", "type": "content", "input": false, "tableView": false, "customConditional": "show = data.reason_for_screening?.family_history"}, {"key": "family_celiac_confirmed_or_suspected", "type": "radio", "input": true, "label": "Was your family member formally diagnosed with celiac disease by a doctor, or do they just suspect it?", "values": [{"label": "Yes, they were diagnosed by a doctor", "value": "confirmed"}, {"label": "No, they just suspect they have it", "value": "suspected"}, {"label": "I'm not sure", "value": "unknown"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Celiac diagnosis status in family:", "customConditional": "show = data.reason_for_screening?.family_history"}, {"key": "family_member_celiac_diagnosed", "type": "selectboxes", "input": true, "label": "Who in your family has (or may have) celiac disease?", "values": [{"label": "Parent", "value": "parent"}, {"label": "Sibling", "value": "sibling"}, {"label": "Child", "value": "child"}, {"label": "Grandparent", "value": "grandparent"}, {"label": "Aunt or uncle", "value": "aunt_uncle"}, {"label": "Cousin", "value": "cousin"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Family member with possible or confirmed celiac:", "customConditional": "show = data.reason_for_screening?.family_history"}, {"key": "age_family_celiac_diagnosed", "data": {"values": [{"label": "Under 10 years old", "value": "under_10"}, {"label": "10-19 years old", "value": "10_19"}, {"label": "20-39 years old", "value": "20_39"}, {"label": "40-59 years old", "value": "40_59"}, {"label": "60+ years old", "value": "60_plus"}, {"label": "I don't know", "value": "unknown"}]}, "type": "select", "input": true, "label": "Do you know how old they were when they were diagnosed?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Age when family member was diagnosed:", "customConditional": "show = data.reason_for_screening?.family_history"}, {"key": "family_history_other_ibd", "type": "selectboxes", "input": true, "label": "Does anyone in your family have any of these other digestive or autoimmune conditions?", "values": [{"label": "<PERSON><PERSON><PERSON>'s disease", "value": "crohns"}, {"label": "Ulcerative colitis", "value": "uc"}, {"label": "<PERSON><PERSON>", "value": "lupus"}, {"label": "Rheumatoid arthritis", "value": "ra"}, {"label": "Type 1 diabetes", "value": "type1"}], "tableView": true, "confirm_label": "Other autoimmune or bowel conditions in family:", "customConditional": "show = data.reason_for_screening?.family_history", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_family_history_other_ibd", "type": "checkbox", "input": true, "label": "None of the above", "validate": {"custom": "valid = !!data.none_of_the_above_family_history_other_ibd || _.some(_.values(data.family_history_other_ibd));"}, "tableView": true, "customClass": "mt-n3", "customConditional": "show = data.reason_for_screening?.family_history"}, {"key": "heading_colon_cancer_and_bleeding", "html": "</br><h4>Colon Cancer Risk and Rectal Bleeding</h4>", "type": "content", "input": false, "tableView": false}, {"key": "family_history_colon_cancer", "type": "selectboxes", "input": true, "label": "Do you have a family history of colon cancer?", "values": [{"label": "Parent", "value": "parent"}, {"label": "Sibling", "value": "sibling"}, {"label": "Grandparent", "value": "grandparent"}, {"label": "Aunt or uncle", "value": "aunt_uncle"}, {"label": "Cousin", "value": "cousin"}], "validate": {"custom": "valid = !!data.none_of_the_above_family_history_colon_cancer || _.some(_.values(data.family_history_colon_cancer));"}, "tableView": true, "confirm_label": "Family history of colon cancer:", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_family_history_colon_cancer", "type": "checkbox", "input": true, "label": "I don't have any family members that I know of with colon cancer", "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "age_youngest_colon_cancer_diagnosis", "data": {"values": [{"label": "Under 40", "value": "under_40"}, {"label": "40-49", "value": "40_49"}, {"label": "50-59", "value": "50_59"}, {"label": "60 or older", "value": "60_plus"}, {"label": "I don't know", "value": "unknown"}]}, "type": "select", "input": true, "label": "How old was the youngest family member when they were diagnosed with colon cancer?", "widget": "html5", "validate": {"required": true}, "confirm_label": "Youngest age at family diagnosis:", "customConditional": "show = _.some(data.family_history_colon_cancer, Boolean)"}, {"key": "recommendation_colonoscopy_early_due_to_family_history", "html": "<div style='border-left: 4px solid #198754; background-color: #d1e7dd; padding: 10px; margin-bottom: 10px;'><strong>Recommendation:</strong> If you have a <strong>first-degree relative</strong> (parent or sibling) diagnosed with colon cancer, screening guidelines recommend you begin colonoscopy <strong>10 years before their age at diagnosis</strong>. For example, if your mother was diagnosed at age 52, you should aim to have your first colonoscopy by age 42. This helps catch changes earlier and may significantly reduce risk. If you're not sure when your relative was diagnosed, speak with your doctor to discuss the best time to begin screening.</div>", "type": "content", "input": false, "customConditional": "show = data.family_history_colon_cancer?.parent === true || data.family_history_colon_cancer?.sibling === true"}, {"key": "recommendation_colonoscopy_early_acknowledgement", "type": "radio", "input": true, "label": "Do you understand this recommendation about starting colonoscopy earlier due to family history?", "values": [{"label": "I understand", "value": "understand"}, {"label": "I do not understand", "value": "do_not_understand"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Understands early colonoscopy recommendation:", "customConditional": "show = data.family_history_colon_cancer?.parent === true || data.family_history_colon_cancer?.sibling === true"}, {"key": "rectal_bleeding_symptoms", "type": "selectboxes", "input": true, "label": "Have you noticed any of the following rectal bleeding symptoms?", "values": [{"label": "Bright red blood on toilet paper", "value": "toilet_paper"}, {"label": "Blood mixed with stool", "value": "mixed_with_stool"}, {"label": "Dark or tarry stool", "value": "dark_stool"}, {"label": "Blood dripping into the toilet", "value": "blood_dripping"}], "validate": {"custom": "valid = !!data.none_of_the_above_rectal_bleeding_symptoms || _.some(data.rectal_bleeding_symptoms, <PERSON><PERSON><PERSON>);"}, "tableView": true, "confirm_label": "Rectal bleeding symptoms:", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_rectal_bleeding_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "rectal_bleeding_prior_diagnosis", "type": "selectboxes", "input": true, "label": "Have you ever been given a diagnosis to explain your rectal bleeding?", "values": [{"label": "Hemorrhoids", "value": "hemorrhoids"}, {"label": "Anal fissure", "value": "fissure"}, {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "diverticulosis"}, {"label": "Inflammatory bowel disease (e.g. <PERSON><PERSON>'s, ulcerative colitis)", "value": "ibd"}, {"label": "Other", "value": "other"}, {"label": "No diagnosis was made", "value": "none"}], "tableView": true, "confirm_label": "Prior diagnosis for bleeding:", "customConditional": "show = data.rectal_bleeding_symptoms && _.some(data.rectal_bleeding_symptoms, Boolean)", "optionsLabelPosition": "right"}, {"key": "rectal_bleeding_first_onset", "data": {"values": [{"label": "Less than 1 week ago", "value": "under_1_week"}, {"label": "1-4 weeks ago", "value": "1_4_weeks"}, {"label": "1-3 months ago", "value": "1_3_months"}, {"label": "4-12 months ago", "value": "4_12_months"}, {"label": "Over 1 year ago (it comes and goes)", "value": "over_1_year_intermittent"}, {"label": "Over 1 year ago (it's been continuous)", "value": "over_1_year_continuous"}, {"label": "I don't remember", "value": "unknown"}]}, "type": "select", "input": true, "label": "When did you first notice rectal bleeding?", "widget": "html5", "validate": {"required": true}, "confirm_label": "Bleeding started:", "customConditional": "show = data.rectal_bleeding_symptoms && _.some(data.rectal_bleeding_symptoms, Boolean)"}, {"key": "rectal_bleeding_frequency", "type": "radio", "input": true, "label": "How often do you experience rectal bleeding?", "values": [{"label": "Only once", "value": "once"}, {"label": "Occasionally (every few weeks)", "value": "occasional"}, {"label": "Frequently (weekly or more)", "value": "frequent"}, {"label": "Constant or near daily", "value": "constant"}], "validate": {"required": true}, "confirm_label": "Bleeding frequency:", "customConditional": "show = data.rectal_bleeding_symptoms && _.some(data.rectal_bleeding_symptoms, Boolean)"}, {"key": "heading_prior_investigations", "html": "</br><h4>Previous Investigations</h4>", "type": "content", "input": false, "tableView": false}, {"key": "prior_gi_testing", "type": "selectboxes", "input": true, "label": "Have you had any of the following tests have you had in the past?", "values": [{"label": "Ultrasound", "value": "ultrasound"}, {"label": "CT scan", "value": "ct"}, {"label": "Endoscopy", "value": "endoscopy"}, {"label": "Colonoscopy", "value": "colonoscopy"}], "validate": {"custom": "valid = !!data.none_of_the_above_prior_gi_testing || _.some(_.values(data.prior_gi_testing));"}, "tableView": true, "confirm_label": "Tests you've had before:", "optionsLabelPosition": "right"}, {"key": "none_of_the_above_prior_gi_testing", "type": "checkbox", "input": true, "label": "None of the above", "tableView": true, "customClass": "mt-n3", "defaultValue": false}, {"key": "heading_ultrasound", "html": "<h4 class='mb-n2'>Abdominal Ultrasound</h4>", "type": "content", "input": false, "tableView": false, "customConditional": "show = data.prior_gi_testing?.ultrasound"}, {"key": "prior_ultrasound_date", "data": {"values": [{"label": "Within the past 6 months", "value": "0_6_months"}, {"label": "6-12 months ago", "value": "6_12_months"}, {"label": "1-2 years ago", "value": "1_2_years"}, {"label": "More than 2 years ago", "value": "2_plus_years"}, {"label": "I don't remember", "value": "unknown"}]}, "type": "select", "input": true, "label": "When was your last abdominal ultrasound?", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.prior_gi_testing?.ultrasound"}, {"key": "ultrasound_findings", "type": "selectboxes", "input": true, "label": "Do you remember if any of the following were found on your ultrasound?", "values": [{"label": "Fatty liver", "value": "fatty_liver"}, {"label": "Gallstones", "value": "gallstones"}, {"label": "Liver cysts", "value": "liver_cysts"}, {"label": "Everything was normal", "value": "normal"}, {"label": "I don't remember", "value": "unknown"}, {"label": "Other", "value": "other"}], "tableView": true, "confirm_label": "Ultrasound findings:", "customConditional": "show = data.prior_ultrasound_date", "optionsLabelPosition": "right"}, {"key": "ultrasound_findings_other", "type": "textfield", "input": true, "label": "Please describe what else was seen (if not listed above):", "customConditional": "show = data.ultrasound_findings?.other === true"}, {"key": "none_of_the_above_ultrasound_findings", "type": "checkbox", "input": true, "label": "None of the above", "validate": {"custom": "valid = !!data.none_of_the_above_ultrasound_findings || _.some(_.values(data.ultrasound_findings));"}, "tableView": true, "customClass": "mt-n3", "customConditional": "show = data.prior_ultrasound_date"}, {"key": "heading_ct", "html": "</br><h4 class='mb-n2'>CT <PERSON>an</h4>", "type": "content", "input": false, "tableView": false, "customConditional": "show = data.prior_gi_testing?.ct"}, {"key": "prior_ct_date", "data": {"values": [{"label": "Within the past 6 months", "value": "0_6_months"}, {"label": "6-12 months ago", "value": "6_12_months"}, {"label": "1-2 years ago", "value": "1_2_years"}, {"label": "More than 2 years ago", "value": "2_plus_years"}, {"label": "I don't remember", "value": "unknown"}]}, "type": "select", "input": true, "label": "When was your last CT scan?", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.prior_gi_testing?.ct"}, {"key": "ct_findings", "type": "selectboxes", "input": true, "label": "Do you remember if any of the following were found on your CT scan?", "values": [{"label": "Inflammation in the bowel", "value": "inflammation"}, {"label": "Enlarged lymph nodes", "value": "lymph_nodes"}, {"label": "Fatty liver", "value": "fatty_liver"}, {"label": "Everything was normal", "value": "normal"}, {"label": "I don't remember", "value": "unknown"}, {"label": "Other", "value": "other"}], "tableView": true, "confirm_label": "CT scan findings:", "customConditional": "show = data.prior_ct_date", "optionsLabelPosition": "right"}, {"key": "ct_findings_other", "type": "textfield", "input": true, "label": "Please describe what else was seen (if not listed above):", "customConditional": "show = data.ct_findings?.other === true"}, {"key": "none_of_the_above_ct_findings", "type": "checkbox", "input": true, "label": "None of the above", "validate": {"custom": "valid = !!data.none_of_the_above_ct_findings || _.some(_.values(data.ct_findings));"}, "tableView": true, "customClass": "mt-n3", "customConditional": "show = data.prior_ct_date"}, {"key": "heading_endoscopy", "html": "</br><h4 class='mb-n2'>Endoscopy</h4>", "type": "content", "input": false, "tableView": false, "customConditional": "show = data.prior_gi_testing?.endoscopy"}, {"key": "prior_endoscopy_date", "data": {"values": [{"label": "Within the past 6 months", "value": "0_6_months"}, {"label": "6-12 months ago", "value": "6_12_months"}, {"label": "1-2 years ago", "value": "1_2_years"}, {"label": "More than 2 years ago", "value": "2_plus_years"}, {"label": "I don't remember", "value": "unknown"}]}, "type": "select", "input": true, "label": "When was your last endoscopy (camera to look at your throat and stomach)?", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.prior_gi_testing?.endoscopy"}, {"key": "endoscopy_findings", "type": "selectboxes", "input": true, "label": "Do you remember if any of the following were found on your endoscopy?", "values": [{"label": "Gastritis", "value": "gastritis"}, {"label": "Esophagitis", "value": "esophagitis"}, {"label": "Duodenitis", "value": "duodenitis"}, {"label": "Biopsies taken", "value": "biopsy"}, {"label": "Everything was normal", "value": "normal"}, {"label": "I don't remember", "value": "unknown"}, {"label": "Other", "value": "other"}], "tableView": true, "confirm_label": "Endoscopy findings:", "customConditional": "show = data.prior_endoscopy_date", "optionsLabelPosition": "right"}, {"key": "endoscopy_findings_other", "type": "textfield", "input": true, "label": "Please describe what else was seen (if not listed above):", "customConditional": "show = data.endoscopy_findings?.other === true"}, {"key": "none_of_the_above_endoscopy_findings", "type": "checkbox", "input": true, "label": "None of the above", "validate": {"custom": "valid = !!data.none_of_the_above_endoscopy_findings || _.some(_.values(data.endoscopy_findings));"}, "tableView": true, "customClass": "mt-n3", "customConditional": "show = data.prior_endoscopy_date"}, {"key": "endoscopy_doctor_reassurance", "type": "radio", "input": true, "label": "Did your doctor mention that they were not concerned about celiac disease based on the endoscopy?", "values": [{"label": "Yes, they said it didn't look like celiac", "value": "reassured"}, {"label": "No, they didn't mention celiac", "value": "not_discussed"}, {"label": "I don't remember", "value": "dont_remember"}], "tableView": true, "confirm_label": "<PERSON><PERSON> mentioned at endoscopy?", "customConditional": "show = data.prior_gi_testing?.endoscopy"}, {"key": "heading_colonoscopy", "html": "</br><h4 class='mb-n2'>Colonoscopy</h4>", "type": "content", "input": false, "tableView": false, "customConditional": "show = data.prior_gi_testing?.colonoscopy"}, {"key": "prior_colonoscopy_date", "data": {"values": [{"label": "Within the past 6 months", "value": "0_6_months"}, {"label": "6-12 months ago", "value": "6_12_months"}, {"label": "1-2 years ago", "value": "1_2_years"}, {"label": "More than 2 years ago", "value": "2_plus_years"}, {"label": "I don't remember", "value": "unknown"}]}, "type": "select", "input": true, "label": "When was your last colonoscopy (camera to look at your bowel)?", "widget": "html5", "validate": {"required": true}, "tableView": true, "customConditional": "show = data.prior_gi_testing?.colonoscopy"}, {"key": "colonoscopy_findings", "type": "selectboxes", "input": true, "label": "Do you remember if any of the following were found on your colonoscopy?", "values": [{"label": "Polyps", "value": "polyps"}, {"label": "Colitis", "value": "colitis"}, {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "diverticulosis"}, {"label": "Hemorrhoids", "value": "hemorrhoids"}, {"label": "Everything was normal", "value": "normal"}, {"label": "I don't remember", "value": "unknown"}, {"label": "Other", "value": "other"}], "tableView": true, "confirm_label": "Colonoscopy findings:", "customConditional": "show = data.prior_colonoscopy_date", "optionsLabelPosition": "right"}, {"key": "colonoscopy_findings_other", "type": "textfield", "input": true, "label": "Please describe what else was seen (if not listed above):", "customConditional": "show = data.colonoscopy_findings?.other === true"}, {"key": "none_of_the_above_colonoscopy_findings", "type": "checkbox", "input": true, "label": "None of the above", "validate": {"custom": "valid = !!data.none_of_the_above_colonoscopy_findings || _.some(_.values(data.colonoscopy_findings));"}, "tableView": true, "customClass": "mt-n3", "customConditional": "show = data.prior_colonoscopy_date"}, {"key": "header_prior_tests", "html": "</br><h2>Prior Lab Testing</h2>", "type": "content", "input": false, "label": "Content", "tableView": false, "refreshOnChange": false}, {"key": "prior_tests_completed", "type": "selectboxes", "input": true, "label": "Have you had any of the following tests completed <strong>after</strong> your symptoms started?", "values": [{"label": "Kidney function (eGFR)", "value": "egfr"}, {"label": "Lipid Profile (Cholesterol levels)", "value": "lipid_profile"}, {"label": "Diabetes Testing (HbA1c)", "value": "a1c"}, {"label": "Fasting Blood Glucose (FBG)", "value": "fasting_glucose"}, {"label": "CBC (Complete Blood Count)", "value": "cbc"}, {"label": "Vitamin B12", "value": "b12"}, {"label": "TSH (Thyroid Stimulating Hormone)", "value": "tsh"}, {"label": "Celiac disease blood test", "value": "celiac"}, {"label": "I have not had these tests completed", "value": "no_prior_tests"}], "validate": {"required": true}, "inputType": "checkbox", "tableView": true, "confirm_label": "Prior Tests Completed:", "optionsLabelPosition": "right"}, {"key": "last_known_lab_timing", "data": {"values": [{"label": "<1 month ago", "value": "Less than 1 month ago"}, {"label": "1-2 months ago", "value": "1-2 months ago"}, {"label": "2-3 months ago", "value": "2-3 months ago"}, {"label": "3-4 months ago", "value": "3-4 months ago"}, {"label": "4-5 months ago", "value": "4-5 months ago"}, {"label": "5-6 months ago", "value": "5-6 months ago"}, {"label": "6-12 months ago", "value": "6-12 months ago"}, {"label": "12-24 months ago", "value": "12-24 months ago"}, {"label": "24+ months ago", "value": "More than 24 months ago"}, {"label": "I haven't had lab testing", "value": "Haven't had lab testing"}]}, "type": "select", "input": true, "label": "Do you recall when your last set of lab tests (bloodwork) was completed?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Last Known Lab Timing:", "customConditional": "show = data.prior_tests_completed?.no_prior_tests === true && Object.values(data.prior_tests_completed).filter(v => v === true).length === 1"}, {"key": "heading_kidney_function", "html": "<h3>Kidney Function (eGFR)</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.prior_tests_completed?.egfr;"}, {"key": "last_kidney_function_test", "data": {"values": [{"label": "<1 month ago", "value": "Less than 1 month ago"}, {"label": "1-2 months ago", "value": "1-2 months ago"}, {"label": "2-3 months ago", "value": "2-3 months ago"}, {"label": "3-4 months ago", "value": "3-4 months ago"}, {"label": "4-5 months ago", "value": "4-5 months ago"}, {"label": "5-6 months ago", "value": "5-6 months ago"}, {"label": "6-12 months ago", "value": "6-12 months ago"}, {"label": "12-24 months ago", "value": "12-24 months ago"}, {"label": "24+ months ago", "value": "More than 24 months ago"}, {"label": "Never had one", "value": "Never had one"}]}, "type": "select", "input": true, "label": "When was your last kidney function test completed?", "widget": "html5", "validate": {"required": true}, "tableView": true, "placeholder": "Select the time range", "confirm_label": "Last Kidney Function Test:", "customConditional": "show = data.prior_tests_completed?.egfr;"}, {"key": "prior_kidney_function_value", "data": {"values": [{"label": "I don't remember my result", "value": "I don't remember my result"}, {"label": "eGFR > 90", "value": "eGFR > 90"}, {"label": "eGFR 85-89", "value": "eGFR 85-89"}, {"label": "eGFR 80-84", "value": "eGFR 80-84"}, {"label": "eGFR 75-79", "value": "eGFR 75-79"}, {"label": "eGFR 70-74", "value": "eGFR 70-74"}, {"label": "eGFR 65-69", "value": "eGFR 65-69"}, {"label": "eGFR 60-64", "value": "eGFR 60-64"}, {"label": "eGFR 55-59", "value": "eGFR 55-59"}, {"label": "eGFR 50-54", "value": "eGFR 50-54"}, {"label": "eGFR 45-49", "value": "eGFR 45-49"}, {"label": "eGFR < 45", "value": "eGFR < 45"}]}, "type": "select", "input": true, "label": "What was your most recent eGFR measurement?", "widget": "html5", "validate": {"required": true}, "tableView": true, "placeholder": "Select the value range", "confirm_label": "eGFR Value Range:", "customConditional": "show = data.prior_tests_completed?.egfr;"}, {"key": "heading_urine_acr", "html": "</br><h3>Urine Albumin-to-Creatinine Ratio (ACR)</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.prior_tests_completed?.urine_acr;"}, {"key": "last_urine_acr_test", "data": {"values": [{"label": "<1 month ago", "value": "Less than 1 month ago"}, {"label": "1-2 months ago", "value": "1-2 months ago"}, {"label": "2-3 months ago", "value": "2-3 months ago"}, {"label": "3-4 months ago", "value": "3-4 months ago"}, {"label": "4-5 months ago", "value": "4-5 months ago"}, {"label": "5-6 months ago", "value": "5-6 months ago"}, {"label": "6-12 months ago", "value": "6-12 months ago"}, {"label": "12-24 months ago", "value": "12-24 months ago"}, {"label": "24+ months ago", "value": "More than 24 months ago"}, {"label": "Never had one", "value": "Never had one"}]}, "type": "select", "input": true, "label": "When was your last urine ACR test completed?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Last Urine ACR Test:", "customConditional": "show = data.prior_tests_completed?.urine_acr;"}, {"key": "heading_lipid_profile", "html": "</br><h3>Lipid Profile (Cholesterol)</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.prior_tests_completed?.lipid_profile;"}, {"key": "last_lipid_profile_test", "data": {"values": [{"label": "<1 month ago", "value": "Less than 1 month ago"}, {"label": "1-2 months ago", "value": "1-2 months ago"}, {"label": "2-3 months ago", "value": "2-3 months ago"}, {"label": "3-4 months ago", "value": "3-4 months ago"}, {"label": "4-5 months ago", "value": "4-5 months ago"}, {"label": "5-6 months ago", "value": "5-6 months ago"}, {"label": "6-12 months ago", "value": "6-12 months ago"}, {"label": "12-24 months ago", "value": "12-24 months ago"}, {"label": "24+ months ago", "value": "More than 24 months ago"}, {"label": "Never had one", "value": "Never had one"}]}, "type": "select", "input": true, "label": "When was your last lipid profile test completed?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Last Lipid Profile Test:", "customConditional": "show = data.prior_tests_completed?.lipid_profile;"}, {"key": "heading_lipid_profile_results", "html": "</br><h3>Lipid Profile Results</h3><p>Select any abnormalities found in your test results.</p>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.last_lipid_profile_test !== undefined;"}, {"key": "lipid_profile_abnormalities", "type": "selectboxes", "input": true, "label": "Were any of the following findings reported in your lipid profile?", "values": [{"label": "High LDL", "value": "high_ldl"}, {"label": "High Triglycerides", "value": "high_triglycerides"}, {"label": "High HDL", "value": "high_hdl"}, {"label": "Normal HDL", "value": "normal_hdl"}, {"label": "Normal lipid profile", "value": "normal_profile"}, {"label": "I don't remember my values", "value": "dont_remember"}, {"label": "I don't remember them but was told they were normal", "value": "dont_remember_normal"}], "tooltip": "Select all that apply based on your most recent test results.", "validate": {"required": true}, "tableView": true, "customClass": "mt-n3", "confirm_label": "Lipid Profile Abnormalities:", "customConditional": "show = data.last_lipid_profile_test !== undefined;"}, {"key": "heading_diabetes_tests", "html": "</br><h3>Diabetes Testing (HbA1c or Fasting Blood Glucose)</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.prior_tests_completed?.a1c === true || data.prior_tests_completed?.fasting_glucose === true;"}, {"key": "last_a1c_test", "data": {"values": [{"label": "<1 month ago", "value": "Less than 1 month ago"}, {"label": "1-2 months ago", "value": "1-2 months ago"}, {"label": "2-3 months ago", "value": "2-3 months ago"}, {"label": "3-4 months ago", "value": "3-4 months ago"}, {"label": "4-5 months ago", "value": "4-5 months ago"}, {"label": "5-6 months ago", "value": "5-6 months ago"}, {"label": "6-12 months ago", "value": "6-12 months ago"}, {"label": "12-24 months ago", "value": "12-24 months ago"}, {"label": "24+ months ago", "value": "More than 24 months ago"}, {"label": "Never had one", "value": "Never had one"}]}, "type": "select", "input": true, "label": "When was your last HbA1c test completed?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Last A1c Test:", "customConditional": "show = data.prior_tests_completed?.a1c === true;"}, {"key": "recent_a1c_value", "data": {"values": [{"label": "I don't remember my result", "value": "dont_remember"}, {"label": "Below 5.7%", "value": "below_5_7"}, {"label": "5.7% - 6.4%", "value": "5_7_6_4"}, {"label": "6.5% - 6.9%", "value": "6_5_6_9"}, {"label": "7.0% - 7.9%", "value": "7_0_7_9"}, {"label": "8.0% - 8.9%", "value": "8_0_8_9"}, {"label": "9.0% - 9.9%", "value": "9_0_9_9"}, {"label": "10.0% or higher", "value": "10_or_higher"}]}, "type": "select", "input": true, "label": "What was your most recent HbA1c result?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Recent A1c Value:", "customConditional": "show = data.prior_tests_completed?.a1c === true;"}, {"key": "last_fasting_glucose_test", "data": {"values": [{"label": "<1 month ago", "value": "Less than 1 month ago"}, {"label": "1-2 months ago", "value": "1-2 months ago"}, {"label": "2-3 months ago", "value": "2-3 months ago"}, {"label": "3-4 months ago", "value": "3-4 months ago"}, {"label": "4-5 months ago", "value": "4-5 months ago"}, {"label": "5-6 months ago", "value": "5-6 months ago"}, {"label": "6-12 months ago", "value": "6-12 months ago"}, {"label": "12-24 months ago", "value": "12-24 months ago"}, {"label": "24+ months ago", "value": "More than 24 months ago"}, {"label": "Never had one", "value": "Never had one"}]}, "type": "select", "input": true, "label": "When was your last fasting blood glucose test completed?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Last Fasting Glucose Test:", "customConditional": "show = data.prior_tests_completed?.fasting_glucose === true;"}, {"key": "recent_fbg_value", "data": {"values": [{"label": "I don't remember my result", "value": "dont_remember"}, {"label": "Below 5.6 mmol/L [<100 mg/dL]", "value": "Below 5.6 mmol/L"}, {"label": "5.6 - 6.9 mmol/L [100-125 mg/dL]", "value": "5.6 - 6.9 mmol/L"}, {"label": "7.0 - 7.9 mmol/L [126-142 mg/dL]", "value": "7.0 - 7.9 mmol/L"}, {"label": "8.0 - 8.9 mmol/L [143-160 mg/dL]", "value": "8.0 - 8.9 mmol/L"}, {"label": "9.0 - 9.9 mmol/L [161-178 mg/dL]", "value": "9.0 - 9.9 mmol/L"}, {"label": "10.0 - 11.9 mmol/L [179-214 mg/dL]", "value": "10.0 - 11.9 mmol/L"}, {"label": "12.0+ mmol/L [215+ mg/dL]", "value": "12.0+ mmol/L"}]}, "type": "select", "input": true, "label": "What was your most recent <strong>fasting blood glucose</strong> (FBG) result?", "widget": "html5", "validate": {"required": true}, "tableView": true, "description": "Select the range that matches your lab result. Canadian units shown (mmol/L), with U.S. units in [mg/dL].", "confirm_label": "Recent FBG Value:", "customConditional": "show = data.prior_tests_completed?.fasting_glucose === true && data.last_fasting_glucose_test !== 'never_had';"}, {"key": "heading_cbc", "html": "</br><h3>CBC (Complete Blood Count)</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.prior_tests_completed?.cbc;"}, {"key": "last_cbc_test", "data": {"values": [{"label": "<1 month ago", "value": "Less than 1 month ago"}, {"label": "1-2 months ago", "value": "1-2 months ago"}, {"label": "2-3 months ago", "value": "2-3 months ago"}, {"label": "3-4 months ago", "value": "3-4 months ago"}, {"label": "4-5 months ago", "value": "4-5 months ago"}, {"label": "5-6 months ago", "value": "5-6 months ago"}, {"label": "6-12 months ago", "value": "6-12 months ago"}, {"label": "12-24 months ago", "value": "12-24 months ago"}, {"label": "24+ months ago", "value": "More than 24 months ago"}, {"label": "Never had one", "value": "Never had one"}]}, "type": "select", "input": true, "label": "When was your last CBC test completed?", "widget": "html5", "validate": {"required": true}, "tableView": true, "placeholder": "Select the time range", "confirm_label": "Last CBC Test:", "customConditional": "show = data.prior_tests_completed?.cbc;"}, {"key": "prior_cbc_value", "data": {"values": [{"label": "I don't remember my result", "value": "I don't remember my result"}, {"label": "< 70 g/L", "value": "Less than 70 g/L"}, {"label": "70-100 g/L", "value": "70-100 g/L"}, {"label": "101-120 g/L", "value": "101-120 g/L"}, {"label": "121-150 g/L", "value": "121-150 g/L"}, {"label": "> 150 g/L", "value": "Greater than 150 g/L"}]}, "type": "select", "input": true, "label": "What was your most recent hemoglobin level (CBC result)?", "widget": "html5", "validate": {"required": true}, "tableView": true, "placeholder": "Select your hemoglobin level", "confirm_label": "Hemoglobin Level:", "customConditional": "show = data.prior_tests_completed?.cbc;"}, {"key": "heading_b12", "html": "</br><h3>Vitamin B12</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.prior_tests_completed?.b12;"}, {"key": "last_b12_test", "data": {"values": [{"label": "<1 month ago", "value": "Less than 1 month ago"}, {"label": "1-2 months ago", "value": "1-2 months ago"}, {"label": "2-3 months ago", "value": "2-3 months ago"}, {"label": "3-4 months ago", "value": "3-4 months ago"}, {"label": "4-5 months ago", "value": "4-5 months ago"}, {"label": "5-6 months ago", "value": "5-6 months ago"}, {"label": "6-12 months ago", "value": "6-12 months ago"}, {"label": "12-24 months ago", "value": "12-24 months ago"}, {"label": "24+ months ago", "value": "More than 24 months ago"}, {"label": "Never had one", "value": "Never had one"}]}, "type": "select", "input": true, "label": "When was your last B12 test completed?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Last B12 Test:", "customConditional": "show = data.prior_tests_completed?.b12;"}, {"key": "prior_b12_value", "data": {"values": [{"label": "I don't remember my result", "value": "dont_remember"}, {"label": "< 150 pmol/L", "value": "lt_150"}, {"label": "150-200 pmol/L", "value": "150_200"}, {"label": "200-300 pmol/L", "value": "200_300"}, {"label": "> 300 pmol/L", "value": "gt_300"}]}, "type": "select", "input": true, "label": "What was your most recent B12 level?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "B12 Level:", "customConditional": "show = data.prior_tests_completed?.b12;"}, {"key": "heading_tsh", "html": "</br><h3>TSH (Thyroid Stimulating Hormone)</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.prior_tests_completed?.tsh;"}, {"key": "last_tsh_test", "data": {"values": [{"label": "<1 month ago", "value": "Less than 1 month ago"}, {"label": "1-2 months ago", "value": "1-2 months ago"}, {"label": "2-3 months ago", "value": "2-3 months ago"}, {"label": "3-4 months ago", "value": "3-4 months ago"}, {"label": "4-5 months ago", "value": "4-5 months ago"}, {"label": "5-6 months ago", "value": "5-6 months ago"}, {"label": "6-12 months ago", "value": "6-12 months ago"}, {"label": "12-24 months ago", "value": "12-24 months ago"}, {"label": "24+ months ago", "value": "More than 24 months ago"}, {"label": "Never had one", "value": "Never had one"}]}, "type": "select", "input": true, "label": "When was your last TSH test completed?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Last TSH Test:", "customConditional": "show = data.prior_tests_completed?.tsh;"}, {"key": "prior_tsh_value", "data": {"values": [{"label": "I don't remember my result", "value": "I don't remember my result"}, {"label": "Below 0.1 mIU/L", "value": "Below 0.1 mIU/L"}, {"label": "0.1 - 0.3 mIU/L", "value": "0.1 - 0.3 mIU/L"}, {"label": "0.4 - 4.0 mIU/L", "value": "0.4 - 4.0 mIU/L"}, {"label": "4.1 - 5.0 mIU/L", "value": "4.1 - 5.0 mIU/L"}, {"label": "5.1 - 7.5 mIU/L", "value": "5.1 - 7.5 mIU/L"}, {"label": "7.6 - 10 mIU/L", "value": "7.6 - 10 mIU/L"}, {"label": "Above 10 mIU/L", "value": "Above 10 mIU/L"}]}, "type": "select", "input": true, "label": "What was your most recent TSH level?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "TSH Level:", "customConditional": "show = data.prior_tests_completed?.tsh;"}, {"key": "heading_celiac_test", "html": "<h3>Celiac Disease Blood Test</h3>", "type": "content", "input": false, "label": "Content", "tableView": false, "customConditional": "show = data.prior_tests_completed?.celiac === true"}, {"key": "prior_celiac_test_timing", "data": {"values": [{"label": "Less than 6 weeks ago", "value": "0_6_weeks"}, {"label": "6 weeks - 3 months ago", "value": "6_weeks_3_months"}, {"label": "3-6 months ago", "value": "3_6_months"}, {"label": "6-12 months ago", "value": "6_12_months"}, {"label": "More than 1 year ago", "value": "1_plus_year"}, {"label": "I don't remember", "value": "unknown"}]}, "type": "select", "input": true, "label": "When was your celiac disease blood test completed?", "widget": "html5", "validate": {"required": true}, "tableView": true, "confirm_label": "Timing of celiac test:", "customConditional": "show = data.prior_tests_completed?.celiac === true"}, {"key": "prior_celiac_test_result", "type": "radio", "input": true, "label": "What was the result of your celiac blood test?", "values": [{"label": "Positive", "value": "positive"}, {"label": "Negative", "value": "negative"}, {"label": "I don't remember", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Celiac test result:", "customConditional": "show = data.prior_tests_completed?.celiac === true"}, {"key": "prior_celiac_test_diet", "type": "radio", "input": true, "label": "Were you eating gluten (e.g. bread, pasta) regularly when the test was done?", "values": [{"label": "Yes - I was eating gluten regularly", "value": "yes"}, {"label": "No - I was already on a gluten-free diet", "value": "no"}, {"label": "I'm not sure", "value": "not_sure"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Gluten exposure during test:", "customConditional": "show = data.prior_tests_completed?.celiac === true"}, {"key": "additional_information", "type": "textarea", "input": true, "label": "Please provide any additional details about your symptoms, medical history, or medications that you think are relevant.", "tableView": true, "autoExpand": false}, {"key": "heading_recommendations_summary", "type": "content", "input": false, "html": "</br><h4>Recommendations</h4>", "tableView": false, "customConditional": "show = !!data.symptom_start_bloating || !!data.symptom_start_diarrhea || !!data.symptom_start_constipation || !!data.symptom_start_abdominal_pain || !!data.symptom_start_fatigue || !!data.symptom_start_weight_loss || !!data.rectal_bleeding_first_onset"}, {"key": "recommendation_same_day", "type": "content", "input": true, "html": "<div style='border-left:4px solid #dc3545;background:#f8d7da;padding:10px;margin-bottom:10px;'><strong>Urgent recommendation:</strong> One or more of your symptoms started recently (within the past 4 weeks) and may need a <strong>same-day in-person medical assessment</strong>. Please visit your doctor or a local walk-in clinic as soon as possible.</div>", "computeValue": "value = (['last_night','today','1_day_ago','2_days_ago','3_days_ago','4_days_ago','5_days_ago','6_days_ago','7_days_ago','8_days_ago','9_days_ago','10_days_ago','11_days_ago','12_days_ago','13_days_ago','14_days_ago','14-21_days','21-28_days'].some(d => [data.symptom_start_bloating, data.symptom_start_diarrhea, data.symptom_start_constipation, data.symptom_start_abdominal_pain, data.symptom_start_fatigue, data.symptom_start_weight_loss].includes(d?.value)) || ['1_3_months','4_12_month','over_1_year_intermittent','over_1_year_continuous'].includes(data.rectal_bleeding_first_onset))", "customConditional": "show = data.recommendation_same_day"}, {"key": "recommendation_prompt", "type": "content", "input": false, "html": "<div style='border-left:4px solid #ffc107;background:#fff3cd;padding:10px;margin-bottom:10px;'><strong>Recommendation:</strong> Some of your symptoms have been ongoing for more than 4 weeks. We recommend <strong>prompt in-person follow-up</strong>. If you haven't already been evaluated, please arrange an appointment with your doctor or a local walk-in clinic.</div>", "customConditional": "show = !data.recommendation_same_day && (['28-60_days','60-90_days','90_days-1_year'].some(d => [data.symptom_start_bloating, data.symptom_start_diarrhea, data.symptom_start_constipation, data.symptom_start_abdominal_pain, data.symptom_start_fatigue, data.symptom_start_weight_loss].includes(d?.value)) || ['1_3_months','4_12_month','over_1_year_intermittent','over_1_year_continuous'].includes(data.rectal_bleeding_first_onset))"}, {"key": "recommendation_combined_followup_acknowledgement", "type": "radio", "input": true, "label": "Do you understand this recommendation for in-person medical follow-up based on your symptoms?", "values": [{"label": "I understand", "value": "understand"}, {"label": "I do not understand", "value": "do_not_understand"}], "validate": {"required": true}, "tableView": true, "confirm_label": "Understands follow-up recommendation:", "customConditional": "show = !!data.symptom_start_bloating || !!data.symptom_start_diarrhea || !!data.symptom_start_constipation || !!data.symptom_start_abdominal_pain || !!data.symptom_start_fatigue || !!data.symptom_start_weight_loss || !!data.rectal_bleeding_first_onset"}, {"key": "intake_denial_reasons", "type": "textfield", "input": true, "label": "Intake Denial Reasons:", "hidden": true, "disabled": true, "multiple": true, "adminFlag": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "value = [];", "refreshOnChange": true}, {"key": "intake_kind", "type": "textfield", "input": true, "label": "Intake Kind", "hidden": true, "disabled": true, "tableView": true, "clearOnHide": false, "calculateValue": "value = !data.intake_denial_reasons?.length ? 'mail' : 'chat';"}, {"key": "intake_mt_key", "type": "textfield", "input": true, "label": "Intake Mail Template Key", "hidden": true, "disabled": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.intake_kind=='mail'?'intake-trt':'intake-denial'"}, {"key": "allowed_rt_keys", "type": "textfield", "input": true, "label": "Allowed Rep<PERSON>", "hidden": true, "disabled": true, "multiple": true, "tableView": false, "clearOnHide": false, "calculateValue": "value = data.intake_mt_key=='intake-denial'?[]:['get-req','appointment-intake','edit-intake']"}]}