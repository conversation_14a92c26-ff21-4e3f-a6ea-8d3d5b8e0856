{% load template_extras %}
{% with data=questionnaire.hpc.data %}
{% with summary=questionnaire.raw_formio_summary %}
{% with insured=questionnaire.insured_assays.all %}
{% with uninsured=questionnaire.uninsured_assays.all %}
{% with rxts=questionnaire.rxts.all %}
<p>Hi {{patient.name}},</p>
<p>This is {{doctor.name}} (CPSO #{{doctor.cpso_number}}) &amp; clinic contact phone {{doctor.phone}}.</p>
<p>I've reviewed your history. If you felt that you did not understand any questions, or felt your answers did not accurately describe or excluded symptoms, we can set up an appointment for real-time secure messaging, otherwise we can proceed with the plan below.</p>

{# --- Exposure History & Prior Doxy-PEP Use -------------------------- #}
<p class="mb-0"><strong>Exposure History and Prior Doxy-PEP Use:</strong></p>
<ul>
<li>Exposure date / time:
  {% if data.exposure_date_time %}
    {% with pretty=data.exposure_date_time|date:"F j, Y \\a\\t g:i A" %}
      {% if pretty %}
        {{ pretty }} {# e.g., August 20, 2025 at 12:00 PM #}
      {% else %}
        {% with iso=data.exposure_date_time %}
          {{ iso|slice:":16"|replace:"T| at " }}
          {% if "T" in iso %}
            {% with hh=iso|slice:"11:13" %}
              {% if hh >= "12" %} PM{% else %} AM{% endif %}
            {% endwith %}
          {% endif %}
        {% endwith %}
      {% endif %}
    {% endwith %}
  {% else %}
    Not provided
  {% endif %}
</li>

  {% if data.time_since_exposure %}
    <li>Hours since exposure: {{ data.time_since_exposure }}</li>
  {% endif %}

  {% if data.past_doxypep_use and data.past_doxypep_use.doxypep %}
    <li>Previous Doxy-PEP use: <strong>Yes</strong></li>

{% if data.last_dose_doxypep %}
  {% with v=data.last_dose_doxypep %}
    <li>Last Doxy-PEP dose:
      {% if v == "within_24h" %}
        Within 24 hours
      {% elif v == "more_than_1_year" %}
        More than 1 year ago
      {% elif "_weeks" in v %}
        {{ v|replace:"_weeks| weeks ago"|replace:"_| " }}
      {% elif "_week" in v %}
        {{ v|replace:"_week| week ago"|replace:"_| " }}
      {% elif "_months" in v %}
        {{ v|replace:"_months| months ago"|replace:"_| " }}
      {% elif "_month" in v %}
        {{ v|replace:"_month| month ago"|replace:"_| " }}
      {% elif "_days" in v %}
        {{ v|replace:"_days| days ago"|replace:"_| " }}
      {% elif "_day" in v %}
        {{ v|replace:"_day| day ago"|replace:"_| " }}
      {% else %}
        {{ v|replace:"_| " }}
      {% endif %}
    </li>
  {% endwith %}
{% endif %}



    {% if data.doxypep_tolerance %}
      <li>Tolerance to last dose:
        {% if data.doxypep_tolerance == "none" %}
          No side effects
        {% elif data.doxypep_tolerance == "mild" %}
          Mild side effects
        {% elif data.doxypep_tolerance == "moderate_tolerable" %}
          Moderate but tolerable side effects
        {% elif data.doxypep_tolerance == "severe" %}
          Severe side effects
        {% else %}
          {{ data.doxypep_tolerance|replace:"_, "|title }}
        {% endif %}
      </li>
    {% endif %}

    {% if data.sti_test_after_doxypep %}
      <li>STI testing since last dose:
        {% if data.sti_test_after_doxypep == "tested_negative" %}
          Completed - all results negative
        {% elif data.sti_test_after_doxypep == "tested_positive" %}
          Completed - tested positive
        {% elif data.sti_test_after_doxypep == "no_testing" %}
          Not yet tested
        {% else %}
          {{ data.sti_test_after_doxypep|replace:"_, "|title }}
        {% endif %}
      </li>
    {% endif %}

  {% elif data.never_used_doxypep %}
    <li>Previous Doxy-PEP use: <strong>Never taken</strong></li>
  {% endif %}
</ul>
{# ------------------------------------------------------------------- #}



<!-- Health Condition Summary -->
<p class="mb-0"><strong>Health Condition Summary</strong></p>
<ul>
  {% with summary=questionnaire.raw_formio_summary %}

  <li><strong>Current Medications:</strong> 
    {% if summary.medications_list and summary.medications_list.c_val %}
      {{ summary.medications_list.c_val }}
    {% else %}
      None
    {% endif %}
  </li>

<li><strong>Medication Allergies:</strong> 
  {% if summary.medication_allergies_list and summary.medication_allergies_list.c_val %}
    {{ summary.medication_allergies_list.c_val }}
  {% else %}
    None
  {% endif %}
</li>

  <li><strong>Past Surgeries:</strong> 
    {% if summary.past_surgeries_list and summary.past_surgeries_list.c_val %}
      {{ summary.past_surgeries_list.c_val }}
    {% else %}
      None
    {% endif %}
  </li>

  <li><strong>Health Conditions:</strong> 
    {% if summary.health_conditions_list and summary.health_conditions_list.c_val %}
      {{ summary.health_conditions_list.c_val }}
    {% else %}
      None
    {% endif %}
  </li>

  {% endwith %}
</ul>

<p class="mb-0"><strong>Doxy-PEP Treatment:</strong></p>
<ul>
  <li>You don't have any symptoms<ul>
    </ul></li>
    <li>Your exposure was within the last 72 hours</li>
    <li>You understand:<ul>
        <li>Doxy-PEP does not cure or treat chlamydia, gonorrhea or syphilis if you have had a confirmed exposure</li>
<li>Doxy-PEP is not HIV post-exposure prophylaxis (PEP), and if you wish to obtain HIV PEP you need to visit an emergency room.</li>
<li>Testing remains an important part of safe sexual health practices.</li>
    </ul></li>
</ul>

{# --- Medication Safety ------------------------------------------------ #}
<p class="mb-0"><strong>Medication Safety:</strong></p>
<ul>
  {# Allergies from summary #}
  {% for s in summary|confirm:"antibiotic_allergies,allergies_not_present" %}
    <li>{{ s|safe }}</li>
  {% endfor %}

  {# Contraindications PRESENT from summary #}
  {% for s in summary|confirm:"doxy_contraindications" %}
    <li>{{ s|safe }}</li>
  {% endfor %}

  {# Custom phrasing for NOT PRESENT items #}
  {% if data.doxy_contraindications %}
    {% if not data.doxy_contraindications.liver_disease %}
      <li>No diagnosis of liver disease</li>
    {% endif %}
    {% if not data.doxy_contraindications.isotretinoin %}
      <li>Not currently on isotretinoin (Epuris, Accutane)</li>
    {% endif %}

    {# Pregnancy / Breastfeeding only if sex is NOT male #}
    {% if data.sex and data.sex|lower != "male" %}
      {% if not data.doxy_contraindications.pregnancy %}
        <li>Not currently pregnant</li>
      {% endif %}
      {% if not data.doxy_contraindications.breastfeeding %}
        <li>Not breastfeeding</li>
      {% endif %}
    {% endif %}
  {% endif %}
</ul>


{# --- Confirmations ---------------------------------------------------- #}
<p class="mb-0"><strong>Confirmations:</strong></p>
<ul>
  {% for s in summary|confirm:"side_effects_doxy_ack,med_info_accuracy_ack,proceed_with_doxypep" %}
    <li>{{ s|safe }}</li>
  {% endfor %}
</ul>

<p class="mb-0"><strong>Plan</strong></p>
{% if insured or uninsured %} <!-- testing -->
<p class="mb-0">Lab testing for the following:</p>
{% if insured and uninsured %}
<strong class="mb-0">Insured</strong>
{% endif %}
<ul>
    {% for a in insured %}
    <li>{{a.name}} ({{a.test_type}} test)</li>
    {% endfor %}
</ul>
{% if uninsured %}
<strong class="mb-0">Uninsured</strong>
{% endif %}
<ul>
    {% for a in uninsured %}
    <li>{{a.name}} ({{a.test_type}} test)</li>
    {% endfor %}
</ul>

{% if data.recommend_any %}
<p class="mb-0">The following tests were recommended to be added to your requisition:</p>
<ul>
    {% if data.recommend_vswb %}
    <li>Vaginal self-swab<ul><li>Bacterial vaginosis (BV)</li><li>Yeast infection</li></ul></li>
    {% endif %}{% if data.recommend_uti %}
    <li>Urine culture<ul><li>Urinary tract-infection (UTI)</li></ul></li>
    {% endif %}
</ul>
{% endif %} 
{% endif %} 
{% if rxts %}
<p>Prescription for the following:</p>
<ul>
    {% for rx in rxts %}
    <li>{{rx.display_name}}</li>
    {% endfor %}
</ul>
{% endif %}
{% if data.opt_out_names %}
<p>It was noted that you opted-out of the following assays: {{data.opt_out_names}}</p>
{% endif %}

{% if data.concerned_about_known_exposure == True %}
You mentioned that you were concerned about exposure to {{data.exposure_keys|join:", "}}.
Based on your preferences I have included prescription treatments in your plan.
{% endif %}
<p class="mb-0">By continuing with the plan you confirm:</p>
<ul>
    <li>You don't have any abdominal/pelvic pain or fevers/chills<ul>
        <li>You'll seek same-day follow-up care in-person if you develop any</li>
    </ul></li>
    <li>Your medical history is correct</li>
    <li>You understand:<ul>
        <li>All advice that was provided in the intake assessment</li>
        <li>Accurate testing requires waiting a certain time after the exposure date (window period)</li>
    </ul></li>
</ul>
<!-- <p><em>Note that your history questionnaire, window period, and advice information is below for your reference.</em></p> -->
<p>Best regards,<br>{{doctor.name}}</p>
<hr>
<p class="mb-0"><strong>Medical History Summary:</strong></p>
<ul>
{% for s in summary|confirm:"sex, chief_complaint,testing_indication,last_sti_test, partner_sex, type_of_sex, hep_b_vaccinated, syphilis_last_treatment, last_rpr_titre_level, currently_pregnant" %}
<li>{{s|safe}}</li>
{% endfor %}
</ul>

{% with symptom_list=summary|confirm:"urinary_symptoms_present,urinary_symptoms_not_present,std_red_flag_symptoms_not_present" %}
  {% if data.sex and data.sex|lower != "male" %}
    {% with vaginal_list=summary|confirm:"vaginal_symptoms_present,vaginal_symptoms_not_present" %}
      {% if symptom_list or vaginal_list %}
        <p class="mb-0">Symptoms:</p>
        <ul>
          {% for s in vaginal_list %}
            <li>{{ s|safe }}</li>
          {% endfor %}
          {% for s in symptom_list %}
            <li>{{ s|safe }}</li>
          {% endfor %}
        </ul>
      {% endif %}
    {% endwith %}
  {% else %}
    {% if symptom_list %}
      <p class="mb-0">Symptoms:</p>
      <ul>
        {% for s in symptom_list %}
          <li>{{ s|safe }}</li>
        {% endfor %}
      </ul>
    {% endif %}
  {% endif %}
{% endwith %}


{% with wp_list=summary|confirm:"exposure_date, not_in_early_window_period, not_in_late_window_period" %}
  {% if wp_list %}
    <p class="mb-0">Window Periods:</p>
    <ul>
      {% for s in wp_list %}
        <li>{{ s|safe }}</li>
      {% endfor %}
    </ul>
  {% endif %}
{% endwith %}

{% endwith %}
{% endwith %}
{% endwith %}
{% endwith %}
{% endwith %}