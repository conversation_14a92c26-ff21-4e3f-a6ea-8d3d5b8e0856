{"components": [{"key": "patient_age_group", "type": "radio", "input": true, "label": "How old are you?", "values": [{"label": "Under 35", "value": "under_35"}, {"label": "35-39", "value": "35_39"}, {"label": "40+", "value": "40_plus"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "amh_test_indication", "type": "selectboxes", "input": true, "label": "Do any of the following apply to you?", "values": [{"label": "I am considering freezing my eggs (i.e., IVF, egg freezing)", "value": "fertility_preservation"}, {"label": "I am undergoing IVF treatments", "value": "ivf_treatments"}, {"label": "I have been unable to conceive after 12 months of trying", "value": "under_35_trying_to_conceive", "customConditional": "show = data.patient_age_group === 'under_35';"}, {"label": "I have been unable to conceive after 6 months of trying", "value": "age_35_trying_to_conceive", "customConditional": "show = data.patient_age_group === '35_39';"}, {"label": "I am planning on conceiving", "value": "age_40_trying_to_conceive", "customConditional": "show = data.patient_age_group === '40_plus';"}, {"label": "I plan on donating my eggs", "value": "donating_eggs"}, {"label": "AMH testing is required by my fertility specialist", "value": "required_by_specialist"}], "inputType": "checkbox", "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = !!data.patient_age_group;"}, {"key": "no_amh_test_indication", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "Please select at least one option or confirm none apply."}, "validate": {"custom": "valid = _.some(_.values(data.amh_test_indication)) || data.no_amh_test_indication;"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = !!data.patient_age_group;"}, {"key": "health_status_questions", "type": "selectboxes", "input": true, "label": "Please indicate if any of the following apply to you:", "values": [{"label": "I am currently pregnant", "value": "currently_pregnant"}, {"label": "I have a history of ovarian cancer", "value": "history_of_ovarian_cancer"}, {"label": "I am concerned I am experiencing a miscarriage", "value": "concerned_about_miscarriage"}], "inputType": "checkbox", "tableView": true, "optionsLabelPosition": "right", "customConditional": "show = !!_.some(_.values(data.amh_test_indication)) && !data.no_amh_test_indication;"}, {"key": "none_of_the_above_health_status", "type": "checkbox", "input": true, "label": "None of the above", "tableView": true, "customClass": "mt-n3", "defaultValue": false, "validate": {"custom": "valid = !!data.none_of_the_above_health_status || !!_.some(_.values(data.health_status_questions));"}, "customConditional": "show = !!_.some(_.values(data.amh_test_indication)) && !data.no_amh_test_indication;"}, {"key": "amh_contraindication", "type": "selectboxes", "input": true, "label": "Are you currently experiencing any of the following symptoms?", "values": [{"label": "Abdominal/Pelvic Pain", "value": "abdominal_pain"}, {"label": "Breast discharge/leakage", "value": "breast_discharge"}, {"label": "Heavier than normal vaginal bleeding", "value": "heavier_vaginal_bleeding"}, {"label": "Spotting between your periods", "value": "spotting_between_periods"}], "inputType": "checkbox", "optionsLabelPosition": "right", "tableView": true, "customConditional": "show = !!data.none_of_the_above_health_status;"}, {"key": "none_of_the_above_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "tableView": true, "customClass": "mt-n3", "defaultValue": false, "validate": {"custom": "valid = !!data.none_of_the_above_symptoms || !!_.some(_.values(data.amh_contraindication));"}, "customConditional": "show = !!data.none_of_the_above_health_status;"}, {"key": "no_indications", "type": "textfield", "input": true, "label": "No Indication", "hidden": true, "disabled": true, "multiple": false, "tableView": true, "clearOnHide": false, "defaultValue": true, "calculateValue": "value = !!_.some(_.values(data.amh_test_indication)) ? false : true;", "refreshOnChange": true}, {"key": "contraindication_keys", "type": "textfield", "input": true, "label": "Contraindication Keys", "hidden": true, "disabled": true, "multiple": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "value = (_.some(_.values(data.amh_contraindication)) || _.some(_.values(data.health_status_questions))) ? ['amh_contraindication_present'] : [];", "refreshOnChange": true}]}