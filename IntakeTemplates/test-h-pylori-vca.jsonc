{"components": [{"key": "indication_for_testing", "type": "selectboxes", "input": true, "label": "Please select if any of the following apply to you:", "tooltip": "Check any that apply to you for better assessment.", "values": [{"label": "I have stomach discomfort or indigestion", "value": "stomach_discomfort", "shortcut": ""}, {"label": "I've had a gastric or duodenal ulcer", "value": "ulcers_history", "shortcut": ""}, {"label": "I have a family member with gastric (stomach) cancer", "value": "gastric_cancer_history", "shortcut": ""}, {"label": "I have a family member with an <PERSON><PERSON> infection", "value": "family_h_pylori_infection", "shortcut": ""}, {"label": "I was born outside of Canada", "value": "immigrant_high_prevalence", "shortcut": ""}, {"label": "None of these apply to me", "value": "none_of_the_above", "shortcut": ""}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "duration_of_symptoms", "type": "radio", "input": true, "label": "Have your stomach discomfort or indigestion symptoms been present for:", "tooltip": "Your answer helps determine the urgency and type of care needed.", "values": [{"label": "Less than 6 months", "value": "less_than_6_months", "shortcut": ""}, {"label": "More than 6 months", "value": "more_than_6_months", "shortcut": ""}], "validate": {"required": false}, "tableView": true, "optionsLabelPosition": "right", "conditional": {"json": {"var": "data.indication_for_testing.stomach_discomfort"}}}, {"key": "pylori_contraindication", "type": "selectboxes", "input": true, "label": "Are you currently experiencing any of the following symptoms:", "values": [{"label": "Feel unwell", "value": "unwell", "shortcut": ""}, {"label": "Chest pain or pressure", "value": "chest_pain", "shortcut": ""}, {"label": "Palpitations (heart racing or fluttering)", "value": "palpitations", "shortcut": ""}, {"label": "Shortness of breath", "value": "shortness_of_breath", "shortcut": ""}, {"label": "Feel lightheaded, dizzy or have had fainting spells", "value": "dizziness", "shortcut": ""}, {"label": "Rectal bleeding (red or tarry/black stools)", "value": "rectal_bleeding", "shortcut": ""}, {"label": "Vomited blood or material that looks like coffee grounds", "value": "vomiting_blood", "shortcut": ""}], "inputType": "checkbox", "tableView": true, "customConditional": "show = !data.indication_for_testing.none_of_the_above;", "optionsLabelPosition": "right"}, {"key": "no_pylori_contraindication", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = !!data.no_pylori_contraindication || !!_.some(_.values(data.pylori_contraindication));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = !data.indication_for_testing.none_of_the_above;"}, {"key": "no_indications", "type": "textfield", "input": true, "label": "No Indication:", "hidden": true, "disabled": true, "multiple": false, "tableView": true, "clearOnHide": false, "calculateValue": "value = data.indication_for_testing.none_of_the_above && data.no_pylori_contraindication;", "refreshOnChange": true}, {"key": "contraindication_keys", "type": "textfield", "input": true, "label": "Contraindication keys:", "hidden": true, "disabled": true, "multiple": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "value = []; if (data.indication_for_testing.none_of_the_above) { value.push('no_indication_for_testing'); } if (data.duration_of_symptoms === 'less_than_6_months') { value.push('duration_of_symptoms_less_than_6_months'); } if (_.keys(_.pickBy(data.pylori_contraindication)).length > 0) { value.push('has_pylori_contraindication'); }", "refreshOnChange": true}]}