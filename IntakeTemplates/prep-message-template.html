{% load template_extras %}
{% with data=questionnaire.hpc.data %}
{% with summary=questionnaire.raw_formio_summary %}
{% with insured=questionnaire.insured_assays.all %}
{% with uninsured=questionnaire.uninsured_assays.all %}
{% with rxts=questionnaire.rxts.all %}

<!-- Introduction -->
<p>Hi {{ patient.name }},</p>
<p>
    This is {{ doctor.name }} (CPSO #{{ doctor.cpso_number }}) from our clinic.  
    Based on your responses, we’ve reviewed your request to start PrEP (Pre-Exposure Prophylaxis) for HIV prevention.  
    If you believe any of your answers were inaccurate or have additional symptoms or risk factors to share, let us know and we can arrange a secure follow-up. Otherwise, here’s the plan moving forward.
</p>

<!-- Screening Summary -->
<h5>HIV Risk & PrEP History</h5>
<ul>
    <li><strong>Previous PrEP use:</strong> {{ summary.previous_prep_use.c_val|default:"Not answered" }}</li>

    {% if summary.previous_prep_use.raw == true %}
    <li><strong>Last used PrEP:</strong> {{ summary.last_prep_use.c_val }}</li>
    <li><strong>Brand used:</strong> {{ summary.daily_prep_brand.c_val }}</li>
    <li><strong>Reason for stopping:</strong> {{ summary.prep_stopping_reason.c_val }}</li>
    {% if summary.prep_stopping_reason_other %}<li><strong>Other reason:</strong> {{ summary.prep_stopping_reason_other }}</li>{% endif %}
    <li><strong>Side effects experienced:</strong>
        {% if summary.experienced_prep_side_effects.c_val == "Yes" %}
        {{ summary.prep_side_effects_list.c_val }}
        <ul>
            <li>Severity: {{ summary.prep_side_effect_severity.c_val }}</li>
            <li>Resolution: {{ summary.prep_side_effects_resolved.c_val }}</li>
            {% if summary.prep_side_effects_other %}<li>Other: {{ summary.prep_side_effects_other }}</li>{% endif %}
        </ul>
        {% else %}None reported{% endif %}
    </li>
    {% endif %}

    <li><strong>HIV Risk Factors:</strong> 
        {{ summary.prep_risk_factors.c_val|default:"Not provided" }}
        {% if summary.prep_risk_factors_other %}<br>Other: {{ summary.prep_risk_factors_other }}{% endif %}
    </li>

    <li><strong>Recent STI Test:</strong> {{ summary.last_sti_test.c_val|default:"Not provided" }}</li>
    <li><strong>Past STI Diagnoses:</strong> {{ summary.past_std.c_val|default:"Not specified" }}</li>
    <li><strong>Sexual Partners (past 6 months):</strong> {{ summary.number_of_recent_sexual_partners.c_val }}</li>
    <li><strong>Route(s) of Exposure:</strong>
        {% if summary.type_of_sex.c_val %}
        <ul>
            {% for item in summary.type_of_sex.c_val|split:", " %}
            <li>{{ item }}</li>
            {% endfor %}
        </ul>
        {% else %}
        Not specified
        {% endif %}
    </li>
</ul>

<!-- Health Condition Summary -->
<h5>Health Summary</h5>
<ul>
  <li><strong>Kidney disease history:</strong> {{ summary.kidney_disease_history.c_val }}</li>
  <li><strong>Bone health diagnosis (osteopenia/osteoporosis):</strong> {{ summary.bone_health_diagnosis.c_val }}</li>

  <li><strong>Current Medications:</strong> 
    {% if summary.medications_list and summary.medications_list.c_val %}
      {{ summary.medications_list.c_val }}
    {% else %}
      None
    {% endif %}
  </li>

  <li><strong>Medication Allergies:</strong> 
    {% if summary.medication_allergies_list and summary.medication_allergies_list.c_val %}
      {{ summary.medication_allergies_list.c_val }}
    {% else %}
      None
    {% endif %}
  </li>

  <li><strong>Past Surgeries:</strong> 
    {% if summary.past_surgeries_list and summary.past_surgeries_list.c_val %}
      {{ summary.past_surgeries_list.c_val }}
    {% else %}
      None
    {% endif %}
  </li>

  <li><strong>Health Conditions:</strong> 
    {% if summary.health_conditions_list and summary.health_conditions_list.c_val %}
      {{ summary.health_conditions_list.c_val }}
    {% else %}
      None
    {% endif %}
  </li>
</ul>

<!-- Advice Section -->
<h5>Advice</h5>
<p>
    You’ve confirmed interest in starting PrEP. This is a highly effective, evidence-based medication strategy for reducing HIV risk.  
    Before initiating, we require basic lab screening to ensure PrEP is safe for you and to rule out underlying conditions such as kidney disease or active STIs.
</p>

<!-- Plan Section -->
<h5>Plan</h5>
{% if insured or uninsured %}
<p><strong>Baseline Tests:</strong></p>
<ul>
    {% if insured %}
    <li><strong>Insured:</strong>
        <ul>
            {% for a in insured %}
            <li>{{ a.name }} ({{ a.test_type }} test)</li>
            {% endfor %}
        </ul>
    </li>
    {% endif %}

    {% if uninsured %}
    <li><strong>Uninsured:</strong>
        <ul>
            {% for a in uninsured %}
            <li>{{ a.name }} ({{ a.test_type }} test)</li>
            {% endfor %}
        </ul>
    </li>
    {% endif %}
</ul>
{% else %}
<p>No tests are currently listed — please contact us if you believe this is incorrect.</p>
{% endif %}

<!-- Instructions Section -->
<h5>Instructions</h5>
<ol>
    <li><strong>Requisition:</strong> Your lab requisition is now available. You may walk into any lab (LifeLabs, Dynacare, etc.) or upload it online if your lab allows that.</li>
    <li><strong>Turnaround Time:</strong> Results usually return in 1–3 business days.</li>
    <li><strong>Next Step:</strong> Once we receive your results, we’ll arrange a secure chat to finalize your PrEP regimen and pharmacy preferences.</li>
    <li><strong>If Normal:</strong> If all results are normal, we’ll issue the prescription directly to your preferred pharmacy.</li>
</ol>

<!-- Footer -->
<p>Best regards,<br>{{ doctor.name }}</p>

{% endwith %}
{% endwith %}
{% endwith %}
{% endwith %}
{% endwith %}
