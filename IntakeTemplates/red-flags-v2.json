{"name": "redFlags", "type": "form", "title": "Red Flags", "display": "form", "components": [{"key": "home-kit", "type": "checkbox", "input": true, "label": "I understand TeleTest does NOT send out home STI kits unless I require oral or anal swabs for site-specific screening.", "hidden": false, "validate": {"required": true}, "tableView": false, "clearOnHide": false, "defaultValue": true, "customConditional": "show = !!data.vca;"}, {"key": "std_red_flag_symptoms", "type": "selectboxes", "input": true, "label": "<strong>Please check if you have any of the following symptoms:</strong>", "values": [{"label": "Abdominal/pelvic pain or cramping, back pain, or rectal pain", "value": "abdominal_pain_back_pain_or_rectal_pain"}, {"label": "Blood in my urine", "value": "blood_in_urine"}, {"label": "Fever", "value": "fever"}, {"label": "<PERSON>well", "value": "feel_unwell"}, {"label": "Unable to urinate", "value": "unable_to_urinate"}, {"label": "<PERSON><PERSON>", "value": "genital_rash", "customConditional": "show = !!data.vca;"}, {"label": "Testicular Pain", "value": "testicular_pain", "customConditional": "show = data.sex == 'male';"}], "inputType": "checkbox", "tableView": false, "customConditional": "show = !data.needle_stick;", "optionsLabelPosition": "right"}, {"key": "other_std_symptoms", "type": "selectboxes", "input": true, "label": "Other STD symptoms:", "values": [{"label": "Tingling at the tip of the penis", "value": "penile_tingling", "shortcut": "", "customConditional": "show = data.sex == 'male'"}], "adminFlag": true, "hideLabel": true, "inputType": "checkbox", "tableView": true, "customClass": "mt-n3", "refreshOnChange": true, "customConditional": "show = !data.needle_stick;", "optionsLabelPosition": "right"}, {"key": "no_std_symptoms", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "required, or select a symptom."}, "validate": {"custom": "valid = !!data.no_std_symptoms || !!_.some(_.values(data.std_red_flag_symptoms)) || !!_.some(_.values(data.uti_symptoms)) || !!_.some(_.values(data.bv_symptoms)) || !!_.some(_.values(data.other_std_symptoms));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = !data.needle_stick;"}, {"key": "red_flags_std", "type": "textfield", "input": true, "label": "Patient indicated they had the following red flag symptoms:", "hidden": true, "disabled": true, "multiple": false, "adminFlag": true, "hideLabel": true, "tableView": true, "spellcheck": false, "clearOnHide": false, "calculateValue": "value = !_.some(_.values(data.std_red_flag_symptoms)) ? 'None' : _.replace(_.join(_.map(_.keys(_.pickBy(data.std_red_flag_symptoms)), _.capitalize), ', '), /_/g, ' ');"}, {"key": "std_red_flag_symptoms_not_present", "type": "textfield", "input": true, "label": "Patient indicated they DO NOT have the following red flag symptoms:", "hidden": true, "disabled": true, "multiple": false, "hideLabel": false, "tableView": true, "spellcheck": false, "clearOnHide": false, "confirm_label": "You DO NOT have the following symptoms:", "calculateValue": "value = _.replace(_.join(_.keys(_.pickBy(data.std_red_flag_symptoms, _.negate(_.identity))).filter(k=>(data.sex=='male'||k!='testicular_pain')).map(_.capitalize), ', '), /_/g, ' ');"}]}