{"components": [{"key": "currently_on_trt", "type": "radio", "input": true, "label": "Have you been prescribed testosterone replacement therapy (TRT) by a licensed physician or nurse practitioner?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "optionsLabelPosition": "right"}, {"key": "trt_symptom_response", "type": "radio", "input": true, "label": "Have your original low-testosterone symptoms improved on TRT?", "values": [{"label": "Yes - feeling better", "value": "responsive"}, {"label": "No - little or no change", "value": "nonresponsive"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.currently_on_trt === 'yes';", "optionsLabelPosition": "right"}, {"key": "trt_for_hrt", "type": "radio", "input": true, "label": "Are you taking testosterone as part of gender-affirming hormone therapy (HRT)?", "values": [{"label": "Yes", "value": "yes"}, {"label": "No", "value": "no"}], "validate": {"required": true}, "tableView": true, "customConditional": "show = data.currently_on_trt === 'yes';", "optionsLabelPosition": "right"}, {"key": "trt_contraindication_symptoms", "type": "selectboxes", "input": true, "label": "Do you currently have any of these symptoms?", "values": [{"label": "Chest pain", "value": "chest_pain"}, {"label": "Shortness of breath with light activity (e.g., walking across a room)", "value": "sob_light_activity"}, {"label": "Swelling of legs (edema)", "value": "edema"}], "inputType": "checkbox", "tableView": true, "customConditional": "show = data.currently_on_trt === 'yes';", "optionsLabelPosition": "right"}, {"key": "trt_contraindication_conditions", "type": "selectboxes", "input": true, "label": "Have you been diagnosed with any of these conditions?", "values": [{"label": "<PERSON><PERSON> (diagnosed)", "value": "dx_angina"}, {"label": "Congestive heart failure (CHF)", "value": "congestive_hf"}, {"label": "Known prostate cancer", "value": "prostate_cancer"}, {"label": "Abnormal prostate exam (e.g., nodule)", "value": "abnormal_prostate_exam"}, {"label": "Untreated severe sleep apnea", "value": "sleep_apnea"}], "inputType": "checkbox", "tableView": true, "customConditional": "show = data.currently_on_trt === 'yes';", "optionsLabelPosition": "right"}, {"key": "no_trt_contraindications", "type": "checkbox", "input": true, "label": "None of the above", "errors": {"custom": "Please select at least one option above or confirm none apply."}, "validate": {"custom": "valid = !!data.no_trt_contraindications || !!_.some(_.values(data.trt_contraindication_symptoms)) || !!_.some(_.values(data.trt_contraindication_conditions));"}, "tableView": true, "customClass": "mt-n3", "defaultValue": false, "customConditional": "show = data.currently_on_trt === 'yes';"}, {"key": "no_indications", "type": "textfield", "input": true, "label": "No Indication", "hidden": true, "disabled": true, "multiple": false, "tableView": true, "clearOnHide": false, "defaultValue": true, "calculateValue": "value = (data.currently_on_trt === 'yes' && data.trt_symptom_response === 'responsive' && data.trt_for_hrt === 'no' && !_.some(_.values(data.trt_contraindication_symptoms)) && !_.some(_.values(data.trt_contraindication_conditions))) ? false : true;", "refreshOnChange": true}, {"key": "contraindication_keys", "type": "textfield", "input": true, "label": "Contraindication keys", "hidden": true, "disabled": true, "multiple": true, "tableView": true, "clearOnHide": false, "defaultValue": [], "calculateValue": "value = (function() { var list = []; if ((_.some(_.values(data.trt_contraindication_symptoms)) || _.some(_.values(data.trt_contraindication_conditions))) && data.currently_on_trt === 'yes') { list.push('trt_contraindication'); } if (data.trt_symptom_response === 'nonresponsive' && data.currently_on_trt === 'yes') { list.push('trt_non_response'); } if (data.trt_for_hrt === 'yes' && data.currently_on_trt === 'yes') { list.push('trt_hrt'); } return list; })();", "refreshOnChange": true}, {"key": "approved_html", "type": "content", "input": true, "tableView": false, "clearOnHide": false, "defaultValue": "<h3 class='text-green'>You're eligible to renew your testosterone prescription.</h3></br><p>TeleTest physicians can:<ul><li>Renew your existing TRT prescription (e.g., Testosterone Cypionate, Enanthate, or licensed gels/patches)</li><li>Adjust the dose or formulation within Health&nbsp;Canada-approved products</li><li>Order and review required monitoring labs (CBC, PSA, serum testosterone, etc.)</li></ul><strong>But we do not:</strong><ul><li>Start TRT if you have never been prescribed testosterone</li><li>Prescribe human chorionic gonadotropin (hCG) or aromatase inhibitors (AIs)</li></ul><p><em>Reminder:</em> Ongoing TRT requires follow-up labs at 3-6&nbsp;months, 12&nbsp;months, then yearly.</p>"}, {"key": "rejected_html", "type": "content", "input": true, "tableView": false, "clearOnHide": false, "defaultValue": "<h3 class='text-red'>Your responses suggest we cannot renew your testosterone prescription online.</h3></br><p>Common reasons include:<ul><li>Significant heart- or prostate-related symptoms/diagnoses (e.g., CHF, angina, prostate cancer)</li><li>No improvement in symptoms despite ongoing therapy</li><li>You’re currently using testosterone as part of gender-affirming hormone therapy (HRT)</li></ul><p>We recommend an in-person medical assessment for further evaluation.</p><p><strong>Hormone therapy (HRT) patients:</strong> TeleTest will be offering prescription renewals for gender-affirming care soon. Please check back later or visit <a href='https://www.rainbowhealthontario.ca' target='_blank' rel='noopener'>Rainbow Health Ontario</a> for resources in the meantime.</p>", "refreshOnChange": true}]}